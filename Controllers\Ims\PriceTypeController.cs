using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Ims;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Ims;
/// <summary> 價格類別管理 </summary>
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("價格類別管理")]
public class PriceTypeController(IPriceTypeService _Interface) : ControllerBase
{
    /// <summary> 取得價格類別列表 </summary>
    [HttpGet]
    [Route("GetAll")]
    [SwaggerOperation(Summary = "取得價格類別列表", Description = "取得價格類別列表")]
    public async Task<IActionResult> GetAll()
    {
        try
        {
            var PriceTypes = await _Interface.GetAllAsync();
            return Ok(new { success = true, message = "取得價格類別列表成功", data = PriceTypes });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得價格類別列表失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 取得價格類別 </summary>
    [HttpGet]
    [Route("{priceTypeId}")]
    [SwaggerOperation(Summary = "取得價格類別", Description = "根據價格類別ID取得單一價格類別詳細資料")]
    public async Task<IActionResult> Get([FromRoute] Guid PriceTypeID)
    {
        try
        {
            if (PriceTypeID == Guid.Empty)
            {
                return BadRequest(new { success = false, message = "價格類別ID不能為空", data = (object?)null });
            }

            var PriceType = await _Interface.GetAsync(PriceTypeID);
            if (PriceType == null)
            {
                return NotFound(new { success = false, message = "找不到指定的價格類別", data = (object?)null });
            }

            return Ok(new { success = true, message = "取得價格類別成功", data = PriceType });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得價格類別失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 新增價格類別 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "新增價格類別", Description = "新增價格類別")]
    public async Task<IActionResult> Add([FromBody] PriceTypeDTO _data)
    {
        try
        {
            var (result, msg) = await _Interface.AddAsync(_data);
            if (!result)
            {
                return BadRequest(new { success = false, message = msg, data = (object?)null });
            }
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"新增價格類別失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 更新價格類別 </summary>
    [HttpPatch]
    [SwaggerOperation(Summary = "更新價格類別", Description = "更新價格類別")]
    public async Task<IActionResult> Update([FromBody] PriceTypeDTO _data)
    {
        try
        {
            var (result, msg) = await _Interface.UpdateAsync(_data);
            if (!result)
            {
                return BadRequest(new { success = false, message = msg, data = (object?)null });
            }
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"更新價格類別失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 刪除價格類別 </summary>
    [HttpDelete]
    [SwaggerOperation(Summary = "刪除價格類別", Description = "刪除價格類別")]
    public async Task<IActionResult> Delete([FromBody] PriceTypeDTO _data)
    {
        try
        {
            var (result, msg) = await _Interface.DeleteAsync(_data);
            if (!result)
            {
                return BadRequest(new { success = false, message = msg, data = (object?)null });
            }
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"刪除價格類別失敗: {ex.Message}", data = (object?)null });
        }
    }
}