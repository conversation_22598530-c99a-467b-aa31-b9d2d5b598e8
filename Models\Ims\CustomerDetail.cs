using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 客戶詳細資訊 </summary>
public class CustomerDetail
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 客戶代碼 </summary>
    [MaxLength(20)]
    [Comment("客戶代碼")]
    [Column(TypeName = "nvarchar(20)")]
    public string? CustomerCode { get; set; }

    /// <summary> 客戶分類編號 </summary>
    [Comment("客戶分類編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid? CustomerCategoryID { get; set; }
    
    /// <summary> 客戶分類導航屬性 </summary>
    public CustomerCategory? CustomerCategory { get; set; }

    /// <summary> 應收結帳日 (每月) </summary>
    [Comment("應收結帳日")]
    [Range(1, 31, ErrorMessage = "結帳日必須在1-31之間")]
    public int? SettlementDay { get; set; }

    /// <summary> 建構式 </summary>
    public CustomerDetail()
    {
        PartnerID = Guid.Empty;
    }
}

/// <summary> 客戶詳細資訊DTO </summary>
public class CustomerDetailDTO
{
    /// <summary> 商業夥伴編號 </summary>
    public Guid PartnerID { get; set; }

    /// <summary> 客戶代碼 </summary>
    [MaxLength(20)]
    public string? CustomerCode { get; set; }

    /// <summary> 客戶分類編號 </summary>
    public Guid? CustomerCategoryID { get; set; }
    
    /// <summary> 客戶分類導航屬性 </summary>
    public CustomerCategory? CustomerCategory { get; set; }

    /// <summary> 應收結帳日 (每月) </summary>
    [Comment("應收結帳日")]
    [Range(1, 31, ErrorMessage = "結帳日必須在1-31之間")]
    public int? SettlementDay { get; set; }

    /// <summary> 建構式 </summary>
    public CustomerDetailDTO()
    {
        PartnerID = Guid.Empty;
    }
}