import { useEffect, useState } from 'react';
import { Modal, Button, Spin, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Typography, Space } from 'antd';
import dayjs from 'dayjs';
import {
    getEnsureList,
    getEnsureDetail,
    addEnsure,
    editEnsure,
    deleteEnsure,
    Ensure,
    createEmptyEnsure,
} from '@/services/pas/EnsureService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    SafetyCertificateOutlined,
    UserOutlined,
    PhoneOutlined,
    FileTextOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    PlusOutlined,
    IdcardOutlined,
    TeamOutlined,
    CalendarOutlined,
    HomeOutlined,
    MailOutlined,
    NumberOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type EnsureInfoProps = {
    userId: string;
    active: boolean;
};

const EnsureInfo: React.FC<EnsureInfoProps> = ({ userId, active }) => {
    const [list, setList] = useState<Ensure[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [detail, setDetail] = useState<Ensure | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) fetchList();
    }, [active, userId]);

    const fetchList = async () => {
        setLoading(true);
        try {
            const res = await getEnsureList(userId);
            if (res.success && res.data) setList(res.data);
            else message.error(res.message || '載入失敗');
        } catch (err: any) {
            setErrorMsg(err.message || '未知錯誤');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const res = await getEnsureDetail(uid);
            if (res.success && res.data) {
                const d = res.data;
                form.resetFields();
                form.setFieldsValue({
                    ...d,
                    guarantorBirthday: d.guarantorBirthday ? dayjs(d.guarantorBirthday) : null,
                });
                setDetail(d);
                setIsModalOpen(true);
            } else message.error(res.message || '載入失敗');
        } catch (err: any) {
            message.error(err.message || '錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleAddNew = () => {
        form.resetFields();
        form.setFieldsValue(createEmptyEnsure());
        setDetail(null);
        setIsModalOpen(true);
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            const payload: Ensure = {
                ...values,
                userId,
                guarantorBirthday: values.guarantorBirthday ? values.guarantorBirthday.format('YYYY-MM-DD') : '',
            };

            setModalLoading(true);
            const res = detail
                ? await editEnsure({ ...payload, uid: detail.uid })
                : await addEnsure(payload);

            if (res.success && res.data?.result) {
                message.success(detail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchList();
            } else {
                message.error(res.data?.msg || res.message || '儲存失敗');
            }
        } catch (err: any) {
            if (!err?.errorFields) message.error(err.message || '儲存錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteEnsure(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchList();
            } else {
                message.error(res.data?.msg || '刪除失敗');
            }
        } catch (err: any) {
            message.error(err.message || '刪除錯誤');
        }
    };

    if (!active) return null;
    if (errorMsg) return (
        <div style={{
            color: '#ff4d4f',
            textAlign: 'center',
            padding: '40px 20px',
            background: '#fff1f0',
            borderRadius: '8px',
            border: '1px solid #ffccc7'
        }}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            錯誤：{errorMsg}
        </div>
    );

    return (
        <>
            <Card
                title={<Title level={4} style={{ margin: 0 }}>保證資料</Title>}
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{ borderRadius: '6px' }}
                    >
                        新增保證資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={list}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 筆`,
                        style: { marginTop: 16 }
                    }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                        style: { cursor: 'pointer' }
                    })}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    columns={[
                        {
                            title: '保證書編號',
                            dataIndex: 'ensureNumber',
                            render: (text) => (
                                <Space>
                                    <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '保證人姓名',
                            dataIndex: 'guarantorName',
                            render: (text) => (
                                <Space>
                                    <UserOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '保證人身分證',
                            dataIndex: 'guarantorPersonalId',
                            render: (text) => (
                                <Space>
                                    <IdcardOutlined style={{ color: '#722ed1' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '關係',
                            dataIndex: 'relationship',
                            render: (text) => (
                                <Space>
                                    <TeamOutlined style={{ color: '#eb2f96' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            ),
                        },
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space direction="vertical" size={16}>
                                    {record.remark && (
                                        <div>
                                            <Space>
                                                <FileTextOutlined style={{ color: '#1890ff' }} />
                                                <Text strong>備註：</Text>
                                                <Text>{record.remark}</Text>
                                            </Space>
                                        </div>
                                    )}
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!record.remark,
                    }}
                />
            </Card>

            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {detail ? '編輯保證資料' : '新增保證資料'}
                    </Title>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                onOk={handleModalOk}
                confirmLoading={modalLoading}
                width={720}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form
                    layout="vertical"
                    form={form}
                    className="mt-4"
                >
                    <Title level={5} className="mb-4">
                        <Space>
                            <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
                            <Text>保證書資訊</Text>
                        </Space>
                    </Title>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>保證書編號</Text>}
                                name="ensureNumber"
                                rules={[{ required: true, message: '請輸入保證書編號' }]}
                            >
                                <Input
                                    placeholder="請輸入保證書編號"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Title level={5} className="mb-4 mt-4">
                        <Space>
                            <UserOutlined style={{ color: '#52c41a' }} />
                            <Text>保證人資訊</Text>
                        </Space>
                    </Title>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>保證人姓名</Text>}
                                name="guarantorName"
                                rules={[{ required: true, message: '請輸入保證人姓名' }]}
                            >
                                <Input
                                    placeholder="請輸入保證人姓名"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>保證人身分證</Text>}
                                name="guarantorPersonalId"
                                rules={[{ required: true, message: '請輸入保證人身分證' }]}
                            >
                                <Input
                                    placeholder="請輸入保證人身分證"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>關係</Text>}
                                name="relationship"
                                rules={[{ required: true, message: '請輸入關係' }]}
                            >
                                <Input
                                    placeholder="請輸入關係"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>出生日期</Text>}
                                name="guarantorBirthday"
                            >
                                <DatePicker
                                    style={{ width: '100%', borderRadius: '6px' }}
                                    format="YYYY-MM-DD"
                                    placeholder="請選擇出生日期"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>電話</Text>}
                                name="guarantorPhone"
                            >
                                <Input
                                    placeholder="請輸入電話"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label={<Text strong>地址</Text>}
                                name="guarantorAddress"
                            >
                                <Input
                                    placeholder="請輸入地址"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>保證財產</Text>}
                                name="guarantorProperty"
                            >
                                <Input placeholder="請輸入財產資料"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={<Text strong>財產價值</Text>}
                                name="propertyValue"
                            >
                                <Input placeholder="請輸入財產價值"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label={<Text strong>備註</Text>}
                                name="remark"
                            >
                                <Input.TextArea
                                    rows={4}
                                    placeholder="請輸入備註內容"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default EnsureInfo;
