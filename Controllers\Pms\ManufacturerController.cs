using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("製造商資料管理")]
    public class ManufacturerController : ControllerBase
    {
        private readonly IManufacturerService _Interface;

        public ManufacturerController(IManufacturerService manufacturerService)
        {
            _Interface = manufacturerService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得製造商列表", Description = "取得所有製造商資料")]
        public async Task<IActionResult> GetManufacturerList()
        {
            var result = await _Interface.GetManufacturerAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得製造商明細", Description = "依ID取得製造商明細")]
        public async Task<IActionResult> GetManufacturerDetail(string id)
        {
            var result = await _Interface.GetManufacturerDetailAsync(id);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增製造商", Description = "新增製造商資料")]
        public async Task<IActionResult> AddManufacturer([FromBody] ManufacturerDTO _data)
        {
            var (result, msg) = await _Interface.AddManufacturerAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯製造商", Description = "修改已存在之製造商資料")]
        public async Task<IActionResult> EditManufacturer([FromBody] ManufacturerDTO _data)
        {
            var (result, msg) = await _Interface.EditManufacturerAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除製造商", Description = "刪除已存在之製造商資料")]
        public async Task<IActionResult> DeleteManufacturer([FromBody] ManufacturerDTO _data)
        {
            var (result, msg) = await _Interface.DeleteManufacturerAsync(_data);
            return Ok(new { result, msg });
        }
    }
}