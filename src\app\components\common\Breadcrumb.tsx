"use client";

/* 麵包屑
  /app/components/common/Breadcrumb.tsx
*/
import React from "react";
import { Breadcrumb as AntBreadcrumb } from "antd";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { routeNames } from "@/config/routes";

const Breadcrumb: React.FC = () => {
  const pathname = usePathname();
  const pathSnippets = pathname.split("/").filter((i) => i);

  // 定義可點擊的路由項目及其導向路徑
  const clickableRoutes: Record<string, string> = {
    "/pms": "/pms",
    /* "/pas": "/pas", */
    "/ims": "/ims",
  };

  // 判斷是否為可點擊的路由項目
  const isClickableRoute = (url: string): boolean => {
    return url in clickableRoutes;
  };

  // 創建麵包屑項目，支援可點擊的路由
  const createBreadcrumbItem = (url: string, title: string) => {
    if (isClickableRoute(url)) {
      return (
        <Link
          href={clickableRoutes[url]}
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          {title}
        </Link>
      );
    }
    return title;
  };

  const extraBreadcrumbItems = pathSnippets.map((_, index) => {
    const url = `/${pathSnippets.slice(0, index + 1).join("/")}`;
    const title = routeNames[url] || url;

    return {
      key: url,
      title: createBreadcrumbItem(url, title),
    };
  });

  const breadcrumbItems = [
    {
      title: (
        <Link
          href="/dashboard"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          {routeNames["/"]}
        </Link>
      ),
      key: "home",
    },
    ...extraBreadcrumbItems,
  ];

  return <AntBreadcrumb style={{}} items={breadcrumbItems} />;
};

export default Breadcrumb;
