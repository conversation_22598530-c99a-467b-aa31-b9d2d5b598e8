using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetCategoryService
    {
        Task<List<AssetCategoryDTO>> GetAllAsync();
        Task<string> GetByIdAsync(string id);
        Task<(bool, string)> AddAsync(AssetCategoryDTO assetCategory);
        Task<(bool, string)> UpdateAsync(AssetCategoryDTO assetCategory);
        Task<(bool, string)> DeleteAsync(AssetCategoryDTO assetCategory);
    }
}