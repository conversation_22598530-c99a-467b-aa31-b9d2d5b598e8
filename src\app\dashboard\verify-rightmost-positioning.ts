// Verification script to ensure widgets can be positioned at rightmost columns
import { defaultLayouts, cols } from './config';
import { DashboardLayout, Breakpoint } from './types';

// Function to verify that each breakpoint can utilize its rightmost columns
export const verifyRightmostPositioning = () => {
  console.log('🔍 Verifying Rightmost Column Positioning...\n');
  
  let allTestsPassed = true;
  
  Object.keys(cols).forEach((bp) => {
    const breakpoint = bp as Breakpoint;
    const maxCols = cols[breakpoint];
    const layout = defaultLayouts[breakpoint];
    
    console.log(`📱 ${breakpoint.toUpperCase()} Breakpoint (${maxCols} columns):`);
    
    // Check if any widget uses the rightmost column
    const rightmostUsage = layout.some((item: DashboardLayout) => {
      const rightEdge = (item.x || 0) + (item.w || 1);
      return rightEdge === maxCols;
    });
    
    // Find widgets that could be moved to rightmost column
    const movableToRightmost = layout.filter((item: DashboardLayout) => {
      const minWidth = item.minW || 1;
      return minWidth <= maxCols; // Can fit in the grid
    });
    
    // Check for boundary violations
    const violations = layout.filter((item: DashboardLayout) => {
      const rightEdge = (item.x || 0) + (item.w || 1);
      return rightEdge > maxCols;
    });
    
    if (violations.length > 0) {
      console.log(`  ❌ Boundary violations found:`);
      violations.forEach(item => {
        const rightEdge = (item.x || 0) + (item.w || 1);
        console.log(`    - ${item.i}: x=${item.x}, w=${item.w}, rightEdge=${rightEdge} > maxCols=${maxCols}`);
      });
      allTestsPassed = false;
    } else {
      console.log(`  ✅ No boundary violations`);
    }
    
    if (rightmostUsage) {
      console.log(`  ✅ Rightmost column is utilized`);
    } else {
      console.log(`  ⚠️  Rightmost column not used in default layout`);
    }
    
    console.log(`  📊 Widgets that can be moved to rightmost: ${movableToRightmost.length}/${layout.length}`);
    
    // Show current layout utilization
    const maxX = Math.max(...layout.map(item => (item.x || 0) + (item.w || 1)));
    const utilization = ((maxX / maxCols) * 100).toFixed(1);
    console.log(`  📈 Grid utilization: ${utilization}% (${maxX}/${maxCols} columns)\n`);
  });
  
  return allTestsPassed;
};

// Function to create test layouts that use rightmost columns
export const createRightmostTestLayouts = () => {
  console.log('🧪 Creating test layouts for rightmost positioning...\n');
  
  Object.keys(cols).forEach((bp) => {
    const breakpoint = bp as Breakpoint;
    const maxCols = cols[breakpoint];
    
    console.log(`${breakpoint.toUpperCase()} (${maxCols} columns):`);
    
    // Test widget positioned at rightmost column
    const rightmostWidget: DashboardLayout = {
      i: `test-rightmost-${breakpoint}`,
      x: maxCols - 1, // Rightmost column
      y: 0,
      w: 1,
      h: 2,
      minW: 1,
      minH: 2
    };
    
    // Test widget that spans to rightmost column
    const spanningWidget: DashboardLayout = {
      i: `test-spanning-${breakpoint}`,
      x: Math.max(0, maxCols - 3), // Start 3 columns from right
      y: 2,
      w: Math.min(3, maxCols), // Span 3 columns or max available
      h: 2,
      minW: 1,
      minH: 2
    };
    
    console.log(`  Single widget at rightmost: x=${rightmostWidget.x}, w=${rightmostWidget.w}`);
    console.log(`  Spanning widget to rightmost: x=${spanningWidget.x}, w=${spanningWidget.w}`);
    console.log(`  Both widgets fit within ${maxCols} columns: ${
      (rightmostWidget.x + rightmostWidget.w <= maxCols) && 
      (spanningWidget.x + spanningWidget.w <= maxCols) ? '✅' : '❌'
    }\n`);
  });
};

// Function to validate that drag-and-drop can reach rightmost columns
export const validateDragDropCapability = () => {
  console.log('🎯 Validating drag-and-drop capability for rightmost positioning...\n');
  
  Object.keys(cols).forEach((bp) => {
    const breakpoint = bp as Breakpoint;
    const maxCols = cols[breakpoint];
    
    console.log(`${breakpoint.toUpperCase()}:`);
    
    // Test various widget sizes at rightmost position
    const testSizes = [1, 2, 3, 4];
    
    testSizes.forEach(width => {
      if (width <= maxCols) {
        const rightmostPosition = maxCols - width;
        const canPosition = rightmostPosition >= 0;
        
        console.log(`  Width ${width}: rightmost position x=${rightmostPosition} ${canPosition ? '✅' : '❌'}`);
      }
    });
    
    console.log('');
  });
};

// Main verification function
export const runCompleteVerification = () => {
  console.log('🚀 Starting Complete Rightmost Positioning Verification\n');
  console.log('=' .repeat(60));
  
  const boundaryCheck = verifyRightmostPositioning();
  
  console.log('=' .repeat(60));
  createRightmostTestLayouts();
  
  console.log('=' .repeat(60));
  validateDragDropCapability();
  
  console.log('=' .repeat(60));
  console.log(`\n🎉 Verification Complete! Overall result: ${boundaryCheck ? '✅ PASSED' : '❌ FAILED'}`);
  
  return boundaryCheck;
};
