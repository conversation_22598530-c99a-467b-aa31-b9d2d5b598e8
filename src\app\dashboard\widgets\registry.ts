import React from 'react';
import { WidgetConfig } from '../types';

// Import all widget components using named imports
import { WelcomeWidget } from './WelcomeWidget';
import { StatsWidget } from './StatsWidget';
import { ChartWidget } from './ChartWidget';
import { TaskWidget } from './TaskWidget';
import { CalendarWidget } from './CalendarWidget';
import { NotificationWidget } from './NotificationWidget';
import { VendorMaintenanceWidget } from './pms/VendorMaintenanceWidget';
import { AssetCarryOutWidget } from './pms/AssetCarryOutWidget';

/**
 * Widget Component Map
 * Maps widget component names to their actual React components
 */
export const widgetComponents: Record<string, React.ComponentType<any>> = {
  WelcomeWidget,
  StatsWidget,
  ChartWidget,
  TaskWidget,
  CalendarWidget,
  NotificationWidget,
  VendorMaintenanceWidget,
  AssetCarryOutWidget,
};

/**
 * Widget Registry
 * Central registry for all available dashboard widgets
 * 
 * To add a new widget:
 * 1. Create your widget component in the widgets folder
 * 2. Import it above
 * 3. Add it to the widgetComponents map
 * 4. Add its configuration to the widgets array below
 */
export const widgets: WidgetConfig[] = [
  {
    id: "welcome",
    title: "歡迎訊息",
    component: "WelcomeWidget",
    defaultSize: { w: 4, h: 3 },
    minSize: { w: 2, h: 3 },
    description: "顯示歡迎訊息和當前日期",
    category: "基本",
    tags: ["歡迎", "日期", "時間"],
    defaultPosition: { x: 0, y: 0 },
  },
  {
    id: "stats",
    title: "統計數據",
    component: "StatsWidget",
    defaultSize: { w: 4, h: 3 },
    minSize: { w: 2, h: 3 },
    description: "顯示系統統計數據",
    category: "數據",
    tags: ["統計", "數據", "指標"],
    defaultPosition: { x: 4, y: 0 },
  },
  {
    id: "chart",
    title: "系統指標",
    component: "ChartWidget",
    defaultSize: { w: 4, h: 3 },
    minSize: { w: 2, h: 3 },
    description: "顯示系統性能指標",
    category: "數據",
    tags: ["圖表", "性能", "監控"],
    defaultPosition: { x: 8, y: 0 },
  },
  {
    id: "tasks",
    title: "任務列表",
    component: "TaskWidget",
    defaultSize: { w: 12, h: 5 },
    minSize: { w: 2, h: 4 },
    description: "顯示近期任務",
    category: "工作",
    tags: ["任務", "待辦", "工作"],
    defaultPosition: { x: 0, y: 3 },
  },
  {
    id: "calendar",
    title: "行事曆",
    component: "CalendarWidget",
    defaultSize: { w: 6, h: 6 },
    minSize: { w: 2, h: 5 },
    description: "顯示行事曆和事件",
    category: "時間",
    tags: ["日曆", "事件", "排程"],
    defaultPosition: { x: 0, y: 8 },
  },
  {
    id: "notifications",
    title: "通知中心",
    component: "NotificationWidget",
    defaultSize: { w: 6, h: 5 },
    minSize: { w: 2, h: 4 },
    description: "顯示系統通知",
    category: "通訊",
    tags: ["通知", "消息", "提醒"],
    defaultPosition: { x: 6, y: 8 },
  },
  {
    id: "vendor_maintenance",
    title: "PMS-廠商修繕申請",
    component: "VendorMaintenanceWidget",
    defaultSize: { w: 6, h: 6 },
    minSize: { w: 3, h: 5 },
    description: "顯示廠商修繕申請統計和最新申請列表",
    category: "PMS",
    tags: ["修繕", "廠商", "資產", "維護"],
    defaultPosition: { x: 6, y: 13 },
  },
  {
    id: "asset_carryout",
    title: "PMS-資產攜出申請",
    component: "AssetCarryOutWidget",
    defaultSize: { w: 6, h: 6 },
    minSize: { w: 3, h: 5 },
    description: "顯示資產攜出申請統計和最新申請列表",
    category: "PMS",
    tags: ["攜出", "資產", "申請", "借用"],
    defaultPosition: { x: 0, y: 19 },
  },
];

/**
 * Get widget component by name
 */
export const getWidgetComponent = (componentName: string): React.ComponentType<any> | null => {
  const component = widgetComponents[componentName];

  if (!component && process.env.NODE_ENV === 'development') {
    console.warn(`Widget component '${componentName}' not found. Available components:`, Object.keys(widgetComponents));
  }

  return component || null;
};

/**
 * Get widget configuration by ID
 */
export const getWidgetConfig = (widgetId: string): WidgetConfig | null => {
  return widgets.find(widget => widget.id === widgetId) || null;
};

/**
 * Get all available widgets
 */
export const getAllWidgets = (): WidgetConfig[] => {
  return widgets;
};

/**
 * Get widgets by category
 */
export const getWidgetsByCategory = (category: string): WidgetConfig[] => {
  return widgets.filter(widget => widget.category === category);
};

/**
 * Get all widget categories
 */
export const getWidgetCategories = (): string[] => {
  const categories = widgets.map(widget => widget.category || '其他');
  return [...new Set(categories)].sort();
};

/**
 * Search widgets by name, description, or tags
 */
export const searchWidgets = (query: string): WidgetConfig[] => {
  const lowercaseQuery = query.toLowerCase();
  return widgets.filter(widget =>
    widget.title.toLowerCase().includes(lowercaseQuery) ||
    widget.description?.toLowerCase().includes(lowercaseQuery) ||
    widget.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * Validate widget configuration
 */
export const validateWidgetConfig = (config: Partial<WidgetConfig>): string[] => {
  const errors: string[] = [];

  if (!config.id) {
    errors.push('Widget ID is required');
  }

  if (!config.title) {
    errors.push('Widget title is required');
  }

  if (!config.component) {
    errors.push('Widget component is required');
  }

  if (config.component && !widgetComponents[config.component]) {
    errors.push(`Widget component '${config.component}' not found in registry`);
  }

  if (!config.defaultSize || !config.defaultSize.w || !config.defaultSize.h) {
    errors.push('Widget defaultSize (w, h) is required');
  }

  if (!config.minSize || !config.minSize.w || !config.minSize.h) {
    errors.push('Widget minSize (w, h) is required');
  }

  return errors;
};

/**
 * Register a new widget dynamically
 * Useful for plugin systems or dynamic widget loading
 */
export const registerWidget = (
  config: WidgetConfig,
  component: React.ComponentType<any>
): boolean => {
  const errors = validateWidgetConfig(config);

  if (errors.length > 0) {
    console.error('Widget registration failed:', errors);
    return false;
  }

  // Check if widget already exists
  if (widgets.find(w => w.id === config.id)) {
    console.error(`Widget with ID '${config.id}' already exists`);
    return false;
  }

  // Register component
  widgetComponents[config.component] = component;

  // Register configuration
  widgets.push(config);

  console.log(`Widget '${config.id}' registered successfully`);
  return true;
};

/**
 * Unregister a widget
 */
export const unregisterWidget = (widgetId: string): boolean => {
  const widgetIndex = widgets.findIndex(w => w.id === widgetId);

  if (widgetIndex === -1) {
    console.error(`Widget with ID '${widgetId}' not found`);
    return false;
  }

  const widget = widgets[widgetIndex];

  // Remove from widgets array
  widgets.splice(widgetIndex, 1);

  // Remove component if no other widgets use it
  const componentStillUsed = widgets.some(w => w.component === widget.component);
  if (!componentStillUsed) {
    delete widgetComponents[widget.component];
  }

  console.log(`Widget '${widgetId}' unregistered successfully`);
  return true;
};

/**
 * Generate responsive layouts from base lg layout
 */
export const generateResponsiveLayouts = (baseLgWidgets: WidgetConfig[]): any => {
  // Import types and utilities here to avoid circular dependencies
  const { DashboardLayout } = require('../types');
  const { cols } = require('../config');

  // Create base lg layout from widget configurations
  const lgLayout = baseLgWidgets.map((widget) => ({
    i: widget.id,
    x: widget.defaultPosition?.x || 0,
    y: widget.defaultPosition?.y || 0,
    w: widget.defaultSize.w,
    h: widget.defaultSize.h,
    minW: widget.minSize.w,
    minH: widget.minSize.h,
  }));

  // Auto-arrange widgets that don't have specific positions
  const arrangedLgLayout = autoArrangeWidgets(lgLayout, 12);

  // Generate layouts for other breakpoints using scaling
  const responsiveLayouts = {
    lg: arrangedLgLayout,
    md: scaleLayoutForBreakpoint(arrangedLgLayout, 12, 10, 'md'),
    sm: scaleLayoutForBreakpoint(arrangedLgLayout, 12, 6, 'sm'),
    xs: scaleLayoutForBreakpoint(arrangedLgLayout, 12, 4, 'xs'),
    xxs: scaleLayoutForBreakpoint(arrangedLgLayout, 12, 2, 'xxs'),
  };

  return responsiveLayouts;
};

/**
 * Auto-arrange widgets in optimal grid positions
 */
const autoArrangeWidgets = (widgets: any[], maxCols: number) => {
  const arranged = [...widgets];
  const grid: boolean[][] = [];

  // Initialize grid
  for (let y = 0; y < 100; y++) {
    grid[y] = new Array(maxCols).fill(false);
  }

  // Place widgets with specified positions first
  arranged.forEach((widget) => {
    if (widget.x !== undefined && widget.y !== undefined) {
      markGridOccupied(grid, widget.x, widget.y, widget.w, widget.h);
    }
  });

  // Auto-arrange widgets without specific positions
  arranged.forEach((widget) => {
    if (widget.x === undefined || widget.y === undefined) {
      const position = findOptimalPosition(grid, widget.w, widget.h, maxCols);
      widget.x = position.x;
      widget.y = position.y;
      markGridOccupied(grid, position.x, position.y, widget.w, widget.h);
    }
  });

  return arranged;
};

/**
 * Mark grid cells as occupied
 */
const markGridOccupied = (grid: boolean[][], x: number, y: number, w: number, h: number) => {
  for (let row = y; row < y + h; row++) {
    for (let col = x; col < x + w; col++) {
      if (grid[row] && col < grid[row].length) {
        grid[row][col] = true;
      }
    }
  }
};

/**
 * Find optimal position for a widget
 */
const findOptimalPosition = (grid: boolean[][], w: number, h: number, maxCols: number) => {
  for (let y = 0; y < grid.length - h; y++) {
    for (let x = 0; x <= maxCols - w; x++) {
      if (canPlaceWidget(grid, x, y, w, h)) {
        return { x, y };
      }
    }
  }
  // Fallback to bottom of grid
  return { x: 0, y: grid.length };
};

/**
 * Check if widget can be placed at position
 */
const canPlaceWidget = (grid: boolean[][], x: number, y: number, w: number, h: number) => {
  for (let row = y; row < y + h; row++) {
    for (let col = x; col < x + w; col++) {
      if (grid[row] && grid[row][col]) {
        return false;
      }
    }
  }
  return true;
};

/**
 * Scale layout for different breakpoint (imported from utility)
 */
const scaleLayoutForBreakpoint = (layout: any[], fromCols: number, toCols: number, targetBreakpoint: string) => {
  if (!layout || layout.length === 0) return [];

  return layout.map((item) => {
    // Calculate relative position (0-1) to maintain spatial relationships
    const relativeX = (item.x || 0) / fromCols;
    const relativeW = (item.w || 1) / fromCols;

    // Scale width proportionally, but ensure minimum constraints
    let newW = Math.max(1, Math.round(relativeW * toCols));

    // Apply minimum constraints based on breakpoint
    const minW = targetBreakpoint === 'xxs' ? 2 :
      targetBreakpoint === 'xs' ? 2 :
        targetBreakpoint === 'sm' ? 2 : 2;

    newW = Math.max(minW, newW);

    // Ensure width doesn't exceed available columns
    newW = Math.min(newW, toCols);

    // Calculate new X position maintaining relative positioning
    let newX = Math.round(relativeX * toCols);

    // Adjust X position if widget would overflow
    if (newX + newW > toCols) {
      newX = Math.max(0, toCols - newW);
    }

    // For widgets that were positioned at the right edge, maintain right alignment
    const wasAtRightEdge = (item.x || 0) + (item.w || 1) === fromCols;
    if (wasAtRightEdge) {
      newX = toCols - newW;
    }

    // For widgets that were centered, try to maintain center alignment
    const wasCentered = Math.abs(((item.x || 0) + (item.w || 1) / 2) - fromCols / 2) < 1;
    if (wasCentered && !wasAtRightEdge) {
      const centerX = Math.round((toCols - newW) / 2);
      newX = Math.max(0, Math.min(centerX, toCols - newW));
    }

    return {
      i: item.i,
      x: newX,
      w: newW,
      y: item.y, // Maintain Y position for vertical relationships
      h: item.h, // Maintain height
      minW: Math.max(item.minW || 2, minW),
      minH: item.minH || 3,
    };
  });
};

export default {
  widgets,
  widgetComponents,
  getWidgetComponent,
  getWidgetConfig,
  getAllWidgets,
  getWidgetsByCategory,
  getWidgetCategories,
  searchWidgets,
  validateWidgetConfig,
  registerWidget,
  unregisterWidget,
  generateResponsiveLayouts,
};
