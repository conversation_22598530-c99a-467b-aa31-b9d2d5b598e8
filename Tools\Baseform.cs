﻿using System.Security.Cryptography;
using System.Text;
using System;
using System.Globalization;


namespace FAST_ERP_Backend.Server.Tools
{
    public class Baseform
    {
        /// <summary>
        /// 判斷小數點位數.
        /// </summary>
        /// <param name="_Inputstr">檢查字串</param>
        /// <param name="limitNum">判斷小數點最多幾位</param>
        /// <returns></returns>
        public bool checkpointnum(string _Inputstr, int limitNum)
        {
            bool result = true;

            string[] arr_str = _Inputstr.Split('.');

            if (arr_str.Length > 1)
            {
                if (arr_str[1].Length > limitNum)
                {
                    result = false;
                }
            }

            return result;
        }

        //小數點後四捨五入.
        public decimal fourthrow(decimal _inputnum, int _scale)
        {
            return Math.Round(Convert.ToDecimal(_inputnum), _scale, MidpointRounding.AwayFromZero);
        }

        //檢查數字欄位.
        public bool CheckNum(string _Inputstr)
        {
            if (_Inputstr.Trim() == "")
            {
                return false;
            }

            bool result = long.TryParse(_Inputstr, out long ts);
            return result;
        }

        //檢查是否為9碼日期格式(yyy/mm/dd).
        public bool CheckCDat(string DAT)
        {
            if (DAT.Trim().Length != 9)
            {
                return false;
            }

            try
            {
                DateTime Dte_result = new DateTime();
                string[] tempstr = DAT.Trim().Split('/');
                if (tempstr.Length > 1)
                {
                    return DateTime.TryParse(Convert.ToInt32(tempstr[0]) + 1911 + "/" + tempstr[1] + "/" + tempstr[2], out Dte_result);
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        //民國年增加位數 前面補幾個0.
        public string adjnumlen(int _num, int len)
        {
            string result = _num.ToString().Trim();

            if (_num.ToString().Length < len)
            {
                for (int i = _num.ToString().Length; i < len; i++)
                {
                    result = "0" + result;
                }
            }

            return result;
        }

        public string adjnumlen(string str_num, int len)
        {
            string result = str_num.Trim();

            if (str_num.Length < len)
            {
                for (int i = str_num.Length; i < len; i++)
                {
                    result = "0" + result;
                }
            }

            return result;
        }


        //檢查身分證字號.
        public bool CheckID(string arg_Identify)
        {
            var d = false;
            if (arg_Identify.Length == 10)
            {
                arg_Identify = arg_Identify.ToUpper();

                //第一碼在A到Z之間.
                if (arg_Identify[0] >= 0x41 && arg_Identify[0] <= 0x5A)
                {
                    var a = new[] { 10, 11, 12, 13, 14, 15, 16, 17, 34, 18, 19, 20, 21, 22, 35, 23, 24, 25, 26, 27, 28, 29, 32, 30, 31, 33 };
                    var b = new int[11];
                    b[1] = a[arg_Identify[0] - 65] % 10;
                    var c = b[0] = a[arg_Identify[0] - 65] / 10;
                    for (var i = 1; i <= 9; i++)
                    {
                        b[i + 1] = arg_Identify[i] - 48;
                        c += b[i] * (10 - i);
                    }
                    if ((c % 10 + b[10]) % 10 == 0)
                    {
                        d = true;
                    }
                }
            }
            return d;
        }

        public DateTime CDate(string _inputStrTime)
        {
            DateTime Dte_result = new DateTime();

            if (CheckCDat(_inputStrTime))
            {
                string[] tempstr = _inputStrTime.Split('/');
                if (tempstr.Length > 1)
                {
                    if (DateTime.TryParse(Convert.ToInt32(tempstr[0]) + 1911 + "/" + tempstr[1] + "/" + tempstr[2], out Dte_result))
                    {
                        return Dte_result;
                    }
                }
            }

            return Dte_result;
        }

        /// <summary>
        /// 西元日期字串 yyyy-MM-dd → timestamp（秒，視為本地時間）
        /// </summary>
        public long? DateStrToTimestamp(string date)
        {
            if (string.IsNullOrWhiteSpace(date))
                return null;

            // Parse 成 DateTime，並視為「本地時間」
            DateTime parsedDate = DateTime.ParseExact(date, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            // 建立本地時間的 offset（例如台灣為 +08:00）
            var offset = TimeZoneInfo.Local.GetUtcOffset(parsedDate);

            // 使用本地 offset 建立 DateTimeOffset
            return new DateTimeOffset(parsedDate, offset).ToUnixTimeSeconds();
        }

        /// <summary>
        /// timestamp（秒）→ 西元日期字串 yyyy-MM-dd（轉回本地時間）
        /// </summary>
        public string TimestampToDateStr(long? timestamp)
        {
            if (!timestamp.HasValue)
                return "";

            // 將 timestamp 當作 UTC 時間，再轉為本地時間
            DateTime localTime = DateTimeOffset.FromUnixTimeSeconds(timestamp.Value).ToLocalTime().DateTime;

            return localTime.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 檢查字串是否為 yyyy-MM-dd 格式的有效日期，或是空白（"" 或 null）
        /// </summary>
        /// <param name="dateStr">輸入的日期字串</param>
        /// <returns>true = 合法；false = 非法</returns>
        public bool IsValidDateOrEmpty(string dateStr)
        {
            if (string.IsNullOrWhiteSpace(dateStr))
                return true;

            return DateTime.TryParseExact(
                dateStr,
                "yyyy-MM-dd",
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out _);
        }

        /// <summary>
        /// 獲取當前本地時間的 timestamp（秒）
        /// 用於替代 DateTimeOffset.UtcNow.ToUnixTimeSeconds()
        /// </summary>
        /// <returns>當前本地時間的 timestamp（秒）</returns>
        public long GetCurrentLocalTimestamp()
        {
            var now = DateTime.Now;
            var offset = TimeZoneInfo.Local.GetUtcOffset(now);
            return new DateTimeOffset(now, offset).ToUnixTimeSeconds();
        }

        /// <summary>
        /// 西元日期時間字串 yyyy-MM-dd HH:mm:ss → timestamp（秒，視為本地時間）
        /// </summary>
        /// <param name="dateTime">日期時間字串，格式：yyyy-MM-dd HH:mm:ss</param>
        /// <returns>timestamp（秒）</returns>
        public long? DateTimeStrToTimestamp(string dateTime)
        {
            if (string.IsNullOrWhiteSpace(dateTime))
                return null;

            // Parse 成 DateTime，並視為「本地時間」
            DateTime parsedDateTime = DateTime.ParseExact(dateTime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            // 建立本地時間的 offset（例如台灣為 +08:00）
            var offset = TimeZoneInfo.Local.GetUtcOffset(parsedDateTime);

            // 使用本地 offset 建立 DateTimeOffset
            return new DateTimeOffset(parsedDateTime, offset).ToUnixTimeSeconds();
        }

        /// <summary>
        /// timestamp（秒）→ 西元日期時間字串 yyyy-MM-dd HH:mm:ss（轉回本地時間）
        /// </summary>
        /// <param name="timestamp">timestamp（秒）</param>
        /// <returns>本地時間字串 yyyy-MM-dd HH:mm:ss</returns>
        public string TimestampToDateTimeStr(long? timestamp)
        {
            if (!timestamp.HasValue)
                return "";

            // 將 timestamp 當作 UTC 時間，再轉為本地時間
            DateTime localTime = DateTimeOffset.FromUnixTimeSeconds(timestamp.Value).ToLocalTime().DateTime;

            return localTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    public class EncryptionHelper
    {
        private readonly string _key;
        private readonly string _iv;

        // 使用構造函數注入 IConfiguration
        public EncryptionHelper(IConfiguration configuration)
        {
            // 從 appsettings.json 中讀取密鑰和向量
            _key = configuration["UserPasswordKey"];
            _iv = configuration["UserPasswordIV"];
        }

        /// <summary>
        /// 加密字串
        /// </summary>
        /// <param name="plainText"></param>
        /// <returns></returns>
        public string EncryptString(string plainText)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes(_key);
                aesAlg.IV = Encoding.UTF8.GetBytes(_iv);

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }
                    byte[] encrypted = msEncrypt.ToArray();
                    return Convert.ToBase64String(encrypted);
                }
            }
        }

        /// <summary>
        /// 解密字串
        /// </summary>
        /// <param name="cipherText"></param>
        /// <returns></returns>
        public string DecryptString(string cipherText)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes(_key);
                aesAlg.IV = Encoding.UTF8.GetBytes(_iv);

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msDecrypt = new MemoryStream(Convert.FromBase64String(cipherText)))
                using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }
    }
}
