﻿using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface ISignalRMessageService
    {
        Task<(bool, string)> SendToAllAsync(NotificationDto notification);

        Task<(bool, string)> SendToUserIdGroupAsync(NotificationDto notification, string _userId);

        Task<(bool, string)> SendToConnectionIdAsync(NotificationDto notification, string _connectionId);

        Task<(bool, string)> SendTestMessageAsync(string _user, string _message, string _connectionId = "");

        
    }
}
