using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class PerformancePointRecordService : IPerformancePointRecordService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public PerformancePointRecordService(ERPDbContext context, Baseform baseform, ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<PerformancePointRecordDTO>> GetByUserIdAsync(string userId)
        {
            return await (
                from r in _context.Pas_PerformancePointRecord
                join t in _context.Pas_PerformancePointType on r.pointUid equals t.uid into rt
                from t in rt.DefaultIfEmpty()
                join g in _context.Pas_PerformancePointGroup on t.groupUid equals g.uid into tg
                from g in tg.DefaultIfEmpty()
                where !r.IsDeleted && r.userId == userId
                orderby r.pointDate descending
                select new PerformancePointRecordDTO
                {
                    uid = r.uid,
                    userId = r.userId,
                    pointDate = _baseform.TimestampToDateStr(r.pointDate),
                    point = r.point.ToString("0.##"),
                    remark = r.remark,
                    pointUid = r.pointUid,
                    pointName = t != null ? t.pointName : "",
                    groupUid = t != null ? t.groupUid : "",
                    groupName = g != null ? g.groupName : ""
                }
            ).ToListAsync();
        }

        public async Task<PerformancePointRecordDTO?> GetDetailAsync(string uid)
        {
            var record = await (
                from r in _context.Pas_PerformancePointRecord
                join t in _context.Pas_PerformancePointType on r.pointUid equals t.uid into rt
                from t in rt.DefaultIfEmpty()
                join g in _context.Pas_PerformancePointGroup on t.groupUid equals g.uid into tg
                from g in tg.DefaultIfEmpty()
                where r.uid == uid && !r.IsDeleted
                select new PerformancePointRecordDTO
                {
                    uid = r.uid,
                    userId = r.userId,
                    pointDate = _baseform.TimestampToDateStr(r.pointDate),
                    point = r.point.ToString("0.##"),
                    remark = r.remark,
                    pointUid = r.pointUid,
                    pointName = t != null ? t.pointName : "",
                    groupUid = t != null ? t.groupUid : "",
                    groupName = g != null ? g.groupName : ""
                }
            ).FirstOrDefaultAsync();

            return record;
        }

        public async Task<(bool, string)> AddAsync(PerformancePointRecordDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = new PerformancePointRecord
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = dto.userId,
                    pointDate = _baseform.DateStrToTimestamp(dto.pointDate),
                    pointUid = dto.pointUid,
                    point = decimal.Parse(dto.point),
                    remark = dto.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                _context.Pas_PerformancePointRecord.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增點數紀錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增點數紀錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditAsync(PerformancePointRecordDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_PerformancePointRecord.FirstOrDefaultAsync(x => x.uid == dto.uid);
                if (entity == null) return (false, "找不到對應點數紀錄資料");

                entity.userId = dto.userId;
                entity.pointDate = _baseform.DateStrToTimestamp(dto.pointDate);
                entity.pointUid = dto.pointUid;
                entity.point = decimal.Parse(dto.point);
                entity.remark = dto.remark;
                entity.UpdateUserId = _currentUserService.UserId;
                entity.UpdateTime = _baseform.GetCurrentLocalTimestamp();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "更新點數紀錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新點數紀錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_PerformancePointRecord.FirstOrDefaultAsync(x => x.uid == uid);
                if (entity == null) return (false, "找不到對應點數紀錄資料");

                entity.IsDeleted = true;
                entity.DeleteUserId = _currentUserService.UserId;
                entity.DeleteTime = _baseform.GetCurrentLocalTimestamp();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除點數紀錄完成");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除點數紀錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<List<PerformancePointSummaryResult>> GetPerformancePointSummaryAsync(PerformancePointSummaryQuery query)
        {
            var start = _baseform.DateStrToTimestamp(query.StartDate) ?? 0;
            var end = _baseform.DateStrToTimestamp(query.EndDate) ?? long.MaxValue;

            var result = await (
                from r in _context.Pas_PerformancePointRecord
                join u in _context.Common_Users on r.userId equals u.UserId
                join t in _context.Pas_PerformancePointType on r.pointUid equals t.uid into rt
                from t in rt.DefaultIfEmpty()
                where !r.IsDeleted
                    && r.pointDate >= start
                    && r.pointDate <= end
                    && (string.IsNullOrEmpty(query.GroupUid) || t.groupUid == query.GroupUid)
                group r by new { r.userId, u.Name } into g
                orderby g.Sum(x => x.point) descending
                select new PerformancePointSummaryResult
                {
                    userId = g.Key.userId,
                    userName = g.Key.Name,
                    totalPoint = g.Sum(x => x.point)
                }
            ).ToListAsync();

            return result;
        }

    }
}


