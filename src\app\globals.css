/* 全域樣式
  /src/app/globals.css
*/

:root {
  --background: #ffffff;
  --foreground: #171717;
}

body {
  margin    : 0;
  padding   : 0;
  background: var(--background);
  color     : var(--foreground);
}

/* 手機版列表樣式 - 優化版 */
.mobile-list {
  padding: 12px 8px;
  background: #f8f9fa;
}

.mobile-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.mobile-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.mobile-title {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  word-break: break-word;
  line-height: 1.4;
  color: #262626;
}

.mobile-content {
  font-size: 14px;
  line-height: 1.5;
}

.mobile-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
  min-height: 24px;
}

.mobile-row strong {
  color: #666;
  min-width: 80px;
  font-size: 13px;
  flex-shrink: 0;
}

.mobile-row .ant-tag {
  margin: 0;
  display: inline-flex;
  align-items: center;
  font-size: 12px;
}

.mobile-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f5f5f5;
}

/* 響應式優化 */
@media screen and (max-width: 768px) {
  /* 通用間距優化 */
  .ant-space {
    gap: 8px !important;
    width: 100%;
  }

  .ant-space-item {
    flex: 1;
  }

  /* 搜尋框優化 */
  .ant-input-search {
    width: 100% !important;
    min-width: 200px;
  }

  /* 分頁優化 */
  .ant-pagination {
    width: 100%;
    text-align: center;
    margin-top: 16px;
  }

  .ant-pagination-options {
    display: flex;
    justify-content: center;
    margin-top: 8px;
    flex-wrap: wrap;
  }

  /* 表格優化 */
  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
}

/* 小螢幕優化 (576px 以下) */
@media screen and (max-width: 576px) {
  .mobile-list {
    padding: 8px 4px;
  }

  .mobile-item {
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
  }

  .mobile-title {
    font-size: 15px;
  }

  .mobile-content {
    font-size: 13px;
  }

  .mobile-row strong {
    min-width: 70px;
    font-size: 12px;
  }

  .mobile-actions {
    gap: 6px;
    margin-top: 8px;
    padding-top: 8px;
  }

  /* 按鈕優化 */
  .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
  }

  .ant-btn-sm {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 表單優化 */
  .ant-form-item {
    margin-bottom: 12px;
  }

  .ant-form-item-label {
    padding-bottom: 2px;
  }

  /* 卡片優化 */
  .ant-card {
    margin-bottom: 8px;
  }

  .ant-card-body {
    padding: 12px;
  }

  .ant-card-head {
    padding: 0 12px;
    min-height: 40px;
  }

  .ant-card-head-title {
    font-size: 14px;
  }
}

/* 觸控優化 */
@media (hover: none) and (pointer: coarse) {
  .mobile-item {
    cursor: pointer;
  }

  .mobile-item:active {
    background: #f8f9fa;
    transform: scale(0.98);
  }

  /* 增大觸控目標 */
  .ant-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .ant-input {
    min-height: 44px;
  }

  .ant-select-selector {
    min-height: 44px;
  }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
  .mobile-list {
    background: #141414;
  }

  .mobile-item {
    background: #1f1f1f;
    border-color: #303030;
    color: #ffffff;
  }

  .mobile-title {
    color: #ffffff;
  }

  .mobile-row strong {
    color: #a6a6a6;
  }
}


/* pas用刪除全域row特效 */
.row-deleting-pulse {
  position: relative;
  overflow: hidden;
  background: linear-gradient(120deg,
      #fff5f5 10%,
      #ffe5e5 25%,
      #ffd6d6 37%,
      #ffcaca 50%,
      #ffd6d6 63%,
      #ffe5e5 75%,
      #fff5f5 90%);
  background-size: 300% 100%;
  animation      : pulse-red-glow 2s ease-in-out infinite;
  z-index        : 0;
}

.row-deleting-pulse td {
  position: relative;
  z-index : 1;
}

@keyframes pulse-red-glow {
  0% {
    background-position: 300% 0;
  }

  50% {
    background-position: 150% 0;
  }

  100% {
    background-position: 0 0;
  }
}


/* pas用刪除全域card特效 */
.card-deleting {
  border         : 2px solid transparent !important;
  background-clip: padding-box;
  position       : relative;
  animation      : red-border-glow 2s ease-in-out infinite;
  z-index        : 0;
}

.card-deleting::before {
  content : "";
  position: absolute;
  top     : -2px;
  bottom  : -2px;
  left    : -2px;
  right   : -2px;
  z-index : -1;
  background: linear-gradient(120deg,
      #fff5f5,
      #ffdada,
      #ffc0c0,
      #ffdada,
      #fff5f5);
  background-size: 300% 100%;
  animation      : pulse-red-border 2s ease-in-out infinite;
  border-radius  : 8px;
}

@keyframes pulse-red-border {
  0% {
    background-position: 300% 0;
  }

  50% {
    background-position: 150% 0;
  }

  100% {
    background-position: 0 0;
  }
}

/* Tabs 相關樣式優化 */
.tabs-container .ant-tabs-nav {
  margin-bottom: 0 !important;
  background   : transparent;
}

.tabs-container .ant-tabs-nav::before {
  border-bottom: none !important;
}

.tabs-container .ant-tabs-tab {
  transition   : all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px !important;
  border       : none !important;
  background   : transparent !important;
  padding      : 8px 16px !important;
  position     : relative;
}

.tabs-container .ant-tabs-tab:hover {
  color: #1890ff !important;
}

.tabs-container .ant-tabs-tab-active {
  background: rgba(24, 144, 255, 0.1) !important;
}

.tabs-container .ant-tabs-tab-active .ant-tabs-tab-btn {
  color      : #1890ff !important;
  font-weight: 600 !important;
}

.tabs-container .ant-tabs-content {
  height    : 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-content {
  position  : relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 內容區塊淡入淡出效果 */
.tab-content>div {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity   : 0;
  transform : translateY(10px);
}

.tab-content>div[style*="display: block"] {
  opacity  : 1;
  transform: translateY(0);
}

/* 自定義滾動條優化 */
.tabs-container ::-webkit-scrollbar {
  width : 6px;
  height: 6px;
}

.tabs-container ::-webkit-scrollbar-track {
  background   : #f1f1f1;
  border-radius: 3px;
}

.tabs-container ::-webkit-scrollbar-thumb {
  background   : #c1c1c1;
  border-radius: 3px;
}

.tabs-container ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 空狀態樣式優化 */
.empty-state {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-state svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-state:hover svg {
  transform: scale(1.05);
}

/* Tab 內容區塊陰影效果 */
.tab-content::before {
  content       : '';
  position      : absolute;
  top           : 0;
  left          : 0;
  right         : 0;
  height        : 40px;
  background    : linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
  pointer-events: none;
  opacity       : 0;
  transition    : opacity 0.3s ease;
}

.tab-content:hover::before {
  opacity: 1;
}

/* Tab 切換時的載入動畫 */
@keyframes tabFadeIn {
  from {
    opacity  : 0;
    transform: translateY(10px);
  }

  to {
    opacity  : 1;
    transform: translateY(0);
  }
}

.tab-content>div[style*="display: block"] {
  animation: tabFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 標籤頁標題樣式 */
.tabs-header h3 {
  position: relative;
  display : inline-block;
}

.tabs-header h3::after {
  content      : '';
  position     : absolute;
  bottom       : -4px;
  left         : 0;
  width        : 40px;
  height       : 2px;
  background   : #1890ff;
  border-radius: 2px;
}