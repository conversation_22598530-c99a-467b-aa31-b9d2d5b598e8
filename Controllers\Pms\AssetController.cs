﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("財產資料管理")]
    public class AssetController : ControllerBase
    {
        private readonly IAssetService _Interface;

        public AssetController(IAssetService assetService)
        {
            _Interface = assetService;
        }

        [HttpGet]
        [Route("GetNewAssetNo")]
        [SwaggerOperation(Summary = "取得新的財產編號", Description = "根據科目、子目和類別生成新的財產編號")]
        public async Task<IActionResult> GetNewAssetNo([FromQuery] AssetNoRequestDTO request)
        {
            // ModelState.IsValid 會自動檢查驗證規則
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _Interface.GetNewAssetNoAsync(request.Subject, request.SubSubject, request.Category);
            if (!result.success)
                return BadRequest(result.message);

            return Ok(new { success = result.success, assetNo = result.assetNo, message = result.message });
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得所有資產", Description = "取得所有資產列表")]
        public async Task<IActionResult> GetAssets()
        {
            var assets = await _Interface.GetAssetAsync();
            return Ok(assets);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得資產詳細資料", Description = "根據ID取得資產詳細資料")]
        public async Task<IActionResult> GetAssetDetail(string id)
        {
            var asset = await _Interface.GetAssetDetailAsync(id);
            if (asset == null)
                return NotFound($"找不到編號為{id}的資產");

            return Ok(asset);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增資產", Description = "新增單一資產")]
        public async Task<IActionResult> AddAsset([FromBody] AssetWithAccessoriesDTO _data)
        {
            var (result, msg) = await _Interface.AddAssetAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯資產", Description = "編輯資產資料")]
        public async Task<IActionResult> EditAsset([FromBody] AssetWithAccessoriesDTO asset)
        {
            var result = await _Interface.EditAssetAsync(asset);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除資產", Description = "刪除資產資料")]
        public async Task<IActionResult> DeleteAsset([FromBody] AssetWithAccessoriesDTO asset)
        {
            var result = await _Interface.DeleteAssetAsync(asset);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("ValidateExcelFile")]
        [SwaggerOperation(Summary = "驗證批次轉檔Excel檔案", Description = "上傳Excel檔案進行格式驗證")]
        public async Task<IActionResult> ValidateExcelFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "請選擇要上傳的檔案" });
                }

                // 檢查檔案格式
                var allowedExtensions = new[] { ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { success = false, message = "檔案格式不正確，請上傳Excel檔案(.xlsx或.xls)" });
                }

                // 檢查檔案大小（限制10MB）
                if (file.Length > 10 * 1024 * 1024)
                {
                    return BadRequest(new { success = false, message = "檔案大小不能超過10MB" });
                }

                using (var stream = file.OpenReadStream())
                {
                    var validationResult = await _Interface.ValidateExcelFileAsync(stream);
                    return Ok(new { success = validationResult.IsValid, data = validationResult });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"檔案驗證發生錯誤：{ex.Message}" });
            }
        }

        [HttpPost]
        [Route("BatchImport")]
        [SwaggerOperation(Summary = "批次匯入資產資料", Description = "上傳Excel檔案進行批次資產匯入")]
        public async Task<IActionResult> BatchImportAssets(IFormFile file, [FromQuery] string userId = "system")
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "請選擇要上傳的檔案" });
                }

                // 檢查檔案格式
                var allowedExtensions = new[] { ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { success = false, message = "檔案格式不正確，請上傳Excel檔案(.xlsx或.xls)" });
                }

                // 檢查檔案大小（限制10MB）
                if (file.Length > 10 * 1024 * 1024)
                {
                    return BadRequest(new { success = false, message = "檔案大小不能超過10MB" });
                }

                using (var stream = file.OpenReadStream())
                {
                    var importResult = await _Interface.BatchImportAssetsAsync(stream, userId);
                    return Ok(new { success = importResult.Success, data = importResult });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"批次匯入發生錯誤：{ex.Message}" });
            }
        }

        [HttpGet]
        [Route("DownloadBatchTemplate")]
        [SwaggerOperation(Summary = "下載批次轉檔範本", Description = "下載Excel批次轉檔範本檔案")]
        public async Task<IActionResult> DownloadBatchTemplate()
        {
            try
            {
                var (fileBytes, fileName) = await _Interface.DownloadBatchTemplateAsync();

                return File(fileBytes,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"下載範本發生錯誤：{ex.Message}" });
            }
        }
    }
}