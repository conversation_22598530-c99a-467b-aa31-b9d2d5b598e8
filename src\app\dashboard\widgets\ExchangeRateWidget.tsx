"use client";

import React, { useState, useEffect } from "react";
import { Card, Table, Spin, Typography, Tag, Space, Button } from "antd";
import { ReloadOutlined } from "@ant-design/icons";

const { Text } = Typography;

interface ExchangeRate {
  currency: string;
  currencyName: string;
  flag: string;
  buySpot: number;
  sellSpot: number;
  buyCash?: number;
  sellCash?: number;
  change?: number;
  changePercent?: number;
}

const ExchangeRateWidget: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [exchangeRates, setExchangeRates] = useState<ExchangeRate[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string>("");
  const [isRealTime, setIsRealTime] = useState(false);

  const fetchExchangeRates = async () => {
    try {
      setLoading(true);
      const response = await fetch("/dashboard/api/exchange-rates");
      const result = await response.json();

      if (result.success && result.data.length > 0) {
        setExchangeRates(result.data);
        setLastUpdate(new Date().toLocaleString("zh-TW"));
        setIsRealTime(true);
      }
    } catch (error) {
      console.error("獲取匯率失敗:", error);
      setLastUpdate(new Date().toLocaleString("zh-TW"));
      setIsRealTime(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 初始載入
    fetchExchangeRates();

    // 每5分鐘更新一次
    const interval = setInterval(fetchExchangeRates, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const columns = [
    {
      title: "幣別",
      dataIndex: "currency",
      key: "currency",
      width: "30%",
      render: (text: string, record: ExchangeRate) => (
        <Space>
          <span style={{ fontSize: "16px" }}>{record.flag}</span>
          <div>
            <div style={{ fontWeight: 600 }}>{text}</div>
            <div style={{ fontSize: "12px", color: "#666" }}>
              {record.currencyName}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "即期買入",
      dataIndex: "buySpot",
      key: "buySpot",
      width: "35%",
      render: (value: number) => (
        <Text style={{ fontWeight: 500, fontSize: "14px" }}>
          {value ? value.toFixed(4) : "--"}
        </Text>
      ),
    },
    {
      title: "即期賣出",
      dataIndex: "sellSpot",
      key: "sellSpot",
      width: "35%",
      render: (value: number) => (
        <Text style={{ fontWeight: 500, fontSize: "14px" }}>
          {value ? value.toFixed(4) : "--"}
        </Text>
      ),
    },
  ];

  if (loading) {
    return (
      <Card className="dashboard-widget-card" style={{ height: "100%" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "200px",
          }}
        >
          <Spin>
            <div style={{ padding: "20px", textAlign: "center" }}>
              載入匯率資料...
            </div>
          </Spin>
        </div>
      </Card>
    );
  }

  return (
    <Card className="dashboard-widget-card" style={{ height: "100%" }}>
      <div style={{ marginBottom: "16px" }}>
        <Space style={{ width: "100%", justifyContent: "space-between" }}>
          <div>
            <Text strong style={{ fontSize: "16px" }}>
              臺銀牌告匯率匯率
            </Text>
            <div>
              <Tag
                color={isRealTime ? "green" : "orange"}
                style={{ fontSize: "14px", marginTop: "4px" }}
              >
                即時資料
              </Tag>
            </div>
          </div>
          <Button
            icon={<ReloadOutlined />}
            size="small"
            onClick={fetchExchangeRates}
            loading={loading}
            title="重新整理匯率"
          />
        </Space>
        <Text style={{ fontSize: "12px", color: "#666" }}>
          更新時間：{lastUpdate || new Date().toLocaleString("zh-TW")}
        </Text>
      </div>

      <Table
        dataSource={exchangeRates}
        columns={columns}
        pagination={false}
        size="small"
        rowKey="currency"
        style={{ fontSize: "13px" }}
        scroll={{ y: 300 }}
      />

      <div style={{ marginTop: "12px", textAlign: "center" }}>
        <Text style={{ fontSize: "11px", color: "#999" }}>
          ※ 本資料僅供參考，實際交易匯率以交易時本行匯率為準
        </Text>
      </div>
    </Card>
  );
};

export default ExchangeRateWidget;
