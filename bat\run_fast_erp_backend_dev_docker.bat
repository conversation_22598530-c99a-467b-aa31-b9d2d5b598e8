@echo off
chcp 65001
setlocal enabledelayedexpansion

echo =====================================================
echo     Fast ERP Backend Docker 測試環境部署腳本
echo =====================================================
echo.

:INPUT_PATH
echo 請輸入專案根目錄的完整路徑
echo 範例：D:\fast_erp\fast_erp_backend
echo 注意：路徑中不要包含引號
echo.
set /p PROJECT_ROOT=請輸入路徑: 

:: 移除路徑首尾的引號（如果有的話）
set PROJECT_ROOT=%PROJECT_ROOT:"=%

:: 檢查路徑是否存在
if not exist "%PROJECT_ROOT%" (
    echo.
    echo [錯誤] 找不到指定的目錄：%PROJECT_ROOT%
    echo 請確認路徑是否正確。
    echo.
    goto INPUT_PATH
)

:: 切換到專案目錄
cd /d "%PROJECT_ROOT%"

:: 檢查 Dockerfile.dev 是否存在
if not exist "Dockerfile.dev" (
    echo.
    echo [錯誤] 在指定目錄中找不到 Dockerfile.dev
    echo 當前目錄: %CD%
    echo 請確認：
    echo 1. 路徑是否正確
    echo 2. Dockerfile.dev 檔案是否存在
    echo.
    goto INPUT_PATH
)

:: 確認路徑
echo.
echo 已選擇的專案路徑：
echo %PROJECT_ROOT%
echo.
set /p CONFIRM=請確認路徑是否正確？(Y/N): 
if /i not "%CONFIRM%"=="Y" goto INPUT_PATH

:: 檢查 Docker 是否正在運行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [錯誤] Docker 未運行或無法連接！
    echo 請確認 Docker Desktop 已啟動。
    pause
    exit /b 1
)

:: 停止並移除舊容器
echo.
echo [步驟 1/4] 停止並移除舊容器...
docker stop fast_erp_backend_dev >nul 2>&1
docker rm fast_erp_backend_dev >nul 2>&1
echo [完成] 舊容器清理完成
echo.

:: 建構 Docker 映像
echo [步驟 2/4] 建構 Docker 映像...
echo 當前目錄: %CD%
echo 開始建構映像...
docker build -t fast_erp_backend:dev -f Dockerfile.dev .
if %errorlevel% neq 0 (
    echo [錯誤] Docker 映像建構失敗！
    pause
    exit /b 1
)
echo [完成] Docker 映像建構成功
echo.

:: 啟動新容器
echo [步驟 3/4] 啟動新容器...
echo 正在啟動容器...

docker run -d ^
    --name fast_erp_backend_dev ^
    -v "%PROJECT_ROOT%:/app" ^
    -v "/app/obj" ^
    -v "/app/bin" ^
    -v "%USERPROFILE%\.aspnet\https:/https/:ro" ^
    -p 7136:80 ^
    -p 7137:443 ^
    -e "ASPNETCORE_ENVIRONMENT=Development" ^
    -e "ASPNETCORE_URLS=https://+:443;http://+:80" ^
    -e "ASPNETCORE_Kestrel__Certificates__Default__Password=1234" ^
    -e "ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx" ^
    fast_erp_backend:dev

if %errorlevel% neq 0 (
    echo [錯誤] 容器啟動失敗！
    pause
    exit /b 1
)

:: 等待容器完全啟動
echo 等待容器啟動中...
timeout /t 5 /nobreak >nul

:: 檢查容器狀態
docker ps --filter "name=fast_erp_backend_dev" --filter "status=running" >nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 容器未能正常運行！
    echo 顯示錯誤日誌：
    docker logs fast_erp_backend_dev
    pause
    exit /b 1
)

:: 顯示成功信息
echo [完成] 容器啟動成功！
echo.
echo [步驟 4/4] 設定完成
echo =====================================================
echo HTTP 網址: http://localhost:7136
echo HTTPS 網址: https://localhost:7137
echo =====================================================
echo.

:: 顯示容器日誌
echo 正在顯示容器日誌（按 Ctrl+C 可停止查看）...
echo =====================================================
docker logs -f fast_erp_backend_dev

pause