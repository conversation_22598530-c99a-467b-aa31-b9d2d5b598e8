using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IDistrictService
    {
        /// <summary>
        /// 取得鄉鎮市區資料
        /// </summary>
        /// <param name="_districtId">鄉鎮市區編號</param>
        /// <returns>鄉鎮市區資料列表</returns>
        Task<List<DistrictDTO>> GetDistrictAsync(string _districtId = "");

        /// <summary>
        /// 新增鄉鎮市區
        /// </summary>
        /// <param name="_data">鄉鎮市區資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddDistrictAsync(DistrictDTO _data);

        /// <summary>
        /// 編輯鄉鎮市區
        /// </summary>
        /// <param name="_data">鄉鎮市區資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditDistrictAsync(DistrictDTO _data);

        /// <summary>
        /// 刪除鄉鎮市區
        /// </summary>
        /// <param name="_data">鄉鎮市區資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteDistrictAsync(DistrictDTO _data);

        /// <summary>
        /// 取得鄉鎮市區詳細資料
        /// </summary>
        /// <param name="_districtId">鄉鎮市區編號</param>
        /// <returns>鄉鎮市區詳細資料</returns>
        Task<DistrictDTO> GetDistrictDetailAsync(string _districtId);

        /// <summary>
        /// 依縣市取得鄉鎮市區資料
        /// </summary>
        /// <param name="_cityId">縣市編號</param>
        /// <returns>鄉鎮市區資料列表</returns>
        Task<List<DistrictDTO>> GetDistrictByCityAsync(string _cityId);
    }
}