# 使用官方 .NET 8 SDK 建置應用程式
FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /app

# 安裝 vsdbg 偵錯工具
RUN apt-get update \
    && apt-get install -y --no-install-recommends unzip procps \
    && rm -rf /var/lib/apt/lists/* \
    && curl -sSL https://aka.ms/getvsdbgsh | bash /dev/stdin -v latest -l /vsdbg

COPY ["*.csproj", "./"]
RUN dotnet restore

# 設定環境變數
ENV ASPNETCORE_URLS="https://+:443;http://+:80"
ENV ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_Kestrel__Certificates__Default__Password="1234"
ENV ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
ENV VSTEST_HOST_DEBUG=1

# 創建憑證目錄
RUN mkdir -p /https

# 複製所有原始碼
COPY . .

# 使用 watch 來監視文件變化
ENTRYPOINT ["dotnet", "watch", "--no-hot-reload", "run", "--urls", "https://+:443;http://+:80"]

EXPOSE 80 443