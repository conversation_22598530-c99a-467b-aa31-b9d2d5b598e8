---
description: 
globs: 
alwaysApply: false
---
# 開發指南

## 專案結構
- `Controllers/` - REST API 端點實現
- `Models/` - 資料庫模型和 DTO
- `Services/` - 業務邏輯實現
- `Interfaces/` - 服務介面定義
- `Middlewares/` - 自定義中間件
- `Tools/` - 工具類和輔助函數
- `Hubs/` - SignalR 實時通訊

## 編碼規範
### 註解規則
- 在註解中使用 `Pms` 時應寫作「財產」並非「資產」
- 例如：`// 取得財產資料`

## 資料庫
- `Migrations/` - Entity Framework Core 遷移文件
- [defaultData.json](mdc:defaultData.json) - 預設資料
- 使用 [reset-ef-database.bat](mdc:reset-ef-database.bat) 重置資料庫

## Docker 開發環境
1. 使用 [Dockerfile.dev](mdc:Dockerfile.dev) 建構開發容器
2. 使用 [docker-compose.yaml](mdc:docker-compose.yaml) 啟動完整開發環境

## API 測試
- [FAST_ERP_Backend.http](mdc:FAST_ERP_Backend.http) - API 測試範例

