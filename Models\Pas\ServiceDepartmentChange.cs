using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 服務部門異動資料表
    /// </summary>
    public class ServiceDepartmentChange : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string Uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用者編號

        [Comment("服務部門編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ServiceDepartmentId { get; set; } // 服務部門編號

        [Comment("服務組別編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ServiceDivisionId { get; set; } // 服務組別編號

        [Comment("異動日期")]
        [Column(TypeName = "bigint")]
        public long? ChangeDate { get; set; } // 異動日期

        [Comment("生效日期")]
        [Column(TypeName = "bigint")]
        public long? EffectiveDate { get; set; } // 生效日期

        [Comment("異動原因")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string ChangeReason { get; set; } // 異動原因

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Remark { get; set; } // 備註

        public ServiceDepartmentChange()
        {
            Uid = "";
            UserId = "";
            ServiceDepartmentId = "";
            ServiceDivisionId = "";
            ChangeDate = null;
            EffectiveDate = null;
            ChangeReason = "";
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class ServiceDepartmentChangeDTO : ModelBaseEntityDTO
    {
        public string Uid { get; set; } // 資料編號
        public string UserId { get; set; } // 使用者編號
        public string ServiceDepartmentId { get; set; } // 服務部門編號
        public string ServiceDepartmentName { get; set; } // 服務部門名稱
        public string ServiceDivisionId { get; set; } // 服務組別編號
        public string ServiceDivisionName { get; set; } // 服務組別名稱
        public string ChangeDate { get; set; } // 異動日期
        public string EffectiveDate { get; set; } // 生效日期
        public string ChangeReason { get; set; } // 異動原因
        public string Remark { get; set; } // 備註

        public ServiceDepartmentChangeDTO()
        {
            Uid = "";
            UserId = "";
            ServiceDepartmentId = "";
            ServiceDepartmentName = "";
            ServiceDivisionId = "";
            ServiceDivisionName = "";
            ChangeDate = "";
            EffectiveDate = "";
            ChangeReason = "";
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}