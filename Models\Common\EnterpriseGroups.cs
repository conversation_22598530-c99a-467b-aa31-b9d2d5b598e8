﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 公司群組資料表
    /// </summary>
    public class EnterpriseGroups : ModelBaseEntity
    {
        [Key]
        [Comment("公司編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string EnterpriseGroupsId { get; set; } // 公司編號

        [Required]
        [Comment("公司名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string Name { get; set; } // 公司名稱

        [Column(TypeName = "nvarchar(20)")]
        [Comment("統一編號")]
        public string UnifiedNumber { get; set; } // 統一編號

        [Column(TypeName = "nvarchar(50)")]
        [Comment("公司負責人")]
        public string Representative { get; set; } // 公司負責人

        [Column(TypeName = "bigint")]
        [Comment("成立日期")]
        public long EstablishDate { get; set; } // 成立日期

        [Column(TypeName = "nvarchar(50)")]
        [Comment("結算週期")]
        public string AccountingPeriod { get; set; } // 結算週期

        [Column(TypeName = "nvarchar(20)")]
        [Comment("公司電話")]
        public string CompanyPhone { get; set; } // 公司電話

        [Column(TypeName = "nvarchar(20)")]
        [Comment("電話")]
        public string Phone { get; set; } // 電話

        [Column(TypeName = "nvarchar(100)")]
        [Comment("電子郵件")]
        public string Email { get; set; } // 電子郵件

        [Column(TypeName = "nvarchar(20)")]
        [Comment("手機號碼")]
        public string MobilePhone { get; set; } // 手機號碼

        [Column(TypeName = "nvarchar(20)")]
        [Comment("傳真")]
        public string Fax { get; set; } // 傳真

        [Column(TypeName = "nvarchar(200)")]
        [Comment("網頁")]
        public string Website { get; set; } // 網頁

        [Column(TypeName = "nvarchar(200)")]
        [Comment("地址1")]
        public string Address1 { get; set; } // 地址1

        [Column(TypeName = "nvarchar(200)")]
        [Comment("地址2")]
        public string Address2 { get; set; } // 地址2

        [Column(TypeName = "nvarchar(100)")]
        [Comment("公司英文名稱")]
        public string EnglishName { get; set; } // 公司英文名稱

        [Column(TypeName = "nvarchar(200)")]
        [Comment("英文地址")]
        public string EnglishAddress { get; set; } // 英文地址

        [Column(TypeName = "int")]
        [Comment("排序號碼")]
        public int SortCode { get; set; } = 0; // 排序號碼

        public EnterpriseGroups()
        {
            EnterpriseGroupsId = "";
            Name = "";
            UnifiedNumber = "";
            Representative = "";
            EstablishDate = 0;
            AccountingPeriod = "";
            CompanyPhone = "";
            Phone = "";
            Email = "";
            MobilePhone = "";
            Fax = "";
            Website = "";
            Address1 = "";
            Address2 = "";
            EnglishName = "";
            EnglishAddress = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class EnterpriseGroupsDTO : ModelBaseEntityDTO
    {
        public string EnterpriseGroupsId { get; set; } // 公司編號
        public string Name { get; set; } // 公司名稱
        public string UnifiedNumber { get; set; } // 統一編號
        public string Representative { get; set; } // 公司負責人
        public long EstablishDate { get; set; } // 成立日期
        public string AccountingPeriod { get; set; } // 結算週期
        public string CompanyPhone { get; set; } // 公司電話
        public string Phone { get; set; } // 電話
        public string Email { get; set; } // 電子郵件
        public string MobilePhone { get; set; } // 手機號碼
        public string Fax { get; set; } // 傳真
        public string Website { get; set; } // 網頁
        public string Address1 { get; set; } // 地址1
        public string Address2 { get; set; } // 地址2
        public string EnglishName { get; set; } // 公司英文名稱
        public string EnglishAddress { get; set; } // 英文地址
        public int SortCode { get; set; } = 0; // 排序號碼

        public EnterpriseGroupsDTO()
        {
            EnterpriseGroupsId = "";
            Name = "";
            UnifiedNumber = "";
            Representative = "";
            EstablishDate = 0;
            AccountingPeriod = "";
            CompanyPhone = "";
            Phone = "";
            Email = "";
            MobilePhone = "";
            Fax = "";
            Website = "";
            Address1 = "";
            Address2 = "";
            EnglishName = "";
            EnglishAddress = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}
