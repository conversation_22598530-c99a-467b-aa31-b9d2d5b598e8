"use client";

import { useState } from "react";
import { Card, Form, Input, Button } from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { routes } from "@/config/routes";
import Logo from "@/app/components/common/Logo";
import { notifySuccess, notifyError } from "@/utils/notification";
import styles from "./login.module.css";

export default function LoginPage() {
  const router = useRouter();
  const { login } = useAuth();
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      const result = await login(values.username, values.password);

      if (!result.success) {
        notifyError("登入失敗", result.message || "請檢查帳號密碼是否正確");
      } else {
        const userName = result.data?.name || "使用者";
        notifySuccess("登入成功", `${userName}，歡迎回來`, { duration: 1 });
        // 轉址至dashboard
        router.push(routes.dashboard);
      }
    } catch (error) {
      notifyError("登入錯誤", "發生未預期的錯誤");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.loginContainer}>
      <Card
        className={styles.loginCard}
        title={<Logo className={styles.cardTitle} imageSize={40} />}
      >
        <Form
          name="login"
          initialValues={{ remember: true, username: "FastAdmin" }}
          onFinish={onFinish}
          size="large"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: "請輸入帳號" }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="帳號"
              autoComplete="username"
              disabled={loading}
            />
          </Form.Item>

          <Form.Item
            name="password"
            initialValue="fast!234"
            rules={[{ required: true, message: "請輸入密碼" }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密碼"
              autoComplete="current-password"
              disabled={loading}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className={styles.loginButton}
              loading={loading}
              block
            >
              {loading ? "登入中..." : "登入"}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
