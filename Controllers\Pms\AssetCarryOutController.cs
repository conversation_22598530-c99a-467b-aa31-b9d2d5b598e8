using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    /// <summary>
    /// 資產攜出作業控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("資產攜出作業管理")]
    public class AssetCarryOutController : ControllerBase
    {
        private readonly IAssetCarryOutService _assetCarryOutService;

        public AssetCarryOutController(IAssetCarryOutService assetCarryOutService)
        {
            _assetCarryOutService = assetCarryOutService;
        }

        /// <summary>
        /// 取得攜出申請列表
        /// </summary>
        /// <param name="status">狀態篩選</param>
        /// <param name="applicantId">申請人篩選</param>
        /// <param name="assetId">財產篩選</param>
        /// <returns>攜出申請列表</returns>
        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得攜出申請列表", Description = "取得攜出申請列表，可依狀態、申請人、財產進行篩選")]
        public async Task<IActionResult> GetCarryOutList(
            [FromQuery] string? status = null,
            [FromQuery] string? applicantId = null,
            [FromQuery] Guid? assetId = null)
        {
            try
            {
                var result = await _assetCarryOutService.GetCarryOutListAsync(status, applicantId, assetId);
                return Ok(new { success = true, message = "取得攜出申請列表成功", data = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"取得攜出申請列表失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 取得攜出申請詳細資料
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <returns>攜出申請詳細資料</returns>
        [HttpGet]
        [Route("{carryOutNo}")]
        [SwaggerOperation(Summary = "取得攜出申請詳細資料", Description = "根據攜出單號取得攜出申請詳細資料")]
        public async Task<IActionResult> GetCarryOutDetail([FromRoute] string carryOutNo)
        {
            try
            {
                var result = await _assetCarryOutService.GetCarryOutDetailAsync(carryOutNo);

                if (result == null)
                {
                    return NotFound(new { success = false, message = "找不到指定的攜出申請" });
                }

                return Ok(new { success = true, message = "取得攜出申請詳細資料成功", data = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"取得攜出申請詳細資料失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 新增攜出申請
        /// </summary>
        /// <param name="carryOutDto">攜出申請資料</param>
        /// <returns>建立結果</returns>
        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增攜出申請", Description = "建立新的資產攜出申請")]
        public async Task<IActionResult> CreateCarryOutApplication([FromBody] AssetCarryOutDTO carryOutDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "資料驗證失敗", errors = ModelState });
                }

                var (success, message) = await _assetCarryOutService.CreateCarryOutApplicationAsync(carryOutDto);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"建立攜出申請失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 修改攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="carryOutDto">攜出申請資料</param>
        /// <returns>修改結果</returns>
        [HttpPost]
        [SwaggerOperation(Summary = "修改攜出申請", Description = "修改現有的攜出申請（僅限待審核狀態）")]
        public async Task<IActionResult> UpdateCarryOutApplication(
            [FromRoute] string carryOutNo,
            [FromBody] AssetCarryOutDTO carryOutDto)
        {
            try
            {
                if (carryOutNo != carryOutDto.CarryOutNo)
                {
                    return BadRequest(new { success = false, message = "路徑參數與請求資料中的攜出單號不符" });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "資料驗證失敗", errors = ModelState });
                }

                var (success, message) = await _assetCarryOutService.UpdateCarryOutApplicationAsync(carryOutDto);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"修改攜出申請失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 刪除攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="userId">操作人員</param>
        /// <returns>刪除結果</returns>
        [HttpPost]
        [Route("{carryOutNo}")]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除攜出申請", Description = "刪除攜出申請（僅限待審核或已駁回狀態）")]
        public async Task<IActionResult> DeleteCarryOutApplication(
            [FromRoute] string carryOutNo,
            [FromQuery, Required] string userId)
        {
            try
            {
                var (success, message) = await _assetCarryOutService.DeleteCarryOutApplicationAsync(carryOutNo, userId);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"刪除攜出申請失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 審核攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="approvalRequest">審核請求</param>
        /// <returns>審核結果</returns>
        [HttpPost]
        [Route("{carryOutNo}/approve")]
        [SwaggerOperation(Summary = "審核攜出申請", Description = "審核攜出申請（核准或駁回）")]
        public async Task<IActionResult> ApproveCarryOutApplication(
            [FromRoute] string carryOutNo,
            [FromBody] ApprovalRequestDto approvalRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "資料驗證失敗", errors = ModelState });
                }

                var (success, message) = await _assetCarryOutService.ApproveCarryOutApplicationAsync(
                    carryOutNo,
                    approvalRequest.IsApproved,
                    approvalRequest.ApproverId,
                    approvalRequest.Comment);

                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"審核攜出申請失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 登記攜出
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="carryOutRequest">攜出登記請求</param>
        /// <returns>登記結果</returns>
        [HttpPost]
        [Route("{carryOutNo}/carry-out")]
        [SwaggerOperation(Summary = "登記攜出", Description = "登記實際攜出資產")]
        public async Task<IActionResult> RegisterCarryOut(
            [FromRoute] string carryOutNo,
            [FromBody] CarryOutRequestDto carryOutRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "資料驗證失敗", errors = ModelState });
                }

                var (success, message) = await _assetCarryOutService.RegisterCarryOutAsync(
                    carryOutNo,
                    carryOutRequest.ActualCarryOutDate,
                    carryOutRequest.OperatorId);

                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"登記攜出失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 登記歸還
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="returnRequest">歸還登記請求</param>
        /// <returns>登記結果</returns>
        [HttpPost]
        [Route("{carryOutNo}/return")]
        [SwaggerOperation(Summary = "登記歸還", Description = "登記實際歸還資產")]
        public async Task<IActionResult> RegisterReturn(
            [FromRoute] string carryOutNo,
            [FromBody] ReturnRequestDto returnRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "資料驗證失敗", errors = ModelState });
                }

                var (success, message) = await _assetCarryOutService.RegisterReturnAsync(
                    carryOutNo,
                    returnRequest.ActualReturnDate,
                    returnRequest.OperatorId);

                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"登記歸還失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 批次處理攜出申請
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>處理結果</returns>
        [HttpPost]
        [Route("batch")]
        [SwaggerOperation(Summary = "批次處理攜出申請", Description = "批次審核、攜出或歸還")]
        public async Task<IActionResult> BatchProcess([FromBody] AssetCarryOutBatchDTO batchDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "資料驗證失敗", errors = ModelState });
                }

                var (success, message) = await _assetCarryOutService.BatchProcessAsync(batchDto);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"批次處理失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 取得攜出統計資料
        /// </summary>
        /// <param name="userId">使用者ID (可選，用於個人統計)</param>
        /// <returns>統計資料</returns>
        [HttpGet]
        [Route("statistics")]
        [SwaggerOperation(Summary = "取得攜出統計資料", Description = "取得攜出申請的統計資料")]
        public async Task<IActionResult> GetStatistics([FromQuery] string? userId = null)
        {
            try
            {
                var result = await _assetCarryOutService.GetStatisticsAsync(userId);
                return Ok(new { success = true, message = "取得攜出統計資料成功", data = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"取得攜出統計資料失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 檢查逾期未還的資產
        /// </summary>
        /// <returns>逾期未還的攜出申請列表</returns>
        [HttpGet]
        [Route("overdue")]
        [SwaggerOperation(Summary = "檢查逾期未還的資產", Description = "檢查所有逾期未還的攜出申請")]
        public async Task<IActionResult> CheckOverdueAssets()
        {
            try
            {
                var result = await _assetCarryOutService.CheckOverdueAssetsAsync();
                return Ok(new { success = true, message = "檢查逾期未還資產成功", data = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"檢查逾期未還資產失敗：{ex.Message}" });
            }
        }

        /// <summary>
        /// 產生攜出申請單號
        /// </summary>
        /// <returns>攜出申請單號</returns>
        [HttpGet]
        [Route("generate-number")]
        [SwaggerOperation(Summary = "產生攜出申請單號", Description = "產生新的攜出申請單號")]
        public async Task<IActionResult> GenerateCarryOutNo()
        {
            try
            {
                var carryOutNo = await _assetCarryOutService.GenerateCarryOutNoAsync();
                return Ok(new { success = true, message = "產生攜出申請單號成功", data = carryOutNo });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"產生攜出申請單號失敗：{ex.Message}" });
            }
        }
    }

    /// <summary>
    /// 審核請求DTO
    /// </summary>
    public class ApprovalRequestDto
    {
        [Required]
        public bool IsApproved { get; set; }

        [Required]
        public string ApproverId { get; set; } = "";

        public string? Comment { get; set; }
    }

    /// <summary>
    /// 攜出登記請求DTO
    /// </summary>
    public class CarryOutRequestDto
    {
        [Required]
        public long ActualCarryOutDate { get; set; }

        [Required]
        public string OperatorId { get; set; } = "";
    }

    /// <summary>
    /// 歸還登記請求DTO
    /// </summary>
    public class ReturnRequestDto
    {
        [Required]
        public long ActualReturnDate { get; set; }

        [Required]
        public string OperatorId { get; set; } = "";
    }
}