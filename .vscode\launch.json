{"version": "0.2.0", "configurations": [{"name": ".NET 8 啟動", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net8.0/FAST_ERP_Backend.dll", "args": [], "cwd": "${workspaceFolder}", "stopAtEntry": false, "console": "internalConsole"}, {"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/<target-framework>/<project-name.dll>", "args": [], "cwd": "${workspaceFolder}", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": ".NET Core Docker Attach", "type": "coreclr", "request": "attach", "processId": "${command:pickRemoteProcess}", "pipeTransport": {"pipeProgram": "docker", "pipeArgs": ["exec", "-i", "fast_erp_backend_dev"], "debuggerPath": "/vsdbg/vsdbg", "pipeCwd": "${workspaceFolder}", "quoteArgs": false}, "sourceFileMap": {"/app": "${workspaceFolder}"}}]}