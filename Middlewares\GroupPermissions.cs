﻿using System.Security.Claims;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;

namespace FAST_ERP_Backend.Middlewares
{
    /// <summary> 群組權限中介軟體，負責根據請求路徑和用戶的群組權限進行授權檢查。 </summary>
    public class GroupPermissions(RequestDelegate _next,IServiceScopeFactory _scopeFactory)
    {
        /// <summary> 處理 HTTP 請求，並根據群組權限進行授權檢查。 </summary>
        /// <param name="context">當前的 HTTP 上下文。</param>
        /// <returns>表示非同步操作的 Task。</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            // 如果當前路徑不需要群組權限驗證，則直接跳過
            if (ShouldValidatePassword(context))
            {
                await _next(context);
                return;
            }

            // 獲取當前登入用戶的 ClaimsPrincipal
            ClaimsPrincipal LoginUser = context.User;
            var LoginAccount = LoginUser.FindFirst(ClaimTypes.Name)?.Value; // 使用 ?. 避免空引用

            // 如果是特定帳號 (例如 FastAdmin)，則跳過群組權限驗證
            if (LoginAccount != null && LoginAccount.Equals("FastAdmin", StringComparison.OrdinalIgnoreCase))
            {
                await _next(context);
                return;
            }

            try
            {
                // 建立 Scoped 範圍以獲取服務 (因為 ISystemGroupsService 可能是 Scoped)
                using (var scope = _scopeFactory.CreateScope())
                {
                    var systemGroupsService = scope.ServiceProvider.GetRequiredService<ISystemGroupsService>();

                    // 從 Request 路徑中解析 Controller 名稱，並推斷群組名稱
                    var controllerName = context.Request.Path.Value.Split('/', StringSplitOptions.RemoveEmptyEntries).Skip(1).FirstOrDefault();
                    if (string.IsNullOrEmpty(controllerName))
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("路徑有誤,請聯繫工程人員");
                        return;
                    }

                    var controllerTypeName = $"{controllerName}Controller";
                    var controllerType = AppDomain.CurrentDomain.GetAssemblies()
                        .SelectMany(a => a.GetTypes())
                        .FirstOrDefault(t => t.Name.Equals(controllerTypeName, StringComparison.OrdinalIgnoreCase));

                    if (controllerType == null || string.IsNullOrEmpty(controllerType.FullName))
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("路徑有誤,請聯繫工程人員");
                        return;
                    }

                    var namespaceParts = controllerType.FullName.Split('.');
                    var controllersIndex = Array.IndexOf(namespaceParts, "Controllers");

                    string GroupName = "";
                    if (controllersIndex != -1 && controllersIndex + 1 < namespaceParts.Length)
                    {
                        GroupName = namespaceParts[controllersIndex + 1];
                    }
                    else
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("無法解析群組名稱,請聯繫工程人員");
                        return;
                    }

                    // 驗證用戶是否擁有該群組的權限
                    bool result = await systemGroupsService.VerifyGroupAsync(GroupName);
                    if (result)
                    {
                        await _next(context); // 權限驗證通過，繼續處理請求
                        return;
                    }
                    else
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("沒有該群組權限");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                // 捕獲異常並返回錯誤訊息
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync($"路徑有誤或權限驗證失敗: {ex.Message}, 請聯繫工程人員");
                return;
            }
        }

        /// <summary> 判斷當前 HTTP 請求的路徑是否需要跳過群組權限驗證。 </summary>
        /// <param name="context">當前的 HTTP 上下文。</param>
        /// <returns>如果路徑在保護路徑列表中，則為 true；否則為 false。</returns>
        private bool ShouldValidatePassword(HttpContext context)
        {
            // 定義不需要群組權限驗證的路徑
            var protectedPaths = new[] {
                "/api/Login" // 登入 API
            };
            // 檢查當前請求路徑是否以任何一個保護路徑開頭
            return protectedPaths.Any(path =>
                context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase));
        }
    }
}