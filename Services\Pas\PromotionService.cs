using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class PromotionService : IPromotionService
    {
        private readonly ERPDbContext _context;
        private readonly EmployeeClass _employeeClass;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public PromotionService(
            Baseform baseform,
            ERPDbContext context,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService)
        {
            _baseform = baseform;
            _context = context;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
        }

        public async Task<List<PromotionDTO>> GetPromotionListAsync(string userId)
        {
            try
            {
                var promotions = await _context.Pas_Promotion
                    .Where(p => p.UserId == userId && !p.IsDeleted)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToListAsync();

                var promotionDTOs = new List<PromotionDTO>();

                foreach (var p in promotions)
                {
                    var promotionDTO = new PromotionDTO
                    {
                        Uid = p.Uid,
                        UserId = p.UserId,
                        PromotionType = p.PromotionType,
                        PromotionTypeName = GetPromotionTypeName(p.PromotionType),
                        JobTitle = p.JobTitle,
                        JobTitleName = GetJobTitleName(p.JobTitle),
                        JobLevel = p.JobLevel,
                        JobLevelName = GetJobLevelName(p.JobLevel),
                        JobRank = p.JobRank,
                        JobRankName = GetJobRankName(p.JobRank),
                        PromotionDate = _baseform.TimestampToDateStr(p.PromotionDate),
                        EffectiveDate = _baseform.TimestampToDateStr(p.EffectiveDate),
                        PromotionReason = p.PromotionReason,
                        ExpenseDepartmentChangeUid = p.ExpenseDepartmentChangeUid,
                        ServiceDepartmentChangeUid = p.ServiceDepartmentChangeUid,
                        JobroleType = p.JobroleType,
                        JobroleTypeName = GetJobroleTypeName(p.JobroleType),
                        SalaryType = p.SalaryType,
                        SalaryTypeName = GetSalaryTypeName(p.SalaryType),
                        SalaryAmount = p.SalaryAmount?.ToString() ?? "",
                        CategoryType = p.CategoryType,
                        CategoryTypeName = GetCategoryTypeName(p.CategoryType),
                        Remark = p.Remark,
                        UpdateTime = p.UpdateTime
                    };

                    // 載入關聯的部門異動資料，用於表格顯示
                    if (!string.IsNullOrEmpty(p.ExpenseDepartmentChangeUid))
                    {
                        promotionDTO.ExpenseDepartmentChange = await GetExpenseDepartmentChangeAsync(p.ExpenseDepartmentChangeUid);
                    }

                    if (!string.IsNullOrEmpty(p.ServiceDepartmentChangeUid))
                    {
                        promotionDTO.ServiceDepartmentChange = await GetServiceDepartmentChangeAsync(p.ServiceDepartmentChangeUid);
                    }

                    promotionDTOs.Add(promotionDTO);
                }

                return promotionDTOs;
            }
            catch (Exception ex)
            {
                throw new Exception("取得升遷異動資料錯誤", ex);
            }
        }

        public async Task<PromotionDTO> GetPromotionDetailAsync(string uid)
        {
            try
            {
                var promotionEntity = await _context.Pas_Promotion
                    .Where(p => p.Uid == uid && !p.IsDeleted)
                    .FirstOrDefaultAsync();

                if (promotionEntity == null)
                    return null;

                var promotion = new PromotionDTO
                {
                    Uid = promotionEntity.Uid,
                    UserId = promotionEntity.UserId,
                    PromotionType = promotionEntity.PromotionType,
                    PromotionTypeName = GetPromotionTypeName(promotionEntity.PromotionType),
                    JobTitle = promotionEntity.JobTitle,
                    JobTitleName = GetJobTitleName(promotionEntity.JobTitle),
                    JobLevel = promotionEntity.JobLevel,
                    JobLevelName = GetJobLevelName(promotionEntity.JobLevel),
                    JobRank = promotionEntity.JobRank,
                    JobRankName = GetJobRankName(promotionEntity.JobRank),
                    PromotionDate = _baseform.TimestampToDateStr(promotionEntity.PromotionDate),
                    EffectiveDate = _baseform.TimestampToDateStr(promotionEntity.EffectiveDate),
                    PromotionReason = promotionEntity.PromotionReason,
                    ExpenseDepartmentChangeUid = promotionEntity.ExpenseDepartmentChangeUid,
                    ServiceDepartmentChangeUid = promotionEntity.ServiceDepartmentChangeUid,
                    JobroleType = promotionEntity.JobroleType,
                    JobroleTypeName = GetJobroleTypeName(promotionEntity.JobroleType),
                    SalaryType = promotionEntity.SalaryType,
                    SalaryTypeName = GetSalaryTypeName(promotionEntity.SalaryType),
                    SalaryAmount = promotionEntity.SalaryAmount?.ToString() ?? "",
                    CategoryType = promotionEntity.CategoryType,
                    CategoryTypeName = GetCategoryTypeName(promotionEntity.CategoryType),
                    Remark = promotionEntity.Remark,
                    UpdateTime = promotionEntity.UpdateTime
                };

                // 載入關聯的部門異動資料
                if (!string.IsNullOrEmpty(promotion.ExpenseDepartmentChangeUid))
                {
                    promotion.ExpenseDepartmentChange = await GetExpenseDepartmentChangeAsync(promotion.ExpenseDepartmentChangeUid);
                }

                if (!string.IsNullOrEmpty(promotion.ServiceDepartmentChangeUid))
                {
                    promotion.ServiceDepartmentChange = await GetServiceDepartmentChangeAsync(promotion.ServiceDepartmentChangeUid);
                }

                return promotion;
            }
            catch (Exception ex)
            {
                throw new Exception("取得升遷異動明細錯誤", ex);
            }
        }

        public async Task<PromotionDTO> GetLatestPromotionAsync(string userId)
        {
            try
            {
                var latestPromotion = await _context.Pas_Promotion
                    .Where(p => p.UserId == userId && !p.IsDeleted)
                    .OrderByDescending(p => p.EffectiveDate)
                    .FirstOrDefaultAsync();

                if (latestPromotion == null)
                    return new PromotionDTO { UserId = userId };

                var promotion = new PromotionDTO
                {
                    UserId = userId,
                    JobTitle = latestPromotion.JobTitle,
                    JobLevel = latestPromotion.JobLevel,
                    JobRank = latestPromotion.JobRank,
                    JobroleType = latestPromotion.JobroleType,
                    SalaryType = latestPromotion.SalaryType,
                    SalaryAmount = latestPromotion.SalaryAmount?.ToString() ?? "",
                    CategoryType = latestPromotion.CategoryType,
                    // 清空其他欄位，讓用戶填入新的資料
                    Uid = "",
                    PromotionType = "",
                    PromotionDate = "",
                    EffectiveDate = "",
                    PromotionReason = "",
                    Remark = "",
                    ExpenseDepartmentChangeUid = "",
                    ServiceDepartmentChangeUid = ""
                };

                // 載入最新的部門資料作為預設值
                if (!string.IsNullOrEmpty(latestPromotion.ExpenseDepartmentChangeUid))
                {
                    var latestExpense = await GetExpenseDepartmentChangeAsync(latestPromotion.ExpenseDepartmentChangeUid);
                    if (latestExpense != null)
                    {
                        promotion.ExpenseDepartmentChange = new ExpenseDepartmentChangeDTO
                        {
                            ExpenseDepartmentId = latestExpense.ExpenseDepartmentId,
                            ChangeDate = "",
                            EffectiveDate = "",
                            ChangeReason = "",
                            Remark = ""
                        };
                    }
                }

                if (!string.IsNullOrEmpty(latestPromotion.ServiceDepartmentChangeUid))
                {
                    var latestService = await GetServiceDepartmentChangeAsync(latestPromotion.ServiceDepartmentChangeUid);
                    if (latestService != null)
                    {
                        promotion.ServiceDepartmentChange = new ServiceDepartmentChangeDTO
                        {
                            ServiceDepartmentId = latestService.ServiceDepartmentId,
                            ServiceDivisionId = latestService.ServiceDivisionId,
                            ChangeDate = "",
                            EffectiveDate = "",
                            ChangeReason = "",
                            Remark = ""
                        };
                    }
                }

                return promotion;
            }
            catch (Exception ex)
            {
                throw new Exception("取得最新升遷資料錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddPromotionAsync(PromotionDTO data)
        {
            List<string> validationErrors = ValidatePromotionInput(data, "add");

            if (validationErrors.Count > 0)
            {
                return (false, validationErrors[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var promotionUid = Guid.NewGuid().ToString().Trim();
                string expenseDepartmentChangeUid = "";
                string serviceDepartmentChangeUid = "";

                // 處理開支部門異動
                if (data.ExpenseDepartmentChange != null &&
                    !string.IsNullOrEmpty(data.ExpenseDepartmentChange.ExpenseDepartmentId))
                {
                    expenseDepartmentChangeUid = await AddExpenseDepartmentChangeAsync(data.ExpenseDepartmentChange, data.UserId);
                }

                // 處理服務部門異動
                if (data.ServiceDepartmentChange != null &&
                    !string.IsNullOrEmpty(data.ServiceDepartmentChange.ServiceDepartmentId))
                {
                    serviceDepartmentChangeUid = await AddServiceDepartmentChangeAsync(data.ServiceDepartmentChange, data.UserId);
                }

                // 建立升遷異動資料
                var newPromotion = new Promotion
                {
                    Uid = promotionUid,
                    UserId = data.UserId,
                    PromotionType = data.PromotionType ?? "",
                    JobTitle = data.JobTitle ?? "",
                    JobLevel = data.JobLevel ?? "",
                    JobRank = data.JobRank ?? "",
                    PromotionDate = _baseform.DateStrToTimestamp(data.PromotionDate ?? ""),
                    EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? ""),
                    PromotionReason = data.PromotionReason ?? "",
                    ExpenseDepartmentChangeUid = expenseDepartmentChangeUid,
                    ServiceDepartmentChangeUid = serviceDepartmentChangeUid,
                    JobroleType = data.JobroleType ?? "",
                    SalaryType = data.SalaryType ?? "",
                    SalaryAmount = string.IsNullOrEmpty(data.SalaryAmount) ? null : decimal.TryParse(data.SalaryAmount, out var amount) ? amount : null,
                    CategoryType = data.CategoryType ?? "",
                    Remark = data.Remark ?? "",
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Pas_Promotion.AddAsync(newPromotion);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "新增升遷異動資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增升遷異動資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditPromotionAsync(PromotionDTO data)
        {
            List<string> validationErrors = ValidatePromotionInput(data, "edit");

            if (validationErrors.Count > 0)
            {
                return (false, validationErrors[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var existingPromotion = await _context.Pas_Promotion
                    .FirstOrDefaultAsync(p => p.Uid == data.Uid && !p.IsDeleted);

                if (existingPromotion == null)
                {
                    return (false, "找不到對應的升遷異動資料");
                }

                // 處理開支部門異動
                string expenseDepartmentChangeUid = existingPromotion.ExpenseDepartmentChangeUid;
                if (data.ExpenseDepartmentChange != null && !string.IsNullOrEmpty(data.ExpenseDepartmentChange.ExpenseDepartmentId))
                {
                    //有選擇ExpenseDepartmentId部門ID才做異動
                    // 有部門異動資料
                    if (!string.IsNullOrEmpty(expenseDepartmentChangeUid))
                    {
                        // 更新既有的部門異動
                        await UpdateExpenseDepartmentChangeAsync(data.ExpenseDepartmentChange, expenseDepartmentChangeUid);
                    }
                    else
                    {
                        // 新增部門異動
                        expenseDepartmentChangeUid = await AddExpenseDepartmentChangeAsync(data.ExpenseDepartmentChange, data.UserId);
                    }
                }
                else
                {
                    // 沒有部門異動資料，但原本有，需要刪除
                    if (!string.IsNullOrEmpty(expenseDepartmentChangeUid))
                    {
                        await DeleteExpenseDepartmentChangeAsync(expenseDepartmentChangeUid);
                        expenseDepartmentChangeUid = "";
                    }
                }

                // 處理服務部門異動
                string serviceDepartmentChangeUid = existingPromotion.ServiceDepartmentChangeUid;
                if (data.ServiceDepartmentChange != null && !string.IsNullOrEmpty(data.ServiceDepartmentChange.ServiceDepartmentId))
                {
                    //有選擇ServiceDepartmentId部門ID才做異動
                    // 有部門異動資料
                    if (!string.IsNullOrEmpty(serviceDepartmentChangeUid))
                    {
                        // 更新既有的部門異動
                        await UpdateServiceDepartmentChangeAsync(data.ServiceDepartmentChange, serviceDepartmentChangeUid);
                    }
                    else
                    {
                        // 新增部門異動
                        serviceDepartmentChangeUid = await AddServiceDepartmentChangeAsync(data.ServiceDepartmentChange, data.UserId);
                    }
                }
                else
                {
                    // 沒有部門異動資料，但原本有，需要刪除
                    if (!string.IsNullOrEmpty(serviceDepartmentChangeUid))
                    {
                        await DeleteServiceDepartmentChangeAsync(serviceDepartmentChangeUid);
                        serviceDepartmentChangeUid = "";
                    }
                }

                // 更新升遷異動資料
                existingPromotion.PromotionType = data.PromotionType ?? "";
                existingPromotion.JobTitle = data.JobTitle ?? "";
                existingPromotion.JobLevel = data.JobLevel ?? "";
                existingPromotion.JobRank = data.JobRank ?? "";
                existingPromotion.PromotionDate = _baseform.DateStrToTimestamp(data.PromotionDate ?? "");
                existingPromotion.EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? "");
                existingPromotion.PromotionReason = data.PromotionReason ?? "";
                existingPromotion.ExpenseDepartmentChangeUid = expenseDepartmentChangeUid;
                existingPromotion.ServiceDepartmentChangeUid = serviceDepartmentChangeUid;
                existingPromotion.JobroleType = data.JobroleType ?? "";
                existingPromotion.SalaryType = data.SalaryType ?? "";
                existingPromotion.SalaryAmount = string.IsNullOrEmpty(data.SalaryAmount) ? null : decimal.TryParse(data.SalaryAmount, out var amount) ? amount : null;
                existingPromotion.CategoryType = data.CategoryType ?? "";
                existingPromotion.Remark = data.Remark ?? "";
                existingPromotion.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existingPromotion.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "更新升遷異動資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新升遷異動資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeletePromotionAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var promotion = await _context.Pas_Promotion
                    .FirstOrDefaultAsync(p => p.Uid == uid && !p.IsDeleted);

                if (promotion == null)
                {
                    return (false, "找不到對應的升遷異動資料");
                }

                // 刪除關聯的部門異動資料
                if (!string.IsNullOrEmpty(promotion.ExpenseDepartmentChangeUid))
                {
                    await DeleteExpenseDepartmentChangeAsync(promotion.ExpenseDepartmentChangeUid);
                }

                if (!string.IsNullOrEmpty(promotion.ServiceDepartmentChangeUid))
                {
                    await DeleteServiceDepartmentChangeAsync(promotion.ServiceDepartmentChangeUid);
                }

                // 軟刪除升遷異動資料
                promotion.IsDeleted = true;
                promotion.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                promotion.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "刪除升遷異動資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除升遷異動資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        #region 驗證與輔助方法

        private List<string> ValidatePromotionInput(PromotionDTO data, string mode)
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(data.UserId))
                errors.Add("使用者編號不能為空");

            if (string.IsNullOrEmpty(data.PromotionType))
                errors.Add("升遷類型不能為空");

            if (string.IsNullOrEmpty(data.JobTitle))
                errors.Add("職稱不能為空");

            if (string.IsNullOrEmpty(data.EffectiveDate))
                errors.Add("生效日期不能為空");

            if (mode == "edit" && string.IsNullOrEmpty(data.Uid))
                errors.Add("資料編號不能為空");

            return errors;
        }

        private string GetPromotionTypeName(string promotionType)
        {
            return _employeeClass.GetPromotionTypeName(promotionType);
        }

        private string GetJobTitleName(string jobTitle)
        {
            return _employeeClass.GetJobtitleName(jobTitle);
        }

        private string GetJobLevelName(string jobLevel)
        {
            return _employeeClass.GetlevelName(jobLevel);
        }

        private string GetJobRankName(string jobRank)
        {
            return _employeeClass.GetrankName(jobRank);
        }

        private string GetJobroleTypeName(string jobroleType)
        {
            return _employeeClass.GetJobroleTypeName(jobroleType);
        }

        private string GetSalaryTypeName(string salaryType)
        {
            return _employeeClass.GetsalaryTypeName(salaryType);
        }

        private string GetCategoryTypeName(string categoryType)
        {
            return _employeeClass.GetcategoryName(categoryType);
        }

        #endregion

        #region 部門異動相關方法

        private async Task<ExpenseDepartmentChangeDTO> GetExpenseDepartmentChangeAsync(string uid)
        {
            var expenseChange = await _context.Pas_ExpenseDepartmentChange
                .Where(e => e.Uid == uid && !e.IsDeleted)
                .FirstOrDefaultAsync();

            if (expenseChange == null)
                return null;

            return new ExpenseDepartmentChangeDTO
            {
                Uid = expenseChange.Uid,
                UserId = expenseChange.UserId,
                ExpenseDepartmentId = expenseChange.ExpenseDepartmentId,
                ExpenseDepartmentName = GetDepartmentName(expenseChange.ExpenseDepartmentId),
                ChangeDate = _baseform.TimestampToDateStr(expenseChange.ChangeDate),
                EffectiveDate = _baseform.TimestampToDateStr(expenseChange.EffectiveDate),
                ChangeReason = expenseChange.ChangeReason,
                Remark = expenseChange.Remark
            };
        }

        private async Task<ServiceDepartmentChangeDTO> GetServiceDepartmentChangeAsync(string uid)
        {
            var serviceChange = await _context.Pas_ServiceDepartmentChange
                .Where(s => s.Uid == uid && !s.IsDeleted)
                .FirstOrDefaultAsync();

            if (serviceChange == null)
                return null;

            return new ServiceDepartmentChangeDTO
            {
                Uid = serviceChange.Uid,
                UserId = serviceChange.UserId,
                ServiceDepartmentId = serviceChange.ServiceDepartmentId,
                ServiceDepartmentName = GetDepartmentName(serviceChange.ServiceDepartmentId),
                ServiceDivisionId = serviceChange.ServiceDivisionId,
                ServiceDivisionName = GetDivisionName(serviceChange.ServiceDivisionId),
                ChangeDate = _baseform.TimestampToDateStr(serviceChange.ChangeDate),
                EffectiveDate = _baseform.TimestampToDateStr(serviceChange.EffectiveDate),
                ChangeReason = serviceChange.ChangeReason,
                Remark = serviceChange.Remark
            };
        }

        private async Task<string> AddExpenseDepartmentChangeAsync(ExpenseDepartmentChangeDTO data, string userId)
        {
            var uid = Guid.NewGuid().ToString().Trim();

            var newExpenseChange = new ExpenseDepartmentChange
            {
                Uid = uid,
                UserId = userId,
                ExpenseDepartmentId = data.ExpenseDepartmentId ?? "",
                ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? ""),
                EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? ""),
                ChangeReason = data.ChangeReason ?? "",
                Remark = data.Remark ?? "",
                CreateTime = _baseform.GetCurrentLocalTimestamp(),
                CreateUserId = _currentUserService.UserId,
            };

            await _context.Pas_ExpenseDepartmentChange.AddAsync(newExpenseChange);
            return uid;
        }

        private async Task<string> AddServiceDepartmentChangeAsync(ServiceDepartmentChangeDTO data, string userId)
        {
            var uid = Guid.NewGuid().ToString().Trim();

            var newServiceChange = new ServiceDepartmentChange
            {
                Uid = uid,
                UserId = userId,
                ServiceDepartmentId = data.ServiceDepartmentId ?? "",
                ServiceDivisionId = data.ServiceDivisionId ?? "",
                ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? ""),
                EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? ""),
                ChangeReason = data.ChangeReason ?? "",
                Remark = data.Remark ?? "",
                CreateTime = _baseform.GetCurrentLocalTimestamp(),
                CreateUserId = _currentUserService.UserId,
            };

            await _context.Pas_ServiceDepartmentChange.AddAsync(newServiceChange);
            return uid;
        }

        private async Task UpdateExpenseDepartmentChangeAsync(ExpenseDepartmentChangeDTO data, string uid)
        {
            var existing = await _context.Pas_ExpenseDepartmentChange
                .FirstOrDefaultAsync(e => e.Uid == uid && !e.IsDeleted);

            if (existing != null)
            {
                existing.ExpenseDepartmentId = data.ExpenseDepartmentId ?? "";
                existing.ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? "");
                existing.EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? "");
                existing.ChangeReason = data.ChangeReason ?? "";
                existing.Remark = data.Remark ?? "";
                existing.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existing.UpdateUserId = _currentUserService.UserId;
            }
        }

        private async Task UpdateServiceDepartmentChangeAsync(ServiceDepartmentChangeDTO data, string uid)
        {
            var existing = await _context.Pas_ServiceDepartmentChange
                .FirstOrDefaultAsync(s => s.Uid == uid && !s.IsDeleted);

            if (existing != null)
            {
                existing.ServiceDepartmentId = data.ServiceDepartmentId ?? "";
                existing.ServiceDivisionId = data.ServiceDivisionId ?? "";
                existing.ChangeDate = _baseform.DateStrToTimestamp(data.ChangeDate ?? "");
                existing.EffectiveDate = _baseform.DateStrToTimestamp(data.EffectiveDate ?? "");
                existing.ChangeReason = data.ChangeReason ?? "";
                existing.Remark = data.Remark ?? "";
                existing.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existing.UpdateUserId = _currentUserService.UserId;
            }
        }

        private async Task DeleteExpenseDepartmentChangeAsync(string uid)
        {
            var existing = await _context.Pas_ExpenseDepartmentChange
                .FirstOrDefaultAsync(e => e.Uid == uid && !e.IsDeleted);

            if (existing != null)
            {
                existing.IsDeleted = true;
                existing.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existing.DeleteUserId = _currentUserService.UserId;
            }
        }

        private async Task DeleteServiceDepartmentChangeAsync(string uid)
        {
            var existing = await _context.Pas_ServiceDepartmentChange
                .FirstOrDefaultAsync(s => s.Uid == uid && !s.IsDeleted);

            if (existing != null)
            {
                existing.IsDeleted = true;
                existing.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existing.DeleteUserId = _currentUserService.UserId;
            }
        }

        private string GetDepartmentName(string departmentId)
        {
            if (string.IsNullOrEmpty(departmentId))
                return "";

            var department = _context.Common_Departments
                .FirstOrDefault(d => d.DepartmentId == departmentId && !d.IsDeleted);

            return department?.Name ?? departmentId;
        }

        private string GetDivisionName(string divisionId)
        {
            if (string.IsNullOrEmpty(divisionId))
                return "";

            var division = _context.Common_Divisions
                .FirstOrDefault(d => d.DivisionId == divisionId && !d.IsDeleted);

            return division?.Name ?? divisionId;
        }

        /// <summary>
        /// 根據生效日期查詢開支部門異動資料
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <param name="effectiveDate">查詢日期 (格式: yyyy-MM-dd)，如果為空則使用當天日期</param>
        /// <returns>該時間點生效的開支部門異動資料</returns>
        private async Task<ExpenseDepartmentChangeDTO?> GetExpenseDepartmentChangeByEffectiveDateAsync(string userId, string effectiveDate = null)
        {
            // 如果沒有提供日期，使用當天日期
            if (string.IsNullOrWhiteSpace(effectiveDate))
            {
                effectiveDate = _baseform.TimestampToDateStr(_baseform.GetCurrentLocalTimestamp());
            }

            // 將日期字串轉換為timestamp進行比較
            long? targetTimestampNullable = _baseform.DateStrToTimestamp(effectiveDate);
            if (!targetTimestampNullable.HasValue)
            {
                return null; // 日期格式錯誤
            }
            long targetTimestamp = targetTimestampNullable.Value;

            var expenseChange = await _context.Pas_ExpenseDepartmentChange
                .Where(e => e.UserId == userId &&
                            !e.IsDeleted &&
                            e.EffectiveDate <= targetTimestamp)
                .OrderByDescending(e => e.EffectiveDate)
                .ThenByDescending(e => e.CreateTime)
                .FirstOrDefaultAsync();

            if (expenseChange == null)
                return null;

            return new ExpenseDepartmentChangeDTO
            {
                Uid = expenseChange.Uid,
                UserId = expenseChange.UserId,
                ExpenseDepartmentId = expenseChange.ExpenseDepartmentId,
                ExpenseDepartmentName = GetDepartmentName(expenseChange.ExpenseDepartmentId),
                ChangeDate = _baseform.TimestampToDateStr(expenseChange.ChangeDate),
                EffectiveDate = _baseform.TimestampToDateStr(expenseChange.EffectiveDate),
                ChangeReason = expenseChange.ChangeReason,
                Remark = expenseChange.Remark
            };
        }

        /// <summary>
        /// 根據生效日期查詢服務部門異動資料
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <param name="effectiveDate">查詢日期 (格式: yyyy-MM-dd)，如果為空則使用當天日期</param>
        /// <returns>該時間點生效的服務部門異動資料</returns>
        private async Task<ServiceDepartmentChangeDTO?> GetServiceDepartmentChangeByEffectiveDateAsync(string userId, string effectiveDate = null)
        {
            // 如果沒有提供日期，使用當天日期
            if (string.IsNullOrWhiteSpace(effectiveDate))
            {
                effectiveDate = _baseform.TimestampToDateStr(_baseform.GetCurrentLocalTimestamp());
            }

            // 將日期字串轉換為timestamp進行比較
            long? targetTimestampNullable = _baseform.DateStrToTimestamp(effectiveDate);
            if (!targetTimestampNullable.HasValue)
            {
                return null; // 日期格式錯誤
            }
            long targetTimestamp = targetTimestampNullable.Value;

            var serviceChange = await _context.Pas_ServiceDepartmentChange
                .Where(s => s.UserId == userId &&
                            !s.IsDeleted &&
                            s.EffectiveDate <= targetTimestamp)
                .OrderByDescending(s => s.EffectiveDate)
                .ThenByDescending(s => s.CreateTime)
                .FirstOrDefaultAsync();

            if (serviceChange == null)
                return null;

            return new ServiceDepartmentChangeDTO
            {
                Uid = serviceChange.Uid,
                UserId = serviceChange.UserId,
                ServiceDepartmentId = serviceChange.ServiceDepartmentId,
                ServiceDepartmentName = GetDepartmentName(serviceChange.ServiceDepartmentId),
                ServiceDivisionId = serviceChange.ServiceDivisionId,
                ServiceDivisionName = GetDivisionName(serviceChange.ServiceDivisionId),
                ChangeDate = _baseform.TimestampToDateStr(serviceChange.ChangeDate),
                EffectiveDate = _baseform.TimestampToDateStr(serviceChange.EffectiveDate),
                ChangeReason = serviceChange.ChangeReason,
                Remark = serviceChange.Remark
            };
        }

        #endregion

        /// <summary>
        /// 根據指定日期取得該時間點生效的升遷資料及部門資訊
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <param name="effectiveDate">查詢日期 (格式: yyyy-MM-dd)，如果為空則使用當天日期</param>
        /// <returns>該時間點生效的升遷資料</returns>
        public async Task<PromotionDTO?> GetPromotionByEffectiveDateAsync(string userId, string effectiveDate = null)
        {
            try
            {
                // 如果沒有提供日期，使用當天日期
                if (string.IsNullOrWhiteSpace(effectiveDate))
                {
                    effectiveDate = _baseform.TimestampToDateStr(_baseform.GetCurrentLocalTimestamp());
                }

                // 將日期字串轉換為timestamp進行比較
                long? targetTimestampNullable = _baseform.DateStrToTimestamp(effectiveDate);
                if (!targetTimestampNullable.HasValue)
                {
                    return null; // 日期格式錯誤
                }
                long targetTimestamp = targetTimestampNullable.Value;

                // 查詢該用戶所有生效日期 <= 指定日期的升遷記錄，按生效日期降序排序取最新的一筆
                var promotion = await _context.Pas_Promotion
                    .Where(p => p.UserId == userId &&
                                !p.IsDeleted &&
                                p.EffectiveDate <= targetTimestamp)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ThenByDescending(p => p.CreateTime) // 如果生效日期相同，按建立時間排序
                    .FirstOrDefaultAsync();

                if (promotion == null)
                    return null;

                // 直接根據生效日期查詢部門異動資料
                var expenseDepartmentChange = await GetExpenseDepartmentChangeByEffectiveDateAsync(userId, effectiveDate);
                var serviceDepartmentChange = await GetServiceDepartmentChangeByEffectiveDateAsync(userId, effectiveDate);

                // 建立DTO並回傳
                return new PromotionDTO
                {
                    Uid = promotion.Uid,
                    UserId = promotion.UserId,
                    PromotionType = promotion.PromotionType,
                    PromotionTypeName = GetPromotionTypeName(promotion.PromotionType),
                    JobTitle = promotion.JobTitle,
                    JobTitleName = GetJobTitleName(promotion.JobTitle),
                    JobLevel = promotion.JobLevel,
                    JobLevelName = GetJobLevelName(promotion.JobLevel),
                    JobRank = promotion.JobRank,
                    JobRankName = GetJobRankName(promotion.JobRank),
                    PromotionDate = _baseform.TimestampToDateStr(promotion.PromotionDate),
                    EffectiveDate = _baseform.TimestampToDateStr(promotion.EffectiveDate),
                    PromotionReason = promotion.PromotionReason,
                    JobroleType = promotion.JobroleType,
                    JobroleTypeName = GetJobroleTypeName(promotion.JobroleType),
                    SalaryType = promotion.SalaryType,
                    SalaryTypeName = GetSalaryTypeName(promotion.SalaryType),
                    SalaryAmount = promotion.SalaryAmount?.ToString() ?? "",
                    CategoryType = promotion.CategoryType,
                    CategoryTypeName = GetCategoryTypeName(promotion.CategoryType),
                    Remark = promotion.Remark,
                    // 部門異動資料是根據生效日期直接查詢，不使用關聯UID
                    ExpenseDepartmentChangeUid = expenseDepartmentChange?.Uid,
                    ExpenseDepartmentChange = expenseDepartmentChange,
                    ServiceDepartmentChangeUid = serviceDepartmentChange?.Uid,
                    ServiceDepartmentChange = serviceDepartmentChange
                };
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                Console.WriteLine($"GetPromotionByEffectiveDateAsync error: {ex.Message}");
                return null;
            }
        }
    }
}