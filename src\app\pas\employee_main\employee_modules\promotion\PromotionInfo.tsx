import { useEffect, useState } from 'react';
import { Modal, Button, message, Table, Form, Input, DatePicker, Card, Typography, Space, Tabs, Row, Col, Divider, Select, Popconfirm, InputNumber } from 'antd';
import dayjs from 'dayjs';
import {
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    PlusOutlined,
    TeamOutlined,
    CalendarOutlined,
    UserOutlined,
    ApartmentOutlined,
    BankOutlined,
    SwapOutlined,
    UpOutlined,
    SettingOutlined
} from '@ant-design/icons';
import {
    getPromotionList,
    getPromotionDetail,
    getLatestPromotion,
    addPromotion,
    editPromotion,
    deletePromotion,
    createEmptyPromotion,
    createPromotionFormData,
    type Promotion
} from '@/services/pas/PromotionService';
import { getDepartments } from '@/services/common/departmentService';
import {
    getJobroleTypeOptions,
    getSalaryTypeOptions,
    getCategoryTypeOptions,
    getJobtitleOptions,
    getJobLevelOptions,
    getJobRankOptions,
    getPromotionTypeOptions
} from '@/services/pas/OptionParameterService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;
const { Option } = Select;

type PromotionInfoProps = {
    userId: string;
    active: boolean;
};

const PromotionInfo: React.FC<PromotionInfoProps> = ({ userId, active }) => {
    const [promotionList, setPromotionList] = useState<Promotion[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [promotionDetail, setPromotionDetail] = useState<Promotion | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [deletingRows, setDeletingRows] = useState<Set<string>>(new Set());
    const [activeTabKey, setActiveTabKey] = useState<string>('1');
    const [form] = Form.useForm();

    // 選項資料
    const [departmentOptions, setDepartmentOptions] = useState<any[]>([]);
    const [divisionOptions, setDivisionOptions] = useState<any[]>([]);

    useEffect(() => {
        if (active) {
            fetchPromotionList();
            loadOptions();
        }
    }, [active, userId]);

    const loadOptions = async () => {
        try {
            const deptRes = await getDepartments();
            if (deptRes.success) setDepartmentOptions(deptRes.data || []);

            // TODO: 載入組織選項，等待divisionService實作
        } catch (error) {
            console.error('載入選項資料失敗:', error);
        }
    };

    const fetchPromotionList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const { success, data, message: msg } = await getPromotionList(userId);
            if (success && data) {
                setPromotionList(data);
            } else {
                message.error(msg || '載入升遷異動資料失敗');
            }
        } catch (error: any) {
            setErrorMsg(error.message || '未知錯誤');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        setActiveTabKey('1'); // 重置到第一個 tab
        try {
            const { success, data, message: msg } = await getPromotionDetail(uid);
            if (success && data) {
                setPromotionDetail(data);
                form.resetFields();

                // 設定升遷基本資料
                form.setFieldsValue({
                    promotionType: data.promotionType,
                    jobTitle: data.jobTitle,
                    jobLevel: data.jobLevel,
                    jobRank: data.jobRank,
                    promotionDate: data.promotionDate ? dayjs(data.promotionDate) : null,
                    effectiveDate: data.effectiveDate ? dayjs(data.effectiveDate) : null,
                    promotionReason: data.promotionReason,
                    jobroleType: data.jobroleType,
                    salaryType: data.salaryType,
                    salaryAmount: data.salaryAmount ? parseFloat(data.salaryAmount) : null,
                    categoryType: data.categoryType,
                    remark: data.remark,

                    // 開支部門異動資料
                    expenseDepartmentId: data.expenseDepartmentChange?.expenseDepartmentId,
                    expenseChangeDate: data.expenseDepartmentChange?.changeDate ? dayjs(data.expenseDepartmentChange.changeDate) : null,
                    expenseEffectiveDate: data.expenseDepartmentChange?.effectiveDate ? dayjs(data.expenseDepartmentChange.effectiveDate) : null,
                    expenseChangeReason: data.expenseDepartmentChange?.changeReason,
                    expenseRemark: data.expenseDepartmentChange?.remark,

                    // 服務部門異動資料
                    serviceDepartmentId: data.serviceDepartmentChange?.serviceDepartmentId,
                    serviceDivisionId: data.serviceDepartmentChange?.serviceDivisionId,
                    serviceChangeDate: data.serviceDepartmentChange?.changeDate ? dayjs(data.serviceDepartmentChange.changeDate) : null,
                    serviceEffectiveDate: data.serviceDepartmentChange?.effectiveDate ? dayjs(data.serviceDepartmentChange.effectiveDate) : null,
                    serviceChangeReason: data.serviceDepartmentChange?.changeReason,
                    serviceRemark: data.serviceDepartmentChange?.remark,
                });

                setIsModalOpen(true);
            } else {
                message.error(msg || '載入升遷異動資料失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '載入升遷異動資料時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            setModalLoading(true);

            // 建立升遷資料物件
            const promotionData: Promotion = {
                uid: promotionDetail?.uid || '',
                userId: userId,
                promotionType: values.promotionType || '',
                jobTitle: values.jobTitle || '',
                jobLevel: values.jobLevel || '',
                jobRank: values.jobRank || '',
                promotionDate: values.promotionDate ? values.promotionDate.format('YYYY-MM-DD') : '',
                effectiveDate: values.effectiveDate ? values.effectiveDate.format('YYYY-MM-DD') : '',
                promotionReason: values.promotionReason || '',
                jobroleType: values.jobroleType || '',
                salaryType: values.salaryType || '',
                salaryAmount: values.salaryAmount ? values.salaryAmount.toString() : '',
                categoryType: values.categoryType || '',
                remark: values.remark || '',
                // 只有當真的選取了開支部門時才創建開支部門異動資料
                expenseDepartmentChange: values.expenseDepartmentId ? {
                    expenseDepartmentId: values.expenseDepartmentId,
                    changeDate: values.expenseChangeDate ? values.expenseChangeDate.format('YYYY-MM-DD') : '',
                    effectiveDate: values.expenseEffectiveDate ? values.expenseEffectiveDate.format('YYYY-MM-DD') : '',
                    changeReason: values.expenseChangeReason || '',
                    remark: values.expenseRemark || ''
                } : undefined,
                // 只有當真的選取了服務部門時才創建服務部門異動資料
                serviceDepartmentChange: values.serviceDepartmentId ? {
                    serviceDepartmentId: values.serviceDepartmentId,
                    serviceDivisionId: values.serviceDivisionId || '',
                    changeDate: values.serviceChangeDate ? values.serviceChangeDate.format('YYYY-MM-DD') : '',
                    effectiveDate: values.serviceEffectiveDate ? values.serviceEffectiveDate.format('YYYY-MM-DD') : '',
                    changeReason: values.serviceChangeReason || '',
                    remark: values.serviceRemark || ''
                } : undefined
            };

            // 轉換為FormData
            const formData = createPromotionFormData(promotionData);

            let res;
            if (promotionDetail) {
                res = await editPromotion(formData);
            } else {
                res = await addPromotion(formData);
            }

            if (res.success) {
                message.success(promotionDetail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchPromotionList();
            } else {
                throw new Error(res.message || '儲存失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '儲存時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleDeleteConfirm = (uid: string) => {
        // 第一步：添加刪除動畫類別到行
        setDeletingRows(prev => new Set(prev.add(uid)));

        // 第二步：設置要刪除的 UID，觸發倒數刪除組件
        setDeleteUid(uid);
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deletePromotion(uid);
            if (res.success) {
                message.success('刪除成功');
                fetchPromotionList();
            } else {
                throw new Error(res.message || '刪除失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '刪除時發生錯誤');
        } finally {
            // 清除刪除狀態
            setDeleteUid(null);
            setDeletingRows(prev => {
                const newSet = new Set(prev);
                newSet.delete(uid);
                return newSet;
            });
        }
    };

    const handleDeleteCancel = () => {
        if (deleteUid) {
            // 取消刪除時清除動畫狀態
            setDeletingRows(prev => {
                const newSet = new Set(prev);
                newSet.delete(deleteUid);
                return newSet;
            });
        }
        setDeleteUid(null);
    };

    const handleAddNew = async () => {
        setModalLoading(true);
        setActiveTabKey('1'); // 重置到第一個 tab
        try {
            // 載入最新升遷資料作為預設值
            const { success, data, message: msg } = await getLatestPromotion(userId);

            let defaultData: Promotion;
            if (success && data) {
                // 使用最新資料作為預設值，但清空其他欄位和部門資料
                defaultData = {
                    ...data,
                    uid: '',
                    promotionType: '',
                    promotionDate: '',
                    effectiveDate: '',
                    promotionReason: '',
                    remark: '',
                    // 不預載入部門資料，讓用戶主動選取
                    expenseDepartmentChange: undefined,
                    serviceDepartmentChange: undefined
                };
                message.info('已載入最新職稱、職等、級數、任用資格、薪俸類型、薪俸、錄用類別作為預設值');
            } else {
                defaultData = createEmptyPromotion(userId);
            }

            setPromotionDetail(null);
            form.resetFields();

            // 設定預設值
            form.setFieldsValue({
                promotionType: '',
                jobTitle: defaultData.jobTitle,
                jobLevel: defaultData.jobLevel,
                jobRank: defaultData.jobRank,
                jobroleType: defaultData.jobroleType,
                salaryType: defaultData.salaryType,
                salaryAmount: defaultData.salaryAmount ? parseFloat(defaultData.salaryAmount) : null,
                categoryType: defaultData.categoryType,
                promotionDate: null,
                effectiveDate: null,
                promotionReason: '',
                remark: '',
                // 部門資料不預載入，讓用戶需要時才選取
                expenseDepartmentId: '',
                expenseChangeDate: null,
                expenseEffectiveDate: null,
                expenseChangeReason: '',
                expenseRemark: '',
                serviceDepartmentId: '',
                serviceDivisionId: '',
                serviceChangeDate: null,
                serviceEffectiveDate: null,
                serviceChangeReason: '',
                serviceRemark: ''
            });

            setIsModalOpen(true);
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '準備新增表單時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleModalCancel = () => {
        setIsModalOpen(false);
        setPromotionDetail(null);
        setActiveTabKey('1'); // 重置到第一個 tab
        form.resetFields();
    };



    const columns = [
        {
            title: '升遷類型',
            dataIndex: 'promotionTypeName',
            key: 'promotionTypeName',
            width: 100,
        },
        {
            title: '職稱',
            dataIndex: 'jobTitleName',
            key: 'jobTitleName',
            width: 120,
            render: (value: string, record: Promotion) => value || record.jobTitle
        },
        {
            title: '職等',
            dataIndex: 'jobLevelName',
            key: 'jobLevelName',
            width: 80,
            render: (value: string, record: Promotion) => value || record.jobLevel
        },
        {
            title: '級數',
            dataIndex: 'jobRankName',
            key: 'jobRankName',
            width: 80,
            render: (value: string, record: Promotion) => value || record.jobRank
        },
        {
            title: '任用資格',
            dataIndex: 'jobroleTypeName',
            key: 'jobroleTypeName',
            width: 100,
            render: (value: string, record: Promotion) => value || record.jobroleType || '-'
        },
        {
            title: '錄用類別',
            dataIndex: 'categoryTypeName',
            key: 'categoryTypeName',
            width: 100,
            render: (value: string, record: Promotion) => value || record.categoryType || '-'
        },
        {
            title: '薪俸類型',
            dataIndex: 'salaryTypeName',
            key: 'salaryTypeName',
            width: 100,
            render: (value: string, record: Promotion) => value || record.salaryType || '-'
        },
        {
            title: '薪俸',
            dataIndex: 'salaryAmount',
            key: 'salaryAmount',
            width: 120,
            render: (value: string, record: Promotion) => {
                if (!value || value === '0' || value === '') return '-';
                const amount = parseFloat(value);
                if (isNaN(amount)) return '-';

                // 根據薪俸類型決定顯示單位：薪點(1)顯示"點"，其他顯示"元"
                const unit = record.salaryType === '1' ? '點' : '元';
                return `${amount.toLocaleString()}${unit}`;
            }
        },
        {
            title: '升遷日期',
            dataIndex: 'promotionDate',
            key: 'promotionDate',
            width: 100,
        },
        {
            title: '生效日期',
            dataIndex: 'effectiveDate',
            key: 'effectiveDate',
            width: 100,
        },
        {
            title: '升遷原因',
            dataIndex: 'promotionReason',
            key: 'promotionReason',
            width: 150,
            ellipsis: true
        },
        {
            title: '備註',
            dataIndex: 'remark',
            key: 'remark',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-'
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (text: any, record: Promotion) => (
                <Space size="small">
                    <Button
                        type="link"
                        icon={<EditOutlined />}
                        onClick={() => handleRowClick(record.uid)}
                        style={{ padding: 0 }}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title="確認刪除"
                        description="確定要刪除這筆升遷異動資料嗎？"
                        onConfirm={() => handleDeleteConfirm(record.uid)}
                        okText="確認"
                        cancelText="取消"
                    >
                        <Button
                            type="link"
                            danger
                            icon={<DeleteOutlined />}
                            style={{ padding: 0 }}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    // 檢查是否有部門異動資訊
    const hasDepartmentChanges = (record: Promotion): boolean => {
        return Boolean(
            (record.expenseDepartmentChangeUid && record.expenseDepartmentChange?.expenseDepartmentName) ||
            (record.serviceDepartmentChangeUid && record.serviceDepartmentChange?.serviceDepartmentName)
        );
    };

    // 展開的部門異動資訊
    const expandedRowRender = (record: Promotion) => {
        const hasExpenseChange = record.expenseDepartmentChangeUid && record.expenseDepartmentChange?.expenseDepartmentName;
        const hasServiceChange = record.serviceDepartmentChangeUid && record.serviceDepartmentChange?.serviceDepartmentName;

        if (!hasExpenseChange && !hasServiceChange) {
            return null;
        }

        return (
            <div style={{ margin: '16px 0', padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
                <div style={{ marginBottom: '12px', fontWeight: 'bold', color: '#1890ff', fontSize: '14px' }}>
                    <ApartmentOutlined style={{ marginRight: '8px' }} />
                    部門異動資訊
                </div>

                {hasExpenseChange && (
                    <div style={{ marginBottom: hasServiceChange ? '12px' : '0' }}>
                        <div style={{ marginBottom: '4px' }}>
                            <BankOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                            <Text strong>開支部門異動</Text>
                        </div>
                        <div style={{ paddingLeft: '24px' }}>
                            <div><Text>部門：{record.expenseDepartmentChange?.expenseDepartmentName}</Text></div>
                            {record.expenseDepartmentChange?.effectiveDate && (
                                <div><Text type="secondary">生效日期：{record.expenseDepartmentChange.effectiveDate}</Text></div>
                            )}
                            {record.expenseDepartmentChange?.changeReason && (
                                <div><Text type="secondary">異動原因：{record.expenseDepartmentChange.changeReason}</Text></div>
                            )}
                        </div>
                    </div>
                )}

                {hasServiceChange && (
                    <div>
                        <div style={{ marginBottom: '4px' }}>
                            <TeamOutlined style={{ marginRight: '8px', color: '#722ed1' }} />
                            <Text strong>服務部門異動</Text>
                        </div>
                        <div style={{ paddingLeft: '24px' }}>
                            <div>
                                <Text>部門：{record.serviceDepartmentChange?.serviceDepartmentName}</Text>
                                {record.serviceDepartmentChange?.serviceDivisionName && (
                                    <Text style={{ marginLeft: '8px' }}>
                                        (組別：{record.serviceDepartmentChange.serviceDivisionName})
                                    </Text>
                                )}
                            </div>
                            {record.serviceDepartmentChange?.effectiveDate && (
                                <div><Text type="secondary">生效日期：{record.serviceDepartmentChange.effectiveDate}</Text></div>
                            )}
                            {record.serviceDepartmentChange?.changeReason && (
                                <div><Text type="secondary">異動原因：{record.serviceDepartmentChange.changeReason}</Text></div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const tabItems = [
        {
            key: '1',
            label: (
                <span>
                    <UserOutlined />
                    升遷基本資料
                </span>
            ),
            children: (
                <div>
                    {/* 基本資訊區塊 */}
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="升遷類型"
                                name="promotionType"
                                rules={[{ required: true, message: '請選擇升遷類型' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getPromotionTypeOptions}
                                    placeholder="請選擇升遷類型"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="生效日期"
                                name="effectiveDate"
                                rules={[{ required: true, message: '請選擇生效日期' }]}
                            >
                                <DatePicker placeholder="請選擇生效日期" style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 職務資訊區塊 */}
                    <Divider orientation="left" style={{ marginTop: 0, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#1890ff' }}>
                            <SettingOutlined style={{ marginRight: 8 }} />
                            職務資訊
                        </span>
                    </Divider>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="職稱"
                                name="jobTitle"
                                rules={[{ required: true, message: '請選擇職稱' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobtitleOptions}
                                    placeholder="請選擇職稱"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="職等"
                                name="jobLevel"
                                rules={[{ required: true, message: '請選擇職等' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobLevelOptions}
                                    placeholder="請選擇職等"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="級數"
                                name="jobRank"
                                rules={[{ required: true, message: '請選擇級數' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobRankOptions}
                                    placeholder="請選擇級數"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="任用資格"
                                name="jobroleType"
                                rules={[{ required: true, message: '請選擇任用資格' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getJobroleTypeOptions}
                                    placeholder="請選擇任用資格"
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="錄用類別"
                                name="categoryType"
                                rules={[{ required: true, message: '請選擇錄用類別' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getCategoryTypeOptions}
                                    placeholder="請選擇錄用類別"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            {/* 空白欄位保持佈局平衡 */}
                        </Col>
                    </Row>

                    {/* 薪俸資訊區塊 */}
                    <Divider orientation="left" style={{ marginTop: 0, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#52c41a' }}>
                            <BankOutlined style={{ marginRight: 8 }} />
                            薪俸資訊
                        </span>
                    </Divider>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                            <Form.Item
                                label="薪俸類型"
                                name="salaryType"
                                rules={[{ required: true, message: '請選擇薪俸類型' }]}
                            >
                                <ApiSelect
                                    fetchOptions={getSalaryTypeOptions}
                                    placeholder="請選擇薪俸類型"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="薪俸"
                                name="salaryAmount"
                                rules={[{ required: true, message: '請輸入薪俸金額' }]}
                            >
                                <InputNumber
                                    placeholder="請輸入薪俸金額"
                                    min={0}
                                    max={999999999}
                                    precision={2}
                                    style={{ width: '100%' }}
                                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 異動資訊區塊 */}
                    <Divider orientation="left" style={{ marginTop: 0, marginBottom: 16 }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#722ed1' }}>
                            <CalendarOutlined style={{ marginRight: 8 }} />
                            異動資訊
                        </span>
                    </Divider>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="升遷日期"
                                name="promotionDate"
                                rules={[{ required: true, message: '請選擇升遷日期' }]}
                            >
                                <DatePicker placeholder="請選擇升遷日期" style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>
                        <Col span={12} style={{ display: 'flex', alignItems: 'end' }}>
                            {/* 空白欄位保持佈局平衡 */}
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label="升遷原因"
                                name="promotionReason"
                            >
                                <Input.TextArea
                                    placeholder="請輸入升遷原因（選填）"
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label="備註"
                                name="remark"
                            >
                                <Input.TextArea
                                    placeholder="請輸入備註（選填）"
                                    rows={3}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </div>
            ),
        },
        {
            key: '2',
            label: (
                <span>
                    <BankOutlined />
                    開支部門異動
                </span>
            ),
            children: (
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="開支部門"
                            name="expenseDepartmentId"
                        >
                            <Select
                                placeholder="請選擇開支部門"
                                allowClear
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                }
                            >
                                {departmentOptions.map(dept => (
                                    <Option key={dept.departmentId} value={dept.departmentId}>
                                        {dept.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="異動日期"
                            name="expenseChangeDate"
                        >
                            <DatePicker placeholder="請選擇異動日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="生效日期"
                            name="expenseEffectiveDate"
                        >
                            <DatePicker placeholder="請選擇生效日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="異動原因"
                            name="expenseChangeReason"
                        >
                            <Input placeholder="請輸入異動原因" />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="備註"
                            name="expenseRemark"
                        >
                            <Input.TextArea
                                placeholder="請輸入備註"
                                rows={3}
                            />
                        </Form.Item>
                    </Col>
                </Row>
            ),
        },
        {
            key: '3',
            label: (
                <span>
                    <ApartmentOutlined />
                    服務部門異動
                </span>
            ),
            children: (
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            label="服務部門"
                            name="serviceDepartmentId"
                        >
                            <Select
                                placeholder="請選擇服務部門"
                                allowClear
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                }
                            >
                                {departmentOptions.map(dept => (
                                    <Option key={dept.departmentId} value={dept.departmentId}>
                                        {dept.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="服務組別"
                            name="serviceDivisionId"
                        >
                            <Select
                                placeholder="請選擇服務組別"
                                allowClear
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children?.toString() || '')?.toLowerCase()?.includes(input.toLowerCase())
                                }
                            >
                                {divisionOptions.map(div => (
                                    <Option key={div.divisionId} value={div.divisionId}>
                                        {div.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="異動日期"
                            name="serviceChangeDate"
                        >
                            <DatePicker placeholder="請選擇異動日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label="生效日期"
                            name="serviceEffectiveDate"
                        >
                            <DatePicker placeholder="請選擇生效日期" style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="異動原因"
                            name="serviceChangeReason"
                        >
                            <Input placeholder="請輸入異動原因" />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label="備註"
                            name="serviceRemark"
                        >
                            <Input.TextArea
                                placeholder="請輸入備註"
                                rows={3}
                            />
                        </Form.Item>
                    </Col>
                </Row>
            ),
        },
    ];

    return (
        <div className="promotion-info">
            <Card
                title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <UpOutlined />
                        <span>升遷異動資料</span>
                    </div>
                }
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        loading={modalLoading}
                    >
                        新增升遷異動
                    </Button>
                }
                style={{ marginBottom: 24 }}
            >
                {errorMsg && (
                    <div style={{ color: 'red', marginBottom: 16 }}>
                        錯誤: {errorMsg}
                    </div>
                )}

                <Table
                    columns={columns}
                    dataSource={promotionList}
                    rowKey="uid"
                    loading={loading}
                    size="small"
                    pagination={{
                        total: promotionList.length,
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
                    }}
                    scroll={{ x: 1520 }}
                    rowClassName={(record) =>
                        deletingRows.has(record.uid) ? 'row-deleting-pulse' : ''
                    }
                    expandable={{
                        expandedRowRender,
                        rowExpandable: hasDepartmentChanges,
                        expandIcon: ({ expanded, onExpand, record }) =>
                            hasDepartmentChanges(record) ? (
                                <Button
                                    type="text"
                                    size="small"
                                    icon={expanded ? <SwapOutlined rotate={90} /> : <SwapOutlined />}
                                    onClick={e => onExpand(record, e)}
                                    style={{
                                        color: expanded ? '#1890ff' : '#666',
                                        marginRight: '8px'
                                    }}
                                />
                            ) : null
                    }}
                />
            </Card>

            <Modal
                title={promotionDetail ? '編輯升遷異動' : '新增升遷異動'}
                open={isModalOpen}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                confirmLoading={modalLoading}
                width={1000}
                maskClosable={false}
            >
                <Form
                    form={form}
                    layout="vertical"
                    style={{ marginTop: 16 }}
                >
                    <Tabs
                        items={tabItems}
                        activeKey={activeTabKey}
                        onChange={setActiveTabKey}
                    />
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={() => handleDelete(deleteUid)}
                    onCancel={handleDeleteCancel}
                />
            )}
        </div>
    );
};

export default PromotionInfo;
