using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("鄉鎮市區資料管理")]
    public class DistrictController : ControllerBase
    {
        private readonly IDistrictService _Interface;

        public DistrictController(IDistrictService districtService)
        {
            _Interface = districtService;
        }

        [HttpGet]
        [Route("GetDistrict")]
        [SwaggerOperation(Summary = "取得鄉鎮市區列表", Description = "取得所有鄉鎮市區列表")]
        public async Task<IActionResult> GetDistrictList()
        {
            var districts = await _Interface.GetDistrictAsync();
            return Ok(districts);
        }

        [HttpGet]
        [Route("GetDistrict/{_districtId?}")]
        [SwaggerOperation(Summary = "取得鄉鎮市區明細", Description = "依ID取得鄉鎮市區明細")]
        public async Task<IActionResult> GetDistrictDetail(string _districtId)
        {
            var district = await _Interface.GetDistrictDetailAsync(_districtId);
            return Ok(district);
        }

        [HttpGet]
        [Route("GetDistrictByCity/{_cityId}")]
        [SwaggerOperation(Summary = "依縣市取得鄉鎮市區", Description = "依縣市ID取得所屬鄉鎮市區列表")]
        public async Task<IActionResult> GetDistrictByCity(string _cityId)
        {
            var districts = await _Interface.GetDistrictByCityAsync(_cityId);
            return Ok(districts);
        }

        [HttpPost]
        [Route("AddDistrict")]
        [SwaggerOperation(Summary = "新增鄉鎮市區", Description = "新增鄉鎮市區資料")]
        public async Task<IActionResult> AddDistrict([FromBody] DistrictDTO _data)
        {
            var (result, msg) = await _Interface.AddDistrictAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditDistrict")]
        [SwaggerOperation(Summary = "編輯鄉鎮市區", Description = "修改已存在之鄉鎮市區資料")]
        public async Task<IActionResult> EditDistrict([FromBody] DistrictDTO _data)
        {
            var (result, msg) = await _Interface.EditDistrictAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteDistrict")]
        [SwaggerOperation(Summary = "刪除鄉鎮市區", Description = "刪除已存在之鄉鎮市區資料")]
        public async Task<IActionResult> DeleteDistrict([FromBody] DistrictDTO _data)
        {
            var (result, msg) = await _Interface.DeleteDistrictAsync(_data);
            return Ok(new { result, msg });
        }
    }
}