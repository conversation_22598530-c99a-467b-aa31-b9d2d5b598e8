// src/services/salaryService.ts
import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 薪資資料介面
export interface Salary {
    userId: string;
    salaryStatus: string;
    salaryStatusName?: string;
    employerContributionRate: number;
    employeeContributionType: string;
    employeeContributionTypeName: string;
    employeeContributionRate: number;
    employeeContributionAmount: number;
    taxType: string;
    taxTypeName: string;
    fixedTaxRate: number;
    fixedTaxAmount: number;
    transferAccount: string;
    remark: string;
    createTime?: number | null;
    createUserId?: string | null;
    updateTime?: number | null;
    updateUserId?: string | null;
    deleteTime?: number | null;
    deleteUserId?: string | null;
    isDeleted: boolean;
}

// 建立空白薪資資料物件
export const createEmptySalary = (): Salary => ({
    userId: "",
    salaryStatus: "",
    salaryStatusName: "",
    employerContributionRate: 0,
    employeeContributionType: "",
    employeeContributionTypeName: "",
    employeeContributionRate: 0,
    employeeContributionAmount: 0,
    taxType: "",
    taxTypeName: "",
    fixedTaxRate: 0,
    fixedTaxAmount: 0,
    transferAccount: "",
    remark: "",
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false,
});

// 取得薪資明細
export async function getSalaryDetail(userId: string): Promise<ApiResponse<Salary>> {
    return await httpClient(`${apiEndpoints.getSalaryDetail}/${userId}`, {
        method: "GET",
    });
}

// 編輯薪資資料
export async function editSalary(data: Partial<Salary>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editSalary, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯薪資資料失敗",
        };
    }
}
