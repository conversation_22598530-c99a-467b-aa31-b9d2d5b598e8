﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 學歷資料表
    /// </summary>
    public class Education : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string Uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用者編號

        [Comment("學位代號")]
        [Column(TypeName = "nvarchar(3)")]
        public string DegreeType { get; set; } // 學位代號

        [Comment("結業代號")]
        [Column(TypeName = "nvarchar(3)")]
        public string GraduateType { get; set; } // 結業代號

        [Comment("學校名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string SchoolName { get; set; } // 學校名稱

        [Comment("院系科別")]
        [Column(TypeName = "nvarchar(100)")]
        public string DepartmentName { get; set; } // 院系科別

        [Comment("修業起日")]
        [Column(TypeName = "bigint")]
        public long? PeriodDateStart { get; set; } // 修業起日

        [Comment("修業迄日")]
        [Column(TypeName = "bigint")]
        public long? PeriodDateEnd { get; set; } // 修業迄日

        [Comment("發證日期")]
        [Column(TypeName = "bigint")]
        public long? CertificateDate { get; set; } // 發證日期

        [Comment("證件字號")]
        [Column(TypeName = "nvarchar(100)")]
        public string CertificateNumber { get; set; } // 證件字號

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Remark { get; set; } // 備註

        public Education()
        {
            UserId = "";
            Uid = "";
            DegreeType = "";
            GraduateType = "";
            SchoolName = "";
            DepartmentName = "";
            PeriodDateStart = null;
            PeriodDateEnd = null;
            CertificateDate = null;
            CertificateNumber = "";
            Remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class EducationDTO : ModelBaseEntityDTO
    {
        public string Uid { get; set; } // 資料編號
        public string UserId { get; set; } // 使用者編號
        public string DegreeType { get; set; } // 學位代號
        public string? DegreeTypeName { get; set; } // 學位代號
        public string GraduateType { get; set; } // 結業代號
        public string? GraduateTypeName { get; set; } // 結業代號
        public string? SchoolName { get; set; } // 學校名稱
        public string? DepartmentName { get; set; } // 院系科別
        public string? PeriodDateStart { get; set; } // 修業起日
        public string? PeriodDateEnd { get; set; } // 修業迄日
        public string? CertificateDate { get; set; } // 發證日期
        public string? CertificateNumber { get; set; } // 證件字號
        public string? Remark { get; set; } // 備註
        public EducationDTO()
        {
            UserId = "";
            Uid = "";
            DegreeType = "";
            DegreeTypeName = "";
            GraduateType = "";
            GraduateTypeName = "";
            SchoolName = "";
            DepartmentName = "";
            PeriodDateStart = "";
            PeriodDateEnd = "";
            CertificateDate = "";
            CertificateNumber = "";
            Remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

