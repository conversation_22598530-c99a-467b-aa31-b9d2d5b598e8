using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Attributes;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 庫存品 </summary>
public class Item : ModelBaseEntity
{
    /// <summary> 庫存品編號 </summary>
    [Key]
    [Comment("庫存品編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ItemID { get; set; }

    /// <summary> 自定義編號 </summary>
    [Comment("自定義編號")]
    [Column(TypeName = "nvarchar(100)")]
    public string CustomNO { get; set; }
    
    /// <summary> 國際條碼 </summary>
    [Comment("國際條碼")]
    [Column(TypeName = "nvarchar(100)")]
    public string InternationalBarCode { get; set; }

    /// <summary> 名稱 </summary>
    [Comment("名稱")]
    [Column(TypeName = "nvarchar(100)")]
    public string Name { get; set; }

    /// <summary> 單位 </summary>
    [Comment("單位")]
    [Column(TypeName = "nvarchar(50)")]
    public string Unit { get; set; }

    /// <summary> 庫存品分類編號 </summary>
    [Comment("庫存品分類編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid? ItemCategoryID { get; set; }

    /// <summary> 庫存品分類 </summary>
    public ItemCategory? ItemCategory { get; set; }

    /// <summary> 描述 </summary>
    [Comment("描述")]
    [Column(TypeName = "nvarchar(500)")]
    public string Description { get; set; }

    /// <summary> Sort Code </summary>
    [Comment("排序")]
    [Column(TypeName = "int")]
    public int SortCode { get; set; }

    /// <summary> 停售 </summary>
    [Comment("停售")]
    public bool IsStop { get; set; }

    /// <summary> 商品稅別 </summary>
    [Comment("商品稅別")]
    public ItemTaxType TaxType { get; set; }

    /// <summary> 庫存品售價 </summary>
    public ICollection<ItemPrice>? Prices { get; set; } = [];

    /// <summary> 建構式 </summary>
    public Item()
    {
        ItemID = Guid.NewGuid();
        CustomNO = string.Empty;
        InternationalBarCode = string.Empty;
        Name = string.Empty;
        Unit = string.Empty;
        ItemCategoryID = null;
        Description = string.Empty;
        SortCode = 0;
        IsStop = false;
        TaxType = ItemTaxType.Taxable; // 預設為應稅
    }
}

/// <summary> 庫存品DTO </summary>
public class ItemDTO : ModelBaseEntityDTO
{
    /// <summary> 庫存品編號 </summary>
    public Guid ItemID { get; set; }

    /// <summary> 自定義編號 </summary>
    public string CustomNO { get; set; }

    /// <summary> 國際條碼 </summary>
    public string InternationalBarCode { get; set; }

    /// <summary> 名稱 </summary>
    public string Name { get; set; }

    /// <summary> 物品分類ID </summary>
    public Guid? ItemCategoryID { get; set; }

    /// <summary> 單位 </summary>
    public string Unit { get; set; }

    /// <summary> 描述 </summary>
    public string Description { get; set; }

    /// <summary> 停售 </summary>
    public bool IsStop { get; set; }

    /// <summary> 商品稅別 </summary>
    public ItemTaxType TaxType { get; set; }

    /// <summary> 庫存品售價 </summary>
    public List<ItemPriceDTO>? Prices { get; set; } = [];

    /// <summary> 建構式 </summary>
    public ItemDTO()
    {
        ItemID = Guid.NewGuid();
        CustomNO = string.Empty;
        InternationalBarCode = string.Empty;
        Name = string.Empty;
        Unit = string.Empty;
        ItemCategoryID = null;
        Description = string.Empty;
        IsStop = false;
        TaxType = ItemTaxType.Taxable; // 預設為應稅
        Prices = null;
    }
}