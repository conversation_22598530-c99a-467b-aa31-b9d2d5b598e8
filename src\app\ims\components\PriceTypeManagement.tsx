"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, Button, Space, Tag, Row, Col, Switch, Popconfirm, message, Card, Badge, Alert } from 'antd';
import type { InputRef } from 'antd';
import { PlusOutlined, EditOutlined, SaveOutlined, UndoOutlined, TagsOutlined } from '@ant-design/icons';
import { PriceType } from '@/services/ims/ItemService';
import { addPriceType, editPriceType, deletePriceType } from '@/services/ims/PriceTypeService';

interface PriceTypeManagementProps {
  visible: boolean;
  onClose: () => void;
  priceTypes: PriceType[];
  onDataChange: () => void; // 回調函數，用於通知父組件重新載入資料
}

const PriceTypeManagement: React.FC<PriceTypeManagementProps> = ({
  visible,
  onClose,
  priceTypes,
  onDataChange
}) => {
  // 狀態管理
  const [selectedPriceType, setSelectedPriceType] = useState<PriceType | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [operationMode, setOperationMode] = useState<'add' | 'edit'>('add');

  // Focus management refs
  const nameInputRef = useRef<InputRef>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);

  // 移動端檢測狀態
  const [isMobile, setIsMobile] = useState(false);

  // 滾動到輸入框的函數
  const scrollToInput = () => {
    setTimeout(() => {
      if (nameInputRef.current && formContainerRef.current) {
        // 先設置焦點
        nameInputRef.current.focus();

        // 然後滾動到輸入框
        const inputElement = nameInputRef.current.input;
        if (inputElement) {
          inputElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }
      }
    }, 150);
  };

  // 重置表單為新增模式
  const handleAddNew = () => {
    setSelectedPriceType(null);
    setOperationMode('add');
    form.resetFields();
    // 確保新增時 allowStop 預設為 true，isStop 預設為 false
    form.setFieldsValue({
      allowStop: true,
      isStop: false
    });
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 修正邏輯：新增時強制設定 allowStop: true，編輯時保持原有值
      const submitData = selectedPriceType
        ? { ...selectedPriceType, ...values } // 編輯時保持原有的 allowStop 值
        : { ...values, allowStop: true }; // 新增時強制設為允許停用

      console.log('🔄 提交價格類型資料:', submitData);

      const response = selectedPriceType
        ? await editPriceType(submitData)
        : await addPriceType(submitData);

      if (response.success) {
        message.success(selectedPriceType ? '價格類型更新成功' : '價格類型新增成功');
        // 重置表單以便繼續新增
        handleAddNew();
        // 通知父組件重新載入資料
        onDataChange();
        console.log('✅ 價格類型操作成功');
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('❌ 價格類型操作失敗:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除
  const handleDelete = async (priceTypeId: string) => {
    try {
      console.log('🔄 刪除價格類型:', priceTypeId);
      const response = await deletePriceType(priceTypeId);
      if (response.success) {
        message.success('價格類型刪除成功');
        onDataChange();
        console.log('✅ 價格類型刪除成功');
      } else {
        message.error(response.message || '刪除失敗');
      }
    } catch (error) {
      console.error('❌ 刪除價格類型失敗:', error);
      message.error('刪除失敗，請重試');
    }
  };

  // 處理編輯
  const handleEdit = (priceType: PriceType) => {
    setSelectedPriceType(priceType);
    setOperationMode('edit');
    form.setFieldsValue(priceType);

    // 設置焦點並滾動到輸入框
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
        nameInputRef.current.select();
        scrollToInput();
      }
    }, 100);
  };

  // 當互動視窗打開時，重置表單並設置焦點
  useEffect(() => {
    if (visible) {
      handleAddNew();
      // 延遲設置焦點並滾動，確保互動視窗完全渲染
      scrollToInput();
    }
  }, [visible]);

  // 當操作模式改變時設置焦點
  useEffect(() => {
    if (visible && (operationMode === 'edit' || operationMode === 'add')) {
      setTimeout(() => {
        if (nameInputRef.current) {
          nameInputRef.current.focus();
          // 如果是編輯模式，選中所有文字
          if (operationMode === 'edit') {
            nameInputRef.current.select();
          }
          scrollToInput();
        }
      }, 100);
    }
  }, [operationMode, visible]);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 操作模式配置
  const modeConfig = {
    add: {
      title: '新增價格類型',
      color: '#52c41a',
      icon: <PlusOutlined />,
      buttonText: '新增',
      cardTitle: '新增模式',
      description: '建立新的價格類型'
    },
    edit: {
      title: '編輯價格類型',
      color: '#1890ff',
      icon: <EditOutlined />,
      buttonText: '更新',
      cardTitle: '編輯模式',
      description: `正在編輯：${selectedPriceType?.name || ''}`
    }
  };

  const currentMode = modeConfig[operationMode];

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <TagsOutlined style={{ color: currentMode.color }} />
          <Badge color={currentMode.color} />
          <span>{currentMode.title}</span>
        </div>
      }
      open={visible}
      onCancel={() => {
        // 清理狀態並關閉
        handleAddNew();
        onClose();
      }}
      footer={[
        <Button key="close" onClick={() => {
          // 清理狀態並關閉
          handleAddNew();
          onClose();
        }}>
          關閉
        </Button>
      ]}
      maskClosable={true}
      keyboard={true}
      focusTriggerAfterClose={false}
      width={isMobile ? '95%' : 800}
      style={isMobile ? { top: 20, margin: '0 auto' } : {}}
      zIndex={1050}
    >
      <Row gutter={isMobile ? [8, 8] : [24, 16]}>
        {/* 左側：現有價格類型列表 */}
        <Col xs={24} lg={12}>
          <Card
            size="small"
            title="現有價格類型"
            extra={<Badge count={priceTypes.length} style={{ backgroundColor: '#52c41a' }} />}
          >
            <div style={{
              maxHeight: 400,
              overflow: 'auto',
              padding: '4px'
            }}>
              {priceTypes.map((priceType) => (
                <Card
                  key={priceType.priceTypeID}
                  size="small"
                  style={{
                    marginBottom: 8,
                    border: selectedPriceType?.priceTypeID === priceType.priceTypeID
                      ? '2px solid #1890ff'
                      : '1px solid #f0f0f0',
                    backgroundColor: selectedPriceType?.priceTypeID === priceType.priceTypeID
                      ? '#f6ffed'
                      : '#fafafa'
                  }}
                  styles={{ body: { padding: '12px' } }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {/* 標題行 */}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '4px'
                    }}>
                      <div style={{ flex: 1 }}>
                        <div style={{
                          fontWeight: 'bold',
                          fontSize: '14px',
                          color: '#262626',
                          marginBottom: '4px'
                        }}>
                          {priceType.name}
                        </div>
                        {priceType.description && (
                          <div style={{
                            fontSize: '12px',
                            color: '#8c8c8c',
                            lineHeight: '1.4'
                          }}>
                            {priceType.description}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 標籤行 */}
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '4px',
                      alignItems: 'center'
                    }}>
                      <Tag
                        color={priceType.isStop ? 'red' : 'green'}
                        style={{ margin: 0, fontSize: '11px' }}
                      >
                        {priceType.isStop ? '停用' : '啟用'}
                      </Tag>
                      <Tag
                        color={priceType.allowStop ? 'blue' : 'orange'}
                        style={{ margin: 0, fontSize: '11px' }}
                      >
                        {priceType.allowStop ? '可停用' : '不可停用'}
                      </Tag>
                      {priceType.sortCode && (
                        <Tag
                          color="purple"
                          style={{ margin: 0, fontSize: '11px' }}
                        >
                          排序: {priceType.sortCode}
                        </Tag>
                      )}
                    </div>

                    {/* 操作按鈕行 */}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: '8px',
                      marginTop: '4px'
                    }}>
                      <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(priceType)}
                        style={{
                          padding: '0 8px',
                          height: '24px',
                          fontSize: '12px'
                        }}
                      >
                        編輯
                      </Button>
                      <Popconfirm
                        title="確定要刪除此價格類型嗎？"
                        onConfirm={() => handleDelete(priceType.priceTypeID)}
                        placement="topRight"
                      >
                        <Button
                          type="link"
                          danger
                          size="small"
                          style={{
                            padding: '0 8px',
                            height: '24px',
                            fontSize: '12px'
                          }}
                        >
                          刪除
                        </Button>
                      </Popconfirm>
                    </div>
                  </div>
                </Card>
              ))}

              {priceTypes.length === 0 && (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 20px',
                  color: '#8c8c8c'
                }}>
                  <div style={{ fontSize: '14px' }}>尚無價格類型</div>
                  <div style={{ fontSize: '12px', marginTop: '4px' }}>
                    請點擊左側表單新增價格類型
                  </div>
                </div>
              )}
            </div>
          </Card>
        </Col>
        {/* 右側：表單區域 */}
        <Col xs={24} lg={12}>
          <div ref={formContainerRef}>
          <Card
            size="small"
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {currentMode.icon}
                <span style={{ color: currentMode.color }}>{currentMode.cardTitle}</span>
              </div>
            }
            extra={
              <Button
                type="dashed"
                size="small"
                icon={<UndoOutlined />}
                onClick={handleAddNew}
              >
                重置為新增
              </Button>
            }
            style={{
              borderColor: currentMode.color,
              borderWidth: 2
            }}
          >
            <Alert
              message={currentMode.description}
              type={operationMode === 'add' ? 'success' : 'info'}
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
            >
              <Form.Item
                label="價格類型名稱"
                name="name"
                rules={[{ required: true, message: '請輸入價格類型名稱' }]}
              >
                <Input
                  ref={nameInputRef}
                  placeholder="請輸入價格類型名稱"
                  autoFocus={true}
                />
              </Form.Item>

              <Form.Item
                label="描述"
                name="description"
              >
                <Input.TextArea rows={3} placeholder="請輸入價格類型描述" />
              </Form.Item>

              <Row gutter={isMobile ? 8 : 16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="排序碼"
                    name="sortCode"
                  >
                    <Input type="number" placeholder="請輸入排序碼" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="狀態"
                    name="isStop"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="停用" unCheckedChildren="啟用" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="允許停用"
                name="allowStop"
                valuePropName="checked"
                tooltip="系統預設價格類型允許停用，此設定無法修改以確保資料完整性"
              >
                <Switch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  disabled={true}
                  defaultChecked={true}
                />
              </Form.Item>

              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={operationMode === 'add' ? <PlusOutlined /> : <SaveOutlined />}
                style={{
                  marginTop: 16,
                  backgroundColor: currentMode.color,
                  borderColor: currentMode.color,
                  width: '100%',
                  borderRadius: '6px',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                  fontWeight: '500'
                }}
              >
                {currentMode.buttonText}
              </Button>
            </Form>
          </Card>
          </div>
        </Col>

      </Row>
    </Modal>
  );
};

export default PriceTypeManagement;
