﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using AutoMapper;
using OfficeOpenXml;
using FAST_ERP_Backend.Helper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AssetService : IAssetService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly IMapper _mapper;
        private readonly IDepreciationFormDetailService _depreciationService;

        public AssetService(Baseform baseform, ERPDbContext context, IMapper mapper, IDepreciationFormDetailService depreciationService)
        {
            _baseform = baseform;
            _context = context;
            _mapper = mapper;
            _depreciationService = depreciationService;
        }

        /// <summary>
        /// 取得新的財產編號
        /// </summary>
        /// <param name="subject">科目</param>
        /// <param name="subSubject">子目</param>
        /// <param name="category">類別</param>
        /// <returns>新的財產編號</returns>
        public async Task<(bool success, string assetNo, string message)> GetNewAssetNoAsync(string subject, string subSubject, string category)
        {
            try
            {
                // 驗證輸入參數
                if (string.IsNullOrEmpty(subject) || string.IsNullOrEmpty(subSubject) || string.IsNullOrEmpty(category))
                {
                    return (false, string.Empty, "科目、子目和類別都不能為空");
                }

                // 組合前綴
                string prefix = $"{subject}{subSubject}{category}";

                // 查詢現有的財產編號
                var existingAssetNos = await _context.Set<Asset>()
                    .Where(a => a.AssetNo.StartsWith(prefix))
                    .Select(a => a.AssetNo)
                    .ToListAsync();

                // 找出最大的流水號
                int maxSerial = 0;
                foreach (var assetNo in existingAssetNos)
                {
                    if (assetNo.Length > prefix.Length)
                    {
                        string serialPart = assetNo.Substring(prefix.Length);
                        if (int.TryParse(serialPart, out int currentSerial))
                        {
                            maxSerial = Math.Max(maxSerial, currentSerial);
                        }
                    }
                }

                // 產生新的流水號
                int newSerial = maxSerial + 1;
                string serialNumber = newSerial.ToString("D5"); // 補足5位數
                string newAssetNo = $"{prefix}{serialNumber}";

                return (true, newAssetNo, "產生財產編號成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"產生財產編號時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }
                return (false, string.Empty, $"產生財產編號失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得財產列表
        /// </summary>
        /// <returns>財產列表</returns>
        public async Task<List<AssetWithAccessoriesDTO>> GetAssetAsync()
        {
            try
            {
                // 查詢所有財產
                var assets = await _context.Set<Asset>()
                    .Where(a => a.DeleteTime == 0 || a.DeleteTime == null)
                    .OrderBy(a => a.AssetNo)
                    .AsNoTracking()
                    .ToListAsync();

                if (assets.Count == 0)
                {
                    return new List<AssetWithAccessoriesDTO>();
                }

                // 取得所有財產ID
                var assetIds = assets.Select(a => a.AssetId).ToList();

                // 取得所有部門ID、組別ID和單位ID
                var departmentIds = assets.Where(a => a.DepartmentId != null).Select(a => a.DepartmentId).Distinct().ToList();
                var divisionIds = assets.Where(a => a.DivisionId != null).Select(a => a.DivisionId).Distinct().ToList();
                var unitIds = assets.Where(a => a.UnitId != null).Select(a => a.UnitId).Distinct().ToList();

                // 批量查詢部門、組別和單位資料
                var departments = await _context.Set<Department>()
                    .Where(d => departmentIds.Contains(d.DepartmentId) && (d.DeleteTime == 0 || d.DeleteTime == null))
                        .AsNoTracking()
                    .ToDictionaryAsync(d => d.DepartmentId, d => d);

                var divisions = await _context.Set<Division>()
                    .Where(d => divisionIds.Contains(d.DivisionId) && (d.DeleteTime == 0 || d.DeleteTime == null))
                        .AsNoTracking()
                    .ToDictionaryAsync(d => d.DivisionId, d => d);

                var units = await _context.Set<Unit>()
                    .Where(u => unitIds.Contains(u.UnitId) && (u.DeleteTime == 0 || u.DeleteTime == null))
                        .AsNoTracking()
                    .ToDictionaryAsync(u => u.UnitId, u => u);

                // 批量查詢所有附屬設備
                var accessoryEquipments = await _context.Set<AccessoryEquipment>()
                    .Where(ae => assetIds.Contains(ae.AssetId) && (ae.DeleteTime == 0 || ae.DeleteTime == null))
                    .AsNoTracking()
                    .ToListAsync();

                // 將附屬設備按財產ID分組
                var accessoryEquipmentsByAssetId = accessoryEquipments
                    .GroupBy(ae => ae.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 批量查詢所有保險單位關聯
                var insuranceUnitMappings = await _context.Set<AssetInsuranceUnitMapping>()
                    .Where(aiu => assetIds.Contains(aiu.AssetId))
                    .AsNoTracking()
                    .ToListAsync();

                // 取得所有保險單位ID
                var insuranceUnitIds = insuranceUnitMappings.Select(m => m.InsuranceUnitId).Distinct().ToList();

                // 批量查詢所有保險單位
                var insuranceUnits = await _context.Set<InsuranceUnit>()
                    .Where(iu => insuranceUnitIds.Contains(iu.InsuranceUnitId) && (iu.DeleteTime == 0 || iu.DeleteTime == null))
                    .AsNoTracking()
                    .ToDictionaryAsync(iu => iu.InsuranceUnitId, iu => iu);

                // 將保險單位關聯按財產ID分組
                var insuranceUnitMappingsByAssetId = insuranceUnitMappings
                    .GroupBy(m => m.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 批量查詢所有攤提來源關聯
                var amortizationSourceMappings = await _context.Set<AssetAmortizationSourceMapping>()
                    .Where(aas => assetIds.Contains(aas.AssetId))
                    .AsNoTracking()
                    .ToListAsync();

                // 取得所有攤提來源ID
                var amortizationSourceIds = amortizationSourceMappings.Select(m => m.AmortizationSourceId).Distinct().ToList();

                // 批量查詢所有攤提來源
                var amortizationSources = await _context.Set<AmortizationSource>()
                    .Where(ams => amortizationSourceIds.Contains(ams.AmortizationSourceId) && (ams.DeleteTime == 0 || ams.DeleteTime == null))
                    .AsNoTracking()
                    .ToDictionaryAsync(ams => ams.AmortizationSourceId, ams => ams);

                // 將攤提來源關聯按財產ID分組
                var amortizationSourceMappingsByAssetId = amortizationSourceMappings
                    .GroupBy(m => m.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 批量查詢所有財產來源關聯
                var assetSourceMappings = await _context.Set<AssetAssetSourceMapping>()
                    .Where(aas => assetIds.Contains(aas.AssetId))
                    .AsNoTracking()
                    .ToListAsync();

                // 取得所有財產來源ID
                var assetSourceIds = assetSourceMappings.Select(m => m.AssetSourceId).Distinct().ToList();

                // 批量查詢所有財產來源
                var assetSources = await _context.Set<AssetSource>()
                    .Where(ass => assetSourceIds.Contains(ass.AssetSourceId) && (ass.DeleteTime == 0 || ass.DeleteTime == null))
                    .AsNoTracking()
                    .ToDictionaryAsync(ass => ass.AssetSourceId, ass => ass);

                // 將財產來源關聯按財產ID分組
                var assetSourceMappingsByAssetId = assetSourceMappings
                    .GroupBy(m => m.AssetId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 組裝結果
                List<AssetWithAccessoriesDTO> result = new List<AssetWithAccessoriesDTO>();

                foreach (var asset in assets)
                {
                    // 建立 DTO 實例
                    var assetDto = new AssetWithAccessoriesDTO
                    {
                        Asset = _mapper.Map<AssetDTO>(asset),
                        AccessoryEquipments = new List<AccessoryEquipmentDTO>(),
                        InsuranceUnits = new List<InsuranceUnitDTO>(),
                        AmortizationSources = new List<AmortizationSourceDTO>(),
                        AssetSources = new List<AssetSourceDTO>()
                    };

                    // 設定部門名稱
                    if (asset.DepartmentId != null && departments.TryGetValue(asset.DepartmentId, out var department))
                    {
                        assetDto.Asset.DepartmentName = department.Name;
                    }

                    // 設定組別名稱
                    if (asset.DivisionId != null && divisions.TryGetValue(asset.DivisionId, out var division))
                    {
                        assetDto.Asset.DivisionName = division.Name;
                    }

                    // 設定單位名稱
                    if (asset.UnitId != null && units.TryGetValue(asset.UnitId, out var unit))
                    {
                        assetDto.Asset.UnitName = unit.Name;
                    }

                    // 設定附屬設備 - 轉換為DTO
                    if (accessoryEquipmentsByAssetId.TryGetValue(asset.AssetId, out var equipments))
                    {
                        assetDto.AccessoryEquipments = _mapper.Map<List<AccessoryEquipmentDTO>>(equipments);
                    }

                    // 設定保險單位
                    if (insuranceUnitMappingsByAssetId.TryGetValue(asset.AssetId, out var insuranceMappings))
                    {
                        foreach (var mapping in insuranceMappings)
                        {
                            if (insuranceUnits.TryGetValue(mapping.InsuranceUnitId, out var insuranceUnit))
                            {
                                assetDto.InsuranceUnits.Add(_mapper.Map<InsuranceUnitDTO>(insuranceUnit));
                            }
                        }
                    }

                    // 設定攤提來源
                    if (amortizationSourceMappingsByAssetId.TryGetValue(asset.AssetId, out var amortizationMappings))
                    {
                        foreach (var mapping in amortizationMappings)
                        {
                            if (amortizationSources.TryGetValue(mapping.AmortizationSourceId, out var amortizationSource))
                            {
                                assetDto.AmortizationSources.Add(_mapper.Map<AmortizationSourceDTO>(amortizationSource));
                            }
                        }
                    }

                    // 設定財產來源
                    if (assetSourceMappingsByAssetId.TryGetValue(asset.AssetId, out var sourceRelations))
                    {
                        foreach (var mapping in sourceRelations)
                        {
                            if (assetSources.TryGetValue(mapping.AssetSourceId, out var assetSource))
                            {
                                assetDto.AssetSources.Add(_mapper.Map<AssetSourceDTO>(assetSource));
                            }
                        }
                    }

                    result.Add(assetDto);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查詢財產列表時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }
                // 如果發生異常，返回空列表
                return new List<AssetWithAccessoriesDTO>();
            }
        }

        /// <summary>
        /// 編輯財產
        /// </summary>
        /// <param name="_data">財產資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> EditAssetAsync(AssetWithAccessoriesDTO _data)
        {
            try
            {
                // 檢查財產是否存在
                var existingAsset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetId == _data.Asset.AssetId && (a.DeleteTime == 0 || a.DeleteTime == null));

                if (existingAsset == null)
                {
                    return (false, "找不到指定的財產");
                }

                // 更新財產資料
                _mapper.Map(_data.Asset, existingAsset);
                existingAsset.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                existingAsset.UpdateUserId = _data.Asset.UpdateUserId;

                // 財產狀態如果有更新的時候，更新狀態異動日期
                if (existingAsset.AssetStatusId != _data.Asset.AssetStatusId)
                {
                    existingAsset.StatusChangeDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                // 更新附屬設備
                var existingAccessories = await _context.Set<AccessoryEquipment>()
                    .Where(ae => ae.AssetId == _data.Asset.AssetId && (ae.DeleteTime == 0 || ae.DeleteTime == null))
                        .ToListAsync();

                // 刪除不在新列表中的附屬設備
                foreach (var existingAccessory in existingAccessories)
                {
                    if (!_data.AccessoryEquipments.Any(ae => ae.AccessoryEquipmentId == existingAccessory.AccessoryEquipmentId))
                    {
                        existingAccessory.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        existingAccessory.DeleteUserId = _data.Asset.UpdateUserId;
                    }
                }

                // 新增或更新附屬設備
                foreach (var accessoryDto in _data.AccessoryEquipments)
                {
                    var existingAccessory = existingAccessories
                        .FirstOrDefault(ae => ae.AccessoryEquipmentId == accessoryDto.AccessoryEquipmentId);

                    if (existingAccessory != null)
                    {
                        // 更新現有附屬設備
                        _mapper.Map(accessoryDto, existingAccessory);
                        existingAccessory.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        existingAccessory.UpdateUserId = _data.Asset.UpdateUserId;
                    }
                    else
                    {
                        // 新增附屬設備
                        var newAccessory = _mapper.Map<AccessoryEquipment>(accessoryDto);
                        newAccessory.AssetId = _data.Asset.AssetId;
                        newAccessory.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        newAccessory.CreateUserId = _data.Asset.UpdateUserId;
                        await _context.Set<AccessoryEquipment>().AddAsync(newAccessory);
                    }
                }

                // 更新保險單位關聯
                var existingMappings = await _context.Set<AssetInsuranceUnitMapping>()
                    .Where(m => m.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                // 刪除不在新列表中的關聯
                foreach (var mapping in existingMappings)
                {
                    if (!_data.InsuranceUnits.Any(iu => iu.InsuranceUnitId == mapping.InsuranceUnitId))
                    {
                        _context.Set<AssetInsuranceUnitMapping>().Remove(mapping);
                    }
                }

                // 新增或更新保險單位
                foreach (var insuranceUnitDto in _data.InsuranceUnits)
                {
                    // 檢查保險單位是否已存在
                    var existingInsuranceUnit = await _context.Set<InsuranceUnit>()
                        .FirstOrDefaultAsync(iu => iu.InsuranceUnitId == insuranceUnitDto.InsuranceUnitId);

                    if (existingInsuranceUnit == null)
                    {
                        // 新增保險單位
                        var insuranceUnit = _mapper.Map<InsuranceUnit>(insuranceUnitDto);
                        await _context.Set<InsuranceUnit>().AddAsync(insuranceUnit);
                        await _context.SaveChangesAsync();
                    }

                    // 檢查關聯是否已存在
                    if (!existingMappings.Any(m => m.InsuranceUnitId == insuranceUnitDto.InsuranceUnitId))
                    {
                        // 新增關聯
                        var mapping = new AssetInsuranceUnitMapping
                        {
                            AssetId = _data.Asset.AssetId,
                            InsuranceUnitId = insuranceUnitDto.InsuranceUnitId,
                            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                            CreateUserId = _data.Asset.UpdateUserId,
                            IsDeleted = false
                        };
                        await _context.Set<AssetInsuranceUnitMapping>().AddAsync(mapping);
                    }
                }

                // 更新攤提來源關聯
                var existingAmortizationMappings = await _context.Set<AssetAmortizationSourceMapping>()
                    .Where(m => m.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                // 刪除不在新列表中的關聯
                foreach (var mapping in existingAmortizationMappings)
                {
                    if (!_data.AmortizationSources.Any(ams => ams.AmortizationSourceId == mapping.AmortizationSourceId))
                    {
                        _context.Set<AssetAmortizationSourceMapping>().Remove(mapping);
                    }
                }

                // 新增新的關聯
                foreach (var amortizationSourceDto in _data.AmortizationSources)
                {
                    if (!existingAmortizationMappings.Any(m => m.AmortizationSourceId == amortizationSourceDto.AmortizationSourceId))
                    {
                        var mapping = new AssetAmortizationSourceMapping
                        {
                            AssetId = _data.Asset.AssetId,
                            AmortizationSourceId = amortizationSourceDto.AmortizationSourceId
                        };
                        await _context.Set<AssetAmortizationSourceMapping>().AddAsync(mapping);
                    }
                }

                // 更新財產來源關聯
                var existingAssetSourceMappings = await _context.Set<AssetAssetSourceMapping>()
                    .Where(m => m.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                // 刪除不在新列表中的關聯
                foreach (var mapping in existingAssetSourceMappings)
                {
                    if (!_data.AssetSources.Any(ass => ass.AssetSourceId == mapping.AssetSourceId))
                    {
                        _context.Set<AssetAssetSourceMapping>().Remove(mapping);
                    }
                }

                // 新增新的關聯
                foreach (var assetSourceDto in _data.AssetSources)
                {
                    if (!existingAssetSourceMappings.Any(m => m.AssetSourceId == assetSourceDto.AssetSourceId))
                    {
                        var mapping = new AssetAssetSourceMapping
                        {
                            AssetId = _data.Asset.AssetId,
                            AssetSourceId = assetSourceDto.AssetSourceId
                        };
                        await _context.Set<AssetAssetSourceMapping>().AddAsync(mapping);
                    }
                }

                await _context.SaveChangesAsync();
                return (true, "更新財產成功");
            }
            catch (Exception ex)
            {
                return (false, $"更新財產失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除財產
        /// </summary>
        /// <param name="_data">財產資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> DeleteAssetAsync(AssetWithAccessoriesDTO _data)
        {
            try
            {
                // 檢查必要欄位
                if (_data.Asset == null)
                {
                    return (false, "財產資料不可為空");
                }

                if (string.IsNullOrEmpty(_data.Asset.AssetId.ToString()) || _data.Asset.AssetId == Guid.Empty)
                {
                    return (false, "財產ID不可為空");
                }

                // 開始交易
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // 查詢要刪除的財產
                    var asset = await _context.Set<Asset>()
                        .FirstOrDefaultAsync(a => a.AssetId == _data.Asset.AssetId);

                    if (asset == null)
                    {
                        return (false, "找不到要刪除的財產");
                    }

                    // 檢查是否已被刪除
                    if (asset.DeleteTime != null && asset.DeleteTime > 0)
                    {
                        return (false, "此財產已被刪除");
                    }

                    long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    // 標記財產為已刪除
                    asset.DeleteTime = currentTime;
                    asset.DeleteUserId = _data.Asset.DeleteUserId;
                    asset.IsDeleted = true;

                    _context.Update(asset);
                    await _context.SaveChangesAsync();
                    Console.WriteLine($"已標記財產為已刪除: {asset.AssetName}, ID: {asset.AssetId}");

                    // 標記相關附屬設備為已刪除
                    var accessoryEquipments = await _context.Set<AccessoryEquipment>()
                        .Where(ae => ae.AssetId == _data.Asset.AssetId && (ae.DeleteTime == 0 || ae.DeleteTime == null))
                        .ToListAsync();

                    foreach (var equipment in accessoryEquipments)
                    {
                        equipment.DeleteTime = currentTime;
                        equipment.DeleteUserId = _data.Asset.DeleteUserId;
                        equipment.UpdateTime = currentTime;
                        equipment.UpdateUserId = _data.Asset.DeleteUserId;
                        _context.Update(equipment);
                    }

                    if (accessoryEquipments.Any())
                    {
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已標記 {accessoryEquipments.Count} 個附屬設備為已刪除");
                    }

                    // 刪除保險單位關聯
                    var insuranceUnitRelations = await _context.Set<AssetInsuranceUnitMapping>()
                        .Where(aiu => aiu.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                    if (insuranceUnitRelations.Any())
                    {
                        _context.Set<AssetInsuranceUnitMapping>().RemoveRange(insuranceUnitRelations);
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已刪除 {insuranceUnitRelations.Count} 個保險單位關聯");
                    }

                    // 刪除攤提來源關聯
                    var amortizationSourceRelations = await _context.Set<AssetAmortizationSourceMapping>()
                        .Where(aas => aas.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                    if (amortizationSourceRelations.Any())
                    {
                        _context.Set<AssetAmortizationSourceMapping>().RemoveRange(amortizationSourceRelations);
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已刪除 {amortizationSourceRelations.Count} 個攤提來源關聯");
                    }

                    // 刪除財產來源關聯
                    var assetSourceRelations = await _context.Set<AssetAssetSourceMapping>()
                        .Where(aas => aas.AssetId == _data.Asset.AssetId)
                        .ToListAsync();

                    if (assetSourceRelations.Any())
                    {
                        _context.Set<AssetAssetSourceMapping>().RemoveRange(assetSourceRelations);
                        await _context.SaveChangesAsync();
                        Console.WriteLine($"已刪除 {assetSourceRelations.Count} 個財產來源關聯");
                    }

                    // 提交交易
                    await transaction.CommitAsync();
                    Console.WriteLine("交易成功提交");
                    return (true, _data.Asset.AssetNo);
                }
                catch (Exception ex)
                {
                    // 回滾交易
                    await transaction.RollbackAsync();
                    Console.WriteLine($"交易回滾: {ex.Message}");

                    // 記錄內部異常詳情
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                        Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                    }

                    return (false, $"刪除財產失敗: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"一般異常: {ex.Message}");

                // 記錄內部異常詳情
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }

                return (false, $"刪除財產失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得財產詳細資料
        /// </summary>
        /// <param name="_assetId">財產ID</param>
        /// <returns>財產詳細資料</returns>
        public async Task<AssetWithAccessoriesDTO> GetAssetDetailAsync(string _assetId)
        {
            try
            {
                if (string.IsNullOrEmpty(_assetId))
                {
                    Console.WriteLine("財產ID不可為空");
                    return null;
                }

                // 轉換ID字串為Guid
                if (!Guid.TryParse(_assetId, out Guid assetId))
                {
                    Console.WriteLine("財產ID格式錯誤");
                    return null;
                }

                // 查詢財產基本資料
                var asset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetId == assetId && (a.DeleteTime == null || a.DeleteTime == 0));

                if (asset == null)
                {
                    Console.WriteLine($"找不到ID為 {_assetId} 的財產");
                    return null;
                }

                Console.WriteLine($"找到財產: {asset.AssetName}, ID: {asset.AssetId}");

                // 查詢部門名稱
                var department = await _context.Set<Department>()
                    .Where(d => d.DepartmentId == asset.DepartmentId && (d.DeleteTime == 0 || d.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // 查詢組別名稱
                var division = await _context.Set<Division>()
                    .Where(d => d.DivisionId == asset.DivisionId && (d.DeleteTime == 0 || d.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // 查詢單位名稱
                var unit = await _context.Set<Unit>()
                    .Where(u => u.UnitId == asset.UnitId && (u.DeleteTime == 0 || u.DeleteTime == null))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // 設定部門和組別名稱
                if (department != null)
                {
                    asset.DepartmentName = department.Name;
                }
                if (division != null)
                {
                    asset.DivisionName = division.Name;
                }

                if (unit != null)
                {
                    asset.UnitName = unit.Name;
                }

                // 建立 DTO 實例
                var assetDto = new AssetWithAccessoriesDTO
                {
                    Asset = _mapper.Map<AssetDTO>(asset),
                    AccessoryEquipments = new List<AccessoryEquipmentDTO>(),
                    InsuranceUnits = new List<InsuranceUnitDTO>(),
                    AmortizationSources = new List<AmortizationSourceDTO>(),
                    AssetSources = new List<AssetSourceDTO>()
                };

                // 查詢附屬設備並轉換為DTO
                var accessoryEquipments = await _context.Set<AccessoryEquipment>()
                    .Where(ae => ae.AssetId == assetId && (ae.DeleteTime == null || ae.DeleteTime == 0))
                    .Include(ae => ae.Asset) // 包含 Asset 以便在 FromEntity 中獲取 AssetNo 和 AssetName
                    .AsNoTracking()
                    .ToListAsync();

                assetDto.AccessoryEquipments = _mapper.Map<List<AccessoryEquipmentDTO>>(accessoryEquipments);

                Console.WriteLine($"附屬設備數量: {assetDto.AccessoryEquipments.Count}");

                // 查詢保險單位關聯
                var insuranceUnitRelations = await _context.Set<AssetInsuranceUnitMapping>()
                    .Where(aiu => aiu.AssetId == assetId)
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var relation in insuranceUnitRelations)
                {
                    var insuranceUnit = await _context.Set<InsuranceUnit>()
                        .Where(iu => iu.InsuranceUnitId == relation.InsuranceUnitId && (iu.DeleteTime == null || iu.DeleteTime == 0))
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (insuranceUnit != null)
                    {
                        assetDto.InsuranceUnits.Add(_mapper.Map<InsuranceUnitDTO>(insuranceUnit));
                    }
                }

                Console.WriteLine($"保險單位數量: {assetDto.InsuranceUnits.Count}");

                // 查詢攤提來源關聯
                var amortizationSourceRelations = await _context.Set<AssetAmortizationSourceMapping>()
                    .Where(aas => aas.AssetId == assetId)
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var relation in amortizationSourceRelations)
                {
                    var amortizationSource = await _context.Set<AmortizationSource>()
                        .Where(ams => ams.AmortizationSourceId == relation.AmortizationSourceId && (ams.DeleteTime == null || ams.DeleteTime == 0))
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (amortizationSource != null)
                    {
                        assetDto.AmortizationSources.Add(_mapper.Map<AmortizationSourceDTO>(amortizationSource));
                    }
                }

                Console.WriteLine($"攤提來源數量: {assetDto.AmortizationSources.Count}");

                // 查詢財產來源關聯
                var assetSourceRelations = await _context.Set<AssetAssetSourceMapping>()
                    .Where(aas => aas.AssetId == assetId)
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var relation in assetSourceRelations)
                {
                    var assetSource = await _context.Set<AssetSource>()
                        .Where(ass => ass.AssetSourceId == relation.AssetSourceId && (ass.DeleteTime == null || ass.DeleteTime == 0))
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (assetSource != null)
                    {
                        assetDto.AssetSources.Add(_mapper.Map<AssetSourceDTO>(assetSource));
                    }
                }

                Console.WriteLine($"財產來源數量: {assetDto.AssetSources.Count}");

                Console.WriteLine($"財產詳細資料查詢完成: {asset.AssetName}");
                return assetDto;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查詢財產詳細資料時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部異常: {ex.InnerException.Message}");
                    Console.WriteLine($"堆疊跟踪: {ex.StackTrace}");
                }
                return null;
            }
        }

        /// <summary>
        /// 新增財產
        /// </summary>
        /// <param name="_data">財產資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> AddAssetAsync(AssetWithAccessoriesDTO _data)
        {
            // 開始交易
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 查詢財產科目
                var assetAccount = await _context.Set<AssetAccount>()
.FirstOrDefaultAsync(a => a.AssetAccountId == _data.Asset.AssetAccountId && (a.DeleteTime == 0 || a.DeleteTime == null));

                if (assetAccount == null)
                {
                    return (false, "找不到指定的財產科目");
                }

                // 查詢財產子目
                var assetSubAccount = await _context.Set<AssetSubAccount>()
.FirstOrDefaultAsync(a => a.AssetSubAccountId == _data.Asset.AssetSubAccountId && (a.DeleteTime == 0 || a.DeleteTime == null));

                if (assetSubAccount == null)
                {
                    return (false, "找不到指定的財產子目");
                }

                // 取得新的財產編號
                var (success, assetNo, message) = await GetNewAssetNoAsync(
                        assetAccount.AssetAccountNo,
                        assetSubAccount.AssetSubAccountNo,
                    _data.Asset.AssetCategoryId);

                if (!success)
                {
                    return (false, message);
                }

                // 設定財產編號
                _data.Asset.AssetNo = assetNo;

                // 檢查財產編號是否已存在
                var existingAsset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetNo == _data.Asset.AssetNo && (a.DeleteTime == 0 || a.DeleteTime == null));

                if (existingAsset != null)
                {
                    return (false, "財產編號已存在");
                }

                // 將 DTO 轉換為實體
                var newAsset = _mapper.Map<Asset>(_data.Asset);
                newAsset.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                newAsset.CreateUserId = _data.Asset.CreateUserId;

                // 新增財產
                await _context.Set<Asset>().AddAsync(newAsset);

                // 新增附屬設備
                if (_data.AccessoryEquipments != null && _data.AccessoryEquipments.Any())
                {
                    foreach (var accessoryDto in _data.AccessoryEquipments)
                    {
                        var accessory = _mapper.Map<AccessoryEquipment>(accessoryDto);
                        accessory.AssetId = newAsset.AssetId;
                        accessory.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        accessory.CreateUserId = _data.Asset.CreateUserId;
                        await _context.Set<AccessoryEquipment>().AddAsync(accessory);
                    }
                }

                // 新增保險單位關聯
                if (_data.InsuranceUnits != null && _data.InsuranceUnits.Any())
                {
                    foreach (var insuranceUnitDto in _data.InsuranceUnits)
                    {
                        var insuranceUnit = _mapper.Map<InsuranceUnit>(insuranceUnitDto);
                        insuranceUnit.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        insuranceUnit.CreateUserId = _data.Asset.CreateUserId;

                        // 檢查保險單位是否已存在
                        var existingInsuranceUnit = await _context.Set<InsuranceUnit>()
                            .FirstOrDefaultAsync(iu => iu.Name == insuranceUnit.Name && (iu.DeleteTime == 0 || iu.DeleteTime == null));

                        if (existingInsuranceUnit == null)
                        {
                            await _context.Set<InsuranceUnit>().AddAsync(insuranceUnit);
                            await _context.SaveChangesAsync(); // 需要先儲存以獲得 InsuranceUnitId

                            // 新增關聯
                            var mapping = new AssetInsuranceUnitMapping
                            {
                                AssetId = newAsset.AssetId,
                                InsuranceUnitId = insuranceUnit.InsuranceUnitId
                            };
                            await _context.Set<AssetInsuranceUnitMapping>().AddAsync(mapping);
                        }
                        else
                        {
                            // 使用現有的保險單位建立關聯
                            var mapping = new AssetInsuranceUnitMapping
                            {
                                AssetId = newAsset.AssetId,
                                InsuranceUnitId = existingInsuranceUnit.InsuranceUnitId
                            };
                            await _context.Set<AssetInsuranceUnitMapping>().AddAsync(mapping);
                        }
                    }
                }

                // 新增攤提來源關聯
                if (_data.AmortizationSources != null && _data.AmortizationSources.Any())
                {
                    foreach (var amortizationSourceDto in _data.AmortizationSources)
                    {
                        // 檢查攤提來源是否已存在
                        var existingAmortizationSource = await _context.Set<AmortizationSource>()
                            .FirstOrDefaultAsync(ams => ams.AmortizationSourceId == amortizationSourceDto.AmortizationSourceId &&
                                (ams.DeleteTime == 0 || ams.DeleteTime == null));

                        if (existingAmortizationSource == null)
                        {
                            var amortizationSource = _mapper.Map<AmortizationSource>(amortizationSourceDto);
                            amortizationSource.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            amortizationSource.CreateUserId = _data.Asset.CreateUserId;

                            await _context.Set<AmortizationSource>().AddAsync(amortizationSource);
                            await _context.SaveChangesAsync(); // 需要先儲存以獲得 AmortizationSourceId
                        }

                        // 新增關聯
                        var mapping = new AssetAmortizationSourceMapping
                        {
                            AssetId = newAsset.AssetId,
                            AmortizationSourceId = amortizationSourceDto.AmortizationSourceId
                        };
                        await _context.Set<AssetAmortizationSourceMapping>().AddAsync(mapping);
                    }
                }

                // 新增財產來源關聯
                if (_data.AssetSources != null && _data.AssetSources.Any())
                {
                    foreach (var assetSourceDto in _data.AssetSources)
                    {
                        var assetSource = _mapper.Map<AssetSource>(assetSourceDto);
                        assetSource.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        assetSource.CreateUserId = _data.Asset.CreateUserId;

                        // 檢查財產來源是否已存在
                        var existingAssetSource = await _context.Set<AssetSource>()
                            .FirstOrDefaultAsync(ams => ams.AssetSourceName == assetSource.AssetSourceName && (ams.DeleteTime == 0 || ams.DeleteTime == null));

                        if (existingAssetSource == null)
                        {
                            await _context.Set<AssetSource>().AddAsync(assetSource);
                            await _context.SaveChangesAsync(); // 需要先儲存以獲得 AssetSourceId

                            // 新增關聯
                            var mapping = new AssetAssetSourceMapping
                            {
                                AssetId = newAsset.AssetId,
                                AssetSourceId = assetSource.AssetSourceId
                            };
                            await _context.Set<AssetAssetSourceMapping>().AddAsync(mapping);
                        }
                        else
                        {
                            // 使用現有的財產來源建立關聯
                            var mapping = new AssetAssetSourceMapping
                            {
                                AssetId = newAsset.AssetId,
                                AssetSourceId = existingAssetSource.AssetSourceId
                            };
                            await _context.Set<AssetAssetSourceMapping>().AddAsync(mapping);
                        }
                    }
                }

                await _context.SaveChangesAsync();

                // 產生折舊紀錄（如果不是土地或未完工程）
                if (assetAccount != null && assetAccount.AssetAccountNo != "1" && assetAccount.AssetAccountNo != "9")
                {
                    var (depreciationSuccess, depreciationMessage) = await _depreciationService.GenerateDepreciationScheduleAsync(
                        newAsset.AssetId.ToString(), _data.Asset.CreateUserId);

                    Console.WriteLine(depreciationMessage);
                }

                // 提交交易
                await transaction.CommitAsync();
                return (true, assetNo);
            }
            catch (Exception ex)
            {
                // 發生錯誤時回滾交易
                await transaction.RollbackAsync();
                return (false, $"新增財產失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 驗證Excel檔案格式和內容
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <returns>驗證結果</returns>
        public async Task<BatchValidationResultDTO> ValidateExcelFileAsync(Stream fileStream)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage(fileStream))
                {
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null)
                    {
                        return new BatchValidationResultDTO
                        {
                            IsValid = false,
                            Errors = new List<BatchValidationErrorDTO>
                            {
                                new BatchValidationErrorDTO
                                {
                                    RowIndex = 0,
                                    ColumnName = "檔案",
                                    ErrorMessage = "Excel檔案中沒有工作表",
                                    CellValue = ""
                                }
                            }
                        };
                    }

                    var columnMappings = ExcelHelper.GetAssetColumnMappings();
                    var validationResult = ExcelHelper.ValidateExcelFormat(worksheet, columnMappings);

                    // 額外驗證：檢查財產編號重複和業務邏輯
                    if (validationResult.IsValid)
                    {
                        await ValidateBusinessLogicAsync(worksheet, columnMappings, validationResult);
                    }

                    // 如果驗證成功，讀取並回傳完整的Excel資料
                    if (validationResult.IsValid)
                    {
                        ReadExcelDataToResult(worksheet, columnMappings, validationResult);
                    }

                    return validationResult;
                }
            }
            catch (Exception ex)
            {
                return new BatchValidationResultDTO
                {
                    IsValid = false,
                    Errors = new List<BatchValidationErrorDTO>
                    {
                        new BatchValidationErrorDTO
                        {
                            RowIndex = 0,
                            ColumnName = "檔案",
                            ErrorMessage = $"檔案驗證發生錯誤：{ex.Message}",
                            CellValue = ""
                        }
                    }
                };
            }
        }

        /// <summary>
        /// 批次匯入財產資料
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <param name="userId">操作使用者ID</param>
        /// <returns>匯入結果</returns>
        public async Task<BatchImportResultDTO> BatchImportAssetsAsync(Stream fileStream, string userId)
        {
            var result = new BatchImportResultDTO();

            try
            {
                // 先驗證檔案格式
                var validationResult = await ValidateExcelFileAsync(fileStream);
                if (!validationResult.IsValid)
                {
                    result.Success = false;
                    result.Message = "檔案驗證失敗，請修正錯誤後重新上傳";
                    result.Errors = validationResult.Errors.Select(e => new BatchImportErrorDTO
                    {
                        RowIndex = e.RowIndex,
                        ErrorMessage = e.ErrorMessage
                    }).ToList();
                    return result;
                }

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                fileStream.Position = 0; // 重置檔案流位置

                using (var package = new ExcelPackage(fileStream))
                {
                    var worksheet = package.Workbook.Worksheets.First();
                    var columnMappings = ExcelHelper.GetAssetColumnMappings();

                    result.TotalRows = validationResult.TotalRows;

                    // 開始逐行處理資料
                    for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                    {
                        try
                        {
                            var assetDto = ExcelHelper.ConvertExcelRowToAssetDTO(worksheet, row, columnMappings);

                            // 設定建立資訊
                            assetDto.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            assetDto.CreateUserId = userId;

                            // 建立包含附屬設備的DTO
                            var assetWithAccessoriesDto = new AssetWithAccessoriesDTO
                            {
                                Asset = assetDto,
                                AccessoryEquipments = new List<AccessoryEquipmentDTO>(),
                                InsuranceUnits = new List<InsuranceUnitDTO>(),
                                AmortizationSources = new List<AmortizationSourceDTO>(),
                                AssetSources = new List<AssetSourceDTO>()
                            };

                            // 新增財產
                            var (success, message) = await AddAssetAsync(assetWithAccessoriesDto);

                            if (success)
                            {
                                result.SuccessRows++;
                                result.SuccessAssetNos.Add(message);

                                // 成功匯入財產資訊
                                result.SuccessAssets.Add(new AssetImportSuccessDTO
                                {
                                    AssetNo = message,
                                    AssetName = assetDto.AssetName
                                });
                            }
                            else
                            {
                                result.FailedRows++;
                                result.Errors.Add(new BatchImportErrorDTO
                                {
                                    RowIndex = row,
                                    AssetNo = assetDto.AssetNo,
                                    AssetName = assetDto.AssetName,
                                    ErrorMessage = message
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            result.FailedRows++;
                            result.Errors.Add(new BatchImportErrorDTO
                            {
                                RowIndex = row,
                                AssetNo = "",
                                AssetName = "",
                                ErrorMessage = $"處理第{row}行時發生錯誤：{ex.Message}"
                            });
                        }
                    }

                    result.Success = result.FailedRows == 0;
                    result.Message = result.Success
                        ? $"批次匯入完成，成功匯入{result.SuccessRows}筆資料"
                        : $"批次匯入完成，成功{result.SuccessRows}筆，失敗{result.FailedRows}筆";

                    return result;
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"批次匯入發生錯誤：{ex.Message}";
                result.Errors.Add(new BatchImportErrorDTO
                {
                    RowIndex = 0,
                    ErrorMessage = ex.Message
                });
                return result;
            }
        }

        /// <summary>
        /// 下載批次轉檔範本
        /// </summary>
        /// <returns>Excel範本檔案</returns>
        public async Task<(byte[] fileBytes, string fileName)> DownloadBatchTemplateAsync()
        {
            try
            {
                var fileBytes = ExcelHelper.GenerateBatchTemplate();
                var fileName = $"PmsBatch_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return (fileBytes, fileName);
            }
            catch (Exception ex)
            {
                throw new Exception($"產生範本檔案時發生錯誤：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 讀取Excel資料到驗證結果中
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="columnMappings">欄位對應</param>
        /// <param name="validationResult">驗證結果</param>
        private void ReadExcelDataToResult(ExcelWorksheet worksheet, List<ExcelColumnMappingDTO> columnMappings, BatchValidationResultDTO validationResult)
        {
            try
            {
                // 設定欄位標題（依照Excel檔案順序）
                validationResult.ColumnHeaders = columnMappings.OrderBy(c => c.ColumnIndex).Select(c => c.ColumnName).ToList();

                // 讀取所有資料行
                for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                {
                    var rowData = new ExcelRowDataDTO
                    {
                        RowIndex = row
                    };

                    // 依照欄位順序讀取每個儲存格的值
                    foreach (var mapping in columnMappings.OrderBy(c => c.ColumnIndex))
                    {
                        var cellValue = worksheet.Cells[row, mapping.ColumnIndex].Value?.ToString()?.Trim() ?? "";
                        rowData.ColumnValues[mapping.ColumnName] = cellValue;
                    }

                    validationResult.ExcelData.Add(rowData);
                }

                Console.WriteLine($"成功讀取Excel資料，共{validationResult.ExcelData.Count}行資料");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"讀取Excel資料時發生錯誤: {ex.Message}");
                // 如果讀取資料失敗，清空已讀取的資料
                validationResult.ColumnHeaders.Clear();
                validationResult.ExcelData.Clear();
            }
        }

        /// <summary>
        /// 驗證業務邏輯
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="columnMappings">欄位對應</param>
        /// <param name="validationResult">驗證結果</param>
        private async Task ValidateBusinessLogicAsync(ExcelWorksheet worksheet, List<ExcelColumnMappingDTO> columnMappings, BatchValidationResultDTO validationResult)
        {
            var assetNos = new HashSet<string>();
            var errors = new List<BatchValidationErrorDTO>();

            // 查詢所有需要驗證的資料
            var allUnits = await _context.Set<Unit>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(u => u.UnitId, u => u);

            var allDepartments = await _context.Set<Department>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(d => d.DepartmentId, d => d);

            var allDivisions = await _context.Set<Division>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(d => d.DivisionId, d => d);

            var allPmsUserMappings = await _context.Set<PmsUserRoleMapping>()
                .Where(u => u.IsDeleted == false)
                .Select(m => m.UserId)
                .Distinct()
                .ToHashSetAsync();

            var allAssetStatuses = await _context.Set<AssetStatus>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(s => s.AssetStatusId, s => s);

            var allAssetAccounts = await _context.Set<AssetAccount>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(a => a.AssetAccountId, a => a);

            var allAssetSubAccounts = await _context.Set<AssetSubAccount>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(a => a.AssetSubAccountId, a => a);

            var allEquipmentTypes = await _context.Set<EquipmentType>()
                .Where(u => u.IsDeleted == false)
                .ToDictionaryAsync(e => e.EquipmentTypeId, e => e);

            for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
            {
                var assetNoCell = worksheet.Cells[row, 1].Value?.ToString()?.Trim();

                // 檢查Excel內部財產編號重複
                if (!string.IsNullOrEmpty(assetNoCell))
                {
                    if (assetNos.Contains(assetNoCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產編號",
                            ErrorMessage = "財產編號在檔案中重複",
                            CellValue = assetNoCell
                        });
                    }
                    else
                    {
                        assetNos.Add(assetNoCell);

                        // 檢查資料庫中是否已存在
                        var existingAsset = await _context.Set<Asset>()
                            .FirstOrDefaultAsync(a => a.AssetNo == assetNoCell && (a.DeleteTime == 0 || a.DeleteTime == null));

                        if (existingAsset != null)
                        {
                            errors.Add(new BatchValidationErrorDTO
                            {
                                RowIndex = row,
                                ColumnName = "財產編號",
                                ErrorMessage = "財產編號已存在於系統中",
                                CellValue = assetNoCell
                            });
                        }
                    }
                }

                // 驗證單位編號是否存在 (第7欄)
                var unitIdCell = worksheet.Cells[row, 7].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(unitIdCell) && Guid.TryParse(unitIdCell, out var unitId))
                {
                    if (!allUnits.ContainsKey(unitId))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "單位編號",
                            ErrorMessage = "單位編號不存在於系統中",
                            CellValue = unitIdCell
                        });
                    }
                }

                // 驗證部門是否存在 (第13欄)
                var departmentIdCell = worksheet.Cells[row, 13].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(departmentIdCell))
                {
                    if (!allDepartments.ContainsKey(departmentIdCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "部門",
                            ErrorMessage = "部門編號不存在於系統中",
                            CellValue = departmentIdCell
                        });
                    }
                }

                // 驗證股別是否存在 (第14欄)
                var divisionIdCell = worksheet.Cells[row, 14].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(divisionIdCell))
                {
                    if (!allDivisions.ContainsKey(divisionIdCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "股別",
                            ErrorMessage = "股別編號不存在於系統中",
                            CellValue = divisionIdCell
                        });
                    }
                }

                // 驗證保管人是否存在 (第15欄) - 使用 PMS 使用者角色系統驗證
                var custodianIdCell = worksheet.Cells[row, 15].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(custodianIdCell))
                {
                    if (!allPmsUserMappings.Contains(custodianIdCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "保管人",
                            ErrorMessage = "保管人編號不存在於 PMS 使用者角色系統中",
                            CellValue = custodianIdCell
                        });
                    }
                }

                // 驗證使用人是否存在 (第16欄) - 使用 PMS 使用者角色系統驗證
                var userIdCell = worksheet.Cells[row, 16].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(userIdCell))
                {
                    if (!allPmsUserMappings.Contains(userIdCell))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "使用人",
                            ErrorMessage = "使用人編號不存在於 PMS 使用者角色系統中",
                            CellValue = userIdCell
                        });
                    }
                }

                // 驗證財產狀態是否存在 (第17欄)
                var assetStatusCell = worksheet.Cells[row, 17].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(assetStatusCell))
                {
                    if (Guid.TryParse(assetStatusCell, out var assetStatusId))
                    {
                        if (!allAssetStatuses.ContainsKey(assetStatusId))
                        {
                            errors.Add(new BatchValidationErrorDTO
                            {
                                RowIndex = row,
                                ColumnName = "財產狀態",
                                ErrorMessage = "財產狀態編號不存在於系統中",
                                CellValue = assetStatusCell
                            });
                        }
                    }
                    else
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產狀態",
                            ErrorMessage = "財產狀態編號不存在於系統中",
                            CellValue = assetStatusCell
                        });
                    }
                }

                // 驗證財產科目是否存在 (第38欄)
                var assetAccountIdCell = worksheet.Cells[row, 38].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(assetAccountIdCell) && Guid.TryParse(assetAccountIdCell, out var assetAccountId))
                {
                    if (!allAssetAccounts.ContainsKey(assetAccountId))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產科目",
                            ErrorMessage = "財產科目ID不存在於系統中",
                            CellValue = assetAccountIdCell
                        });
                    }
                }

                // 驗證財產子目是否存在 (第39欄)
                var assetSubAccountIdCell = worksheet.Cells[row, 39].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(assetSubAccountIdCell) && Guid.TryParse(assetSubAccountIdCell, out var assetSubAccountId))
                {
                    if (!allAssetSubAccounts.ContainsKey(assetSubAccountId))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "財產子目",
                            ErrorMessage = "財產子目ID不存在於系統中",
                            CellValue = assetSubAccountIdCell
                        });
                    }
                }

                // 驗證設備類型是否存在 (第48欄)
                var equipmentTypeIdCell = worksheet.Cells[row, 48].Value?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(equipmentTypeIdCell) && Guid.TryParse(equipmentTypeIdCell, out var equipmentTypeId))
                {
                    if (!allEquipmentTypes.ContainsKey(equipmentTypeId))
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = row,
                            ColumnName = "設備類型",
                            ErrorMessage = "設備類型ID不存在於系統中",
                            CellValue = equipmentTypeIdCell
                        });
                    }
                }
            }

            if (errors.Any())
            {
                validationResult.IsValid = false;
                validationResult.Errors.AddRange(errors);
                validationResult.ErrorRows = validationResult.Errors.Select(e => e.RowIndex).Distinct().Count();
                validationResult.ValidRows = validationResult.TotalRows - validationResult.ErrorRows;
            }
        }
    }
}
