"use client";

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Modal, Form, Input, Button, Space, Row, Col, TreeSelect, Popconfirm, message, Card, Badge, Alert } from 'antd';
import type { InputRef } from 'antd';
import { PlusOutlined, EditOutlined, SaveOutlined, ApartmentOutlined, NodeIndexOutlined, ExpandOutlined, CompressOutlined, CaretRightOutlined, CaretDownOutlined, DeleteOutlined } from '@ant-design/icons';

// 通用分類介面
export interface GenericCategory {
  id: string;
  name: string;
  description?: string;
  parentID?: string | null;
  sortCode?: number;
  children?: GenericCategory[];
}

// 通用分類服務介面
export interface CategoryService<T extends GenericCategory> {
  add: (category: Partial<T>) => Promise<{ success: boolean; message?: string }>;
  edit: (category: Partial<T>) => Promise<{ success: boolean; message?: string }>;
  delete: (id: string) => Promise<{ success: boolean; message?: string }>;
  buildTree: (categories: T[]) => T[];
}

// 組件配置介面
export interface CategoryConfig {
  title: string;
  icon: React.ReactNode;
  emptyMessage: string;
  emptyDescription: string;
  entityName: string; // 如 "客戶分類"、"供應商分類"
}

interface GenericCategoryManagementProps<T extends GenericCategory> {
  visible: boolean;
  onClose: () => void;
  categories: T[];
  onDataChange: () => void;
  config: CategoryConfig;
  service: CategoryService<T>;
  getIdField: (category: T) => string;
  mapToGeneric: (category: T) => GenericCategory;
  mapFromGeneric: (generic: GenericCategory, original?: T) => Partial<T>;
}

function GenericCategoryManagement<T extends GenericCategory>({
  visible,
  onClose,
  categories,
  onDataChange,
  config,
  service,
  getIdField,
  mapToGeneric,
  mapFromGeneric
}: GenericCategoryManagementProps<T>) {
  // 狀態管理
  const [selectedCategory, setSelectedCategory] = useState<T | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [operationMode, setOperationMode] = useState<'add' | 'edit' | 'addChild'>('add');
  const [parentCategoryForChild, setParentCategoryForChild] = useState<T | null>(null);

  // 折疊功能狀態管理
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isAllExpanded, setIsAllExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 表單輸入框引用
  const nameInputRef = useRef<InputRef>(null);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 建立分類樹狀資料
  const categoryTreeData = useMemo(() => {
    if (!Array.isArray(categories)) {
      console.warn('⚠️ GenericCategoryManagement: categories 不是陣列');
      return [];
    }

    const tree = service.buildTree(categories);
    
    const convertToTreeData = (cats: T[]): any[] => {
      return cats.map(cat => {
        const generic = mapToGeneric(cat);
        return {
          title: generic.name,
          value: generic.id,
          key: generic.id,
          children: generic.children && generic.children.length > 0 ? convertToTreeData(cat.children || []) : undefined
        };
      });
    };

    return convertToTreeData(tree);
  }, [categories, service, mapToGeneric]);

  // 排序後的分類顯示資料
  const sortedCategoriesForDisplay = useMemo(() => {
    if (!Array.isArray(categories)) {
      return [];
    }

    const sortedCategories = [...categories].sort((a, b) => {
      const aGeneric = mapToGeneric(a);
      const bGeneric = mapToGeneric(b);
      
      // 先按 sortCode 排序，再按名稱排序
      if ((aGeneric.sortCode || 0) !== (bGeneric.sortCode || 0)) {
        return (aGeneric.sortCode || 0) - (bGeneric.sortCode || 0);
      }
      return aGeneric.name.localeCompare(bGeneric.name);
    });

    return sortedCategories;
  }, [categories, mapToGeneric]);

  // 重置表單
  const resetForm = () => {
    form.resetFields();
    setSelectedCategory(null);
    setOperationMode('add');
    setParentCategoryForChild(null);
  };

  // 處理新增分類
  const handleAdd = () => {
    resetForm();
    setOperationMode('add');
    
    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理編輯
  const handleEdit = (category: T) => {
    setSelectedCategory(category);
    setParentCategoryForChild(null);
    setOperationMode('edit');
    
    const generic = mapToGeneric(category);
    form.setFieldsValue({
      name: generic.name,
      description: generic.description,
      parentID: generic.parentID,
      sortCode: generic.sortCode
    });

    setTimeout(() => {
      nameInputRef.current?.focus();
      nameInputRef.current?.select();
    }, 100);
  };

  // 處理快速建立子分類
  const handleAddChild = (parentCategory: T) => {
    setSelectedCategory(null);
    setParentCategoryForChild(parentCategory);
    setOperationMode('addChild');
    form.resetFields();
    
    const parentGeneric = mapToGeneric(parentCategory);
    form.setFieldsValue({
      parentID: parentGeneric.id
    });

    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理刪除
  const handleDelete = async (category: T) => {
    try {
      setLoading(true);
      const categoryId = getIdField(category);
      const response = await service.delete(categoryId);
      
      if (response.success) {
        message.success(`${config.entityName}刪除成功`);
        onDataChange();
      } else {
        message.error(response.message || '刪除失敗');
      }
    } catch (error) {
      console.error(`❌ 刪除${config.entityName}時發生錯誤:`, error);
      message.error('刪除失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      let response;

      if (operationMode === 'edit' && selectedCategory) {
        // 編輯模式
        const updateData = mapFromGeneric({
          id: getIdField(selectedCategory),
          name: values.name,
          description: values.description || '',
          parentID: values.parentID || null,
          sortCode: values.sortCode || 0,
        }, selectedCategory);
        
        response = await service.edit(updateData);
      } else {
        // 新增模式 (包括新增子分類)
        const createData = mapFromGeneric({
          id: '', // 新增時 ID 由後端生成
          name: values.name,
          description: values.description || '',
          parentID: values.parentID || null,
          sortCode: values.sortCode || 0,
        });
        
        response = await service.add(createData);
      }

      if (response.success) {
        const action = operationMode === 'edit' ? '更新' : '新增';
        message.success(`${config.entityName}${action}成功`);
        onDataChange();
        resetForm();
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error(`❌ 提交${config.entityName}時發生錯誤:`, error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 切換分類展開/折疊
  const toggleCategoryExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // 全部展開/折疊
  const toggleAllExpansion = () => {
    if (isAllExpanded) {
      setExpandedCategories(new Set());
    } else {
      const allIds = new Set(categories.map(cat => getIdField(cat)));
      setExpandedCategories(allIds);
    }
    setIsAllExpanded(!isAllExpanded);
  };

  // 初始化展開狀態（預設展開第一層分類）
  useEffect(() => {
    if (visible && categories && Array.isArray(categories) && categories.length > 0) {
      try {
        const topLevelCategories = categories.filter(cat => {
          const generic = mapToGeneric(cat);
          return !generic.parentID;
        });
        const topLevelIds = new Set(topLevelCategories.map(cat => getIdField(cat)));
        setExpandedCategories(topLevelIds);
        setIsAllExpanded(false);
      } catch (error) {
        console.error('❌ GenericCategoryManagement: 初始化展開狀態時發生錯誤:', error);
        setExpandedCategories(new Set());
        setIsAllExpanded(false);
      }
    }
  }, [visible, categories, mapToGeneric, getIdField]);

  // 當互動視窗打開時，重置表單並設置焦點
  useEffect(() => {
    if (visible) {
      resetForm();
      setTimeout(() => {
        nameInputRef.current?.focus();
      }, 100);
    }
  }, [visible]);

  // 渲染分類項目
  const renderCategoryItem = (category: T, level: number = 0): React.ReactNode => {
    const categoryId = getIdField(category);
    const generic = mapToGeneric(category);
    const hasChildren = generic.children && generic.children.length > 0;
    const isExpanded = expandedCategories.has(categoryId);

    const indentStyle = {
      marginLeft: `${level * 20}px`,
      borderLeft: level > 0 ? '2px solid #f0f0f0' : 'none',
      paddingLeft: level > 0 ? '12px' : '0',
    };

    return (
      <div key={categoryId} style={{ marginBottom: '8px' }}>
        <div
          style={{
            ...indentStyle,
            padding: '8px 12px',
            backgroundColor: level === 0 ? '#fafafa' : '#ffffff',
            border: '1px solid #e8e8e8',
            borderRadius: '6px',
            transition: 'all 0.2s ease',
          }}
        >
          <Row justify="space-between" align="middle">
            <Col flex="auto">
              <Space>
                {hasChildren && (
                  <Button
                    type="text"
                    size="small"
                    icon={isExpanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
                    onClick={() => toggleCategoryExpansion(categoryId)}
                    style={{ padding: '0 4px' }}
                  />
                )}
                <span style={{ fontWeight: level === 0 ? 'bold' : 'normal' }}>
                  {generic.name}
                </span>
                {generic.description && (
                  <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
                    ({generic.description})
                  </span>
                )}
              </Space>
            </Col>
            
            <Col>
              <Space size="small">
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddChild(category)}
                  title="新增子分類"
                />
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(category)}
                  title="編輯分類"
                />
                <Popconfirm
                  title={`確定要刪除此${config.entityName}嗎？`}
                  description="刪除後無法復原，請確認。"
                  onConfirm={() => handleDelete(category)}
                  okText="確定"
                  cancelText="取消"
                >
                  <Button
                    type="link"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                    title="刪除分類"
                  />
                </Popconfirm>
              </Space>
            </Col>
          </Row>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {category.children!.map(child => renderCategoryItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          {config.icon}
          <span>{config.title}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={isMobile ? '95%' : 1000}
      style={{ top: 20 }}
    >
      <Row gutter={16} style={{ height: '70vh' }}>
        {/* 左側：分類列表 */}
        <Col span={isMobile ? 24 : 14}>
          <Card
            title={
              <Space>
                <ApartmentOutlined />
                <span>分類結構</span>
                <Badge count={categories.length} style={{ backgroundColor: '#52c41a' }} />
              </Space>
            }
            extra={
              <Button
                type="text"
                size="small"
                icon={isAllExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                onClick={toggleAllExpansion}
              >
                {isAllExpanded ? '全部折疊' : '全部展開'}
              </Button>
            }
            styles={{ body: { padding: '12px', maxHeight: '60vh', overflowY: 'auto' } }}
          >
            {categories.length === 0 ? (
              <Alert
                message={config.emptyMessage}
                description={config.emptyDescription}
                type="info"
                showIcon
              />
            ) : (
              <div>
                {service.buildTree(categories).map(category => renderCategoryItem(category))}
              </div>
            )}
          </Card>
        </Col>

        {/* 右側：操作表單 */}
        <Col span={isMobile ? 24 : 10}>
          <Card
            title={
              <Space>
                <NodeIndexOutlined />
                <span>
                  {operationMode === 'edit' ? '編輯分類' : 
                   operationMode === 'addChild' ? '新增子分類' : '新增分類'}
                </span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                size="small"
              >
                新增分類
              </Button>
            }
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
            >
              <Form.Item
                label="分類名稱"
                name="name"
                rules={[{ required: true, message: '請輸入分類名稱' }]}
              >
                <Input
                  ref={nameInputRef}
                  placeholder="請輸入分類名稱"
                  autoFocus={true}
                />
              </Form.Item>
              
              <Form.Item
                label="上級分類"
                name="parentID"
                tooltip="選擇上級分類，留空則為頂級分類"
              >
                <TreeSelect
                  placeholder="請選擇上級分類（可留空）"
                  allowClear
                  treeData={categoryTreeData}
                  treeDefaultExpandAll
                  showSearch
                  treeNodeFilterProp="title"
                  treeNodeLabelProp="title"
                  disabled={operationMode === 'addChild'}
                />
              </Form.Item>
              
              <Form.Item
                label="描述"
                name="description"
              >
                <Input.TextArea rows={3} placeholder="請輸入分類描述" />
              </Form.Item>
              
              <Form.Item
                label="排序碼"
                name="sortCode"
              >
                <Input type="number" placeholder="請輸入排序碼" />
              </Form.Item>
              
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={operationMode === 'add' ? <PlusOutlined /> : <SaveOutlined />}
                  style={{ 
                    marginTop: 16,
                    width: '100%'
                  }}
                >
                  {operationMode === 'edit' ? '更新分類' : 
                   operationMode === 'addChild' ? '新增子分類' : '新增分類'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </Modal>
  );
}

export default GenericCategoryManagement;
