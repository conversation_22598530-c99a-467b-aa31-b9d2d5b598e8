import { Modal, Button } from 'antd';
import { useEffect, useState, useRef } from 'react';

interface DeleteWithCountdownProps {
    onDelete: () => void;
    onCancel: () => void;
}

const DeleteWithCountdown: React.FC<DeleteWithCountdownProps> = ({ onDelete, onCancel }) => {
    const [countdown, setCountdown] = useState(5);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const hasDeletedRef = useRef(false); // <== 新增這行

    const executeDelete = () => {
        if (!hasDeletedRef.current) {
            hasDeletedRef.current = true;
            onDelete();
        }
    };

    useEffect(() => {
        timerRef.current = setInterval(() => {
            setCountdown(prev => {
                if (prev > 1) {
                    return prev - 1;
                } else {
                    clearInterval(timerRef.current!);
                    executeDelete(); // 使用旗標避免重複
                    return 0;
                }
            });
        }, 1000);

        return () => clearInterval(timerRef.current!);
    }, []);

    return (
        <Modal
            open={true}
            title="即將刪除"
            onCancel={onCancel}
            footer={[
                <Button key="cancel" onClick={onCancel}>取消</Button>,
                <Button key="confirm" type="primary" danger onClick={() => {
                    clearInterval(timerRef.current!);
                    executeDelete(); // 使用旗標避免重複
                }}>
                    立即刪除
                </Button>,
            ]}
        >
            <p>將於 <strong>{countdown}</strong> 秒後自動刪除資料。</p>
        </Modal>
    );
};


export default DeleteWithCountdown;
