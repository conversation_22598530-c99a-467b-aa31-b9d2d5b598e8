export type ImageFormat = "image/jpeg" | "image/png" | "image/gif";

export const siteConfig = {
    name: "FastERP",
    logo: {
        text: "FastERP",
        image: "/logo.png", // Logo 圖片路徑
    },
    description: "企業資源管理系統",
    copyright: "財團法人農漁會南區資訊中心",
    upload: {
        image: {
            maxSize: 5 * 1024 * 1024, // 5MB in bytes
            acceptedFormats: ["image/jpeg", "image/png", "image/gif"] as ImageFormat[],
            acceptedFormatsList: "JPG、PNG、GIF",
        },
    },
    email: {
        domains: [
            "mail.fast.org.tw",
            "gmail.com",
            "yahoo.com.tw",
            "hotmail.com",
            "outlook.com"
        ]
    },
    auth: {
        // 閒置超時設定（單位：毫秒）
        idleTimeout: 20 * 60 * 1000, // 20分鐘
        // 顯示閒置警告對話框的時間點（單位：毫秒）- 在超時前5分鐘
        idleWarningTime: 5 * 60 * 1000,
        // 閒置警告對話框倒計時（單位：秒）
        idleCountdownDuration: 60,
    },
    googleMaps: {
        apiKey: "AIzaSyBTj4GvHEHLSmC-WlnnIqVMn_7uJFCSTe4",
        defaultZoom: 16,
    }
} as const; 