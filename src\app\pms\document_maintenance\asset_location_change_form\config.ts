// 財產位置變動單配置

// 預設值
export const DEFAULT_VALUES = {
    approvalStatus: "PENDING",
    executionStatus: "PENDING",
    transferReason: "",
    notes: "",
    pageSize: 10,
    current: 1
};

// 表格設定
export const TABLE_CONFIG = {
    size: "middle" as const,
    bordered: true,
    scroll: { x: 1200 },
    pagination: {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number, range: [number, number]) =>
            `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
        pageSizeOptions: ["10", "20", "50", "100"],
        defaultPageSize: 10
    }
};

// 表單佈局設定
export const FORM_LAYOUT = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
};

export const MODAL_FORM_LAYOUT = {
    labelCol: { span: 7 },
    wrapperCol: { span: 17 }
};

// 搜尋表單佈局
export const SEARCH_FORM_LAYOUT = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 }
};

// 模態框設定
export const MODAL_CONFIG = {
    width: 1200,
    destroyOnHidden: true,
    maskClosable: false
};

// 表單規則
export const FORM_RULES = {
    transferDate: [
        { required: true, message: "請選擇變動日期" }
    ],
    applicantId: [
        { required: true, message: "請選擇申請人" }
    ],
    applicantDepartmentId: [
        { required: true, message: "請選擇申請部門" }
    ],
    transferReason: [
        { required: true, message: "請輸入變動原因" },
        { max: 500, message: "變動原因不得超過 500 字" }
    ],
    assetId: [
        { required: true, message: "請選擇財產" }
    ],
    newLocationId: [
        { required: true, message: "請選擇新存放地點" }
    ],
    approvalComments: [
        { max: 500, message: "審核意見不得超過 500 字" }
    ]
};

// 審核狀態選項
export const APPROVAL_STATUS_OPTIONS = [
    { label: "全部", value: "" },
    { label: "待審核", value: "PENDING" },
    { label: "已審核", value: "APPROVED" },
    { label: "已駁回", value: "REJECTED" }
];

// 執行狀態選項
export const EXECUTION_STATUS_OPTIONS = [
    { label: "全部", value: "" },
    { label: "待執行", value: "PENDING" },
    { label: "已執行", value: "COMPLETED" },
    { label: "已取消", value: "CANCELLED" }
];

// 變動項目選項
export const CHANGE_ITEMS_OPTIONS = [
    { label: "存放地點", value: "LOCATION" },
    { label: "保管人", value: "CUSTODIAN" },
    { label: "使用人", value: "USER" },
    { label: "部門", value: "DEPARTMENT" },
    { label: "股別", value: "DIVISION" }
];

// 狀態顏色映射
export const STATUS_COLORS = {
    // 審核狀態
    PENDING: "orange",
    APPROVED: "green",
    REJECTED: "red",
    // 執行狀態
    COMPLETED: "green",
    CANCELLED: "red"
};

// 快速搜尋選項
export const QUICK_SEARCH_OPTIONS = [
    { label: "今日申請", value: "today" },
    { label: "本週申請", value: "week" },
    { label: "本月申請", value: "month" },
    { label: "待我審核", value: "pending_approval" },
    { label: "待執行", value: "pending_execution" }
];

// 批次操作選項
export const BATCH_ACTIONS = {
    APPROVE: "approve",
    REJECT: "reject",
    EXECUTE: "execute",
    CANCEL: "cancel",
    DELETE: "delete"
};

// 批次操作配置
export const BATCH_ACTION_CONFIG = {
    [BATCH_ACTIONS.APPROVE]: {
        label: "批次審核",
        icon: "CheckOutlined",
        type: "primary" as const,
        danger: false
    },
    [BATCH_ACTIONS.REJECT]: {
        label: "批次駁回",
        icon: "CloseOutlined",
        type: "default" as const,
        danger: true
    },
    [BATCH_ACTIONS.EXECUTE]: {
        label: "批次執行",
        icon: "PlayCircleOutlined",
        type: "primary" as const,
        danger: false
    },
    [BATCH_ACTIONS.CANCEL]: {
        label: "批次取消",
        icon: "StopOutlined",
        type: "default" as const,
        danger: true
    },
    [BATCH_ACTIONS.DELETE]: {
        label: "批次刪除",
        icon: "DeleteOutlined",
        type: "default" as const,
        danger: true
    }
};

// 匯出配置
export const EXPORT_CONFIG = {
    filename: "財產位置變動單",
    sheetName: "位置變動單列表",
    columns: [
        { header: "變動單號", key: "transferNo", width: 20 },
        { header: "變動日期", key: "transferDate", width: 15 },
        { header: "申請人", key: "applicantName", width: 15 },
        { header: "申請部門", key: "applicantDepartmentName", width: 20 },
        { header: "變動原因", key: "transferReason", width: 30 },
        { header: "審核狀態", key: "approvalStatus", width: 15 },
        { header: "執行狀態", key: "executionStatus", width: 15 },
        { header: "審核人", key: "approverName", width: 15 },
        { header: "執行人", key: "executorName", width: 15 },
        { header: "備註", key: "notes", width: 30 }
    ]
};

// 權限設定
export const PERMISSIONS = {
    CREATE: "asset_location_transfer:create",
    READ: "asset_location_transfer:read",
    UPDATE: "asset_location_transfer:update",
    DELETE: "asset_location_transfer:delete",
    APPROVE: "asset_location_transfer:approve",
    EXECUTE: "asset_location_transfer:execute",
    EXPORT: "asset_location_transfer:export"
};

// 操作日誌類型
export const OPERATION_TYPES = {
    CREATE: "CREATE",
    UPDATE: "UPDATE",
    DELETE: "DELETE",
    APPROVE: "APPROVE",
    REJECT: "REJECT",
    EXECUTE: "EXECUTE",
    CANCEL: "CANCEL"
};

// 系統設定
export const SYSTEM_CONFIG = {
    // 自動產生單號格式
    transferNoFormat: "LT{YYYYMMDD}{NNNN}",
    // 預設審核期限（天）
    defaultApprovalDays: 7,
    // 預設執行期限（天）
    defaultExecutionDays: 30,
    // 允許的檔案類型
    allowedFileTypes: [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"],
    // 最大檔案大小（MB）
    maxFileSize: 10,
    // 批次操作最大數量
    maxBatchSize: 100
};

// 訊息提示
export const MESSAGES = {
    SUCCESS: {
        CREATE: "新增財產位置變動單成功",
        UPDATE: "更新財產位置變動單成功",
        DELETE: "刪除財產位置變動單成功",
        APPROVE: "審核完成",
        EXECUTE: "執行完成",
        EXPORT: "匯出成功"
    },
    ERROR: {
        CREATE: "新增財產位置變動單失敗",
        UPDATE: "更新財產位置變動單失敗",
        DELETE: "刪除財產位置變動單失敗",
        APPROVE: "審核失敗",
        EXECUTE: "執行失敗",
        EXPORT: "匯出失敗",
        NETWORK: "網路連線異常",
        PERMISSION: "權限不足"
    },
    CONFIRM: {
        DELETE: "確定要刪除此財產位置變動單嗎？",
        APPROVE: "確定要審核此財產位置變動單嗎？",
        REJECT: "確定要駁回此財產位置變動單嗎？",
        EXECUTE: "確定要執行此財產位置變動嗎？",
        CANCEL: "確定要取消此財產位置變動嗎？",
        BATCH_DELETE: "確定要批次刪除選中的財產位置變動單嗎？"
    }
};

// 欄位映射
export const FIELD_MAPPING = {
    transferNo: "變動單號",
    transferDate: "變動日期",
    applicantName: "申請人",
    applicantDepartmentName: "申請部門",
    transferReason: "變動原因",
    approvalStatus: "審核狀態",
    executionStatus: "執行狀態",
    approverName: "審核人",
    executorName: "執行人",
    notes: "備註"
};

export default {
    DEFAULT_VALUES,
    TABLE_CONFIG,
    FORM_LAYOUT,
    MODAL_CONFIG,
    FORM_RULES,
    STATUS_COLORS,
    PERMISSIONS,
    MESSAGES
}; 