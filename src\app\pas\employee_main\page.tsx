'use client';

import React, { useEffect, useState } from 'react';
import { Employee, getEmployeeList, FilterData, getEmployeeDetail, deleteEmployee } from '@/services/pas/EmployeeService';
import { Promotion, getPromotionByEffectiveDate } from '@/services/pas/PromotionService';
import { Input, Button, Space, message, Card, Select, Modal, Badge, Row, Col, Avatar, Popconfirm, Drawer, Tabs } from 'antd';
import { LeftOutlined, RightOutlined, MenuOutlined, UserAddOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

// Material Icons CSS
import '@/styles/material-icons.css';

// component.
import FilterTypeSelect from '@/app/pas/components/FilterTypeSelect'; // 搜尋框
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import TabsCard from '@/app/pas/employee_main/taboptions/TabsCard'; // 功能選項
import { allTabs } from '@/app/pas/employee_main/taboptions/constants/tabOptions'; // 選單項目


// employee_modules_page.
import PromotionInfo from '@/app/pas/employee_main/employee_modules/promotion/PromotionInfo'; // 升遷資料
import EducationInfo from '@/app/pas/employee_main/employee_modules/education/EducationInfo'; // 學歷資料
import TrainInfo from '@/app/pas/employee_main/employee_modules/train/TrainInfo'; // 教育訓練資料
import ExaminationInfo from '@/app/pas/employee_main/employee_modules/examination/ExaminationInfo'; // 考試資料
import CertificationInfo from '@/app/pas/employee_main/employee_modules/certification/CertificationInfo'; // 檢覈資料
import UndergoInfo from '@/app/pas/employee_main/employee_modules/undergo/UndergoInfo'; // 經歷資料
import EnsureInfo from '@/app/pas/employee_main/employee_modules/ensure/EnsureInfo'; // 保證資料
import SuspendInfo from '@/app/pas/employee_main/employee_modules/suspend/SuspendInfo'; // 留停資料
import HensureInfo from '@/app/pas/employee_main/employee_modules/hensure/HensureInfo'; // 眷保資料
import DependentInfo from '@/app/pas/employee_main/employee_modules/dependent/DependentInfo'; // 扶養資料
import PerformancePointRecordInfo from '@/app/pas/employee_main/employee_modules/performancePointRecord/PerformancePointRecordInfo' //績效點數紀錄資料
import RegularSalaryRecordInfo from '@/app/pas/employee_main/employee_modules/regularSalaryRecord/RegularSalaryRecordInfo'; // 常態性薪資管理
import InsuranceHistoryInfo from './employee_modules/insuranceHistory/InsuranceHistoryInfo';

import SalaryInfo from '@/app/pas/employee_main/employee_modules/salary/SalaryInfo'; // 薪資主檔資料
import EmployeeInfo from '@/app/pas/employee_main/employee_modules/employee/EmployeeInfo'; // 人事主檔資料
import EditEmployeeForm from '@/app/pas/employee_main/employee_modules/employee/EditEmployeeForm'; // 編輯人事資料頁面

import './styles/employeePage.css';


const EmployeePage = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>(''); // 當前指到的userid
  const [deleteUserid, setDeleteUserid] = useState<string | null>(null);
  const [page, setPage] = useState(0); // 第幾頁（0開始）
  const [currentIndex, setCurrentIndex] = useState(0); // 目前在第幾筆 (for單筆切換)
  const [viewMode, setViewMode] = useState<'card' | 'single'>('card'); // 顯示模式：多張/單筆

  const pageSize = 6; // 一頁6筆
  const total = employees.length; // 總筆數
  const totalPages = Math.ceil(total / pageSize); // 總頁數

  const [employeeDetail, setEmployeeDetail] = useState<Employee | null>(null); //當筆員工資料明細.
  const [currentPromotion, setCurrentPromotion] = useState<Promotion | null>(null); //當前員工的升遷資料
  const [isModalVisible, setIsModalVisible] = useState(false); // 功能選單modal顯示狀態
  const [editModalVisible, setEditModalVisible] = useState(false); //人事資料用modal顯示狀態
  const [editStatus, setEditStatus] = useState<string>('') //人事資料用modal模式
  const [filterData, setFilterData] = useState<FilterData>({ //篩選條件
    filterType: 'EmpNo',
    filterValue: '',
  });
  const [selectedTab, setSelectedTab] = useState<string>('employee'); // 當前選取的功能選單
  const [tabUpdateIdx, setTabUpdateIdx] = useState<number>(0); // 更新flag.

  useEffect(() => {
    fetchEmployees(); // 初始撈全部使用者ID
  }, []);

  // 依輸入條件搜尋user清單.
  const handleSearch = () => {
    fetchEmployees();
  };

  const fetchEmployees = async () => {
    try {
      const response = await getEmployeeList(filterData);
      if (response.success && Array.isArray(response.data)) {
        setEmployees(response.data);
        if (response.data.length > 0) {
          setCurrentIndex(0); // 當前第幾筆.
          setPage(0); // 重新搜尋返回第1頁
          setSelectedUserId(response.data[0].usersDTO.userId);
        } else {
          setEmployees([]); // 把員工清單清空（防止殘留）
          setEmployeeDetail(null);
          setCurrentPromotion(null);
          setCurrentIndex(-1); // 當前第幾筆.
          setPage(0); // 沒資料時頁碼也回到0
          setSelectedUserId('');
          message.info('查無資料');
        }
      } else {
        message.error(response.message || '取得員工列表失敗');
      }
    } catch (error) {
      message.error('伺服器錯誤');
    }
  };

  // 明細獲取更新
  useEffect(() => {
    fetchEmployeeDetail(selectedUserId);
    fetchCurrentPromotion(selectedUserId);
  }, [selectedUserId]);

  // 依目前userid搜尋員工明細
  const fetchEmployeeDetail = async (userid: string) => {
    if (userid === '') {
      return;
    }

    try {
      const response = await getEmployeeDetail(userid);
      if (response.success) {
        if (response.data) {
          setEmployeeDetail(response.data);
        } else {
          setEmployeeDetail(null);
          message.info('查無資料');
        }
      } else {
        message.error(response.message || '取得員工明細資料失敗');
      }

    } catch (error) {
      console.error('取得員工明細失敗', error);
      message.error('載入員工明細失敗');
    }
  };

  // 依目前userid獲取當天生效的升遷資料
  const fetchCurrentPromotion = async (userid: string) => {
    if (userid === '') {
      setCurrentPromotion(null);
      return;
    }

    try {
      const response = await getPromotionByEffectiveDate(userid); // 不傳日期，使用當天
      if (response.success && response.data) {
        setCurrentPromotion(response.data);
      } else {
        setCurrentPromotion(null);
      }
    } catch (error) {
      console.error('取得升遷資料失敗', error);
      setCurrentPromotion(null);
    }
  };

  // 功能選單控制
  useEffect(() => {
    setIsModalVisible(false);
  }, [selectedTab]);

  const handleAddClick = () => {
    setEditModalVisible(true);
    setEditStatus('add');
  };

  const handleEditClick = () => {
    setEditModalVisible(true);
    setEditStatus('edit');
  };

  const handlecompleteClick = () => {
    setEditStatus('complete');
    setEditModalVisible(true);
  };

  const deleteEmployeeDetail = async (userid: string) => {
    if (userid === '') return;

    try {
      const response = await deleteEmployee(userid);
      if (response.data.result) {
        message.success('刪除完成');

        // Step 1: 找出目前 index
        const currentIdx = employees.findIndex(emp => emp.usersDTO.userId === userid);
        let newSelectedUserId = '';
        let newIndex = -1;

        // Step 2: 刪除成功後選擇的下一筆
        if (employees.length > 1) {
          if (currentIdx > 0) {
            // 有上一筆，選上一筆
            newIndex = currentIdx - 1;
          } else {
            // 沒有上一筆，選下一筆
            newIndex = 1;
          }
          newSelectedUserId = employees[newIndex].usersDTO.userId;
        }

        // Step 3: 重新撈資料
        const result = await getEmployeeList(filterData);
        if (result.success && Array.isArray(result.data)) {
          setEmployees(result.data);

          if (newSelectedUserId) {
            setSelectedUserId(newSelectedUserId);
            setCurrentIndex(newIndex);
            setPage(Math.floor(newIndex / pageSize));
          } else {
            // 如果刪完沒有資料
            setSelectedUserId('');
            setCurrentIndex(-1);
            setPage(0);
            setEmployeeDetail(null);
            setCurrentPromotion(null);
          }
        }

        setTabUpdateIdx(prev => prev + 1);

      } else {
        message.error(response.data.msg || '刪除員工資料失敗');
      }

    } catch (error) {
      console.error('刪除員工資料失敗', error);
      message.error('刪除員工資料失敗');
    }
  };



  const handleEditSuccess = () => {
    setEditModalVisible(false);
    updateEmployees(); // 異動成功後重新撈資料
    setTabUpdateIdx(prev => prev + 1);
  };

  const handleEditCancel = () => {
    setEditModalVisible(false);
    setEditStatus('');
  };

  //更新員工列表資料 (delete update add)
  const updateEmployees = async () => {
    const response = await getEmployeeList(filterData);

    if (response.success && Array.isArray(response.data)) {
      setEmployees(response.data);
      fetchEmployeeDetail(selectedUserId);
      fetchCurrentPromotion(selectedUserId);
    } else {
      message.error(response.message || '更新員工列表失敗');
    }
  };

  /* 員工資料卡檢視控制 */
  const currentEmployees = employees.slice(page * pageSize, page * pageSize + pageSize);

  // 切上一筆（單筆）
  const handlePrevEmployee = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      setSelectedUserId(employees[newIndex].usersDTO.userId);

      const newPage = Math.floor(newIndex / pageSize);
      if (newPage !== page) setPage(newPage);
    }
  };

  // 切下一筆（單筆）
  const handleNextEmployee = () => {
    if (currentIndex < total - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      setSelectedUserId(employees[newIndex].usersDTO.userId);

      const newPage = Math.floor(newIndex / pageSize);
      if (newPage !== page) setPage(newPage);
    }
  };

  return (
    <div className="employee-page-container">
      <h2 className="employee-page-title">員工資料主頁</h2>
      <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginBottom: 16 }}>
        <Space wrap>
          <FilterTypeSelect
            filterType={filterData.filterType}
            filterValue={filterData.filterValue}
            onFilterChange={(field, value) => setFilterData(prev => ({ ...prev, [field]: value }))}
            onSearch={handleSearch}
          />
        </Space>

        <div>
          <span>總筆數: {total}</span>
          <Button disabled={currentIndex <= 0} onClick={handlePrevEmployee} style={{ marginLeft: 8 }}>
            上一筆
          </Button>
          <Button disabled={currentIndex >= total - 1} onClick={handleNextEmployee} style={{ marginLeft: 8 }}>
            下一筆
          </Button>
          <Button onClick={() => setViewMode(viewMode === 'card' ? 'single' : 'card')} style={{ marginLeft: 8 }}>
            {viewMode === 'card' ? '切到單筆' : '切到名片'}
          </Button>
        </div>
      </div>



      <Card>
        <Row gutter={16}>
          {/* 左邊名片區 */}
          <Col xs={24} sm={24} md={24} lg={18} xl={18} style={{ transition: 'all 0.3s ease' }}>
            {employees.length === 0 ? (
              <div className="empty-state">
                <span className="empty-state-title">查無員工資料</span>
              </div>
            ) : (
              <>
                {viewMode === 'card' ? (
                  // 名片模式
                  <Card>
                    <div style={{ paddingTop: '40px' }}>
                      <Row gutter={[16, 16]}>
                        {currentEmployees.map((emp) => {
                          return (
                            <Col key={emp.usersDTO.userId} sm={24} md={12} xl={8}>
                              <Card
                                hoverable
                                className={`employee-card ${selectedUserId === emp.usersDTO.userId ? 'employee-card-selected' : 'employee-card-normal'} ${deleteUserid === emp.usersDTO.userId ? 'card-deleting' : ''}`}

                                onClick={() => {
                                  setSelectedUserId(emp.usersDTO.userId);
                                  const newIndex = employees.findIndex(e => e.usersDTO.userId === emp.usersDTO.userId);
                                  if (newIndex !== -1) {
                                    setCurrentIndex(newIndex);
                                  }
                                  if (emp.userId === "") {
                                    Modal.confirm({
                                      title: '資料不完整',
                                      content: '此員工資料尚未補全，是否立即補全？',
                                      okText: '是',
                                      cancelText: '否',
                                      onOk() {
                                        handlecompleteClick();
                                      },
                                      onCancel() {
                                      }
                                    });
                                  }
                                }}
                              >
                                <div className="employee-card-body">
                                  <div className="employee-card-header">
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                                      <span className="employee-name-tag">姓名</span>
                                      <h3 className="employee-name">
                                        {emp.usersDTO.name}
                                        {selectedUserId === emp.usersDTO.userId && (
                                          <span className="selected-badge">已選取</span>
                                        )}
                                      </h3>
                                    </div>
                                  </div>

                                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                    <div className="employee-info-row">
                                      <span className="info-label">員工編號</span>
                                      <span className="info-value">{emp.empNo}</span>
                                    </div>

                                    <div className="employee-info-row">
                                      <span className="info-label">身份證號</span>
                                      <span className="info-value">{emp.idNo}</span>
                                    </div>
                                  </div>

                                  {emp.userId === "" && (
                                    <div className="incomplete-warning">
                                      <span className="warning-dot"></span>
                                      <span className="warning-text">員工資料未建立</span>
                                    </div>
                                  )}
                                </div>
                              </Card>
                            </Col>
                          );
                        })}
                      </Row>
                    </div>

                    <div className="pagination-controls">
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<LeftOutlined />}
                        disabled={page === 0}
                        onClick={() => setPage(page - 1)}
                      />
                      <span className="page-number">
                        {page + 1} / {totalPages}
                      </span>
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<RightOutlined />}
                        disabled={page >= totalPages - 1}
                        onClick={() => setPage(page + 1)}
                      />
                    </div>
                  </Card>
                ) : (
                  // 單筆詳細資料模式
                  <Card title="詳細資料">
                    {selectedUserId ? (
                      (() => {
                        const selectedEmp = employees.find(emp => emp.userId === selectedUserId); //這邊搜尋用selectid 對 emp userid 沒有代表沒有建立employee資料.
                        return selectedEmp ? (
                          <div>
                            <Row>
                              <Col flex={3}>
                                <Row>
                                  <Col flex={3}>
                                    {/* 人事資料 */}
                                    <Card>
                                      <Row>
                                        <Col flex={2}>
                                          <p><strong>員工編號：</strong>{selectedEmp?.empNo ?? ''}</p>
                                          <p><strong>身份證字號：</strong>{selectedEmp?.idNo ?? ''}</p>
                                          <p><strong>生日：</strong>{selectedEmp?.birthday ?? ''}</p>
                                          <p><strong>任用資格：</strong>{currentPromotion?.jobroleTypeName ?? ''}</p>
                                          <p><strong>錄用類別：</strong>{currentPromotion?.categoryTypeName ?? ''}</p>
                                        </Col>
                                        <Col flex={2}>
                                          <p><strong>帳號：</strong>{selectedEmp?.usersDTO.account ?? ''}</p>
                                          <p><strong>姓名：</strong>{selectedEmp?.usersDTO.name ?? ''}</p>
                                          <p><strong>服務部門：</strong>{
                                            currentPromotion?.serviceDepartmentChange ?
                                              (currentPromotion.serviceDepartmentChange.serviceDepartmentName +
                                                (currentPromotion.serviceDepartmentChange.serviceDivisionName ?
                                                  ` (${currentPromotion.serviceDepartmentChange.serviceDivisionName})` : '')) :
                                              ''
                                          }</p>
                                          <p><strong>職稱：</strong>{currentPromotion?.jobTitleName ?? ''}</p>
                                          <p><strong>職務：</strong>{selectedEmp?.usersDTO.positionName ?? ''}</p>
                                        </Col>
                                      </Row>
                                    </Card>
                                  </Col>
                                  <Col flex={1}>
                                    {/* 薪俸資料 */}
                                    <Card>
                                      <p><strong>開支部門：</strong>{currentPromotion?.expenseDepartmentChange?.expenseDepartmentName ?? ''}</p>
                                      <p><strong>薪俸類型：</strong>{currentPromotion?.salaryTypeName ?? ''}</p>
                                      <p><strong>薪俸：</strong>{
                                        currentPromotion?.salaryAmount ?
                                          (() => {
                                            const amount = parseFloat(currentPromotion.salaryAmount);
                                            return isNaN(amount) ? currentPromotion.salaryAmount : `${amount.toLocaleString()}元`;
                                          })() : ''
                                      }</p>
                                      <p><strong>職等：</strong>{currentPromotion?.jobLevelName ?? ''}</p>
                                      <p><strong>級數：</strong>{currentPromotion?.jobRankName ?? ''}</p>
                                    </Card>
                                  </Col>
                                </Row>
                              </Col>
                            </Row>
                          </div>
                        ) : (
                          <p>無對應員工資料 請完成員工資料補全</p>
                        );
                      })()
                    ) : (
                      <p>請先選取員工資料</p>
                    )}
                  </Card>
                )}
              </>
            )}
          </Col>

          {/* 右邊功能選單 */}
          <Col xs={24} sm={24} md={24} lg={6} xl={6} style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            transition: 'all 0.3s ease'
          }}>
            <Card
              className="function-menu-card"
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f8fafc)',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px'
              }}>
                <Button
                  type="primary"
                  icon={<MenuOutlined />}
                  block
                  className="function-button primary-function-button"
                  onClick={() => setIsModalVisible(true)}
                >
                  開啟功能選單
                </Button>

                <Button
                  type="default"
                  icon={<UserAddOutlined />}
                  block
                  className="function-button default-function-button"
                  onClick={handleAddClick}
                >
                  新增員工資料
                </Button>

                <Button
                  type="default"
                  icon={<EditOutlined />}
                  block
                  disabled={selectedUserId === ''}
                  className="function-button default-function-button"
                  onClick={employeeDetail?.userId === "" ? handlecompleteClick : handleEditClick}
                >
                  {employeeDetail?.userId === "" ? "補全員工資料" : "編輯員工資料"}
                </Button>

                <Popconfirm
                  title="確定要刪除這位員工資料嗎？"
                  onConfirm={() => setDeleteUserid(selectedUserId)}
                  okText="確認"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    block
                    disabled={selectedUserId === ''}
                    className="function-button danger-function-button"
                  >
                    刪除員工資料
                  </Button>
                </Popconfirm>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 分頁控制自行顯示 loading 或 error， */}
      {/* 明細 */}


      <Card>
        <div className="tabs-container">
          <div className="tabs-header">
            <h3 className="tabs-title">員工資料詳情</h3>
          </div>

          <Tabs
            type="card"
            items={allTabs.map(tab => ({
              key: tab.key,
              label: (
                <div style={{
                  padding: '10px 20px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '14px',
                  fontWeight: selectedTab === tab.key ? 600 : 400,
                  color: selectedTab === tab.key ? '#1890ff' : '#666',
                  transition: 'all 0.3s ease',
                  borderRadius: '6px',
                  position: 'relative'
                }}>
                  {tab.label}
                  {selectedTab === tab.key && (
                    <div style={{
                      position: 'absolute',
                      bottom: '-2px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '20px',
                      height: '2px',
                      background: '#1890ff',
                      borderRadius: '2px'
                    }} />
                  )}
                </div>
              ),
            }))}
            activeKey={selectedTab}
            onChange={setSelectedTab}
            tabBarStyle={{
              marginBottom: '20px',
              borderBottom: '2px solid #e8e8e8',
              padding: '0 8px'
            }}
            tabBarGutter={16}
            animated={{ tabPane: true }}
          />

          <div className="tab-content">
            {selectedUserId ? (
              <>
                <div style={{ display: selectedTab === 'employee' ? 'block' : 'none' }}>
                  <EmployeeInfo userId={selectedUserId} active={selectedTab === 'employee'} tabUpdateidx={tabUpdateIdx} />
                </div>
                <div style={{ display: selectedTab === 'promotion' ? 'block' : 'none' }}>
                  <PromotionInfo userId={selectedUserId} active={selectedTab === 'promotion'} />
                </div>
                <div style={{ display: selectedTab === 'education' ? 'block' : 'none' }}>
                  <EducationInfo userId={selectedUserId} active={selectedTab === 'education'} />
                </div>
                <div style={{ display: selectedTab === 'train' ? 'block' : 'none' }}>
                  <TrainInfo userId={selectedUserId} active={selectedTab === 'train'} />
                </div>
                <div style={{ display: selectedTab === 'examination' ? 'block' : 'none' }}>
                  <ExaminationInfo userId={selectedUserId} active={selectedTab === 'examination'} />
                </div>
                <div style={{ display: selectedTab === 'certification' ? 'block' : 'none' }}>
                  <CertificationInfo userId={selectedUserId} active={selectedTab === 'certification'} />
                </div>
                <div style={{ display: selectedTab === 'undergo' ? 'block' : 'none' }}>
                  <UndergoInfo userId={selectedUserId} active={selectedTab === 'undergo'} />
                </div>
                <div style={{ display: selectedTab === 'ensure' ? 'block' : 'none' }}>
                  <EnsureInfo userId={selectedUserId} active={selectedTab === 'ensure'} />
                </div>
                <div style={{ display: selectedTab === 'suspend' ? 'block' : 'none' }}>
                  <SuspendInfo userId={selectedUserId} active={selectedTab === 'suspend'} />
                </div>
                <div style={{ display: selectedTab === 'salary' ? 'block' : 'none' }}>
                  <SalaryInfo userId={selectedUserId} active={selectedTab === 'salary'} />
                </div>
                <div style={{ display: selectedTab === 'hensure' ? 'block' : 'none' }}>
                  <HensureInfo userId={selectedUserId} active={selectedTab === 'hensure'} />
                </div>
                <div style={{ display: selectedTab === 'dependent' ? 'block' : 'none' }}>
                  <DependentInfo userId={selectedUserId} active={selectedTab === 'dependent'} />
                </div>
                <div style={{ display: selectedTab === 'performance' ? 'block' : 'none' }}>
                  <PerformancePointRecordInfo userId={selectedUserId} active={selectedTab === 'performance'} />
                </div>
                <div style={{ display: selectedTab === 'regularSalary' ? 'block' : 'none' }}>
                  <RegularSalaryRecordInfo userId={selectedUserId} active={selectedTab === 'regularSalary'} />
                </div>
                <div style={{ display: selectedTab === 'experience' ? 'block' : 'none' }}>
                  <div className="empty-state">
                    <div style={{ marginBottom: '16px' }}>
                      <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="64" height="64" rx="32" fill="#F3F4F6" />
                        <path d="M32 24V40M24 32H40" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round" />
                      </svg>
                    </div>
                    工作經歷內容
                  </div>
                </div>
                <div style={{ display: selectedTab === 'license' ? 'block' : 'none' }}>
                  <div className="empty-state">
                    <div style={{ marginBottom: '16px' }}>
                      <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="64" height="64" rx="32" fill="#F3F4F6" />
                        <path d="M32 24V40M24 32H40" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round" />
                      </svg>
                    </div>
                    證照資料內容
                  </div>
                </div>
                <div style={{ display: selectedTab === 'insuranceHistory' ? 'block' : 'none' }}>
                  <InsuranceHistoryInfo userId={selectedUserId} active={selectedTab === 'insuranceHistory'} />
                </div>
              </>
            ) : (
              <div className="empty-state">
                <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="80" height="80" rx="40" fill="#F3F4F6" />
                  <path d="M40 28V52M28 40H52" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round" />
                </svg>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', alignItems: 'center' }}>
                  <span className="empty-state-title">尚未選取員工</span>
                  <span className="empty-state-subtitle">請先從左側列表選擇一位員工</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      <Modal
        title="功能選單"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
      >
        <TabsCard
          selectedKey={selectedTab}
          onSelect={setSelectedTab}
        />
      </Modal>

      <EditEmployeeForm
        userId={selectedUserId}
        mode={editStatus}
        visible={editModalVisible}
        onSuccess={handleEditSuccess}
        onCancel={handleEditCancel}
      />

      {deleteUserid && (
        <DeleteWithCountdown
          onDelete={async () => {
            try {
              await deleteEmployeeDetail(deleteUserid);
              setDeleteUserid(null);
            } catch (error) {
              message.error('刪除失敗，請稍後再試');
            }
          }}
          onCancel={() => setDeleteUserid(null)}
        />
      )}
    </div>
  );
};

export default EmployeePage;
