using FAST_ERP_Backend.Models.Ims;
using System;
using System.Collections.Generic;
using System.Data;

namespace FAST_ERP_Backend.Interfaces.Ims;
/// <summary> 價格類別服務介面 </summary>
public interface IPriceTypeService
{
    /// <summary> 價格類別列表 </summary>
    Task<List<PriceTypeDTO>> GetAllAsync();

    /// <summary> 價格類別取得 </summary>
    Task<List<PriceTypeDTO>> GetAsync(Guid PriceTypeID);

    /// <summary> 價格類別新增 </summary>
    Task<(bool, string)> AddAsync(PriceTypeDTO item);

    /// <summary> 價格類別更新 </summary>
    Task<(bool, string)> UpdateAsync(PriceTypeDTO item);

    /// <summary> 價格類別刪除 </summary>
    Task<(bool, string)> DeleteAsync(PriceTypeDTO item);
}