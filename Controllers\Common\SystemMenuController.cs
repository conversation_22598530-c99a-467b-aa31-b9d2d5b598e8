﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using System.Threading.Tasks;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;
using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("系統選單管理")]
    public class SystemMenuController : ControllerBase
    {
        private readonly ISystemMenuService _systemMenuService;
        private readonly IUsersService _usersService;
        public SystemMenuController(ISystemMenuService systemMenuService, IUsersService usersService)
        {
            _systemMenuService = systemMenuService;
            _usersService = usersService;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        [HttpGet]
        [Route("GetSystemMenu")]
        [SwaggerOperation(Summary = "取得系統選單列表", Description = "取得系統選單列表")]
        public async Task<IActionResult> GetSystemMenuList()
        {
            var menus = await _systemMenuService.GetSystemMenuAsync();
            return Ok(menus);
        }

        [HttpGet]
        [Route("GetSystemMenu/{systemMenuId?}")]
        [SwaggerOperation(Summary = "取得系統選單明細", Description = "依ID取得系統選單明細")]
        public async Task<IActionResult> GetSystemMenuDetail(string systemMenuId)
        {
            var menu = await _systemMenuService.GetSystemMenuAsync(systemMenuId);
            return Ok(menu);
        }

        [HttpGet]
        [Route("GetAllMenu")]
        [SwaggerOperation(Summary = "取得系統所有功能選單列表", Description = "取得系統所有功能選單列表")]
        public async Task<IActionResult> GetAllMenu()
        {
            var menus = await _systemMenuService.GetGroupMenuAsync();
            return Ok(menus);
        }

        [HttpGet]
        [Route("GetMyMenu")]
        [SwaggerOperation(Summary = "依使用者權限取得系統側邊選單列表", Description = "依使用者權限取得側邊選單列表")]
        public async Task<IActionResult> GetMyMenu(string systemGroupId)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var loginUser = (await _usersService.GetUsersAsync(tokenUid)).FirstOrDefault();
            var menus = await _systemMenuService.GetGroupMenuAsync(systemGroupId,loginUser.RolesId);
            return Ok(menus);
        }

        [HttpGet]
        [Route("GetMyAllMenu")]
        [SwaggerOperation(Summary = "依使用者權限取得所有系統側邊選單列表", Description = "依使用者權限取得所有側邊選單列表")]
        public async Task<IActionResult> GetMyAllMenu()
        {
            var systemGroupId = "";
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var loginUser = (await _usersService.GetUsersAsync(tokenUid)).FirstOrDefault();
            var menus = await _systemMenuService.GetGroupMenuAsync(systemGroupId,loginUser.RolesId);
            return Ok(menus);
        }

        [HttpPost]
        [Route("AddSystemMenu")]
        [SwaggerOperation(Summary = "新增系統選單", Description = "新增系統選單資料")]
        public async Task<IActionResult> AddSystemMenu([FromBody] SystemMenuDTO menu)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _systemMenuService.AddSystemMenuAsync(menu, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditSystemMenu")]
        [SwaggerOperation(Summary = "編輯系統選單", Description = "修改已存在之系統選單資料")]
        public async Task<IActionResult> EditSystemMenu([FromBody] SystemMenuDTO menu)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _systemMenuService.EditSystemMenuAsync(menu, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteSystemMenu")]
        [SwaggerOperation(Summary = "刪除系統選單", Description = "刪除已存在之系統選單資料")]
        public async Task<IActionResult> DeleteSystemMenu([FromBody] SystemMenuDTO menu)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _systemMenuService.DeleteSystemMenuAsync(menu, tokenUid);
            return Ok(new { result, msg });
        }
    }
}
