using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetAccountService
    {
        /// <summary>
        /// 取得所有資產科目
        /// </summary>
        /// <returns>所有資產科目</returns>
        Task<List<AssetAccountDTO>> GetAllAsync();

        /// <summary>
        /// 取得資產科目ById
        /// </summary>
        /// <param name="id">資產科目編號</param>
        /// <returns>資產科目</returns>
        Task<string> GetByIdAsync(int id);

        /// <summary>
        /// 新增資產科目
        /// </summary>
        /// <param name="assetAccount">資產科目</param>
        /// <returns>新增的資產科目</returns>
        Task<(bool, string)> AddAsync(AssetAccountDTO assetAccount);

        /// <summary>
        /// 更新資產科目
        /// </summary>
        /// <param name="assetAccount">資產科目</param>
        /// <returns>更新後的資產科目</returns>
        Task<(bool, string)> UpdateAsync(AssetAccountDTO assetAccount);

        /// <summary>
        /// 刪除資產科目
        /// </summary>
        /// <param name="_data">資產科目</param>
        /// <returns>是否刪除成功</returns> 
        Task<(bool, string)> DeleteAsync(AssetAccountDTO _data);
    }
}