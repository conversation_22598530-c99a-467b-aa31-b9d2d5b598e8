using FAST_ERP_Backend.Models.Common;

public static class UsersHelper
{
    public static void UpdateUserFields(Users existingUser, UsersDTO _data)
    {
        existingUser.Account = _data.Account;
        existingUser.Name = _data.Name;
        existingUser.EnterpriseGroupId = _data.EnterpriseGroupId;
        existingUser.RolesId = _data.RolesId;
        existingUser.PositionId = _data.PositionId;
        existingUser.EMail = _data.EMail;
        existingUser.PermanentAddress = _data.PermanentAddress;
        existingUser.MailingAddress = _data.MailingAddress;
        existingUser.TelNo = _data.TelNo;
        existingUser.Phone = _data.Phone;
        existingUser.AltPhone = _data.AltPhone;
        existingUser.SortCode = _data.SortCode;
    }

    public static void UpdateBaseUserData(Users existingUser, UsersDTO _data)
    {
        existingUser.Name = _data.Name;
        existingUser.EMail = _data.EMail;
        existingUser.PermanentAddress = _data.PermanentAddress;
        existingUser.MailingAddress = _data.MailingAddress;
        existingUser.TelNo = _data.TelNo;
        existingUser.Phone = _data.Phone;
        existingUser.AltPhone = _data.AltPhone;
    }
}
