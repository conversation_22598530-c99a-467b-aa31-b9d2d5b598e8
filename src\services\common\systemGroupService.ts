import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 系統群組介面
export interface SystemGroup {
    systemGroupId: string;
    name: string;
    description: string | null;
}

// 獲取系統群組列表
export async function getSystemGroups(): Promise<ApiResponse<SystemGroup[]>> {
    try {
        const response = await httpClient(apiEndpoints.getSystemGroups, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統群組列表失敗",
            data: []
        };
    }
}

// 獲取單一系統群組
export async function getSystemGroup(systemGroupId: string): Promise<ApiResponse<SystemGroup>> {
    try {
        const response = await httpClient(`${apiEndpoints.getSystemGroups}/${systemGroupId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統群組失敗",
            data: undefined
        };
    }
}