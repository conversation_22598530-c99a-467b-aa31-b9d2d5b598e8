import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 角色
export interface Role {
    rolesId: string;
    name: string;
    rolesPermissions?: rolesPermissions[];
}

// 角色權限
export interface rolesPermissions {
    rolesPermissionsId: string;
    rolesId: string;
    systemMenuId: string;
}

// 獲取角色列表
export const getRoles = async (): Promise<ApiResponse<Role[]>> => {
    try {
        const response = await httpClient(apiEndpoints.getRoles, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取角色列表失敗",
            data: []
        };
    }
};

// 新增角色
export const AddRole = async (data: Partial<Role>): Promise<ApiResponse<Role[]>> => {
    try {
        const response = await httpClient(apiEndpoints.addRole, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增腳色資訊失敗",
        };
    }
} ;

// 編輯角色
export const editRole = async (data: Partial<Role>): Promise<ApiResponse<Role[]>> => {
    try {
        const response = await httpClient(apiEndpoints.editRole, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯腳色資訊失敗",
        };
    }
} ;
// 刪除角色
export const deleteRole = async (data: Partial<Role>): Promise<ApiResponse<Role[]>> => {
    try {
        const response = await httpClient(apiEndpoints.deleteRole, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除腳色資訊失敗",
        };
    }
} ;
