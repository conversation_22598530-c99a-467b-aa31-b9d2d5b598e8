"use client";

import React, { useEffect, useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import { fetchCurrentConnectionCount } from "@/services/common/signalRService";

const ConnectionCountComponent: React.FC = () => {
  const [connectionCount, setConnectionCount] = useState<string | null>(null);

  useEffect(() => {
    // 定義一個函數來獲取當前連線數並更新狀態
    const updateConnectionCount = async () => {
      const count = await fetchCurrentConnectionCount();
      if (count !== undefined) {
        setConnectionCount(count);
      } else {
        setConnectionCount(null);
      }
    };

    // 立即調用一次來初始化
    updateConnectionCount();

    // 設置定時器，每 10 秒更新一次連線數
    const interval = setInterval(updateConnectionCount, 10000);

    // 清除定時器
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center">
      <span className="font-medium mr-1">連線數量:</span>
      {connectionCount !== null ? (
        <span className="inline-block">{connectionCount}</span>
      ) : (
        <LoadingOutlined className="text-gray-400" />
      )}
    </div>
  );
};

export default ConnectionCountComponent;
