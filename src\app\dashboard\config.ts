import { DashboardLayout, ResponsiveLayouts } from "./types";
import { getAllWidgets, generateResponsiveLayouts } from "./widgets/registry";

// Get available widgets from the registry
export const availableWidgets = getAllWidgets();

// Responsive breakpoints configuration
export const breakpoints = {
    lg: 1200,
    md: 996,
    sm: 768,
    xs: 480,
    xxs: 0,
};

// Column configuration for each breakpoint
export const cols = {
    lg: 12,
    md: 10,
    sm: 6,
    xs: 4,
    xxs: 2,
};

// Responsive margin configuration
export const responsiveMargin = {
    lg: [16, 16] as [number, number],
    md: [16, 16] as [number, number],
    sm: [12, 12] as [number, number],
    xs: [8, 8] as [number, number],
    xxs: [8, 8] as [number, number],
};

// Responsive container padding configuration
export const responsiveContainerPadding = {
    lg: [16, 16] as [number, number],
    md: [16, 16] as [number, number],
    sm: [12, 12] as [number, number],
    xs: [8, 8] as [number, number],
    xxs: [8, 8] as [number, number],
};

// Generate default layouts automatically from widget configurations
export const defaultLayouts: ResponsiveLayouts = generateResponsiveLayouts(availableWidgets);

export const gridSettings = {
    rowHeight: 80,
    margin: [16, 16] as [number, number],
    containerPadding: [16, 16] as [number, number],
};