﻿using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IEducationService
    {
        /// <summary>
        /// 取得所有資料列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<EducationDTO>> GetEducationListAsync(string _userid);

        /// <summary>
        /// 取得該筆資料明細
        /// </summary>
        /// <param name="_uid">userID</param>
        /// <returns>取得資料明細</returns>
        Task<EducationDTO> GetEducationDetailAsync(string _uid);

        /// <summary>
        /// 新增資料
        /// </summary>
        /// <param name="_data">資料結構</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddEducationAsync(EducationDTO _data);

        /// <summary>
        /// 編輯資料
        /// </summary>
        /// <param name="_data">資料結構</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditEducationAsync(EducationDTO _data);

        /// <summary>
        /// 刪除資料
        /// </summary>
        /// <param name="_data">資料結構</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteEducationAsync(string _uid);
    }
}
