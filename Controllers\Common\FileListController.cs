﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using System.Threading.Tasks;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace FAST_ERP_Backend.Controllers.Common;
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("系統參數管理")]
public class FileListController(IFileListService _IFileListService,IUsersService _usersService) : ControllerBase
{
    //取得登入者token資訊,在middleware時就會將資訊存入
    private ClaimsPrincipal LoginUser => HttpContext.User;

    [HttpGet]
    [Route("GetFileList")]
    [SwaggerOperation(Summary = "取得檔案列表", Description = "取得檔案列表")]
    public async Task<IActionResult> GetFileList([FromQuery] GetFileListRequest request)
    {
        var fileList = await _IFileListService.GetFileListAsync(request.FileListId);
        return Ok(fileList);
    }

    [HttpPost]
    [Route("UploadFiles")]
    [SwaggerOperation(Summary = "上傳檔案", Description = "上傳檔案")]
    public async Task<IActionResult> UploadFiles([FromForm] string fileData)
    {
        var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;

        // 將 fileData JSON 字串轉成 DTO List
        var fileList = JsonSerializer.Deserialize<List<FileListUploadDTO>>(fileData);
        var files = Request.Form.Files;

        if (fileList == null || files.Count == 0)
        {
            return BadRequest("缺少檔案或檔案描述資料");
        }

        // 建立 fileName 到 IFormFile 的對應字典
        var fileDict = files.ToDictionary(f => f.FileName, f => f);

        // 將檔案對應到 DTO
        foreach (var item in fileList)
        {
            if (fileDict.TryGetValue(item.uploadFileName, out var matchedFile))
            {
                item.uploadFile = matchedFile;
            }
            else
            {
                return BadRequest($"找不到與檔案描述中 `{item.uploadFileName}` 對應的實體檔案");
            }
        }

        var (result, msg) = await _IFileListService.UploadFilesAsync(fileList, tokenUid);
        return Ok(new { result, msg });
    }

    /// <summary> 更新檔案資訊 </summary>
    /// <param name="fileList">要更新的檔案資訊列表</param>
    /// <returns>更新結果</returns>
    [HttpPost]
    [Route("ChangesFile")]
    [SwaggerOperation(Summary = "更新檔案資訊", Description = "更新檔案資訊")]
    public async Task<IActionResult> ChangesFile([FromBody] List<FileListDTO> fileList)
    {
        var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
        var (result, msg) = await _IFileListService.ChangesFileAsync(fileList, tokenUid);
        return Ok(new { result, msg });
    }
}