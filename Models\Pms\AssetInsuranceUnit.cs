using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// 財產-承保單位 關聯表
/// </summary>
[Table("Pms_AssetInsuranceUnitMapping", Schema = "dbo")]
public class AssetInsuranceUnitMapping : ModelBaseEntity
{
    [Key]
    public Guid Id { get; set; }

    [Comment("財產流水號")]
    public Guid AssetId { get; set; }

    [Comment("承保單位編號")]
    public Guid InsuranceUnitId { get; set; }

    [Comment("新增時間")]
    [Column(TypeName = "bigint")]
    public long? CreateTime { get; set; }

    [Comment("新增者編號")]
    [Column(TypeName = "nvarchar(100)")]
    public string? CreateUserId { get; set; }

    [Comment("刪除時間")]
    [Column(TypeName = "bigint")]
    public long? DeleteTime { get; set; }

    [Comment("刪除者編號")]
    [Column(TypeName = "nvarchar(100)")]
    public string? DeleteUserId { get; set; }

    // 財產-外鍵關聯
    [ForeignKey("AssetId")]
    public virtual Asset Asset { get; set; }

    // 承保單位-外鍵關聯
    [ForeignKey("InsuranceUnitId")]
    public virtual InsuranceUnit InsuranceUnit { get; set; }
}