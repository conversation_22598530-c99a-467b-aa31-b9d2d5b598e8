﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class EnterpriseGroupsService : IEnterpriseGroupsService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;

        public EnterpriseGroupsService(Baseform baseform, ERPDbContext context)
        {
            _baseform = baseform;
            _context = context;
        }

        // 新增公司群組
        public async Task<(bool, string)> AddEnterpriseGroupsAsync(EnterpriseGroupsDTO _data)
        {
            try
            {
                var entity = new EnterpriseGroups
                {
                    EnterpriseGroupsId = Guid.NewGuid().ToString().Trim(),
                    Name = _data.Name,
                    SortCode = 0,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = _data.CreateUserId,
                    Representative = _data.Representative,
                    UnifiedNumber = _data.UnifiedNumber,
                    EstablishDate = _data.EstablishDate,
                    AccountingPeriod = _data.AccountingPeriod,
                    CompanyPhone = _data.CompanyPhone,
                    Phone = _data.Phone,
                    Email = _data.Email,
                    MobilePhone = _data.MobilePhone,
                    Fax = _data.Fax,
                    Website = _data.Website,
                    Address1 = _data.Address1,
                    Address2 = _data.Address2,
                    EnglishName = _data.EnglishName,
                    EnglishAddress = _data.EnglishAddress
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        // 刪除公司群組
        public async Task<(bool, string)> DeleteEnterpriseGroupsAsync(EnterpriseGroupsDTO _data)
        {
            try
            {
                var entity = await _context.Set<EnterpriseGroups>().FindAsync(_data.EnterpriseGroupsId);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        // 編輯公司群組
        public async Task<(bool, string)> EditEnterpriseGroupsAsync(EnterpriseGroupsDTO _data)
        {
            try
            {
                var entity = await _context.Set<EnterpriseGroups>().FindAsync(_data.EnterpriseGroupsId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.Name = _data.Name;
                entity.SortCode = _data.SortCode;
                entity.UpdateTime = _data.UpdateTime;
                entity.UpdateUserId = _data.UpdateUserId;
                entity.DeleteTime = _data.DeleteTime;
                entity.DeleteUserId = _data.DeleteUserId;
                entity.Representative = _data.Representative;
                entity.UnifiedNumber = _data.UnifiedNumber;
                entity.EstablishDate = _data.EstablishDate;
                entity.AccountingPeriod = _data.AccountingPeriod;
                entity.CompanyPhone = _data.CompanyPhone;
                entity.Phone = _data.Phone;
                entity.Email = _data.Email;
                entity.MobilePhone = _data.MobilePhone;
                entity.Fax = _data.Fax;
                entity.Website = _data.Website;
                entity.Address1 = _data.Address1;
                entity.Address2 = _data.Address2;
                entity.EnglishName = _data.EnglishName;
                entity.EnglishAddress = _data.EnglishAddress;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        // 取得公司群組詳細資料
        public async Task<string> GetEnterpriseGroupsDetailAsync(string _uid)
        {
            try
            {
                var entity = await _context.Set<EnterpriseGroups>().FindAsync(_uid);
                if (entity == null)
                {
                    return "找不到資料";
                }

                var dto = new EnterpriseGroupsDTO
                {
                    EnterpriseGroupsId = entity.EnterpriseGroupsId,
                    Name = entity.Name,
                    SortCode = entity.SortCode,
                    CreateTime = entity.CreateTime ?? 0,
                    CreateUserId = entity.CreateUserId,
                    UpdateTime = entity.UpdateTime,
                    UpdateUserId = entity.UpdateUserId,
                    DeleteTime = entity.DeleteTime ?? 0,
                    DeleteUserId = entity.DeleteUserId,
                    Representative = entity.Representative,
                    UnifiedNumber = entity.UnifiedNumber,
                    EstablishDate = entity.EstablishDate,
                    AccountingPeriod = entity.AccountingPeriod,
                    CompanyPhone = entity.CompanyPhone,
                    Phone = entity.Phone,
                    Email = entity.Email,
                    MobilePhone = entity.MobilePhone,
                    Fax = entity.Fax,
                    Website = entity.Website,
                    Address1 = entity.Address1,
                    Address2 = entity.Address2,
                    EnglishName = entity.EnglishName,
                    EnglishAddress = entity.EnglishAddress
                };

                return JsonConvert.SerializeObject(dto);
            }
            catch (Exception ex)
            {
                return $"取得資料失敗: {ex.Message}";
            }
        }

        // 取得公司群組列表
        public async Task<List<EnterpriseGroupsDTO>> GetEnterpriseGroupsListAsync()
        {
            try
            {
                var entities = await _context.Set<EnterpriseGroups>().ToListAsync();
                var dtos = entities.Select(entity => new EnterpriseGroupsDTO
                {
                    EnterpriseGroupsId = entity.EnterpriseGroupsId,
                    Name = entity.Name,
                    SortCode = entity.SortCode,
                    CreateTime = entity.CreateTime ?? 0,
                    CreateUserId = entity.CreateUserId,
                    UpdateTime = entity.UpdateTime,
                    UpdateUserId = entity.UpdateUserId,
                    DeleteTime = entity.DeleteTime ?? 0,
                    DeleteUserId = entity.DeleteUserId,
                    Representative = entity.Representative,
                    UnifiedNumber = entity.UnifiedNumber,
                    EstablishDate = entity.EstablishDate,
                    AccountingPeriod = entity.AccountingPeriod,
                    CompanyPhone = entity.CompanyPhone,
                    Phone = entity.Phone,
                    Email = entity.Email,
                    MobilePhone = entity.MobilePhone,
                    Fax = entity.Fax,
                    Website = entity.Website,
                    Address1 = entity.Address1,
                    Address2 = entity.Address2,
                    EnglishName = entity.EnglishName,
                    EnglishAddress = entity.EnglishAddress
                }).ToList();

                return dtos;
            }
            catch (Exception ex)
            {
                return new List<EnterpriseGroupsDTO> { new EnterpriseGroupsDTO { Name = $"取得列表失敗: {ex.Message}" } };
            }
        }
    }
}
