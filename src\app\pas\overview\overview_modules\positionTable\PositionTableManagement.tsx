'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button, Modal, message, Space } from 'antd';
import { PrinterOutlined, FilePdfOutlined } from '@ant-design/icons';
import './PositionTableManagement.css';



interface Employee {
    id: string;
    name: string;
    position: string;
    department: string;
    level: number;
    phone?: string;
    email?: string;
    parentId?: string;
    photoUrl?: string;
    profileUrl?: string;
    children?: Employee[];
    _children?: Employee[]; // 用於展開/收合功能
    x?: number;
    y?: number;
    collapsed?: boolean;
}

interface TreeNode {
    id: string;
    name: string;
    position: string;
    department: string;
    level: number;
    phone?: string;
    email?: string;
    parentId?: string;
    photoUrl?: string;
    profileUrl?: string;
    depth: number;
    children?: TreeNode[];
    _children?: TreeNode[];
    parent?: TreeNode;
    x?: number;
    y?: number;
    collapsed?: boolean;
}

const PositionTableManagement: React.FC = () => {
    // 將扁平數據轉換為樹狀結構
    const [treeData, setTreeData] = useState<TreeNode | null>(null);
    const [visibleNodes, setVisibleNodes] = useState<TreeNode[]>([]);
    const svgRef = useRef<SVGSVGElement>(null);

    // 配置選項 - 優化為更緊湊的顯示
    const config = {
        cardWidth: 120,
        cardHeight: 80,
        photoWidth: 45,
        photoHeight: 55,
        nodeVerticalSpacing: 130,
        nodeHorizontalSpacing: 20,
        paddingTop: 50,
        paddingLeft: 60,
        paddingRight: 60,
        paddingBottom: 80
    };

    // 組織架構員工數據 - 總幹事 -> 各部門主任 -> 組長 -> 組員
    const employeeData: Employee[] = [
        // 第1層 - 總幹事
        { id: '1', name: '總幹事', position: '總幹事', department: '總管理處', level: 1 },

        // 第2層 - 秘書及各部門主任
        { id: '2', name: '秘書', position: '秘書', department: '總管理處', level: 2, parentId: '1' },
        { id: '3', name: '陳主任', position: '會務主任', department: '會務部', level: 2, parentId: '1' },
        { id: '4', name: '林主任', position: '保險主任', department: '保險部', level: 2, parentId: '1' },
        { id: '5', name: '張主任', position: '信用主任', department: '信用部', level: 2, parentId: '1' },
        { id: '6', name: '劉主任', position: '供銷主任', department: '供銷部', level: 2, parentId: '1' },

        // 第3層 - 各部門組長
        { id: '7', name: '組長A', position: '會務組長', department: '會務部', level: 3, parentId: '3' },
        { id: '8', name: '組長B', position: '會務組長', department: '會務部', level: 3, parentId: '3' },

        { id: '9', name: '組長A', position: '保險組長', department: '保險部', level: 3, parentId: '4' },
        { id: '10', name: '組長B', position: '保險組長', department: '保險部', level: 3, parentId: '4' },

        { id: '11', name: '組長A', position: '信用組長', department: '信用部', level: 3, parentId: '5' },

        { id: '12', name: '組長A', position: '供銷組長', department: '供銷部', level: 3, parentId: '6' },
        { id: '13', name: '組長B', position: '供銷組長', department: '供銷部', level: 3, parentId: '6' },

        // 第4層 - 各組組員
        { id: '14', name: '組員A1', position: '會務組員', department: '會務部', level: 4, parentId: '7' },
        { id: '15', name: '組員A2', position: '會務組員', department: '會務部', level: 4, parentId: '7' },
        { id: '16', name: '組員A3', position: '會務組員', department: '會務部', level: 4, parentId: '7' },

        { id: '17', name: '組員B1', position: '會務組員', department: '會務部', level: 4, parentId: '8' },
        { id: '18', name: '組員B2', position: '會務組員', department: '會務部', level: 4, parentId: '8' },

        { id: '19', name: '組員A1', position: '保險組員', department: '保險部', level: 4, parentId: '9' },
        { id: '20', name: '組員A2', position: '保險組員', department: '保險部', level: 4, parentId: '9' },
        { id: '21', name: '組員A3', position: '保險組員', department: '保險部', level: 4, parentId: '9' },

        { id: '22', name: '組員B1', position: '保險組員', department: '保險部', level: 4, parentId: '10' },
        { id: '23', name: '組員B2', position: '保險組員', department: '保險部', level: 4, parentId: '10' },

        { id: '24', name: '組員A1', position: '信用組員', department: '信用部', level: 4, parentId: '11' },
        { id: '25', name: '組員A2', position: '信用組員', department: '信用部', level: 4, parentId: '11' },
        { id: '26', name: '組員A3', position: '信用組員', department: '信用部', level: 4, parentId: '11' },

        { id: '27', name: '組員A1', position: '供銷組員', department: '供銷部', level: 4, parentId: '12' },
        { id: '28', name: '組員A2', position: '供銷組員', department: '供銷部', level: 4, parentId: '12' },

        { id: '29', name: '組員B1', position: '供銷組員', department: '供銷部', level: 4, parentId: '13' },
        { id: '30', name: '組員B2', position: '供銷組員', department: '供銷部', level: 4, parentId: '13' },
        { id: '31', name: '組員B3', position: '供銷組員', department: '供銷部', level: 4, parentId: '13' },

        { id: '32', name: '組員B4', position: '供銷組員', department: '供銷部', level: 5, parentId: '31' }
    ];

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState<TreeNode | null>(null);
    const [zoomLevel, setZoomLevel] = useState(1);
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [scrollStart, setScrollStart] = useState({ x: 0, y: 0 });
    const chartContainerRef = useRef<HTMLDivElement>(null);

    // 滑鼠滾輪縮放控制
    const handleWheel = (event: React.WheelEvent) => {
        // 檢查是否按住 Shift 鍵，只有在按住 Shift 時才進行縮放
        if (event.shiftKey) {
            event.preventDefault();
            event.stopPropagation();
            const delta = event.deltaY > 0 ? -0.1 : 0.1;
            setZoomLevel(prev => Math.max(0.3, Math.min(3, prev + delta)));
        }
    };

    // 滑鼠拖拽滾動控制
    const handleMouseDown = (event: React.MouseEvent) => {
        const target = event.target as HTMLElement;

        // 檢查是否點擊在員工卡片的互動元素上
        if (target.classList.contains('position-table-employee-card-background') ||
            target.classList.contains('position-table-avatar-background') ||
            target.classList.contains('position-table-avatar-text') ||
            target.tagName === 'text' ||
            (target.tagName === 'circle' && target.getAttribute('r') === '6') ||
            target.closest('.position-table-employee-node')) {
            return; // 如果點擊在員工卡片上，不啟動拖拽
        }

        if (event.button === 0) { // 只響應左鍵
            setIsDragging(true);
            setDragStart({ x: event.clientX, y: event.clientY });
            if (chartContainerRef.current) {
                setScrollStart({
                    x: chartContainerRef.current.scrollLeft,
                    y: chartContainerRef.current.scrollTop
                });
            }
            event.preventDefault();
            event.stopPropagation();
        }
    };

    const handleMouseMove = (event: React.MouseEvent) => {
        if (isDragging && chartContainerRef.current) {
            const deltaX = event.clientX - dragStart.x;
            const deltaY = event.clientY - dragStart.y;

            const newScrollLeft = Math.max(0, Math.min(
                chartContainerRef.current.scrollWidth - chartContainerRef.current.clientWidth,
                scrollStart.x - deltaX
            ));
            const newScrollTop = Math.max(0, Math.min(
                chartContainerRef.current.scrollHeight - chartContainerRef.current.clientHeight,
                scrollStart.y - deltaY
            ));

            const container = chartContainerRef.current;
            container.scrollLeft = newScrollLeft;
            container.scrollTop = newScrollTop;

            event.preventDefault();
        }
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    const handleMouseLeave = () => {
        setIsDragging(false);
    };

    // 列印功能
    const handlePrint = () => {
        if (svgRef.current) {
            const printWindow = window.open('', '_blank');
            if (printWindow) {
                const svgElement = svgRef.current;
                const svgData = new XMLSerializer().serializeToString(svgElement);

                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>組織架構圖</title>
                        <style>
                            body { margin: 0; padding: 20px; }
                            svg { max-width: 100%; height: auto; }
                            @media print {
                                body { margin: 0; }
                                svg { width: 100% !important; height: auto !important; }
                            }
                        </style>
                    </head>
                    <body>
                        ${svgData}
                    </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
            }
        }
    };

    // 轉換為 PDF (先暫時簡化，下載為 SVG)
    const handleExportPDF = () => {
        if (!svgRef.current) return;

        try {
            const svgElement = svgRef.current;
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const blob = new Blob([svgData], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = '組織架構圖.svg';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            message.success('SVG 檔案已下載！');
        } catch (error) {
            message.error('檔案下載失敗，請稍後再試。');
            console.error('SVG export error:', error);
        }
    };

    // 將扁平數據轉換為樹狀結構
    const buildTreeFromFlatData = (data: Employee[]): TreeNode | null => {
        if (!data.length) return null;

        const idMap = new Map<string, TreeNode>();

        // 將所有員工轉換為TreeNode並建立id映射
        data.forEach(emp => {
            const node: TreeNode = {
                id: emp.id,
                name: emp.name,
                position: emp.position,
                department: emp.department,
                level: emp.level,
                phone: emp.phone,
                email: emp.email,
                parentId: emp.parentId,
                photoUrl: emp.photoUrl,
                profileUrl: emp.profileUrl,
                depth: 0,
                children: [],
                collapsed: false
            };
            idMap.set(emp.id, node);
        });

        let root: TreeNode | null = null;

        // 建立父子關係
        data.forEach(emp => {
            const node = idMap.get(emp.id)!;

            if (emp.parentId) {
                const parent = idMap.get(emp.parentId);
                if (parent) {
                    parent.children = parent.children || [];
                    parent.children.push(node);
                    node.parent = parent;
                    node.depth = parent.depth + 1;
                }
            } else {
                root = node; // 沒有父節點的是根節點
            }
        });

        return root;
    };

    // 計算節點位置（改進版樹布局算法）
    const calculatePositions = (root: TreeNode) => {
        if (!root) return;

        // 先計算每個節點需要的寬度（子樹寬度）
        const calculateSubtreeWidth = (node: TreeNode): number => {
            if (!node.children || node.collapsed || node.children.length === 0) {
                return config.cardWidth + config.nodeHorizontalSpacing;
            }

            const childrenWidth = node.children.reduce((sum, child) => {
                return sum + calculateSubtreeWidth(child);
            }, 0);

            return Math.max(config.cardWidth + config.nodeHorizontalSpacing, childrenWidth);
        };

        // 遞歸設置位置（改進版）
        const positionNodes = (node: TreeNode, startX: number, y: number): number => {
            node.y = y;

            if (!node.children || node.collapsed || node.children.length === 0) {
                node.x = startX + config.cardWidth / 2;
                return startX + config.cardWidth + config.nodeHorizontalSpacing;
            }

            // 按部門分組並排序
            const groupedChildren = groupChildrenByDepartment(node.children);
            const departmentKeys = Object.keys(groupedChildren).sort();

            let currentX = startX;
            const childPositions: number[] = [];

            // 為每個部門組分配位置，確保部門間有適當間距
            departmentKeys.forEach((dept, deptIndex) => {
                const departmentChildren = groupedChildren[dept];

                // 為同部門的員工預留額外空間
                departmentChildren.forEach((child, childIndex) => {
                    currentX = positionNodes(child, currentX, y + config.nodeVerticalSpacing);
                    childPositions.push(child.x!);
                });

                // 部門間加大間距（除了最後一個部門）
                if (deptIndex < departmentKeys.length - 1) {
                    currentX += config.nodeHorizontalSpacing * 0.3;
                }
            });

            // 父節點位置設在所有子節點的中心
            if (childPositions.length > 0) {
                const minX = Math.min(...childPositions);
                const maxX = Math.max(...childPositions);
                node.x = (minX + maxX) / 2;
            } else {
                node.x = startX + config.cardWidth / 2;
            }

            return currentX;
        };

        // 開始計算位置
        positionNodes(root, config.paddingLeft, config.paddingTop);
    };

    // 按部門分組子節點（解決重複問題）
    const groupChildrenByDepartment = (children: TreeNode[]): Record<string, TreeNode[]> => {
        return children.reduce((groups, child) => {
            const dept = child.department;
            if (!groups[dept]) {
                groups[dept] = [];
            }
            groups[dept].push(child);
            return groups;
        }, {} as Record<string, TreeNode[]>);
    };

    // 計算實際需要的 SVG 尺寸
    const calculateSVGDimensions = (nodes: TreeNode[]): { width: number; height: number } => {
        if (nodes.length === 0) {
            return {
                width: config.paddingLeft + config.paddingRight + config.cardWidth,
                height: config.paddingTop + config.paddingBottom + config.cardHeight
            };
        }

        const positions = nodes.map(node => ({
            x: (node.x || 0),
            y: (node.y || 0)
        }));

        const minX = Math.min(...positions.map(p => p.x)) - config.cardWidth / 2;
        const maxX = Math.max(...positions.map(p => p.x)) + config.cardWidth / 2;
        const minY = Math.min(...positions.map(p => p.y));
        const maxY = Math.max(...positions.map(p => p.y)) + config.cardHeight;

        const calculatedWidth = maxX - minX + config.paddingLeft + config.paddingRight;
        const calculatedHeight = maxY - minY + config.paddingTop + config.paddingBottom;

        return {
            width: Math.max(1200, calculatedWidth),
            height: Math.max(600, calculatedHeight)
        };
    };

    // 獲取可見節點（用於渲染）
    const getVisibleNodes = (root: TreeNode): TreeNode[] => {
        if (!root) return [];

        const visible: TreeNode[] = [];

        const traverse = (node: TreeNode) => {
            visible.push(node);
            if (node.children && !node.collapsed) {
                node.children.forEach(child => traverse(child));
            }
        };

        traverse(root);
        return visible;
    };

    // 獲取連接線數據
    const getLinks = (nodes: TreeNode[]): { source: TreeNode; target: TreeNode }[] => {
        const links: { source: TreeNode; target: TreeNode }[] = [];

        nodes.forEach(node => {
            if (node.children && !node.collapsed) {
                node.children.forEach(child => {
                    links.push({ source: node, target: child });
                });
            }
        });

        return links;
    };

    // 切換節點展開/收合狀態
    const toggleNode = (nodeId: string) => {
        if (!treeData) return;

        const findAndToggle = (node: TreeNode): boolean => {
            if (node.id === nodeId) {
                if (node.children && node.children.length > 0) {
                    node.collapsed = !node.collapsed;
                    return true;
                }
                return false;
            }

            if (node.children && !node.collapsed) {
                return node.children.some(child => findAndToggle(child));
            }

            return false;
        };

        if (findAndToggle(treeData)) {
            // 重新計算位置和可見節點
            calculatePositions(treeData);
            setVisibleNodes(getVisibleNodes(treeData));
            setTreeData({ ...treeData }); // 強制更新
        }
    };

    // 處理全域滑鼠事件
    useEffect(() => {
        const handleGlobalMouseMove = (event: MouseEvent) => {
            if (isDragging && chartContainerRef.current) {
                const deltaX = event.clientX - dragStart.x;
                const deltaY = event.clientY - dragStart.y;

                const newScrollLeft = Math.max(0, Math.min(
                    chartContainerRef.current.scrollWidth - chartContainerRef.current.clientWidth,
                    scrollStart.x - deltaX
                ));
                const newScrollTop = Math.max(0, Math.min(
                    chartContainerRef.current.scrollHeight - chartContainerRef.current.clientHeight,
                    scrollStart.y - deltaY
                ));

                // 確保滾動生效
                const container = chartContainerRef.current;

                container.scrollTo({
                    left: newScrollLeft,
                    top: newScrollTop,
                    behavior: 'auto'
                });

                container.scrollLeft = newScrollLeft;
                container.scrollTop = newScrollTop;

                requestAnimationFrame(() => {
                    if (container.scrollLeft !== newScrollLeft) {
                        container.scrollLeft = newScrollLeft;
                    }
                    if (container.scrollTop !== newScrollTop) {
                        container.scrollTop = newScrollTop;
                    }
                });

                event.preventDefault();
            }
        };

        const handleGlobalMouseUp = () => {
            setIsDragging(false);
        };

        if (isDragging) {
            document.addEventListener('mousemove', handleGlobalMouseMove);
            document.addEventListener('mouseup', handleGlobalMouseUp);
            document.addEventListener('mouseleave', handleGlobalMouseUp);

            // 防止選取文字
            document.body.style.userSelect = 'none';
        } else {
            document.body.style.userSelect = '';
        }

        return () => {
            document.removeEventListener('mousemove', handleGlobalMouseMove);
            document.removeEventListener('mouseup', handleGlobalMouseUp);
            document.removeEventListener('mouseleave', handleGlobalMouseUp);
            document.body.style.userSelect = '';
        };
    }, [isDragging, dragStart.x, dragStart.y, scrollStart.x, scrollStart.y]);

    // 初始化樹狀結構
    useEffect(() => {
        const tree = buildTreeFromFlatData(employeeData);
        if (tree) {
            calculatePositions(tree);
            setTreeData(tree);
            setVisibleNodes(getVisibleNodes(tree));
        }
    }, []);

    const handleViewDetails = (node: TreeNode) => {
        setSelectedEmployee(node);
        setIsModalVisible(true);
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
        setSelectedEmployee(null);
    };

    // 渲染員工卡片（參考D3版本的設計）
    const renderEmployeeCard = (node: TreeNode) => {
        const hasChildren = node.children && node.children.length > 0;
        const isCollapsed = node.collapsed;

        return (
            <g key={node.id} className="position-table-employee-node" transform={`translate(${(node.x || 0) - config.cardWidth / 2}, ${node.y || 0})`}>
                {/* 卡片外框 */}
                <rect
                    width={config.cardWidth}
                    height={config.cardHeight}
                    rx={5}
                    ry={5}
                    fill={getCardColor(node.depth)}
                    stroke="#336699"
                    strokeWidth={2}
                    className="position-table-employee-card-background"
                    style={{ cursor: 'pointer' }}
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleViewDetails(node);
                    }}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                    }}
                />

                {/* 照片區域 - 威嚴風格頭像（調整為圓形更緊湊） */}
                <defs>
                    <linearGradient id={`avatar-gradient-${node.id}`} x1="0%" y1="0%" x2="100%" y2="100%">
                        {getAvatarStyle(node.position).backgroundColor.includes('gradient') ? (
                            <>
                                <stop offset="0%" stopColor={getAvatarStyle(node.position).backgroundColor.match(/#[0-9a-f]{6}/g)?.[0] || '#4c1d95'} />
                                <stop offset="100%" stopColor={getAvatarStyle(node.position).backgroundColor.match(/#[0-9a-f]{6}/g)?.[1] || '#7c3aed'} />
                            </>
                        ) : (
                            <stop offset="0%" stopColor={getAvatarStyle(node.position).backgroundColor} />
                        )}
                    </linearGradient>
                </defs>

                {/* 圓形頭像背景 */}
                <circle
                    cx={8 + config.photoWidth / 2}
                    cy={config.cardHeight / 2}
                    r={config.photoWidth / 2 - 2}
                    fill={getAvatarStyle(node.position).backgroundColor.includes('gradient') ?
                        `url(#avatar-gradient-${node.id})` :
                        getAvatarStyle(node.position).backgroundColor}
                    stroke={getAvatarStyle(node.position).border?.match(/#[0-9a-f]{6}/)?.[0] || '#e1e5e9'}
                    strokeWidth={getAvatarStyle(node.position).border ? parseInt(getAvatarStyle(node.position).border?.match(/\d+/)?.[0] || '1') : 1}
                    style={{
                        cursor: 'pointer',
                        filter: getAvatarStyle(node.position).boxShadow ? 'drop-shadow(1px 1px 3px rgba(0,0,0,0.2))' : 'none'
                    }}
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleViewDetails(node);
                    }}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                    }}
                    className="position-table-avatar-background"
                />

                {/* 文字頭像 - 顯示職位縮寫 */}
                <text
                    x={8 + config.photoWidth / 2}
                    y={config.cardHeight / 2}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize="16"
                    fontWeight="900"
                    fill={getAvatarStyle(node.position).color}
                    style={{
                        pointerEvents: 'none',
                        textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
                    }}
                    className="position-table-avatar-text"
                >
                    {getAvatarText(node.position)}
                </text>

                {/* 職位標籤（簡化顯示） */}
                <text
                    x={config.photoWidth + 15}
                    y={25}
                    fontSize="10"
                    fontWeight="bold"
                    fill="#2563eb"
                >
                    {node.position.length > 6 ? node.position.substring(0, 6) + '...' : node.position}
                </text>

                {/* 姓名 */}
                <text
                    x={config.photoWidth + 15}
                    y={40}
                    fontSize="11"
                    fontWeight="bold"
                    fill="#333"
                    style={{ cursor: 'pointer' }}
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleViewDetails(node);
                    }}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                    }}
                >
                    {node.name.length > 6 ? node.name.substring(0, 6) + '...' : node.name}
                </text>

                {/* 部門標籤（簡化） */}
                <text
                    x={config.photoWidth + 15}
                    y={55}
                    fontSize="9"
                    fill="#666"
                    style={{ cursor: 'pointer' }}
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleViewDetails(node);
                    }}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                    }}
                >
                    {node.department.replace('部', '').replace('總管理處', '總處').length > 5 ?
                        node.department.replace('部', '').replace('總管理處', '總處').substring(0, 5) + '...' :
                        node.department.replace('部', '').replace('總管理處', '總處')}
                </text>

                {/* 展開/收合按鈕（調整大小） */}
                {hasChildren && (
                    <>
                        <circle
                            cx={config.cardWidth - 8}
                            cy={config.cardHeight / 2}
                            r={6}
                            fill="#336699"
                            style={{ cursor: 'pointer' }}
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                toggleNode(node.id);
                            }}
                            onMouseDown={(e) => {
                                e.stopPropagation();
                            }}
                        />
                        <text
                            x={config.cardWidth - 8}
                            y={config.cardHeight / 2}
                            textAnchor="middle"
                            dominantBaseline="middle"
                            fontSize="10"
                            fontWeight="bold"
                            fill="white"
                            style={{ pointerEvents: 'none' }}
                        >
                            {isCollapsed ? '+' : '-'}
                        </text>
                    </>
                )}
            </g>
        );
    };

    // 根據深度獲取卡片顏色
    const getCardColor = (depth: number): string => {
        const colors = ['#ffffff', '#f9f9ff', '#f0f0ff', '#e8e8ff', '#e0e0ff', '#d8d8ff'];
        return colors[Math.min(depth, colors.length - 1)];
    };

    // 根據職位獲取頭像文字
    const getAvatarText = (position: string): string => {
        const textMap: Record<string, string> = {
            '總幹事': '總',
            '秘書': '秘',
            '會務主任': '會',
            '保險主任': '保',
            '信用主任': '信',
            '供銷主任': '供',
            '會務組長': '會',
            '保險組長': '保',
            '信用組長': '信',
            '供銷組長': '供',
            '會務組員': '會',
            '保險組員': '保',
            '信用組員': '信',
            '供銷組員': '供'
        };
        return textMap[position] || position.charAt(0);
    };

    // 根據職位獲取頭像背景顏色和樣式
    const getAvatarStyle = (position: string): { backgroundColor: string; color: string; border?: string; boxShadow?: string } => {
        const styleMap: Record<string, { backgroundColor: string; color: string; border?: string; boxShadow?: string }> = {
            '總幹事': {
                backgroundColor: 'linear-gradient(135deg, #4c1d95 0%, #7c3aed 100%)',
                color: '#ffffff',
                border: '3px solid #fbbf24',
                boxShadow: '0 4px 20px rgba(124, 58, 237, 0.4)'
            },
            '秘書': {
                backgroundColor: 'linear-gradient(135deg, #dc2626 0%, #f59e0b 100%)',
                color: '#ffffff',
                border: '2px solid #fbbf24',
                boxShadow: '0 3px 15px rgba(245, 158, 11, 0.3)'
            },
            '會務主任': {
                backgroundColor: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                color: '#ffffff',
                border: '2px solid #60a5fa',
                boxShadow: '0 3px 15px rgba(59, 130, 246, 0.3)'
            },
            '保險主任': {
                backgroundColor: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                color: '#ffffff',
                border: '2px solid #60a5fa',
                boxShadow: '0 3px 15px rgba(59, 130, 246, 0.3)'
            },
            '信用主任': {
                backgroundColor: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                color: '#ffffff',
                border: '2px solid #60a5fa',
                boxShadow: '0 3px 15px rgba(59, 130, 246, 0.3)'
            },
            '供銷主任': {
                backgroundColor: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                color: '#ffffff',
                border: '2px solid #60a5fa',
                boxShadow: '0 3px 15px rgba(59, 130, 246, 0.3)'
            },
            '會務組長': {
                backgroundColor: '#10b981',
                color: '#ffffff',
                boxShadow: '0 2px 10px rgba(16, 185, 129, 0.2)'
            },
            '保險組長': {
                backgroundColor: '#10b981',
                color: '#ffffff',
                boxShadow: '0 2px 10px rgba(16, 185, 129, 0.2)'
            },
            '信用組長': {
                backgroundColor: '#10b981',
                color: '#ffffff',
                boxShadow: '0 2px 10px rgba(16, 185, 129, 0.2)'
            },
            '供銷組長': {
                backgroundColor: '#10b981',
                color: '#ffffff',
                boxShadow: '0 2px 10px rgba(16, 185, 129, 0.2)'
            }
        };

        // 所有組員使用統一的灰色樣式
        if (position.includes('組員')) {
            return {
                backgroundColor: '#6b7280',
                color: '#ffffff',
                boxShadow: '0 1px 5px rgba(107, 114, 128, 0.1)'
            };
        }

        return styleMap[position] || { backgroundColor: '#6b7280', color: '#ffffff' };
    };

    // 生成連接線路徑（改進版，支援部門分組）
    const generateLinkPath = (source: TreeNode, target: TreeNode): string => {
        const sourceX = source.x || 0;
        const sourceY = (source.y || 0) + config.cardHeight;
        const targetX = target.x || 0;
        const targetY = target.y || 0;

        // 計算連接點
        const verticalGap = targetY - sourceY;
        const midY = sourceY + verticalGap / 2;

        // 生成更優美的階梯式路徑
        if (Math.abs(sourceX - targetX) < 10) {
            // 幾乎垂直的連線：直接連接
            return `M${sourceX},${sourceY} L${targetX},${targetY}`;
        } else {
            // 階梯式連線：父節點 -> 中間點 -> 子節點
            return `M${sourceX},${sourceY} L${sourceX},${midY} L${targetX},${midY} L${targetX},${targetY}`;
        }
    };

    // 主要渲染函數
    const renderOrgChart = () => {
        if (!treeData || visibleNodes.length === 0) {
            return (
                <div className="loading-container">
                    <div>載入組織架構中...</div>
                </div>
            );
        }

        const links = getLinks(visibleNodes);
        const svgDimensions = calculateSVGDimensions(visibleNodes);

        return (
            <div
                className={`org-chart-container ${isDragging ? 'dragging' : ''}`}
                ref={chartContainerRef}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
                style={{
                    cursor: isDragging ? 'grabbing' : 'grab',
                    userSelect: 'none'
                }}
            >
                <svg
                    ref={svgRef}
                    width={svgDimensions.width * zoomLevel}
                    height={svgDimensions.height * zoomLevel}
                    viewBox={`0 0 ${svgDimensions.width} ${svgDimensions.height}`}
                    className="org-chart-svg"
                >
                    {/* 背景區域 - 用於拖曳 */}
                    <rect
                        width="100%"
                        height="100%"
                        fill="transparent"
                        style={{ cursor: 'inherit' }}
                    />

                    {/* 連接線圖層 */}
                    <g className="links-layer">
                        {links.map((link, index) => (
                            <path
                                key={`link-${link.source.id}-${link.target.id}-${index}`}
                                d={generateLinkPath(link.source, link.target)}
                                fill="none"
                                stroke="#555"
                                strokeWidth={1.5}
                                style={{ pointerEvents: 'none' }}
                            />
                        ))}
                    </g>

                    {/* 節點圖層 */}
                    <g className="nodes-layer">
                        {visibleNodes.map(node => renderEmployeeCard(node))}
                    </g>
                </svg>
            </div>
        );
    };

    return (
        <div className="position-table-container">
            <div className="header-section">
                <div className="header-content">
                    <div className="header-text">
                        <h2>職務定位表</h2>
                        <p>組織架構圖 - 顯示各部門職務層級關係</p>
                    </div>
                    <div className="header-actions">
                        <Space>
                            <Button
                                type="default"
                                icon={<PrinterOutlined />}
                                onClick={handlePrint}
                            >
                                列印
                            </Button>
                            <Button
                                type="primary"
                                icon={<FilePdfOutlined />}
                                onClick={handleExportPDF}
                            >
                                下載 SVG
                            </Button>
                        </Space>
                        <div className="zoom-controls">
                            <div className="zoom-buttons">
                                <button
                                    className="zoom-btn"
                                    onClick={() => setZoomLevel(prev => Math.max(0.3, prev - 0.2))}
                                    disabled={zoomLevel <= 0.3}
                                >
                                    －
                                </button>
                                <span className="zoom-info">
                                    {Math.round(zoomLevel * 100)}%
                                </span>
                                <button
                                    className="zoom-btn"
                                    onClick={() => setZoomLevel(prev => Math.min(3, prev + 0.2))}
                                    disabled={zoomLevel >= 3}
                                >
                                    ＋
                                </button>
                            </div>
                            <div className="zoom-hint">
                                Shift + 滾輪也可縮放
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="org-chart">
                <div className="chart-zoom-area" onWheel={handleWheel}>
                    {renderOrgChart()}
                    {zoomLevel !== 1 && (
                        <div className="zoom-reset-hint">
                            <button
                                className="zoom-reset-btn"
                                onClick={() => setZoomLevel(1)}
                            >
                                重置縮放 (100%)
                            </button>
                        </div>
                    )}
                </div>
            </div>

            <Modal
                title="員工詳細資料"
                open={isModalVisible}
                onCancel={handleModalCancel}
                footer={[
                    <Button key="close" onClick={handleModalCancel}>
                        關閉
                    </Button>
                ]}
                width={600}
            >
                {selectedEmployee && (
                    <div className="employee-details">
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '24px', padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}>
                            {/* 頭像區域 */}
                            <div
                                style={{
                                    width: '80px',
                                    height: '80px',
                                    borderRadius: '8px',
                                    background: getAvatarStyle(selectedEmployee.position).backgroundColor,
                                    border: getAvatarStyle(selectedEmployee.position).border || '2px solid #e1e5e9',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginRight: '20px',
                                    fontSize: '32px',
                                    fontWeight: '900',
                                    color: getAvatarStyle(selectedEmployee.position).color,
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
                                }}
                            >
                                {getAvatarText(selectedEmployee.position)}
                            </div>

                            {/* 基本資訊 */}
                            <div>
                                <h3 style={{ margin: '0 0 8px 0', fontSize: '24px', color: '#333' }}>
                                    {selectedEmployee.name}
                                </h3>
                                <p style={{ margin: '0', fontSize: '16px', color: '#666' }}>
                                    {selectedEmployee.position}
                                </p>
                            </div>
                        </div>

                        {/* 詳細資訊列表 */}
                        <div className="detail-items">
                            <div className="detail-item">
                                <span className="detail-label">員工編號：</span>
                                <span className="detail-value">{selectedEmployee.id}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">姓名：</span>
                                <span className="detail-value">{selectedEmployee.name}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">職位：</span>
                                <span className="detail-value">{selectedEmployee.position}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">部門：</span>
                                <span className="detail-value">{selectedEmployee.department}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">職級：</span>
                                <span className="detail-value">第 {selectedEmployee.level} 層</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">電話：</span>
                                <span className="detail-value">{selectedEmployee.phone || '尚未設定'}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">電子郵件：</span>
                                <span className="detail-value">{selectedEmployee.email || '尚未設定'}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">下屬人數：</span>
                                <span className="detail-value">{selectedEmployee.children?.length || 0} 人</span>
                            </div>
                        </div>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default PositionTableManagement; 