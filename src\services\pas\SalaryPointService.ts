import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 薪點金額資料型別
export interface SalaryPoint {
    uid: string;
    pointLevel: string;
    amount: number;
    effectiveDate: string; // timestamp 格式字串
    adjustmentReason: string | null;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// 建立空的薪點資料
export const createEmptySalaryPoint = (): SalaryPoint => ({
    uid: '',
    pointLevel: '',
    amount: 0,
    effectiveDate: '',
    adjustmentReason: '',
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false,
});

// 取得薪點資料列表
export async function getSalaryPointList(): Promise<ApiResponse<SalaryPoint[]>> {
    return await httpClient(apiEndpoints.getSalaryPointList, { method: "GET" });
}

// 取得薪點資料明細
export async function getSalaryPointDetail(uid: string): Promise<ApiResponse<SalaryPoint>> {
    return await httpClient(`${apiEndpoints.getSalaryPointDetail}/${uid}`, { method: "GET" });
}

// 新增薪點資料
export async function addSalaryPoint(data: Partial<SalaryPoint>): Promise<ApiResponse> {
    try {
        return await httpClient(apiEndpoints.addSalaryPoint, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
    } catch (error: any) {
        return { success: false, message: error.message || "新增薪點資料失敗" };
    }
}

// 編輯薪點資料
export async function editSalaryPoint(data: Partial<SalaryPoint>): Promise<ApiResponse> {
    try {
        return await httpClient(apiEndpoints.editSalaryPoint, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
    } catch (error: any) {
        return { success: false, message: error.message || "編輯薪點資料失敗" };
    }
}

// 刪除薪點資料
export async function deleteSalaryPoint(uid: string): Promise<ApiResponse> {
    try {
        return await httpClient(apiEndpoints.deleteSalaryPoint, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
    } catch (error: any) {
        return { success: false, message: error.message || "刪除薪點資料失敗" };
    }
}

// 取得指定日期當時薪點金額
export async function getAmountByDate(dateStr: string): Promise<ApiResponse<number | null>> {
    return await httpClient(`${apiEndpoints.getSalaryPointAmountByDate}?dateStr=${encodeURIComponent(dateStr)}`, {
        method: "GET",
    });
}
