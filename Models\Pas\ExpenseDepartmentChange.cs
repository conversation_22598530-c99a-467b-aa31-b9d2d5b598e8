using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 開支部門異動資料表
    /// </summary>
    public class ExpenseDepartmentChange : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string Uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用者編號

        [Comment("開支部門編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ExpenseDepartmentId { get; set; } // 開支部門編號

        [Comment("異動日期")]
        [Column(TypeName = "bigint")]
        public long? ChangeDate { get; set; } // 異動日期

        [Comment("生效日期")]
        [Column(TypeName = "bigint")]
        public long? EffectiveDate { get; set; } // 生效日期

        [Comment("異動原因")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string ChangeReason { get; set; } // 異動原因

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Remark { get; set; } // 備註

        public ExpenseDepartmentChange()
        {
            Uid = "";
            UserId = "";
            ExpenseDepartmentId = "";
            ChangeDate = null;
            EffectiveDate = null;
            ChangeReason = "";
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class ExpenseDepartmentChangeDTO : ModelBaseEntityDTO
    {
        public string Uid { get; set; } // 資料編號
        public string UserId { get; set; } // 使用者編號
        public string ExpenseDepartmentId { get; set; } // 開支部門編號
        public string ExpenseDepartmentName { get; set; } // 開支部門名稱
        public string ChangeDate { get; set; } // 異動日期
        public string EffectiveDate { get; set; } // 生效日期
        public string ChangeReason { get; set; } // 異動原因
        public string Remark { get; set; } // 備註

        public ExpenseDepartmentChangeDTO()
        {
            Uid = "";
            UserId = "";
            ExpenseDepartmentId = "";
            ExpenseDepartmentName = "";
            ChangeDate = "";
            EffectiveDate = "";
            ChangeReason = "";
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}