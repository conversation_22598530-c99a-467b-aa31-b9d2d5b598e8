{"name": "fast_erp_frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "next dev --experimental-https", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/cssinjs": "^1.18.4", "@ant-design/icons": "^6.0.0", "@microsoft/signalr": "^8.0.7", "antd": "^5.26.2", "antd-mask-input": "^2.0.7", "axios": "^1.7.9", "axios-auth-refresh": "^3.3.6", "mitt": "^3.0.1", "next": "^14.2.29", "qrcode.react": "^4.2.0", "react": "^18", "react-barcode": "^1.6.1", "react-dom": "^18", "react-grid-layout": "^1.5.1", "react-resizable": "^3.0.5", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "signalr": "^2.4.3", "sweetalert2": "^11.6.13", "xlsx": "^0.18.5", "zustand": "^4.5.0"}, "devDependencies": {"@types/node": "^20", "@types/qrcode.react": "^3.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@types/react-grid-layout": "^1.3.5", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}