"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Table,
  Modal,
  message,
  Card,
  Typography,
  Tag,
  Tooltip,
  Divider,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  SwapOutlined,
  SaveOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { getCookie } from "@/utils/cookies";

// 引入服務
import {
  createAssetLocationTransfer,
  updateAssetLocationTransfer,
  generateTransferNo,
  getAssetCurrentLocation,
  validateAssetForTransfer,
} from "@/services/pms/assetLocationTransferService";

import { getAssets } from "@/services/pms/assetService";
import { getDepartments } from "@/services/common/departmentService";
import { getDivisions } from "@/services/common/divisionService";
import { getUsers } from "@/services/common/userService";
import { getStorageLocations } from "@/services/pms/storageLocationService";

// 引入配置和介面
import {
  AssetLocationTransferWithDetails,
  AssetLocationTransferDetail,
  FormProps,
  formInitialValues,
  detailInitialValues,
  CHANGE_ITEMS,
  CHANGE_ITEMS_MAPPING,
} from "./interface";

import { FORM_LAYOUT, FORM_RULES, MESSAGES } from "./config";
import { getDetailTableColumns } from "./columns";
import styles from "./page.module.css";

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 表單組件
const TransferForm: React.FC<FormProps> = ({
  editingTransfer,
  isViewMode,
  onCancel,
  onSuccess,
  form,
}) => {
  // 狀態管理
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [details, setDetails] = useState<AssetLocationTransferDetail[]>([]);
  const [assetSelectionVisible, setAssetSelectionVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingDetail, setEditingDetail] =
    useState<AssetLocationTransferDetail | null>(null);

  // 選項數據
  const [departments, setDepartments] = useState<any[]>([]);
  const [divisions, setDivisions] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [storageLocations, setStorageLocations] = useState<any[]>([]);
  const [assets, setAssets] = useState<any[]>([]);

  // 表單實例
  const [detailForm] = Form.useForm();
  const [assetSearchForm] = Form.useForm();

  // 當前用戶資訊
  const currentUserId = getCookie("userId") || "";
  const currentUserName = getCookie("userName") || "";

  // 載入基礎數據
  const loadBaseData = useCallback(async () => {
    setLoading(true);
    try {
      const [departmentsRes, divisionsRes, usersRes, locationsRes, assetsRes] =
        await Promise.all([
          getDepartments(),
          getDivisions(),
          getUsers(),
          getStorageLocations(),
          getAssets(),
        ]);

      if (departmentsRes.success) setDepartments(departmentsRes.data || []);
      if (divisionsRes.success) setDivisions(divisionsRes.data || []);
      if (usersRes.success) setUsers(usersRes.data || []);
      if (locationsRes.success) setStorageLocations(locationsRes.data || []);
      if (assetsRes.success)
        setAssets(assetsRes.data?.map((item) => item.asset) || []);
    } catch (error) {
      console.error("載入基礎數據失敗:", error);
      message.error("載入基礎數據失敗");
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化表單
  const initializeForm = useCallback(async () => {
    if (editingTransfer) {
      // 編輯模式
      const { transfer, details: transferDetails } = editingTransfer;
      form.setFieldsValue({
        ...transfer,
        transferDate: transfer.transferDate
          ? dayjs(transfer.transferDate)
          : dayjs(),
      });
      setDetails(transferDetails || []);
    } else {
      // 新增模式
      try {
        const transferNoRes = await generateTransferNo(Date.now());
        form.setFieldsValue({
          ...formInitialValues,
          transferNo: transferNoRes.success ? transferNoRes.data : "",
          transferDate: dayjs(),
          applicantId: currentUserId,
          applicantName: currentUserName,
        });
        setDetails([]);
      } catch (error) {
        console.error("初始化表單失敗:", error);
      }
    }
  }, [editingTransfer, form, currentUserId, currentUserName]);

  // 初始載入
  useEffect(() => {
    loadBaseData();
    initializeForm();
  }, [loadBaseData, initializeForm]);

  // 處理申請人變更
  const handleApplicantChange = (userId: string) => {
    const user = users.find((u) => u.userId === userId);
    if (user) {
      form.setFieldsValue({
        applicantName: user.userName,
        applicantDepartmentId: user.departmentId,
        applicantDepartmentName: user.departmentName,
      });
    }
  };

  // 添加變動明細
  const handleAddDetail = () => {
    setEditingDetail(null);
    detailForm.resetFields();
    setDetailModalVisible(true);
  };

  // 編輯變動明細
  const handleEditDetail = (detail: AssetLocationTransferDetail) => {
    setEditingDetail(detail);
    detailForm.setFieldsValue(detail);
    setDetailModalVisible(true);
  };

  // 刪除變動明細
  const handleDeleteDetail = (detail: AssetLocationTransferDetail) => {
    Modal.confirm({
      title: "確定要刪除此變動明細嗎？",
      content: `財產：${detail.assetName}`,
      onOk: () => {
        setDetails((prev) =>
          prev.filter((d) => d.detailId !== detail.detailId)
        );
        message.success("刪除成功");
      },
    });
  };

  // 選擇財產
  const handleSelectAsset = async (assetId: string) => {
    try {
      const asset = assets.find((a) => a.assetId === assetId);
      if (!asset) {
        message.error("未找到財產資訊");
        return;
      }

      // 驗證財產是否可變動
      const validateRes = await validateAssetForTransfer(asset.assetNo);
      if (!validateRes.success) {
        message.error(
          `財產 ${asset.assetNo} 無法進行位置變動：${validateRes.message}`
        );
        return;
      }

      // 獲取當前位置資訊
      const locationRes = await getAssetCurrentLocation(asset.assetNo);
      if (!locationRes.success) {
        message.error("獲取財產當前位置失敗");
        return;
      }

      const currentLocation = locationRes.data;

      // 檢查 currentLocation 是否存在
      if (!currentLocation) {
        message.error("無法獲取財產當前位置資訊");
        return;
      }

      // 設置表單值
      detailForm.setFieldsValue({
        assetId: asset.assetId,
        assetNo: asset.assetNo,
        assetName: asset.assetName,
        originalLocationId: currentLocation.currentLocationId,
        originalLocationName: currentLocation.currentLocationName,
        originalCustodianId: currentLocation.currentCustodianId,
        originalCustodianName: currentLocation.currentCustodianName,
        originalUserId: currentLocation.currentUserId,
        originalUserName: currentLocation.currentUserName,
        originalDepartmentId: currentLocation.currentDepartmentId,
        originalDepartmentName: currentLocation.currentDepartmentName,
        originalDivisionId: currentLocation.currentDivisionId,
        originalDivisionName: currentLocation.currentDivisionName,
      });
    } catch (error) {
      console.error("選擇財產失敗:", error);
      message.error("選擇財產失敗");
    }
  };

  // 處理變動項目變更
  const handleChangeItemsChange = (items: string[]) => {
    const changeItems = items.join(",");
    detailForm.setFieldsValue({ changeItems });

    // 根據變動項目自動更新對應欄位
    const formValues = detailForm.getFieldsValue();
    const updateFields: any = {};

    items.forEach((item) => {
      switch (item) {
        case CHANGE_ITEMS.LOCATION:
          // 位置變動時，清空新位置
          if (!updateFields.newLocationId) {
            updateFields.newLocationId = "";
            updateFields.newLocationName = "";
          }
          break;
        case CHANGE_ITEMS.CUSTODIAN:
          // 保管人變動時，清空新保管人
          if (!updateFields.newCustodianId) {
            updateFields.newCustodianId = "";
            updateFields.newCustodianName = "";
          }
          break;
        case CHANGE_ITEMS.USER:
          // 使用人變動時，清空新使用人
          if (!updateFields.newUserId) {
            updateFields.newUserId = "";
            updateFields.newUserName = "";
          }
          break;
        case CHANGE_ITEMS.DEPARTMENT:
          // 部門變動時，清空新部門
          if (!updateFields.newDepartmentId) {
            updateFields.newDepartmentId = "";
            updateFields.newDepartmentName = "";
          }
          break;
        case CHANGE_ITEMS.DIVISION:
          // 股別變動時，清空新股別
          if (!updateFields.newDivisionId) {
            updateFields.newDivisionId = "";
            updateFields.newDivisionName = "";
          }
          break;
      }
    });

    detailForm.setFieldsValue(updateFields);
  };

  // 處理新位置變更
  const handleNewLocationChange = (locationId: string) => {
    const location = storageLocations.find(
      (l) => l.storageLocationId === locationId
    );
    if (location) {
      detailForm.setFieldsValue({
        newLocationName: location.locationName,
      });
    }
  };

  // 處理新保管人變更
  const handleNewCustodianChange = (userId: string) => {
    const user = users.find((u) => u.userId === userId);
    if (user) {
      detailForm.setFieldsValue({
        newCustodianName: user.userName,
      });
    }
  };

  // 處理新使用人變更
  const handleNewUserChange = (userId: string) => {
    const user = users.find((u) => u.userId === userId);
    if (user) {
      detailForm.setFieldsValue({
        newUserName: user.userName,
      });
    }
  };

  // 處理新部門變更
  const handleNewDepartmentChange = (departmentId: string) => {
    const department = departments.find((d) => d.departmentId === departmentId);
    if (department) {
      detailForm.setFieldsValue({
        newDepartmentName: department.departmentName,
      });
    }
  };

  // 處理新股別變更
  const handleNewDivisionChange = (divisionId: string) => {
    const division = divisions.find((d) => d.divisionId === divisionId);
    if (division) {
      detailForm.setFieldsValue({
        newDivisionName: division.divisionName,
      });
    }
  };

  // 儲存變動明細
  const handleSaveDetail = async () => {
    try {
      const values = await detailForm.validateFields();

      if (editingDetail) {
        // 編輯模式
        setDetails((prev) =>
          prev.map((d) =>
            d.detailId === editingDetail.detailId
              ? { ...editingDetail, ...values }
              : d
          )
        );
      } else {
        // 新增模式
        const newDetail: AssetLocationTransferDetail = {
          ...detailInitialValues,
          detailId: `temp_${Date.now()}`,
          ...values,
        } as AssetLocationTransferDetail;

        setDetails((prev) => [...prev, newDetail]);
      }

      setDetailModalVisible(false);
      message.success(editingDetail ? "更新成功" : "新增成功");
    } catch (error) {
      console.error("儲存明細失敗:", error);
    }
  };

  // 表單提交
  const handleSubmit = async () => {
    try {
      setSubmitLoading(true);

      // 驗證主表單
      const values = await form.validateFields();

      // 驗證明細
      if (details.length === 0) {
        message.error("請至少新增一筆變動明細");
        return;
      }

      // 準備提交數據
      const submitData: AssetLocationTransferWithDetails = {
        transfer: {
          ...values,
          transferDate: values.transferDate
            ? dayjs(values.transferDate).valueOf()
            : Date.now(),
          createTime: Date.now(),
          createUserId: currentUserId,
          updateTime: Date.now(),
          updateUserId: currentUserId,
        },
        details: details.map((detail) => ({
          ...detail,
          createTime: Date.now(),
          createUserId: currentUserId,
          updateTime: Date.now(),
          updateUserId: currentUserId,
        })),
      };

      // 提交數據
      let response;
      if (editingTransfer) {
        response = await updateAssetLocationTransfer(
          values.transferNo,
          submitData
        );
      } else {
        response = await createAssetLocationTransfer(submitData);
      }

      if (response.success) {
        message.success(
          editingTransfer ? MESSAGES.SUCCESS.UPDATE : MESSAGES.SUCCESS.CREATE
        );
        onSuccess(submitData);
      } else {
        message.error(
          response.message ||
            (editingTransfer ? MESSAGES.ERROR.UPDATE : MESSAGES.ERROR.CREATE)
        );
      }
    } catch (error) {
      console.error("提交失敗:", error);
      message.error(
        editingTransfer ? MESSAGES.ERROR.UPDATE : MESSAGES.ERROR.CREATE
      );
    } finally {
      setSubmitLoading(false);
    }
  };

  // 明細表格欄位
  const detailColumns = getDetailTableColumns(
    handleEditDetail,
    handleDeleteDetail,
    isViewMode
  );

  return (
    <div className={styles.formContent}>
      <Form form={form} {...FORM_LAYOUT} disabled={isViewMode}>
        {/* 基本資訊 */}
        <div className={styles.formSection}>
          <Title level={5} className={styles.formSectionTitle}>
            基本資訊
          </Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="transferNo"
                label="變動單號"
                rules={[{ required: true, message: "請輸入變動單號" }]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="transferDate"
                label="變動日期"
                rules={FORM_RULES.transferDate}
              >
                <DatePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="applicantId"
                label="申請人"
                rules={FORM_RULES.applicantId}
              >
                <Select
                  placeholder="請選擇申請人"
                  showSearch
                  optionFilterProp="children"
                  onChange={handleApplicantChange}
                >
                  {users.map((user) => (
                    <Option key={user.userId} value={user.userId}>
                      {user.userName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="applicantDepartmentId" label="申請部門">
                <Select placeholder="請選擇部門" disabled>
                  {departments.map((dept) => (
                    <Option key={dept.departmentId} value={dept.departmentId}>
                      {dept.departmentName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="transferReason"
                label="變動原因"
                rules={FORM_RULES.transferReason}
              >
                <TextArea rows={4} placeholder="請輸入變動原因" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="notes" label="備註">
                <TextArea rows={3} placeholder="請輸入備註" />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* 變動明細 */}
        <div className={styles.formSection}>
          <div className={styles.detailTableHeader}>
            <Title level={5} className={styles.detailTableTitle}>
              變動明細
            </Title>
            {!isViewMode && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddDetail}
                className={styles.addDetailButton}
              >
                新增明細
              </Button>
            )}
          </div>

          <Table
            columns={detailColumns}
            dataSource={details}
            rowKey="detailId"
            pagination={false}
            size="small"
            scroll={{ x: 800 }}
            className={styles.detailTable}
            locale={{
              emptyText: (
                <div className={styles.emptyState}>
                  <div className={styles.emptyText}>
                    {isViewMode
                      ? "暫無變動明細"
                      : "請點擊「新增明細」按鈕添加變動項目"}
                  </div>
                </div>
              ),
            }}
          />
        </div>

        {/* 表單按鈕 */}
        {!isViewMode && (
          <div style={{ textAlign: "right", marginTop: 24 }}>
            <Space>
              <Button onClick={onCancel}>取消</Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={submitLoading}
                onClick={handleSubmit}
              >
                {editingTransfer ? "更新" : "儲存"}
              </Button>
            </Space>
          </div>
        )}
      </Form>

      {/* 變動明細模態框 */}
      <Modal
        title={editingDetail ? "編輯變動明細" : "新增變動明細"}
        open={detailModalVisible}
        onOk={handleSaveDetail}
        onCancel={() => setDetailModalVisible(false)}
        width={800}
        okText="確定"
        cancelText="取消"
      >
        <Form form={detailForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="assetId" label="財產" rules={FORM_RULES.assetId}>
                <Select
                  placeholder="請選擇財產"
                  showSearch
                  filterOption={(input, option) => {
                    const asset = assets.find(
                      (a) => a.assetId === option?.value
                    );
                    if (!asset) return false;
                    return (
                      asset.assetNo
                        .toLowerCase()
                        .includes(input.toLowerCase()) ||
                      asset.assetName
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    );
                  }}
                  onChange={handleSelectAsset}
                >
                  {assets.map((asset) => (
                    <Option key={asset.assetId} value={asset.assetId}>
                      {asset.assetNo} - {asset.assetName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="changeItems"
                label="變動項目"
                rules={[{ required: true, message: "請選擇變動項目" }]}
              >
                <Select
                  mode="multiple"
                  placeholder="請選擇變動項目"
                  onChange={handleChangeItemsChange}
                >
                  {Object.entries(CHANGE_ITEMS_MAPPING).map(([key, label]) => (
                    <Option key={key} value={key}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 原始資訊 */}
          <Card title="原始資訊" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="originalLocationName" label="存放地點">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="originalCustodianName" label="保管人">
                  <Input disabled />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="originalUserName" label="使用人">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="originalDepartmentName" label="部門">
                  <Input disabled />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 新資訊 */}
          <Card title="變更為" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="newLocationId" label="存放地點">
                  <Select
                    placeholder="請選擇新存放地點"
                    allowClear
                    onChange={handleNewLocationChange}
                  >
                    {storageLocations.map((location) => (
                      <Option
                        key={location.storageLocationId}
                        value={location.storageLocationId}
                      >
                        {location.locationName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="newCustodianId" label="保管人">
                  <Select
                    placeholder="請選擇新保管人"
                    allowClear
                    onChange={handleNewCustodianChange}
                  >
                    {users.map((user) => (
                      <Option key={user.userId} value={user.userId}>
                        {user.userName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="newUserId" label="使用人">
                  <Select
                    placeholder="請選擇新使用人"
                    allowClear
                    onChange={handleNewUserChange}
                  >
                    {users.map((user) => (
                      <Option key={user.userId} value={user.userId}>
                        {user.userName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="newDepartmentId" label="部門">
                  <Select
                    placeholder="請選擇新部門"
                    allowClear
                    onChange={handleNewDepartmentChange}
                  >
                    {departments.map((dept) => (
                      <Option key={dept.departmentId} value={dept.departmentId}>
                        {dept.departmentName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Form.Item name="detailNotes" label="備註">
            <TextArea rows={3} placeholder="請輸入備註" />
          </Form.Item>

          {/* 隱藏欄位 */}
          <Form.Item name="assetNo" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="assetName" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="originalLocationId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="originalCustodianId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="originalUserId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="originalDepartmentId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="originalDivisionId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="newLocationName" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="newCustodianName" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="newUserName" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="newDepartmentName" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="newDivisionName" hidden>
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TransferForm;
