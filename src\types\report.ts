/**
 * 報表相關的類型定義
 */

/**
 * 報表抬頭組件的屬性介面
 */
export interface ReportHeaderProps {
    /** 報表名稱 */
    reportTitle: string;
    /** 當前頁數 */
    currentPage?: number;
    /** 總頁數 */
    totalPages?: number;
    /** 列印日期，預設為當前日期 */
    printDate?: string;
    /** 是否顯示為列印模式 */
    isPrintMode?: boolean;
    /** 自訂樣式 */
    style?: React.CSSProperties;
    /** 自訂類別名稱 */
    className?: string;
}

/**
 * 報表列印選項
 */
export interface ReportPrintOptions {
    /** 報表標題 */
    title: string;
    /** 紙張大小 */
    paperSize?: 'A4' | 'A3' | 'Letter';
    /** 方向 */
    orientation?: 'portrait' | 'landscape';
    /** 邊距設定 */
    margins?: {
        top?: string;
        right?: string;
        bottom?: string;
        left?: string;
    };
    /** 是否顯示頁首 */
    showHeader?: boolean;
    /** 是否顯示頁尾 */
    showFooter?: boolean;
}

/**
 * 通用報表Hook的返回值
 */
export interface UseReportReturn {
    /** 是否為列印模式 */
    isPrintMode: boolean;
    /** 切換列印模式 */
    togglePrintMode: () => void;
    /** 執行列印 */
    handlePrint: () => void;
    /** 匯出PDF */
    exportToPDF?: () => void;
    /** 匯出Excel */
    exportToExcel?: () => void;
    /** 當前頁數 */
    currentPage: number;
    /** 總頁數 */
    totalPages: number;
    /** 設定頁數 */
    setPageInfo: (current: number, total: number) => void;
}

/**
 * 報表統計資料的基本介面
 */
export interface ReportStatistics {
    /** 總計數量 */
    totalCount: number;
    /** 總計金額 */
    totalAmount?: number;
    /** 其他統計欄位 */
    [key: string]: any;
} 