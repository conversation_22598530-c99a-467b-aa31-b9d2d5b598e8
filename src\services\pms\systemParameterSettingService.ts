import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 系統參數設定介面
export interface SystemParameter {
    parameterId: string;
    parameterName: string;
    parameterValue: string;
    parameterDescription: string;
    parameterType: string;
    isEnabled: boolean;
    sortOrder: number;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
    isDeleted: boolean;
}

// 折舊法設定介面
export interface DepreciationMethod {
    code: string;
    description: string;
    formula: string;
    isDefault: boolean;
    rate?: number;
}

// 餘額遞減法折舊率設定介面
export interface DecliningBalanceRate {
    assetAccountId: string;
    assetAccountName: string;
    rate: number;
}

// 設定餘額遞減法折舊率請求介面
export interface SetDecliningBalanceRateRequest {
    assetAccountId: string;
    rate: number;
    userId: string;
}

// 取得所有系統參數
export async function getSystemParameters(): Promise<ApiResponse<SystemParameter[]>> {
    try {
        const response = await httpClient(apiEndpoints.getPmsSystemParameters, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統參數列表失敗",
            data: [],
        };
    }
}

// 依類型取得系統參數
export async function getSystemParametersByType(parameterType: string): Promise<ApiResponse<SystemParameter[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPmsSystemParametersByType}/${parameterType}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統參數列表失敗",
            data: [],
        };
    }
}

// 依ID取得系統參數
export async function getSystemParameterById(id: string): Promise<ApiResponse<SystemParameter>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPmsSystemParameterDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統參數詳細資料失敗",
            data: {} as SystemParameter,
        };
    }
}

// 新增系統參數
export async function addSystemParameter(parameterData: SystemParameter): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addPmsSystemParameter, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(parameterData),
        });


        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增系統參數失敗",
            data: null,
        };
    }
}

// 編輯系統參數
export async function editSystemParameter(parameterData: SystemParameter): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editPmsSystemParameter, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(parameterData),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯系統參數失敗",
            data: null,
        };
    }
}

// 刪除系統參數
export async function deleteSystemParameter(parameterData: SystemParameter): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.deletePmsSystemParameter, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(parameterData),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除系統參數失敗",
            data: null,
        };
    }
}

// 取得參數設定
export async function getDepreciationMethods(): Promise<ApiResponse<SystemParameter[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDepreciationMethods, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊法設定失敗",
            data: [],
        };
    }
}

// 設定默認折舊法
export async function setDefaultDepreciationMethod(methodId: string): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(`${apiEndpoints.setDefaultDepreciationMethod}/${methodId}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "設定默認折舊法失敗",
            data: null,
        };
    }
}

// 取得所有餘額遞減法折舊率設定
export async function getDecliningBalanceRates(): Promise<ApiResponse<SystemParameter[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDecliningBalanceRates, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取餘額遞減法折舊率設定失敗",
            data: [],
        };
    }
}

// 取得特定財產科目的餘額遞減法折舊率
export async function getDecliningBalanceRate(assetAccountId: string): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDecliningBalanceRate}/${assetAccountId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取特定財產科目的餘額遞減法折舊率失敗",
            data: null,
        };
    }
}

// 設定特定財產科目的餘額遞減法折舊率
export async function setDecliningBalanceRate(request: SetDecliningBalanceRateRequest): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.setDecliningBalanceRate, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(request),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "設定特定財產科目的餘額遞減法折舊率失敗",
            data: null,
        };
    }
}

// 取得系統初始化狀態
export async function getInitializationStatus(): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.getInitializationStatus, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得系統初始化狀態失敗",
            data: null,
        };
    }
}

// 設定系統初始化狀態
export async function setInitializationStatus(isInitialized: boolean): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(`${apiEndpoints.setInitializationStatus}/${isInitialized}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "設定系統初始化狀態失敗",
            data: null,
        };
    }
}

