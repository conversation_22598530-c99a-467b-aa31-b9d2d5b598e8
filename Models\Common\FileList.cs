﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 檔案列表 DTO
    /// </summary>
    /// <remarks>
    /// 這個類別用於檔案列表的資料傳輸物件，包含檔案編號、名稱、說明、排序編號和是否異動或刪除資料的標記。
    /// </remarks>
    public class FileListDTO
    {
        public string FileId { get; set; } //檔案列表編號
        public string FileName { get; set; } //檔案名稱
        public string FileType { get; set; } //檔案類型
        public string FilePath { get; set; } //檔案路徑
        public string Description { get; set; } //檔案說明
        public int SortOrder { get; set; } //排序編號
        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public bool isEdit { get; set; } //是否異動資料
        public bool isDelete { get; set; } //是否刪除資料


       public FileListDTO()
        {
            FileId = "";
            FileName = "";
            FileType = "";
            FilePath = "";
            Description = "";
            SortOrder = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            isEdit = false;
            isDelete = false;
        }
    }

    public class FileList : ModelBaseEntity
    {
        [Key]
        [Comment("檔案列表編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string FileId { get; set; } //檔案列表編號

        [Comment("檔案來源編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string FileListId { get; set; } //檔案來源編號

        [Comment("來源資料表")]
        [Column(TypeName = "nvarchar(100)")]
        public string SourceTable { get; set; } //來源資料表

        [Comment("檔案名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string FileName { get; set; } //檔案名稱

        [Comment("檔案類型")]
        [Column(TypeName = "nvarchar(50)")]
        public string FileType { get; set; } //檔案類型

        [Comment("檔案路徑")]
        [Column(TypeName = "nvarchar(100)")]
        public string FilePath { get; set; } //檔案路徑

        [Comment("檔案說明")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Description { get; set; } //檔案說明

        [Comment("排序編號")]
        [Column(TypeName = "int")]
        public int SortOrder { get; set; } //排序編號

        public FileList()
        {
            FileId = "";
            FileListId = "";
            SourceTable = "";
            FileName = "";
            FileType = "";
            FilePath = "";
            Description = "";
            SortOrder = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }    
    /// <summary>
    /// 檔案上傳 DTO
    /// </summary>
    /// <remarks>
    /// 這個類別用於檔案上傳的資料傳輸物件，包含檔案名稱、說明、排序編號和上傳的檔案。
    /// </remarks>
    public class FileListUploadDTO
    {
        public string fileName { get; set; } //檔案名稱
        public string description { get; set; } //檔案說明
        public int sortOrder { get; set; } //排序編號
        public string uploadFileName { get; set; }    // 用來比對 formData 中檔案名稱

        [Required]
        public IFormFile uploadFile { get; set; } // 用來接收上傳的檔案

        public FileListUploadDTO()
        {
            fileName = "";
            description = "";
            sortOrder = 0;
            uploadFileName = "";
            uploadFile = null;
        }
    }

    public class GetFileListRequest
    {
        [Required(ErrorMessage = "請輸入檔案清單 ID")]
        public string FileListId { get; set; }
    }
}
