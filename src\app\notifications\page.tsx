"use client";

import React, { useEffect, useState, Suspense } from "react";
import { Card, Tabs, List, Badge, Typography, Space, Button } from "antd";
import { BellOutlined, CheckOutlined, UserOutlined } from "@ant-design/icons";
import {
  Notification,
  NotificationType,
  getSystemNotifications,
  getPersonalNotifications,
  markNotificationAsRead,
} from "@/services/common/notificationService";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { notifySuccess, notifyError } from "@/utils/notification";
import { useSearchParams, useRouter } from "next/navigation";

const { Text } = Typography;

// 创建一个内部组件来使用 useSearchParams
function NotificationsContent() {
  const [systemNotifications, setSystemNotifications] = useState<
    Notification[]
  >([]);
  const [personalNotifications, setPersonalNotifications] = useState<
    Notification[]
  >([]);
  const [loading, setLoading] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const activeTab = searchParams.get("tab") || "system";

  // 加載通知
  const loadNotifications = async () => {
    setLoading(true);
    try {
      const [systemResponse, personalResponse] = await Promise.all([
        getSystemNotifications(),
        getPersonalNotifications(),
      ]);

      if (systemResponse.success && systemResponse.data) {
        setSystemNotifications(systemResponse.data);
      }
      if (personalResponse.success && personalResponse.data) {
        setPersonalNotifications(personalResponse.data);
      }
    } catch (error) {
      notifyError("載入通知失敗");
    } finally {
      setLoading(false);
    }
  };

  // 處理頁籤切換
  const handleTabChange = (key: string) => {
    router.push(`/notifications?tab=${key}`);
  };

  // 標記通知為已讀
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const response = await markNotificationAsRead(notificationId);
      if (response.success) {
        notifySuccess("通知已標記為已讀");
        await loadNotifications(); // 重新加載通知
      } else {
        notifyError(response.message || "標記通知失敗");
      }
    } catch (error) {
      notifyError("標記通知失敗");
    }
  };

  useEffect(() => {
    loadNotifications();
  }, []);

  // 通知列表項目渲染
  const renderNotificationItem = (notification: Notification) => (
    <List.Item
      actions={[
        !notification.isRead && (
          <Button
            type="link"
            icon={<CheckOutlined />}
            onClick={() => handleMarkAsRead(notification.notificationId)}
          >
            標記已讀
          </Button>
        ),
      ].filter(Boolean)}
    >
      <List.Item.Meta
        title={
          <Space>
            {!notification.isRead && <Badge status="processing" />}
            <Text strong>{notification.title}</Text>
            {notification.type === NotificationType.System && (
              <Badge count="系統" style={{ backgroundColor: "#52c41a" }} />
            )}
            {notification.type === NotificationType.Personal && (
              <Badge count="個人" style={{ backgroundColor: "#1890ff" }} />
            )}
          </Space>
        }
        description={
          <div>
            <div>{notification.content}</div>
            <Text type="secondary">
              {DateTimeExtensions.formatFromTimestamp(notification.createTime)}
            </Text>
          </div>
        }
      />
    </List.Item>
  );

  const items = [
    {
      key: "system",
      label: (
        <span>
          <BellOutlined />
          系統通知
          <Badge
            count={systemNotifications.filter((n) => !n.isRead).length}
            style={{ marginLeft: 8 }}
          />
        </span>
      ),
      children: (
        <List
          loading={loading}
          dataSource={systemNotifications}
          renderItem={renderNotificationItem}
        />
      ),
    },
    {
      key: "personal",
      label: (
        <span>
          <UserOutlined />
          個人通知
          <Badge
            count={personalNotifications.filter((n) => !n.isRead).length}
            style={{ marginLeft: 8 }}
          />
        </span>
      ),
      children: (
        <List
          loading={loading}
          dataSource={personalNotifications}
          renderItem={renderNotificationItem}
        />
      ),
    },
    {
      key: "all",
      label: (
        <span>
          <BellOutlined />
          所有通知
          <Badge
            count={
              [...systemNotifications, ...personalNotifications].filter(
                (n) => !n.isRead
              ).length
            }
            style={{ marginLeft: 8 }}
          />
        </span>
      ),
      children: (
        <List
          loading={loading}
          dataSource={[...systemNotifications, ...personalNotifications]}
          renderItem={renderNotificationItem}
        />
      ),
    },
  ];

  return (
    <Card>
      <Tabs items={items} activeKey={activeTab} onChange={handleTabChange} />
    </Card>
  );
}

// 加载中的占位组件
function LoadingFallback() {
  return (
    <Card>
      <div>載入中...</div>
    </Card>
  );
}

const NotificationsPage: React.FC = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <NotificationsContent />
    </Suspense>
  );
};

export default NotificationsPage;
