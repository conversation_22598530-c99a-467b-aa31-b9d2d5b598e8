using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Services.Ims;
/// <summary> 庫存品價格服務 </summary>
public class ItemPriceService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : IItemPriceService
{
    /// <summary> 庫存品價格列表 </summary>
    public async Task<List<ItemPriceDTO>> GetAllAsync()
    {
        var entity = await _context.Ims_ItemPrice
            .OrderBy(e => e.ItemPriceID)
            .Select(t => new ItemPriceDTO
            {
                ItemPriceID = t.ItemPriceID,
                ItemID = t.ItemID,
                PriceTypeID = t.PriceTypeID,
                Price = t.Price,
                CreateTime = t.CreateTime,
                CreateUserId = t.CreateUserId,
                UpdateTime = t.UpdateTime,
                UpdateUserId = t.UpdateUserId,
                DeleteTime = t.DeleteTime,
                DeleteUserId = t.DeleteUserId,
                IsDeleted = t.IsDeleted
            })
            .ToListAsync();
        return entity;
    }

    /// <summary> 庫存品價格取得 </summary>
    public async Task<ItemPriceDTO> GetAsync(Guid ItemPriceID)
    {
        var entity = await _context.Ims_ItemPrice
            .Where(e => e.ItemPriceID == ItemPriceID)
            .OrderBy(e => e.ItemPriceID)
            .Select(t => new ItemPriceDTO
            {
                ItemPriceID = t.ItemPriceID,
                ItemID = t.ItemID,
                PriceTypeID = t.PriceTypeID,
                Price = t.Price,
                CreateTime = t.CreateTime,
                CreateUserId = t.CreateUserId,
                UpdateTime = t.UpdateTime,
                UpdateUserId = t.UpdateUserId,
                DeleteTime = t.DeleteTime,
                DeleteUserId = t.DeleteUserId,
                IsDeleted = t.IsDeleted
            })
            .FirstOrDefaultAsync();
        return entity;
    }

    /// <summary> 庫存品價格新增 </summary>
    public async Task<(bool, string)> AddAsync(ItemPriceDTO DTO)
    {
        if (DTO == null)
        {
            return (false, "ItemPrice data is null");
        }

        var entity = new ItemPrice
        {
            ItemPriceID = Guid.NewGuid(),
            ItemID = DTO.ItemID,
            PriceTypeID = DTO.PriceTypeID,
            Price = DTO.Price,
            CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId
        };

        _context.Ims_ItemPrice.Add(entity);
        await _context.SaveChangesAsync();
        return (true, $"庫存品價格 {entity.ItemPriceID} 新增成功!");
    }

    /// <summary> 庫存品價格更新 </summary>
    public async Task<(bool, string)> UpdateAsync(ItemPriceDTO DTO)
    {
        var entity = await _context.Ims_ItemPrice.FirstOrDefaultAsync(i => i.ItemPriceID == DTO.ItemPriceID);
        if (entity == null)
        {
            return (false, $"ItemPrice {DTO.ItemPriceID} not found");
        }

        entity.ItemID = DTO.ItemID;
        entity.PriceTypeID = DTO.PriceTypeID;
        entity.Price = DTO.Price;
        entity.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
        entity.UpdateUserId = _currentUserService.UserId;

        _context.Ims_ItemPrice.Update(entity);
        await _context.SaveChangesAsync();
        return (true, $"ItemPrice {entity.ItemPriceID} updated successfully");
    }

    /// <summary> 庫存品價格刪除 </summary>
    public async Task<(bool, string)> DeleteAsync(ItemPriceDTO DTO)
    {
        var entity = await _context.Ims_ItemPrice
            .FirstOrDefaultAsync(i => i.ItemPriceID == DTO.ItemPriceID);
        if (entity == null)
        {
            return (false, $"ItemPrice {DTO.ItemPriceID} not found");
        }
        entity.IsDeleted = true;
        entity.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
        entity.DeleteUserId = _currentUserService.UserId;

        _context.Ims_ItemPrice.Update(entity);
        await _context.SaveChangesAsync();
        return (true, $"ItemPrice {entity.ItemPriceID} deleted successfully");
    }
}