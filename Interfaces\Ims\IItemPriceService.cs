using FAST_ERP_Backend.Models.Ims;
using System;
using System.Collections.Generic;
using System.Data;

namespace FAST_ERP_Backend.Interfaces.Ims;
/// <summary> 庫存品價格服務介面 </summary>
public interface IItemPriceService
{
    /// <summary> 庫存品價格列表 </summary>
    Task<List<ItemPriceDTO>> GetAllAsync();

    /// <summary> 庫存品價格取得 </summary>
    Task<ItemPriceDTO> GetAsync(Guid ItemPriceID);

    /// <summary> 庫存品價格新增 </summary>
    Task<(bool, string)> AddAsync(ItemPriceDTO item);

    /// <summary> 庫存品價格更新 </summary>
    Task<(bool, string)> UpdateAsync(ItemPriceDTO item);

    /// <summary> 庫存品價格刪除 </summary>
    Task<(bool, string)> DeleteAsync(ItemPriceDTO item);
}