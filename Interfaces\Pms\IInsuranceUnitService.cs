using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IInsuranceUnitService
    {
        /// <summary>
        /// 取得保險單位資料
        /// </summary>
        /// <param name="_insuranceUnitId">保險單位編號</param>
        /// <returns>保險單位資料列表</returns>
        Task<List<InsuranceUnitDTO>> GetInsuranceUnitAsync(string _insuranceUnitId = "");

        /// <summary>
        /// 新增保險單位資料
        /// </summary>
        /// <param name="_data">保險單位資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddInsuranceUnitAsync(InsuranceUnitDTO _data);

        /// <summary>
        /// 編輯保險單位資料
        /// </summary>
        /// <param name="_data">保險單位資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditInsuranceUnitAsync(InsuranceUnitDTO _data);

        /// <summary>
        /// 刪除保險單位資料
        /// </summary>
        /// <param name="_data">保險單位資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteInsuranceUnitAsync(InsuranceUnitDTO _data);

        /// <summary>
        /// 取得保險單位詳細資料
        /// </summary>
        /// <param name="_insuranceUnitId">保險單位編號</param>
        /// <returns>保險單位詳細資料</returns>
        Task<InsuranceUnitDTO> GetInsuranceUnitDetailAsync(string _insuranceUnitId);
    }
}