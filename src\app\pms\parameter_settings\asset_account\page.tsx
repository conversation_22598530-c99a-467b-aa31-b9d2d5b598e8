"use client";

/* 財產科目
  /app/pms/parameter_settings/asset_account/page.tsx
 */

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Tag,
  Select,
  List,
  Typography,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  AssetAccount,
  getAssetAccounts,
  createAssetAccount,
  updateAssetAccount,
  deleteAssetAccount,
} from "@/services/pms/assetAccountService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { useAuth } from "@/contexts/AuthContext";

const { Text } = Typography;

const AssetAccountPage: React.FC = () => {
  const [assetAccounts, setAssetAccounts] = useState<AssetAccount[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<AssetAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<AssetAccount | null>(
    null
  );
  const [form] = Form.useForm();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [accountToDelete, setAccountToDelete] = useState<AssetAccount | null>(
    null
  );
  const [searchText, setSearchText] = useState("");
  const [selectedAssetAccountId, setSelectedAssetAccountId] = useState<
    string | undefined
  >(undefined);
  const { user } = useAuth();
  const [isMobile, setIsMobile] = useState(false);

  // 加載財產科目列表
  const loadAssetAccounts = async () => {
    setLoading(true);
    try {
      const response = await getAssetAccounts();
      if (response.success && response.data) {
        setAssetAccounts(response.data);
        setFilteredAccounts(response.data);
      } else {
        notifyError("獲取財產科目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產科目列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAssetAccounts();
  }, []);

  // 處理搜尋和篩選
  useEffect(() => {
    let filtered = assetAccounts;

    // 按財產科目篩選
    if (selectedAssetAccountId) {
      filtered = filtered.filter(
        (account) => account.assetAccountId === selectedAssetAccountId
      );
    }

    // 按搜尋文字篩選
    if (searchText) {
      filtered = filtered.filter((account) =>
        account.assetAccountName
          .toLowerCase()
          .includes(searchText.toLowerCase())
      );
    }

    setFilteredAccounts(filtered);
  }, [searchText, selectedAssetAccountId, assetAccounts]);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 表格列定義
  const columns: ColumnsType<AssetAccount> = [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "科目編號",
      dataIndex: "assetAccountNo",
      key: "assetAccountNo",
    },
    {
      title: "科目名稱",
      dataIndex: "assetAccountName",
      key: "assetAccountName",
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (text) => (
        <span>{DateTimeExtensions.formatFromTimestamp(text)}</span>
      ),
    },
    {
      title: "建立者",
      dataIndex: "createUserName",
      key: "createUserName",
    },
    /* {
      title: "更新時間",
      dataIndex: "updateTime",
      key: "updateTime",
    },
    {
      title: "更新者",
      dataIndex: "updateUserId",
      key: "updateUserId",
    }, */

    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          {/* <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button> */}
        </Space>
      ),
    },
  ];

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 檢查科目編號是否已存在
      if (!editingAccount) {
        const isDuplicate = assetAccounts.some(
          (account) => account.assetAccountNo === values.assetAccountNo
        );
        if (isDuplicate) {
          notifyError("新增失敗", "科目編號已存在");
          return;
        }
      }

      if (editingAccount) {
        // 更新財產科目
        const response = await updateAssetAccount({
          ...values,
          assetAccountNo: editingAccount.assetAccountNo,
          assetAccountId: editingAccount.assetAccountId,
          updateUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("更新成功", "財產科目已更新");
          loadAssetAccounts();
        } else {
          notifyError("更新失敗", response.message);
        }
      } else {
        // 新增財產科目
        const response = await createAssetAccount({
          ...values,
          createUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("新增成功", "財產科目已新增");
          loadAssetAccounts();
        } else {
          notifyError("新增失敗", response.message);
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (account: AssetAccount) => {
    setEditingAccount(account);
    form.setFieldsValue(account);
    setIsModalVisible(true);
  };

  // 處理刪除
  /* const handleDelete = (account: AssetAccount) => {
    setAccountToDelete(account);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  }; */

  // 執行刪除
  /* const executeDelete = async () => {
    if (!accountToDelete) return;

    try {
      const response = await deleteAssetAccount(accountToDelete);
      if (response.success) {
        notifySuccess("刪除成功", "財產科目已刪除");
        loadAssetAccounts();
        setDeleteModalVisible(false);
        setAccountToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message);
      }
    } catch (error) {
      notifyError("刪除失敗", "請稍後再試");
    }
  }; */

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredAccounts}
        renderItem={(account) => (
          <List.Item
            key={account.assetAccountId}
            style={{
              padding: "12px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <div style={{ width: "100%" }}>
              {/* 標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <div
                  style={{
                    width: 24,
                    height: 24,
                    background: "#f0f0f0",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "14px",
                    flexShrink: 0,
                  }}
                >
                  {filteredAccounts.indexOf(account) + 1}
                </div>
                <Text strong style={{ fontSize: "16px" }}>
                  {account.assetAccountName}
                </Text>
                <Text type="secondary" style={{ fontSize: "14px" }}>
                  ({account.assetAccountNo})
                </Text>
              </div>

              {/* 時間資訊 */}
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "13px" }}>
                  建立時間：
                  {DateTimeExtensions.formatFromTimestamp(account.createTime)}
                </Text>
              </div>

              {/* 建立者 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px", color: "#666" }}>
                  建立者：{account.createUserName}
                </Text>
              </div>

              {/* 操作按鈕列 */}
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginTop: "12px",
                }}
              >
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(account)}
                  style={{ padding: 0 }}
                >
                  編輯
                </Button>
              </div>
            </div>
          </List.Item>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
          size: "small",
          style: { marginTop: "16px" },
        }}
      />
    );
  };

  return (
    <Card
      title="財產科目"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
        wrap
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingAccount(null);
            setIsModalVisible(true);
          }}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增財產科目
        </Button>
        <Select
          placeholder="選擇財產科目"
          allowClear
          style={{ width: "100%" }}
          options={[
            { label: "全部", value: "" },
            ...assetAccounts.map((account) => ({
              label: account.assetAccountName,
              value: account.assetAccountId,
            })),
          ]}
          onChange={(value) => setSelectedAssetAccountId(value)}
        />
        <Input
          placeholder="搜尋財產科目名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: "100%" }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredAccounts}
          rowKey="assetAccountId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingAccount ? "編輯財產科目" : "新增財產科目"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        maskClosable={false}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsModalVisible(false);
              form.resetFields();
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={form.submit}
          >
            {editingAccount ? "儲存" : "新增"}
          </Button>,
        ]}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="assetAccountNo"
            label="科目編號"
            rules={[
              { required: true, message: "請輸入科目編號" },
              {
                validator: async (_, value) => {
                  if (!editingAccount && value) {
                    const isDuplicate = assetAccounts.some(
                      (account) => account.assetAccountNo === value
                    );
                    if (isDuplicate) {
                      throw new Error("科目編號已存在");
                    }
                  }
                },
              },
            ]}
          >
            {editingAccount ? (
              <span>{editingAccount.assetAccountNo}</span>
            ) : (
              <Input placeholder="請輸入科目編號" maxLength={10} />
            )}
          </Form.Item>

          <Form.Item
            name="assetAccountName"
            label="財產科目名稱"
            rules={[{ required: true, message: "請輸入財產科目名稱" }]}
          >
            <Input placeholder="請輸入財產科目名稱" maxLength={20} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 刪除確認對話框 */}
      {/* <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setAccountToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled:
            deleteConfirmText !== (accountToDelete?.assetAccountName || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{accountToDelete?.assetAccountName}」</strong>
            以確認刪除：
          </p>
          <Input
            placeholder="請輸入財產科目名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal> */}
    </Card>
  );
};

export default AssetAccountPage;
