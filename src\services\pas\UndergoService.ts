import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 歷任經歷資料
export interface Undergo {
    uid: string;                  // 資料編號
    userId: string;              // 使用者編號
    agencyName: string;          // 服務機關名稱
    departmentName: string;      // 服務部門名稱
    jobTitle: string;            // 職稱
    duty: string;                // 職務
    jobGrade: string;            // 薪級
    hireDate: string;            // 到職年月（timestamp）
    terminationDate: string;     // 卸職年月（timestamp）
    supervisorName: string;      // 主管姓名
    certificateDate: string;     // 發證日期（timestamp）
    certificateNumber: string;   // 證書文號
    remark: string;              // 備註
}


export const createEmptyUndergo = (): Undergo => ({
    uid: '',
    userId: '',
    agencyName: '',
    departmentName: '',
    jobTitle: '',
    duty: '',
    jobGrade: '',
    hireDate: '',
    terminationDate: '',
    supervisorName: '',
    certificateDate: '',
    certificateNumber: '',
    remark: '',
});


// 搜尋歷任經歷列表
export async function getUndergoList(userid: string): Promise<ApiResponse<Undergo[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getUndergoList}/${userid}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋歷任經歷資料失敗",
        };
    }
}

// 搜尋歷任經歷明細
export async function getUndergoDetail(uid: string): Promise<ApiResponse<Undergo>> {
    return await httpClient(`${apiEndpoints.getUndergoDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增歷任經歷資料
export async function addUndergo(data: Partial<Undergo>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addUndergo, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增歷任經歷資料失敗",
        };
    }
}

// 編輯歷任經歷資料
export async function editUndergo(data: Partial<Undergo>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editUndergo, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯歷任經歷資料失敗",
        };
    }
}

// 刪除歷任經歷資料
export async function deleteUndergo(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteUndergo, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除歷任經歷資料失敗",
        };
    }
}
