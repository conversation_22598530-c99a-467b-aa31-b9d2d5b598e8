﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("管理公司群組")]
    public class EnterpriseGroupsController : Controller
    {
        private readonly IEnterpriseGroupsService _service;

        public EnterpriseGroupsController(IEnterpriseGroupsService service)
        {
            _service = service;
        }

        // 取得公司群組列表
        [HttpGet("GetEnterpriseGroupsList")]
        [SwaggerOperation(Summary = "取得公司群組列表", Description = "取得所有公司群組的列表")]
        public async Task<IActionResult> GetEnterpriseGroupsList()
        {
            var result = await _service.GetEnterpriseGroupsListAsync();
            return Ok(result);
        }

        // 取得公司群組詳細資料
        [HttpGet("GetEnterpriseGroupsDetail/{id}")]
        [SwaggerOperation(Summary = "取得公司群組詳細資料", Description = "根據ID取得公司群組的詳細資料")]
        public async Task<IActionResult> GetEnterpriseGroupsDetail(string id)
        {
            var result = await _service.GetEnterpriseGroupsDetailAsync(id);
            if (result == "找不到資料")
            {
                return NotFound(result);
            }
            return Ok(result);
        }

        // 新增公司群組
        [HttpPost("AddEnterpriseGroups")]
        [SwaggerOperation(Summary = "新增公司群組", Description = "新增新的公司群組")]
        public async Task<IActionResult> AddEnterpriseGroups([FromBody] EnterpriseGroupsDTO data)
        {
            var (success, message) = await _service.AddEnterpriseGroupsAsync(data);
            if (success)
            {
                return Ok(message);
            }
            return BadRequest(message);
        }

        // 編輯公司群組
        [HttpPost("EditEnterpriseGroups")]
        [SwaggerOperation(Summary = "編輯公司群組", Description = "編輯已存在的公司群組")]
        public async Task<IActionResult> EditEnterpriseGroups([FromBody] EnterpriseGroupsDTO data)
        {
            var (success, message) = await _service.EditEnterpriseGroupsAsync(data);
            if (success)
            {
                return Ok(message);
            }
            return BadRequest(message);
        }

        // 刪除公司群組
        [HttpPost("DeleteEnterpriseGroups")]
        [SwaggerOperation(Summary = "刪除公司群組", Description = "刪除已存在的公司群組")]
        public async Task<IActionResult> DeleteEnterpriseGroups([FromBody] EnterpriseGroupsDTO data)
        {
            var (success, message) = await _service.DeleteEnterpriseGroupsAsync(data);
            if (success)
            {
                return Ok(message);
            }
            return BadRequest(message);
        }
    }
}
