﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class UnitService : IUnitService
    {
        private readonly ERPDbContext _context;

        public UnitService(ERPDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得單位資料
        /// </summary>
        /// <param name="_unitId"></param>
        /// <returns></returns>
        public async Task<List<UnitDTO>> GetUnitAsync(string _unitId = "")
        {
            var query = _context.Set<Unit>().AsQueryable();

            if (!string.IsNullOrEmpty(_unitId))
            {
                query = query.Where(u => u.UnitId == Guid.Parse(_unitId));
            }

            return await query.Select(u => new UnitDTO
            {
                UnitId = u.UnitId,
                UnitNo = u.UnitNo,
                Name = u.Name,
                SortCode = u.SortCode,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                DeleteTime = u.DeleteTime,
                DeleteUserId = u.DeleteUserId,
                IsDeleted = u.IsDeleted
            }).ToListAsync();
        }

        /// <summary>
        /// 新增單位
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddUnitAsync(UnitDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 檢查單位編號是否已存在
                var existingUnitId = await _context.Set<Unit>()
                    .Where(u => u.UnitId == _data.UnitId && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingUnitId != null)
                {
                    return (false, "單位編號已存在");
                }

                // 檢查單位名稱是否已存在
                /* var existingUnitName = await _context.Set<Unit>()
                    .Where(u => u.Name == _data.Name && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingUnitName != null)
                {
                    return (false, "單位名稱已存在");
                } */

                var entity = new Unit
                {
                    UnitId = _data.UnitId,
                    UnitNo = _data.UnitNo,
                    Name = _data.Name,
                    CreateUserId = _data.CreateUserId,
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯單位
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditUnitAsync(UnitDTO _data)
        {
            try
            {
                var entity = await _context.Set<Unit>().FindAsync(_data.UnitId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 檢查單位編號是否已存在
                var existingUnitNo = await _context.Set<Unit>()
                    .Where(u => u.UnitNo == _data.UnitNo && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingUnitNo != null && existingUnitNo.UnitId != _data.UnitId)
                {
                    return (false, "單位編號已存在");
                }

                // 檢查單位名稱是否已存在
                var existingUnitName = await _context.Set<Unit>()
                    .Where(u => u.Name == _data.Name && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingUnitName != null && existingUnitName.UnitId != _data.UnitId)
                {
                    return (false, "單位名稱已存在");
                }

                entity.UnitNo = _data.UnitNo;
                entity.Name = _data.Name;
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除單位
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteUnitAsync(UnitDTO _data)
        {
            try
            {
                var entity = await _context.Set<Unit>().FindAsync(_data.UnitId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得單位詳細資料
        /// </summary>
        /// <param name="_unitId"></param>
        /// <returns></returns>
        public async Task<UnitDTO> GetUnitDetailAsync(string _unitId)
        {
            var entity = await _context.Set<Unit>().FindAsync(_unitId);
            if (entity == null)
            {
                return null;
            }

            return new UnitDTO
            {
                UnitId = entity.UnitId,
                Name = entity.Name,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }
    }
}

