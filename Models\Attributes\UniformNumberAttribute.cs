using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Attributes
{
    /// <summary>
    /// 驗證台灣統一編號格式的屬性
    /// 
    /// 基本使用
    /// [UniformNumber]
    /// public string UniformNumber { get; set; }
    /// 
    /// 有效的統一編號格式範例：
    /// 12345678
    /// 123456789
    /// 1234567890
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class UniformNumberAttribute : ValidationAttribute
    {
        public UniformNumberAttribute() : base("統一編號格式不正確")
        {
        }

        /// <summary>
        /// 驗證方法
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success; // 若要求必填，請搭配 [Required] 屬性
            }

            string uniformNumber = value.ToString();

            // 移除可能的空格和特殊符號
            uniformNumber = Regex.Replace(uniformNumber, @"[\s-]", "");

            // 基本格式檢查: 8位數字
            if (!Regex.IsMatch(uniformNumber, @"^\d{8}$"))
            {
                return new ValidationResult(ErrorMessage);
            }

            // 台灣統一編號新驗證規則
            int[] weights = { 1, 2, 1, 2, 1, 2, 4, 1 };
            int sum = 0;
            bool specialCase = false;

            for (int i = 0; i < 8; i++)
            {
                int product = int.Parse(uniformNumber[i].ToString()) * weights[i];
                sum += product / 10 + product % 10; // 將乘積的十位數和個位數相加
            }

            // 檢查是否為專案特殊案例（第七位為7時）
            if (uniformNumber[6] == '7')
            {
                specialCase = (sum % 10 == 0) || ((sum + 1) % 10 == 0);
            }

            // 標準驗證
            bool standardCase = (sum % 5 == 0);

            if (!(standardCase || specialCase))
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }
}