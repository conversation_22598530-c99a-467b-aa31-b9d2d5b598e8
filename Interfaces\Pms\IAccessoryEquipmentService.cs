using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAccessoryEquipmentService
    {
        Task<List<AccessoryEquipmentDTO>> GetAllAsync();
        Task<string> GetByIdAsync(Guid id);
        Task<(bool, string)> AddAsync(AccessoryEquipmentDTO accessoryEquipment);
        Task<(bool, string)> UpdateAsync(AccessoryEquipmentDTO accessoryEquipment);
        Task<(bool, string)> DeleteAsync(AccessoryEquipmentDTO accessoryEquipment);
    }
}
