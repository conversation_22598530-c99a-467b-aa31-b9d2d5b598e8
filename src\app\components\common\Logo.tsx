"use client";

import { siteConfig } from "@/config/site";
import Image from "next/image";
import Link from "next/link";

interface LogoProps {
  className?: string;
  showText?: boolean;
  imageSize?: number;
}

export default function Logo({
  className = "",
  showText = true,
  imageSize = 32,
}: LogoProps) {
  return (
    <Link
      href="/dashboard"
      className={`inline-flex items-center gap-3 cursor-pointer hover:opacity-80 transition-opacity ${className}`}
    >
      {siteConfig.logo.image && (
        <Image
          src={siteConfig.logo.image}
          alt={siteConfig.name}
          width={imageSize}
          height={imageSize}
          priority
          style={{ objectFit: "contain" }}
        />
      )}
      {showText && (
        <span
          style={{
            fontSize: "20px",
            fontWeight: "bold",
            color: "#1a1a1a",
            whiteSpace: "nowrap",
          }}
        >
          {siteConfig.name}
        </span>
      )}
    </Link>
  );
}
