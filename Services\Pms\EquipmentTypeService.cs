using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Pms
{
    public class EquipmentTypeService : IEquipmentTypeService
    {
        private readonly ERPDbContext _context;

        public EquipmentTypeService(ERPDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得設備類型資料
        /// </summary>
        /// <param name="_equipmentTypeId"></param>
        /// <returns></returns>
        public async Task<List<EquipmentTypeDTO>> GetEquipmentTypeAsync(string _equipmentTypeId = "")
        {
            var query = _context.Set<EquipmentType>().AsQueryable();

            if (!string.IsNullOrEmpty(_equipmentTypeId))
            {
                query = query.Where(u => u.EquipmentTypeId == Guid.Parse(_equipmentTypeId));
            }

            return await query.Select(u => new EquipmentTypeDTO
            {
                EquipmentTypeId = u.EquipmentTypeId,
                EquipmentTypeNo = u.EquipmentTypeNo,
                Name = u.Name,
                SortCode = u.SortCode,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                DeleteTime = u.DeleteTime,
                DeleteUserId = u.DeleteUserId
            }).ToListAsync();
        }

        /// <summary>
        /// 新增設備類型
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddEquipmentTypeAsync(EquipmentTypeDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 檢查設備類型編號是否已存在
                var existingTypeNo = await _context.Set<EquipmentType>()
                    .Where(u => u.EquipmentTypeId == _data.EquipmentTypeId && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingTypeNo != null)
                {
                    return (false, "設備類型編號已存在");
                }

                // 檢查設備類型名稱是否已存在
                var existingTypeName = await _context.Set<EquipmentType>()
                    .Where(u => u.Name == _data.Name && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingTypeName != null)
                {
                    return (false, "設備類型名稱已存在");
                }

                var entity = new EquipmentType
                {
                    EquipmentTypeId = _data.EquipmentTypeId,
                    EquipmentTypeNo = _data.EquipmentTypeNo,
                    Name = _data.Name,
                    CreateUserId = _data.CreateUserId,
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯設備類型
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditEquipmentTypeAsync(EquipmentTypeDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Set<EquipmentType>().FindAsync(_data.EquipmentTypeId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 檢查設備類型編號是否已存在
                var existingTypeNo = await _context.Set<EquipmentType>()
                    .Where(u => u.EquipmentTypeNo == _data.EquipmentTypeNo && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingTypeNo != null && existingTypeNo.EquipmentTypeId != _data.EquipmentTypeId)
                {
                    return (false, "設備類型編號已存在");
                }

                // 檢查設備類型名稱是否已存在
                var existingTypeName = await _context.Set<EquipmentType>()
                    .Where(u => u.Name == _data.Name && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingTypeName != null && existingTypeName.EquipmentTypeId != _data.EquipmentTypeId)
                {
                    return (false, "設備類型名稱已存在");
                }

                entity.EquipmentTypeId = _data.EquipmentTypeId;
                entity.EquipmentTypeNo = _data.EquipmentTypeNo;
                entity.Name = _data.Name;
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除設備類型
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteEquipmentTypeAsync(EquipmentTypeDTO _data)
        {
            try
            {
                var entity = await _context.Set<EquipmentType>().FindAsync(_data.EquipmentTypeId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得設備類型詳細資料
        /// </summary>
        /// <param name="_equipmentTypeId"></param>
        /// <returns></returns>
        public async Task<EquipmentTypeDTO> GetEquipmentTypeDetailAsync(string _equipmentTypeId)
        {
            var entity = await _context.Set<EquipmentType>().FindAsync(_equipmentTypeId);
            if (entity == null)
            {
                return null;
            }

            return new EquipmentTypeDTO
            {
                EquipmentTypeId = entity.EquipmentTypeId,
                EquipmentTypeNo = entity.EquipmentTypeNo,
                Name = entity.Name,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }
    }
}