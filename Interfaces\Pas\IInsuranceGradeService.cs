using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IInsuranceGradeService
    {
        /// <summary>
        /// 取得所有保險級距資料列表
        /// </summary>
        /// <param name="_typeid">保險類型 (1=勞保, 2=健保, 3=職災)</param>
        /// <returns>保險級距資料列表</returns>
        Task<List<InsuranceGradeDTO>> GetInsuranceGradeListAsync(int _typeid);

        /// <summary>
        /// 取得保險級距資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>保險級距資料明細</returns>
        Task<InsuranceGradeDTO> GetInsuranceGradeDetailAsync(string _uid);

        /// <summary>
        /// 新增保險級距資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddInsuranceGradeAsync(InsuranceGradeDTO _data);

        /// <summary>
        /// 編輯保險級距資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditInsuranceGradeAsync(InsuranceGradeDTO _data);

        /// <summary>
        /// 刪除保險級距資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteInsuranceGradeAsync(string _uid);

        /// <summary>
        /// 依據月投保薪資取得對應的保險級距
        /// </summary>
        /// <param name="monthlySalary">月投保薪資</param>
        /// <param name="insuranceType">保險類型</param>
        /// <returns>保險級距資料</returns>
        Task<InsuranceGradeDTO> GetInsuranceGradeBySalaryAsync(int monthlySalary, int insuranceType);

        /// <summary>
        /// 取得保險級距的員工詳細列表
        /// </summary>
        /// <param name="gradeUid">級距UID</param>
        /// <param name="status">員工狀態 (current=目前生效, pending=待生效)</param>
        /// <returns>員工詳細列表</returns>
        Task<List<InsuranceGradeEmployeeDetailDTO>> GetEmployeeInsuranceGradeDetailAsync(string gradeUid, string status);
    }
}