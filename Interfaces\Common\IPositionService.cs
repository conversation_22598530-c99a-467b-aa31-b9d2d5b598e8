﻿using FAST_ERP_Backend.Models.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IPositionService
    {
        // 取得職稱資料
        Task<List<PositionDTO>> GetPositionAsync(string _positionId = "");
        // 新增職稱
        Task<(bool, string)> AddPositionAsync(PositionDTO _data);
        // 更新職稱
        Task<(bool, string)> EditPositionAsync(PositionDTO _data);
        // 刪除職稱
        Task<(bool, string)> DeletePositionAsync(PositionDTO _data);
    }
}
