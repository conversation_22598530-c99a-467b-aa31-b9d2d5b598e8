---
description: 
globs: 
alwaysApply: false
---
# FAST ERP 後端專案概述

這是一個使用 .NET Core 開發的企業資源規劃（ERP）系統後端。

## 系統模組宣告
- `Ims` - 進銷存管理系統 (Inventory Management System)
- `Pms` - 財產管理系統 (Property Management System)
- `Pas` - 人事薪資管理系統 (Personnel and Salary Management System)

## 主要入口點
- `Program.cs` - 應用程式的主要入口點，包含服務配置和中間件設置
- `appsettings.json` - 主要配置文件
- `appsettings.Development.json` - 開發環境配置文件
- `log4net.config` - 日誌配置文件

## 核心組件
- `Attributes/` - 自訂驗證屬性
  - `MobileNumberAttribute.cs` - 台灣手機號碼驗證
  - `UniformNumberAttribute.cs` - 台灣統一編號驗證
  - `EmailAttribute.cs` - Email格式驗證
  - `PhoneNumberAttribute.cs` - 台灣市內電話驗證
- `Controllers/` - API 控制器
  - `Ims/` - 進銷存管理系統控制器
  - `Pms/` - 財產管理系統控制器
  - `Pas/` - 人事薪資管理系統控制器
  - `Common/` - 共用控制器
- `Models/` - 資料模型定義
  - `Ims/` - 進銷存管理系統模型
  - `Pms/` - 財產管理系統模型
  - `Pas/` - 人事薪資管理系統模型
  - `Common/` - 共用模型
- `Services/` - 業務邏輯服務
  - `Ims/` - 進銷存管理系統服務
  - `Pms/` - 財產管理系統服務
  - `Pas/` - 人事薪資管理系統服務
  - `Common/` - 共用服務
- `Interfaces/` - 介面定義
  - `Ims/` - 進銷存管理系統介面
  - `Pms/` - 財產管理系統介面
  - `Pas/` - 人事薪資管理系統介面
  - `Common/` - 共用介面
- `Migrations/` - 資料庫遷移文件
- `Hubs/` - SignalR 即時通訊集線器
- `Mappings/` - AutoMapper 映射配置
- `Middlewares/` - 自訂中間件
- `Tools/` - 工具類別
- `wwwroot/` - 靜態資源目錄
  - `uploads/` - 上傳文件存放目錄

## 開發工具與配置
- `Dockerfile` - 生產環境容器配置
- `Dockerfile.dev` - 開發環境容器配置
- `docker-compose.yaml` - Docker 編排配置
- `reset-ef-database.bat` - 資料庫重置工具
- `.editorconfig` - 編輯器配置
- `.gitignore` - Git 忽略配置

## 專案檔案
- `FAST_ERP_Backend.csproj` - 專案配置文件
- `FAST_ERP_Backend.sln` - 解決方案文件
- `defaultData.json` - 預設資料配置

## 資料驗證
系統實作了多種自訂驗證機制：

1. 台灣手機號碼驗證 (`MobileNumberAttribute`)
   - 驗證格式：09開頭的10位數字
   - 自動移除空格和特殊符號

2. 台灣統一編號驗證 (`UniformNumberAttribute`)
   - 驗證格式：8位數字
   - 實作最新驗證演算法
   - 支援特殊案例處理

3. Email格式驗證 (`EmailAttribute`)
   - 支援完整的 RFC 5321 標準
   - 可限制只允許特定域名（如 .edu.tw、.gov.tw 等）
   - 內建常見台灣域名驗證
   - 自動檢查：
     - Email 總長度（最大 254 字元）
     - 本地部分長度（最大 64 字元）
     - 特殊字符規則
     - 域名格式

4. 台灣市內電話驗證 (`PhoneNumberAttribute`)
   - 支援所有台灣地區區碼（2-4碼）
   - 支援電話號碼（6-8碼）
   - 支援分機號碼（可選）
   - 自動處理格式：
     - 移除空格、橫線、括號等特殊符號
     - 支援多種分機分隔符（#、,、轉）
   - 內建區碼地區對照：
     - 北部：02（台北）、03（桃園）等
     - 中部：037（苗栗）、04（台中）等
     - 南部：06（台南）、07（高雄）等
     - 東部：089（台東）等
     - 離島：082（金門）、0836（馬祖）等

## API 文件
使用 Swagger 自動生成 API 文件，並依模組分類：
- Ims API - 進銷存管理系統 API
- Pms API - 財產管理系統 API
- Pas API - 人事薪資管理系統 API
- Common API - 共用 API

