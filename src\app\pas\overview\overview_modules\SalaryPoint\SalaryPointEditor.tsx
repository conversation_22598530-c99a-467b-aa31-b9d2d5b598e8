import React, { useEffect } from "react";
import { Form, Input, InputNumber, Button, DatePicker, message, Spin, Alert, Space, Typography } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { getSalaryPointDetail, addSalaryPoint, editSalaryPoint } from "@/services/pas/SalaryPointService";
import type { SalaryPoint } from "@/services/pas/SalaryPointService";

const { Title } = Typography;

interface Props {
    uid?: string;
    onSuccess: () => void;
    onCancel?: () => void;
}

interface SalaryPointFormData extends Omit<SalaryPoint, 'effectiveDate'> {
    effectiveDate: Dayjs | null;
}

const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
};

const SalaryPointEditor: React.FC<Props> = ({ uid, onSuccess, onCancel }) => {
    const [form] = Form.useForm<SalaryPointFormData>();
    const [loading, setLoading] = React.useState(false);
    const [submitLoading, setSubmitLoading] = React.useState(false);

    useEffect(() => {
        if (!uid) {
            form.resetFields();
            return;
        }

        setLoading(true);
        getSalaryPointDetail(uid)
            .then((res) => {
                if (res.success && res.data) {
                    form.setFieldsValue({
                        ...res.data,
                        effectiveDate: res.data.effectiveDate ? dayjs(res.data.effectiveDate) : null,
                    });
                } else {
                    message.error("取得資料失敗");
                }
            })
            .finally(() => setLoading(false));
    }, [uid, form]);

    const onFinish = async (values: SalaryPointFormData) => {
        if (!values.effectiveDate) {
            message.error("請選擇生效日期");
            return;
        }

        setSubmitLoading(true);
        try {
            const payload = {
                ...values,
                effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
                uid: uid ?? undefined,
            };

            const res = uid
                ? await editSalaryPoint(payload)
                : await addSalaryPoint(payload);

            if (res.success) {
                message.success(uid ? "編輯成功" : "新增成功");
                onSuccess();
                form.resetFields();
            } else {
                message.error(res.message || "操作失敗");
            }
        } catch (error: any) {
            message.error(error.message || "操作失敗");
        } finally {
            setSubmitLoading(false);
        }
    };

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
                <Spin size="large" tip="資料載入中..." />
            </div>
        );
    }

    return (
        <>
            {uid && (
                <Alert
                    message={<strong>編輯提醒</strong>}
                    description={
                        <ul style={{ paddingLeft: '20px', marginBottom: 0 }}>
                            <li>薪點金額會依據生效日期自動生效。</li>
                            <li>同一個薪點名稱在同一天只能有一筆記錄。</li>
                            <li>系統會自動使用最新生效日期的記錄作為當前薪點金額。</li>
                        </ul>
                    }
                    type="info"
                    showIcon
                    style={{ marginBottom: 24, borderRadius: '6px' }}
                />
            )}

            <Form
                {...layout}
                form={form}
                onFinish={onFinish}
                initialValues={{ amount: 0 }}
            >
                <Form.Item
                    label="薪點名稱"
                    name="pointLevel"
                    rules={[
                        { required: true, message: "請輸入薪點名稱" },
                        { max: 20, message: "薪點名稱不可超過20個字" }
                    ]}
                >
                    <Input
                        placeholder="例如 XXX年度薪點金額"
                        maxLength={20}
                        style={{ borderRadius: '4px' }}
                    />
                </Form.Item>

                <Form.Item
                    label="薪點金額"
                    name="amount"
                    rules={[
                        { required: true, message: "請輸入薪點金額" },
                        { type: "number", min: 0, message: "金額不可小於0" }
                    ]}
                >
                    <InputNumber
                        min={0}
                        precision={2}
                        style={{ width: "100%", borderRadius: '4px' }}
                        placeholder="請輸入薪點金額"
                        formatter={(value: number | undefined) =>
                            value ? `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''
                        }
                        parser={(value: string | undefined) =>
                            value ? Number(value.replace(/\$\s?|(,*)/g, '')) : 0
                        }
                    />
                </Form.Item>

                <Form.Item
                    label="生效日期"
                    name="effectiveDate"
                    rules={[{ required: true, message: "請選擇生效日期" }]}
                    help="新的薪點金額將從此日期開始生效"
                    tooltip="選擇此薪點金額開始生效的日期"
                >
                    <DatePicker
                        style={{ width: "100%", borderRadius: '4px' }}
                        placeholder="請選擇生效日期"
                        format="YYYY-MM-DD"
                    />
                </Form.Item>

                <Form.Item
                    label="調整原因"
                    name="adjustmentReason"
                    rules={[{ max: 200, message: "調整原因不可超過200個字" }]}
                >
                    <Input.TextArea
                        rows={3}
                        placeholder="請輸入調整原因"
                        maxLength={200}
                        showCount
                        style={{ borderRadius: '4px' }}
                    />
                </Form.Item>

                <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
                    <Space size="middle">
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                            style={{
                                minWidth: '120px',
                                borderRadius: '4px',
                                height: '40px'
                            }}
                        >
                            {uid ? "儲存修改" : "新增薪點"}
                        </Button>
                        {onCancel && (
                            <Button
                                style={{
                                    minWidth: '80px',
                                    borderRadius: '4px',
                                    height: '40px'
                                }}
                                onClick={onCancel}
                            >
                                取消
                            </Button>
                        )}
                    </Space>
                </Form.Item>
            </Form>
        </>
    );
};

export default SalaryPointEditor;
