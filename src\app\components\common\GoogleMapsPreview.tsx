import { siteConfig } from "@/config/site";

/**使用 Google Maps API 顯示地圖**/

interface GoogleMapsPreviewProps {
  address: string;
  width?: number | string;
  height?: number | string;
}

const GoogleMapsPreview: React.FC<GoogleMapsPreviewProps> = ({
  address,
  width = 300,
  height = 200,
}) => {
  const encodedAddress = encodeURIComponent(address);

  return (
    <div style={{ width, height }}>
      <iframe
        width="100%"
        height="100%"
        style={{ border: "none" }}
        src={`https://www.google.com/maps/embed/v1/place?key=${siteConfig.googleMaps.apiKey}&q=${encodedAddress}&zoom=${siteConfig.googleMaps.defaultZoom}`}
        allowFullScreen
      />
    </div>
  );
};

export default GoogleMapsPreview;
