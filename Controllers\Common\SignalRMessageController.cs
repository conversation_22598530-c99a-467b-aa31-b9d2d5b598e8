﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("SignalR訊息發送")]
    public class SignalRMessageController : ControllerBase
    {
        private readonly ISignalRMessageService _interface;

        public SignalRMessageController(ISignalRMessageService signalRMessageService)
        {
            _interface = signalRMessageService;
        }

        [HttpPost]
        [Route("SendToAll")]
        [SwaggerOperation(Summary = "發送全域訊息", Description = "發送全域訊息，監聽Type:GlobalNotificationUI,Action:notice")]
        public async Task<IActionResult> SendToAllAsync(string type = "", string action = "", string message = "")
        {
            var notification = new NotificationDto
            {
                Type = (type != "") ? type : "GlobalNotificationUI",
                Action = (action != "") ? action : "notice",
                Data = new { Message = (message != "") ? message : "這是全域訊息" }
            };
            var (result, msg) = await _interface.SendToAllAsync(notification);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("SendToGroup")]
        [SwaggerOperation(Summary = "發送群組訊息", Description = "發送群組訊息，預設監聽Type:GlobalNotificationUI,Action:notice")]
        public async Task<IActionResult> SendToGroupAsync(string userId, string type = "", string action = "", string message = "")
        {
            var notification = new NotificationDto
            {
                Type = (type != "") ? type : "GlobalNotificationUI",
                Action = (action != "") ? action : "notice",
                Data = new { Message = (message != "") ? message : "這是群組ID訊息" }
            };
            var (result, msg) = await _interface.SendToUserIdGroupAsync(notification, userId);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("SendToConnectionId")]
        [SwaggerOperation(Summary = "發送個人訊息", Description = "發送個人訊息，監聽Type:GlobalNotificationUI,Action:notice")]
        public async Task<IActionResult> SendToConnectionIdAsync(string connectionId, string type = "", string action = "", string message = "")
        {
            var notification = new NotificationDto
            {
                Type = (type != "") ? type : "GlobalNotificationUI",
                Action = (action != "") ? action : "notice",
                Data = new { Message = (message != "") ? message : "這是個人連線ID訊息" }
            };
            var (result, msg) = await _interface.SendToConnectionIdAsync(notification, connectionId);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("SendTest")]
        [SwaggerOperation(Summary = "發送測試訊息", Description = "發送測試動作訊息，監聽Type:msgUI,Action:newmsg")]
        public async Task<IActionResult> SendTestMessageAsync(string user, string message, string _connectionId = "")
        {
            var (result, msg) = await _interface.SendTestMessageAsync(user, message, _connectionId);
            return Ok(new { result, msg });
        }
    }
}

