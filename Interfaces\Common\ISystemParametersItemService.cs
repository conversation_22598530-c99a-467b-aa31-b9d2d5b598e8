﻿using FAST_ERP_Backend.Models.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface ISystemParametersItemService
    {
        Task<List<SystemParametersItemDTO>> GetSystemParametersItemAsync(string SystemParametersId = "");
        Task<(bool, string)> AddSystemParametersItemAsync(SystemParametersItemDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> EditSystemParametersItemAsync(SystemParametersItemDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> DeleteSystemParametersItemAsync(SystemParametersItemDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> RestoreSystemParametersItemAsync(SystemParametersItemDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> DeleteBySystemParameterIdAsync(string SystemParametersId = "",String tokenUid="", long DeleteTime = 0);
        Task<(bool, string)> RestoreBySystemParameterIdAsync(string SystemParametersId = "",String tokenUid="", long DeleteTime = 0);
    }
}
