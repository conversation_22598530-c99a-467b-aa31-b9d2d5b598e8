'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { InputNumber, Tooltip } from 'antd';
import type { InputNumberProps } from 'antd';

/**
 * TestDataCountInput 組件的 Props 接口
 */
export interface TestDataCountInputProps {
  /** 當前值，支持 number 或 null */
  value?: number | null;
  
  /** 最小值，預設為 1 */
  min?: number;
  
  /** 最大值，預設為 50000 */
  max?: number;
  
  /** 預設值，當值為空時顯示的預設值 */
  defaultValue?: number;
  
  /** 佔位符文字 */
  placeholder?: string;
  
  /** 是否禁用 */
  disabled?: boolean;
  
  /** 自訂樣式 */
  style?: React.CSSProperties;
  
  /** 自訂 CSS 類名 */
  className?: string;
  
  /** 是否顯示提示文字 */
  showHint?: boolean;
  
  /** 自訂提示文字 */
  hintText?: string;
  
  /** 失去焦點時的回調 */
  onBlur?: (value: number | null) => void;
  
  /** 獲得焦點時的回調 */
  onFocus?: (value: number | null) => void;
  
  /** 值變更回調函數 */
  onChange?: (value: number | null) => void;
}

/**
 * 測試資料數量輸入組件
 * 
 * 解決了以下問題：
 * 1. 支持用戶清空輸入框而不自動重置為預設值
 * 2. 支持自訂數量輸入並在失去焦點後保持該值
 * 3. 包含數量範圍驗證
 * 4. 支持數字格式化顯示（千分位逗號）
 * 5. 提供清晰的 TypeScript 類型安全
 */
const TestDataCountInput: React.FC<TestDataCountInputProps> = ({
  value,
  min = 1,
  max = 50000,
  defaultValue = 20000,
  placeholder,
  disabled = false,
  style,
  className,
  showHint = true,
  hintText,
  onBlur,
  onFocus,
  onChange,
}) => {
  // 內部狀態管理
  const [internalValue, setInternalValue] = useState<number | null>(value ?? null);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<any>(null);

  // 同步外部 value 變化
  useEffect(() => {
    setInternalValue(value ?? null);
  }, [value]);

  /**
   * 數字格式化函數 - 添加千分位逗號
   */
  const formatNumber = useCallback((num: number | string | undefined): string => {
    if (num === undefined || num === null || num === '') {
      return '';
    }
    return String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }, []);

  /**
   * 解析格式化的數字字符串
   */
  const parseNumber = useCallback((str: string | undefined): number | undefined => {
    if (!str || str.trim() === '') {
      return undefined;
    }

    // 移除所有非數字字符（逗號、空格、$符號等）
    const cleanStr = str.replace(/[^\d]/g, '');
    if (cleanStr === '') {
      return undefined;
    }

    const parsed = parseInt(cleanStr, 10);
    return isNaN(parsed) ? undefined : parsed;
  }, []);

  /**
   * 驗證數值是否在有效範圍內
   */
  const validateValue = useCallback((val: number): number => {
    if (val < min) return min;
    if (val > max) return max;
    return val;
  }, [min, max]);

  /**
   * 處理值變更
   */
  const handleChange = useCallback((newValue: number | string | null) => {
    console.log('🔄 TestDataCountInput: 值變更', { newValue, currentValue: internalValue });

    let numericValue: number | null;
    if (typeof newValue === 'string') {
      numericValue = parseNumber(newValue) ?? null;
    } else {
      numericValue = newValue;
    }
    
    setInternalValue(numericValue);
    
    // 通知外部組件
    if (onChange) {
      onChange(numericValue);
    }
  }, [internalValue, onChange, parseNumber]);

  /**
   * 處理失去焦點事件
   */
  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    
    const inputValue = e.target.value;
    console.log('👋 TestDataCountInput: 失去焦點', { inputValue, internalValue });
    
    // 如果輸入框為空，保持空值狀態
    if (!inputValue || inputValue.trim() === '') {
      console.log('📝 TestDataCountInput: 保持空值狀態');
      handleChange(null);
      if (onBlur) onBlur(null);
      return;
    }
    
    // 解析並驗證輸入值
    const parsed = parseNumber(inputValue);
    if (parsed !== undefined) {
      const validated = validateValue(parsed);
      console.log('✅ TestDataCountInput: 驗證後的值', { parsed, validated });
      handleChange(validated);
      if (onBlur) onBlur(validated);
    } else {
      // 無效輸入，重置為最小值
      console.log('❌ TestDataCountInput: 無效輸入，重置為最小值', min);
      handleChange(min);
      if (onBlur) onBlur(min);
    }
  }, [internalValue, parseNumber, validateValue, handleChange, onBlur, min]);

  /**
   * 處理獲得焦點事件
   */
  const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    console.log('👀 TestDataCountInput: 獲得焦點', { value: internalValue });
    
    if (onFocus) {
      onFocus(internalValue);
    }
  }, [internalValue, onFocus]);

  // 生成提示文字
  const getHintText = useCallback((): string => {
    if (hintText) return hintText;
    return `建議範圍：${min.toLocaleString()}-${max.toLocaleString()} 筆，預設 ${defaultValue.toLocaleString()} 筆`;
  }, [hintText, min, max, defaultValue]);

  // 生成佔位符文字
  const getPlaceholder = useCallback((): string => {
    if (placeholder) return placeholder;
    return `請輸入要產生的數量 (${min.toLocaleString()}-${max.toLocaleString()})`;
  }, [placeholder, min, max]);

  // 為 InputNumber 適配的 parser 函數
  const inputNumberParser = useCallback((displayValue: string | undefined): number => {
    const parsed = parseNumber(displayValue);
    return parsed ?? 0; // InputNumber 的 parser 必須返回 number
  }, [parseNumber]);

  // InputNumber 的 props
  const inputNumberProps: Omit<InputNumberProps, 'ref'> = {
    min,
    max,
    value: internalValue,
    onChange: handleChange,
    onBlur: handleBlur,
    onFocus: handleFocus,
    formatter: formatNumber,
    parser: inputNumberParser,
    placeholder: getPlaceholder(),
    disabled,
    style: { width: '100%', ...style },
    className,
  };

  return (
    <div className="test-data-count-input">
      <Tooltip 
        title={isFocused ? '輸入數量後按 Enter 或點擊其他地方確認' : getPlaceholder()}
        placement="top"
      >
        <InputNumber ref={inputRef} {...inputNumberProps} />
      </Tooltip>
      
      {showHint && (
        <div 
          style={{ 
            fontSize: '12px', 
            color: '#8c8c8c', 
            marginTop: '4px',
            lineHeight: '1.4'
          }}
        >
          {getHintText()}
        </div>
      )}
      
      <style jsx>{`
        .test-data-count-input {
          width: 100%;
        }
        
        .test-data-count-input .ant-input-number {
          transition: all 0.3s ease;
        }
        
        .test-data-count-input .ant-input-number:hover {
          border-color: #40a9ff;
        }
        
        .test-data-count-input .ant-input-number:focus,
        .test-data-count-input .ant-input-number-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .test-data-count-input .ant-input-number-input {
          text-align: left;
        }
      `}</style>
    </div>
  );
};

export default TestDataCountInput;
