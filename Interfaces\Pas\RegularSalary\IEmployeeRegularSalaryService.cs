using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IEmployeeRegularSalaryService
    {
        Task<List<EmployeeRegularSalaryDTO>> GetByUserIdAsync(string userId);
        Task<EmployeeRegularSalaryDTO?> GetByIdAsync(string uid);
        Task<(bool, string)> AddAsync(EmployeeRegularSalaryDTO dto);
        Task<(bool, string)> EditAsync(EmployeeRegularSalaryDTO dto);
        Task<(bool, string)> DeleteAsync(string uid);

    }

}
