"use client";

import React, { useEffect, useState, Suspense } from "react";
import { Card, Tabs, List, Badge, Typography, Space, Button } from "antd";
import { BellOutlined, CheckOutlined } from "@ant-design/icons";
import {
  Message,
  getUnreadMessages,
  getReadMessages,
  markMessageAsRead,
} from "@/services/common/messageService";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { notifySuccess, notifyError } from "@/utils/notification";
import { useSearchParams, useRouter } from "next/navigation";

const { Text } = Typography;

// 创建一个内部组件来使用 useSearchParams
function MessagesContent() {
  const [unreadMessages, setUnreadMessages] = useState<Message[]>([]);
  const [readMessages, setReadMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const activeTab = searchParams.get("tab") || "unread";

  // 加載訊息
  const loadMessages = async () => {
    setLoading(true);
    try {
      const [unreadResponse, readResponse] = await Promise.all([
        getUnreadMessages(),
        getReadMessages(),
      ]);

      if (unreadResponse.success && unreadResponse.data) {
        setUnreadMessages(unreadResponse.data);
      }
      if (readResponse.success && readResponse.data) {
        setReadMessages(readResponse.data);
      }
    } catch (error) {
      notifyError("載入訊息失敗");
    } finally {
      setLoading(false);
    }
  };

  // 處理頁籤切換
  const handleTabChange = (key: string) => {
    router.push(`/messages?tab=${key}`);
  };

  // 標記訊息為已讀
  const handleMarkAsRead = async (messageId: string) => {
    try {
      const response = await markMessageAsRead(messageId);
      if (response.success) {
        notifySuccess("訊息已標記為已讀");
        await loadMessages(); // 重新加載訊息
      } else {
        notifyError(response.message || "標記訊息失敗");
      }
    } catch (error) {
      notifyError("標記訊息失敗");
    }
  };

  useEffect(() => {
    loadMessages();
  }, []);

  // 訊息列表項目渲染
  const renderMessageItem = (message: Message, isUnread: boolean) => (
    <List.Item
      actions={[
        isUnread && (
          <Button
            type="link"
            icon={<CheckOutlined />}
            onClick={() => handleMarkAsRead(message.messageId)}
          >
            標記已讀
          </Button>
        ),
      ].filter(Boolean)}
    >
      <List.Item.Meta
        title={
          <Space>
            {isUnread && <Badge status="processing" />}
            <Text strong>{message.title}</Text>
          </Space>
        }
        description={
          <div>
            <div>{message.content}</div>
            <Text type="secondary">
              {DateTimeExtensions.formatFromTimestamp(message.createTime)}
            </Text>
          </div>
        }
      />
    </List.Item>
  );

  const items = [
    {
      key: "unread",
      label: (
        <span>
          <BellOutlined />
          未讀訊息
          <Badge count={unreadMessages.length} style={{ marginLeft: 8 }} />
        </span>
      ),
      children: (
        <List
          loading={loading}
          dataSource={unreadMessages}
          renderItem={(message) => renderMessageItem(message, true)}
        />
      ),
    },
    {
      key: "read",
      label: (
        <span>
          <CheckOutlined />
          已讀訊息
        </span>
      ),
      children: (
        <List
          loading={loading}
          dataSource={readMessages}
          renderItem={(message) => renderMessageItem(message, false)}
        />
      ),
    },
    {
      key: "all",
      label: (
        <span>
          <CheckOutlined />
          所有訊息
        </span>
      ),
      children: (
        <List
          loading={loading}
          dataSource={[...unreadMessages, ...readMessages]}
          renderItem={(message) => renderMessageItem(message, !message.isRead)}
        />
      ),
    },
  ];

  return (
    <Card>
      <Tabs items={items} activeKey={activeTab} onChange={handleTabChange} />
    </Card>
  );
}

// 加載中的占位組件
function LoadingFallback() {
  return (
    <Card>
      <div>載入中...</div>
    </Card>
  );
}

const MessagesPage: React.FC = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <MessagesContent />
    </Suspense>
  );
};

export default MessagesPage;
