using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("攤提來源管理")]
    public class AmortizationSourceController : ControllerBase
    {
        private readonly IAmortizationSourceService _amortizationSourceService;

        public AmortizationSourceController(IAmortizationSourceService amortizationSourceService)
        {
            _amortizationSourceService = amortizationSourceService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得攤提來源列表", Description = "取得所有攤提來源列表")]
        public async Task<IActionResult> GetAmortizationSourceList()
        {
            var sources = await _amortizationSourceService.GetAllAsync();
            return Ok(sources);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得攤提來源明細", Description = "依ID取得攤提來源明細")]
        public async Task<IActionResult> GetAmortizationSourceDetail(Guid id)
        {
            var source = await _amortizationSourceService.GetByIdAsync(id);
            if (source == null)
                return NotFound($"找不到編號為{id}的攤提來源。");

            return Ok(source);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增攤提來源", Description = "新增攤提來源")]
        public async Task<IActionResult> AddAmortizationSource([FromBody] AmortizationSourceDTO amortizationSource)
        {
            var result = await _amortizationSourceService.AddAsync(amortizationSource);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯攤提來源", Description = "編輯攤提來源")]
        public async Task<IActionResult> EditAmortizationSource([FromBody] AmortizationSourceDTO amortizationSource)
        {
            var result = await _amortizationSourceService.UpdateAsync(amortizationSource);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除攤提來源", Description = "刪除攤提來源")]
        public async Task<IActionResult> DeleteAmortizationSource([FromBody] AmortizationSourceDTO amortizationSource)
        {
            var result = await _amortizationSourceService.DeleteAsync(amortizationSource);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }
    }
}