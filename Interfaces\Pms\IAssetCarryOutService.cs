using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    /// <summary>
    /// 資產攜出作業服務介面
    /// </summary>
    public interface IAssetCarryOutService
    {
        /// <summary>
        /// 取得攜出申請列表
        /// </summary>
        /// <param name="status">狀態篩選</param>
        /// <param name="applicantId">申請人篩選</param>
        /// <param name="assetId">財產篩選</param>
        /// <returns>攜出申請列表</returns>
        Task<List<AssetCarryOutDTO>> GetCarryOutListAsync(string? status = null, string? applicantId = null, Guid? assetId = null);

        /// <summary>
        /// 取得攜出申請詳細資料
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <returns>攜出申請詳細資料</returns>
        Task<AssetCarryOutDTO?> GetCarryOutDetailAsync(string carryOutNo);

        /// <summary>
        /// 新增攜出申請
        /// </summary>
        /// <param name="carryOutDto">攜出申請資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> CreateCarryOutApplicationAsync(AssetCarryOutDTO carryOutDto);

        /// <summary>
        /// 修改攜出申請
        /// </summary>
        /// <param name="carryOutDto">攜出申請資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> UpdateCarryOutApplicationAsync(AssetCarryOutDTO carryOutDto);

        /// <summary>
        /// 刪除攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="userId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> DeleteCarryOutApplicationAsync(string carryOutNo, string userId);

        /// <summary>
        /// 審核攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="isApproved">是否核准</param>
        /// <param name="approverId">審核人員</param>
        /// <param name="comment">審核意見</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> ApproveCarryOutApplicationAsync(string carryOutNo, bool isApproved, string approverId, string? comment = null);

        /// <summary>
        /// 登記攜出
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="actualCarryOutDate">實際攜出日期</param>
        /// <param name="operatorId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> RegisterCarryOutAsync(string carryOutNo, long actualCarryOutDate, string operatorId);

        /// <summary>
        /// 登記歸還
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="actualReturnDate">實際歸還日期</param>
        /// <param name="operatorId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> RegisterReturnAsync(string carryOutNo, long actualReturnDate, string operatorId);

        /// <summary>
        /// 批次處理攜出申請
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        Task<(bool success, string message)> BatchProcessAsync(AssetCarryOutBatchDTO batchDto);

        /// <summary>
        /// 取得攜出統計資料
        /// </summary>
        /// <param name="userId">使用者ID (可選，用於個人統計)</param>
        /// <returns>統計資料</returns>
        Task<AssetCarryOutStatisticsDTO> GetStatisticsAsync(string? userId = null);

        /// <summary>
        /// 產生攜出申請單號
        /// </summary>
        /// <returns>攜出申請單號</returns>
        Task<string> GenerateCarryOutNoAsync();

        /// <summary>
        /// 檢查逾期未還的資產
        /// </summary>
        /// <returns>逾期未還的攜出申請列表</returns>
        Task<List<AssetCarryOutDTO>> CheckOverdueAssetsAsync();
    }
}