using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 廠商修繕作業實體
    /// </summary>
    [Table("VendorMaintenance")]
    public class VendorMaintenance : ModelBaseEntity
    {
        /// <summary>
        /// 修繕單號 (主鍵)
        /// </summary>
        [Key]
        [StringLength(20)]
        [Comment("修繕單號")]
        public string MaintenanceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 財產編號 (外鍵)
        /// </summary>
        [Required]
        [Comment("財產流水號")]
        public Guid AssetId { get; set; } = Guid.Empty;

        /// <summary>
        /// 申請人編號
        /// </summary>
        [Required]
        [Comment("申請人編號")]
        [Column(TypeName = "nvarchar(100)")]
        [ForeignKey("UserId")]
        public string ApplicantId { get; set; } = string.Empty;

        /// <summary>
        /// 申請部門
        /// </summary>
        [Required]
        [Column(TypeName = "nvarchar(100)")]
        [Comment("申請部門編號")]
        public string DepartmentId { get; set; } = string.Empty;

        /// <summary>
        /// 申請日期
        /// </summary>
        [Required]
        [Comment("申請日期")]
        public long ApplicationDate { get; set; }

        /// <summary>
        /// 故障描述
        /// </summary>
        [Required]
        [StringLength(1000)]
        [Comment("故障描述")]
        public string FaultDescription { get; set; } = string.Empty;

        /// <summary>
        /// 修繕類型 (維修/保養/更換/升級)
        /// </summary>
        [Required]
        [StringLength(20)]
        [Comment("修繕類型")]
        public string MaintenanceType { get; set; } = string.Empty;

        /// <summary>
        /// 緊急程度 (一般/緊急/非常緊急)
        /// </summary>
        [Required]
        [StringLength(20)]
        [Comment("緊急程度")]
        public string UrgencyLevel { get; set; } = string.Empty;

        /// <summary>
        /// 預估修繕費用
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        [Comment("預估修繕費用")]
        public decimal EstimatedCost { get; set; }

        /// <summary>
        /// 廠商名稱
        /// </summary>
        [StringLength(100)]
        [Comment("廠商名稱")]
        public string? VendorName { get; set; }

        /// <summary>
        /// 廠商聯絡人
        /// </summary>
        [StringLength(50)]
        [Comment("廠商聯絡人")]
        public string? VendorContact { get; set; }

        /// <summary>
        /// 廠商聯絡電話
        /// </summary>
        [StringLength(20)]
        [Comment("廠商聯絡電話")]
        public string? VendorPhone { get; set; }

        /// <summary>
        /// 預計開始日期
        /// </summary>
        public long ScheduledStartDate { get; set; }

        /// <summary>
        /// 預計完成日期
        /// </summary>
        public long ScheduledEndDate { get; set; }

        /// <summary>
        /// 實際開始日期
        /// </summary>
        public long ActualStartDate { get; set; }

        /// <summary>
        /// 實際完成日期
        /// </summary>
        public long ActualEndDate { get; set; }

        /// <summary>
        /// 實際修繕費用
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ActualCost { get; set; }

        /// <summary>
        /// 修繕結果描述
        /// </summary>
        [StringLength(1000)]
        public string? MaintenanceResult { get; set; }

        /// <summary>
        /// 驗收人編號
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        [Comment("驗收人編號")]
        [ForeignKey("UserId")]
        public string? InspectorId { get; set; }

        /// <summary>
        /// 驗收日期
        /// </summary>
        public long InspectionDate { get; set; }

        /// <summary>
        /// 驗收結果 (合格/不合格/待驗收)
        /// </summary>
        [StringLength(20)]
        public string? InspectionResult { get; set; }

        /// <summary>
        /// 驗收備註
        /// </summary>
        [StringLength(500)]
        public string? InspectionNotes { get; set; }

        /// <summary>
        /// 狀態
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = VendorMaintenanceStatus.PENDING;

        /// <summary>
        /// 備註
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// 審核人編號
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        [Comment("審核人編號")]
        [ForeignKey("UserId")]
        public string? ApproverId { get; set; }

        /// <summary>
        /// 審核日期
        /// </summary>
        public long ApprovalDate { get; set; }

        /// <summary>
        /// 審核意見
        /// </summary>
        [StringLength(500)]
        public string? ApprovalComment { get; set; }

        // 導航屬性
        /// <summary>
        /// 資產資訊
        /// </summary>
        [ForeignKey("AssetId")]
        public virtual Asset? Asset { get; set; }
    }

    /// <summary>
    /// 廠商修繕作業 DTO
    /// </summary>
    public class VendorMaintenanceDTO
    {
        public string MaintenanceNumber { get; set; } = string.Empty;
        public Guid AssetId { get; set; } = Guid.Empty;
        public string ApplicantId { get; set; } = string.Empty;
        public string DepartmentId { get; set; } = string.Empty;
        public long ApplicationDate { get; set; }
        public string FaultDescription { get; set; } = string.Empty;
        public string MaintenanceType { get; set; } = string.Empty;
        public string UrgencyLevel { get; set; } = string.Empty;
        public decimal EstimatedCost { get; set; }
        public string? VendorName { get; set; }
        public string? VendorContact { get; set; }
        public string? VendorPhone { get; set; }
        public long ScheduledStartDate { get; set; }
        public long ScheduledEndDate { get; set; }
        public long ActualStartDate { get; set; }
        public long ActualEndDate { get; set; }
        public decimal? ActualCost { get; set; }
        public string? MaintenanceResult { get; set; }
        public string? InspectorId { get; set; }
        public long InspectionDate { get; set; }
        public string? InspectionResult { get; set; }
        public string? InspectionNotes { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public long? CreateTime { get; set; }
        public string? CreateUserId { get; set; }
        public long? UpdateTime { get; set; }
        public string? UpdateUserId { get; set; }
    }

    /// <summary>
    /// 廠商修繕狀態常數
    /// </summary>
    public static class VendorMaintenanceStatus
    {
        public const string PENDING = "PENDING";           // 待審核
        public const string APPROVED = "APPROVED";         // 已核准
        public const string REJECTED = "REJECTED";         // 已拒絕
        public const string ASSIGNED = "ASSIGNED";         // 已指派廠商
        public const string IN_PROGRESS = "IN_PROGRESS";   // 施工中
        public const string COMPLETED = "COMPLETED";       // 已完成
        public const string INSPECTED = "INSPECTED";       // 已驗收
        public const string CLOSED = "CLOSED";             // 已結案
        public const string CANCELLED = "CANCELLED";       // 已取消
    }

    /// <summary>
    /// 修繕類型常數
    /// </summary>
    public static class MaintenanceType
    {
        public const string REPAIR = "REPAIR";             // 維修
        public const string MAINTENANCE = "MAINTENANCE";   // 保養
        public const string REPLACEMENT = "REPLACEMENT";   // 更換
        public const string UPGRADE = "UPGRADE";           // 升級
    }

    /// <summary>
    /// 緊急程度常數
    /// </summary>
    public static class UrgencyLevel
    {
        public const string NORMAL = "NORMAL";             // 一般
        public const string URGENT = "URGENT";             // 緊急
        public const string CRITICAL = "CRITICAL";         // 非常緊急
    }

    /// <summary>
    /// 驗收結果常數
    /// </summary>
    public static class InspectionResult
    {
        public const string PENDING = "PENDING";           // 待驗收
        public const string PASSED = "PASSED";             // 合格
        public const string FAILED = "FAILED";             // 不合格
    }

    /// <summary>
    /// 廠商修繕批次處理 DTO
    /// </summary>
    public class VendorMaintenanceBatchDTO
    {
        // 修繕單號
        public List<string> MaintenanceNumbers { get; set; } = new List<string>();
        // 操作類型
        public string Action { get; set; } = string.Empty;
        // 操作原因
        public string? Reason { get; set; }
        // 操作人編號
        public string OperatorId { get; set; } = string.Empty;
        // 操作人姓名
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 廠商修繕統計 DTO
    /// </summary>
    public class VendorMaintenanceStatisticsDTO
    {
        // 總數
        public int TotalCount { get; set; }
        // 待審核
        public int PendingCount { get; set; }
        // 已核准
        public int ApprovedCount { get; set; }
        // 施工中
        public int InProgressCount { get; set; }
        // 已完成
        public int CompletedCount { get; set; }
        // 已驗收
        public int InspectedCount { get; set; }
        // 已結案
        public int ClosedCount { get; set; }
        // 總預估修繕費用
        public decimal TotalEstimatedCost { get; set; }
        // 總實際修繕費用
        public decimal TotalActualCost { get; set; }
        // 平均完成天數
        public double AverageCompletionDays { get; set; }
        // 修繕類型統計
        public List<MaintenanceTypeStatistic> TypeStatistics { get; set; } = new List<MaintenanceTypeStatistic>();
        // 緊急程度統計
        public List<UrgencyLevelStatistic> UrgencyStatistics { get; set; } = new List<UrgencyLevelStatistic>();
    }

    /// <summary>
    /// 修繕類型統計
    /// </summary>
    public class MaintenanceTypeStatistic
    {
        // 修繕類型
        public string Type { get; set; } = string.Empty;
        // 修繕類型數量
        public int Count { get; set; }
        // 修繕類型總費用
        public decimal TotalCost { get; set; }
    }

    /// <summary>
    /// 緊急程度統計
    /// </summary>
    public class UrgencyLevelStatistic
    {
        // 緊急程度
        public string Level { get; set; } = string.Empty;
        // 緊急程度數量
        public int Count { get; set; }
        // 緊急程度平均完成天數
        public double AverageCompletionDays { get; set; }
    }
}