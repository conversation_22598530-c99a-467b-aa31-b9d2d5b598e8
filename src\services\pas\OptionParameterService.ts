import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 學歷資料
export interface SelectOption {
    optionValue: string;
    optionText: string;
}

export const createEmptySelectOption = (): SelectOption => ({
    optionValue: "",
    optionText: "",
});

// 搜尋職稱選項列表
export async function getJobtitleOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getJobtitleOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋職稱選項列表失敗",
        };
    }
}

// 搜尋證號別選項列表
export async function getIdTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getIdTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋證號別選項列表失敗",
        };
    }
}

// 搜尋證號錯誤註記選項列表
export async function getIdErrorOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getIdErrorOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋證號錯誤註記選項列表失敗",
        };
    }
}

// 搜尋血型選項列表
export async function getBloodTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getBloodTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋血型選項列表失敗",
        };
    }
}

// 搜尋學位選項列表
export async function getDegreeTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDegreeTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋學位選項列表失敗",
        };
    }
}

// 搜尋結業選項列表
export async function getGraduateOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getGraduateOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋結業選項列表失敗",
        };
    }
}

// 搜尋留停類型選項列表
export async function getSuspendTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getSuspendTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋留停類型選項列表失敗",
        };
    }
}

// 搜尋留停種類選項列表
export async function getSuspendKindOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getSuspendKindOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋留停種類選項列表失敗",
        };
    }
}

// 取得員工自提額類型選項
export async function getEmployeeContributionOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getEmployeeContributionOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得員工自提額類型選項失敗",
        };
    }
}

// 取得計稅型式類型選項
export async function getIncomeTaxTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getIncomeTaxTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得計稅型式類型選項失敗",
        };
    }
}

// 取得發薪狀況選項
export async function getPayoffTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPayoffTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得發薪狀況選項失敗",
        };
    }
}

// 取得發薪狀況選項
export async function getDepTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得稱謂關係選項失敗",
        };
    }
}

// 取得薪資項目類型選項
export async function getRegularSalaryCreaseTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getRegularSalaryCreaseTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得薪資項目類型選項失敗",
        };
    }
}

// 取得任用資格選項
export async function getJobroleTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getJobroleTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得任用資格選項失敗",
        };
    }
}

// 取得薪俸類型選項
export async function getSalaryTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getSalaryTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得薪俸類型選項失敗",
        };
    }
}

// 取得錄用類別選項
export async function getCategoryTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getCategoryTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得錄用類別選項失敗",
        };
    }
}

// 取得職等選項
export async function getJobLevelOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getJobLevelOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得職等選項失敗",
        };
    }
}

// 取得級數選項
export async function getJobRankOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getJobRankOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得級數選項失敗",
        };
    }
}

// 取得升遷類型選項
export async function getPromotionTypeOptions(): Promise<ApiResponse<SelectOption[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPromotionTypeOptions}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得升遷類型選項失敗",
        };
    }
}






