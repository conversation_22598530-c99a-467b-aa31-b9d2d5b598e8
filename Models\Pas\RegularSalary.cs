﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 常態薪資選項資料表
    /// </summary>
    public class RegularSalaryItem : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("項目名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string itemName { get; set; } // 項目名稱

        [Comment("加減項類型")]
        [Column(TypeName = "nvarchar(1)")]
        public string itemType { get; set; } // 加減項類型 (1:加項,2:減項)

        [Comment("扣稅類型")]
        [Column(TypeName = "bit")]
        public bool isTaxable { get; set; } // 扣稅類型 (true:計算扣稅,false:不扣稅)

        [Comment("項目描述")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string description { get; set; } // 項目描述

        [Comment("項目啟用")]
        [Column(TypeName = "bit")]
        public bool isEnable { get; set; } // 項目啟用 (true:啟用,false:不啟用)

        public RegularSalaryItem()
        {
            uid = "";
            itemName = "";
            itemType = "";
            isTaxable = false;
            description = "";
            isEnable = true;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 常態薪資選項資料 DTO
    /// </summary>
    public class RegularSalaryItemDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string itemName { get; set; } // 項目名稱
        public string itemType { get; set; } // 加減項類型
        public string? itemTypeName => itemType == "1" ? "加項" : "減項"; // 加減項類型名稱
        public bool isTaxable { get; set; } // 扣稅類型
        public string? isTaxableName { get; set; } // 扣稅類型名稱
        public string description { get; set; } // 項目描述 
        public bool isEnable { get; set; } // 項目啟用
        public string? isEnableName { get; set; } // 項目啟用名稱

        public RegularSalaryItemDTO()
        {
            uid = "";
            itemName = "";
            itemType = ((int)SalaryItemType.Add).ToString(); // 預設為 "1"
            isTaxable = false;
            isTaxableName = "";
            description = "";
            isEnable = true;
            isEnableName = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }


    /// <summary>
    /// 員工常態薪資資料表
    /// </summary>
    public class EmployeeRegularSalary : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("員工userid")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 員工userid

        [Comment("常態薪資資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string salaryItemUid { get; set; } // 常態薪資資料編號

        [Comment("設定金額")]
        [Column(TypeName = "decimal(8,0)")]
        public decimal amount { get; set; } // 設定金額

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string remark { get; set; } // 備註

        [Comment("個別項目啟用")]
        [Column(TypeName = "bit")]
        public bool isEnable { get; set; } // 個別項目啟用 (true:啟用,false:不啟用) 以RegularSalaryItem之isEnable優先 兩者都true才計算此常態加減薪.

        public EmployeeRegularSalary()
        {
            uid = "";
            userId = "";
            salaryItemUid = "";
            amount = 0M;
            remark = "";
            isEnable = true;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 員工常態薪資資料DTO
    /// </summary>
    public class EmployeeRegularSalaryDTO : ModelBaseEntity
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 員工userid
        public string salaryItemUid { get; set; } // 關聯常態薪資資料編號
        public string? salaryItemName { get; set; } // 關聯常態薪資資料名稱
        public string amount { get; set; } // 設定金額
        public string remark { get; set; } // 備註
        public bool isEnable { get; set; } // 個別項目啟用  (true:啟用,false:不啟用) 以RegularSalaryItem之isEnable優先 兩者都true才計算此常態加減薪.
        public string itemType { get; set; } // 加減項類型
        public string? itemTypeName => itemType == "1" ? "加項" : "減項"; // 加減項類型名稱

        public EmployeeRegularSalaryDTO()
        {
            uid = "";
            userId = "";
            salaryItemUid = "";
            salaryItemName = "";
            amount = "0";
            remark = "";
            isEnable = true;
            itemType = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public enum SalaryItemType
    {
        Add = 1,
        Subtract = 2
    }
}

