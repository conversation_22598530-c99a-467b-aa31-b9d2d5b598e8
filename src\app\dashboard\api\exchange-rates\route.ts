import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
    try {
        // 代理請求臺灣銀行匯率 API
        const response = await fetch('https://rate.bot.com.tw/xrt?Lang=zh-TW', {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const html = await response.text();

        // 解析 HTML 提取匯率資料
        const exchangeRates = parseExchangeRates(html);

        return NextResponse.json({
            success: true,
            data: exchangeRates,
            timestamp: new Date().toISOString(),
        });

    } catch (error) {
        console.error('獲取匯率資料失敗:', error);
        return NextResponse.json({
            success: false,
            data: [],
            timestamp: new Date().toISOString(),
            message: '獲取匯率資料失敗',
            error: error instanceof Error ? error.message : '未知錯誤',
        });
    }
}

function parseExchangeRates(html: string) {
    const exchangeRates: any[] = [];

    // 貨幣對照表
    const currencyMap: { [key: string]: { name: string; flag: string } } = {
        'USD': { name: '美元', flag: '🇺🇸' },
        'HKD': { name: '港幣', flag: '🇭🇰' },
        'GBP': { name: '英鎊', flag: '🇬🇧' },
        'EUR': { name: '歐元', flag: '🇪🇺' },
        'JPY': { name: '日圓', flag: '🇯🇵' },
        'CNY': { name: '人民幣', flag: '🇨🇳' },
        'AUD': { name: '澳幣', flag: '🇦🇺' },
        'CAD': { name: '加拿大幣', flag: '🇨🇦' },
        'SGD': { name: '新加坡幣', flag: '🇸🇬' },
        'CHF': { name: '瑞士法郎', flag: '🇨🇭' },
        'NZD': { name: '紐元', flag: '🇳🇿' },
        'THB': { name: '泰幣', flag: '🇹🇭' },
    };

    try {
        // 更精確的正則表達式來解析臺銀表格
        // 查找包含貨幣代碼的行
        const currencyRowRegex = /幣別國旗"[^>]*>\s*([^(]+)\s*\(([A-Z]{3})\)[^<]*<[^>]*>\s*([0-9]+(?:\.[0-9]+)?)\s*([0-9]+(?:\.[0-9]+)?)\s*([0-9]+(?:\.[0-9]+)?)\s*([0-9]+(?:\.[0-9]+)?)/g;

        let match;
        while ((match = currencyRowRegex.exec(html)) !== null) {
            const [, currencyName, currency, buyCash, sellCash, buySpot, sellSpot] = match;

            if (currencyMap[currency]) {
                exchangeRates.push({
                    currency,
                    currencyName: currencyMap[currency].name,
                    flag: currencyMap[currency].flag,
                    buyCash: parseFloat(buyCash) || 0,
                    sellCash: parseFloat(sellCash) || 0,
                    buySpot: parseFloat(buySpot) || 0,
                    sellSpot: parseFloat(sellSpot) || 0,
                });
            }
        }

        // 如果上面的方法失敗，使用備用解析方法
        if (exchangeRates.length === 0) {
            console.log('使用備用解析方法...');

            // 簡化的解析方法 - 直接查找已知的貨幣和數值
            const currencies = Object.keys(currencyMap);

            for (const currency of currencies) {
                // 查找包含該貨幣代碼的模式
                const currencyRegex = new RegExp(`\\(${currency}\\)[^\\d]*([\\d.]+)[^\\d]+([\\d.]+)[^\\d]+([\\d.]+)[^\\d]+([\\d.]+)`, 'i');
                const match = html.match(currencyRegex);

                if (match) {
                    const [, buyCash, sellCash, buySpot, sellSpot] = match;
                    exchangeRates.push({
                        currency,
                        currencyName: currencyMap[currency].name,
                        flag: currencyMap[currency].flag,
                        buyCash: parseFloat(buyCash) || 0,
                        sellCash: parseFloat(sellCash) || 0,
                        buySpot: parseFloat(buySpot) || 0,
                        sellSpot: parseFloat(sellSpot) || 0,
                    });
                }
            }
        }

    } catch (error) {
        console.error('解析匯率資料失敗:', error);
    }

    return exchangeRates;
} 