"use client";

import React from "react";
import {
  Card,
  Button,
  Alert,
  Typography,
  Statistic,
  Row,
  Col,
  Table,
  Tag,
} from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;

interface ImportResultsProps {
  result: any;
  onRestart: () => void;
}

export function ImportResults({ result, onRestart }: ImportResultsProps) {
  const data = result?.data || result;
  const stats = {
    total: data?.totalRows || 0,
    success: data?.successRows || 0,
    failed: data?.failedRows || 0,
    updated: 0,
    created: data?.successRows || 0,
  };

  // 成功匯入的財產列表
  const successAssets = data?.successAssets || [];
  const successAssetNos = data?.successAssetNos || [];
  const successItems =
    successAssets.length > 0
      ? successAssets
      : successAssetNos.map((assetNo: string) => ({
          assetNo,
          assetName: "未取得財產名稱",
        }));

  // 成功財產表格
  const successAssetColumns = [
    {
      title: "序號",
      dataIndex: "index",
      key: "index",
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
      width: 200,
      render: (text: string) => (
        <Text strong style={{ color: "#1890ff" }}>
          {text}
        </Text>
      ),
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
      width: 200,
      render: (text: string) => (
        <Text strong style={{ color: "#1890ff" }}>
          {text}
        </Text>
      ),
    },
    {
      title: "狀態",
      key: "status",
      width: 100,
      render: () => (
        <Tag color="green" icon={<CheckCircleOutlined />}>
          匯入成功
        </Tag>
      ),
    },
  ];

  // 匯入成功
  const isSuccess = stats.failed === 0;

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      {/* 匯入結果狀態 */}
      <Card
        style={{
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.09)",
          border: "1px solid #f0f0f0",
        }}
      >
        <div style={{ textAlign: "center", marginBottom: "32px" }}>
          {isSuccess ? (
            <>
              <div
                style={{
                  backgroundColor: "rgba(82, 196, 26, 0.1)",
                  borderRadius: "50%",
                  width: "100px",
                  height: "100px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  margin: "0 auto 24px auto",
                }}
              >
                <CheckCircleOutlined
                  style={{
                    fontSize: "64px",
                    color: "#52c41a",
                  }}
                />
              </div>
              <Title
                level={2}
                style={{ color: "#52c41a", margin: "0 0 8px 0" }}
              >
                匯入完成！
              </Title>
              <Text
                style={{
                  fontSize: "18px",
                  display: "block",
                  marginBottom: "16px",
                }}
              >
                {data?.message || "所有資料已成功匯入到系統中"}
              </Text>
            </>
          ) : (
            <>
              <div
                style={{
                  backgroundColor: "rgba(255, 77, 79, 0.1)",
                  borderRadius: "50%",
                  width: "100px",
                  height: "100px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  margin: "0 auto 24px auto",
                }}
              >
                <ExclamationCircleOutlined
                  style={{
                    fontSize: "64px",
                    color: "#ff4d4f",
                  }}
                />
              </div>
              <Title
                level={2}
                style={{ color: "#ff4d4f", margin: "0 0 8px 0" }}
              >
                匯入完成（部分失敗）
              </Title>
              <Text
                style={{
                  fontSize: "18px",
                  display: "block",
                  marginBottom: "16px",
                }}
              >
                部分資料匯入失敗，請檢查錯誤詳情
              </Text>
            </>
          )}
        </div>

        {/* 統計結果 */}
        <Row gutter={24} style={{ marginBottom: "32px" }}>
          <Col span={6}>
            <Card style={{ textAlign: "center", height: "100%" }}>
              <Statistic
                title={<div style={{ fontSize: "16px" }}>總計筆數</div>}
                value={stats.total}
                prefix={
                  <ExclamationCircleOutlined style={{ marginRight: "8px" }} />
                }
                valueStyle={{ color: "#1890ff", fontSize: "28px" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ textAlign: "center", height: "100%" }}>
              <Statistic
                title={<div style={{ fontSize: "16px" }}>成功筆數</div>}
                value={stats.success}
                valueStyle={{ color: "#52c41a", fontSize: "28px" }}
                prefix={<CheckCircleOutlined style={{ marginRight: "8px" }} />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ textAlign: "center", height: "100%" }}>
              <Statistic
                title={<div style={{ fontSize: "16px" }}>失敗筆數</div>}
                value={stats.failed}
                valueStyle={{ color: "#ff4d4f", fontSize: "28px" }}
                prefix={<CloseCircleOutlined style={{ marginRight: "8px" }} />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ textAlign: "center", height: "100%" }}>
              <Statistic
                title={<div style={{ fontSize: "16px" }}>成功率</div>}
                value={
                  stats.total > 0
                    ? ((stats.success / stats.total) * 100).toFixed(1)
                    : 0
                }
                suffix="%"
                precision={1}
                valueStyle={{
                  color: isSuccess ? "#52c41a" : "#faad14",
                  fontSize: "28px",
                }}
              />
            </Card>
          </Col>
        </Row>

        {/* 成功匯入標示 */}
        <Alert
          message={
            <div
              style={{
                display: "flex",
                alignItems: "center",
                fontSize: "16px",
              }}
            >
              <CheckCircleOutlined
                style={{ color: "#52c41a", marginRight: "8px" }}
              />
              <Text strong>匯入成功</Text>
            </div>
          }
          description={`成功匯入 ${stats.success} 筆資料（新增 ${stats.created} 筆，更新 ${stats.updated} 筆）`}
          type="success"
          showIcon={false}
          style={{ marginBottom: "24px" }}
        />

        {/* 操作按鈕 */}
        <div style={{ display: "flex", justifyContent: "center", gap: "16px" }}>
          <Button
            type="primary"
            size="large"
            icon={<ReloadOutlined />}
            onClick={onRestart}
          >
            重新匯入
          </Button>
        </div>
      </Card>

      {/* 成功匯入詳情 */}
      {stats.success > 0 && successItems.length > 0 && (
        <Card
          title={
            <div style={{ display: "flex", alignItems: "center" }}>
              <CheckCircleOutlined
                style={{
                  color: "#52c41a",
                  marginRight: "8px",
                  fontSize: "18px",
                }}
              />
              <span style={{ fontSize: "18px", fontWeight: 500 }}>
                成功匯入財產列表 ({successItems.length} 筆)
              </span>
            </div>
          }
          style={{
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.09)",
            border: "1px solid #f0f0f0",
          }}
        >
          <Table
            dataSource={successItems}
            columns={successAssetColumns}
            pagination={{ pageSize: 10, showSizeChanger: true }}
            size="middle"
            rowKey="assetNo"
            bordered
          />
        </Card>
      )}

      <Card
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <InfoCircleOutlined
              style={{ color: "#1890ff", marginRight: "8px", fontSize: "18px" }}
            />
            <span style={{ fontSize: "18px", fontWeight: 500 }}>操作建議</span>
          </div>
        }
        style={{
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.09)",
          border: "1px solid #f0f0f0",
        }}
      >
        <ul style={{ paddingLeft: "20px", fontSize: "16px" }}>
          <li style={{ marginBottom: "12px" }}>
            <Text>
              資料已全部匯入成功，您可以前往
              <a href="/pms/document_maintenance/fixed_asset_maintenance_form">
                固定資產維護單
              </a>
              檢視
            </Text>
          </li>
          <li style={{ marginBottom: "12px" }}>
            <Text>建議定期檢查匯入的資料是否正確</Text>
          </li>
          <li>
            <Text>如需繼續匯入其他資料，可點選「重新匯入」</Text>
          </li>
        </ul>
      </Card>
    </div>
  );
}
