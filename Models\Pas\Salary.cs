﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 薪資基本資料表
    /// </summary>
    public class Salary : ModelBaseEntity
    {
        [Key]
        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; }

        [Comment("發薪狀態 (0:停薪 1:正常)")]
        [Column(TypeName = "nvarchar(1)")]
        public string SalaryStatus { get; set; }

        [Comment("雇主提撥比率")]
        [Column(TypeName = "decimal(5, 2)")]
        public decimal? EmployerContributionRate { get; set; } // 預設新增為6?

        [Comment("員工自提類型 (0:不提撥、1:提撥比率、2:固定自提金額)")]
        [Column(TypeName = "nvarchar(1)")]
        public string EmployeeContributionType { get; set; }

        [Comment("員工自提比例")]
        [Column(TypeName = "decimal(5, 2)")]
        public decimal? EmployeeContributionRate { get; set; }

        [Comment("員工自提金額")]
        [Column(TypeName = "decimal(12, 2)")]
        public decimal? EmployeeContributionAmount { get; set; }

        [Comment("計稅型式 (1:自動計算、2:固定稅率、3:固定稅額)")]
        [Column(TypeName = "nvarchar(1)")]
        public string TaxType { get; set; }

        [Comment("固定稅率")]
        [Column(TypeName = "decimal(5, 2)")]
        public decimal? FixedTaxRate { get; set; }

        [Comment("固定稅額")]
        [Column(TypeName = "decimal(12, 2)")]
        public decimal? FixedTaxAmount { get; set; }

        [Comment("轉帳帳號")]
        [Column(TypeName = "nvarchar(20)")]
        public string TransferAccount { get; set; }

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Remark { get; set; }

        public Salary()
        {
            UserId = "";
            SalaryStatus = "0";
            EmployerContributionRate = 0.00M;
            EmployeeContributionType = "0";
            EmployeeContributionRate = 0.00M;
            EmployeeContributionAmount = 0.00M;
            TaxType = "1";
            FixedTaxRate = 0.00M;
            FixedTaxAmount = 0.00M;
            TransferAccount = "";
            Remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 薪資基本資料 DTO
    /// </summary>
    public class SalaryDTO : ModelBaseEntityDTO
    {
        public string UserId { get; set; } // 使用者編號

        public string SalaryStatus { get; set; } // 發薪狀態 (0:停薪 1:正常)

        public string? SalaryStatusName { get; set; } // 發薪狀態名稱 (0:停薪 1:正常)

        public decimal? EmployerContributionRate { get; set; } // 雇主提撥比率

        public string EmployeeContributionType { get; set; } // 員工自提類型 (0:不提撥、1:提撥比率、2:固定自提金額)

        public string? EmployeeContributionTypeName { get; set; } // 員工自提類型名稱

        public decimal? EmployeeContributionRate { get; set; } // 員工自提比例

        public decimal? EmployeeContributionAmount { get; set; } // 員工自提金額

        public string TaxType { get; set; } // 計稅型式 (1:自動計算、2:固定稅率、3:固定稅額)

        public string? TaxTypeName { get; set; } // 計稅型式名稱

        public decimal? FixedTaxRate { get; set; } // 固定稅率

        public decimal? FixedTaxAmount { get; set; } // 固定稅額

        public string TransferAccount { get; set; } // 轉帳帳號

        public string Remark { get; set; } // 備註

        public SalaryDTO()
        {
            UserId = "";
            SalaryStatus = "0";
            SalaryStatusName = "";
            EmployerContributionRate = 0.00M;
            EmployeeContributionType = "0";
            EmployeeContributionTypeName = "";
            EmployeeContributionRate = 0.00M;
            EmployeeContributionAmount = 0.00M;
            TaxType = "1";
            TaxTypeName = "";
            FixedTaxRate = 0.00M;
            FixedTaxAmount = 0.00M;
            TransferAccount = "";
            Remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

