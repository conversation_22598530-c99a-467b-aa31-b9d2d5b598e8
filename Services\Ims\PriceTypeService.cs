using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Services.Ims;
/// <summary> 價格類別服務 </summary>
public class PriceTypeService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : IPriceTypeService
{
    /// <summary> 價格類別列表 </summary>
    public async Task<List<PriceTypeDTO>> GetAllAsync()
    {
        var result = await _context.Ims_PriceType
            .Where(e => !e.IsDeleted)
            .OrderBy(e => e.SortCode)
            .ThenBy(e => e.Name)
            .Select(t => new PriceTypeDTO
            {
                PriceTypeID = t.PriceTypeID,
                Name = t.Name,
                Description = t.Description,
                SortCode = t.SortCode,
                AllowStop = t.AllowStop,
                IsStop = t.IsStop,
                CreateTime = t.CreateTime,
                CreateUserId = t.CreateUserId,
                UpdateTime = t.UpdateTime,
                UpdateUserId = t.UpdateUserId,
                DeleteTime = t.DeleteTime,
                DeleteUserId = t.DeleteUserId,
                IsDeleted = t.IsDeleted
            })
            .ToListAsync();
        return result;
    }

    /// <summary> 價格類別取得 </summary>
    public async Task<List<PriceTypeDTO>> GetAsync(Guid PriceTypeID)
    {
        var entity = await _context.Ims_PriceType
            .Where(e => e.PriceTypeID == PriceTypeID && !e.IsDeleted)
            .Select(t => new PriceTypeDTO
            {
                PriceTypeID = t.PriceTypeID,
                Name = t.Name,
                Description = t.Description,
                SortCode = t.SortCode,
                AllowStop = t.AllowStop,
                IsStop = t.IsStop,
                CreateTime = t.CreateTime,
                CreateUserId = t.CreateUserId,
                UpdateTime = t.UpdateTime,
                UpdateUserId = t.UpdateUserId,
                DeleteTime = t.DeleteTime,
                DeleteUserId = t.DeleteUserId,
                IsDeleted = t.IsDeleted
            })
            .ToListAsync();
        return entity;
    }

    /// <summary> 價格類別新增 </summary>
    public async Task<(bool, string)> AddAsync(PriceTypeDTO DTO)
    {
        if (DTO == null)
        {
            return (false, "PriceType data is null");
        }

        var entity = new PriceType
        {
            PriceTypeID = Guid.NewGuid(),
            Name = DTO.Name,
            Description = DTO.Description ?? string.Empty,
            SortCode = DTO.SortCode,
            AllowStop = DTO.AllowStop,
            IsStop = DTO.IsStop,
            CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId
        };

        _context.Ims_PriceType.Add(entity);
        await _context.SaveChangesAsync();
        return (true, $"PriceType {entity.PriceTypeID} added successfully");
    }

    /// <summary> 價格類別更新 </summary>
    public async Task<(bool, string)> UpdateAsync(PriceTypeDTO DTO)
    {
        try
        {
            var entity = await _context.Ims_PriceType
                .FirstOrDefaultAsync(p => p.PriceTypeID == DTO.PriceTypeID);
            if (entity == null)
            {
                return (false, $"PriceType {DTO.PriceTypeID} not found");
            }

            entity.Name = DTO.Name;
            entity.Description = DTO.Description ?? string.Empty;
            entity.SortCode = DTO.SortCode;
            entity.AllowStop = DTO.AllowStop;
            entity.IsStop = DTO.IsStop;
            entity.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
            entity.UpdateUserId = _currentUserService.UserId;

            _context.Ims_PriceType.Update(entity);
            await _context.SaveChangesAsync();
            return (true, $"PriceType {entity.PriceTypeID} updated successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Update PriceType failed: {ex.Message}");
        }
    }

    /// <summary> 價格類別刪除 </summary>
    public async Task<(bool, string)> DeleteAsync(PriceTypeDTO DTO)
    {
        try
        {
            var entity = await _context.Ims_PriceType
                .FirstOrDefaultAsync(p => p.PriceTypeID == DTO.PriceTypeID);
            if (entity == null)
            {
                return (false, "PriceType not found");
            }
            entity.IsDeleted = true;
            entity.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
            entity.DeleteUserId = _currentUserService.UserId;

            _context.Update(entity);
            await _context.SaveChangesAsync();
            return (true, $"PriceType {entity.PriceTypeID} deleted successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Delete PriceType failed: {ex.Message}");
        }
    }
}