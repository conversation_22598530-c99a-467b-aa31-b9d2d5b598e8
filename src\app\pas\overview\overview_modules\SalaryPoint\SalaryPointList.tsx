import React, { useEffect, useState } from "react";
import { Table, Button, Space, message, Popconfirm, Typo<PERSON>, Modal, Card } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
    getSalaryPointList,
    deleteSalaryPoint,
    SalaryPoint,
} from "@/services/pas/SalaryPointService";
import SalaryPointEditor from "./SalaryPointEditor";
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    PlusOutlined,
    DollarOutlined,
    CalendarOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const SalaryPointList: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<SalaryPoint[]>([]);
    const [selectedUid, setSelectedUid] = useState<string | null>(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    // 載入列表
    const loadList = async () => {
        setLoading(true);
        try {
            const result = await getSalaryPointList();
            if (result.success) {
                setData(result.data || []);
            } else {
                message.error(result.message || "載入薪點資料失敗");
            }
        } catch (error: any) {
            message.error(error.message || "載入薪點資料失敗");
        } finally {
            setLoading(false);
        }
    };

    // 點擊編輯
    const handleEdit = (uid: string) => {
        setSelectedUid(uid);
        setModalVisible(true);
    };

    // 點擊刪除
    const handleDelete = async (uid: string) => {
        try {
            const result = await deleteSalaryPoint(uid);
            if (result.success) {
                message.success("刪除成功");
                loadList();
            } else {
                message.error(result.message || "刪除失敗");
            }
        } catch (error: any) {
            message.error(error.message || "刪除失敗");
        }
    };

    // 點擊新增
    const handleAdd = () => {
        setSelectedUid(null);
        setModalVisible(true);
    };

    // 表格欄位定義
    const columns: ColumnsType<SalaryPoint> = [
        {
            title: "薪點名稱",
            dataIndex: "pointLevel",
            width: 120,
            render: (text) => (
                <Space>
                    <DollarOutlined style={{ color: '#1890ff' }} />
                    <Text strong>{text}</Text>
                </Space>
            ),
        },
        {
            title: "薪點金額",
            dataIndex: "amount",
            width: 120,
            render: (value: number) => (
                <Text type="success">
                    {value?.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}
                </Text>
            ),
        },
        {
            title: "生效日期",
            dataIndex: "effectiveDate",
            width: 120,
            render: (text) => (
                <Space>
                    <CalendarOutlined style={{ color: '#722ed1' }} />
                    <Text>{text ? dayjs(text).format("YYYY-MM-DD") : "-"}</Text>
                </Space>
            ),
        },
        {
            title: "調整原因",
            dataIndex: "adjustmentReason",
            ellipsis: true,
            render: (text) => (
                <Space>
                    <InfoCircleOutlined style={{ color: '#faad14' }} />
                    <Text>{text}</Text>
                </Space>
            ),
        },
        {
            title: "操作",
            key: "action",
            render: (_, record) => (
                <Space>
                    <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record.uid)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title={
                            <div>
                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                <Text>確定要刪除此筆資料嗎？</Text>
                            </div>
                        }
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確認"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                    >
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    useEffect(() => {
        loadList();
    }, []);

    return (
        <div style={{ padding: '24px' }}>
            <Card
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Title level={4} style={{ margin: 0 }}>薪點金額管理</Title>
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAdd}
                        style={{ borderRadius: '6px' }}
                    >
                        新增薪點
                    </Button>
                </div>

                <Table
                    rowKey="uid"
                    columns={columns}
                    dataSource={data}
                    loading={loading}
                    scroll={{ x: 1000 }}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    pagination={{
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 筆`,
                        style: { marginTop: 16 }
                    }}
                />
            </Card>

            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {selectedUid ? "編輯薪點資料" : "新增薪點資料"}
                    </Title>
                }
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={null}
                width={480}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <SalaryPointEditor
                    uid={selectedUid ?? undefined}
                    onSuccess={() => {
                        setModalVisible(false);
                        loadList();
                    }}
                    onCancel={() => setModalVisible(false)}
                />
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </div>
    );
};

export default SalaryPointList;
