﻿using FAST_ERP_Backend.Models.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface ISystemParametersService
    {
        Task<List<SystemParametersDTO>> GetSystemParametersAsync(string SystemParametersId = "");
        Task<(bool, string)> AddSystemParametersAsync(SystemParametersDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> EditSystemParametersAsync(SystemParametersDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> DeleteSystemParametersAsync(SystemParametersDTO SystemParameters,String tokenUid="");
        Task<(bool, string)> RestoreSystemParametersAsync(SystemParametersDTO SystemParameters,String tokenUid="");
    }
}
