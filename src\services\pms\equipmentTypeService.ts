import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 設備類型
export interface EquipmentType {
    equipmentTypeId: string;
    equipmentTypeNo: string;
    name: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 獲取設備類型列表
export async function getEquipmentTypes(): Promise<ApiResponse<EquipmentType[]>> {
    try {
        const response = await httpClient(apiEndpoints.getEquipmentTypes, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取設備類型列表失敗",
            data: [],
        };
    }
}

// 獲取設備類型詳情
export async function getEquipmentTypeDetail(id: string): Promise<ApiResponse<EquipmentType>> {
    try {
        const response = await httpClient(`${apiEndpoints.getEquipmentTypeDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取設備類型詳情失敗",
            data: undefined,
        };
    }
}

// 新增設備類型
export async function createEquipmentType(data: Partial<EquipmentType>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addEquipmentType, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增設備類型失敗",
        };
    }
}

// 更新設備類型
export async function updateEquipmentType(data: Partial<EquipmentType>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editEquipmentType, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新設備類型失敗",
        };
    }
}

// 刪除設備類型
export async function deleteEquipmentType(data: Partial<EquipmentType>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteEquipmentType, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除設備類型失敗",
        };
    }
}
