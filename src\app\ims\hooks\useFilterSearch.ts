import { useState, useCallback, useMemo } from 'react';
import { message } from 'antd';

// TypeScript 介面定義
export interface FilterSearchState {
  searchText: string;
  activeFilters: string[];
  filterValues: Record<string, any>;
}

export interface FilterSearchActions {
  setSearchText: (text: string) => void;
  setFilterData: (data: { activeFilters: string[]; filterValues: Record<string, any> }) => void;
  clearAll: () => void;
  updateFilter: (filterKey: string, value: any) => void;
  removeFilter: (filterKey: string) => void;
}

export interface FilterSearchReturn extends FilterSearchState, FilterSearchActions {
  hasActiveFilters: boolean;
  filterCount: number;
  reset: () => void;
}

export interface UseFilterSearchOptions {
  /** 初始搜尋文字 */
  initialSearchText?: string;
  /** 初始篩選條件 */
  initialFilters?: {
    activeFilters: string[];
    filterValues: Record<string, any>;
  };
  /** 清除時是否顯示成功訊息 */
  showClearMessage?: boolean;
  /** 自定義清除訊息 */
  clearMessage?: string;
  /** 額外的清除回調 */
  onClear?: () => void;
  /** 搜尋變更回調 */
  onSearchChange?: (text: string) => void;
  /** 篩選變更回調 */
  onFilterChange?: (state: FilterSearchState) => void;
}

/**
 * 通用的篩選搜尋 Hook
 * 
 * 提供完整的篩選和搜尋狀態管理，包括：
 * - 搜尋文字管理
 * - 篩選條件管理
 * - 統一的清除功能
 * - 狀態變更回調
 * 
 * @example
 * ```tsx
 * const filterSearch = useFilterSearch({
 *   showClearMessage: true,
 *   onClear: () => {
 *     // 額外的清除邏輯
 *     setOtherState('');
 *   }
 * });
 * 
 * // 使用
 * <FilterSearchPanel
 *   searchText={filterSearch.searchText}
 *   onSearchChange={filterSearch.setSearchText}
 *   filterData={{ 
 *     activeFilters: filterSearch.activeFilters, 
 *     filterValues: filterSearch.filterValues 
 *   }}
 *   onFilterChange={(event) => filterSearch.setFilterData(event)}
 *   onClearAll={filterSearch.clearAll}
 * />
 * ```
 */
export const useFilterSearch = (options: UseFilterSearchOptions = {}): FilterSearchReturn => {
  const {
    initialSearchText = '',
    initialFilters = { activeFilters: [], filterValues: {} },
    showClearMessage = true,
    clearMessage = '已清除所有篩選條件',
    onClear,
    onSearchChange,
    onFilterChange
  } = options;

  // 狀態管理
  const [searchText, setSearchTextState] = useState(initialSearchText);
  const [activeFilters, setActiveFilters] = useState<string[]>(initialFilters.activeFilters);
  const [filterValues, setFilterValues] = useState<Record<string, any>>(initialFilters.filterValues);

  // 計算衍生狀態
  const hasActiveFilters = useMemo(() => {
    return activeFilters.length > 0 || searchText.trim().length > 0;
  }, [activeFilters.length, searchText]);

  const filterCount = useMemo(() => {
    return activeFilters.length + (searchText.trim().length > 0 ? 1 : 0);
  }, [activeFilters.length, searchText]);

  // 搜尋文字設定
  const setSearchText = useCallback((text: string) => {
    setSearchTextState(text);
    onSearchChange?.(text);
    
    // 觸發整體狀態變更回調
    onFilterChange?.({
      searchText: text,
      activeFilters,
      filterValues
    });
  }, [activeFilters, filterValues, onSearchChange, onFilterChange]);

  // 篩選資料設定
  const setFilterData = useCallback((data: { activeFilters: string[]; filterValues: Record<string, any> }) => {
    setActiveFilters(data.activeFilters);
    setFilterValues(data.filterValues);
    
    // 觸發整體狀態變更回調
    onFilterChange?.({
      searchText,
      activeFilters: data.activeFilters,
      filterValues: data.filterValues
    });
  }, [searchText, onFilterChange]);

  // 更新單個篩選條件
  const updateFilter = useCallback((filterKey: string, value: any) => {
    let newActiveFilters = [...activeFilters];
    let newFilterValues = { ...filterValues };

    if (value === undefined || value === null || value === '' || 
        (Array.isArray(value) && value.length === 0)) {
      // 移除篩選條件
      newActiveFilters = newActiveFilters.filter(key => key !== filterKey);
      delete newFilterValues[filterKey];
    } else {
      // 新增或更新篩選條件
      if (!newActiveFilters.includes(filterKey)) {
        newActiveFilters.push(filterKey);
      }
      newFilterValues[filterKey] = value;
    }

    setActiveFilters(newActiveFilters);
    setFilterValues(newFilterValues);

    // 觸發整體狀態變更回調
    onFilterChange?.({
      searchText,
      activeFilters: newActiveFilters,
      filterValues: newFilterValues
    });
  }, [searchText, activeFilters, filterValues, onFilterChange]);

  // 移除單個篩選條件
  const removeFilter = useCallback((filterKey: string) => {
    const newActiveFilters = activeFilters.filter(key => key !== filterKey);
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];

    setActiveFilters(newActiveFilters);
    setFilterValues(newFilterValues);

    // 觸發整體狀態變更回調
    onFilterChange?.({
      searchText,
      activeFilters: newActiveFilters,
      filterValues: newFilterValues
    });
  }, [searchText, activeFilters, filterValues, onFilterChange]);

  // 清除所有篩選
  const clearAll = useCallback(() => {
    console.log('🧹 useFilterSearch: 開始清除所有篩選條件');
    
    // 重置所有狀態
    setSearchTextState('');
    setActiveFilters([]);
    setFilterValues({});

    // 調用額外的清除回調
    onClear?.();

    // 顯示成功訊息
    if (showClearMessage) {
      message.success(clearMessage);
    }

    // 觸發整體狀態變更回調
    onFilterChange?.({
      searchText: '',
      activeFilters: [],
      filterValues: {}
    });

    console.log('🧹 useFilterSearch: 清除所有篩選條件完成');
  }, [onClear, showClearMessage, clearMessage, onFilterChange]);

  // 重置到初始狀態
  const reset = useCallback(() => {
    setSearchTextState(initialSearchText);
    setActiveFilters(initialFilters.activeFilters);
    setFilterValues(initialFilters.filterValues);

    // 觸發整體狀態變更回調
    onFilterChange?.({
      searchText: initialSearchText,
      activeFilters: initialFilters.activeFilters,
      filterValues: initialFilters.filterValues
    });
  }, [initialSearchText, initialFilters, onFilterChange]);

  return {
    // 狀態
    searchText,
    activeFilters,
    filterValues,
    hasActiveFilters,
    filterCount,
    
    // 動作
    setSearchText,
    setFilterData,
    clearAll,
    updateFilter,
    removeFilter,
    reset
  };
};
