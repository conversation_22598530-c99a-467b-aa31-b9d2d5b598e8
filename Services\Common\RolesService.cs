﻿using System;
using System.Data;
using System.Security.Cryptography;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FAST_ERP_Backend.Services.Common
{
    public class RolesService : IRolesService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly IRolesPermissionsService _rolesPermissionsService;

        public RolesService(Baseform baseform, ERPDbContext context, IRolesPermissionsService rolesPermissionsService)
        {
            _baseform = baseform;
            _context = context;
            _rolesPermissionsService = rolesPermissionsService;
        }

        public async Task<List<RolesDTO>> GetRolesAsync(string _rolesId)
        {

            var query = _context.Common_Roles
                .Include(x => x.RolesPermissions)
                .AsQueryable();

            // 如果 _rolesId 不為空，則加上篩選條件
            if (!string.IsNullOrEmpty(_rolesId))
            {
                query = query.Where(e => e.RolesId == _rolesId);
            }

            var result = await query
                .OrderBy(e => e.CreateTime)
                .Select(t => new RolesDTO
                {
                    RolesId = t.RolesId,
                    Name = t.Name,
                    RolesPermissions = t.RolesPermissions
                        .Select(y => new RolesPermissionsDTO
                        {
                            RolesPermissionsId = y.RolesPermissionsId,
                            RolesId = y.RolesId,
                            SystemMenuId = y.SystemMenuId
                        }).ToList()
                })
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// 新增角色資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddRolesAsync(RolesDTO _data, string tokenUid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                string uid = Guid.NewGuid().ToString().Trim();
                var nowTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                var newRole = new Roles
                {
                    RolesId = uid,
                    Name = _data.Name,
                    CreateTime = nowTime,
                    CreateUserId = tokenUid,
                };
                //新增角色
                await _context.Common_Roles.AddAsync(newRole);

                foreach (var permission in _data.RolesPermissions)
                {
                    var (Success, Message) = await _rolesPermissionsService.AddRolesPermissionsAsync(permission, tokenUid, newRole.RolesId, nowTime);
                    if (!Success)
                    {
                        throw new Exception($"新增角色權限失敗：{Message}");
                    }
                }
                //提交交易
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增角色成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增角色失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 變更角色資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditRolesAsync(RolesDTO _data, string tokenUid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existRole = await _context.Common_Roles
                .Include(x => x.RolesPermissions)
                .FirstOrDefaultAsync(e => e.RolesId == _data.RolesId);

                if (existRole == null)
                {
                    throw new Exception("角色不存在");
                }

                var nowTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                //更新角色資料
                existRole.Name = _data.Name;
                existRole.UpdateUserId = tokenUid;
                existRole.UpdateTime = nowTime;

                //更新角色權限資料  
                var existingPermissions = existRole.RolesPermissions.ToList();

                //找出要刪除的權限
                var permissionsToDelete = existingPermissions
                    .Where(x => !_data.RolesPermissions.Any(y => y.SystemMenuId == x.SystemMenuId))
                    .ToList();
                //執行刪除
                foreach (var permission in permissionsToDelete)
                {
                    var (deleteSuccess, deleteMessage) = await _rolesPermissionsService.DeleteRolesPermissionsAsync(permission, tokenUid, existRole.RolesId, nowTime);
                    if (!deleteSuccess)
                    {
                        throw new Exception(deleteMessage);
                    }
                }
               
                //找出要新增的權限
                var permissionsToCreate = _data.RolesPermissions
                    .Where(x => !existingPermissions.Any(y => y.SystemMenuId == x.SystemMenuId))
                    .ToList();
                //新增新的權限
                foreach (var permission in permissionsToCreate){
                    var (Success, Message) = await _rolesPermissionsService.AddRolesPermissionsAsync(permission, tokenUid, existRole.RolesId, nowTime);
                    if (!Success)
                    {
                        throw new Exception(Message);
                    }
                }
                
                //提交交易
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "更新角色成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新角色失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除角色資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteRolesAsync(RolesDTO _data, string tokenUid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existRole = await _context.Common_Roles
                .Include(x => x.RolesPermissions)
                .FirstOrDefaultAsync(e => e.RolesId == _data.RolesId);

                if (existRole == null)
                {
                    throw new Exception("角色不存在");
                }

                var nowTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                //刪除角色資料
                existRole.DeleteUserId = tokenUid;
                existRole.DeleteTime = nowTime;
                existRole.IsDeleted = true;

                //找出要刪除的權限 
                var existingPermissions = existRole.RolesPermissions.ToList();

                //執行刪除
                foreach (var permission in existingPermissions)
                {
                    var (deleteSuccess, deleteMessage) = await _rolesPermissionsService.DeleteRolesPermissionsAsync(permission, tokenUid, existRole.RolesId, nowTime);
                    if (!deleteSuccess)
                    {
                        throw new Exception(deleteMessage);
                    }
                }
               
                //提交交易
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除角色成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除角色失敗: {ex.Message}");
            }
        }
    }
}
