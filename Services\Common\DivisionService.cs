﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class DivisionService : IDivisionService
    {
        private readonly ERPDbContext _context;

        public DivisionService(ERPDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得組別資料
        /// </summary>
        /// <param name="_divisionId"></param>
        /// <returns></returns>
        public async Task<List<DivisionDTO>> GetDivisionAsync(string _divisionId = "")
        {
            var query = _context.Set<Division>().AsQueryable();

            if (!string.IsNullOrEmpty(_divisionId))
            {
                query = query.Where(d => d.DivisionId == _divisionId);
            }

            return await query.Select(d => new DivisionDTO
            {
                DivisionId = d.DivisionId,
                Name = d.Name,
                SortCode = d.SortCode,
                CreateTime = d.CreateTime,
                CreateUserId = d.CreateUserId,
                UpdateTime = d.UpdateTime,
                UpdateUserId = d.UpdateUserId,
                DeleteTime = d.DeleteTime,
                DeleteUserId = d.DeleteUserId,
                DepartmentId = d.DepartmentId
            }).ToListAsync();
        }

        /// <summary>
        /// 新增組別
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddDivisionAsync(DivisionDTO _data)
        {
            string uid = Guid.NewGuid().ToString().Trim();
            try
            {
                var entity = new Division
                {
                    DivisionId = uid,
                    Name = _data.Name,
                    SortCode = _data.SortCode,
                    CreateTime = _data.CreateTime,
                    CreateUserId = _data.CreateUserId,
                    UpdateTime = _data.UpdateTime,
                    UpdateUserId = _data.UpdateUserId,
                    DeleteTime = _data.DeleteTime,
                    DeleteUserId = _data.DeleteUserId,
                    DepartmentId = _data.DepartmentId
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯組別
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditDivisionAsync(DivisionDTO _data)
        {
            try
            {
                var entity = await _context.Set<Division>().FindAsync(_data.DivisionId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.Name = _data.Name;
                entity.SortCode = _data.SortCode;
                entity.CreateTime = _data.CreateTime;
                entity.CreateUserId = _data.CreateUserId;
                entity.UpdateTime = _data.UpdateTime;
                entity.UpdateUserId = _data.UpdateUserId;
                entity.DeleteTime = _data.DeleteTime;
                entity.DeleteUserId = _data.DeleteUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除組別
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteDivisionAsync(DivisionDTO _data)
        {
            try
            {
                var entity = await _context.Set<Division>().FindAsync(_data.DivisionId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得組別詳細資料
        /// </summary>
        /// <param name="_divisionId"></param>
        /// <returns></returns>
        public async Task<DivisionDTO> GetDivisionDetailAsync(string _divisionId)
        {
            var entity = await _context.Set<Division>().FindAsync(_divisionId);
            if (entity == null)
            {
                return null;
            }

            return new DivisionDTO
            {
                DivisionId = entity.DivisionId,
                Name = entity.Name,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }
    }
}

