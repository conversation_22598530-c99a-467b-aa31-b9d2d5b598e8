using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 承保單位
    /// </summary>
    public class InsuranceUnit : ModelBaseEntity
    {
        [Key]
        [Comment("承保單位編號")]
        public Guid InsuranceUnitId { get; set; } // 承保單位編號    

        [Comment("承保單位名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string Name { get; set; } // 承保單位名稱

        [Comment("投保金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InsuranceAmount { get; set; } = 0; // 投保金額

        [Comment("投保起日")]
        [Column(TypeName = "bigint")]
        public long? InsuranceStartDate { get; set; } // 投保起日

        [Comment("投保迄日")]
        [Column(TypeName = "bigint")]
        public long? InsuranceExpiryDate { get; set; } // 投保迄日

        [Comment("公司統一編號")]
        [Column(TypeName = "nvarchar(50)")]
        public string CompanyNo { get; set; } // 公司統一編號

        [Comment("聯絡人")]
        [Column(TypeName = "nvarchar(50)")]
        public string ContactPerson { get; set; } // 聯絡人

        [Comment("聯絡電話")]
        [Column(TypeName = "nvarchar(20)")]
        public string ContactPhone { get; set; } // 聯絡電話

        [Comment("聯絡信箱")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContactEmail { get; set; } // 聯絡信箱

        [Comment("公司地址")]
        [Column(TypeName = "nvarchar(200)")]
        public string Address { get; set; } // 公司地址

        [Comment("公司網站")]
        [Column(TypeName = "nvarchar(200)")]
        public string Website { get; set; } // 公司網站

        [Comment("說明描述")]
        [Column(TypeName = "nvarchar(500)")]
        public string Description { get; set; } // 說明描述

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        [Comment("新增時間")]
        [Column(TypeName = "bigint")]
        public long? CreateTime { get; set; } // 新增時間

        [Comment("新增者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? CreateUserId { get; set; } // 新增者編號

        [Comment("更新時間")]
        [Column(TypeName = "bigint")]
        public long? UpdateTime { get; set; } // 更新時間

        [Comment("更新者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? UpdateUserId { get; set; } // 更新者編號

        [Comment("刪除時間")]
        [Column(TypeName = "bigint")]
        public long? DeleteTime { get; set; } // 刪除時間

        [Comment("刪除者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? DeleteUserId { get; set; } // 刪除者編號

        // 資產承保單位
        public virtual ICollection<AssetInsuranceUnitMapping> AssetInsuranceUnits { get; set; }

        public InsuranceUnit()
        {
            InsuranceUnitId = Guid.NewGuid();
            Name = string.Empty;
            InsuranceAmount = 0;
            InsuranceStartDate = null;
            InsuranceExpiryDate = null;
            CompanyNo = string.Empty;
            ContactPerson = string.Empty;
            ContactPhone = string.Empty;
            ContactEmail = string.Empty;
            Address = string.Empty;
            Website = string.Empty;
            Description = string.Empty;
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            AssetInsuranceUnits = new List<AssetInsuranceUnitMapping>();
        }
    }

    public class InsuranceUnitDTO
    {
        public Guid InsuranceUnitId { get; set; } // 承保單位編號
        public string Name { get; set; } // 承保單位名稱
        public decimal InsuranceAmount { get; set; } // 投保金額
        public long? InsuranceStartDate { get; set; } // 投保起日
        public long? InsuranceExpiryDate { get; set; } // 投保迄日
        public string CompanyNo { get; set; } // 公司統一編號
        public string ContactPerson { get; set; } // 聯絡人
        public string ContactPhone { get; set; } // 聯絡電話
        public string ContactEmail { get; set; } // 聯絡信箱
        public string Address { get; set; } // 公司地址
        public string Website { get; set; } // 公司網站
        public string Description { get; set; } // 說明描述
        public int SortCode { get; set; } // 排序號碼
        public long? CreateTime { get; set; } // 新增時間
        public string? CreateUserId { get; set; } // 新增者編號
        public long? UpdateTime { get; set; } // 更新時間
        public string? UpdateUserId { get; set; } // 更新者編號
        public long? DeleteTime { get; set; } // 刪除時間
        public string? DeleteUserId { get; set; } // 刪除者編號

        // 資產承保單位
        public List<AssetInsuranceUnitMapping> AssetInsuranceUnits { get; set; }

        public InsuranceUnitDTO()
        {
            InsuranceUnitId = Guid.Empty;
            Name = string.Empty;
            InsuranceAmount = 0;
            InsuranceStartDate = null;
            InsuranceExpiryDate = null;
            CompanyNo = string.Empty;
            ContactPerson = string.Empty;
            ContactPhone = string.Empty;
            ContactEmail = string.Empty;
            Address = string.Empty;
            Website = string.Empty;
            Description = string.Empty;
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            AssetInsuranceUnits = new List<AssetInsuranceUnitMapping>();
        }
    }
}

