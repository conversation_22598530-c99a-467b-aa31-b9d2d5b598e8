using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IPmsUserRoleService
    {
        /// <summary>
        /// 取得所有財產系統使用者身分
        /// </summary>
        /// <returns></returns>
        Task<List<PmsUserRoleDTO>> GetAllPmsUserRolesAsync();

        /// <summary>
        /// 取得指定財產系統使用者身分
        /// </summary>
        Task<PmsUserRoleDTO> GetPmsUserRoleByIdAsync(Guid id);

        /// <summary>
        /// 新增財產系統使用者身分
        /// </summary>
        /// <param name="pmsUserRoleDTO"></param>
        /// <returns></returns>
        Task<PmsUserRoleDTO> CreatePmsUserRoleAsync(PmsUserRoleDTO pmsUserRoleDTO);

        /// <summary>
        /// 更新財產系統使用者身分
        /// </summary>
        /// <param name="pmsUserRoleDTO"></param>
        /// <returns></returns>
        Task<PmsUserRoleDTO> UpdatePmsUserRoleAsync(PmsUserRoleDTO pmsUserRoleDTO);

        /// <summary>
        /// 刪除財產系統使用者身分
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> DeletePmsUserRoleAsync(Guid id);

        /// <summary>
        /// 取得使用者身分
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<List<PmsUserRoleDTO>> GetUserRolesAsync(string userId);

        /// <summary>
        /// 指派使用者身分
        /// </summary>
        /// <param name="mappingDTO"></param>
        /// <returns></returns>
        Task<PmsUserRoleMappingDTO> AssignRoleToUserAsync(PmsUserRoleMappingDTO mappingDTO);

        /// <summary>
        /// 移除使用者身分
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        Task<bool> RemoveRoleFromUserAsync(string userId, Guid roleId);
    }
}