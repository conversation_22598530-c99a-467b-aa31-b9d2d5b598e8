using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class HensureService : IHensureService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly EmployeeClass _employeeClass;
        private readonly ICurrentUserService _currentUserService;

        public HensureService(
            ERPDbContext context,
            Baseform baseform,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
        }

        public async Task<List<HensureDTO>> GetHensureListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Hensure
                    .Where(h => h.userId == userId && h.IsDeleted != true)
                    .OrderByDescending(h => h.HealthInsStartDate)
                    .ThenByDescending(h => h.HealthInsEndDate)
                    .Select(h => new HensureDTO
                    {
                        uid = h.uid,
                        userId = h.userId,
                        dependentRocId = h.dependentRocId,
                        dependentName = h.dependentName,
                        dependentRelationType = h.dependentRelationType,
                        dependentRelationTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_deptypedata, h.dependentRelationType),
                        HealthInsStartDate = _baseform.TimestampToDateStr(h.HealthInsStartDate),
                        HealthInsEndDate = _baseform.TimestampToDateStr(h.HealthInsEndDate),
                        remark = h.remark,
                        CreateTime = h.CreateTime,
                        CreateUserId = h.CreateUserId,
                        UpdateTime = h.UpdateTime,
                        UpdateUserId = h.UpdateUserId
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得眷屬依附資料錯誤", ex);
            }
        }

        public async Task<HensureDTO> GetHensureDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Hensure
                    .Where(h => h.uid == uid && h.IsDeleted != true)
                    .Select(h => new HensureDTO
                    {
                        uid = h.uid,
                        userId = h.userId,
                        dependentRocId = h.dependentRocId,
                        dependentName = h.dependentName,
                        dependentRelationType = h.dependentRelationType,
                        dependentRelationTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_deptypedata, h.dependentRelationType),
                        HealthInsStartDate = _baseform.TimestampToDateStr(h.HealthInsStartDate),
                        HealthInsEndDate = _baseform.TimestampToDateStr(h.HealthInsEndDate),
                        remark = h.remark,
                        CreateTime = h.CreateTime,
                        CreateUserId = h.CreateUserId,
                        UpdateTime = h.UpdateTime,
                        UpdateUserId = h.UpdateUserId
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得眷屬依附資料明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddHensureAsync(HensureDTO data)
        {
            var list_msg_check = CheckHensureInput(data, "add");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newHensure = new Hensure
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    dependentRocId = data.dependentRocId,
                    dependentName = data.dependentName,
                    dependentRelationType = data.dependentRelationType,
                    HealthInsStartDate = _baseform.DateStrToTimestamp(data.HealthInsStartDate),
                    HealthInsEndDate = _baseform.DateStrToTimestamp(data.HealthInsEndDate),
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Hensure.AddAsync(newHensure);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "眷屬依附資料新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"眷屬依附資料新增失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditHensureAsync(HensureDTO data)
        {
            var list_msg_check = CheckHensureInput(data, "edit");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Hensure.FirstOrDefaultAsync(h => h.uid == data.uid && h.IsDeleted != true);
                if (exist == null)
                    return (false, "找不到對應眷屬依附資料");

                //exist.userId = data.userId; //編輯不用改依附者ID
                exist.dependentRocId = data.dependentRocId;
                exist.dependentName = data.dependentName;
                exist.dependentRelationType = data.dependentRelationType;
                exist.HealthInsStartDate = _baseform.DateStrToTimestamp(data.HealthInsStartDate);
                exist.HealthInsEndDate = _baseform.DateStrToTimestamp(data.HealthInsEndDate);
                exist.remark = data.remark;
                exist.remark = data.remark;
                exist.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                exist.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "眷屬依附資料編輯成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"眷屬依附資料編輯失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteHensureAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Hensure.FirstOrDefaultAsync(h => h.uid == uid && h.IsDeleted != true);
                if (exist == null)
                    return (false, "資料已刪除或不存在");

                exist.IsDeleted = true;
                exist.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                exist.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "眷屬依附資料刪除成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"眷屬依附資料刪除失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckHensureInput(HensureDTO data, string _mode)
        {
            var list = new List<string>();

            // 檢核item.
            bool idNoExists = false; // 身分證存在.

            if (_mode == "add" && data.isNewDependent)
            {
                // 新增需判斷該眷屬有沒有已依附該員工 有就不新增
                idNoExists = _context.Pas_Hensure
                    .Any(x =>
                    x.dependentRocId == data.dependentRocId &&
                    x.userId == data.userId &&
                    x.IsDeleted != true);
            }

            if (idNoExists)
            {
                list.Add("該眷屬資料已存在，請直接新增投保起迄範圍");
            }

            if (string.IsNullOrWhiteSpace(data.userId))
                list.Add("依附者編號獲取錯誤");

            if (!string.IsNullOrEmpty(data.HealthInsStartDate) && !_baseform.IsValidDateOrEmpty(data.HealthInsStartDate))
                list.Add("投保起日格式錯誤");

            if (!string.IsNullOrEmpty(data.HealthInsEndDate) && !_baseform.IsValidDateOrEmpty(data.HealthInsEndDate))
                list.Add("投保迄日格式錯誤");

            // 根據實際需求可加上必要欄位檢查
            if (string.IsNullOrWhiteSpace(data.dependentRocId))
                list.Add("請輸入眷屬身分證字號");
            if (string.IsNullOrWhiteSpace(data.dependentName))
                list.Add("請輸入眷屬姓名");

            // 若有以上錯誤直接回傳
            if (list.Any()) return list;

            // 把日期字串轉成 timestamp (long秒)
            long startTimestamp = _baseform.DateStrToTimestamp(data.HealthInsStartDate) ?? 0;
            long? endTimestamp = _baseform.DateStrToTimestamp(data.HealthInsEndDate);

            // 1. 檢查是否有未結束的投保期間(同一眷屬)
            var hasOngoingPeriod = _context.Pas_Hensure.Any(x =>
                x.userId == data.userId &&
                x.dependentRocId == data.dependentRocId &&
                x.IsDeleted != true &&
                (x.HealthInsEndDate == null || x.HealthInsEndDate == 0)); // 迄日為 null 或 0 表示未結束

            if (_mode == "add" && hasOngoingPeriod)
            {
                list.Add("已有未結束的投保期間，無法新增新的投保期間");
            }

            // 2. 檢查新投保期間是否與既有投保期間重疊
            var existingPeriods = _context.Pas_Hensure
                .Where(x => x.userId == data.userId &&
                            x.dependentRocId == data.dependentRocId &&
                            x.IsDeleted != true)
                .ToList();

            if (_mode == "edit")
            {
                existingPeriods = existingPeriods.Where(x => x.uid != data.uid).ToList();
            }

            foreach (var period in existingPeriods)
            {
                long periodStart = period.HealthInsStartDate ?? 0;
                long periodEnd = period.HealthInsEndDate ?? long.MaxValue; // 沒迄日視為最大值
                long newEnd = endTimestamp ?? long.MaxValue;

                // 判斷期間是否重疊 (start ≤ periodEnd && newEnd ≥ periodStart)
                bool isOverlap = startTimestamp <= periodEnd && newEnd >= periodStart;

                if (isOverlap)
                {
                    var existingStartStr = _baseform.TimestampToDateStr(periodStart);
                    var existingEndStr = period.HealthInsEndDate.HasValue && period.HealthInsEndDate != 0
                                         ? _baseform.TimestampToDateStr(periodEnd)
                                         : "迄日未定";
                    list.Add($"投保期間與既有期間 {existingStartStr} ~ {existingEndStr} 重疊");
                    break;
                }
            }

            return list;
        }
    }
}
