using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class EnsureService : IEnsureService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public EnsureService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<EnsureDTO>> GetEnsureListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Ensure
                    .Where(e => e.userId == userId && e.IsDeleted != true)
                    .OrderByDescending(e => e.CreateTime)
                    .Select(e => new EnsureDTO
                    {
                        uid = e.uid,
                        userId = e.userId,
                        ensureNumber = e.ensureNumber,
                        guarantorName = e.guarantorName,
                        guarantorPersonalId = e.guarantorPersonalId,
                        guarantorBirthday = _baseform.TimestampToDateStr(e.guarantorBirthday),
                        guarantorAddress = e.guarantorAddress,
                        guarantorPhone = e.guarantorPhone,
                        relationship = e.relationship,
                        guarantorProperty = e.guarantorProperty,
                        propertyValue = e.propertyValue,
                        remark = e.remark,
                        UpdateTime = e.UpdateTime
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得保證書資料錯誤", ex);
            }
        }

        public async Task<EnsureDTO> GetEnsureDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Ensure
                    .Where(e => e.uid == uid && e.IsDeleted != true)
                    .Select(e => new EnsureDTO
                    {
                        uid = e.uid,
                        userId = e.userId,
                        ensureNumber = e.ensureNumber,
                        guarantorName = e.guarantorName,
                        guarantorPersonalId = e.guarantorPersonalId,
                        guarantorBirthday = _baseform.TimestampToDateStr(e.guarantorBirthday),
                        guarantorAddress = e.guarantorAddress,
                        guarantorPhone = e.guarantorPhone,
                        relationship = e.relationship,
                        guarantorProperty = e.guarantorProperty,
                        propertyValue = e.propertyValue,
                        remark = e.remark,
                        UpdateTime = e.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得保證書明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddEnsureAsync(EnsureDTO data)
        {
            var list_msg_check = CheckEnsureInput(data, "add");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newEnsure = new Ensure
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    ensureNumber = data.ensureNumber,
                    guarantorName = data.guarantorName,
                    guarantorPersonalId = data.guarantorPersonalId,
                    guarantorBirthday = _baseform.DateStrToTimestamp(data.guarantorBirthday),
                    guarantorAddress = data.guarantorAddress,
                    guarantorPhone = data.guarantorPhone,
                    relationship = data.relationship,
                    guarantorProperty = data.guarantorProperty,
                    propertyValue = data.propertyValue,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Ensure.AddAsync(newEnsure);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "保證書資料新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增保證書資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditEnsureAsync(EnsureDTO data)
        {
            var list_msg_check = CheckEnsureInput(data, "edit");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Ensure.FirstOrDefaultAsync(e => e.uid == data.uid && e.IsDeleted != true);
                if (exist == null)
                    return (false, "找不到對應的保證書資料");

                exist.ensureNumber = data.ensureNumber;
                exist.guarantorName = data.guarantorName;
                exist.guarantorPersonalId = data.guarantorPersonalId;
                exist.guarantorBirthday = _baseform.DateStrToTimestamp(data.guarantorBirthday);
                exist.guarantorAddress = data.guarantorAddress;
                exist.guarantorPhone = data.guarantorPhone;
                exist.relationship = data.relationship;
                exist.guarantorProperty = data.guarantorProperty;
                exist.propertyValue = data.propertyValue;
                exist.remark = data.remark;
                exist.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                exist.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "保證書資料編輯成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯保證書資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteEnsureAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Ensure.FirstOrDefaultAsync(e => e.uid == uid && e.IsDeleted != true);
                if (exist == null)
                    return (false, "資料已刪除或不存在");

                exist.IsDeleted = true;
                exist.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                exist.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除保證書資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除保證書資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckEnsureInput(EnsureDTO data, string mode)
        {
            var list = new List<string>();

            if (string.IsNullOrWhiteSpace(data.ensureNumber))
                list.Add("請輸入保證書編號");

            if (string.IsNullOrWhiteSpace(data.guarantorName))
                list.Add("請輸入保證人姓名");

            if (!string.IsNullOrEmpty(data.guarantorBirthday) && !_baseform.IsValidDateOrEmpty(data.guarantorBirthday))
                list.Add("保證人生日格式錯誤");

            return list;
        }
    }
}
