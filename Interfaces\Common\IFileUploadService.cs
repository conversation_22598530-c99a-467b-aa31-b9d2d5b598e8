using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary> 檔案上傳服務介面 </summary>
    public interface IFileUploadService
    {
        /// <summary> 上傳單一檔案 </summary>
        /// <param name="request">檔案上傳請求</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>上傳結果和檔案資訊</returns>
        Task<(bool Success, string Message, FileUploadDTO? FileInfo)> UploadFileAsync(FileUploadRequestDTO request, string userId);

        /// <summary> 批量上傳檔案 </summary>
        /// <param name="request">批量檔案上傳請求</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>上傳結果和檔案資訊列表</returns>
        Task<(bool Success, string Message, List<FileUploadDTO> FileInfos)> UploadFilesAsync(BatchFileUploadRequestDTO request, string userId);

        /// <summary> 下載檔案 </summary>
        /// <param name="request">檔案下載請求</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>檔案流和檔案資訊</returns>
        Task<(bool Success, string Message, Stream? FileStream, FileUploadDTO? FileInfo)> DownloadFileAsync(FileDownloadRequestDTO request,  string userId);

        /// <summary> 刪除檔案 /// </summary>
        /// <param name="fileUploadId">檔案編號</param>
        /// <param name="enterpriseGroupsId">企業群組編號</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>刪除結果</returns>
        Task<(bool Success, string Message)> DeleteFileAsync(string fileUploadId, string enterpriseGroupsId, string userId);

        /// <summary> 取得檔案列表 </summary>
        /// <param name="request">檔案查詢請求</param>
        /// <returns>檔案列表</returns>
        Task<(bool Success, string Message, List<FileUploadDTO> Files, int TotalCount)> GetFilesAsync( FileQueryRequestDTO request);

        /// <summary> 取得檔案資訊 </summary>
        /// <param name="fileUploadId">檔案編號</param>
        /// <param name="enterpriseGroupsId">企業群組編號</param>
        /// <returns>檔案資訊</returns>
        Task<(bool Success, string Message, FileUploadDTO? FileInfo)> GetFileInfoAsync(string fileUploadId, string enterpriseGroupsId);

        /// <summary> 更新檔案資訊 </summary>
        /// <param name="fileInfo">檔案資訊</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>更新結果</returns>
        Task<(bool Success, string Message)> UpdateFileInfoAsync( FileUploadDTO fileInfo, string userId);

        /// <summary> 檢查檔案是否存在 </summary>
        /// <param name="fileUploadId">檔案編號</param>
        /// <param name="enterpriseGroupsId">企業群組編號</param>
        /// <returns>檔案是否存在</returns>
        Task<bool> FileExistsAsync(string fileUploadId, string enterpriseGroupsId);

        /// <summary> 根據雜湊值檢查重複檔案 </summary>
        /// <param name="fileHash">檔案雜湊值</param>
        /// <param name="enterpriseGroupsId">企業群組編號</param>
        /// <returns>重複檔案資訊</returns>
        Task<FileUploadDTO?> GetDuplicateFileAsync(string fileHash, string enterpriseGroupsId);

        /// <summary> 清理過期檔案 </summary>
        /// <param name="daysOld">過期天數</param>
        /// <returns>清理結果</returns>
        Task<(bool Success, string Message, int CleanedCount)> CleanupExpiredFilesAsync(int daysOld = 30);

        /// <summary> 取得企業群組的儲存統計 </summary>
        /// <param name="enterpriseGroupsId">企業群組編號</param>
        /// <returns>儲存統計資訊</returns>
        Task<(long TotalFiles, long TotalSize, long TotalDownloads)> GetStorageStatsAsync( string enterpriseGroupsId);

        /// <summary> 驗證檔案類型和大小 </summary>
        /// <param name="file">檔案</param>
        /// <param name="allowedExtensions">允許的副檔名</param>
        /// <param name="maxSizeInMB">最大檔案大小（MB）</param>
        /// <returns>驗證結果</returns>
        (bool IsValid, string Message) ValidateFile(
            IFormFile file, 
            string[] allowedExtensions = null, 
            int maxSizeInMB = 10);

        /// <summary> 產生檔案雜湊值 </summary>
        /// <param name="fileStream">檔案流</param>
        /// <returns>SHA256雜湊值</returns>
        Task<string> GenerateFileHashAsync(Stream fileStream);
    }
}