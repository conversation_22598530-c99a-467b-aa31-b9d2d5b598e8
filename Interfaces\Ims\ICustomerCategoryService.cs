using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Interfaces.Ims;

/// <summary> 客戶分類服務介面 </summary>
public interface ICustomerCategoryService
{
    /// <summary> 取得所有客戶分類 </summary>
    Task<List<CustomerCategoryDTO>> GetAllAsync();

    /// <summary> 根據ID取得客戶分類 </summary>
    Task<CustomerCategoryDTO?> GetByIdAsync(Guid customerCategoryId);

    /// <summary> 新增客戶分類 </summary>
    Task<(bool Success, string Message)> AddAsync(CustomerCategoryDTO dto);

    /// <summary> 更新客戶分類 </summary>
    Task<(bool Success, string Message)> UpdateAsync(CustomerCategoryDTO dto);

    /// <summary> 刪除客戶分類 </summary>
    Task<(bool Success, string Message)> DeleteAsync(Guid customerCategoryId);

    /// <summary> 檢查分類層級 </summary>
    Task<bool> CheckLevelAsync(Guid? parentId, int maxDepth);

    /// <summary> 取得子分類 </summary>
    Task<List<CustomerCategoryDTO>> GetChildrenAsync(Guid parentId);

    /// <summary> 取得根分類 </summary>
    Task<List<CustomerCategoryDTO>> GetRootCategoriesAsync();
}
