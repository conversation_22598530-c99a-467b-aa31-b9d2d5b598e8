"use client";

import React, { useCallback, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Upload } from "antd";
import {
  UploadOutlined,
  FileExcelOutlined,
  CloseOutlined,
} from "@ant-design/icons";

interface FileUploadProps {
  onFileUpload: (file: File) => void;
  isLoading?: boolean;
}

export function FileUpload({
  onFileUpload,
  isLoading = false,
}: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // 檢查檔案類型
  const isValidFileType = (file: File) => {
    const validTypes = [
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ];
    const validExtensions = [".xls", ".xlsx"];

    return (
      validTypes.includes(file.type) ||
      validExtensions.some((ext) => file.name.toLowerCase().endsWith(ext))
    );
  };

  // 處理檔案
  const handleFile = useCallback((file: File) => {
    if (!isValidFileType(file)) {
      alert("請選擇Excel檔案 (*.xls, *.xlsx)");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      alert("檔案大小不能超過 10MB");
      return;
    }

    setSelectedFile(file);
  }, []);

  // 拖拽事件處理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFile(e.dataTransfer.files[0]);
      }
    },
    [handleFile]
  );

  // 檔案選擇事件處理
  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        handleFile(e.target.files[0]);
      }
    },
    [handleFile]
  );

  // 上傳檔案
  const handleUpload = () => {
    if (selectedFile) {
      onFileUpload(selectedFile);
    }
  };

  // 移除檔案
  const removeFile = () => {
    setSelectedFile(null);
  };

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      {/* 檔案上傳區域 */}
      <div
        style={{
          border: `2px dashed ${dragActive ? "#1890ff" : "#d9d9d9"}`,
          borderRadius: "8px",
          backgroundColor: dragActive ? "#f0f5ff" : "transparent",
          cursor: "pointer",
          transition: "all 0.3s ease",
          padding: "24px",
        }}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            padding: "32px 24px",
            textAlign: "center",
          }}
        >
          <UploadOutlined
            style={{ fontSize: "48px", color: "#1890ff", marginBottom: "16px" }}
          />
          <div style={{ marginBottom: "16px" }}>
            <h3
              style={{ margin: "0 0 8px 0", fontSize: "18px", fontWeight: 500 }}
            >
              拖拽Excel檔案到此處
            </h3>
            <p style={{ margin: 0, fontSize: "14px", color: "#8c8c8c" }}>
              支援的檔案格式：*.xls, *.xlsx (最大 10MB)
            </p>
          </div>

          <input
            type="file"
            accept=".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            onChange={handleFileSelect}
            style={{ display: "none" }}
            id="file-upload"
            disabled={isLoading}
          />

          <Button
            type="primary"
            onClick={() => document.getElementById("file-upload")?.click()}
            icon={<UploadOutlined />}
            disabled={isLoading}
          >
            選擇檔案
          </Button>
        </div>
      </div>

      {/* 已選擇的檔案 */}
      {selectedFile && (
        <Card style={{ marginTop: "16px" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              padding: "8px 0",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <FileExcelOutlined
                style={{ fontSize: "32px", color: "#52c41a" }}
              />
              <div>
                <p style={{ margin: 0, fontWeight: 500 }}>
                  {selectedFile.name}
                </p>
                <p style={{ margin: 0, fontSize: "12px", color: "#8c8c8c" }}>
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>

            <div style={{ display: "flex", gap: "8px" }}>
              <Button
                danger
                onClick={removeFile}
                disabled={isLoading}
                icon={<CloseOutlined />}
              >
                移除
              </Button>

              <Button
                type="primary"
                onClick={handleUpload}
                loading={isLoading}
                style={{ minWidth: "100px" }}
              >
                {isLoading ? "驗證中..." : "開始驗證"}
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* 使用說明 */}
      <Card title="注意事項" size="small" style={{ marginTop: "16px" }}>
        <ul style={{ margin: 0, paddingLeft: "16px" }}>
          <li style={{ marginBottom: "8px" }}>
            請使用提供的範本格式進行資料整理
          </li>
          <li style={{ marginBottom: "8px" }}>檔案大小不能超過 10MB</li>
          <li style={{ marginBottom: "8px" }}>
            僅支援 Excel 檔案格式 (.xls, .xlsx)
          </li>
          <li style={{ marginBottom: "8px" }}>上傳前系統會先進行格式驗證</li>
          <li>驗證通過後可預覽資料並確認匯入</li>
        </ul>
      </Card>
    </div>
  );
}
