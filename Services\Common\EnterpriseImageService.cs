﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using System;
using System.Drawing;

namespace FAST_ERP_Backend.Services.Common
{
    public class EnterpriseImageService : IEnterpriseImageService
    {
        private readonly ERPDbContext _context;
        private readonly EncryptionHelper _encryptionHelper;
        private readonly TokenHandler _tokenHandler;
        private readonly IWebHostEnvironment _environment;

        public EnterpriseImageService(ERPDbContext context, EncryptionHelper encryptionHelper, TokenHandler tokenHandler, IWebHostEnvironment environment)
        {
            _context = context;
            _encryptionHelper = encryptionHelper;
            _tokenHandler = tokenHandler;
            _environment = environment;
        }

        /// <summary>
        /// 刪除圖片
        /// </summary>
        /// <param name="request"></param>
        /// <param name="tokenUid"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteImageAsync(EnterpriseImageDTO request, string tokenUid = "")
        {
            var existingMenu = await _context.Common_EnterpriseImage
                .FirstOrDefaultAsync(e => e.ImageId == request.ImageId);

            if (existingMenu != null)
            {
                try
                {
                    existingMenu.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingMenu.DeleteUserId = request.DeleteUserId;
                    existingMenu.IsDeleted = true;

                    await _context.SaveChangesAsync();
                    return (true, "刪除圖片成功");
                }
                catch (Exception ex)
                {
                    return (false, $"刪除圖片失敗: {ex.Message}");
                }
            }
            return (false, "圖片不存在");
        }

        /// <summary>
        /// 取得圖片
        /// </summary>
        /// <param name="ImageId"></param>
        /// <returns></returns>
        public async Task<EnterpriseImageDTO> GetImageAsync(string ImageId)
        {
            var entity = await _context.Set<EnterpriseImage>()
        .FirstOrDefaultAsync(x => x.ImageId == ImageId);

            if (entity == null)
            {
                return null;
            }

            return new EnterpriseImageDTO
            {
                ImagePath = entity.ImagePath
            };
        }

        /// <summary>
        /// 取得圖片列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<EnterpriseImageDTO>> GetImageListAsync()
        {
            try
            {
                var entities = await _context.Set<EnterpriseImage>().ToListAsync();
                var dtos = entities.Select(entity => new EnterpriseImageDTO
                {
                    ImageId = entity.ImageId,
                    ImageType = entity.ImageType,
                    ImageName = entity.ImageName,
                    ImagePath = entity.ImagePath,
                    CreateTime = entity.CreateTime,
                    CreateUserId = entity.CreateUserId,
                    UpdateTime = entity.UpdateTime,
                    UpdateUserId = entity.UpdateUserId
                }).ToList();

                return dtos;
            }
            catch (Exception ex)
            {
                return new List<EnterpriseImageDTO> { new EnterpriseImageDTO { ImageId = $"取得列表失敗: {ex.Message}" } };
            }
        }

        /// <summary>
        /// 上傳圖片
        /// </summary>
        /// <param name="request">EnterpriseImageUploadDTO 實例</param>
        /// <param name="tokenUid">用戶標識</param>
        /// <returns>上傳結果</returns>
        public async Task<(bool, string)> UploadImageAsync(EnterpriseImageUploadDTO request, string tokenUid = "")
        {
            try
            {
                // 生成新的Guid
                var newGuid = Guid.NewGuid().ToString();

                // 檢查檔案是否為空
                if (request.ImageFile.Length == 0)
                {
                    return (false, "檔案為空");
                }

                // 確定上傳目錄
                var uploadRoot = _environment.WebRootPath ?? Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
                var uploadPath = Path.Combine(uploadRoot, "uploads");
                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                // 確定文件路徑
                var fileName = $"{newGuid}_{request.ImageFile.FileName}";
                var filePath = Path.Combine(uploadPath, fileName);

                // 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await request.ImageFile.CopyToAsync(stream);
                }

                // 設置相對路徑
                var relativePath = Path.Combine("uploads", fileName);

                var newImage = new EnterpriseImage
                {
                    ImageId = newGuid,
                    ImageName = request.ImageName,
                    ImageType = request.ImageType,
                    ImagePath = relativePath,
                    CreateUserId = request.CreateUserId,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };
                await _context.Common_EnterpriseImage.AddAsync(newImage);
                await _context.SaveChangesAsync();

                return (true, "上傳成功");
            }
            catch (Exception ex)
            {
                return (false, $"上傳失敗: {ex.Message}");
            }
        }
    }
}