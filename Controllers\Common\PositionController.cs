using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("職稱資料管理")]
    public class PositionController : ControllerBase
    {
        private readonly IPositionService _interface;

        public PositionController(IPositionService positionService)
        {
            _interface = positionService;
        }

        // 取得所有職稱資料
        [HttpGet]
        [Route("GetPosition")]
        [SwaggerOperation(Summary = "取得職稱列表", Description = "取得所有職稱列表")]
        public async Task<IActionResult> GetPositionList()
        {
            var positions = await _interface.GetPositionAsync();
            return Ok(positions);
        }

        // 取得指定職稱資料
        [HttpGet]
        [Route("GetPosition/{_positionId?}")]
        [SwaggerOperation(Summary = "取得職稱明細", Description = "依ID取得職稱明細")]
        public async Task<IActionResult> GetPositionDetail(string _positionId)
        {
            var position = await _interface.GetPositionAsync(_positionId);
            return Ok(position);
        }

        // 新增職稱資料
        [HttpPost]
        [Route("AddPosition")]
        [SwaggerOperation(Summary = "新增職稱", Description = "新增職稱資料")]
        public async Task<IActionResult> AddPosition([FromBody] PositionDTO _data)
        {
            var (result, msg) = await _interface.AddPositionAsync(_data);
            return Ok(new { result, msg });
        }

        // 更新職稱資料
        [HttpPost]
        [Route("EditPosition")]
        [SwaggerOperation(Summary = "編輯職稱", Description = "修改已存在之職稱資料")]
        public async Task<IActionResult> EditPosition([FromBody] PositionDTO _data)
        {
            var (result, msg) = await _interface.EditPositionAsync(_data);
            return Ok(new { result, msg });
        }

        // 刪除職稱資料
        [HttpPost]
        [Route("DeletePosition")]
        [SwaggerOperation(Summary = "刪除職稱", Description = "刪除已存在之職稱資料")]
        public async Task<IActionResult> DeletePosition([FromBody] PositionDTO _data)
        {
            var (result, msg) = await _interface.DeletePositionAsync(_data);
            return Ok(new { result, msg });
        }
    }
}
