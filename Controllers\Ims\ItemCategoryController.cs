using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Ims;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Ims;
/// <summary> 庫存品分類管理 </summary>
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("庫存品分類管理")]
public class ItemCategoryController(IItemCategoryService _Interface) : ControllerBase
{
    /// <summary> 取得庫存品分類列表 </summary>
    [HttpGet]
    [Route("GetAll")]
    [SwaggerOperation(Summary = "取得庫存品分類列表", Description = "取得庫存品分類列表")]
    public async Task<IActionResult> GetAll()
    {
        try
        {
            var itemCategories = await _Interface.GetAllAsync();
            return Ok(new { success = true, message = "取得庫存品分類列表成功", data = itemCategories });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得庫存品分類列表失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 取得庫存品分類 </summary>
    [HttpGet]
    [Route("{categoryId}")]
    [SwaggerOperation(Summary = "取得庫存品分類", Description = "根據分類ID取得單一庫存品分類詳細資料")]
    public async Task<IActionResult> Get([FromRoute] Guid categoryId)
    {
        try
        {
            if (categoryId == Guid.Empty)
            {
                return BadRequest(new { success = false, message = "分類ID不能為空", data = (object?)null });
            }

            var itemCategory = await _Interface.GetAsync(categoryId);
            if (itemCategory == null)
            {
                return NotFound(new { success = false, message = "找不到指定的分類", data = (object?)null });
            }

            return Ok(new { success = true, message = "取得庫存品分類成功", data = itemCategory });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得庫存品分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 新增庫存品分類 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "新增庫存品分類", Description = "新增庫存品分類")]
    public async Task<IActionResult> Add([FromBody] ItemCategoryDTO _data)
    {
        try
        {
            var (result, msg) = await _Interface.AddAsync(_data);
            if (!result)
            {
                return BadRequest(new { success = false, message = msg, data = (object?)null });
            }
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"新增庫存品分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 更新庫存品分類 </summary>
    [HttpPatch]
    [SwaggerOperation(Summary = "更新庫存品分類", Description = "更新庫存品分類")]
    public async Task<IActionResult> Update([FromBody] ItemCategoryDTO _data)
    {
        try
        {
            var (result, msg) = await _Interface.UpdateAsync(_data);
            if (!result)
            {
                return BadRequest(new { success = false, message = msg, data = (object?)null });
            }
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"更新庫存品分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 刪除庫存品分類 </summary>
    [HttpDelete]
    [SwaggerOperation(Summary = "刪除庫存品分類", Description = "刪除庫存品分類")]
    public async Task<IActionResult> Delete([FromBody] ItemCategoryDTO _data)
    {
        try
        {
            var (result, msg) = await _Interface.DeleteAsync(_data);
            if (!result)
            {
                return BadRequest(new { success = false, message = msg, data = (object?)null });
            }
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"刪除庫存品分類失敗: {ex.Message}", data = (object?)null });
        }
    }
}