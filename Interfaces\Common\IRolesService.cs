﻿using FAST_ERP_Backend.Models.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IRolesService
    {
        Task<List<RolesDTO>> GetRolesAsync(string _rolesId = "");
        Task<(bool, string)> AddRolesAsync(RolesDTO _data, string tokenUid);
        Task<(bool, string)> EditRolesAsync(RolesDTO _data, string tokenUid);
        Task<(bool, string)> DeleteRolesAsync(RolesDTO _data, string tokenUid);
    }

}
