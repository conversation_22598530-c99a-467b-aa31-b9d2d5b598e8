import { useEffect, useState } from 'react';
import { Salary, editSalary, getSalaryDetail } from '@/services/pas/SalaryService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import { getEmployeeContributionOptions, getIncomeTaxTypeOptions, getPayoffTypeOptions } from '@/services/pas/OptionParameterService';
import {
    Button,
    message,
    Card,
    Row,
    Col,
    Descriptions,
    Drawer,
    Form,
    Input,
    Alert,
    Space,
    Tag,
    Typography
} from 'antd';
import {
    DollarOutlined,
    BankOutlined,
    PercentageOutlined,
    SettingOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Text } = Typography;

type SalaryInfoProps = {
    userId: string;
    active: boolean;
};

const SalaryInfo: React.FC<SalaryInfoProps> = ({ userId, active }) => {
    const [salaryDetail, setSalaryDetail] = useState<Salary | null>(null);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [editVisible, setEditVisible] = useState(false);
    const [form] = Form.useForm();
    const employeeContributionTypeViewType = Form.useWatch('employeeContributionType', form);
    const taxTypeViewType = Form.useWatch('taxType', form);

    useEffect(() => {
        if (active) {
            fetchSalaryDetail(userId);
        }
    }, [active, userId]);

    const fetchSalaryDetail = async (userId: string) => {
        if (userId === '') {
            setSalaryDetail(null);
            return;
        }

        setLoading(true);
        setErrorMsg('');

        try {
            const response = await getSalaryDetail(userId);
            if (response.success) {
                if (response.data) {
                    setSalaryDetail(response.data);
                } else {
                    setSalaryDetail(null);
                    message.info('查無資料');
                }
            } else {
                setErrorMsg(response.message || '取得員工薪資資料失敗');
                message.error(response.message || '取得員工薪資資料失敗');
            }
        } catch (err: any) {
            setErrorMsg(err.message || '未知錯誤');
            message.error(err.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const openEditDrawer = () => {
        if (salaryDetail) {
            form.setFieldsValue(salaryDetail);
            setEditVisible(true);
        }
    };

    const handleSave = async () => {
        try {
            const values = await form.validateFields();
            const payload = {
                ...salaryDetail,
                ...values,
                userId: userId,
            };

            const res = await editSalary(payload);
            if (res.success) {
                message.success('儲存成功');
                setEditVisible(false);
                fetchSalaryDetail(userId);
            } else {
                message.error(res.message || '儲存失敗');
            }
        } catch (err) {
            console.error('驗證失敗', err);
        }
    };

    if (!active) return null;

    if (errorMsg) {
        return (
            <Alert
                message="錯誤"
                description={errorMsg}
                type="error"
                showIcon
                style={{ margin: '20px' }}
            />
        );
    }

    return (
        <>
            <Card
                className="salary-info-card"
                loading={loading}
                title={
                    <Space>
                        <DollarOutlined />
                        <span>薪資資料</span>
                        {(salaryDetail && salaryDetail?.userId !== "") && (
                            <Tag color={salaryDetail.salaryStatus === '0' ? 'red' : 'green'}>
                                {salaryDetail.salaryStatusName}
                            </Tag>
                        )}
                    </Space>
                }
                extra={
                    (salaryDetail && salaryDetail?.userId !== "") && (
                        <Button type="primary" onClick={openEditDrawer}>
                            編輯
                        </Button>
                    )
                }
            >
                {(salaryDetail && salaryDetail?.userId !== "") ? (
                    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                        {/* 基本設定 */}
                        <Card
                            size="small"
                            title={<Space><BankOutlined />基本設定</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="轉帳帳號" span={2}>
                                    <Text strong>{salaryDetail.transferAccount}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="備註" span={2}>
                                    <div style={{ whiteSpace: 'pre-wrap' }}>{salaryDetail.remark}</div>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 提撥設定 */}
                        <Card
                            size="small"
                            title={<Space><PercentageOutlined />提撥設定</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Row gutter={[16, 16]}>
                                <Col span={12}>
                                    <Card size="small" bordered={false}>
                                        <Descriptions column={1} size="small">
                                            <Descriptions.Item label="雇主提撥比率">
                                                <Text strong>{salaryDetail.employerContributionRate}%</Text>
                                            </Descriptions.Item>
                                        </Descriptions>
                                    </Card>
                                </Col>
                                <Col span={12}>
                                    <Card size="small" bordered={false}>
                                        <Descriptions column={1} size="small">
                                            <Descriptions.Item label="員工自提方式">
                                                <Text strong>{salaryDetail.employeeContributionTypeName}</Text>
                                            </Descriptions.Item>
                                            {salaryDetail.employeeContributionType === "1" && (
                                                <Descriptions.Item label="員工自提比率">
                                                    <Text strong>{salaryDetail.employeeContributionRate}%</Text>
                                                </Descriptions.Item>
                                            )}
                                            {salaryDetail.employeeContributionType === "2" && (
                                                <Descriptions.Item label="員工自提金額">
                                                    <Text strong>{salaryDetail.employeeContributionAmount}</Text>
                                                </Descriptions.Item>
                                            )}
                                        </Descriptions>
                                    </Card>
                                </Col>
                            </Row>
                        </Card>

                        {/* 稅額設定 */}
                        <Card
                            size="small"
                            title={<Space><DollarOutlined />稅額設定</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="稅額類型">
                                    <Text strong>{salaryDetail.taxTypeName}</Text>
                                </Descriptions.Item>
                                {salaryDetail.taxType === "2" && (
                                    <Descriptions.Item label="計算稅率">
                                        <Text strong>{salaryDetail.fixedTaxRate}%</Text>
                                    </Descriptions.Item>
                                )}
                                {salaryDetail.taxType === "3" && (
                                    <Descriptions.Item label="計算稅額">
                                        <Text strong>{salaryDetail.fixedTaxAmount}</Text>
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        </Card>

                        {/* 系統資料 */}
                        <Card
                            size="small"
                            title={<Space><SettingOutlined />系統資料</Space>}
                            bordered={false}
                            style={{ background: '#fafafa' }}
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="登錄者">
                                    <Text type="secondary">{salaryDetail.createUserId}</Text>
                                </Descriptions.Item>
                                <Descriptions.Item label="最後更新時間">
                                    <Text type="secondary">{salaryDetail.updateTime}</Text>
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>
                    </Space>
                ) : (
                    <Alert
                        message="提示"
                        description="請先選取員工，或尚未建立員工資料"
                        type="info"
                        showIcon
                        style={{ margin: '20px' }}
                    />
                )}
            </Card>

            {/* 編輯 Drawer 區塊 */}
            <Drawer
                title="編輯薪資資料"
                width={720}
                open={editVisible}
                onClose={() => setEditVisible(false)}
                footer={
                    <div style={{ textAlign: 'right' }}>
                        <Button onClick={() => setEditVisible(false)} style={{ marginRight: 8 }}>
                            取消
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            儲存
                        </Button>
                    </div>
                }
            >
                <Form form={form} layout="vertical">
                    {/* 基本設定 */}
                    <div className="form-section">
                        <Typography.Title level={5}>
                            <Space>
                                <BankOutlined />
                                基本設定
                            </Space>
                        </Typography.Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item name="salaryStatus" label="發薪狀態">
                                    <ApiSelect fetchOptions={getPayoffTypeOptions} />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item name="transferAccount" label="轉帳帳號">
                                    <Input />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 提撥設定 */}
                    <div className="form-section">
                        <Typography.Title level={5}>
                            <Space>
                                <PercentageOutlined />
                                提撥設定
                            </Space>
                        </Typography.Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    name="employerContributionRate"
                                    label="雇主提撥比率"
                                    rules={[
                                        { required: true, message: '請輸入雇主提撥比率' }
                                    ]}
                                >
                                    <Input
                                        type="number"
                                        suffix="%"
                                        min={0}
                                        max={100}
                                        step={0.1}
                                        style={{ width: '100%' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item name="employeeContributionType" label="員工自提方式">
                                    <ApiSelect fetchOptions={getEmployeeContributionOptions} />
                                </Form.Item>
                            </Col>
                            {employeeContributionTypeViewType === '1' && (
                                <Col xs={24} sm={24} md={12}>
                                    <Form.Item
                                        name="employeeContributionRate"
                                        label="員工自提比率"
                                        rules={[{ required: true, message: '請輸入自提比率' }]}
                                    >
                                        <Input
                                            type="number"
                                            suffix="%"
                                            min={0}
                                            max={100}
                                            step={0.1}
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Col>
                            )}
                            {employeeContributionTypeViewType === '2' && (
                                <Col xs={24} sm={24} md={12}>
                                    <Form.Item
                                        name="employeeContributionAmount"
                                        label="員工自提金額"
                                        rules={[{ required: true, message: '請輸入自提金額' }]}
                                    >
                                        <Input
                                            type="number"
                                            min={0}
                                            step={1}
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Col>
                            )}
                        </Row>
                    </div>

                    {/* 稅額設定 */}
                    <div className="form-section">
                        <Typography.Title level={5}>
                            <Space>
                                <DollarOutlined />
                                稅額設定
                            </Space>
                        </Typography.Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item name="taxType" label="稅額類型">
                                    <ApiSelect fetchOptions={getIncomeTaxTypeOptions} />
                                </Form.Item>
                            </Col>
                            {taxTypeViewType === '2' && (
                                <Col xs={24} sm={24} md={12}>
                                    <Form.Item
                                        name="fixedTaxRate"
                                        label="計算稅率"
                                        rules={[{ required: true, message: '請輸入計算稅率' }]}
                                    >
                                        <Input
                                            type="number"
                                            suffix="%"
                                            min={0}
                                            max={100}
                                            step={0.1}
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Col>
                            )}
                            {taxTypeViewType === '3' && (
                                <Col xs={24} sm={24} md={12}>
                                    <Form.Item
                                        name="fixedTaxAmount"
                                        label="計算稅額"
                                        rules={[{ required: true, message: '請輸入計算稅額' }]}
                                    >
                                        <Input
                                            type="number"
                                            min={0}
                                            step={1}
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Col>
                            )}
                        </Row>
                    </div>

                    {/* 其他資訊 */}
                    <div className="form-section">
                        <Typography.Title level={5}>
                            <Space>
                                <SettingOutlined />
                                其他資訊
                            </Space>
                        </Typography.Title>
                        <Row gutter={[24, 16]}>
                            <Col span={24}>
                                <Form.Item name="remark" label="備註">
                                    <Input.TextArea rows={3} />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </Form>

            </Drawer>
        </>
    );
};

export default SalaryInfo;
