# 財產位置變動單 API 使用說明

## 概述

財產位置變動單功能允許使用者建立、管理和執行財產的位置變動，包括存放地點、保管人、使用人、部門、股別等資訊的變更。

## 業務流程

1. **申請階段**：使用者建立財產位置變動單
2. **審核階段**：具權限人員審核變動單（核准/拒絕）
3. **執行階段**：已核准的變動單可以執行，自動更新財產主檔

## API 端點

### 1. 取得財產位置變動單列表

**GET** `/api/AssetLocationTransfer`

**查詢參數：**

- `searchTerm` (string, optional)：搜尋關鍵字（變動單號或變動原因）
- `approvalStatus` (string, optional)：審核狀態（PENDING/APPROVED/REJECTED）
- `executionStatus` (string, optional)：執行狀態（PENDING/COMPLETED/CANCELLED）
- `startDate` (long, optional)：開始日期（Unix 時間戳）
- `endDate` (long, optional)：結束日期（Unix 時間戳）

**回應範例：**

```json
[
  {
    "transferId": "123e4567-e89b-12d3-a456-426614174000",
    "transferNo": "LT202412001",
    "transferDate": 1703980800,
    "applicantId": "USER001",
    "applicantName": "張三",
    "applicantDepartmentId": "DEPT001",
    "applicantDepartmentName": "資訊部",
    "transferReason": "部門調動",
    "approvalStatus": "PENDING",
    "executionStatus": "PENDING"
  }
]
```

### 2. 取得指定變動單詳細資料

**GET** `/api/AssetLocationTransfer/{transferId}`

**回應範例：**

```json
{
  "transfer": {
    "transferId": "123e4567-e89b-12d3-a456-426614174000",
    "transferNo": "LT202412001",
    "transferDate": 1703980800,
    "applicantId": "USER001",
    "transferReason": "部門調動",
    "approvalStatus": "PENDING",
    "executionStatus": "PENDING"
  },
  "details": [
    {
      "detailId": "456e7890-e89b-12d3-a456-426614174001",
      "assetId": "789e1234-e89b-12d3-a456-426614174002",
      "assetNo": "A001",
      "assetName": "桌上型電腦",
      "originalLocationId": "LOC001",
      "originalLocationName": "一樓辦公室",
      "newLocationId": "LOC002",
      "newLocationName": "二樓辦公室",
      "originalCustodianId": "USER001",
      "originalCustodianName": "張三",
      "newCustodianId": "USER002",
      "newCustodianName": "李四",
      "changeItems": "位置,保管人"
    }
  ]
}
```

### 3. 新增財產位置變動單

**POST** `/api/AssetLocationTransfer`

**請求範例：**

```json
{
  "transfer": {
    "transferDate": 1703980800,
    "applicantDepartmentId": "DEPT001",
    "transferReason": "部門調動",
    "notes": "因應組織調整進行設備移轉"
  },
  "details": [
    {
      "assetId": "789e1234-e89b-12d3-a456-426614174002",
      "originalLocationId": "LOC001",
      "newLocationId": "LOC002",
      "originalCustodianId": "USER001",
      "newCustodianId": "USER002",
      "originalUserId": "USER001",
      "newUserId": "USER002",
      "originalDepartmentId": "DEPT001",
      "newDepartmentId": "DEPT002",
      "originalDivisionId": "DIV001",
      "newDivisionId": "DIV002",
      "changeItems": "位置,保管人,使用人,部門,股別",
      "detailNotes": "設備移至新辦公室"
    }
  ]
}
```

**回應範例：**

```json
{
  "message": "新增財產位置變動單成功",
  "transferNo": "LT202412001"
}
```

### 4. 更新財產位置變動單

**PUT** `/api/AssetLocationTransfer/{transferId}`

**請求格式：** 同新增 API，但會包含 transferId

### 5. 刪除財產位置變動單

**DELETE** `/api/AssetLocationTransfer/{transferId}`

**回應範例：**

```json
{
  "message": "刪除成功"
}
```

### 6. 審核財產位置變動單

**POST** `/api/AssetLocationTransfer/{transferId}/approve`

**請求範例：**

```json
{
  "approvalStatus": "APPROVED",
  "approvalComments": "審核通過，同意進行位置變動"
}
```

**審核狀態說明：**

- `APPROVED`：核准
- `REJECTED`：拒絕

### 7. 執行財產位置變動

**POST** `/api/AssetLocationTransfer/{transferId}/execute`

**請求範例：**

```json
{
  "executionStatus": "COMPLETED"
}
```

**執行狀態說明：**

- `COMPLETED`：完成執行
- `CANCELLED`：取消執行

### 8. 取得財產目前位置資訊

**GET** `/api/AssetLocationTransfer/asset/{assetId}/current-location`

**回應範例：**

```json
{
  "assetId": "789e1234-e89b-12d3-a456-426614174002",
  "assetNo": "A001",
  "assetName": "桌上型電腦",
  "originalLocationId": "LOC001",
  "originalLocationName": "一樓辦公室",
  "originalCustodianId": "USER001",
  "originalCustodianName": "張三"
}
```

### 9. 驗證財產是否可變動

**GET** `/api/AssetLocationTransfer/asset/{assetId}/validate`

**回應範例：**

```json
{
  "isValid": true,
  "message": "財產可以進行位置變動"
}
```

### 10. 產生變動單號

**GET** `/api/AssetLocationTransfer/generate-transfer-no?transferDate={transferDate}`

**回應範例：**

```json
"LT202412001"
```

## 狀態說明

### 審核狀態 (ApprovalStatus)

- `PENDING`：待審核
- `APPROVED`：已核准
- `REJECTED`：已拒絕

### 執行狀態 (ExecutionStatus)

- `PENDING`：待執行
- `COMPLETED`：已完成
- `CANCELLED`：已取消

## 變動單號規則

格式：`LT + 年月 + 序號`

- LT：Location Transfer 縮寫
- 年月：YYYYMM 格式
- 序號：4位數字，從 0001 開始

範例：`LT202412001`（2024年12月第1號）

## 業務規則

1. **建立變動單**：
   - 必須指定至少一項變動內容
   - 同一財產在同時間只能有一個進行中的變動單

2. **審核流程**：
   - 只有狀態為 PENDING 的變動單可以審核
   - 審核後狀態不可再修改

3. **執行流程**：
   - 只有 APPROVED 狀態的變動單可以執行
   - 執行時會自動更新財產主檔的相關資訊
   - 執行完成後不可再修改

4. **刪除限制**：
   - 只有 PENDING 狀態的變動單可以刪除
   - 已審核或執行的變動單不可刪除

## 錯誤處理

常見錯誤碼和訊息：

- `400 Bad Request`：請求參數錯誤或業務規則驗證失敗
- `401 Unauthorized`：未授權或使用者資訊無效
- `404 Not Found`：找不到指定的變動單或財產
- `500 Internal Server Error`：系統內部錯誤

## 使用注意事項

1. 所有日期欄位都使用 Unix 時間戳格式
2. 變動單建立後會自動設定申請人為目前登入使用者
3. 執行變動時會在事務中進行，確保資料一致性
4. 建議在執行前先呼叫驗證 API 確認財產狀態
