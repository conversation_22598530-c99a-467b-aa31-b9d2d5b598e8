import * as signalR from '@microsoft/signalr';
import { notifySuccess, notifyError, notifyWarning } from "@/utils/notification";
import { eventBus } from "@/utils/eventBus";
import { getUserInfo } from "@/utils/userInfo";
import { BASE_URL } from "@/config/api";

const HUB_BASE_URL = `${BASE_URL}`;
let connection: signalR.HubConnection | null = null;
let connectionStatusListeners: ((status: string, connectionId: string | null) => void)[] = [];
let currentConnectionId: string | undefined; // 用來儲存當前的 connectionId
let reconnectInterval: NodeJS.Timeout | null = null; // 用來儲存重連的計時器

// 添加狀態監聽器
export const addConnectionStatusListener = (listener: (status: string, connectionId: string | null) => void) => {
  connectionStatusListeners.push(listener);
};

// 移除狀態監聽器
export const removeConnectionStatusListener = (listener: (status: string, connectionId: string | null) => void) => {
  connectionStatusListeners = connectionStatusListeners.filter((l) => l !== listener);
};

// 通知所有監聽器當前狀態和 connectionId
const notifyConnectionStatus = (status: string, connectionId: string | null = null) => {
  connectionStatusListeners.forEach((listener) => listener(status, connectionId));
};

// 開始重連計時器
const startReconnectInterval = () => {
  //notifyWarning("重新連線中");
  notifyConnectionStatus("Reconnecting", null);

  if (!reconnectInterval) {
    reconnectInterval = setInterval(() => {
      console.log("嘗試重新連線...");
      attemptReconnect(); // 定期嘗試重連
    }, 5000); // 每5秒嘗試重連一次
  }
};

// 停止重連計時器
const stopReconnectInterval = () => {
  if (reconnectInterval) {
    clearInterval(reconnectInterval);
    reconnectInterval = null;
  }
};

// 嘗試手動重連
const attemptReconnect = async () => {
  if (connection) {
    try {
      await connection.start();
      currentConnectionId = await connection.invoke<string>("GetConnectionId"); // 獲取 connectionId
      //notifySuccess("重新連接成功");
      notifyConnectionStatus("Connected", currentConnectionId);
      console.log("SignalR Reconnected with connection ID:", currentConnectionId);

      // 重連成功後，停止重連計時器
      stopReconnectInterval();
    } catch (err) {
      console.log("手動重連失敗，將繼續嘗試...", err);
    }
  }
};

// 創建 SignalR 連線
export const createSignalRConnection = async (): Promise<void> => {
  if (!connection) {
    // 取得使用者資訊
    const userInfo = getUserInfo();
    // 準備基本 URL
    let url = `${HUB_BASE_URL}hubs/SignalRHub`;

    // 如果有使用者資訊，就把必要的資訊附加為查詢參數
    if (userInfo) {
      const params = new URLSearchParams({
        userId: userInfo.userId,
        account: userInfo.account,
      });
      url += `?${params.toString()}`;
    }

    connection = new signalR.HubConnectionBuilder()
      .withUrl(url)
      .withAutomaticReconnect([0, 2000, 10000, 30000]) // 可自定義重連時間間隔
      .build();

    // withAutomaticReconnect根據秒數自動重連
    connection.onreconnecting((error) => {
      //notifyWarning("重新連線中");
      notifyConnectionStatus("Reconnecting", null);
      //console.log("SignalR Reconnecting...", error);
    });

    // withAutomaticReconnect自動重連成功
    connection.onreconnected((connectionId) => {
      currentConnectionId = connectionId; // 更新 connectionId
      //notifySuccess("重新連接成功");
      notifyConnectionStatus("Connected", connectionId);
      //console.log("SignalR Reconnected with connection ID:", connectionId);
    });

    connection.onclose((error) => {
      //notifyError("斷線");
      notifyConnectionStatus("Disconnected", null);
      //console.log("SignalR connection closed:", error);

      // 自動重連達上限次數後會斷線，後續連線需使用手動重連，開始重連計時器
      startReconnectInterval();
    });

    try {
      await connection.start();
      currentConnectionId = await connection.invoke<string>("GetConnectionId"); // 獲取 connectionId
      //notifySuccess("連線成功");
      notifyConnectionStatus("Connected", currentConnectionId);
      console.log("SignalR Connected with connection ID:", currentConnectionId);
    } catch (err) {
      //notifyError("連線失敗");
      notifyConnectionStatus("Connection Failed", null);
      console.error("SignalR Connection Error: ", err);

      // 初始連線失敗不會進入自動重連，需使用手動重連，開始重連計時器
      startReconnectInterval();
    }

    // 接收來自後端的事件
    connection.on("ReceiveNotification", (type, action, data) => {
      console.log(`收到事件: ${type} - ${action}`, data);

      if (!type || !action) {
        console.warn("無效事件格式:", { type, action, data });
        return;
      }

      // 傳遞事件到 eventBus
      eventBus.emit(type, { action, data });
    });

  }
};

// 停止 SignalR 連線(釋放connection 後續不再繼續重連 通常登出使用 登入在建立新的connection)
export const stopSignalRConnection = async (): Promise<void> => {
  if (connection) {
    try {
      await connection.stop();
      //notifyError("中斷連線");
      notifyConnectionStatus("Disconnected", null);
      console.log("SignalR Disconnected");

      // 停止重連計時器
      stopReconnectInterval();

      // 釋放 connection 資源
      connection = null; // 清除 connection
    } catch (err) {
      notifyError("中斷連線失敗");
      console.error("Error stopping SignalR connection: ", err);
    }
  }
};

// services/signalRService.ts
export const getCurrentConnectionStatus = (): { status: string, connectionId: string | null } => {
  let status = "未連線";
  if (connection) {
    switch (connection.state) {
      case signalR.HubConnectionState.Connected:
        status = "Connected";
        break;
      case signalR.HubConnectionState.Connecting:
        status = "Connecting";
        break;
      case signalR.HubConnectionState.Reconnecting:
        status = "Reconnecting";
        break;
      case signalR.HubConnectionState.Disconnected:
        status = "Disconnected";
        break;
    }
  }
  return { status, connectionId: currentConnectionId || null };
};

// 請求後端連線數
export const fetchCurrentConnectionCount = async (): Promise<string | undefined> => {
  if (connection) {
    try {
      const connectionCount = await connection.invoke<number>("GetCurrentConnectionCount");
      return `${connectionCount}`;
    } catch (error) {
      return undefined;
    }
  }
};

