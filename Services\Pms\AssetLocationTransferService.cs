using Microsoft.EntityFrameworkCore;
using AutoMapper;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Server.Tools;

namespace FAST_ERP_Backend.Services.Pms
{
    /// <summary>
    /// 財產位置變動單服務
    /// </summary>
    public class AssetLocationTransferService : IAssetLocationTransferService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;
        private readonly Baseform _baseform;

        /// <summary>
        /// 初始化財產位置變動單服務
        /// </summary>
        /// <param name="context">資料庫內容</param>
        /// <param name="mapper">物件對映器</param>
        /// <param name="baseform">基本表單工具</param>
        public AssetLocationTransferService(ERPDbContext context, IMapper mapper, Baseform baseform)
        {
            _context = context;
            _mapper = mapper;
            _baseform = baseform;
        }

        /// <summary>
        /// 取得財產位置變動單列表
        /// </summary>
        public async Task<List<AssetLocationTransferDTO>> GetTransfersAsync(
            string? searchTerm,
            string? approvalStatus,
            string? executionStatus,
            long? startDate,
            long? endDate)
        {
            try
            {
                var query = _context.Set<AssetLocationTransfer>()
                    .Where(t => !t.IsDeleted);

                // 搜尋條件
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(t => t.TransferNo.Contains(searchTerm) ||
                                           t.TransferReason.Contains(searchTerm));
                }

                // 審核狀態篩選
                if (!string.IsNullOrEmpty(approvalStatus))
                {
                    query = query.Where(t => t.ApprovalStatus == approvalStatus);
                }

                // 執行狀態篩選
                if (!string.IsNullOrEmpty(executionStatus))
                {
                    query = query.Where(t => t.ExecutionStatus == executionStatus);
                }

                // 日期範圍篩選
                if (startDate.HasValue)
                {
                    query = query.Where(t => t.TransferDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(t => t.TransferDate <= endDate.Value);
                }

                var transfers = await query
                    .OrderByDescending(t => t.TransferDate)
                    .ToListAsync();

                return _mapper.Map<List<AssetLocationTransferDTO>>(transfers);
            }
            catch (Exception ex)
            {
                throw new Exception($"取得財產位置變動單列表時發生錯誤：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根據變動單號取得財產位置變動單詳細資料
        /// </summary>
        public async Task<AssetLocationTransferWithDetailsDTO?> GetTransferByTransferNoAsync(string transferNo)
        {
            try
            {
                var transfer = await _context.Set<AssetLocationTransfer>()
                    .Include(t => t.TransferDetails)
                    .FirstOrDefaultAsync(t => t.TransferNo == transferNo && !t.IsDeleted);

                if (transfer == null)
                    return null;

                var result = new AssetLocationTransferWithDetailsDTO
                {
                    Transfer = _mapper.Map<AssetLocationTransferDTO>(transfer),
                    Details = _mapper.Map<List<AssetLocationTransferDetailDTO>>(
                        transfer.TransferDetails.Where(d => !d.IsDeleted))
                };

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"取得財產位置變動單詳細資料時發生錯誤：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 新增財產位置變動單
        /// </summary>
        public async Task<(bool success, string message, string? transferNo)> AddTransferAsync(AssetLocationTransferWithDetailsDTO data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var transferNo = await GenerateTransferNoAsync(data.Transfer.TransferDate);

                var transfer = _mapper.Map<AssetLocationTransfer>(data.Transfer);
                transfer.TransferId = Guid.NewGuid();
                transfer.TransferNo = transferNo;
                transfer.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.Set<AssetLocationTransfer>().AddAsync(transfer);

                foreach (var detailDto in data.Details)
                {
                    var detail = _mapper.Map<AssetLocationTransferDetail>(detailDto);
                    detail.DetailId = Guid.NewGuid();
                    detail.TransferId = transfer.TransferId;
                    detail.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    await _context.Set<AssetLocationTransferDetail>().AddAsync(detail);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "新增財產位置變動單成功", transferNo);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗：{ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新財產位置變動單
        /// </summary>
        public async Task<(bool success, string message)> UpdateTransferAsync(AssetLocationTransferWithDetailsDTO data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingTransfer = await _context.Set<AssetLocationTransfer>()
                    .FirstOrDefaultAsync(t => t.TransferId == data.Transfer.TransferId && !t.IsDeleted);

                if (existingTransfer == null)
                {
                    return (false, "找不到指定的變動單");
                }

                _mapper.Map(data.Transfer, existingTransfer);
                existingTransfer.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                var existingDetails = await _context.Set<AssetLocationTransferDetail>()
                    .Where(d => d.TransferId == data.Transfer.TransferId && !d.IsDeleted)
                    .ToListAsync();

                foreach (var detail in existingDetails)
                {
                    detail.IsDeleted = true;
                    detail.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                foreach (var detailDto in data.Details)
                {
                    var detail = _mapper.Map<AssetLocationTransferDetail>(detailDto);
                    detail.DetailId = Guid.NewGuid();
                    detail.TransferId = existingTransfer.TransferId;
                    detail.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                    await _context.Set<AssetLocationTransferDetail>().AddAsync(detail);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "更新成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 刪除財產位置變動單
        /// </summary>
        public async Task<(bool success, string message)> DeleteTransferAsync(string transferNo, string deleteUserId)
        {
            try
            {
                var transfer = await _context.Set<AssetLocationTransfer>()
                    .FirstOrDefaultAsync(t => t.TransferNo == transferNo && !t.IsDeleted);

                if (transfer == null)
                {
                    return (false, "找不到指定的變動單");
                }

                transfer.IsDeleted = true;
                transfer.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                transfer.DeleteUserId = deleteUserId;

                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 審核財產位置變動單
        /// </summary>
        public async Task<(bool success, string message)> ApproveTransferAsync(AssetLocationTransferApprovalDTO approvalData)
        {
            try
            {
                var transfer = await _context.Set<AssetLocationTransfer>()
                    .FirstOrDefaultAsync(t => t.TransferNo == approvalData.TransferNo && !t.IsDeleted);

                if (transfer == null)
                {
                    return (false, "找不到指定的變動單");
                }

                transfer.ApprovalStatus = approvalData.ApprovalStatus;
                transfer.ApproverId = approvalData.ApproverId;
                transfer.ApprovalDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                transfer.ApprovalComments = approvalData.ApprovalComments;
                transfer.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();
                return (true, "審核成功");
            }
            catch (Exception ex)
            {
                return (false, $"審核失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 執行財產位置變動
        /// </summary>
        public async Task<(bool success, string message)> ExecuteTransferAsync(AssetLocationTransferExecutionDTO executionData)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var transfer = await _context.Set<AssetLocationTransfer>()
                    .Include(t => t.TransferDetails)
                    .FirstOrDefaultAsync(t => t.TransferNo == executionData.TransferNo && !t.IsDeleted);

                if (transfer == null)
                {
                    return (false, "找不到指定的變動單");
                }

                if (executionData.ExecutionStatus == "COMPLETED")
                {
                    foreach (var detail in transfer.TransferDetails.Where(d => !d.IsDeleted))
                    {
                        var asset = await _context.Set<Asset>()
                            .FirstOrDefaultAsync(a => a.AssetId == detail.AssetId && !a.IsDeleted);

                        if (asset != null)
                        {
                            if (!string.IsNullOrEmpty(detail.NewLocationId))
                                asset.StorageLocationId = detail.NewLocationId;

                            if (!string.IsNullOrEmpty(detail.NewCustodianId))
                                asset.CustodianId = detail.NewCustodianId;

                            if (!string.IsNullOrEmpty(detail.NewUserId))
                                asset.UserId = detail.NewUserId;

                            if (!string.IsNullOrEmpty(detail.NewDepartmentId))
                                asset.DepartmentId = detail.NewDepartmentId;

                            if (!string.IsNullOrEmpty(detail.NewDivisionId))
                                asset.DivisionId = detail.NewDivisionId;

                            asset.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            asset.UpdateUserId = executionData.ExecutorId;
                        }
                    }
                }

                transfer.ExecutionStatus = executionData.ExecutionStatus;
                transfer.ExecutorId = executionData.ExecutorId;
                transfer.ExecutionDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                transfer.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "執行成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"執行失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 產生變動單號
        /// </summary>
        public async Task<string> GenerateTransferNoAsync(long transferDate)
        {
            try
            {
                var dateTime = DateTimeOffset.FromUnixTimeSeconds(transferDate).ToLocalTime();
                var prefix = $"LT{dateTime:yyyyMM}";

                var maxNo = await _context.Set<AssetLocationTransfer>()
                    .Where(t => t.TransferNo.StartsWith(prefix))
                    .CountAsync();

                var newSequence = maxNo + 1;
                return $"{prefix}{newSequence:D4}";
            }
            catch (Exception ex)
            {
                throw new Exception($"產生變動單號失敗：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 驗證財產是否可以進行位置變動
        /// </summary>
        public async Task<(bool isValid, string message)> ValidateAssetForTransferAsync(string assetNo)
        {
            try
            {
                var asset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetNo == assetNo && !a.IsDeleted);

                if (asset == null)
                {
                    return (false, "找不到指定的財產");
                }

                return (true, "財產可以進行位置變動");
            }
            catch (Exception ex)
            {
                return (false, $"驗證失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 取得財產目前的位置資訊
        /// </summary>
        public async Task<AssetLocationTransferDetailDTO?> GetAssetCurrentLocationAsync(string assetNo)
        {
            try
            {
                var asset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetNo == assetNo && !a.IsDeleted);

                if (asset == null)
                    return null;

                return new AssetLocationTransferDetailDTO
                {
                    AssetId = asset.AssetId,
                    AssetNo = asset.AssetNo,
                    AssetName = asset.AssetName,
                    OriginalLocationId = asset.StorageLocationId ?? "",
                    OriginalCustodianId = asset.CustodianId ?? "",
                    OriginalUserId = asset.UserId ?? "",
                    OriginalDepartmentId = asset.DepartmentId ?? "",
                    OriginalDivisionId = asset.DivisionId ?? ""
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"取得財產位置資訊失敗：{ex.Message}", ex);
            }
        }

    }
}