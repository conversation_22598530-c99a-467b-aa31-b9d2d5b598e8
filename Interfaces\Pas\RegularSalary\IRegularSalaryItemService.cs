using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IRegularSalaryItemService
    {
        Task<List<RegularSalaryItemDTO>> GetAllAsync();
        Task<RegularSalaryItemDTO?> GetByIdAsync(string uid);
        Task<(bool, string)> AddAsync(RegularSalaryItemDTO dto);
        Task<(bool, string)> EditAsync(RegularSalaryItemDTO dto);
        Task<(bool, string)> DeleteAsync(string uid);
        Task<List<CascaderOptionDTO>> GetSalaryItemTypeOptionsAsync();
    }
}
