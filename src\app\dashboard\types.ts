import { Layout } from "react-grid-layout";

export interface WidgetConfig {
    id: string;
    title: string;
    component: string;
    defaultSize: {
        w: number;
        h: number;
    };
    minSize: {
        w: number;
        h: number;
    };
    description?: string;
    category?: string;
    tags?: string[];
    icon?: string;
    author?: string;
    version?: string;
    enabled?: boolean;
    defaultPosition?: {
        x: number;
        y: number;
    };
}

export interface DashboardLayout extends Layout {
    i: string;
    x: number;
    y: number;
    w: number;
    h: number;
    minW?: number;
    minH?: number;
}

export interface DashboardConfig {
    layouts: DashboardLayout[];
    widgets: WidgetConfig[];
}

// Responsive breakpoint types
export type Breakpoint = 'lg' | 'md' | 'sm' | 'xs' | 'xxs';

// Responsive layouts interface
export interface ResponsiveLayouts {
    lg: DashboardLayout[];
    md: DashboardLayout[];
    sm: DashboardLayout[];
    xs: DashboardLayout[];
    xxs: DashboardLayout[];
    [key: string]: DashboardLayout[]; // 添加索引簽名以兼容 react-grid-layout 的 Layouts 類型
}