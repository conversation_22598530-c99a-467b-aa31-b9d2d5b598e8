using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    public class PerformancePointGroup : ModelBaseEntity
    {
        [Key]
        [Comment("群組UID")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; }

        [Comment("群組名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string groupName { get; set; }

        [Comment("加權比例 (群組層級)")]
        [Column(TypeName = "decimal(5, 2)")]
        public decimal weightRatio { get; set; }

        public PerformancePointGroup()
        {
            uid = "";
            groupName = "";
            weightRatio = 1.00m;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }

    }

    public class PerformancePointGroupDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 群組UID
        public string groupName { get; set; } // 群組名稱
        public string weightRatio { get; set; } // 群組加權比例
        public int? pointTypeCount { get; set; } // 此群組底下的點數項目數量（維護參考用）

        public PerformancePointGroupDTO()
        {
            uid = "";
            groupName = "";
            weightRatio = "1.00";
            pointTypeCount = 0;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class PerformancePointType : ModelBaseEntity
    {
        [Key]
        [Comment("點數類型UID")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; }

        [Comment("點數名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string pointName { get; set; }

        [Comment("所屬群組UID")]
        [Column(TypeName = "nvarchar(100)")]
        public string groupUid { get; set; }

        [Comment("加權比例 (點數項目層級)")]
        [Column(TypeName = "decimal(5, 2)")]
        public decimal weightRatio { get; set; }

        public PerformancePointType()
        {
            uid = "";
            pointName = "";
            groupUid = "";
            weightRatio = 1.00m;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class PerformancePointTypeDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 點數類型UID
        public string pointName { get; set; } // 點數名稱
        public string groupUid { get; set; } // 所屬群組UID
        public string? groupName { get; set; } // 群組名稱（顯示用）
        public string weightRatio { get; set; } // 加權比例 (點數項目層級)

        public PerformancePointTypeDTO()
        {
            uid = "";
            pointName = "";
            groupUid = "";
            groupName = "";
            weightRatio = "1.00";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }



    public class PerformancePointRecord : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; }

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; }

        [Comment("點數日期")]
        [Column(TypeName = "bigint")]
        public long? pointDate { get; set; }

        [Comment("點數類型UID")]
        [Column(TypeName = "nvarchar(100)")]
        public string pointUid { get; set; }

        [Comment("點數")]
        [Column(TypeName = "decimal(10, 2)")]
        public decimal point { get; set; }

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string remark { get; set; }

        public PerformancePointRecord()
        {
            uid = "";
            userId = "";
            pointDate = null;
            pointUid = "";
            point = 0.00m;
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 整合點數記錄的 DTO，包含點數類型與所屬群組名稱
    /// </summary>
    public class PerformancePointRecordDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string pointDate { get; set; } // 點數日期 (timestamp)
        public string point { get; set; } // 點數
        public string remark { get; set; } // 備註
        public string pointUid { get; set; } // 點數類型 UID
        public string pointName { get; set; } // 點數名稱
        //public string pointWeightRatio { get; set; } // 點數類型加權比例
        public string groupUid { get; set; } // 群組 UID
        public string groupName { get; set; } // 群組名稱
        //public string groupWeightRatio { get; set; } // 群組加權比例

        public PerformancePointRecordDTO()
        {
            uid = "";
            userId = "";
            pointDate = "";
            point = "0";
            remark = "";

            pointUid = "";
            pointName = "";

            groupUid = "";
            groupName = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    // 點數搜尋用日期範圍
    public class PerformancePointSummaryQuery
    {
        public string? StartDate { get; set; }  // timestamp 開始日期
        public string? EndDate { get; set; }    // timestamp 結束日期
        public string? GroupUid { get; set; } // 群組 UID（可選）
    }

    // 績效點數總覽列表
    public class PerformancePointSummaryResult
    {
        public string userId { get; set; } = "";
        public string userName { get; set; } = "";
        public decimal totalPoint { get; set; }
    }
}