# DB關聯圖

建立者: Slade
建立時間: 2025年1月9日 下午3:07
DB相關: DB相關-Common

```mermaid
erDiagram
  Contract{
    nvarchar(100) ContractId PK
    nvarchar(100) EnterpriseGroupsId PK
    nvarchar(50) Name
    money Amount
    long ConTractDate
    nvarchar(MAX) Remark
    nvarchar(100) FileListId
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId
  }

  FileList{
    nvarchar(100) FileId PK
    nvarchar(100) FileListId
    nvarchar(100) SourceTable  
    nvarchar(50) FileName
    nvarchar(100) FilePath
    nvarchar(MAX) Description
    int SortOrder
    bigint CreateTime   
    nvarchar(100) CreateUserId 
    bigint DeleteTime   
    nvarchar(100) DeleteUserId 
  }

  DocManage {
    nvarchar(100) DocManageId PK
    nvarchar(50) Type
    nvarchar(50) seqNo
    nvarchar(100) Title
    nvarchar(MAX) Content
    nvarchar(100) FileListId
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId
  }

  DocManageFields {
    nvarchar(100) DocManageFieldsId PK
    nvarchar(100) DocManageId PK
    nvarchar(50) FieldCode
    nvarchar(50) FieldName
    nvarchar(Max) FieldValue
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId
  }

  Division {
    nvarchar(100) DivisionId PK 
    nvarchar(50) Name
    nvarchar(100) DepartmentId PK 
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
     
  Department {
    nvarchar(100) DepartmentId PK 
    nvarchar(50) Name
    int SortCode
    nvarchar(100) EnterpriseGroupsId FK 
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }

  EnterpriseGroups {
    nvarchar(100) EnterpriseGroupsId PK 
    nvarchar(50) Name
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
    
  Position {
    nvarchar(100) PositionId PK 
    nvarchar(50) Name
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId   
  }

  JobRank {
    nvarchar(100) JobRankId PK 
    nvarchar(50) Name
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
    
  Users {
    nvarchar(100) UserId 
    nvarchar(50) Account PK
    nvarchar(100) DivisionId FK
    nvarchar(100) DepartmentId FK
    nvarchar(100) EnterpriseGroupsId FK
    nvarchar(100) RolesId FK
    nvarchar(100) PositionId FK
    nvarchar(100) JobRank FK
    nvarchar(50) EMail
    nvarchar(50) Address
    nvarchar(15) TelNo
    nvarchar(10) Phone
    int SortCode
    bigint UnlockTime
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId
  }
    
  Roles {
    nvarchar(100) RolesId PK 
    nvarchar(50) Name
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
    
  RolesPermissions {
    nvarchar(100) RolesPermissionsId PK 
    nvarchar(100) RolesId FK
    nvarchar(100) SystemMenuId FK
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
    
  SystemMenu {
    nvarchar(100) SystemMenuId PK 
    nvarchar(50) Name
    nvarchar(100) SystemGroupsId FK 
    nvarchar(50) UrlPath
    nvarchar(100) ParentId
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
    
  SystemGroups {
    nvarchar(100) SystemGroupsId PK 
    nvarchar(50) SystemCode
    nvarchar(50) Name
    nvarchar(200) Option
    nvarchar(MAX) Remark
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }

  EnterpriseImage {
    nvarchar(100) EnterpriseGroupsId PK 
    nvarchar(50) Name
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }

   
  SystemParameters{
    nvarchar(100) SystemParametersId
    nvarchar(50) SystemGroupId
    nvarchar(50) ParameterCode
    nvarchar(50) ParameterName
    nvarchar(MAX) Description
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId
  }

  SystemParametersItem{
    nvarchar(100) SystemParametersItemId
    nvarchar(100) SystemParameterId
    nvarchar(100) Name
    nvarchar(100) Value
    int SortOrder
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId
  }

 Contract ||--|| FileList : ""
 FileList ||--|| DocManage: ""
 Department ||--o{ Division : ""
 Department ||--o{ Users : ""
 Division ||--o{ Users : ""
 EnterpriseGroups ||--o{ Users : ""
 EnterpriseGroups ||--o{ Department : ""
 EnterpriseGroups ||--o{ Contract : ""
 Position ||--o{ Users : ""
 JobRank ||--o{ Users : ""
 Roles ||--o{ Users : ""
 Roles o{--o{ RolesPermissions : ""
 RolesPermissions o{--o{ SystemMenu : ""
 SystemGroups||--o{ SystemMenu : ""
 EnterpriseImage o{--|| EnterpriseGroups : ""
 SystemParameters ||--o{ SystemParametersItem: ""
 SystemGroups ||--o{ SystemParameters: ""
 DocManage ||--o{ DocManageFields: ""
```

```mermaid
erDiagram
  AuditLogs {
    nvarchar(100) AuditLogsId PK 
    nvarchar(MAX) log
    nvarchar(50) IP
    nvarchar(50) RequestUrl
    nvarchar(50) Agent
    bigint CreateTime
    nvarchar(100) CreateUserId
  }
    
  City {
    nvarchar(100) CityId PK 
    nvarchar(50) Name
    nvarchar(100) EnglishName
    nvarchar(500) Description
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
    
  District{
    nvarchar(100) DistrictId PK 
    nvarchar(100) CityId FK 
    nvarchar(50) Name
    nvarchar(100) EnglishName
    nvarchar(10) PostalCode
    nvarchar(500) Description
    int SortCode
    bigint CreateTime
    nvarchar(100) CreateUserId
    bigint UpdateTime
    nvarchar(100) UpdateUserId
    bigint DeleteTime
    nvarchar(100) DeleteUserId 
  }
 
  City ||--o{ District: ""
```