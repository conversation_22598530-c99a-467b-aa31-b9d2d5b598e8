using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("員工常態薪資設定")]
    public class EmployeeRegularSalaryController : ControllerBase
    {
        private readonly IEmployeeRegularSalaryService _service;

        public EmployeeRegularSalaryController(IEmployeeRegularSalaryService service)
        {
            _service = service;
        }

        [HttpGet]
        [Route("GetByUser/{userId}")]
        [SwaggerOperation(Summary = "取得員工常態薪資資料", Description = "根據員工 userId 取得常態薪資清單")]
        public async Task<IActionResult> GetByUser(string userId)
        {
            var result = await _service.GetByUserIdAsync(userId);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得單筆常態薪資資料", Description = "根據 uid 取得單筆員工常態薪資明細")]
        public async Task<IActionResult> GetDetail(string uid)
        {
            var result = await _service.GetByIdAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增員工常態薪資", Description = "新增一筆員工常態薪資資料")]
        public async Task<IActionResult> Add([FromBody] EmployeeRegularSalaryDTO data)
        {
            var (result, msg) = await _service.AddAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯員工常態薪資", Description = "編輯一筆員工常態薪資資料")]
        public async Task<IActionResult> Edit([FromBody] EmployeeRegularSalaryDTO data)
        {
            var (result, msg) = await _service.EditAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除員工常態薪資", Description = "根據 uid 軟刪除員工常態薪資")]
        public async Task<IActionResult> Delete([FromBody] string uid)
        {
            var (result, msg) = await _service.DeleteAsync(uid);
            return Ok(new { result, msg });
        }
    }
}
