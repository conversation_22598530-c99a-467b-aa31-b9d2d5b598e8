using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Services.Ims;

/// <summary> 供應商分類服務 </summary>
public class SupplierCategoryService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : ISupplierCategoryService
{
    private const int MaxAllowedDepth = 3;

    /// <summary> 取得所有供應商分類 </summary>
    public async Task<List<SupplierCategoryDTO>> GetAllAsync()
    {
        var entities = await _context.Ims_SupplierCategory
            .Where(c => !c.IsDeleted)
            .Select(c => new SupplierCategoryDTO
            {
                SupplierCategoryID = c.SupplierCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId,
                IsDeleted = c.IsDeleted
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        return entities;
    }

    /// <summary> 根據ID取得供應商分類 </summary>
    public async Task<SupplierCategoryDTO?> GetByIdAsync(Guid supplierCategoryId)
    {
        var entity = await _context.Ims_SupplierCategory
            .Where(c => c.SupplierCategoryID == supplierCategoryId && !c.IsDeleted)
            .Select(c => new SupplierCategoryDTO
            {
                SupplierCategoryID = c.SupplierCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId,
                IsDeleted = c.IsDeleted
            })
            .FirstOrDefaultAsync();

        return entity;
    }

    /// <summary> 新增供應商分類 </summary>
    public async Task<(bool Success, string Message)> AddAsync(SupplierCategoryDTO dto)
    {
        if (dto == null)
        {
            return (false, "供應商分類資料不可為空");
        }

        // 檢查名稱重複
        if (await _context.Ims_SupplierCategory.AnyAsync(c => c.Name == dto.Name && !c.IsDeleted))
        {
            return (false, "分類名稱已存在");
        }

        // 驗證 ParentID 有效性
        if (dto.ParentID.HasValue)
        {
            if (!await _context.Ims_SupplierCategory.AnyAsync(c => c.SupplierCategoryID == dto.ParentID.Value && !c.IsDeleted))
            {
                return (false, "指定的父分類不存在");
            }

            if (await CheckLevelAsync(dto.ParentID.Value, MaxAllowedDepth))
            {
                return (false, $"供應商分類層級超過最大允許層級 {MaxAllowedDepth} 層!");
            }
        }

        var entity = new SupplierCategory
        {
            SupplierCategoryID = Guid.NewGuid(),
            Name = dto.Name,
            Description = dto.Description,
            ParentID = dto.ParentID,
            SortCode = dto.SortCode,
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId,
            IsDeleted = false
        };

        _context.Ims_SupplierCategory.Add(entity);
        await _context.SaveChangesAsync();

        await _logger.LogInfoAsync($"新增供應商分類成功: {entity.Name}", "SupplierCategoryService");
        return (true, "新增供應商分類成功");
    }

    /// <summary> 更新供應商分類 </summary>
    public async Task<(bool Success, string Message)> UpdateAsync(SupplierCategoryDTO dto)
    {
        if (dto == null)
        {
            return (false, "供應商分類資料不可為空");
        }

        var entity = await _context.Ims_SupplierCategory
            .FirstOrDefaultAsync(c => c.SupplierCategoryID == dto.SupplierCategoryID && !c.IsDeleted);

        if (entity == null)
        {
            return (false, "找不到指定的供應商分類");
        }

        // 檢查名稱重複（排除自己）
        if (await _context.Ims_SupplierCategory.AnyAsync(c => c.Name == dto.Name && c.SupplierCategoryID != dto.SupplierCategoryID && !c.IsDeleted))
        {
            return (false, "分類名稱已存在");
        }

        // 驗證 ParentID 有效性
        if (dto.ParentID.HasValue)
        {
            if (dto.ParentID == dto.SupplierCategoryID)
            {
                return (false, "不能將自己設為父分類");
            }

            if (!await _context.Ims_SupplierCategory.AnyAsync(c => c.SupplierCategoryID == dto.ParentID.Value && !c.IsDeleted))
            {
                return (false, "指定的父分類不存在");
            }

            if (await CheckLevelAsync(dto.ParentID.Value, MaxAllowedDepth))
            {
                return (false, $"供應商分類層級超過最大允許層級 {MaxAllowedDepth} 層!");
            }
        }

        entity.Name = dto.Name;
        entity.Description = dto.Description;
        entity.ParentID = dto.ParentID;
        entity.SortCode = dto.SortCode;
        entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.UpdateUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync();

        await _logger.LogInfoAsync($"更新供應商分類成功: {entity.Name}", "SupplierCategoryService");
        return (true, "更新供應商分類成功");
    }

    /// <summary> 刪除供應商分類 </summary>
    public async Task<(bool Success, string Message)> DeleteAsync(Guid supplierCategoryId)
    {
        var entity = await _context.Ims_SupplierCategory
            .FirstOrDefaultAsync(c => c.SupplierCategoryID == supplierCategoryId && !c.IsDeleted);

        if (entity == null)
        {
            return (false, "找不到指定的供應商分類");
        }

        // 檢查是否有子分類
        if (await _context.Ims_SupplierCategory.AnyAsync(c => c.ParentID == supplierCategoryId && !c.IsDeleted))
        {
            return (false, "此分類下還有子分類，無法刪除");
        }

        // 檢查是否有供應商使用此分類
        if (await _context.Ims_SupplierDetail.AnyAsync(c => c.SupplierCategoryID == supplierCategoryId))
        {
            return (false, "此分類下還有供應商，無法刪除");
        }

        entity.IsDeleted = true;
        entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.DeleteUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync();

        await _logger.LogInfoAsync($"刪除供應商分類成功: {entity.Name}", "SupplierCategoryService");
        return (true, "刪除供應商分類成功");
    }

    /// <summary> 檢查分類層級 </summary>
    public async Task<bool> CheckLevelAsync(Guid? parentId, int maxDepth)
    {
        if (!parentId.HasValue || maxDepth <= 0)
            return false;

        var currentDepth = 1;
        var currentParentId = parentId;

        while (currentParentId.HasValue && currentDepth < maxDepth)
        {
            var parent = await _context.Ims_SupplierCategory
                .Where(c => c.SupplierCategoryID == currentParentId.Value && !c.IsDeleted)
                .Select(c => c.ParentID)
                .FirstOrDefaultAsync();

            if (parent == null)
                break;

            currentParentId = parent;
            currentDepth++;
        }

        return currentDepth >= maxDepth;
    }

    /// <summary> 取得子分類 </summary>
    public async Task<List<SupplierCategoryDTO>> GetChildrenAsync(Guid parentId)
    {
        var children = await _context.Ims_SupplierCategory
            .Where(c => c.ParentID == parentId && !c.IsDeleted)
            .Select(c => new SupplierCategoryDTO
            {
                SupplierCategoryID = c.SupplierCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        return children;
    }

    /// <summary> 取得根分類 </summary>
    public async Task<List<SupplierCategoryDTO>> GetRootCategoriesAsync()
    {
        var rootCategories = await _context.Ims_SupplierCategory
            .Where(c => c.ParentID == null && !c.IsDeleted)
            .Select(c => new SupplierCategoryDTO
            {
                SupplierCategoryID = c.SupplierCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        return rootCategories;
    }
}
