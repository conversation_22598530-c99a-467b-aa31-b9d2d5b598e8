using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class UndergoService : IUndergoService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public UndergoService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<UndergoDTO>> GetUndergoListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Undergo
                    .Where(u => u.userId == userId && u.IsDeleted != true)
                    .OrderByDescending(u => u.hireDate)
                    .Select(u => new UndergoDTO
                    {
                        uid = u.uid,
                        userId = u.userId,
                        agencyName = u.agencyName,
                        departmentName = u.departmentName,
                        jobTitle = u.jobTitle,
                        duty = u.duty,
                        jobGrade = u.jobGrade,
                        hireDate = u.hireDate,
                        terminationDate = u.terminationDate,
                        supervisorName = u.supervisorName,
                        certificateDate = _baseform.TimestampToDateStr(u.certificateDate),
                        certificateNumber = u.certificateNumber,
                        remark = u.remark,
                        UpdateTime = u.UpdateTime
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得歷史經歷資料錯誤", ex);
            }
        }

        public async Task<UndergoDTO> GetUndergoDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Undergo
                    .Where(u => u.uid == uid && u.IsDeleted != true)
                    .Select(u => new UndergoDTO
                    {
                        uid = u.uid,
                        userId = u.userId,
                        agencyName = u.agencyName,
                        departmentName = u.departmentName,
                        jobTitle = u.jobTitle,
                        duty = u.duty,
                        jobGrade = u.jobGrade,
                        hireDate = u.hireDate,
                        terminationDate = u.terminationDate,
                        supervisorName = u.supervisorName,
                        certificateDate = _baseform.TimestampToDateStr(u.certificateDate),
                        certificateNumber = u.certificateNumber,
                        remark = u.remark,
                        UpdateTime = u.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得歷史經歷明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddUndergoAsync(UndergoDTO data)
        {
            var list_msg_check = CheckUndergoInput(data, "add");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newUndergo = new Undergo
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    agencyName = data.agencyName,
                    departmentName = data.departmentName,
                    jobTitle = data.jobTitle,
                    duty = data.duty,
                    jobGrade = data.jobGrade,
                    hireDate = data.hireDate,
                    terminationDate = data.terminationDate,
                    supervisorName = data.supervisorName,
                    certificateDate = _baseform.DateStrToTimestamp(data.certificateDate),
                    certificateNumber = data.certificateNumber,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Undergo.AddAsync(newUndergo);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "歷史經歷資料登錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"歷史經歷資料登錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditUndergoAsync(UndergoDTO data)
        {
            var list_msg_check = CheckUndergoInput(data, "edit");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Undergo.FirstOrDefaultAsync(u => u.uid == data.uid && u.IsDeleted != true);
                if (exist == null)
                    return (false, "找不到對應的歷史經歷資料");

                exist.agencyName = data.agencyName;
                exist.departmentName = data.departmentName;
                exist.jobTitle = data.jobTitle;
                exist.duty = data.duty;
                exist.jobGrade = data.jobGrade;
                exist.hireDate = data.hireDate;
                exist.terminationDate = data.terminationDate;
                exist.supervisorName = data.supervisorName;
                exist.certificateDate = _baseform.DateStrToTimestamp(data.certificateDate);
                exist.certificateNumber = data.certificateNumber;
                exist.remark = data.remark;
                exist.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                exist.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯歷史經歷資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯歷史經歷資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteUndergoAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Undergo.FirstOrDefaultAsync(u => u.uid == uid && u.IsDeleted != true);
                if (exist == null)
                    return (false, "資料已刪除或不存在");

                exist.IsDeleted = true;
                exist.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                exist.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除歷史經歷資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除歷史經歷資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckUndergoInput(UndergoDTO data, string mode)
        {
            var list = new List<string>();

            if (string.IsNullOrWhiteSpace(data.agencyName))
                list.Add("請輸入服務機關名稱");

            //看需不需要檢核年月YYYY-MM
            // if (!string.IsNullOrEmpty(data.hireDate) && !_baseform.IsValidDateOrEmpty(data.hireDate))
            //     list.Add("到職日期格式錯誤");

            // if (!string.IsNullOrEmpty(data.terminationDate) && !_baseform.IsValidDateOrEmpty(data.terminationDate))
            //     list.Add("卸職日期格式錯誤");

            if (!string.IsNullOrEmpty(data.certificateDate) && !_baseform.IsValidDateOrEmpty(data.certificateDate))
                list.Add("發證日期格式錯誤");

            return list;
        }
    }
}
