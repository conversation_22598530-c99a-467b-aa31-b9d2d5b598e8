import { notification } from "antd";
import type { NotificationPlacement } from "antd/es/notification/interface";

// 通知配置類型
interface NotifyConfig {
    duration?: number;
    placement?: NotificationPlacement;
}

// 默認配置
const defaultConfig: NotifyConfig = {
    duration: 3,
    placement: "topRight",
};

// 成功通知
export const notifySuccess = (
    message: string,
    description?: string,
    config: NotifyConfig = defaultConfig
) => {
    notification.success({
        message,
        description,
        ...config,
    });
};

// 錯誤通知
export const notifyError = (
    message: string,
    description?: string,
    config: NotifyConfig = defaultConfig
) => {
    notification.error({
        message,
        description,
        ...config,
    });
};

// 警告通知
export const notifyWarning = (
    message: string,
    description?: string,
    config: NotifyConfig = defaultConfig
) => {
    notification.warning({
        message,
        description,
        ...config,
    });
};

// 信息通知
export const notifyInfo = (
    message: string,
    description?: string,
    config: NotifyConfig = defaultConfig
) => {
    notification.info({
        message,
        description,
        ...config,
    });
}; 