import { httpClient } from '@/services/http';

// 升遷異動資料類型定義
export interface Promotion {
    uid: string;
    userId: string;
    promotionType: string;
    promotionTypeName?: string;
    jobTitle: string;
    jobTitleName?: string;
    jobLevel: string;
    jobLevelName?: string;
    jobRank: string;
    jobRankName?: string;
    promotionDate: string;
    effectiveDate: string;
    promotionReason: string;
    expenseDepartmentChangeUid?: string;
    serviceDepartmentChangeUid?: string;
    jobroleType: string;
    jobroleTypeName?: string;
    salaryType: string;
    salaryTypeName?: string;
    salaryAmount: string;
    categoryType: string;
    categoryTypeName?: string;
    remark: string;
    expenseDepartmentChange?: ExpenseDepartmentChange;
    serviceDepartmentChange?: ServiceDepartmentChange;
    updateTime?: number;
}

// 開支部門異動資料類型定義
export interface ExpenseDepartmentChange {
    uid?: string;
    userId?: string;
    expenseDepartmentId: string;
    expenseDepartmentName?: string;
    changeDate: string;
    effectiveDate: string;
    changeReason: string;
    remark: string;
}

// 服務部門異動資料類型定義
export interface ServiceDepartmentChange {
    uid?: string;
    userId?: string;
    serviceDepartmentId: string;
    serviceDepartmentName?: string;
    serviceDivisionId: string;
    serviceDivisionName?: string;
    changeDate: string;
    effectiveDate: string;
    changeReason: string;
    remark: string;
}

// 表單資料類型定義
export interface PromotionFormData {
    uid?: string;
    userId: string;
    promotionType: string;
    jobTitle: string;
    jobLevel: string;
    jobRank: string;
    promotionDate: string;
    effectiveDate: string;
    promotionReason: string;
    jobroleType: string;
    salaryType: string;
    salaryAmount: string;
    categoryType: string;
    remark: string;

    // 開支部門異動資料
    expenseDepartmentId?: string;
    expenseChangeDate?: string;
    expenseEffectiveDate?: string;
    expenseChangeReason?: string;
    expenseRemark?: string;

    // 服務部門異動資料
    serviceDepartmentId?: string;
    serviceDivisionId?: string;
    serviceChangeDate?: string;
    serviceEffectiveDate?: string;
    serviceChangeReason?: string;
    serviceRemark?: string;
}

// API回應類型
interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
}

// 取得升遷異動列表
export const getPromotionList = async (userId: string): Promise<ApiResponse<Promotion[]>> => {
    try {
        const response = await httpClient<Promotion[]>(`/Promotion/GetAll/${userId}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入升遷異動資料失敗'
        };
    }
};

// 取得升遷異動明細
export const getPromotionDetail = async (uid: string): Promise<ApiResponse<Promotion>> => {
    try {
        const response = await httpClient<Promotion>(`/Promotion/Get/${uid}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入升遷異動明細失敗'
        };
    }
};

// 取得最新升遷資料作為預設值
export const getLatestPromotion = async (userId: string): Promise<ApiResponse<Promotion>> => {
    try {
        const response = await httpClient<Promotion>(`/Promotion/GetLatest/${userId}`);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '載入最新升遷資料失敗'
        };
    }
};

// 新增升遷異動
export const addPromotion = async (formData: FormData): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/Promotion/Add', {
            method: 'POST',
            body: formData
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '新增升遷異動失敗'
        };
    }
};

// 編輯升遷異動
export const editPromotion = async (formData: FormData): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/Promotion/Edit', {
            method: 'POST',
            body: formData
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '編輯升遷異動失敗'
        };
    }
};

// 刪除升遷異動
export const deletePromotion = async (uid: string): Promise<ApiResponse<any>> => {
    try {
        const response = await httpClient<any>('/Promotion/Delete', {
            method: 'POST',
            body: JSON.stringify(uid)
        });
        return {
            success: response.success,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '刪除升遷異動失敗'
        };
    }
};

// 建立空的升遷異動資料
export const createEmptyPromotion = (userId: string): Promotion => {
    return {
        uid: '',
        userId,
        promotionType: '',
        jobTitle: '',
        jobLevel: '',
        jobRank: '',
        promotionDate: '',
        effectiveDate: '',
        promotionReason: '',
        jobroleType: '',
        salaryType: '',
        salaryAmount: '',
        categoryType: '',
        remark: '',
        expenseDepartmentChange: {
            expenseDepartmentId: '',
            changeDate: '',
            effectiveDate: '',
            changeReason: '',
            remark: ''
        },
        serviceDepartmentChange: {
            serviceDepartmentId: '',
            serviceDivisionId: '',
            changeDate: '',
            effectiveDate: '',
            changeReason: '',
            remark: ''
        }
    };
};

// 將升遷資料轉換為FormData
export const createPromotionFormData = (promotion: Promotion): FormData => {
    const formData = new FormData();

    // 升遷基本資料
    formData.append('uid', promotion.uid || '');
    formData.append('userId', promotion.userId);
    formData.append('promotionType', promotion.promotionType);
    formData.append('jobTitle', promotion.jobTitle);
    formData.append('jobLevel', promotion.jobLevel);
    formData.append('jobRank', promotion.jobRank);
    formData.append('promotionDate', promotion.promotionDate);
    formData.append('effectiveDate', promotion.effectiveDate);
    formData.append('promotionReason', promotion.promotionReason);
    formData.append('jobroleType', promotion.jobroleType);
    formData.append('salaryType', promotion.salaryType);
    formData.append('salaryAmount', promotion.salaryAmount);
    formData.append('categoryType', promotion.categoryType);
    formData.append('remark', promotion.remark);

    // 開支部門異動資料
    if (promotion.expenseDepartmentChange) {
        formData.append('expenseDepartmentId', promotion.expenseDepartmentChange.expenseDepartmentId || '');
        formData.append('expenseChangeDate', promotion.expenseDepartmentChange.changeDate || '');
        formData.append('expenseEffectiveDate', promotion.expenseDepartmentChange.effectiveDate || '');
        formData.append('expenseChangeReason', promotion.expenseDepartmentChange.changeReason || '');
        formData.append('expenseRemark', promotion.expenseDepartmentChange.remark || '');
    }

    // 服務部門異動資料
    if (promotion.serviceDepartmentChange) {
        formData.append('serviceDepartmentId', promotion.serviceDepartmentChange.serviceDepartmentId || '');
        formData.append('serviceDivisionId', promotion.serviceDepartmentChange.serviceDivisionId || '');
        formData.append('serviceChangeDate', promotion.serviceDepartmentChange.changeDate || '');
        formData.append('serviceEffectiveDate', promotion.serviceDepartmentChange.effectiveDate || '');
        formData.append('serviceChangeReason', promotion.serviceDepartmentChange.changeReason || '');
        formData.append('serviceRemark', promotion.serviceDepartmentChange.remark || '');
    }

    return formData;
};

// 根據指定日期取得生效的升遷資料
export const getPromotionByEffectiveDate = async (userId: string, effectiveDate?: string): Promise<ApiResponse<Promotion>> => {
    try {
        // 如果有提供日期，驗證日期格式
        if (effectiveDate) {
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(effectiveDate)) {
                throw new Error('日期格式錯誤，請使用 YYYY-MM-DD 格式');
            }
        }

        // 建構API路徑，如果沒有提供日期則不包含日期參數
        const apiPath = effectiveDate
            ? `/Promotion/GetByEffectiveDate/${userId}/${effectiveDate}`
            : `/Promotion/GetByEffectiveDate/${userId}`;

        const response = await httpClient<Promotion>(apiPath);
        return {
            success: response.success || true,
            data: response.data,
            message: response.message
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || '取得指定日期的升遷資料失敗'
        };
    }
}; 