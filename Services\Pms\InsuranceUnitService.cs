﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Pms
{
    public class InsuranceUnitService : IInsuranceUnitService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;

        public InsuranceUnitService(ERPDbContext context, Baseform baseform)
        {
            _context = context;
            _baseform = baseform;
        }

        /// <summary>
        /// 取得保險單位資料
        /// </summary>
        /// <param name="_insuranceUnitId"></param>
        /// <returns></returns>
        public async Task<List<InsuranceUnitDTO>> GetInsuranceUnitAsync(string _insuranceUnitId = "")
        {
            var query = _context.Set<InsuranceUnit>().AsQueryable();

            if (!string.IsNullOrEmpty(_insuranceUnitId))
            {
                query = query.Where(u => u.InsuranceUnitId == Guid.Parse(_insuranceUnitId));
            }

            return await query.Select(u => new InsuranceUnitDTO
            {
                InsuranceUnitId = u.InsuranceUnitId,
                Name = u.Name,
                CompanyNo = u.CompanyNo,
                ContactPerson = u.ContactPerson,
                ContactPhone = u.ContactPhone,
                ContactEmail = u.ContactEmail,
                Address = u.Address,
                Website = u.Website,
                Description = u.Description,
                SortCode = u.SortCode,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                DeleteTime = u.DeleteTime,
                DeleteUserId = u.DeleteUserId
            }).ToListAsync();
        }

        /// <summary>
        /// 新增保險單位
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddInsuranceUnitAsync(InsuranceUnitDTO _data)
        {
            try
            {
                var entity = new InsuranceUnit
                {
                    InsuranceUnitId = _data.InsuranceUnitId,
                    Name = _data.Name,
                    CompanyNo = _data.CompanyNo,
                    ContactPerson = _data.ContactPerson,
                    ContactPhone = _data.ContactPhone,
                    ContactEmail = _data.ContactEmail,
                    Address = _data.Address,
                    Website = _data.Website,
                    Description = _data.Description,
                    SortCode = _data.SortCode,
                    CreateTime = _data.CreateTime,
                    CreateUserId = _data.CreateUserId,
                    UpdateTime = _data.UpdateTime,
                    UpdateUserId = _data.UpdateUserId,
                    DeleteTime = _data.DeleteTime,
                    DeleteUserId = _data.DeleteUserId
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯保險單位
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditInsuranceUnitAsync(InsuranceUnitDTO _data)
        {
            try
            {
                var entity = await _context.Set<InsuranceUnit>().FindAsync(_data.InsuranceUnitId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.Name = _data.Name;
                entity.CompanyNo = _data.CompanyNo;
                entity.ContactPerson = _data.ContactPerson;
                entity.ContactPhone = _data.ContactPhone;
                entity.ContactEmail = _data.ContactEmail;
                entity.Address = _data.Address;
                entity.Website = _data.Website;
                entity.Description = _data.Description;
                entity.SortCode = _data.SortCode;
                entity.CreateTime = _data.CreateTime;
                entity.CreateUserId = _data.CreateUserId;
                entity.UpdateTime = _data.UpdateTime;
                entity.UpdateUserId = _data.UpdateUserId;
                entity.DeleteTime = _data.DeleteTime;
                entity.DeleteUserId = _data.DeleteUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除保險單位
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteInsuranceUnitAsync(InsuranceUnitDTO _data)
        {
            try
            {
                var entity = await _context.Set<InsuranceUnit>().FindAsync(_data.InsuranceUnitId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得保險單位詳細資料
        /// </summary>
        /// <param name="_insuranceUnitId"></param>
        /// <returns></returns>
        public async Task<InsuranceUnitDTO> GetInsuranceUnitDetailAsync(string _insuranceUnitId)
        {
            var entity = await _context.Set<InsuranceUnit>().FindAsync(_insuranceUnitId);
            if (entity == null)
            {
                return null;
            }

            return new InsuranceUnitDTO
            {
                InsuranceUnitId = entity.InsuranceUnitId,
                Name = entity.Name,
                CompanyNo = entity.CompanyNo,
                ContactPerson = entity.ContactPerson,
                ContactPhone = entity.ContactPhone,
                ContactEmail = entity.ContactEmail,
                Address = entity.Address,
                Website = entity.Website,
                Description = entity.Description,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }
    }
}

