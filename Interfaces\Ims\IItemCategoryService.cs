using FAST_ERP_Backend.Models.Ims;
using System;
using System.Collections.Generic;
using System.Data;

namespace FAST_ERP_Backend.Interfaces.Ims;
/// <summary> 庫存品分類服務介面 </summary>
public interface IItemCategoryService
{
    /// <summary> 取得所有分類 </summary>
    Task<List<ItemCategoryDTO>> GetAllAsync();

    /// <summary> 取得單一分類 </summary>
    Task<ItemCategoryDTO> GetAsync(Guid ItemCategoryID);

    /// <summary> 新增分類 </summary>
    Task<(bool, string)> AddAsync(ItemCategoryDTO DTO);

    /// <summary> 更新分類 </summary>
    Task<(bool, string)> UpdateAsync(ItemCategoryDTO DTO);

    /// <summary> 刪除分類 </summary>
    Task<(bool, string)> DeleteAsync(ItemCategoryDTO DTO);
}