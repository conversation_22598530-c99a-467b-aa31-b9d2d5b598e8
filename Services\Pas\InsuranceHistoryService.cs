using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class InsuranceHistoryService : IInsuranceHistoryService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public InsuranceHistoryService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<(bool, string)> AddAsync(InsuranceHistoryDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newRecord = new InsuranceHistory
                {
                    uid = Guid.NewGuid().ToString(),
                    UserId = dto.UserId,
                    InsuranceType = dto.InsuranceType,
                    InsuranceGradeUid = dto.InsuranceGradeUid,
                    StartDate = _baseform.DateStrToTimestamp(dto.StartDate ?? "") ?? 0,
                    EndDate = !string.IsNullOrEmpty(dto.EndDate) ? _baseform.DateStrToTimestamp(dto.EndDate) : null,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId,
                    IsDeleted = false
                };

                await _context.Pas_InsuranceHistory.AddAsync(newRecord);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增保險級距歷程成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增保險級距歷程失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditAsync(InsuranceHistoryDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingRecord = await _context.Pas_InsuranceHistory
                    .FirstOrDefaultAsync(x => x.uid == dto.uid && x.IsDeleted != true);

                if (existingRecord == null)
                {
                    return (false, "找不到對應的保險級距歷程資料");
                }

                existingRecord.InsuranceType = dto.InsuranceType;
                existingRecord.InsuranceGradeUid = dto.InsuranceGradeUid;
                existingRecord.StartDate = _baseform.DateStrToTimestamp(dto.StartDate ?? "") ?? 0;
                existingRecord.EndDate = !string.IsNullOrEmpty(dto.EndDate) ? _baseform.DateStrToTimestamp(dto.EndDate) : null;
                existingRecord.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existingRecord.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯保險級距歷程成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯保險級距歷程失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingRecord = await _context.Pas_InsuranceHistory
                    .FirstOrDefaultAsync(x => x.uid == uid && x.IsDeleted != true);

                if (existingRecord == null)
                {
                    return (false, "資料已刪除或不存在");
                }

                existingRecord.IsDeleted = true;
                existingRecord.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existingRecord.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除保險級距歷程成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除保險級距歷程失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<InsuranceHistoryDTO?> GetByUidAsync(string uid)
        {
            try
            {
                return await _context.Pas_InsuranceHistory
                    .Where(x => x.uid == uid && x.IsDeleted != true)
                    .Select(x => new InsuranceHistoryDTO
                    {
                        uid = x.uid,
                        UserId = x.UserId,
                        InsuranceType = x.InsuranceType,
                        InsuranceGradeUid = x.InsuranceGradeUid,
                        StartDate = _baseform.TimestampToDateStr(x.StartDate),
                        EndDate = x.EndDate.HasValue ? _baseform.TimestampToDateStr(x.EndDate.Value) : null
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"取得保險級距歷程資料錯誤: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<InsuranceHistoryDTO?> GetEffectiveGradeAsync(string userId, int insuranceType, string targetDate)
        {
            try
            {
                var targetDateTimestamp = _baseform.DateStrToTimestamp(targetDate);

                return await _context.Pas_InsuranceHistory
                    .Where(x => x.UserId == userId
                               && x.InsuranceType == insuranceType
                               && x.IsDeleted != true
                               && x.StartDate <= targetDateTimestamp
                               && (x.EndDate == null || x.EndDate >= targetDateTimestamp))
                    .OrderByDescending(x => x.StartDate)
                    .Select(x => new InsuranceHistoryDTO
                    {
                        uid = x.uid,
                        UserId = x.UserId,
                        InsuranceType = x.InsuranceType,
                        InsuranceGradeUid = x.InsuranceGradeUid,
                        StartDate = _baseform.TimestampToDateStr(x.StartDate),
                        EndDate = x.EndDate.HasValue ? _baseform.TimestampToDateStr(x.EndDate.Value) : null
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"取得有效保險級距資料錯誤: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<Dictionary<int, InsuranceHistoryDTO?>> GetAllEffectiveGradesAsync(string userId, string targetDate)
        {
            try
            {
                // 保險類型常數（1: 勞保, 2: 健保, 3: 職災）
                var insuranceTypes = new[] { 1, 2, 3 };
                var result = new Dictionary<int, InsuranceHistoryDTO?>();

                // 將目標日期轉換為時間戳
                var targetDateTimestamp = _baseform.DateStrToTimestamp(targetDate);

                // 一次查詢獲取所有保險類型的有效級距
                var effectiveGrades = await _context.Pas_InsuranceHistory
                    .Where(x => x.UserId == userId
                               && insuranceTypes.Contains(x.InsuranceType)
                               && x.IsDeleted != true
                               && x.StartDate <= targetDateTimestamp
                               && (x.EndDate == null || x.EndDate >= targetDateTimestamp))
                    .OrderByDescending(x => x.StartDate)
                    .Select(x => new { x.InsuranceType, x.uid, x.UserId, x.InsuranceGradeUid, x.StartDate, x.EndDate })
                    .ToListAsync();

                // 初始化所有保險類型為 null
                foreach (var type in insuranceTypes)
                {
                    result[type] = null;
                }

                // 按保險類型分組並取每組最新的記錄
                var latestGrades = effectiveGrades
                    .GroupBy(x => x.InsuranceType)
                    .Select(g => g.First())
                    .ToList();

                // 填入查詢到的有效級距
                foreach (var grade in latestGrades)
                {
                    result[grade.InsuranceType] = new InsuranceHistoryDTO
                    {
                        uid = grade.uid,
                        UserId = grade.UserId,
                        InsuranceType = grade.InsuranceType,
                        InsuranceGradeUid = grade.InsuranceGradeUid,
                        StartDate = _baseform.TimestampToDateStr(grade.StartDate),
                        EndDate = grade.EndDate.HasValue ? _baseform.TimestampToDateStr(grade.EndDate.Value) : null
                    };
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"取得所有有效保險級距資料錯誤: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<List<InsuranceHistoryDTO>> GetByUserAndTypeAsync(string userId, int insuranceType)
        {
            try
            {
                return await _context.Pas_InsuranceHistory
                    .Where(x => x.UserId == userId
                               && x.InsuranceType == insuranceType
                               && x.IsDeleted != true)
                    .OrderByDescending(x => x.StartDate)
                    .Select(x => new InsuranceHistoryDTO
                    {
                        uid = x.uid,
                        UserId = x.UserId,
                        InsuranceType = x.InsuranceType,
                        InsuranceGradeUid = x.InsuranceGradeUid,
                        StartDate = _baseform.TimestampToDateStr(x.StartDate),
                        EndDate = x.EndDate.HasValue ? _baseform.TimestampToDateStr(x.EndDate.Value) : null
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"取得保險級距歷程列表錯誤: {ex.InnerException?.Message ?? ex.Message}");
            }
        }
    }
}
