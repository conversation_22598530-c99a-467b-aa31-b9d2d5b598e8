using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("角色權限管理")]
    public class RolesPermissionsController : ControllerBase
    {
        private readonly IRolesPermissionsService _rolesPermissionsService;

        public RolesPermissionsController(IRolesPermissionsService rolesPermissionsService)
        {
            _rolesPermissionsService = rolesPermissionsService;
        }

        private ClaimsPrincipal LoginUser => HttpContext.User;

        /// <summary>
        /// 取得所有角色權限
        /// </summary>
        [HttpGet]
        [Route("GetRolesPermissions")]
        [SwaggerOperation(Summary = "取得角色權限列表", Description = "取得所有角色權限資料")]
        public async Task<IActionResult> GetRolesPermissionsList()
        {
            var rolesPermissions = await _rolesPermissionsService.GetRolesPermissionsAsync();
            return Ok(rolesPermissions);
        }
        
        /// <summary>
        /// 取得特定角色權限資料
        /// </summary>
        [HttpGet]
        [Route("GetRolesPermissions/{roleId?}")]
        [SwaggerOperation(Summary = "取得角色權限明細", Description = "依角色ID取得特定角色權限資料")]
        public async Task<IActionResult> GetRolesPermissionsByRoleId(string roleId)
        {
            var rolesPermissions = await _rolesPermissionsService.GetRolesPermissionsAsync(roleId);
            return Ok(rolesPermissions);
        }
    }
}
