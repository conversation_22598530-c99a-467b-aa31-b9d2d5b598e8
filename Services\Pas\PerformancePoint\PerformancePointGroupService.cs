using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class PerformancePointGroupService : IPerformancePointGroupService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public PerformancePointGroupService(ERPDbContext context, Baseform baseform, ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<PerformancePointGroupDTO>> GetAllAsync()
        {
            return await _context.Pas_PerformancePointGroup
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.CreateTime)
                .Select(x => new PerformancePointGroupDTO
                {
                    uid = x.uid,
                    groupName = x.groupName,
                    weightRatio = x.weightRatio.ToString("0.##"),
                    pointTypeCount = _context.Pas_PerformancePointType.Count(p => p.groupUid == x.uid && !p.IsDeleted),
                })
                .ToListAsync();
        }

        public async Task<PerformancePointGroupDTO?> GetDetailAsync(string uid)
        {
            var entity = await _context.Pas_PerformancePointGroup
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.uid == uid && !x.IsDeleted);

            if (entity == null) return null;

            return new PerformancePointGroupDTO
            {
                uid = entity.uid,
                groupName = entity.groupName,
                weightRatio = entity.weightRatio.ToString("0.##"),
                pointTypeCount = _context.Pas_PerformancePointType.Count(p => p.groupUid == entity.uid && !p.IsDeleted),
            };
        }

        public async Task<(bool, string)> AddAsync(PerformancePointGroupDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = new PerformancePointGroup
                {
                    uid = Guid.NewGuid().ToString(),
                    groupName = _data.groupName,
                    weightRatio = decimal.Parse(_data.weightRatio),
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                _context.Pas_PerformancePointGroup.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增點數群組成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增點數群組失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditAsync(PerformancePointGroupDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_PerformancePointGroup.FirstOrDefaultAsync(x => x.uid == _data.uid);
                if (entity == null) return (false, "找不到對應點數群組資料");

                entity.groupName = _data.groupName;
                entity.weightRatio = decimal.Parse(_data.weightRatio);
                entity.UpdateUserId = _currentUserService.UserId;
                entity.UpdateTime = _baseform.GetCurrentLocalTimestamp();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增點數群組成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除點數群組失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_PerformancePointGroup.FirstOrDefaultAsync(x => x.uid == uid);
                if (entity == null) return (false, "找不到對應點數群組資料");

                entity.IsDeleted = true;
                entity.DeleteUserId = _currentUserService.UserId;
                entity.DeleteTime = _baseform.GetCurrentLocalTimestamp();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除點數群組完成");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除點數群組失敗: {ex.InnerException?.Message ?? ex.Message}");
            }

        }
    }


}

