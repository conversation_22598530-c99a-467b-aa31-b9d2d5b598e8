using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 聯絡人角色 </summary>
public class ContactRole
{
    /// <summary> 角色編號 </summary>
    [Key]
    [Comment("角色編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ContactRoleID { get; set; }

    /// <summary> 角色名稱 </summary>
    [Required]
    [MaxLength(50)]
    [Comment("角色名稱")]
    [Column(TypeName = "nvarchar(50)")]
    public string Name { get; set; }

    /// <summary> 導覽屬性 - 關聯的商業夥伴聯絡人映射 </summary>
    public ICollection<PartnerContact> PartnerContacts { get; set; }

    /// <summary> 建構式 </summary>
    public ContactRole()
    {
        ContactRoleID = Guid.NewGuid();
        Name = string.Empty;
        PartnerContacts = new List<PartnerContact>();
    }
}

/// <summary> 聯絡人角色 DTO </summary>
public class ContactRoleDTO
{
    /// <summary> 角色編號 </summary>
    public Guid ContactRoleID { get; set; }

    /// <summary> 角色名稱 </summary>
    public string Name { get; set; }

    /// <summary> 建構式 </summary>
    public ContactRoleDTO()
    {
        ContactRoleID = Guid.NewGuid();
        Name = string.Empty;
    }
}
