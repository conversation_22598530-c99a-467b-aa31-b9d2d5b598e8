"use client";

import { useEffect, useState } from "react";
import { Modal, Button, Typography, Progress, Space, Flex } from "antd";

interface IdleTimeoutDialogProps {
  open: boolean;
  onLogout: () => void;
  onContinue: () => void;
  countdownDuration: number; // 倒計時時間（秒）
}

const IdleTimeoutDialog = ({
  open,
  onLogout,
  onContinue,
  countdownDuration,
}: IdleTimeoutDialogProps) => {
  const [remainingTime, setRemainingTime] = useState(countdownDuration);

  // 當對話框打開時，重置倒計時
  useEffect(() => {
    if (open) {
      setRemainingTime(countdownDuration);
    }
  }, [open, countdownDuration]);

  // 倒計時邏輯
  useEffect(() => {
    if (!open) {
      return;
    }

    if (remainingTime <= 0) {
      // 時間到自動登出
      onLogout();
      return;
    }

    // 計時器
    const timer = setInterval(() => {
      setRemainingTime((prevTime) => prevTime - 1);
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [open, remainingTime, onLogout]);

  // 計算進度
  const progress = (remainingTime / countdownDuration) * 100;

  // 繼續使用按鈕處理函數
  const handleContinue = () => {
    // 先重置倒計時（雖然對話框關閉後會重置，但為了安全起見）
    setRemainingTime(countdownDuration);
    // 調用父組件的繼續函數
    onContinue();
  };

  return (
    <Modal
      title={<Typography.Title level={4}>系統閒置提醒</Typography.Title>}
      open={open}
      closable={false}
      maskClosable={false}
      keyboard={false}
      footer={[
        <Button key="logout" danger onClick={onLogout}>
          登出系統
        </Button>,
        <Button
          key="continue"
          type="primary"
          onClick={handleContinue}
          autoFocus
        >
          繼續使用
        </Button>,
      ]}
    >
      <Flex vertical align="center" style={{ padding: "16px 0" }}>
        <Typography.Paragraph>
          由於您已閒置一段時間，系統將在以下時間後自動登出：
        </Typography.Paragraph>

        <Space direction="vertical" align="center" style={{ margin: "16px 0" }}>
          <Progress
            type="circle"
            percent={progress}
            format={() => remainingTime}
            status="exception"
            size={80}
          />
        </Space>

        <Typography.Text type="secondary">
          請選擇是否繼續使用系統
        </Typography.Text>
      </Flex>
    </Modal>
  );
};

export default IdleTimeoutDialog;
