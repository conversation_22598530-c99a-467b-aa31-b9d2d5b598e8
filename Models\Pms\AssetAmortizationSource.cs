using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// 財產-攤提來源 關聯表
/// </summary>
[Table("Pms_AssetAmortizationSourceMapping", Schema = "dbo")]
public class AssetAmortizationSourceMapping : ModelBaseEntity
{
    [Key]
    public Guid Id { get; set; }

    [Comment("財產流水號")]
    public Guid AssetId { get; set; }

    [Comment("攤提來源編號")]
    public Guid AmortizationSourceId { get; set; }

    [ForeignKey("AssetId")]
    public virtual Asset Asset { get; set; }

    [ForeignKey("AmortizationSourceId")]
    public virtual AmortizationSource AmortizationSource { get; set; }
}