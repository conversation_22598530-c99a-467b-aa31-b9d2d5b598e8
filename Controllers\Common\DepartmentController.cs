using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("部門資料管理")]
    public class DepartmentController : ControllerBase
    {
        private readonly IDepartmentService _Interface;

        // 在建構子中注入多個依賴
        public DepartmentController(IDepartmentService common_Department_Interface)
        {
            _Interface = common_Department_Interface;
        }

        [HttpGet]
        [Route("GetDepartment")]
        [SwaggerOperation(Summary = "取得部門列表", Description = "取得所有部門列表")]
        public async Task<IActionResult> GetDepartmentList()
        {
            var users = await _Interface.GetDepartmentAsync();
            return Ok(users);
        }

        [HttpGet]
        [Route("GetDepartment/{_departmentId?}")]
        [SwaggerOperation(Summary = "取得部門明細", Description = "依ID取得部門明細")]
        public async Task<IActionResult> GetDepartmentDetail(string _departmentId)
        {
            var users = await _Interface.GetDepartmentAsync(_departmentId);
            return Ok(users);
        }

        [HttpPost]
        [Route("AddDepartment")]
        [SwaggerOperation(Summary = "新增部門", Description = "新增部門資料")]
        public async Task<IActionResult> AddDepartment([FromBody] DepartmentDTO _data)
        {
            var (result, msg) = await _Interface.AddDepartmentAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditDepartment")]
        [SwaggerOperation(Summary = "編輯部門", Description = "修改已存在之部門資料")]
        public async Task<IActionResult> EditDepartment([FromBody] DepartmentDTO _data)
        {
            var (result, msg) = await _Interface.EditDepartmentAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteDepartment")]
        [SwaggerOperation(Summary = "刪除部門", Description = "刪除已存在之部門資料")]
        public async Task<IActionResult> DeleteDepartment([FromBody] DepartmentDTO _data)
        {
            var (result, msg) = await _Interface.DeleteDepartmentAsync(_data);
            return Ok(new { result, msg });
        }

    }

}
