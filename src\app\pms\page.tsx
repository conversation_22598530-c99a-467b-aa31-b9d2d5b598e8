"use client";

import { useState } from "react";
import { Card, Row, Col, Typo<PERSON>, Badge, Button, Flex, Space } from "antd";
import {
  FileTextOutlined,
  SettingOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  SafetyCertificateOutlined,
  ArrowLeftOutlined,
  ExportOutlined,
  HomeOutlined,
  ToolOutlined,
  ShopOutlined,
  DollarOutlined,
  ReconciliationOutlined,
  ContainerOutlined,
  PrinterOutlined,
  AuditOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";

const { Title, Text } = Typography;

interface SettingCard {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  path?: string;
  component?: React.ReactNode;
  status?: "success" | "processing" | "default" | "error" | "warning";
}

const PMSOverviewPage = () => {
  const router = useRouter();
  const [selectedSetting, setSelectedSetting] = useState<SettingCard | null>(
    null
  );
  const [isExpanded, setIsExpanded] = useState(false);

  const settingCards: SettingCard[] = [
    // 單據維護模組
    {
      key: "fixedAssetMaintenance",
      title: "固定資產維護",
      description: "新增、修改、查詢固定資產基本資料",
      icon: <DatabaseOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/document_maintenance/fixed_asset_maintenance_form",
      status: "processing",
    },
    {
      key: "vendorMaintenance",
      title: "廠商修繕申請",
      description: "廠商修繕申請、審核、派工管理",
      icon: <ToolOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/document_maintenance/vendor_maintenance_form",
      status: "processing",
    },
    {
      key: "assetCarryOut",
      title: "資產攜出申請",
      description: "資產攜出申請、審核、歸還管理",
      icon: <ExportOutlined style={{ fontSize: "24px", color: "#722ed1" }} />,
      path: "/pms/document_maintenance/asset_carryout_form",
      status: "processing",
    },
    {
      key: "assetDepreciation",
      title: "固定資產折舊",
      description: "資產折舊計算與管理",
      icon: <DollarOutlined style={{ fontSize: "24px", color: "#fa8c16" }} />,
      path: "/pms/document_maintenance/fixed_asset_depreciation_form",
      status: "default",
    },
    {
      key: "assetLocationChange",
      title: "資產位置異動",
      description: "資產存放位置變更記錄",
      icon: <HomeOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/document_maintenance/asset_location_change_form",
      status: "default",
    },
    {
      key: "assetSale",
      title: "固定資產出售",
      description: "資產出售申請與處理",
      icon: <ShopOutlined style={{ fontSize: "24px", color: "#eb2f96" }} />,
      path: "/pms/document_maintenance/fixed_asset_sale_form",
      status: "default",
    },
    {
      key: "assetScrap",
      title: "固定資產報廢",
      description: "資產報廢申請與處理",
      icon: (
        <ReconciliationOutlined
          style={{ fontSize: "24px", color: "#f5222d" }}
        />
      ),
      path: "/pms/document_maintenance/fixed_asset_scrap_form",
      status: "default",
    },

    // 參數設定模組
    {
      key: "assetCategory",
      title: "財產類別設定",
      description: "設定與管理財產分類項目",
      icon: <SettingOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/parameter_settings/asset_category",
      status: "processing",
    },
    {
      key: "assetAccount",
      title: "財產科目設定",
      description: "設定財產會計科目項目",
      icon: <DatabaseOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/parameter_settings/asset_account",
      status: "processing",
    },
    {
      key: "assetSubAccount",
      title: "財產子目設定",
      description: "設定財產會計子科目項目",
      icon: (
        <ReconciliationOutlined
          style={{ fontSize: "24px", color: "#722ed1" }}
        />
      ),
      path: "/pms/parameter_settings/asset_sub_account",
      status: "processing",
    },
    {
      key: "storageLocation",
      title: "存放地點設定",
      description: "設定資產存放位置資料",
      icon: <HomeOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/parameter_settings/storage_location",
      status: "processing",
    },
    {
      key: "manufacturer",
      title: "製造商設定",
      description: "維護製造商基本資料",
      icon: (
        <SafetyCertificateOutlined
          style={{ fontSize: "24px", color: "#eb2f96" }}
        />
      ),
      path: "/pms/parameter_settings/manufacturer",
      status: "processing",
    },
    {
      key: "assetSource",
      title: "財產來源設定",
      description: "設定財產取得來源分類",
      icon: (
        <ContainerOutlined style={{ fontSize: "24px", color: "#fa8c16" }} />
      ),
      path: "/pms/parameter_settings/asset_source",
      status: "processing",
    },
    {
      key: "accessoryEquipment",
      title: "附屬設備設定",
      description: "設定附屬設備項目管理",
      icon: <ToolOutlined style={{ fontSize: "24px", color: "#13c2c2" }} />,
      path: "/pms/parameter_settings/accessory_equipment",
      status: "processing",
    },
    {
      key: "amortizationSource",
      title: "攤提來源設定",
      description: "設定資產攤提來源項目",
      icon: <DollarOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/parameter_settings/amortization_source",
      status: "processing",
    },
    {
      key: "userRole",
      title: "保管人&使用人",
      description: "設定資產保管人與使用人權限",
      icon: <TeamOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/parameter_settings/user_role",
      status: "processing",
    },
    {
      key: "assetBatchImport",
      title: "財產批次匯入",
      description: "大量財產資料批次匯入系統",
      icon: (
        <ContainerOutlined style={{ fontSize: "24px", color: "#13c2c2" }} />
      ),
      path: "/pms/parameter_settings/asset_batch_import",
      status: "processing",
    },
    {
      key: "systemParameter",
      title: "系統參數設定",
      description: "PMS 系統基本參數與進階設定",
      icon: <SettingOutlined style={{ fontSize: "24px", color: "#f5222d" }} />,
      path: "/pms/parameter_settings/system_parameter_setting",
      status: "processing",
    },

    // 報表列印模組
    {
      key: "assetInventoryDetail",
      title: "財產盤點明細表",
      description: "財產盤點作業明細記錄報表",
      icon: <AuditOutlined style={{ fontSize: "24px", color: "#1890ff" }} />,
      path: "/pms/asset_report/asset_inventory_record",
      status: "default",
    },
    {
      key: "assetDepreciationReport",
      title: "財產折舊表",
      description: "財產折舊計算與統計報表",
      icon: <DollarOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/asset_report/asset_depreciation",
      status: "default",
    },
    {
      key: "assetRegister",
      title: "財產清冊",
      description: "完整財產清單與基本資料",
      icon: <DatabaseOutlined style={{ fontSize: "24px", color: "#722ed1" }} />,
      path: "/pms/asset_report/asset_list",
      status: "processing",
    },
    {
      key: "assetCard",
      title: "財產卡",
      description: "個別財產詳細資料卡片",
      icon: (
        <ReconciliationOutlined
          style={{ fontSize: "24px", color: "#fa8c16" }}
        />
      ),
      path: "/pms/asset_report/asset_card",
      status: "processing",
    },
    {
      key: "assetScrapReport",
      title: "財產報廢清冊",
      description: "已報廢財產統計清單",
      icon: <ExportOutlined style={{ fontSize: "24px", color: "#f5222d" }} />,
      path: "/pms/asset_report/asset_scrap",
      status: "default",
    },
    {
      key: "assetInventoryRecord",
      title: "財產盤點紀錄表",
      description: "財產盤點執行過程記錄",
      icon: <FileTextOutlined style={{ fontSize: "24px", color: "#08979c" }} />,
      path: "/pms/asset_report/asset_inventory_record",
      status: "default",
    },
    {
      key: "assetLabel",
      title: "財產標籤",
      description: "產生與列印財產條碼標籤",
      icon: <PrinterOutlined style={{ fontSize: "24px", color: "#eb2f96" }} />,
      path: "/pms/asset_report/asset_label",
      status: "processing",
    },
    {
      key: "assetInventoryList",
      title: "財產盤點清冊",
      description: "財產盤點作業清冊列表",
      icon: (
        <ContainerOutlined style={{ fontSize: "24px", color: "#13c2c2" }} />
      ),
      path: "/pms/asset_report/asset_inventory_list",
      status: "default",
    },
    {
      key: "assetIncreaseDecrease",
      title: "財產增減表",
      description: "財產增加與減少異動統計",
      icon: <BarChartOutlined style={{ fontSize: "24px", color: "#52c41a" }} />,
      path: "/pms/asset_report/asset_increase_decrease",
      status: "default",
    },
  ];

  const handleCardClick = (setting: SettingCard) => {
    if (setting.path) {
      // 如果有路徑，直接導航
      router.push(setting.path);
    } else if (setting.component) {
      // 如果有組件，展開顯示
      setSelectedSetting(setting);
      setIsExpanded(true);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleBack = () => {
    setIsExpanded(false);
    setTimeout(() => setSelectedSetting(null), 300);
  };

  const renderSettingCards = () => (
    <div style={{ overflow: "hidden", width: "100%" }}>
      {/* 參數設定模組 */}
      <div className="mb-8">
        <Title level={3} className="mb-4">
          <SettingOutlined className="mr-2" />
          參數設定
        </Title>
        <Row
          gutter={[24, 24]}
          style={{
            opacity: isExpanded ? 0 : 1,
            transform: `translateY(${isExpanded ? "20px" : "0"})`,
            transition: "all 0.3s ease-in-out",
            display: isExpanded ? "none" : "flex",
            margin: "0 -8px",
          }}
        >
          {settingCards
            .filter((card) =>
              [
                "assetCategory",
                "assetAccount",
                "assetSubAccount",
                "storageLocation",
                "manufacturer",
                "assetSource",
                "accessoryEquipment",
                "amortizationSource",
                "userRole",
                "assetBatchImport",
                "systemParameter",
              ].includes(card.key)
            )
            .map((setting) => (
              <Col
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                key={setting.key}
                style={{ padding: "0 12px" }}
              >
                <Badge.Ribbon
                  text={
                    setting.status === "processing"
                      ? "使用中"
                      : setting.status === "success"
                      ? "已設定"
                      : setting.status === "warning"
                      ? "待設定"
                      : "開發中"
                  }
                  color={
                    setting.status === "processing"
                      ? "blue"
                      : setting.status === "success"
                      ? "green"
                      : setting.status === "warning"
                      ? "orange"
                      : "gray"
                  }
                >
                  <Card
                    hoverable
                    onClick={() => handleCardClick(setting)}
                    className="h-full"
                    style={{
                      height: "100%",
                      transition: "all 0.3s",
                      width: "100%",
                      minHeight: "120px",
                    }}
                  >
                    <div className="flex items-center mb-4">
                      {setting.icon}
                      <Title level={5} className="mb-0 ml-3">
                        {setting.title}
                      </Title>
                    </div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {setting.description}
                    </Text>
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
        </Row>
      </div>

      {/* 單據維護模組 */}
      <div className="mb-8">
        <Title level={3} className="mb-4">
          <FileTextOutlined className="mr-2" />
          單據維護
        </Title>
        <Row
          gutter={[24, 24]}
          style={{
            opacity: isExpanded ? 0 : 1,
            transform: `translateY(${isExpanded ? "20px" : "0"})`,
            transition: "all 0.3s ease-in-out",
            display: isExpanded ? "none" : "flex",
            margin: "0 -8px",
          }}
        >
          {settingCards
            .filter((card) =>
              [
                "fixedAssetMaintenance",
                "vendorMaintenance",
                "assetCarryOut",
                "assetDepreciation",
                "assetLocationChange",
                "assetSale",
                "assetScrap",
              ].includes(card.key)
            )
            .map((setting) => (
              <Col
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                key={setting.key}
                style={{ padding: "0 12px" }}
              >
                <Badge.Ribbon
                  text={
                    setting.status === "processing"
                      ? "使用中"
                      : setting.status === "success"
                      ? "已設定"
                      : setting.status === "warning"
                      ? "待設定"
                      : "開發中"
                  }
                  color={
                    setting.status === "processing"
                      ? "blue"
                      : setting.status === "success"
                      ? "green"
                      : setting.status === "warning"
                      ? "orange"
                      : "gray"
                  }
                >
                  <Card
                    hoverable
                    onClick={() => handleCardClick(setting)}
                    className="h-full"
                    style={{
                      height: "100%",
                      transition: "all 0.3s",
                      width: "100%",
                      minHeight: "120px",
                    }}
                  >
                    <div className="flex items-center mb-4">
                      {setting.icon}
                      <Title level={5} className="mb-0 ml-3">
                        {setting.title}
                      </Title>
                    </div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {setting.description}
                    </Text>
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
        </Row>
      </div>

      {/* 報表列印模組 */}
      <div className="mb-8">
        <Title level={3} className="mb-4">
          <BarChartOutlined className="mr-2" />
          報表列印
        </Title>
        <Row
          gutter={[24, 24]}
          style={{
            opacity: isExpanded ? 0 : 1,
            transform: `translateY(${isExpanded ? "20px" : "0"})`,
            transition: "all 0.3s ease-in-out",
            display: isExpanded ? "none" : "flex",
            margin: "0 -8px",
          }}
        >
          {settingCards
            .filter((card) =>
              [
                "assetInventoryDetail",
                "assetDepreciationReport",
                "assetRegister",
                "assetCard",
                "assetScrapReport",
                "assetInventoryRecord",
                "assetLabel",
                "assetInventoryList",
                "assetIncreaseDecrease",
              ].includes(card.key)
            )
            .map((setting) => (
              <Col
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                key={setting.key}
                style={{ padding: "0 12px" }}
              >
                <Badge.Ribbon
                  text={
                    setting.status === "processing"
                      ? "使用中"
                      : setting.status === "success"
                      ? "已設定"
                      : setting.status === "warning"
                      ? "待設定"
                      : "開發中"
                  }
                  color={
                    setting.status === "processing"
                      ? "blue"
                      : setting.status === "success"
                      ? "green"
                      : setting.status === "warning"
                      ? "orange"
                      : "gray"
                  }
                >
                  <Card
                    hoverable
                    onClick={() => handleCardClick(setting)}
                    className="h-full"
                    style={{
                      height: "100%",
                      transition: "all 0.3s",
                      width: "100%",
                      minHeight: "120px",
                    }}
                  >
                    <div className="flex items-center mb-4">
                      {setting.icon}
                      <Title level={5} className="mb-0 ml-3">
                        {setting.title}
                      </Title>
                    </div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {setting.description}
                    </Text>
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
        </Row>
      </div>
    </div>
  );

  const renderSettingContent = () => (
    <div
      style={{
        opacity: isExpanded ? 1 : 0,
        transform: `translateY(${isExpanded ? "0" : "-20px"})`,
        transition: "all 0.3s ease-in-out",
        display: !isExpanded ? "none" : "block",
        width: "100%",
        overflow: "hidden",
      }}
    >
      <Flex vertical gap="middle">
        <Space className="py-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            style={{
              display: "flex",
              alignItems: "center",
              fontSize: "15px",
              height: "40px",
              borderRadius: "8px",
              transition: "all 0.3s",
            }}
            className="hover:bg-gray-100 hover:border-gray-300"
          >
            返回功能總覽
          </Button>
        </Space>
        <Card
          title={
            <Flex align="center" gap="middle">
              {selectedSetting?.icon}
              <span>{selectedSetting?.title}</span>
            </Flex>
          }
          style={{ width: "100%" }}
        >
          {selectedSetting?.component}
        </Card>
      </Flex>
    </div>
  );

  return (
    <div className="p-6 overflow-x-hidden" style={{ maxWidth: "100%" }}>
      <div className="mb-6">
        <Title level={2}>
          <DatabaseOutlined className="mr-3" />
          財產管理系統功能總覽
        </Title>
        <Text type="secondary">
          {isExpanded
            ? selectedSetting?.description
            : "選擇下方任一功能模組進行操作與管理"}
        </Text>
      </div>

      {renderSettingCards()}
      {renderSettingContent()}
    </div>
  );
};

export default PMSOverviewPage;
