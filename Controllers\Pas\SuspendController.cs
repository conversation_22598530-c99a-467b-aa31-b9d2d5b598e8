using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("留職停薪資料管理")]
    public class SuspendController : ControllerBase
    {
        private readonly ISuspendService _interface;

        public SuspendController(ISuspendService suspendService)
        {
            _interface = suspendService;
        }

        [HttpGet]
        [Route("GetAll/{_userId}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有留職停薪資料列表")]
        public async Task<IActionResult> GetSuspendList(string _userId)
        {
            var result = await _interface.GetSuspendListAsync(_userId);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得留職停薪明細", Description = "依uid取得留職停薪明細")]
        public async Task<IActionResult> GetSuspendDetail(string _uid)
        {
            var result = await _interface.GetSuspendDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增留職停薪資料", Description = "新增留職停薪資料")]
        public async Task<IActionResult> AddSuspend([FromBody] SuspendDTO _data)
        {
            var (result, msg) = await _interface.AddSuspendAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯留職停薪資料", Description = "編輯留職停薪資料")]
        public async Task<IActionResult> EditSuspend([FromBody] SuspendDTO _data)
        {
            var (result, msg) = await _interface.EditSuspendAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除留職停薪資料", Description = "刪除留職停薪資料")]
        public async Task<IActionResult> DeleteSuspend([FromBody] string _uid)
        {
            var (result, msg) = await _interface.DeleteSuspendAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}
