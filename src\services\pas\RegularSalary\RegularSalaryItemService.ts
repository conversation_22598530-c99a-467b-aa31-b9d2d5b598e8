// RegularSalaryItem 常態薪資項目管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "@/services/http";
import { ApiResponse } from "@/config/api";

// 常態薪資項目資料型別
export interface RegularSalaryItem {
    uid: string;
    itemName: string;
    itemType: string; // 加項或扣項
    itemTypeName: string;
    isTaxable: boolean;
    isTaxableName: string;
    description: string;
    isEnable: boolean;
    isEnableName: string;
}

// 薪資項目選項資料型別（用於級聯選單）
export interface SalaryItemTypeOption {
    label: string;
    value: string;
    children?: SalaryItemTypeOption[];
}

// 建立空的常態薪資項目資料
export const createEmptyRegularSalaryItem = (): RegularSalaryItem => ({
    uid: '',
    itemName: '',
    itemType: '',
    itemTypeName: '',
    isTaxable: false,
    isTaxableName: '',
    description: '',
    isEnable: true,
    isEnableName: '',
});

// 取得所有常態薪資項目
export async function getRegularSalaryItemList(): Promise<ApiResponse<RegularSalaryItem[]>> {
    return await httpClient(apiEndpoints.getRegularSalaryItemList, {
        method: "GET",
    });
}

// 取得單一常態薪資項目明細
export async function getRegularSalaryItemDetail(uid: string): Promise<ApiResponse<RegularSalaryItem>> {
    return await httpClient(`${apiEndpoints.getRegularSalaryItemDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增常態薪資項目
export async function addRegularSalaryItem(data: Partial<RegularSalaryItem>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addRegularSalaryItem, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增常態薪資項目失敗",
        };
    }
}

// 編輯常態薪資項目
export async function editRegularSalaryItem(data: Partial<RegularSalaryItem>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editRegularSalaryItem, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯常態薪資項目失敗",
        };
    }
}

// 刪除常態薪資項目
export async function deleteRegularSalaryItem(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteRegularSalaryItem, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除常態薪資項目失敗",
        };
    }
}

// 取得薪資項目級聯選項（依加項/扣項）
export async function getSalaryItemTypeOptions(): Promise<ApiResponse<SalaryItemTypeOption[]>> {
    return await httpClient(apiEndpoints.getSalaryItemTypeOptions, {
        method: "GET",
    });
}
