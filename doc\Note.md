# Next.JS 專案建置說明

## 初始化專案

```bash
# 建立 Next.js 專案
npx create-next-app@latest fast_erp_frontend

# 安裝 Yarn
npm install yarn

# 安裝專案依賴
yarn install
```

## 安裝必要套件

```bash
# 安裝 Next.js 核心套件
yarn add next react react-dom

# 安裝 Ant Design 相關套件
yarn add antd
yarn add @ant-design/nextjs-registry
yarn add @ant-design/icons

# 安裝 SignalR
yarn add signalr
```

## 啟動開發環境

```bash
# 使用 HTTPS 啟動開發伺服器
yarn dev:https
```

## 啟動 Docker 容器，將本地的專案目錄掛載到容器中

```bash
# 建立 Docker 映像檔
docker build -t fast_erp_frontend:dev -f Dockerfile.dev . 

# 運行 Docker 容器
docker run -p 3000:3000 -v 專案路徑:/app -v /app/node_modules --name fast_erp_frontend_dev fast_erp_frontend:dev
```
