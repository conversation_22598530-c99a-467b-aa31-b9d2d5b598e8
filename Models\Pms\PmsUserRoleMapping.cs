using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Models.Pms
{
    public class PmsUserRoleMapping : ModelBaseEntity
    {
        [Key]
        [Comment("對應編號")]
        [Column(TypeName = "nvarchar(100)")]
        public Guid PmsUserRoleMappingId { get; set; }

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; }

        [Comment("財產系統使用者身分編號")]
        public Guid PmsUserRoleId { get; set; }

        // 導航屬性
        [ForeignKey("UserId")]
        public Users User { get; set; }

        // 導航屬性
        [ForeignKey("PmsUserRoleId")]
        public PmsUserRole PmsUserRole { get; set; }

        public PmsUserRoleMapping()
        {
            PmsUserRoleMappingId = Guid.Empty;
            UserId = "";
            PmsUserRoleId = Guid.Empty;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class PmsUserRoleMappingDTO
    {
        public Guid PmsUserRoleMappingId { get; set; }  // 對應編號
        public string UserId { get; set; }                // 使用者編號
        public Guid PmsUserRoleId { get; set; }         // 財產系統使用者身分編號
        public long? CreateTime { get; set; }            // 新增時間
        public string? CreateUserId { get; set; }        // 新增者編號
        public long? UpdateTime { get; set; }            // 更新時間
        public string? UpdateUserId { get; set; }        // 更新者編號
        public long? DeleteTime { get; set; }            // 刪除時間
        public string? DeleteUserId { get; set; }        // 刪除者編號
        public bool IsDeleted { get; set; }              // 刪除狀態
        public PmsUserRoleMappingDTO()
        {
            PmsUserRoleMappingId = Guid.Empty;
            UserId = "";
            PmsUserRoleId = Guid.Empty;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}