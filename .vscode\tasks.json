{"version": "2.0.0", "tasks": [{"label": "Build & Run Docker", "type": "shell", "command": "docker build -t myapp-debug -f Dockerfile.dev . && docker run --name myapp-container -p 8080:80 -v ${workspaceFolder}:/app myapp-debug", "group": {"kind": "build", "isDefault": true}}, {"label": "Stop Docker Container", "type": "shell", "command": "docker stop myapp-container && docker rm myapp-container", "problemMatcher": []}]}