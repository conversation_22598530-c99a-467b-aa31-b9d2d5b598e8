'use client';

import { Tabs } from 'antd';
import PerformancePointSummary from './PerformancePointSummary';
import PerformancePointSettings from './PerformancePointSetting';

// 績效點數管理主顯示頁面
const PerformancePointDashboard = () => {
    const items = [
        {
            key: 'summary',
            label: '點數總表',
            children: <PerformancePointSummary />,
        },
        {
            key: 'settings',
            label: '點數資料設定',
            children: <PerformancePointSettings />,
        },
    ];

    return (
        <div>
            <h2>績效點數管理</h2>
            <Tabs defaultActiveKey="summary" items={items} />
        </div>
    );
};

export default PerformancePointDashboard;
