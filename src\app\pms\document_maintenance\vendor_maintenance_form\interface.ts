import { FormInstance } from "antd";
import { DEFAULT_VALUES } from './config';

// 廠商修繕單介面（根據 API 規格更新）
export interface VendorMaintenance {
    maintenanceNumber: string;
    assetId: string;
    applicantId: string;
    departmentId: string;
    applicationDate: number;
    faultDescription: string;
    maintenanceType: string;
    urgencyLevel: string;
    estimatedCost: number;
    vendorId?: string;
    vendorName: string;
    vendorContact: string;
    vendorPhone: string;
    scheduledStartDate: number;
    scheduledEndDate: number;
    actualStartDate: number;
    actualEndDate: number;
    actualCost: number;
    maintenanceResult: string;
    inspectorId: string;
    inspectionDate: number;
    inspectionResult: string;
    inspectionNotes: string;
    status: string;
    notes: string;
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    assetNo?: string;
    assetName?: string;
    applicantName?: string;
    applicantDepartment?: string;
    inspectorName?: string;
}

// 廠商修繕單查詢參數
export interface VendorMaintenanceQuery {
    status?: string;
    maintenanceType?: string;
    urgencyLevel?: string;
    startDate?: number;
    endDate?: number;
    keyword?: string;
    applicantId?: string;
    vendorId?: string;
}

// 審核請求
export interface ApprovalRequest {
    isApproved: boolean;
    reason: string;
    approverId: string;
    approverName: string;
}

// 指派廠商請求
export interface AssignVendorRequest {
    vendorId: string;
    vendorName: string;
    vendorContact: string;
    vendorPhone: string;
    scheduledStartDate: number;
    scheduledEndDate: number;
    operatorId: string;
    operatorName: string;
}

// 開始施工請求
export interface StartWorkRequest {
    actualStartDate: number;
    operatorId: string;
    operatorName: string;
}

// 完成修繕請求
export interface CompleteMaintenanceRequest {
    actualEndDate: number;
    actualCost: number;
    maintenanceResult: string;
    operatorId: string;
    operatorName: string;
}

// 驗收請求
export interface InspectionRequest {
    inspectionResult: string;
    inspectionNotes: string;
    inspectorId: string;
    inspectorName: string;
}

// 結案請求
export interface CloseRequest {
    operatorId: string;
    operatorName: string;
}

// 取消修繕請求
export interface CancelRequest {
    reason: string;
    operatorId: string;
    operatorName: string;
}

// 批次處理請求
export interface BatchRequest {
    maintenanceNumbers: string[];
    action: string;
    reason: string;
    operatorId: string;
    operatorName: string;
}

// 統計資料
export interface MaintenanceStatistics {
    totalCount: number;
    pendingCount: number;
    approvedCount: number;
    assignedCount: number;
    inProgressCount: number;
    completedCount: number;
    inspectedCount: number;
    closedCount: number;
    totalEstimatedCost: number;
    totalActualCost: number;
    averageCompletionDays: number;
    typeStatistics: TypeStatistic[];
    urgencyStatistics: UrgencyStatistic[];
}

export interface TypeStatistic {
    type: string;
    count: number;
    totalCost: number;
}

export interface UrgencyStatistic {
    level: string;
    count: number;
    averageCompletionDays: number;
}

// 從配置檔案導入選項和映射
export {
    MAINTENANCE_TYPES,
    URGENCY_LEVELS,
    STATUS_OPTIONS,
    STATUS_COLORS,
    STATUS_MAPPING,
    MAINTENANCE_TYPE_MAPPING,
    URGENCY_MAPPING,
    URGENCY_COLORS,
    DEFAULT_VALUES
} from './config';

// 表單初始值
export const formInitialValues = {
    maintenanceNumber: "",
    assetId: "",
    applicantId: "",
    departmentId: "",
    applicationDate: Date.now(),
    faultDescription: "",
    maintenanceType: DEFAULT_VALUES.maintenanceType,
    urgencyLevel: DEFAULT_VALUES.urgencyLevel,
    estimatedCost: 0,
    vendorId: "",
    vendorName: "",
    vendorContact: "",
    vendorPhone: "",
    scheduledStartDate: 0,
    scheduledEndDate: 0,
    actualStartDate: 0,
    actualEndDate: 0,
    actualCost: 0,
    maintenanceResult: "",
    inspectorId: "",
    inspectionDate: 0,
    inspectionResult: "",
    inspectionNotes: "",
    status: DEFAULT_VALUES.status,
    notes: "",
    createTime: 0,
    createUserId: "",
    updateTime: 0,
    updateUserId: ""
};

// 表單Props
export interface FormProps {
    editingMaintenance: VendorMaintenance | null;
    isViewMode: boolean;
    onCancel: () => void;
    onSuccess: (data: VendorMaintenance) => void;
    isMobile?: boolean;
    form: FormInstance;
} 