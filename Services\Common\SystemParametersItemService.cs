﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class SystemParametersItemService : ISystemParametersItemService
    {
        private readonly ERPDbContext _context;

        public SystemParametersItemService(ERPDbContext context)
        {
            _context = context;
        }

        public Task<(bool, string)> AddSystemParametersItemAsync(SystemParametersItemDTO SystemParameters, string tokenUid = "")
        {
            try
            {
                var newSystemParametersItem = new SystemParametersItem
                {
                    SystemParametersItemId = Guid.NewGuid().ToString(),
                    SystemParametersId = SystemParameters.SystemParametersId,
                    Name = SystemParameters.Name,
                    Value = SystemParameters.Value,
                    SortOrder = SystemParameters.SortOrder,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid,
                };

                _context.Common_SystemParametersItem.Add(newSystemParametersItem);
                _context.SaveChanges();

                return Task.FromResult((true, "新增系統參數項目成功"));
            }
            catch (Exception ex)
            {
                return Task.FromResult((false, $"新增系統參數項目失敗: {ex.Message}"));
            }
        }

        public async Task<(bool, string)> DeleteSystemParametersItemAsync(SystemParametersItemDTO SystemParameters, string tokenUid = "")
        {
            try
            {
                var existingSystemParametersItem = _context.Common_SystemParametersItem.Find(SystemParameters.SystemParametersItemId);
                if (existingSystemParametersItem != null)
                {
                    existingSystemParametersItem.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingSystemParametersItem.DeleteUserId = SystemParameters.DeleteUserId;
                    existingSystemParametersItem.IsDeleted = true;


                    await _context.SaveChangesAsync();
                    return (true, "刪除系統選單成功");
                }
                else
                {
                    return (false, "系統選單不存在或已被刪除");
                }
            }
            catch (Exception ex)
            {
                return (false, $"刪除系統參數項目失敗: {ex.Message}");
            }
        }

        public async Task<(bool, string)> EditSystemParametersItemAsync(SystemParametersItemDTO SystemParameters, string tokenUid = "")
        {
            try
            {
                var existingSystemParametersItem = await _context.Common_SystemParametersItem
                    .FirstOrDefaultAsync(e => e.SystemParametersItemId == SystemParameters.SystemParametersItemId);

                if (existingSystemParametersItem != null)
                {
                    existingSystemParametersItem.Name = SystemParameters.Name;
                    existingSystemParametersItem.Value = SystemParameters.Value;
                    existingSystemParametersItem.SortOrder = SystemParameters.SortOrder;
                    existingSystemParametersItem.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingSystemParametersItem.UpdateUserId = tokenUid;

                    await _context.SaveChangesAsync();
                    return (true, "編輯系統參數項目成功");
                }
                else
                {
                    return (false, "系統參數項目不存在或已被刪除");
                }
            }
            catch (Exception ex)
            {
                return (false, $"編輯系統參數項目失敗: {ex.Message}");
            }
        }

        public async Task<List<SystemParametersItemDTO>> GetSystemParametersItemAsync(string SystemParametersId = "")
        {
            var query = _context.Common_SystemParametersItem.AsQueryable();
            if (!string.IsNullOrEmpty(SystemParametersId))
            {
                query = query.Where(e => e.SystemParametersId == SystemParametersId);
            }

            return await query.OrderBy(e => e.SortOrder).Select(e => new SystemParametersItemDTO
            {
                SystemParametersItemId = e.SystemParametersItemId,
                SystemParametersId = e.SystemParametersId,
                Name = e.Name,
                Value = e.Value,
                SortOrder = e.SortOrder
            }).ToListAsync();
        }

        public async Task<(bool, string)> RestoreSystemParametersItemAsync(SystemParametersItemDTO SystemParameters, string tokenUid = "")
        {
            try
            {
                var existingSystemParametersItem = await _context.Common_SystemParametersItem
                    .IgnoreQueryFilters() //忽略全域篩選條件
                    .FirstOrDefaultAsync(
                        e => e.SystemParametersItemId == SystemParameters.SystemParametersItemId
                        && e.DeleteTime != null
                    );

                if (existingSystemParametersItem != null)
                {
                    existingSystemParametersItem.DeleteTime = null;
                    existingSystemParametersItem.DeleteUserId = null;

                    await _context.SaveChangesAsync();
                    return (true, "還原系統參數項目成功");
                }
                else
                {
                    return (false, "系統參數項目不存在或已被刪除");
                }
            }
            catch (Exception ex)
            {
                return (false, $"還原系統參數項目失敗: {ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteBySystemParameterIdAsync(string SystemParametersId = "", string tokenUid = "", long DeleteTime = 0)
        {
            try
            {
                var items = await _context.Common_SystemParametersItem
                    .Where(e => e.SystemParametersId == SystemParametersId)
                    .ToListAsync();

                foreach (var item in items)
                {
                    item.DeleteTime = DeleteTime;
                    item.DeleteUserId = tokenUid;
                }

                return (true, "刪除所有項目成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除所有項目失敗: {ex.Message}");
            }
        }

        public async Task<(bool, string)> RestoreBySystemParameterIdAsync(string SystemParametersId = "", string tokenUid = "", long DeleteTime = 0)
        {
            try
            {
                var items = await _context.Common_SystemParametersItem
                    .IgnoreQueryFilters() //忽略全域篩選條件
                    .Where(e => e.SystemParametersId == SystemParametersId && e.DeleteTime == DeleteTime)
                    .ToListAsync();

                foreach (var item in items)
                {
                    item.DeleteTime = null;
                    item.DeleteUserId = null;
                }

                return (true, "還原所有項目成功");
            }
            catch (Exception ex)
            {
                return (false, $"還原所有項目失敗: {ex.Message}");
            }
        }
    }
}
