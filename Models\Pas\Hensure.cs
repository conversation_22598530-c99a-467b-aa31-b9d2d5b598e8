﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Drawing;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 眷屬依附健保資料表
    /// </summary>
    public class Hensure : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("依附使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 依附的主保使用者userid

        [Comment("眷屬身分證字號")]
        [Column(TypeName = "nvarchar(100)")]
        public string dependentRocId { get; set; } // 眷屬身分證字號

        [Comment("眷屬姓名")]
        [Column(TypeName = "nvarchar(100)")]
        public string dependentName { get; set; } // 眷屬姓名

        [Comment("依附關係類型")]
        [Column(TypeName = "nvarchar(3)")]
        public string dependentRelationType { get; set; } // 眷屬依附關係類型

        [Comment("健保投保起日")]
        [Column(TypeName = "bigint")]
        public long? HealthInsStartDate { get; set; } // 健保投保起日

        [Comment("健保投保迄日")]
        [Column(TypeName = "bigint")]
        public long? HealthInsEndDate { get; set; } // 健保投保迄日

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string remark { get; set; } // 備註

        public Hensure()
        {
            uid = "";
            userId = "";
            dependentRocId = "";
            dependentName = "";
            dependentRelationType = "";
            HealthInsStartDate = null;
            HealthInsEndDate = null;
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class HensureDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 依附使用者編號
        public string dependentRocId { get; set; } // 眷屬身分證字號
        public string dependentName { get; set; } // 眷屬姓名
        public string dependentRelationType { get; set; } // 眷屬依附關係類型
        public string dependentRelationTypeName { get; set; } // 眷屬依附關係名稱
        public string HealthInsStartDate { get; set; } // 健保投保起日
        public string HealthInsEndDate { get; set; } // 健保投保迄日
        public string remark { get; set; } // 備註
        public bool isNewDependent { get; set; } // 是否為新增新眷屬
        public HensureDTO()
        {
            uid = "";
            userId = "";
            dependentRocId = "";
            dependentName = "";
            dependentRelationType = "";
            dependentRelationTypeName = "";
            HealthInsStartDate = "";
            HealthInsEndDate = "";
            remark = "";
            isNewDependent = true;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

