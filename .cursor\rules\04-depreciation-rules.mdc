---
description: 
globs: 
alwaysApply: false
---
# 資產折舊計算方法

本文件說明了系統中使用的資產折舊計算方法，主要用於固定資產折舊單的開發參考。
本系統提供兩種折舊法1、直線法 2、餘額遞減法

## 系統參數設定

系統透過 `PmsSystemParameterService` 提供以下折舊相關的設定功能：

1. 取得折舊法設定：
   - 使用 `GetDepreciationMethodsAsync()` 方法
   - 參數類型為 `PmsParameterType.depreciation_method`

2. 設定默認折舊法：
   - 使用 `SetDefaultDepreciationMethodAsync(string methodId)` 方法
   - 可將特定折舊法設為系統默認值
   - 同時只能有一個默認折舊法

3. 餘額遞減法折舊率設定：
   - 可針對特定財產科目設定專屬折舊率
   - 使用 `SetDecliningBalanceRateForAssetAccountAsync(string assetAccountId, decimal rate, string userId)` 方法
   - 折舊率以百分比方式儲存（例如：40 代表 40%）
   - 若未設定特定科目折舊率，則使用預設值

4. 查詢折舊率設定：
   - 查詢特定科目：`GetDecliningBalanceRateForAssetAccountAsync(string assetAccountId)`
   - 查詢所有設定：`GetAllDecliningBalanceRatesAsync()`

## 直線法

直線法是最常見的折舊計算方式，特點是每年折舊金額相同，計算簡單明瞭。

**計算公式：年折舊金額 = (折舊金額 − 估計殘值) ÷ 耐用年限**

### 特點
- 折舊費用在使用年限內平均分配
- 每期折舊金額固定不變
- 適用於使用壽命可預測且價值損耗均勻的資產
- 會計處理簡單，容易理解和計算

**範例**

一台設備：
- 折舊金額：100,000元
- 耐用年限：5年
- 估計殘值：10,000元

計算：
每年折舊金額 = (100,000 − 10,000) ÷ 5 = 18,000元


## 餘額遞減法

餘額遞減法是一種加速折舊的方法，前期折舊金額較大，後期逐漸減少。

**計算公式：年折舊金額 = 期初帳面價值 × 折舊率**

折舊率依以下方式計算：
1、使用固定比率

### 特點
- 資產使用初期折舊費用高，後期逐漸減少
- 反映許多資產實際價值損耗模式（新資產貶值較快）
- 可以在資產使用初期獲得較高的稅務減免
- 適合技術更新快、初期維修費用少的資產

### 系統設定說明
- 可針對不同財產科目設定不同的折舊率
- 折舊率以百分比方式設定（例如：40 代表 40%）
- 若未針對特定財產科目設定折舊率，則使用系統預設值
- 折舊率設定儲存於 `Pms_SystemParameters` 表中，參數類型為 `declining_balance_rate`

**範例**

同樣設備：
- 成本：100,000元
- 預計使用年限：5年
- 殘值：10,000元
- 折舊率：40%

計算過程：
1. 第1年：100,000 × 40% = 40,000元
2. 第2年：(100,000 - 40,000) × 40% = 24,000元
3. 第3年：(100,000 - 40,000 - 24,000) × 40% = 14,400元
4. 以此類推...





