import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 使用者身分介面定義
export interface PmsUserRole {
    pmsUserRoleId: string;
    roleName: string;
    description: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 使用者身分映射介面
export interface PmsUserRoleMapping {
    pmsUserRoleMappingId: string;
    userId: string;
    pmsUserRoleId: string;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// 使用者身分與映射組合介面
export interface UserRoleWithMapping extends PmsUserRole {
    pmsUserRoleMappingId: string;
}

// 取得使用者身分列表
export async function getPmsUserRoles(): Promise<ApiResponse<PmsUserRole[]>> {
    try {
        const response = await httpClient(apiEndpoints.getPmsUserRoles, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        console.log("API Error:", error);
        return {
            success: false,
            message: error.message || "獲取使用者身分列表失敗",
            data: [],
        };
    }
}

// 取得使用者身分詳情
export async function getPmsUserRoleById(id: string): Promise<ApiResponse<PmsUserRole>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPmsUserRoleDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取使用者身分詳情失敗",
            data: undefined,
        };
    }
}

// 新增使用者身分
export async function addPmsUserRole(data: Partial<PmsUserRole>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addPmsUserRole, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增使用者身分失敗",
        };
    }
}

// 編輯使用者身分
export async function editPmsUserRole(data: Partial<PmsUserRole>): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.editPmsUserRole}?id=${data.pmsUserRoleId}`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯使用者身分失敗",
        };
    }
}

// 刪除使用者身分
export async function deletePmsUserRole(data: Partial<PmsUserRole>): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.deletePmsUserRole}?id=${data.pmsUserRoleId}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除使用者身分失敗",
        };
    }
}

// 取得使用者的身分列表
export async function getUserRoles(userId: string): Promise<ApiResponse<UserRoleWithMapping[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getUserRoles}?userId=${userId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取使用者身分列表失敗",
            data: [],
        };
    }
}

// 指派身分給使用者
export async function assignRoleToUser(data: Partial<PmsUserRoleMapping>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.assignRoleToUser, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "指派使用者身分失敗",
        };
    }
}

// 移除使用者身分
export async function removeRoleFromUser(data: Partial<PmsUserRoleMapping>): Promise<ApiResponse> {
    try {
        const response = await httpClient(
            apiEndpoints.removeRoleFromUser,
            {
                method: "POST",
                body: JSON.stringify(data),
                headers: {
                    "Content-Type": "application/json",
                },
            }
        );

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "移除使用者身分失敗",
        };
    }
}
