"use client";

/* 內容區
  /app/components/layout/Content.tsx
*/
import React, { useEffect, useState } from "react";
import { Layout } from "antd";
import Breadcrumb from "../common/Breadcrumb";
import ChatMessage from "../common/ChatMessage";

const { Content: AntContent } = Layout;
const Content: React.FC<{
  children?: React.ReactNode;
}> = ({ children }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <AntContent
      style={{
        margin: isMobile ? 0 : "24px",
        minHeight: 280,
        background: "#f5f5f5",
        borderRadius: isMobile ? 0 : 8,
        display: "flex",
        flexDirection: "column",
        flex: 1,
        overflow: "auto",
        WebkitOverflowScrolling: "touch",
        position: "relative",
        width: "100%",
        marginLeft: isMobile ? "80px" : 0,
      }}
    >
      <div
        style={{
          padding: isMobile ? "12px" : "24px",
          flex: 1,
          width: isMobile ? "calc(100% - 80px)" : "100%",
          maxWidth: "100%",
          boxSizing: "border-box",
        }}
      >
        <div
          style={{
            marginBottom: isMobile ? "12px" : "6px",
            display: isMobile ? "flex" : "block",
            alignItems: "center",
            overflowX: "auto",
            WebkitOverflowScrolling: "touch",
          }}
        >
          <Breadcrumb />
        </div>
        <div
          style={{
            maxWidth: "100%",
            overflowX: "auto",
            WebkitOverflowScrolling: "touch",
          }}
        >
          {children}
        </div>
      </div>
      <ChatMessage />
    </AntContent>
  );
};

export default Content;
