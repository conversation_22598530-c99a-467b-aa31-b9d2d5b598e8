import React, { useEffect, useState } from 'react';
import { Form, Input, TreeSelect, Button, Space, message, Spin, Checkbox } from 'antd';
import {
    EmployeeRegularSalary,
    createEmptyEmployeeRegularSalary,
    addEmployeeRegularSalary,
    editEmployeeRegularSalary,
    getEmployeeRegularSalaryDetail
} from '@/services/pas/RegularSalary/EmployeeRegularSalaryService';
import {
    RegularSalaryItem,
    getRegularSalaryItemList
} from '@/services/pas/RegularSalary/RegularSalaryItemService';

interface RegularSalaryRecordEditorProps {
    userId: string;
    regularsalaryItemUid?: string;
    onSuccess: () => void;
    onCancel: () => void;
}

const RegularSalaryRecordEditor: React.FC<RegularSalaryRecordEditorProps> = ({
    userId,
    regularsalaryItemUid,
    onSuccess,
    onCancel
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);
    const [salaryItems, setSalaryItems] = useState<RegularSalaryItem[]>([]);
    const [initialData, setInitialData] = useState<EmployeeRegularSalary>(
        createEmptyEmployeeRegularSalary()
    );
    const [selectedItemType, setSelectedItemType] = useState<string>('');
    const [formKey, setFormKey] = useState<number>(0);

    // 將薪資項目轉換為樹狀結構
    const transformToTreeData = (items: RegularSalaryItem[]) => {
        const additionItems = items.filter(item => item.itemType === '1' || item.itemTypeName.includes('加項'));
        const deductionItems = items.filter(item => item.itemType === '2' || item.itemTypeName.includes('減項'));

        return [
            {
                title: '加項',
                value: 'addition',
                key: 'addition',
                selectable: false,
                children: additionItems.map(item => ({
                    title: item.itemName,
                    value: item.uid,
                    key: item.uid,
                    itemType: item.itemType
                }))
            },
            {
                title: '減項',
                value: 'deduction',
                key: 'deduction',
                selectable: false,
                children: deductionItems.map(item => ({
                    title: item.itemName,
                    value: item.uid,
                    key: item.uid,
                    itemType: item.itemType
                }))
            }
        ];
    };

    // 獲取薪資項目列表
    const fetchSalaryItems = async () => {
        try {
            const res = await getRegularSalaryItemList();
            if (res.success && res.data) {
                setSalaryItems(res.data);
            } else {
                message.error(res.message || '載入薪資項目失敗');
            }
        } catch (err) {
            message.error('載入薪資項目失敗');
        }
    };

    // 獲取特定薪資項目詳細資料
    const fetchSalaryDetail = async () => {
        setLoading(true);
        try {
            const res = await getEmployeeRegularSalaryDetail(regularsalaryItemUid!);
            if (res.success && res.data) {
                setInitialData(res.data);
                form.setFieldsValue(res.data);
                // 設置選中的項目類型
                const selectedItem = salaryItems.find(item => item.uid === res.data?.salaryItemUid);
                if (selectedItem) {
                    setSelectedItemType(selectedItem.itemType);
                }
            } else {
                message.error(res.message || '載入薪資資料失敗');
            }
        } catch (err) {
            message.error('載入薪資資料失敗');
        } finally {
            setLoading(false);
        }
    };

    const handleFormSuccess = () => {
        onSuccess();
        form.resetFields();
        setFormKey(prev => prev + 1);
    };

    const handleCancel = () => {
        onCancel();
        form.resetFields();
        setFormKey(prev => prev + 1);
    };

    const handleSubmit = async (values: any) => {
        setSaving(true);
        try {
            const payload = {
                ...initialData,
                ...values
            };

            let res;
            if (regularsalaryItemUid) {
                res = await editEmployeeRegularSalary({ ...payload, uid: regularsalaryItemUid });
            } else {
                res = await addEmployeeRegularSalary({ ...payload, userId });
            }

            if (res.success) {
                message.success(regularsalaryItemUid ? '更新成功' : '新增成功');
                handleFormSuccess();
            } else {
                message.error(res.message || '操作失敗');
            }
        } catch (err: any) {
            if (!err?.errorFields) {
                message.error('儲存時發生錯誤');
            }
        } finally {
            setSaving(false);
        }
    };

    useEffect(() => {
        fetchSalaryItems();

        if (regularsalaryItemUid) {
            fetchSalaryDetail();
        } else {
            const emptyData = {
                ...createEmptyEmployeeRegularSalary(),
                userId,
                salaryItemUid: ''
            };
            setInitialData(emptyData);
            form.setFieldsValue(emptyData);
        }
    }, [userId, regularsalaryItemUid, formKey]);

    const handleSalaryItemChange = (value: string) => {
        const selectedItem = salaryItems.find(item => item.uid === value);
        if (selectedItem) {
            setSelectedItemType(selectedItem.itemType);
        }
    };

    if (loading) {
        return (
            <div style={{ textAlign: "center", padding: 20 }}>
                <Spin>
                    <div style={{ padding: "20px" }}>載入中...</div>
                </Spin>
            </div>
        );
    }

    return (
        <Form
            key={formKey}
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="pas-form"
            initialValues={initialData}
        >
            <Form.Item
                name="salaryItemUid"
                label="薪資項目"
                rules={[{ required: true, message: '請選擇薪資項目' }]}
            >
                <TreeSelect
                    placeholder="請選擇薪資項目"
                    disabled={!!regularsalaryItemUid}
                    treeData={transformToTreeData(salaryItems)}
                    onChange={handleSalaryItemChange}
                    treeDefaultExpandAll
                />
            </Form.Item>

            <Form.Item
                name="amount"
                label="金額"
                rules={[
                    { required: true, message: '請輸入金額' },
                    {
                        pattern: /^\d+$/,
                        message: '請輸入有效的金額（整數）'
                    }
                ]}
            >
                <Input placeholder="請輸入金額" />
            </Form.Item>

            <Form.Item
                name="remark"
                label="備註"
            >
                <Input.TextArea
                    rows={4}
                    placeholder="請輸入備註"
                />
            </Form.Item>

            <Form.Item>
                <Space>
                    <Button type="primary" htmlType="submit" loading={saving}>
                        {regularsalaryItemUid ? '更新' : '新增'}
                    </Button>
                    <Button onClick={handleCancel}>
                        取消
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};

export default RegularSalaryRecordEditor; 