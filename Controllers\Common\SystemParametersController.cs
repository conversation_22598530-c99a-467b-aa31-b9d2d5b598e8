﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using System.Threading.Tasks;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;
using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("系統參數管理")]
    public class SystemParametersController : ControllerBase
    {
        private readonly ISystemParametersService _SystemParametersService;
        private readonly IUsersService _usersService;
        public SystemParametersController(ISystemParametersService SystemParametersService, IUsersService usersService)
        {
            _SystemParametersService = SystemParametersService;
            _usersService = usersService;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        [HttpGet]
        [Route("GetSystemParameters")]
        [SwaggerOperation(Summary = "取得系統參數列表", Description = "取得系統參數列表")]
        public async Task<IActionResult> GetSystemParameters()
        {
            var paramList = await _SystemParametersService.GetSystemParametersAsync();
            return Ok(paramList);
        }

        [HttpPost]
        [Route("AddSystemParameters")]
        [SwaggerOperation(Summary = "新增系統參數", Description = "新增系統參數資料")]
        public async Task<IActionResult> AddSystemParameters([FromBody] SystemParametersDTO parameters)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersService.AddSystemParametersAsync(parameters, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditSystemParameters")]
        [SwaggerOperation(Summary = "編輯系統參數", Description = "編輯系統參數資料")]
        public async Task<IActionResult> EditSystemParameters([FromBody] SystemParametersDTO parameters)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersService.EditSystemParametersAsync(parameters, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteSystemParameters")]
        [SwaggerOperation(Summary = "刪除系統參數", Description = "刪除系統參數資料")]
        public async Task<IActionResult> DeleteSystemParameters([FromBody] SystemParametersDTO parameters)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersService.DeleteSystemParametersAsync(parameters, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("RestoreSystemParameters")]
        [SwaggerOperation(Summary = "還原系統參數", Description = "還原系統參數資料")]
        public async Task<IActionResult> RestoreSystemParameters([FromBody] SystemParametersDTO parameters)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersService.RestoreSystemParametersAsync(parameters, tokenUid);
            return Ok(new { result, msg });
        }
    }
}
