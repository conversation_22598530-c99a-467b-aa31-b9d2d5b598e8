using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 鄉鎮市區基本資料表
    /// </summary>
    public class District : ModelBaseEntity
    {
        [Key]
        [Comment("鄉鎮市區編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string DistrictId { get; set; } // 鄉鎮市區編號

        [Comment("縣市編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string CityId { get; set; } // 縣市編號

        [Comment("鄉鎮市區名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } // 鄉鎮市區名稱

        [Comment("郵遞區號")]
        [Column(TypeName = "nvarchar(5)")]
        public string ZipCode { get; set; } // 郵遞區號

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public District()
        {
            DistrictId = "";
            CityId = "";
            Name = "";
            ZipCode = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class DistrictDTO : ModelBaseEntityDTO
    {
        public string DistrictId { get; set; } // 鄉鎮市區編號
        public string CityId { get; set; } // 縣市編號
        public string Name { get; set; } // 鄉鎮市區名稱
        public string ZipCode { get; set; } // 郵遞區號
        public int SortCode { get; set; } // 排序號碼

        public DistrictDTO()
        {
            DistrictId = "";
            CityId = "";
            Name = "";
            ZipCode = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}