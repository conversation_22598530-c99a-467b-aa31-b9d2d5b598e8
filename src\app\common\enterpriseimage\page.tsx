"use client";

/* 公司商標&印章管理
  /app/common/enterpriseimage/page.tsx
*/

import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Upload,
  message,
  Image,
  Input,
  Form,
  Select,
  Typography,
  Tag,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  SearchOutlined,
  UploadOutlined,
  FileImageOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { UploadProps, UploadFile } from "antd/es/upload/interface";
import {
  EnterpriseImage,
  getEnterpriseImages,
  updateEnterpriseImage,
  deleteEnterpriseImage,
} from "@/services/common/enterpriseImageService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { useAuth } from "@/contexts/AuthContext";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { apiConfig } from "@/config/api";
import { siteConfig, ImageFormat } from "@/config/site";

const { Text } = Typography;

const EnterpriseImagePage: React.FC = () => {
  const [images, setImages] = useState<EnterpriseImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<EnterpriseImage[]>([]);
  const [searchText, setSearchText] = useState("");
  const [selectedType, setSelectedType] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [imageToDelete, setImageToDelete] = useState<EnterpriseImage | null>(
    null
  );
  const { user } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [form] = Form.useForm();
  const [formValid, setFormValid] = useState(false);

  // 載入圖片列表
  const loadImages = async () => {
    setLoading(true);
    try {
      const response = await getEnterpriseImages();
      if (response.success && response.data) {
        setImages(response.data);
      } else {
        notifyError("載入失敗", response.message || "無法獲取圖片列表");
      }
    } catch (error) {
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadImages();
  }, []);

  // 處理搜尋和篩選
  useEffect(() => {
    let filtered = images;

    // 按圖片類型篩選
    if (selectedType) {
      filtered = filtered.filter((image) => image.imageType === selectedType);
    }

    // 按名稱搜尋
    if (searchText) {
      filtered = filtered.filter((image) =>
        image.imageName.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredImages(filtered);
  }, [searchText, selectedType, images]);

  // 處理上傳
  const handleUpload = async (values: any) => {
    try {
      if (!selectedFile) {
        notifyError("上傳失敗", "請選擇圖片");
        return;
      }

      if (!values.imageName?.trim()) {
        notifyError("上傳失敗", "請輸入圖片名稱");
        return;
      }

      if (!values.imageType?.trim()) {
        notifyError("上傳失敗", "請選擇圖片類型");
        return;
      }

      setLoading(true);

      const formData = new FormData();
      formData.append("ImageType", values.imageType.trim());
      formData.append("ImageName", values.imageName.trim());
      formData.append("ImageFile", selectedFile);
      formData.append("CreateUserId", user?.userId || "");

      console.log("Uploading file:", selectedFile.name);
      console.log("Form data:", {
        ImageType: values.imageType.trim(),
        ImageName: values.imageName.trim(),
        CreateUserId: user?.userId || "",
      });

      const response = await updateEnterpriseImage(formData);
      if (response.success) {
        notifySuccess("上傳成功", "圖片已上傳");
        loadImages();
        handleCancel();
      } else {
        notifyError("上傳失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      console.error("Upload error:", error);
      notifyError("上傳錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 監聽表單值變化
  const handleFormChange = async () => {
    try {
      const values = form.getFieldsValue();
      const isValid =
        selectedFile !== null &&
        values.imageName?.trim() !== "" &&
        values.imageType?.trim() !== "";
      setFormValid(isValid);
    } catch (error) {
      setFormValid(false);
    }
  };

  // 處理文件選擇
  const handleFileSelect: UploadProps["onChange"] = async (info) => {
    console.log("File info:", info);
    if (info.fileList && info.fileList.length > 0) {
      const file = info.fileList[info.fileList.length - 1];
      if (file.originFileObj) {
        // 檢查檔案大小
        if (file.originFileObj.size > siteConfig.upload.image.maxSize) {
          notifyError("檔案過大", "圖片大小不能超過 5MB");
          setSelectedFile(null);
          setFormValid(false);
          return;
        }

        // 檢查檔案格式
        if (
          !siteConfig.upload.image.acceptedFormats.includes(
            file.originFileObj.type as ImageFormat
          )
        ) {
          notifyError(
            "格式不支援",
            `僅支援 ${siteConfig.upload.image.acceptedFormatsList} 格式`
          );
          setSelectedFile(null);
          setFormValid(false);
          return;
        }

        console.log("Selected file:", file.originFileObj);
        setSelectedFile(file.originFileObj);
        // 自動設置檔案名稱
        const fileName = file.name.split(".")[0];
        await form.setFieldsValue({
          imageName: fileName,
        });
        // 立即觸發表單驗證
        setTimeout(() => {
          handleFormChange();
        }, 0);
      }
    } else {
      console.log("No file selected");
      setSelectedFile(null);
      setFormValid(false);
    }
  };

  // 處理取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setSelectedFile(null);
    setFormValid(false);
    form.resetFields();
  };

  // 處理刪除
  const handleDelete = (image: EnterpriseImage) => {
    setImageToDelete(image);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!imageToDelete) return;

    try {
      const deleteData = {
        imageId: imageToDelete.imageId,
        deleteUserId: user?.userId || "",
        createUserId: user?.userId || "",
        updateUserId: user?.userId || "",
      };
      const response = await deleteEnterpriseImage(deleteData);
      if (response.success) {
        notifySuccess("刪除成功", "圖片已刪除");
        loadImages();
        setDeleteModalVisible(false);
        setImageToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      notifyError("刪除錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 獲取完整的圖片URL
  const getImageUrl = (imagePath: string) => {
    if (!imagePath) return "";
    // 如果已經是完整URL就直接返回
    if (imagePath.startsWith("http")) return imagePath;
    // 使用正確的路徑格式
    const baseUrl = apiConfig.baseURL.replace("/api", "");
    return `${baseUrl}/${imagePath}`;
  };

  // 表格欄位定義
  const columns: ColumnsType<EnterpriseImage> = [
    {
      title: "圖片預覽",
      key: "preview",
      render: (_, record) => (
        <Image
          src={getImageUrl(record.imagePath)}
          alt={record.imageName}
          width={100}
          height={100}
          style={{ objectFit: "contain" }}
        />
      ),
    },
    {
      title: "圖片名稱",
      dataIndex: "imageName",
      key: "imageName",
    },
    {
      title: "圖片類型",
      dataIndex: "imageType",
      key: "imageType",
    },
    {
      title: "上傳時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (createTime: number) =>
        DateTimeExtensions.formatDateFromTimestamp(createTime),
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card title="公司商標&印章管理">
      <Space style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          新增圖片
        </Button>
        <Select
          placeholder="選擇圖片類型"
          allowClear
          style={{ width: 200 }}
          onChange={(value) => setSelectedType(value)}
          value={selectedType}
          options={[
            { label: "全部類型", value: "" },
            { label: "Logo", value: "Logo" },
            { label: "印章", value: "印章" },
            { label: "商標", value: "商標" },
            { label: "其他", value: "其他" },
          ]}
          showSearch
          optionFilterProp="label"
        />
        <Input
          placeholder="搜尋圖片名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </Space>

      <Table
        columns={columns}
        dataSource={filteredImages}
        loading={loading}
        rowKey="imageId"
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
        }}
      />

      <Modal
        title="上傳圖片"
        open={isModalVisible}
        onOk={form.submit}
        onCancel={handleCancel}
        okButtonProps={{
          disabled: !formValid,
          icon: <UploadOutlined />,
          loading: loading
            ? { delay: 300, icon: <SyncOutlined spin /> }
            : false,
        }}
        okText="確認上傳"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
          onValuesChange={handleFormChange}
        >
          <Form.Item>
            <Upload
              accept={siteConfig.upload.image.acceptedFormats.join(",")}
              maxCount={1}
              beforeUpload={() => false}
              onChange={handleFileSelect}
              showUploadList={false}
            >
              <Button icon={<FileImageOutlined />}>選擇圖片</Button>
            </Upload>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                支援格式：{siteConfig.upload.image.acceptedFormatsList}
                ，檔案大小上限：
                {(siteConfig.upload.image.maxSize / 1024 / 1024).toFixed(0)}MB
              </Text>
            </div>
            {selectedFile && (
              <div style={{ marginTop: 16 }}>
                <img
                  src={URL.createObjectURL(selectedFile)}
                  alt="預覽"
                  style={{
                    maxWidth: "100%",
                    maxHeight: "200px",
                    objectFit: "contain",
                  }}
                />
                <div style={{ marginTop: 8 }}>
                  <Tag color="blue">{selectedFile.name}</Tag>
                  <Tag color="green">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </Tag>
                </div>
              </div>
            )}
          </Form.Item>

          <Form.Item
            name="imageName"
            label="圖片名稱"
            rules={[{ required: true, message: "請輸入圖片名稱" }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="imageType"
            label="圖片類型"
            rules={[{ required: true, message: "請選擇圖片類型" }]}
          >
            <Select placeholder="請選擇圖片類型">
              <Select.Option value="Logo">Logo</Select.Option>
              <Select.Option value="印章">印章</Select.Option>
              <Select.Option value="商標">商標</Select.Option>
              <Select.Option value="其他">其他</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onOk={executeDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setImageToDelete(null);
          setDeleteConfirmText("");
        }}
        okButtonProps={{
          icon: <DeleteOutlined />,
          danger: true,
          disabled: deleteConfirmText !== (imageToDelete?.imageName || ""),
        }}
        okText="確認刪除"
        cancelText="取消"
      >
        <p>
          請輸入<strong>「{imageToDelete?.imageName}」</strong>以確認刪除：
        </p>
        <Input
          value={deleteConfirmText}
          onChange={(e) => setDeleteConfirmText(e.target.value)}
          placeholder="請輸入圖片名稱"
        />
      </Modal>
    </Card>
  );
};

export default EnterpriseImagePage;
