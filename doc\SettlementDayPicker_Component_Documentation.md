# SettlementDayPicker 組件文檔

## 概述

`SettlementDayPicker` 是一個專為 FastERP 系統設計的結算日期選擇器組件，提供了兩種選擇方式：日曆選擇和下拉選擇。該組件最終儲存為月份中的日期數字 (1-31)，符合後端 `CustomerDetail` 和 `SupplierDetail` 的 `SettlementDay` 欄位要求。

## 功能特點

### 🎯 核心功能
- **雙模式選擇**: 支援日曆選擇和下拉選擇兩種模式
- **智能切換**: 用戶可以隨時在兩種模式間切換
- **數據一致性**: 確保與後端 API 的數據格式完全匹配
- **用戶友好**: 提供直觀的視覺反饋和操作提示

### 🎨 UI/UX 設計
- **響應式設計**: 適配不同螢幕尺寸
- **視覺提示**: 顯示選中的結算日期
- **工具提示**: 提供操作指引
- **圖標指示**: 清楚標示當前模式

## 技術規格

### 介面定義
```typescript
interface SettlementDayPickerProps {
  value?: number;                    // 當前值 (1-31)
  onChange?: (value: number | undefined) => void;  // 值變更回調
  placeholder?: string;              // 佔位符文字
  disabled?: boolean;                // 是否禁用
  style?: React.CSSProperties;       // 自定義樣式
}
```

### 數據格式
- **輸入**: `number | undefined` (1-31)
- **輸出**: `number | undefined` (1-31)
- **後端對應**: `CustomerDetail.SettlementDay` 和 `SupplierDetail.SettlementDay`

## 使用方法

### 基本用法
```tsx
import SettlementDayPicker from '@/app/ims/components/shared/SettlementDayPicker';

// 在表單中使用
<Form.Item label="結算日" name="settlementDay">
  <SettlementDayPicker placeholder="請選擇結算日" />
</Form.Item>
```

### 進階用法
```tsx
// 帶有工具提示和圖標的標籤
<Form.Item 
  label={
    <Space>
      <CalendarOutlined style={{ color: '#1890ff' }} />
      <span>結算日</span>
    </Space>
  } 
  name={['customerDetail', 'settlementDay']}
  tooltip="選擇每月的結算日期，用於客戶應收帳款結算"
>
  <SettlementDayPicker placeholder="請選擇客戶結算日" />
</Form.Item>
```

### 受控組件用法
```tsx
const [settlementDay, setSettlementDay] = useState<number | undefined>(15);

<SettlementDayPicker 
  value={settlementDay}
  onChange={setSettlementDay}
  placeholder="請選擇結算日"
/>
```

## 組件行為

### 日曆模式
- 顯示當前月份的日曆
- 只允許選擇當前月份的日期
- 選擇後提取日期數字 (1-31)
- 格式顯示為 "DD"

### 下拉模式
- 提供 1-31 的數字選項
- 顯示格式為 "每月 X 日"
- 支援清除選擇
- 支援鍵盤導航

### 模式切換
- 點擊右側按鈕可切換模式
- 切換時保持當前選中的值
- 提供工具提示說明

## 整合說明

### 在 PartnerFormModal 中的應用
```tsx
// 客戶結算日
<Form.Item 
  label={
    <Space>
      <CalendarOutlined style={{ color: '#1890ff' }} />
      <span>結算日</span>
    </Space>
  } 
  name={['customerDetail', 'settlementDay']}
  tooltip="選擇每月的結算日期，用於客戶應收帳款結算"
>
  <SettlementDayPicker placeholder="請選擇客戶結算日" />
</Form.Item>

// 供應商結算日
<Form.Item 
  label={
    <Space>
      <CalendarOutlined style={{ color: '#1890ff' }} />
      <span>結算日</span>
    </Space>
  } 
  name={['supplierDetail', 'settlementDay']}
  tooltip="選擇每月的結算日期，用於供應商應付帳款結算"
>
  <SettlementDayPicker placeholder="請選擇供應商結算日" />
</Form.Item>
```

### 後端數據對應
```csharp
// CustomerDetail.cs
[Range(1, 31, ErrorMessage = "結帳日必須在1-31之間")]
public int? SettlementDay { get; set; }

// SupplierDetail.cs  
[Range(1, 31, ErrorMessage = "結帳日必須在1-31之間")]
public int? SettlementDay { get; set; }
```

## 樣式自定義

### 預設樣式
- 使用 Ant Design 的設計語言
- 支援主題色彩配置
- 響應式佈局

### 自定義樣式
```tsx
<SettlementDayPicker 
  style={{ 
    width: '200px',
    borderRadius: '8px'
  }}
/>
```

## 可訪問性

### 鍵盤支援
- Tab 鍵導航
- Enter 鍵確認選擇
- 方向鍵在日曆中導航

### 螢幕閱讀器支援
- 適當的 ARIA 標籤
- 語義化的 HTML 結構
- 清楚的狀態描述

## 測試建議

### 功能測試
1. 驗證兩種模式的切換
2. 測試數據格式的正確性
3. 確認與表單的整合
4. 驗證清除功能

### 響應式測試
1. 移動端顯示效果
2. 平板端操作體驗
3. 桌面端完整功能

### 可訪問性測試
1. 鍵盤導航
2. 螢幕閱讀器相容性
3. 高對比度模式

## 維護說明

### 依賴項
- `antd`: DatePicker, Select, Space, Button, Tooltip
- `dayjs`: 日期處理
- `@ant-design/icons`: 圖標組件

### 更新注意事項
- 保持與後端 API 的數據格式一致
- 遵循 FastERP 的設計規範
- 確保向後相容性

### 已知限制
- 日曆模式僅顯示當前月份
- 不支援跨月份的日期選擇
- 數值範圍限制在 1-31

## 版本歷史

### v1.0.0 (2024-01-07)
- 初始版本發布
- 支援雙模式選擇
- 整合到 PartnerFormModal
- 完整的 TypeScript 支援
