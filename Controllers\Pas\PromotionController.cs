using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("升遷異動資料管理")]
    public class PromotionController : ControllerBase
    {
        private readonly IPromotionService _promotionService;

        public PromotionController(IPromotionService promotionService)
        {
            _promotionService = promotionService;
        }

        [HttpGet]
        [Route("GetAll/{userId}")]
        [SwaggerOperation(Summary = "取得升遷異動列表", Description = "取得指定使用者的所有升遷異動資料列表")]
        public async Task<IActionResult> GetPromotionList(string userId)
        {
            try
            {
                var result = await _promotionService.GetPromotionListAsync(userId);
                return Ok(new { success = true, message = "取得升遷異動列表成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得升遷異動明細", Description = "依升遷異動編號取得明細資料，包含關聯的部門異動資料")]
        public async Task<IActionResult> GetPromotionDetail(string uid)
        {
            try
            {
                var result = await _promotionService.GetPromotionDetailAsync(uid);
                if (result == null)
                {
                    return NotFound(new { success = false, message = "找不到指定的升遷異動資料" });
                }
                return Ok(new { success = true, message = "取得升遷異動明細成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        [Route("GetLatest/{userId}")]
        [SwaggerOperation(Summary = "取得最新升遷資料", Description = "取得使用者最新的升遷資料作為新增時的預設值")]
        public async Task<IActionResult> GetLatestPromotion(string userId)
        {
            try
            {
                var result = await _promotionService.GetLatestPromotionAsync(userId);
                return Ok(new { success = true, message = "取得最新升遷資料成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增升遷異動", Description = "新增升遷異動資料，可同時新增開支部門異動和服務部門異動")]
        public async Task<IActionResult> AddPromotion([FromForm] PromotionFormDTO formData)
        {
            try
            {
                var promotionData = MapFormToDTO(formData);
                var (result, message) = await _promotionService.AddPromotionAsync(promotionData);
                return Ok(new { success = result, message = message, data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯升遷異動", Description = "編輯升遷異動資料，可同時更新開支部門異動和服務部門異動")]
        public async Task<IActionResult> EditPromotion([FromForm] PromotionFormDTO formData)
        {
            try
            {
                var promotionData = MapFormToDTO(formData);
                var (result, message) = await _promotionService.EditPromotionAsync(promotionData);
                return Ok(new { success = result, message = message, data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除升遷異動", Description = "刪除升遷異動資料，會同時刪除關聯的部門異動資料")]
        public async Task<IActionResult> DeletePromotion([FromBody] string uid)
        {
            try
            {
                var (result, message) = await _promotionService.DeletePromotionAsync(uid);
                return Ok(new { success = result, message = message, data = (object?)null });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpGet]
        [Route("GetByEffectiveDate/{userId}/{effectiveDate}")]
        [SwaggerOperation(Summary = "根據指定日期取得生效的升遷資料", Description = "取得使用者在指定日期時生效的升遷資料及部門資訊，供其他系統模組查詢員工在特定時間點的職務狀態")]
        public async Task<IActionResult> GetPromotionByEffectiveDate(string userId, string effectiveDate)
        {
            try
            {
                // 驗證日期格式
                if (!DateTime.TryParseExact(effectiveDate, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _))
                {
                    return BadRequest(new { success = false, message = "日期格式錯誤，請使用 yyyy-MM-dd 格式", data = (object?)null });
                }

                var result = await _promotionService.GetPromotionByEffectiveDateAsync(userId, effectiveDate);

                if (result == null)
                {
                    return Ok(new { success = true, message = "在指定日期未找到生效的升遷資料", data = (object?)null });
                }

                return Ok(new { success = true, message = "取得指定日期的升遷資料成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        [HttpGet]
        [Route("GetByEffectiveDate/{userId}")]
        [SwaggerOperation(Summary = "根據當天日期取得生效的升遷資料", Description = "查詢該用戶當天生效的升遷資料及部門資訊")]
        public async Task<IActionResult> GetPromotionByCurrentDate(string userId)
        {
            try
            {
                // 驗證輸入參數
                if (string.IsNullOrWhiteSpace(userId))
                {
                    return BadRequest(new { success = false, message = "使用者編號不能為空", data = (object?)null });
                }

                // 不提供日期參數，將使用當天日期
                var result = await _promotionService.GetPromotionByEffectiveDateAsync(userId);

                if (result == null)
                {
                    return Ok(new { success = true, message = "在當天日期未找到生效的升遷資料", data = (object?)null });
                }

                return Ok(new { success = true, message = "取得當天的升遷資料成功", data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message, data = (object?)null });
            }
        }

        #region 私有方法

        private PromotionDTO MapFormToDTO(PromotionFormDTO formData)
        {
            var promotionData = new PromotionDTO
            {
                Uid = formData.Uid ?? "",
                UserId = formData.UserId ?? "",
                PromotionType = formData.PromotionType ?? "",
                JobTitle = formData.JobTitle ?? "",
                JobLevel = formData.JobLevel ?? "",
                JobRank = formData.JobRank ?? "",
                PromotionDate = formData.PromotionDate ?? "",
                EffectiveDate = formData.EffectiveDate ?? "",
                PromotionReason = formData.PromotionReason ?? "",
                JobroleType = formData.JobroleType ?? "",
                SalaryType = formData.SalaryType ?? "",
                SalaryAmount = formData.SalaryAmount ?? "",
                CategoryType = formData.CategoryType ?? "",
                Remark = formData.Remark ?? ""
            };

            // 處理開支部門異動資料
            if (!string.IsNullOrEmpty(formData.ExpenseDepartmentId))
            {
                promotionData.ExpenseDepartmentChange = new ExpenseDepartmentChangeDTO
                {
                    ExpenseDepartmentId = formData.ExpenseDepartmentId ?? "",
                    ChangeDate = formData.ExpenseChangeDate ?? "",
                    EffectiveDate = formData.ExpenseEffectiveDate ?? "",
                    ChangeReason = formData.ExpenseChangeReason ?? "",
                    Remark = formData.ExpenseRemark ?? ""
                };
            }

            // 處理服務部門異動資料
            if (!string.IsNullOrEmpty(formData.ServiceDepartmentId))
            {
                promotionData.ServiceDepartmentChange = new ServiceDepartmentChangeDTO
                {
                    ServiceDepartmentId = formData.ServiceDepartmentId ?? "",
                    ServiceDivisionId = formData.ServiceDivisionId ?? "",
                    ChangeDate = formData.ServiceChangeDate ?? "",
                    EffectiveDate = formData.ServiceEffectiveDate ?? "",
                    ChangeReason = formData.ServiceChangeReason ?? "",
                    Remark = formData.ServiceRemark ?? ""
                };
            }

            return promotionData;
        }

        #endregion
    }

    /// <summary>
    /// 升遷異動表單DTO，用於接收前端Form資料
    /// </summary>
    public class PromotionFormDTO
    {
        // 升遷異動基本資料
        public string? Uid { get; set; }
        public string? UserId { get; set; }
        public string? PromotionType { get; set; }
        public string? JobTitle { get; set; }
        public string? JobLevel { get; set; }
        public string? JobRank { get; set; }
        public string? PromotionDate { get; set; }
        public string? EffectiveDate { get; set; }
        public string? PromotionReason { get; set; }
        public string? JobroleType { get; set; }
        public string? SalaryType { get; set; }
        public string? SalaryAmount { get; set; }
        public string? CategoryType { get; set; }
        public string? Remark { get; set; }

        // 開支部門異動資料
        public string? ExpenseDepartmentId { get; set; }
        public string? ExpenseChangeDate { get; set; }
        public string? ExpenseEffectiveDate { get; set; }
        public string? ExpenseChangeReason { get; set; }
        public string? ExpenseRemark { get; set; }

        // 服務部門異動資料
        public string? ServiceDepartmentId { get; set; }
        public string? ServiceDivisionId { get; set; }
        public string? ServiceChangeDate { get; set; }
        public string? ServiceEffectiveDate { get; set; }
        public string? ServiceChangeReason { get; set; }
        public string? ServiceRemark { get; set; }
    }
}