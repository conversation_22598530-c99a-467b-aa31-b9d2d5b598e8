import { FormInstance } from "antd";
import {
    AssetLocationTransfer,
    AssetLocationTransferDetail,
    AssetLocationTransferWithDetails,
    AssetLocationTransferQuery
} from "@/services/pms/assetLocationTransferService";

// 狀態常數
export const APPROVAL_STATUS = {
    PENDING: "PENDING",
    APPROVED: "APPROVED",
    REJECTED: "REJECTED"
} as const;

export const EXECUTION_STATUS = {
    PENDING: "PENDING",
    COMPLETED: "COMPLETED",
    CANCELLED: "CANCELLED"
} as const;

// 狀態映射
export const APPROVAL_STATUS_MAPPING = {
    [APPROVAL_STATUS.PENDING]: "待審核",
    [APPROVAL_STATUS.APPROVED]: "已審核",
    [APPROVAL_STATUS.REJECTED]: "已駁回"
};

export const EXECUTION_STATUS_MAPPING = {
    [EXECUTION_STATUS.PENDING]: "待執行",
    [EXECUTION_STATUS.COMPLETED]: "已執行",
    [EXECUTION_STATUS.CANCELLED]: "已取消"
};

// 狀態顏色
export const APPROVAL_STATUS_COLORS = {
    [APPROVAL_STATUS.PENDING]: "orange",
    [APPROVAL_STATUS.APPROVED]: "green",
    [APPROVAL_STATUS.REJECTED]: "red"
};

export const EXECUTION_STATUS_COLORS = {
    [EXECUTION_STATUS.PENDING]: "blue",
    [EXECUTION_STATUS.COMPLETED]: "green",
    [EXECUTION_STATUS.CANCELLED]: "red"
};

// 變動項目常數
export const CHANGE_ITEMS = {
    LOCATION: "LOCATION",
    CUSTODIAN: "CUSTODIAN",
    USER: "USER",
    DEPARTMENT: "DEPARTMENT",
    DIVISION: "DIVISION"
} as const;

export const CHANGE_ITEMS_MAPPING = {
    [CHANGE_ITEMS.LOCATION]: "存放地點",
    [CHANGE_ITEMS.CUSTODIAN]: "保管人",
    [CHANGE_ITEMS.USER]: "使用人",
    [CHANGE_ITEMS.DEPARTMENT]: "部門",
    [CHANGE_ITEMS.DIVISION]: "股別"
};

// 導入主要介面
export type {
    AssetLocationTransfer,
    AssetLocationTransferDetail,
    AssetLocationTransferWithDetails,
    AssetLocationTransferQuery
} from "@/services/pms/assetLocationTransferService";

// 查詢介面擴展
export interface AssetLocationChangeFormQuery extends AssetLocationTransferQuery {
    keyword?: string;
    applicantId?: string;
    applicantDepartmentId?: string;
}

// 表單初始值
export const formInitialValues: Partial<AssetLocationTransfer> = {
    transferNo: "",
    transferDate: Date.now(),
    applicantId: "",
    applicantName: "",
    applicantDepartmentId: "",
    applicantDepartmentName: "",
    transferReason: "",
    approvalStatus: APPROVAL_STATUS.PENDING,
    approverId: "",
    approverName: "",
    approvalDate: 0,
    approvalComments: "",
    executionStatus: EXECUTION_STATUS.PENDING,
    executorId: "",
    executorName: "",
    executionDate: 0,
    notes: ""
};

// 明細初始值
export const detailInitialValues: Partial<AssetLocationTransferDetail> = {
    detailId: "",
    transferId: "",
    assetId: "",
    assetNo: "",
    assetName: "",
    originalLocationId: "",
    originalLocationName: "",
    newLocationId: "",
    newLocationName: "",
    originalCustodianId: "",
    originalCustodianName: "",
    newCustodianId: "",
    newCustodianName: "",
    originalUserId: "",
    originalUserName: "",
    newUserId: "",
    newUserName: "",
    originalDepartmentId: "",
    originalDepartmentName: "",
    newDepartmentId: "",
    newDepartmentName: "",
    originalDivisionId: "",
    originalDivisionName: "",
    newDivisionId: "",
    newDivisionName: "",
    changeItems: "",
    detailNotes: ""
};

// 統計資料介面
export interface TransferStatistics {
    totalCount: number;
    pendingApprovalCount: number;
    approvedCount: number;
    rejectedCount: number;
    pendingExecutionCount: number;
    completedCount: number;
    cancelledCount: number;
    thisMonthCount: number;
    thisYearCount: number;
}

// 表單Props
export interface FormProps {
    editingTransfer: AssetLocationTransferWithDetails | null;
    isViewMode: boolean;
    onCancel: () => void;
    onSuccess: (data: AssetLocationTransferWithDetails) => void;
    form: FormInstance;
}

// 審核Props
export interface ApprovalModalProps {
    visible: boolean;
    transfer: AssetLocationTransfer | null;
    onCancel: () => void;
    onSuccess: () => void;
}

// 執行Props
export interface ExecutionModalProps {
    visible: boolean;
    transfer: AssetLocationTransfer | null;
    onCancel: () => void;
    onSuccess: () => void;
}

// 財產選擇Props
export interface AssetSelectionModalProps {
    visible: boolean;
    onCancel: () => void;
    onConfirm: (assets: any[]) => void;
    selectedAssets?: string[];
}

// 批次操作Props
export interface BatchOperationProps {
    selectedTransfers: AssetLocationTransfer[];
    onBatchApprove: () => void;
    onBatchReject: () => void;
    onBatchExecute: () => void;
    onBatchCancel: () => void;
}

// 選項介面
export interface SelectOption {
    label: string;
    value: string;
    disabled?: boolean;
}

// 審核選項
export const APPROVAL_OPTIONS: SelectOption[] = [
    { label: APPROVAL_STATUS_MAPPING[APPROVAL_STATUS.PENDING], value: APPROVAL_STATUS.PENDING },
    { label: APPROVAL_STATUS_MAPPING[APPROVAL_STATUS.APPROVED], value: APPROVAL_STATUS.APPROVED },
    { label: APPROVAL_STATUS_MAPPING[APPROVAL_STATUS.REJECTED], value: APPROVAL_STATUS.REJECTED }
];

// 執行選項
export const EXECUTION_OPTIONS: SelectOption[] = [
    { label: EXECUTION_STATUS_MAPPING[EXECUTION_STATUS.PENDING], value: EXECUTION_STATUS.PENDING },
    { label: EXECUTION_STATUS_MAPPING[EXECUTION_STATUS.COMPLETED], value: EXECUTION_STATUS.COMPLETED },
    { label: EXECUTION_STATUS_MAPPING[EXECUTION_STATUS.CANCELLED], value: EXECUTION_STATUS.CANCELLED }
];

// 變動項目選項
export const CHANGE_ITEMS_OPTIONS: SelectOption[] = [
    { label: CHANGE_ITEMS_MAPPING[CHANGE_ITEMS.LOCATION], value: CHANGE_ITEMS.LOCATION },
    { label: CHANGE_ITEMS_MAPPING[CHANGE_ITEMS.CUSTODIAN], value: CHANGE_ITEMS.CUSTODIAN },
    { label: CHANGE_ITEMS_MAPPING[CHANGE_ITEMS.USER], value: CHANGE_ITEMS.USER },
    { label: CHANGE_ITEMS_MAPPING[CHANGE_ITEMS.DEPARTMENT], value: CHANGE_ITEMS.DEPARTMENT },
    { label: CHANGE_ITEMS_MAPPING[CHANGE_ITEMS.DIVISION], value: CHANGE_ITEMS.DIVISION }
];

// 工具函數
export const getApprovalStatusText = (status: string): string => {
    return APPROVAL_STATUS_MAPPING[status as keyof typeof APPROVAL_STATUS_MAPPING] || status;
};

export const getExecutionStatusText = (status: string): string => {
    return EXECUTION_STATUS_MAPPING[status as keyof typeof EXECUTION_STATUS_MAPPING] || status;
};

export const getApprovalStatusColor = (status: string): string => {
    return APPROVAL_STATUS_COLORS[status as keyof typeof APPROVAL_STATUS_COLORS] || "default";
};

export const getExecutionStatusColor = (status: string): string => {
    return EXECUTION_STATUS_COLORS[status as keyof typeof EXECUTION_STATUS_COLORS] || "default";
};

export const getChangeItemsText = (items: string): string => {
    if (!items) return "";
    const itemList = items.split(",");
    return itemList.map(item =>
        CHANGE_ITEMS_MAPPING[item.trim() as keyof typeof CHANGE_ITEMS_MAPPING] || item
    ).join("、");
}; 