using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("參數選項管理")]
    public class PasOptionParameterController : ControllerBase
    {
        private readonly IPasOptionParameterService _Interface;

        public PasOptionParameterController(IPasOptionParameterService pasOptionParameterService)
        {
            _Interface = pasOptionParameterService;
        }

        [HttpGet]
        [Route("GetJobtitleOptions")]
        [SwaggerOperation(Summary = "取得職稱選項", Description = "取得職稱選項")]
        public async Task<IActionResult> GetJobtitleOptions()
        {
            var result = await _Interface.GetJobtitleOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetIdTypeOptions")]
        [SwaggerOperation(Summary = "取得證號別選項", Description = "取得證號別選項")]
        public async Task<IActionResult> GetIdTypeOptions()
        {
            var result = await _Interface.GetIdTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetIdErrorOptions")]
        [SwaggerOperation(Summary = "取得證號錯誤註記選項", Description = "取得證號錯誤註記選項")]
        public async Task<IActionResult> GetIdErrorOptions()
        {
            var result = await _Interface.GetIdErrorOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetBloodTypeOptions")]
        [SwaggerOperation(Summary = "取得血型選項", Description = "取得血型選項")]
        public async Task<IActionResult> GetBloodTypeOptions()
        {
            var result = await _Interface.GetBloodTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetDegreeTypeOptions")]
        [SwaggerOperation(Summary = "取得學位選項", Description = "取得學位選項")]
        public async Task<IActionResult> GetDegreeTypeOptions()
        {
            var result = await _Interface.GetDegreeTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetGraduateOptions")]
        [SwaggerOperation(Summary = "取得結業選項", Description = "取得結業選項")]
        public async Task<IActionResult> GetGraduateOptions()
        {
            var result = await _Interface.GetGraduateOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetSuspendTypeOptions")]
        [SwaggerOperation(Summary = "取得留停類型選項", Description = "取得留停類型選項")]
        public async Task<IActionResult> GetSuspendTypeOptions()
        {
            var result = await _Interface.GetSuspendTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetSuspendKindOptions")]
        [SwaggerOperation(Summary = "取得留停種類選項", Description = "取得留停種類選項")]
        public async Task<IActionResult> GetSuspendKindOptions()
        {
            var result = await _Interface.GetSuspendKindOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetEmployeeContributionOptions")]
        [SwaggerOperation(Summary = "取得員工自提額類型選項", Description = "取得員工自提額類型選項")]
        public async Task<IActionResult> GetEmployeeContributionOptions()
        {
            var result = await _Interface.GetEmployeeContributionOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetIncomeTaxTypeOptions")]
        [SwaggerOperation(Summary = "取得計稅型式類型選項", Description = "取得計稅型式類型選項")]
        public async Task<IActionResult> GetIncomeTaxTypeOptions()
        {
            var result = await _Interface.GetIncomeTaxTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetPayoffTypeOptions")]
        [SwaggerOperation(Summary = "取得發薪狀況選項", Description = "取得發薪狀況選項")]
        public async Task<IActionResult> GetPayoffTypeOptions()
        {
            var result = await _Interface.GetPayoffTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetDepTypeOptions")]
        [SwaggerOperation(Summary = "取得關係類型選項", Description = "取得關係類型選項")]
        public async Task<IActionResult> GetDepTypeOptions()
        {
            var result = await _Interface.GetDepTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetRegularSalaryCreaseTypeOptions")]
        [SwaggerOperation(Summary = "取得薪資項目類型選項", Description = "取得薪資項目類型選項")]
        public async Task<IActionResult> GetRegularSalaryCreaseTypeOptions()
        {
            var result = await _Interface.GetRegularSalaryCreaseTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetJobroleTypeOptions")]
        [SwaggerOperation(Summary = "取得任用資格選項", Description = "取得任用資格選項")]
        public async Task<IActionResult> GetJobroleTypeOptions()
        {
            var result = await _Interface.GetJobroleTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetSalaryTypeOptions")]
        [SwaggerOperation(Summary = "取得薪俸類型選項", Description = "取得薪俸類型選項")]
        public async Task<IActionResult> GetSalaryTypeOptions()
        {
            var result = await _Interface.GetSalaryTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetCategoryTypeOptions")]
        [SwaggerOperation(Summary = "取得錄用類別選項", Description = "取得錄用類別選項")]
        public async Task<IActionResult> GetCategoryTypeOptions()
        {
            var result = await _Interface.GetCategoryTypeOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetJobLevelOptions")]
        [SwaggerOperation(Summary = "取得職等選項", Description = "取得職等選項")]
        public async Task<IActionResult> GetJobLevelOptions()
        {
            var result = await _Interface.GetJobLevelOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetJobRankOptions")]
        [SwaggerOperation(Summary = "取得級數選項", Description = "取得級數選項")]
        public async Task<IActionResult> GetJobRankOptions()
        {
            var result = await _Interface.GetJobRankOptionsAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetPromotionTypeOptions")]
        [SwaggerOperation(Summary = "取得升遷類型選項", Description = "取得升遷類型選項")]
        public async Task<IActionResult> GetPromotionTypeOptions()
        {
            var result = await _Interface.GetPromotionTypeOptionsAsync();
            return Ok(result);
        }

    }
}