using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class DepreciationFormService : IDepreciationFormService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public DepreciationFormService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得固定資產折舊單資料
        /// </summary>
        /// <returns>固定資產折舊單資料列表</returns>
        public async Task<List<DepreciationFormDTO>> GetDepreciationFormsAsync()
        {
            // 查詢折舊主檔
            var query = from d in _context.Set<DepreciationForm>().Where(d => !d.IsDeleted)
                        select d;

            var depreciationForms = await query
                .OrderBy(item => item.DepreciationYear)
                .ThenBy(item => item.DepreciationMonth)
                .ToListAsync();

            var result = new List<DepreciationFormDTO>();

            foreach (var form in depreciationForms)
            {
                // 映射主檔資料
                var formDto = _mapper.Map<DepreciationFormDTO>(form);

                // 查詢相關的折舊明細
                var detailsQuery = from detail in _context.Set<DepreciationFormDetail>().Where(d => !d.IsDeleted)
                                   join asset in _context.Pms_Assets on detail.AssetId equals asset.AssetId into assetGroup
                                   from asset in assetGroup.DefaultIfEmpty()
                                   orderby detail.DepreciationYear, detail.DepreciationMonth
                                   where detail.DepreciationYear == form.DepreciationYear
                                         && detail.DepreciationMonth == form.DepreciationMonth
                                   select new { Detail = detail, Asset = asset };

                var details = await detailsQuery.ToListAsync();

                // 映射明細資料
                formDto.DepreciationFormDetail = details.Select(d =>
                {
                    var detailDto = _mapper.Map<DepreciationFormDetailDTO>(d.Detail);
                    if (d.Asset != null)
                    {
                        detailDto.AssetNo = d.Asset.AssetNo;
                        detailDto.AssetName = d.Asset.AssetName;
                    }
                    return detailDto;
                }).ToList();

                result.Add(formDto);
            }

            return result;
        }

        /// <summary>
        /// 新增固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        public async Task<(bool, string)> AddDepreciationFormAsync(DepreciationFormDTO _data)
        {
            try
            {
                // 檢查折舊紀錄是否已被使用
                if (await IsDepreciationUsedAsync(_data.DepreciationId))
                {
                    return (false, "此折舊紀錄已被使用");
                }

                // 取得對應的折舊明細資料
                if (Guid.TryParse(_data.DepreciationId, out Guid depreciationId))
                {
                    var detail = await _context.Set<DepreciationFormDetail>()
                        .FirstOrDefaultAsync(d => d.DepreciationId == depreciationId && !d.IsDeleted);

                    if (detail != null)
                    {
                        // 更新折舊日期
                        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        if (_data.DepreciationFormDetail != null && _data.DepreciationFormDetail.Any())
                        {
                            foreach (var detailDto in _data.DepreciationFormDetail)
                            {
                                // 更新折舊日期
                                detailDto.DepreciationDate = currentTime;

                                // 更新折舊明細記錄
                                var detailEntity = await _context.Set<DepreciationFormDetail>()
                                    .FirstOrDefaultAsync(d => d.DepreciationId == Guid.Parse(detailDto.DepreciationId) && !d.IsDeleted);

                                if (detailEntity != null)
                                {
                                    detailEntity.DepreciationDate = currentTime;
                                    detailEntity.UpdateTime = currentTime;
                                    detailEntity.UpdateUserId = _data.CreateUserId;
                                    _context.Update(detailEntity);

                                    // 更新資產的累計折舊金額
                                    var asset = await _context.Pms_Assets
                                        .FirstOrDefaultAsync(a => a.AssetId == detailEntity.AssetId && !a.IsDeleted);
                                    if (asset != null)
                                    {
                                        asset.AccumulatedDepreciationAmount = detailEntity.AccumulatedDepreciation;
                                        asset.UpdateTime = currentTime;
                                        asset.UpdateUserId = _data.CreateUserId;
                                        _context.Update(asset);
                                    }
                                }
                            }
                        }

                        // 更新主表的折舊日期
                        _data.DepreciationDate = currentTime;
                    }
                }

                var entity = _mapper.Map<DepreciationForm>(_data);
                entity.DepreciationDate = _data.DepreciationDate;
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.IsDeleted = false;

                Console.WriteLine(DateTimeOffset.FromUnixTimeSeconds(entity.DepreciationDate).ToString("yyyy-MM-dd"));

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        public async Task<(bool, string)> EditDepreciationFormAsync(DepreciationFormDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationForm>()
                    .FindAsync(Guid.Parse(_data.DepreciationFormId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 如果折舊紀錄編號有變更，檢查新的折舊紀錄是否已被使用
                if (entity.DepreciationId != Guid.Parse(_data.DepreciationId))
                {
                    if (await IsDepreciationUsedAsync(_data.DepreciationId))
                    {
                        return (false, "此折舊紀錄已被使用");
                    }

                    // 取得對應的折舊明細資料
                    if (Guid.TryParse(_data.DepreciationId, out Guid depreciationId))
                    {
                        var detail = await _context.Set<DepreciationFormDetail>()
                            .FirstOrDefaultAsync(d => d.DepreciationId == depreciationId && !d.IsDeleted);

                        if (detail != null)
                        {
                            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            if (_data.DepreciationFormDetail != null && _data.DepreciationFormDetail.Any())
                            {
                                foreach (var detailDto in _data.DepreciationFormDetail)
                                {
                                    // 更新折舊日期
                                    detailDto.DepreciationDate = currentTime;

                                    // 更新折舊明細記錄
                                    var detailEntity = await _context.Set<DepreciationFormDetail>()
                                        .FirstOrDefaultAsync(d => d.DepreciationId == Guid.Parse(detailDto.DepreciationId) && !d.IsDeleted);

                                    // 更新折舊明細記錄
                                    if (detailEntity != null)
                                    {
                                        detailEntity.DepreciationDate = currentTime;
                                        detailEntity.UpdateTime = currentTime;
                                        detailEntity.UpdateUserId = _data.UpdateUserId;
                                        _context.Update(detailEntity);
                                    }
                                }
                            }

                            // 更新主表的折舊日期
                            _data.DepreciationDate = currentTime;
                        }
                    }
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        public async Task<(bool, string)> DeleteDepreciationFormAsync(DepreciationFormDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationForm>()
                    .FindAsync(Guid.Parse(_data.DepreciationFormId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 更新折舊明細的折舊日期為0
                var details = await _context.Set<DepreciationFormDetail>()
                    .Where(d => d.DepreciationYear == entity.DepreciationYear &&
                           d.DepreciationMonth == entity.DepreciationMonth &&
                           !d.IsDeleted)
                    .ToListAsync();

                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                foreach (var detail in details)
                {
                    detail.DepreciationDate = 0;
                    detail.UpdateTime = currentTime;
                    detail.UpdateUserId = _data.DeleteUserId;
                    _context.Update(detail);
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得固定資產折舊單明細
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>固定資產折舊單明細</returns>
        public async Task<DepreciationFormDetailDTO> GetDepreciationFormDetailAsync(string _depreciationId)
        {
            if (!Guid.TryParse(_depreciationId, out Guid depreciationId))
            {
                return null;
            }

            var query = from d in _context.Set<DepreciationFormDetail>()
                        where d.DepreciationId == depreciationId && !d.IsDeleted
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()
                        select new { Detail = d, AssetNo = asset != null ? asset.AssetNo : "", AssetName = asset != null ? asset.AssetName : "" };

            var result = await query.FirstOrDefaultAsync();
            if (result == null)
            {
                return null;
            }

            var dto = _mapper.Map<DepreciationFormDetailDTO>(result.Detail);
            dto.AssetNo = result.AssetNo;
            dto.AssetName = result.AssetName;
            return dto;
        }

        /// <summary>
        /// 檢查折舊紀錄是否已被使用
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>是否已被使用</returns>
        public async Task<bool> IsDepreciationUsedAsync(string _depreciationId)
        {
            if (!Guid.TryParse(_depreciationId, out Guid depreciationId))
            {
                return false;
            }

            return await _context.Set<DepreciationForm>()
                .AnyAsync(d => d.DepreciationId == depreciationId && !d.IsDeleted && d.DepreciationDate > 0);
        }
    }
}