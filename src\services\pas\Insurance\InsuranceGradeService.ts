import { apiEndpoints } from "@/config/api";
import { httpClient } from "../../http";
import { ApiResponse } from "@/config/api";
import {
    INSURANCE_TYPES,
    INSURANCE_TYPE_OPTIONS,
    getInsuranceTypeName,
    DATE_FORMAT,
    InsuranceType
} from "@/services/pas/Insurance/constants/insuranceConstants";

// 保險級距介面 - 匹配後端 DTO 欄位名稱
export interface InsuranceGrade {
    uid: string;
    insuranceType: number;
    monthlySalary: number;
    startDate: string;
    endDate?: string | null;
    remark?: string | null;
    createTime?: number | null;
    createUserId?: string | null;
    updateTime?: number | null;
    updateUserId?: string | null;
    deleteTime?: number | null;
    deleteUserId?: string | null;
    isDeleted: boolean;
    // 員工統計欄位
    currentEmployeeCount?: number;
    pendingEmployeeCount?: number;
    totalEmployeeCount?: number;
}

export const createEmptyInsuranceGrade = (): InsuranceGrade => ({
    uid: '',
    insuranceType: INSURANCE_TYPES.LABOR, // 預設勞保
    monthlySalary: 0,
    startDate: '',
    endDate: null,
    remark: null,
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false,
});

// 重新匯出常數以保持向後相容性
export { INSURANCE_TYPES, INSURANCE_TYPE_OPTIONS, getInsuranceTypeName };

// 取得保險級距列表
export async function getInsuranceGradeList(insuranceType: number): Promise<ApiResponse<InsuranceGrade[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getInsuranceGradeList}/${insuranceType}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得保險級距列表失敗",
        };
    }
}

// 取得保險級距明細
export async function getInsuranceGradeDetail(uid: string): Promise<ApiResponse<InsuranceGrade>> {
    try {
        const response = await httpClient(`${apiEndpoints.getInsuranceGradeDetail}/${uid}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得保險級距明細失敗",
        };
    }
}

// 新增保險級距
export async function addInsuranceGrade(data: Partial<InsuranceGrade>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addInsuranceGrade, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增保險級距失敗",
        };
    }
}

// 編輯保險級距
export async function editInsuranceGrade(data: Partial<InsuranceGrade>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editInsuranceGrade, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯保險級距失敗",
        };
    }
}

// 刪除保險級距
export async function deleteInsuranceGrade(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteInsuranceGrade, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除保險級距失敗",
        };
    }
}

// 依據月投保薪資取得對應的保險級距
export async function getInsuranceGradeBySalary(monthlySalary: number, insuranceType: number): Promise<ApiResponse<InsuranceGrade>> {
    try {
        const response = await httpClient(
            `${apiEndpoints.getInsuranceGradeBySalary}?monthlySalary=${monthlySalary}&insuranceType=${insuranceType}`,
            {
                method: "GET",
            }
        );

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "查詢保險級距失敗",
        };
    }
}

// 保險級距員工詳細資訊介面
export interface InsuranceGradeEmployeeDetail {
    userId: string;
    userName: string;
    departmentName: string;
    positionName: string;
    startDate: string;
    endDate?: string | null;
    monthlySalary: number;
    statusDescription: string;
}

// 取得保險級距員工詳細列表
export async function getEmployeeInsuranceGradeDetail(gradeUid: string, status: string): Promise<ApiResponse<InsuranceGradeEmployeeDetail[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getEmployeeInsuranceGradeDetail}/${gradeUid}/${status}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得保險級距員工詳細資料失敗",
        };
    }
}

