using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IDependentService
    {
        /// <summary>
        /// 取得所有扶養資料列表
        /// </summary>
        /// <param name="_userId">使用者編號</param>
        /// <returns>扶養資料列表</returns>
        Task<List<DependentDTO>> GetDependentListAsync(string _userId);

        /// <summary>
        /// 取得扶養資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>扶養資料明細</returns>
        Task<DependentDTO> GetDependentDetailAsync(string _uid);

        /// <summary>
        /// 新增扶養資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddDependentAsync(DependentDTO _data);

        /// <summary>
        /// 編輯扶養資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditDependentAsync(DependentDTO _data);

        /// <summary>
        /// 刪除扶養資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteDependentAsync(string _uid);
    }
}
