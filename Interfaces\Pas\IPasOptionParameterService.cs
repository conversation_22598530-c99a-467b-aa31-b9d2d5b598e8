﻿using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IPasOptionParameterService
    {
        /// <summary>
        /// 取得職稱選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetJobtitleOptionsAsync();

        /// <summary>
        /// 取得證號別選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetIdTypeOptionsAsync();

        /// <summary>
        /// 取得證號錯誤註記選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetIdErrorOptionsAsync();

        /// <summary>
        /// 取得血型選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetBloodTypeOptionsAsync();

        /// <summary>
        /// 取得學位選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetDegreeTypeOptionsAsync();

        /// <summary>
        /// 取得結業選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetGraduateOptionsAsync();

        /// <summary>
        /// 取得留停類型選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetSuspendTypeOptionsAsync();

        /// <summary>
        /// 取得留復職種類選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetSuspendKindOptionsAsync();

        /// <summary>
        /// 取得員工自提額類型選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetEmployeeContributionOptionsAsync();

        /// <summary>
        /// 取得計稅型式類型選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetIncomeTaxTypeOptionsAsync();

        /// <summary>
        /// 取得發薪狀況選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetPayoffTypeOptionsAsync();


        /// <summary>
        /// 取得關係類型選項列表
        /// </summary>
        /// <returns>取得關係類型列表</returns>
        Task<List<SelectOption>> GetDepTypeOptionsAsync();

        /// <summary>
        /// 取得薪資項目類型選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetRegularSalaryCreaseTypeOptionsAsync();

        /// <summary>
        /// 取得任用資格選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetJobroleTypeOptionsAsync();

        /// <summary>
        /// 取得薪俸類型選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetSalaryTypeOptionsAsync();

        /// <summary>
        /// 取得錄用類別選項列表
        /// </summary>
        /// <returns>取得所有資料列表</returns>
        Task<List<SelectOption>> GetCategoryTypeOptionsAsync();

        /// <summary>
        /// 取得職等選項列表
        /// </summary>
        /// <returns>取得所有職等資料列表</returns>
        Task<List<SelectOption>> GetJobLevelOptionsAsync();

        /// <summary>
        /// 取得級數選項列表
        /// </summary>
        /// <returns>取得所有級數資料列表</returns>
        Task<List<SelectOption>> GetJobRankOptionsAsync();

        /// <summary>
        /// 取得升遷類型選項列表
        /// </summary>
        /// <returns>取得所有升遷類型資料列表</returns>
        Task<List<SelectOption>> GetPromotionTypeOptionsAsync();

    }
}
