/**
 * 格式化為台幣顯示
 * @param value 要格式化的數值
 * @param options 格式化選項
 * @returns 格式化後的字串
 */
export const formatTWCurrency = (
    value: number | null | undefined,
    options?: {
        showSymbol?: boolean;
        minimumFractionDigits?: number;
        maximumFractionDigits?: number;
    }
): string => {
    if (value === null || value === undefined) {
        return 'NT$ 0';
    }

    const {
        showSymbol = true,
        minimumFractionDigits = 0,
        maximumFractionDigits = 0,
    } = options || {};

    return new Intl.NumberFormat('zh-TW', {
        style: showSymbol ? 'currency' : 'decimal',
        currency: 'TWD',
        minimumFractionDigits,
        maximumFractionDigits,
    }).format(value);
};

/**
 * 格式化數字顯示千分位
 * @param value 要格式化的數值
 * @returns 格式化後的字串，包含千分位
 */
export const formatThousands = (value: number | string | undefined | null): string => {
    if (value === null || value === undefined) {
        return '$ 0';
    }

    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return `$ ${numValue}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * 解析帶有千分位及貨幣符號的金額字符串
 * @param value 要解析的金額字串
 * @returns 解析後的數值
 */
export const parseAmount = (value: string | undefined): number => {
    if (!value) return 0;
    return parseFloat(value.replace(/\$\s?|(,*)/g, ''));
}; 