using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Linq;

namespace FAST_ERP_Backend.Attributes
{
    /// <summary>
    /// 驗證Email格式的屬性
    /// 
    /// 基本使用
    /// [Email]
    /// public string Email { get; set; }
    /// 
    /// 只允許常見域名
    /// [Email(allowCommonDomainsOnly: true)]
    /// public string Email { get; set; }
    /// 
    /// 有效的Email格式範例：
    /// <EMAIL>
    /// <EMAIL>
    /// <EMAIL>
    /// <EMAIL>
    /// <EMAIL>
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class EmailAttribute : ValidationAttribute
    {
        private readonly bool _allowCommonDomainsOnly;
        private readonly string[] _commonDomains = new[]
        {
            "mail.fast.org.tw",
            "gmail.com",
            "yahoo.com",
            "yahoo.com.tw",
            "hotmail.com",
            "outlook.com",
            "msn.com",
            "icloud.com",
            "me.com",
            "mac.com",
            "live.com",
            "qq.com",
            "163.com",
            "ntu.edu.tw",
            "nthu.edu.tw",
            "nctu.edu.tw",
            "ncku.edu.tw",
            "gov.tw",
            "edu.tw",
            "org.tw",
            "com.tw"
        };

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="allowCommonDomainsOnly">是否只允許常見域名，預設為false</param>
        public EmailAttribute(bool allowCommonDomainsOnly = false)
            : base("Email格式不正確")
        {
            _allowCommonDomainsOnly = allowCommonDomainsOnly;
        }

        /// <summary>
        /// 驗證方法
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success; // 若要求必填，請搭配 [Required] 屬性
            }

            string email = value.ToString().Trim().ToLower();

            // 基本Email格式驗證
            var emailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
            if (!emailRegex.IsMatch(email))
            {
                return new ValidationResult("Email格式不正確");
            }

            // 檢查是否包含多個@符號
            if (email.Count(c => c == '@') > 1)
            {
                return new ValidationResult("Email不能包含多個@符號");
            }

            // 檢查域名部分
            string domain = email.Split('@')[1];

            // 如果設定只允許常見域名，則進行域名檢查
            if (_allowCommonDomainsOnly && !_commonDomains.Contains(domain))
            {
                return new ValidationResult($"只允許使用常見的Email域名，例如：{string.Join(", ", _commonDomains.Take(5))}等");
            }

            // 其他驗證規則
            if (email.Length > 254) // RFC 5321規定
            {
                return new ValidationResult("Email長度不能超過254個字元");
            }

            string localPart = email.Split('@')[0];
            if (localPart.Length > 64) // RFC 5321規定
            {
                return new ValidationResult("Email帳號部分不能超過64個字元");
            }

            // 檢查是否有連續的點號
            if (email.Contains(".."))
            {
                return new ValidationResult("Email不能包含連續的點號");
            }

            // 檢查開頭和結尾是否為點號
            if (localPart.StartsWith(".") || localPart.EndsWith("."))
            {
                return new ValidationResult("Email帳號部分不能以點號開頭或結尾");
            }

            return ValidationResult.Success;
        }
    }
}