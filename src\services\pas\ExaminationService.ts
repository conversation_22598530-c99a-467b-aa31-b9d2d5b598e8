import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 考試資料
export interface Examination {
    uid: string;
    userId: string;
    examName: string;
    examType: string;
    admittanceGrade: string;
    examInstitution: string;
    examStartDate: string;
    examEndDate: string;
    certificateDate: string;
    certificateNumber: string;
    remark: string;
}

export const createEmptyExamination = (): Examination => ({
    uid: '',
    userId: '',
    examName: '',
    examType: '',
    admittanceGrade: '',
    examInstitution: '',
    examStartDate: '',
    examEndDate: '',
    certificateDate: '',
    certificateNumber: '',
    remark: '',
});


// 搜尋考試列表
export async function getExaminationList(userid: string): Promise<ApiResponse<Examination[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getExaminationList}/${userid}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋考試資料失敗",
        };
    }
}

// 搜尋考試明細
export async function getExaminationDetail(uid: string): Promise<ApiResponse<Examination>> {
    return await httpClient(`${apiEndpoints.getExaminationDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增考試資料
export async function addExamination(data: Partial<Examination>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addExamination, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增考試資料失敗",
        };
    }
}

// 編輯考試資料
export async function editExamination(data: Partial<Examination>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editExamination, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯考試資料失敗",
        };
    }
}

// 刪除考試資料
export async function deleteExamination(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteExamination, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除考試資料失敗",
        };
    }
}