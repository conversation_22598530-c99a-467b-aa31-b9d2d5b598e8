using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Linq.Expressions;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;

namespace FAST_ERP_Backend.Extensions.Pas
{
    public static class EmployeeQueryExtensions
    {
        public static IQueryable<EmployeeDTO> ToEmployeeDTO(this IQueryable<Users> query, IQueryable<Employee> employeeQuery, EmployeeClass employeeClass)
        {
            Baseform baseform = new Baseform();

            return from usr in query
                   join emp in employeeQuery
                   on usr.UserId.ToString() equals emp.UserId into userEmp
                   from emp in userEmp.DefaultIfEmpty()
                   select new EmployeeDTO
                   {
                       UserId = emp.UserId == null ? "" : emp.UserId,
                       IdNo = emp.IdNo == null ? "" : emp.IdNo,
                       IdType = emp.IdType == null ? "" : emp.IdType,
                       IdTypeName = employeeClass.GetidtypeName(emp.IdType),
                       errorMark = emp.errorMark == null ? "" : emp.errorMark,
                       errorMarkName = employeeClass.GetlistCompareName(employeeClass.list_iderrdata, emp.errorMark),
                       EmpNo = emp.EmpNo == null ? "" : emp.EmpNo,
                       Birthday = baseform.TimestampToDateStr(emp.Birthday),
                       BloodType = emp.BloodType == null ? "" : emp.BloodType,
                       BloodTypeName = employeeClass.GetbloodtypeName(emp.BloodType),
                       SpouseIdNo = emp.SpouseIdNo == null ? "" : emp.SpouseIdNo,
                       SpouseName = emp.SpouseName == null ? "" : emp.SpouseName,
                       EduLevel = emp.EduLevel == null ? "" : emp.EduLevel,
                       EduLevelName = employeeClass.GeteduDegreeTypeName(emp.EduLevel),
                       HireDate = baseform.TimestampToDateStr(emp.HireDate),
                       ProbStartDate = baseform.TimestampToDateStr(emp.ProbStartDate),
                       OfficialHireDate = baseform.TimestampToDateStr(emp.OfficialHireDate),
                       LeaveDate = baseform.TimestampToDateStr(emp.LeaveDate),
                       LaborInsStartDate = baseform.TimestampToDateStr(emp.LaborInsStartDate),
                       HealthInsStartDate = baseform.TimestampToDateStr(emp.HealthInsStartDate),
                       TransferDate = baseform.TimestampToDateStr(emp.TransferDate),
                       remark = emp.remark == null ? "" : emp.remark,
                       CreateTime = emp.CreateTime,
                       CreateUserId = emp.CreateUserId,
                       UpdateTime = emp.UpdateTime,
                       UpdateUserId = emp.UpdateUserId,
                       DeleteTime = emp.DeleteTime,
                       DeleteUserId = emp.DeleteUserId,

                       // 然後把Common_Users的資料塞進 usersDTO
                       usersDTO = new UsersDTO
                       {
                           UserId = usr.UserId,
                           Account = usr.Account,
                           Password = "",
                           Name = usr.Name,
                           EnterpriseGroupId = usr.EnterpriseGroupId,
                           EnterpriseGroupName = "",
                           RolesId = usr.RolesId,
                           RolesName = "",
                           PositionId = usr.PositionId,
                           PositionName = "",
                           EMail = usr.EMail,
                           PermanentAddress = usr.PermanentAddress,
                           MailingAddress = usr.MailingAddress,
                           TelNo = usr.TelNo,
                           Phone = usr.Phone,
                           AltPhone = usr.AltPhone,
                           SortCode = usr.SortCode,
                           UnlockTime = null,
                           CreateTime = null,
                           CreateUserId = null,
                           UpdateTime = null,
                           UpdateUserId = null,
                           DeleteTime = null,
                           DeleteUserId = null,
                           IsDeleted = false,
                       }
                   };
        }
    }
}
