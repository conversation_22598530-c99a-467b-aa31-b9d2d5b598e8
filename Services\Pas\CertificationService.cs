using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class CertificationService : ICertificationService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public CertificationService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<CertificationDTO>> GetCertificationListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Certification
                    .Where(c => c.userId == userId && c.IsDeleted != true)
                    .OrderByDescending(c => c.certificateDate)
                    .Select(c => new CertificationDTO
                    {
                        uid = c.uid,
                        userId = c.userId,
                        certificateName = c.certificateName,
                        certificateYearMonth = c.certificateYearMonth,
                        certificateInstitution = c.certificateInstitution,
                        certificateDate = _baseform.TimestampToDateStr(c.certificateDate),
                        certificateNumber = c.certificateNumber,
                        remark = c.remark,
                        UpdateTime = c.UpdateTime
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得檢覈認證資料錯誤", ex);
            }
        }

        public async Task<CertificationDTO> GetCertificationDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Certification
                    .Where(c => c.uid == uid && c.IsDeleted != true)
                    .Select(c => new CertificationDTO
                    {
                        uid = c.uid,
                        userId = c.userId,
                        certificateName = c.certificateName,
                        certificateYearMonth = c.certificateYearMonth,
                        certificateInstitution = c.certificateInstitution,
                        certificateDate = _baseform.TimestampToDateStr(c.certificateDate),
                        certificateNumber = c.certificateNumber,
                        remark = c.remark,
                        UpdateTime = c.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得檢覈認證明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddCertificationAsync(CertificationDTO data)
        {
            var list_msg_check = CheckCertificationInput(data, "add");
            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newCert = new Certification
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    certificateName = data.certificateName,
                    certificateYearMonth = data.certificateYearMonth,
                    certificateInstitution = data.certificateInstitution,
                    certificateDate = _baseform.DateStrToTimestamp(data.certificateDate),
                    certificateNumber = data.certificateNumber,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Certification.AddAsync(newCert);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "檢覈認證資料登錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"檢覈認證資料登錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditCertificationAsync(CertificationDTO data)
        {
            var list_msg_check = CheckCertificationInput(data, "edit");
            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingCert = await _context.Pas_Certification.FirstOrDefaultAsync(c => c.uid == data.uid && c.IsDeleted != true);
                if (existingCert == null)
                {
                    return (false, "找不到對應的檢覈認證資料");
                }

                existingCert.certificateName = data.certificateName;
                existingCert.certificateYearMonth = data.certificateYearMonth;
                existingCert.certificateInstitution = data.certificateInstitution;
                existingCert.certificateDate = _baseform.DateStrToTimestamp(data.certificateDate);
                existingCert.certificateNumber = data.certificateNumber;
                existingCert.remark = data.remark;

                existingCert.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existingCert.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯檢覈認證資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯檢覈認證資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteCertificationAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingCert = await _context.Pas_Certification.FirstOrDefaultAsync(c => c.uid == uid && c.IsDeleted != true);
                if (existingCert == null)
                {
                    return (false, "資料已刪除或不存在");
                }

                existingCert.IsDeleted = true;
                existingCert.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existingCert.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除檢覈認證資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除檢覈認證資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckCertificationInput(CertificationDTO data, string mode)
        {
            List<string> list_errorMsg = new List<string>();

            if (string.IsNullOrWhiteSpace(data.certificateName))
                list_errorMsg.Add("請輸入檢覈名稱");

            if (!string.IsNullOrEmpty(data.certificateDate) && !_baseform.IsValidDateOrEmpty(data.certificateDate))
                list_errorMsg.Add("發證日期格式輸入錯誤");

            // 可依需求自行增加其他欄位驗證

            return list_errorMsg;
        }
    }
}
