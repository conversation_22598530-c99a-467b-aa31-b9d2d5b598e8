/* 主容器 */
.container {
    padding: 24px;
    background: #f5f5f5;
    min-height: 100vh;
}

/* 內容區域 */
.content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 頁面標題 */
.pageTitle {
    font-size: 24px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

/* 統計卡片區域 */
.statisticsSection {
    margin-bottom: 24px;
}

.statisticsCards {
    margin-bottom: 16px;
}

/* 流程步驟區域 */
.processSection {
    margin-bottom: 24px;
    padding: 24px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.processHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.processTitle {
    font-size: 18px;
    font-weight: 600;
    color: #262626;
    margin: 0;
}

/* 工具列 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
}

.toolbarLeft {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toolbarRight {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 搜尋區域 */
.searchSection {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.searchForm {
    margin-bottom: 0;
}

.searchRow {
    margin-bottom: 0;
}

.searchButton {
    width: 100%;
}

/* 表格區域 */
.tableSection {
    padding: 24px;
}

.tableContainer {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

/* 批次操作區域 */
.batchSection {
    padding: 16px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 12px;
}

.batchInfo {
    color: #6c757d;
    font-size: 14px;
}

/* 分頁區域 */
.paginationSection {
    padding: 16px 24px;
    display: flex;
    justify-content: center;
    background: white;
    border-top: 1px solid #f0f0f0;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .toolbarLeft,
    .toolbarRight {
        justify-content: center;
    }

    .pageTitle {
        font-size: 20px;
    }

    .searchSection {
        padding: 12px 16px;
    }

    .tableSection {
        padding: 16px;
    }

    .processSection {
        padding: 16px;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 8px;
    }

    .pageTitle {
        font-size: 18px;
        margin-bottom: 16px;
    }

    .toolbar {
        padding: 12px 16px;
    }

    .searchSection {
        padding: 8px 12px;
    }

    .tableSection {
        padding: 12px;
    }

    .processSection {
        padding: 12px;
    }
}

/* 表格自定義樣式 */
.customTable {
    background: white;
}

.customTable :global(.ant-table-thead > tr > th) {
    background: #fafafa;
    border-bottom: 2px solid #e8e8e8;
    font-weight: 600;
    color: #262626;
}

.customTable :global(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
}

.customTable :global(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background: #e6f7ff;
}

/* 狀態標籤樣式 */
.statusTag {
    border-radius: 4px;
    font-weight: 500;
    padding: 2px 8px;
}

/* 操作按鈕樣式 */
.actionButton {
    border: none;
    box-shadow: none;
    padding: 4px 8px;
}

.actionButton:hover {
    background: rgba(24, 144, 255, 0.1);
}

.actionButton:focus {
    box-shadow: none;
}

/* 載入狀態 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

/* 空狀態 */
.empty {
    text-align: center;
    padding: 48px 24px;
    color: #8c8c8c;
}

.emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d9d9d9;
}

.emptyText {
    font-size: 16px;
    color: #8c8c8c;
}