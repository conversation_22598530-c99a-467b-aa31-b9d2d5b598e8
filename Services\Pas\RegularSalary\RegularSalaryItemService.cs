using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class RegularSalaryItemService : IRegularSalaryItemService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public RegularSalaryItemService(ERPDbContext context, Baseform baseform, ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<RegularSalaryItemDTO>> GetAllAsync()
        {
            return await _context.Pas_RegularSalaryItem
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.itemName)
                .Select(x => new RegularSalaryItemDTO
                {
                    uid = x.uid,
                    itemName = x.itemName,
                    itemType = x.itemType,
                    isTaxable = x.isTaxable,
                    description = x.description,
                    isEnable = x.isEnable,
                    CreateTime = x.CreateTime,
                    UpdateTime = x.UpdateTime,
                    isTaxableName = x.isTaxable ? "計稅" : "不計稅",
                    isEnableName = x.isEnable ? "啟用" : "停用"
                }).ToListAsync();
        }

        public async Task<RegularSalaryItemDTO?> GetByIdAsync(string uid)
        {
            return await _context.Pas_RegularSalaryItem
                .Where(x => x.uid == uid && !x.IsDeleted)
                .Select(x => new RegularSalaryItemDTO
                {
                    uid = x.uid,
                    itemName = x.itemName,
                    itemType = x.itemType,
                    isTaxable = x.isTaxable,
                    isTaxableName = x.isTaxable ? "計稅" : "不計稅",
                    description = x.description,
                    isEnable = x.isEnable,
                    isEnableName = x.isEnable ? "啟用" : "停用",
                    CreateTime = x.CreateTime,
                    UpdateTime = x.UpdateTime
                }).FirstOrDefaultAsync();
        }

        public async Task<(bool, string)> AddAsync(RegularSalaryItemDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易
            try
            {
                var entity = new RegularSalaryItem
                {
                    uid = Guid.NewGuid().ToString(),
                    itemName = dto.itemName,
                    itemType = dto.itemType,
                    isTaxable = dto.isTaxable,
                    description = dto.description,
                    isEnable = dto.isEnable,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                _context.Pas_RegularSalaryItem.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增常態薪資項目成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增常態薪資項目失敗: {ex.InnerException?.Message ?? ex.Message}");
            }

        }

        public async Task<(bool, string)> EditAsync(RegularSalaryItemDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                var entity = await _context.Pas_RegularSalaryItem.FirstOrDefaultAsync(x => x.uid == dto.uid && !x.IsDeleted);
                if (entity == null)
                {
                    throw new Exception("找不到指定資料");
                }

                entity.itemName = dto.itemName;
                entity.itemType = dto.itemType;
                entity.isTaxable = dto.isTaxable;
                entity.description = dto.description;
                entity.isEnable = dto.isEnable;
                entity.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                entity.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "更新常態薪資項目成功");

            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新常態薪資項目失敗: {ex.InnerException?.Message ?? ex.Message}");
            }

        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                var entity = await _context.Pas_RegularSalaryItem.FirstOrDefaultAsync(x => x.uid == uid && !x.IsDeleted);
                if (entity == null)
                {
                    throw new Exception("資料不存在");
                }

                entity.IsDeleted = true;
                entity.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                entity.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除常態薪資項目成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除常態薪資項目失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<List<CascaderOptionDTO>> GetSalaryItemTypeOptionsAsync()
        {
            var items = await _context.Pas_RegularSalaryItem
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.CreateTime)
                .AsNoTracking()
                .ToListAsync();

            // 使用 Enum 定義分組
            var result = new List<CascaderOptionDTO>();

            foreach (SalaryItemType type in Enum.GetValues(typeof(SalaryItemType)))
            {
                string typeCode = ((int)type).ToString();
                string typeName = type.ToString() switch
                {
                    nameof(SalaryItemType.Add) => "加項",
                    nameof(SalaryItemType.Subtract) => "扣項",
                    _ => "未知"
                };

                var children = items
                    .Where(i => i.itemType == typeCode)
                    .Select(i => new CascaderOptionDTO
                    {
                        value = i.uid,
                        label = i.itemName
                    })
                    .ToList();

                if (children.Any())
                {
                    result.Add(new CascaderOptionDTO
                    {
                        value = typeCode,
                        label = typeName,
                        children = children
                    });
                }
            }

            return result;
        }
    }


}