"use client";

/* 頁首
  /app/components/layout/Header.tsx
*/
import React, { useState, useEffect } from "react";
import {
  Layout,
  Input,
  Button,
  theme,
  Dropdown,
  Badge,
  notification,
  Select,
  Space,
  Spin,
} from "antd";
import type { MenuProps } from "antd";
import {
  BellOutlined,
  MessageOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  HomeOutlined,
  CompassOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { routes } from "@/config/routes";
import { useAuth } from "@/contexts/AuthContext";
import * as authService from "@/services/authService";
import Logo from "@/app/components/common/Logo";

const { Header: AntHeader } = Layout;
const { Option } = Select;

// 選單樹結構介面
interface MenuTree {
  label: string;
  key: string;
  icon?: string;
  children?: MenuTree[];
  value?: string;
  path?: string;
}

// 選單項目介面
interface FlatMenuItem {
  label: string;
  value: string;
  path: string;
  parentLabels: string[];
}

const Header: React.FC = () => {
  const router = useRouter();
  const { logout } = useAuth();
  const [api, contextHolder] = notification.useNotification();
  const [menuTrees, setMenuTrees] = useState<MenuTree[]>([]);
  const [flatMenuItems, setFlatMenuItems] = useState<FlatMenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [mobileSearchVisible, setMobileSearchVisible] = useState(false);

  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // 檢查是否為手機瀏覽
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 獲取選單資料
  useEffect(() => {
    const fetchMenus = async () => {
      setLoading(true);
      try {
        const result = await authService.getAllMeun();
        if (
          result.success &&
          result.data &&
          Array.isArray(result.data) &&
          result.data.length > 0
        ) {
          // 選單資料
          const menuData = result.data.flatMap((group) => group.menus);
          setMenuTrees(menuData);

          // 選單項目
          const flatItems: FlatMenuItem[] = [];

          const flattenMenu = (
            menus: MenuTree[],
            parentLabels: string[] = []
          ) => {
            menus.forEach((menu) => {
              const newParentLabels = [...parentLabels, menu.label];

              // 確保有 path 才添加到選單項目中
              if (menu.key && routes[menu.key as keyof typeof routes]) {
                flatItems.push({
                  label: menu.label,
                  value: menu.key,
                  path: routes[menu.key as keyof typeof routes],
                  parentLabels: parentLabels,
                });
              }

              if (menu.children && menu.children.length > 0) {
                flattenMenu(menu.children, newParentLabels);
              }
            });
          };

          flattenMenu(menuData);
          setFlatMenuItems(flatItems);
        }
      } catch (error) {
        console.error("獲取選單失敗:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchMenus();
  }, []);

  // 處理選單選擇
  const handleMenuSelect = (value: string) => {
    const selectedItem = flatMenuItems.find((item) => item.value === value);
    if (selectedItem && selectedItem.path) {
      router.push(selectedItem.path);
      // 手機版選擇後隱藏搜尋框
      if (isMobile) {
        setMobileSearchVisible(false);
      }
    }
  };

  // 選單標籤
  const renderMenuLabel = (item: FlatMenuItem) => {
    if (item.parentLabels.length === 0) {
      return <span>{item.label}</span>;
    }

    return (
      <Space size={4}>
        <span style={{ color: "#999", fontSize: "12px" }}>
          {item.parentLabels.join(" > ")} &gt;
        </span>
        <span style={{ fontSize: "16px", fontWeight: "bold", color: "#000" }}>
          {item.label}
        </span>
      </Space>
    );
  };

  // 處理登出
  const handleLogout = () => {
    try {
      logout();
      api.success({
        message: "登出成功",
        placement: "topRight",
        duration: 3,
      });
    } catch {
      api.error({
        message: "登出失敗",
        description: "請稍後再試",
        placement: "topRight",
        duration: 3,
      });
    }
  };

  // 通知選單項目
  const notificationItems: MenuProps["items"] = [
    {
      key: "1",
      label: "系統通知 (5)",
      onClick: () => router.push(`${routes.notifications}?tab=system`),
    },
    {
      key: "2",
      label: "個人通知 (2)",
      onClick: () => router.push(`${routes.notifications}?tab=personal`),
    },
    {
      type: "divider",
    },
    {
      key: "3",
      label: "查看所有通知",
      onClick: () => router.push(`${routes.notifications}?tab=all`),
    },
  ];

  // 訊息選單項目
  const messageItems: MenuProps["items"] = [
    {
      key: "1",
      label: "未讀訊息 (3)",
      onClick: () => router.push(`${routes.messages}?tab=unread`),
    },
    {
      key: "2",
      label: "已讀訊息",
      onClick: () => router.push(`${routes.messages}?tab=read`),
    },
    {
      type: "divider",
    },
    {
      key: "3",
      label: "查看所有訊息",
      onClick: () => router.push(`${routes.messages}?tab=all`),
    },
  ];

  // 用戶選單項目
  const userItems: MenuProps["items"] = [
    {
      key: "1",
      icon: <UserOutlined />,
      label: "個人資料",
      onClick: () => router.push(routes.profile),
    },
    {
      key: "2",
      icon: <SettingOutlined />,
      label: "修改密碼",
      onClick: () => router.push(routes.settings),
    },
    {
      type: "divider",
    },
    {
      key: "3",
      icon: <LogoutOutlined />,
      label: "登出",
      danger: true,
      onClick: handleLogout,
    },
  ];

  // 手機版搜尋框
  const renderMobileSearch = () => {
    if (!mobileSearchVisible) return null;

    return (
      <div
        style={{
          position: "absolute",
          top: 64,
          left: 0,
          width: "100%",
          padding: "12px 16px",
          background: colorBgContainer,
          zIndex: 1000,
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
        }}
      >
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="快速查詢功能"
          optionFilterProp="label"
          onChange={handleMenuSelect}
          loading={loading}
          notFoundContent={loading ? <Spin size="small" /> : "沒有找到相關選單"}
          filterOption={(input, option) => {
            const item = flatMenuItems.find(
              (menu) => menu.value === option?.value
            );
            if (!item) return false;
            return (
              item.label.toLowerCase().includes(input.toLowerCase()) ||
              item.parentLabels.some((label) =>
                label.toLowerCase().includes(input.toLowerCase())
              )
            );
          }}
        >
          {flatMenuItems.map((item) => (
            <Option
              key={item.value}
              value={item.value}
              label={`${item.parentLabels.join(" > ")} > ${item.label}`}
            >
              {renderMenuLabel(item)}
            </Option>
          ))}
        </Select>
      </div>
    );
  };

  return (
    <>
      <AntHeader
        style={{
          padding: "0 24px",
          background: colorBgContainer,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.03)",
          height: 64,
          position: "relative",
          zIndex: 1000,
        }}
      >
        {contextHolder}
        <div style={{ fontSize: 18, color: "#666" }}>
          {isMobile && <Logo showText={true} imageSize={32} />}
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: isMobile ? 8 : 16,
          }}
        >
          {isMobile ? (
            <Button
              type="text"
              icon={<CompassOutlined />}
              onClick={() => setMobileSearchVisible(!mobileSearchVisible)}
            />
          ) : (
            <Select
              showSearch
              style={{ width: 450 }}
              placeholder={
                <Space>
                  <CompassOutlined />
                  快速查詢功能
                </Space>
              }
              optionFilterProp="label"
              onChange={handleMenuSelect}
              loading={loading}
              notFoundContent={
                loading ? <Spin size="small" /> : "沒有找到相關選單"
              }
              filterOption={(input, option) => {
                const item = flatMenuItems.find(
                  (menu) => menu.value === option?.value
                );
                if (!item) return false;
                return (
                  item.label.toLowerCase().includes(input.toLowerCase()) ||
                  item.parentLabels.some((label) =>
                    label.toLowerCase().includes(input.toLowerCase())
                  )
                );
              }}
              dropdownRender={(menu) => (
                <div>
                  {loading ? (
                    <div style={{ padding: "10px", textAlign: "center" }}>
                      <Spin>
                        <div style={{ padding: "10px" }}>載入中...</div>
                      </Spin>
                    </div>
                  ) : (
                    menu
                  )}
                </div>
              )}
            >
              {flatMenuItems.map((item) => (
                <Option
                  key={item.value}
                  value={item.value}
                  label={`${item.parentLabels.join(" > ")} > ${item.label}`}
                >
                  {renderMenuLabel(item)}
                </Option>
              ))}
            </Select>
          )}

          <Dropdown
            menu={{ items: notificationItems }}
            placement="bottomRight"
            arrow
            trigger={["click"]}
          >
            <Badge count={5} size="small">
              <Button type="text" icon={<BellOutlined />} />
            </Badge>
          </Dropdown>
          <Dropdown
            menu={{ items: messageItems }}
            placement="bottomRight"
            arrow
            trigger={["click"]}
          >
            <Badge count={3} size="small">
              <Button type="text" icon={<MessageOutlined />} />
            </Badge>
          </Dropdown>
          <Dropdown
            menu={{ items: userItems }}
            placement="bottomRight"
            arrow
            trigger={["click"]}
          >
            <Button type="text" icon={<UserOutlined />} />
          </Dropdown>
        </div>
      </AntHeader>
      {renderMobileSearch()}
    </>
  );
};

export default Header;
