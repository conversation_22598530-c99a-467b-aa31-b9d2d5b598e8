using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Controllers.Pms
{
    /// <summary>
    /// 財產位置變動單控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AssetLocationTransferController : ControllerBase
    {
        private readonly IAssetLocationTransferService _transferService;
        private readonly ICurrentUserService _currentUserService;

        /// <summary>
        /// 初始化財產位置變動單控制器
        /// </summary>
        /// <param name="transferService">財產位置變動單服務</param>
        /// <param name="currentUserService">當前使用者服務</param>
        public AssetLocationTransferController(
            IAssetLocationTransferService transferService,
            ICurrentUserService currentUserService)
        {
            _transferService = transferService;
            _currentUserService = currentUserService;
        }

        /// <summary>
        /// 取得財產位置變動單列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<AssetLocationTransferDTO>>> GetTransfers(
            [FromQuery] string? searchTerm,
            [FromQuery] string? approvalStatus,
            [FromQuery] string? executionStatus,
            [FromQuery] long? startDate,
            [FromQuery] long? endDate)
        {
            try
            {
                var result = await _transferService.GetTransfersAsync(
                    searchTerm, approvalStatus, executionStatus, startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 根據ID取得財產位置變動單詳細資料
        /// </summary>
        [HttpGet("{TransferNo}")]
        public async Task<ActionResult<AssetLocationTransferWithDetailsDTO>> GetTransferById(string TransferNo)
        {
            try
            {
                var result = await _transferService.GetTransferByTransferNoAsync(TransferNo);
                if (result == null)
                {
                    return NotFound(new { message = "找不到指定的財產位置變動單" });
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 新增財產位置變動單
        /// </summary>
        [HttpPost]
        public async Task<ActionResult> CreateTransfer([FromBody] AssetLocationTransferWithDetailsDTO data)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentUserService.UserId))
                {
                    return Unauthorized(new { message = "無法取得目前使用者資訊" });
                }

                data.Transfer.CreateUserId = _currentUserService.UserId;
                data.Transfer.ApplicantId = _currentUserService.UserId;

                var result = await _transferService.AddTransferAsync(data);
                if (result.success)
                {
                    return Ok(new { message = result.message, transferNo = result.transferNo });
                }
                else
                {
                    return BadRequest(new { message = result.message });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新財產位置變動單
        /// </summary>
        [HttpPost("{transferNo}")]
        public async Task<ActionResult> UpdateTransfer(string transferNo, [FromBody] AssetLocationTransferWithDetailsDTO data)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentUserService.UserId))
                {
                    return Unauthorized(new { message = "無法取得目前使用者資訊" });
                }

                data.Transfer.TransferNo = transferNo;
                data.Transfer.UpdateUserId = _currentUserService.UserId;

                var result = await _transferService.UpdateTransferAsync(data);
                if (result.success)
                {
                    return Ok(new { message = result.message });
                }
                else
                {
                    return BadRequest(new { message = result.message });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除財產位置變動單
        /// </summary>
        [HttpPost("{transferNo}/delete")]
        public async Task<ActionResult> DeleteTransfer(string transferNo)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentUserService.UserId))
                {
                    return Unauthorized(new { message = "無法取得目前使用者資訊" });
                }

                var result = await _transferService.DeleteTransferAsync(transferNo, _currentUserService.UserId);
                if (result.success)
                {
                    return Ok(new { message = result.message });
                }
                else
                {
                    return BadRequest(new { message = result.message });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 審核財產位置變動單
        /// </summary>
        [HttpPost("{transferNo}/approve")]
        public async Task<ActionResult> ApproveTransfer(string transferNo, [FromBody] AssetLocationTransferApprovalDTO approvalData)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentUserService.UserId))
                {
                    return Unauthorized(new { message = "無法取得目前使用者資訊" });
                }

                approvalData.TransferNo = transferNo;
                approvalData.ApproverId = _currentUserService.UserId;

                var result = await _transferService.ApproveTransferAsync(approvalData);
                if (result.success)
                {
                    return Ok(new { message = result.message });
                }
                else
                {
                    return BadRequest(new { message = result.message });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 執行財產位置變動
        /// </summary>
        [HttpPost("{transferNo}/execute")]
        public async Task<ActionResult> ExecuteTransfer(string transferNo, [FromBody] AssetLocationTransferExecutionDTO executionData)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentUserService.UserId))
                {
                    return Unauthorized(new { message = "無法取得目前使用者資訊" });
                }

                executionData.TransferNo = transferNo;
                executionData.ExecutorId = _currentUserService.UserId;

                var result = await _transferService.ExecuteTransferAsync(executionData);
                if (result.success)
                {
                    return Ok(new { message = result.message });
                }
                else
                {
                    return BadRequest(new { message = result.message });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得財產目前的位置資訊
        /// </summary>
        [HttpGet("asset/{assetNo}/current-location")]
        public async Task<ActionResult<AssetLocationTransferDetailDTO>> GetAssetCurrentLocation(string assetNo)
        {
            try
            {
                var result = await _transferService.GetAssetCurrentLocationAsync(assetNo);
                if (result == null)
                {
                    return NotFound(new { message = "找不到指定的財產" });
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 驗證財產是否可以進行位置變動
        /// </summary>
        [HttpGet("asset/{assetNo}/validate")]
        public async Task<ActionResult> ValidateAssetForTransfer(string assetNo)
        {
            try
            {
                var result = await _transferService.ValidateAssetForTransferAsync(assetNo);
                return Ok(new { isValid = result.isValid, message = result.message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 產生變動單號（預覽）
        /// </summary>
        [HttpGet("generate-transfer-no")]
        public async Task<ActionResult<string>> GenerateTransferNo([FromQuery] long transferDate)
        {
            try
            {
                var transferNo = await _transferService.GenerateTransferNoAsync(transferDate);
                return Ok(new { transferNo });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}