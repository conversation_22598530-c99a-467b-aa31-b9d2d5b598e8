﻿using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IEnterpriseGroupsService
    {
        /// <summary>
        /// 取得企業群組列表
        /// </summary>
        /// <returns>企業群組列表</returns>
        Task<List<EnterpriseGroupsDTO>> GetEnterpriseGroupsListAsync();

        /// <summary>
        /// 取得企業群組詳細資料
        /// </summary>
        /// <param name="_uid">企業群組唯一識別碼</param>
        /// <returns>企業群組詳細資料</returns>
        Task<string> GetEnterpriseGroupsDetailAsync(string _uid);

        /// <summary>
        /// 新增企業群組
        /// </summary>
        /// <param name="_data">企業群組資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddEnterpriseGroupsAsync(EnterpriseGroupsDTO _data);

        /// <summary>
        /// 編輯企業群組
        /// </summary>
        /// <param name="_data">企業群組資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditEnterpriseGroupsAsync(EnterpriseGroupsDTO _data);

        /// <summary>
        /// 刪除企業群組
        /// </summary>
        /// <param name="_data">企業群組資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteEnterpriseGroupsAsync(EnterpriseGroupsDTO _data);
    }
}
