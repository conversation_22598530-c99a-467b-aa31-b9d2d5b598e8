import { Space, Select, Input } from 'antd';

type FilterTypeSelectProps = {
    filterType: string;
    filterValue: string;
    onFilterChange: (field: 'filterType' | 'filterValue', value: string) => void;
    onSearch: () => void;
};

const FilterTypeSelect: React.FC<FilterTypeSelectProps> = ({
    filterType,
    filterValue,
    onFilterChange,
    onSearch
}) => {
    return (
        <Space className="mb-6" wrap>
            <Select
                value={filterType}
                onChange={(value) => onFilterChange('filterType', value)}
                style={{ width: 120 }}
            >
                <Select.Option value="EmpNo">員工編號</Select.Option>
                <Select.Option value="IdNo">身分證字號</Select.Option>
                <Select.Option value="Name">姓名</Select.Option>
            </Select>

            <Input.Search
                value={filterValue}
                onChange={(e) => onFilterChange('filterValue', e.target.value)}
                onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                        onSearch();
                    }
                }}
                onSearch={onSearch}
                placeholder="請輸入關鍵字"
                enterButton
            />
        </Space>
    );
};

export default FilterTypeSelect;
