using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface ICityService
    {
        /// <summary>
        /// 取得縣市資料
        /// </summary>
        /// <param name="_cityId">縣市編號</param>
        /// <returns>縣市資料列表</returns>
        Task<List<CityDTO>> GetCityAsync(string _cityId = "");

        /// <summary>
        /// 新增縣市
        /// </summary>
        /// <param name="_data">縣市資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddCityAsync(CityDTO _data);

        /// <summary>
        /// 編輯縣市
        /// </summary>
        /// <param name="_data">縣市資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditCityAsync(CityDTO _data);

        /// <summary>
        /// 刪除縣市
        /// </summary>
        /// <param name="_data">縣市資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteCityAsync(CityDTO _data);

        /// <summary>
        /// 取得縣市詳細資料
        /// </summary>
        /// <param name="_cityId">縣市編號</param>
        /// <returns>縣市詳細資料</returns>
        Task<CityDTO> GetCityDetailAsync(string _cityId);
    }
}