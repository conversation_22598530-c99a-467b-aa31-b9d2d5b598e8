using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IExaminationService
    {
        /// <summary>
        /// 取得所有考試資料列表
        /// </summary>
        /// <param name="_userid">使用者編號</param>
        /// <returns>考試資料列表</returns>
        Task<List<ExaminationDTO>> GetExaminationListAsync(string _userid);

        /// <summary>
        /// 取得考試資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>考試資料明細</returns>
        Task<ExaminationDTO> GetExaminationDetailAsync(string _uid);

        /// <summary>
        /// 新增考試資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddExaminationAsync(ExaminationDTO _data);

        /// <summary>
        /// 編輯考試資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditExaminationAsync(ExaminationDTO _data);

        /// <summary>
        /// 刪除考試資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteExaminationAsync(string _uid);
    }
}
