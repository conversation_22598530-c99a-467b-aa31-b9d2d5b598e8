using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Ims;
/// <summary> 商業夥伴服務 </summary>
public class PartnerService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : IPartnerService
{
    /// <summary> 商業夥伴列表取得 </summary>
    public async Task<List<PartnerDTO>> GetAllAsync()
    {
        var partners = await _context.Ims_Partner
            .Include(p => p.IndividualDetail)
            .Include(p => p.OrganizationDetail)
            .Include(p => p.CustomerDetail)
            .Include(p => p.SupplierDetail)
            .Include(p => p.Addresses)
            .Include(p => p.PartnerContacts)
                .ThenInclude(pc => pc.Contact)
            .Include(p => p.PartnerContacts)
                .ThenInclude(pc => pc.ContactRole)
            .ToListAsync();

        return partners.Select(p => EntityToDto(p)).ToList();
    }

    /// <summary> 商業夥伴取得 </summary>
    public async Task<PartnerDTO> GetAsync(Guid PartnerID)
    {
        var partner = await _context.Ims_Partner
            .Include(p => p.IndividualDetail)
            .Include(p => p.OrganizationDetail)
            .Include(p => p.CustomerDetail)
            .Include(p => p.SupplierDetail)
            .Include(p => p.Addresses)
            .Include(p => p.PartnerContacts)
                .ThenInclude(pc => pc.Contact)
            .Include(p => p.PartnerContacts)
                .ThenInclude(pc => pc.ContactRole)
            .FirstOrDefaultAsync(p => p.PartnerID == PartnerID);


        return EntityToDto(partner);
    }

    /// <summary> 商業夥伴新增 </summary>
    public async Task<(bool, string)> AddAsync(PartnerDTO dto)
    {
        var entity = new Partner
        {
            PartnerID = Guid.NewGuid(),
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId
        };
        if (dto.IndividualDetail != null && dto.OrganizationDetail != null)
        {
            throw new Exception($"商業夥伴不得同時為法人及自然人!");
        }
        MapDtoToEntity(dto, entity);

        _context.Ims_Partner.Add(entity);
        await _context.SaveChangesAsync();

        return (true, $"商業夥伴 {entity.PartnerID} 新增成功!");
    }

    /// <summary> 商業夥伴更新 </summary>
    public async Task<(bool, string)> UpdateAsync(PartnerDTO dto)
    {
        var entity = await _context.Ims_Partner
            .Include(p => p.IndividualDetail)
            .Include(p => p.OrganizationDetail)
            .Include(p => p.CustomerDetail)
            .Include(p => p.SupplierDetail)
            .FirstOrDefaultAsync(p => p.PartnerID == dto.PartnerID);

        ValidateDTO(dto);
        MapDtoToEntity(dto, entity);

        entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.UpdateUserId = _currentUserService.UserId;

        var result = await _context.SaveChangesAsync();
        if (result == 0)
        {
            throw new Exception("更新失敗，沒有任何筆數被更新");
        }

        return (true, $"商業夥伴 {entity.PartnerID} 更新成功!");
    }

    /// <summary> 商業夥伴刪除 </summary>

    public async Task<(bool, string)> DeleteAsync(Guid PartnerID)
    {
        var enitity = await _context.Ims_Partner.FirstOrDefaultAsync(p => p.PartnerID == PartnerID);

        if (enitity == null)
        {
            throw new Exception($"商業夥伴 {PartnerID} 不存在");
        }

        // 軟刪除
        enitity.IsDeleted = true;
        enitity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        enitity.DeleteUserId = _currentUserService.UserId;

        _context.Ims_Partner.Update(enitity);
        await _context.SaveChangesAsync();
        return (true, $"商業夥伴 {PartnerID} 更新成功!");
    }

    private PartnerDTO EntityToDto(Partner entity)
    {
        if (entity == null) return null;

        var dto = new PartnerDTO
        {
            PartnerID = entity.PartnerID,
            // 複製 ModelBaseEntity 的屬性
            CreateTime = entity.CreateTime,
            CreateUserId = entity.CreateUserId,
            UpdateTime = entity.UpdateTime,
            UpdateUserId = entity.UpdateUserId,
            DeleteTime = entity.DeleteTime,
            DeleteUserId = entity.DeleteUserId,
            IsDeleted = entity.IsDeleted,

            // 映射一對一關聯的詳細資料
            IndividualDetail = entity.IndividualDetail != null ? new IndividualDetailDTO
            {
                PartnerID = entity.IndividualDetail.PartnerID,
                LastName = entity.IndividualDetail.LastName,
                FirstName = entity.IndividualDetail.FirstName,
                IdentificationNumber = entity.IndividualDetail.IdentificationNumber,
                BirthDate = entity.IndividualDetail.BirthDate
            } : null,
            OrganizationDetail = entity.OrganizationDetail != null ? new OrganizationDetailDTO
            {
                PartnerID = entity.OrganizationDetail.PartnerID,
                CompanyName = entity.OrganizationDetail.CompanyName,
                BussinessID = entity.OrganizationDetail.BussinessID,
                TaxID = entity.OrganizationDetail.TaxID,
                ResponsiblePerson = entity.OrganizationDetail.ResponsiblePerson
            } : null,
            CustomerDetail = entity.CustomerDetail != null ? new CustomerDetailDTO
            {
                PartnerID = entity.CustomerDetail.PartnerID,
                CustomerCode = entity.CustomerDetail.CustomerCode,
                CustomerCategoryID = entity.CustomerDetail.CustomerCategoryID,
                SettlementDay = entity.CustomerDetail.SettlementDay,
                // 注意：CustomerCategory 導航屬性可能需要額外映射，取決於需求
            } : null,
            SupplierDetail = entity.SupplierDetail != null ? new SupplierDetailDTO
            {
                PartnerID = entity.SupplierDetail.PartnerID,
                SupplierCode = entity.SupplierDetail.SupplierCode,
                SupplierCategoryID = entity.SupplierDetail.SupplierCategoryID,
                SettlementDay = entity.SupplierDetail.SettlementDay,
                // 注意：SupplierCategory 導航屬性可能需要額外映射，取決於需求
            } : null,
            PartnerContacts = entity.PartnerContacts?.Select(pc => new PartnerContactDTO
            {
                PartnerID = pc.PartnerID,
                ContactID = pc.ContactID,
                ContactRoleID = pc.ContactRoleID,
                IsPrimary = pc.IsPrimary
            }).ToList() ?? new List<PartnerContactDTO>(),
            Addresses = entity.Addresses?.Select(a => new PartnerAddressDTO
            {
                PartnerAddressID = a.PartnerAddressID,
                PartnerID = a.PartnerID,
                Address = a.Address,
                City = a.City,
                District = a.District,
                PostalCode = a.PostalCode,
                Country = a.Country,
                IsPrimary = a.IsPrimary
            }).ToList() ?? new List<PartnerAddressDTO>()
        };
        return dto;
    }

    /// <summary> 將 PartnerDto 映射到現有的 Partner Entity (用於 Update) </summary>
    private void MapDtoToEntity(PartnerDTO dto, Partner entity)
    {
        if (dto == null || entity == null) return;

        // 映射一對一關聯的詳細資料 (如果存在且需要更新)
        if (dto.IndividualDetail != null)
        {
            if (entity.IndividualDetail == null)
            {
                entity.IndividualDetail = new IndividualDetail { PartnerID = entity.PartnerID };
            }
            entity.IndividualDetail.LastName = dto.IndividualDetail.LastName;
            entity.IndividualDetail.FirstName = dto.IndividualDetail.FirstName;
            entity.IndividualDetail.IdentificationNumber = dto.IndividualDetail.IdentificationNumber;
            entity.IndividualDetail.BirthDate = dto.IndividualDetail.BirthDate;
        }
        else if (entity.IndividualDetail != null) // 如果 DTO 中沒有但 Entity 中有，則移除
        {
            _context.Ims_IndividualDetail.Remove(entity.IndividualDetail);
            entity.IndividualDetail = null;
        }

        if (dto.OrganizationDetail != null)
        {
            if (entity.OrganizationDetail == null)
            {
                entity.OrganizationDetail = new OrganizationDetail { PartnerID = entity.PartnerID };
            }
            entity.OrganizationDetail.CompanyName = dto.OrganizationDetail.CompanyName;
            entity.OrganizationDetail.BussinessID = dto.OrganizationDetail.BussinessID;
            entity.OrganizationDetail.TaxID = dto.OrganizationDetail.TaxID;
            entity.OrganizationDetail.ResponsiblePerson = dto.OrganizationDetail.ResponsiblePerson;
        }
        else if (entity.OrganizationDetail != null)
        {
            _context.Ims_OrganizationDetail.Remove(entity.OrganizationDetail);
            entity.OrganizationDetail = null;
        }

        // Handle CustomerDetail
        if (dto.CustomerDetail != null)
        {
            if (entity.CustomerDetail == null)
            {
                entity.CustomerDetail = new CustomerDetail { PartnerID = entity.PartnerID };
            }
            entity.CustomerDetail.CustomerCode = dto.CustomerDetail.CustomerCode;
            entity.CustomerDetail.CustomerCategoryID = dto.CustomerDetail.CustomerCategoryID;
            entity.CustomerDetail.SettlementDay = dto.CustomerDetail.SettlementDay;
        }
        else if (entity.CustomerDetail != null)
        {
            _context.Ims_CustomerDetail.Remove(entity.CustomerDetail);
            entity.CustomerDetail = null;
        }

        // Handle SupplierDetail
        if (dto.SupplierDetail != null)
        {
            if (entity.SupplierDetail == null)
            {
                entity.SupplierDetail = new SupplierDetail { PartnerID = entity.PartnerID };
            }
            entity.SupplierDetail.SupplierCode = dto.SupplierDetail.SupplierCode;
            entity.SupplierDetail.SupplierCategoryID = dto.SupplierDetail.SupplierCategoryID;
            entity.SupplierDetail.SettlementDay = dto.SupplierDetail.SettlementDay;
        }
        else if (entity.SupplierDetail != null)
        {
            _context.Ims_SupplierDetail.Remove(entity.SupplierDetail);
            entity.SupplierDetail = null;
        }
    }

    private void ValidateDTO(PartnerDTO dto)
    {
        if (dto == null)
        {
            throw new ArgumentNullException(nameof(dto), "商業夥伴資料不能為空");
        }

        // 驗證 IndividualDetail(自然人) 或 OrganizationDetail(法人) 資料
        if (dto.IndividualDetail != null)
        {
            if (string.IsNullOrWhiteSpace(dto.IndividualDetail.LastName))
            {
                throw new ArgumentException("名字不能為空", nameof(dto.IndividualDetail.LastName));
            }

            if (string.IsNullOrWhiteSpace(dto.IndividualDetail.FirstName))
            {
                throw new ArgumentException("姓氏不能為空", nameof(dto.IndividualDetail.FirstName));
            }

            // if (string.IsNullOrWhiteSpace(dto.IndividualDetail.IdentificationNumber))
            // {
            //     throw new ArgumentException("自然人身分證字號不能為空", nameof(dto.IndividualDetail.IdentificationNumber));
            // }

            // if (dto.IndividualDetail.BirthDate == null)
            // {
            //     throw new ArgumentException("自然人生日不能為空", nameof(dto.IndividualDetail.BirthDate));
            // }
        }
        else if (dto.OrganizationDetail != null)
        {
            if (string.IsNullOrWhiteSpace(dto.OrganizationDetail.CompanyName))
            {
                throw new ArgumentException("法人名稱不能為空", nameof(dto.OrganizationDetail.CompanyName));
            }

            // if (string.IsNullOrWhiteSpace(dto.OrganizationDetail.BussinessID))
            // {
            //     throw new ArgumentException("法人統一編號不能為空", nameof(dto.OrganizationDetail.BussinessID));
            // }

            // if (string.IsNullOrWhiteSpace(dto.OrganizationDetail.TaxID))
            // {
            //     throw new ArgumentException("法人公司稅務識別號不能為空", nameof(dto.OrganizationDetail.TaxID));
            // }

            // if (string.IsNullOrWhiteSpace(dto.OrganizationDetail.ResponsiblePerson))
            // {
            //     throw new ArgumentException("法人公司負責人不能為空", nameof(dto.OrganizationDetail.ResponsiblePerson));
            // }
        }

        if (dto.CustomerDetail != null)
        {
            if (string.IsNullOrWhiteSpace(dto.CustomerDetail.CustomerCode))
            {
                throw new ArgumentException("客戶代碼不能為空", nameof(dto.CustomerDetail.CustomerCode));
            }

            if (dto.CustomerDetail.CustomerCategoryID == null)
            {
                throw new ArgumentException("客戶分類不能為空", nameof(dto.CustomerDetail.CustomerCategoryID));
            }

            if (dto.CustomerDetail.SettlementDay == null)
            {
                throw new ArgumentException("客戶結算日不能為空", nameof(dto.CustomerDetail.SettlementDay));
            }
        }

        if (dto.SupplierDetail != null)
        {
            if (string.IsNullOrWhiteSpace(dto.SupplierDetail.SupplierCode))
            {
                throw new ArgumentException("供應商代碼不能為空", nameof(dto.SupplierDetail.SupplierCode));
            }

            if (dto.SupplierDetail.SupplierCategoryID == null)
            {
                throw new ArgumentException("供應商分類不能為空", nameof(dto.SupplierDetail.SupplierCategoryID));
            }

            if (dto.SupplierDetail.SettlementDay == null)
            {
                throw new ArgumentException("供應商結算日不能為空", nameof(dto.SupplierDetail.SettlementDay));
            }
        }
    }
}