using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    /// <summary>
    /// 廠商修繕作業控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("廠商修繕作業管理")]
    public class VendorMaintenanceController : ControllerBase
    {
        private readonly IVendorMaintenanceService _vendorMaintenanceService;

        public VendorMaintenanceController(IVendorMaintenanceService vendorMaintenanceService)
        {
            _vendorMaintenanceService = vendorMaintenanceService;
        }

        /// <summary>
        /// 取得修繕申請列表
        /// </summary>
        /// <param name="status">狀態篩選</param>
        /// <param name="maintenanceType">修繕類型篩選</param>
        /// <param name="urgencyLevel">緊急程度篩選</param>
        /// <param name="startDate">開始日期 (Unix 時間戳)</param>
        /// <param name="endDate">結束日期 (Unix 時間戳)</param>
        /// <returns>修繕申請列表</returns>
        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得修繕申請列表", Description = "根據條件篩選取得修繕申請列表")]
        [SwaggerResponse(200, "成功取得修繕申請列表", typeof(List<VendorMaintenanceDTO>))]
        public async Task<ActionResult<object>> GetVendorMaintenanceList(
            [FromQuery] string? status = null,
            [FromQuery] string? maintenanceType = null,
            [FromQuery] string? urgencyLevel = null,
            [FromQuery] long? startDate = null,
            [FromQuery] long? endDate = null)
        {
            try
            {
                var result = await _vendorMaintenanceService.GetVendorMaintenanceListAsync(status, maintenanceType, urgencyLevel, startDate, endDate);
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 根據修繕單號取得詳細資料
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <returns>修繕申請詳細資料</returns>
        [HttpGet]
        [Route("{maintenanceNumber}")]
        [SwaggerOperation(Summary = "取得修繕申請詳細資料", Description = "根據修繕單號取得修繕申請的詳細資料")]
        [SwaggerResponse(200, "成功取得修繕申請詳細資料", typeof(VendorMaintenanceDTO))]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> GetVendorMaintenanceDetail([Required] string maintenanceNumber)
        {
            try
            {
                var result = await _vendorMaintenanceService.GetVendorMaintenanceByNumberAsync(maintenanceNumber);
                if (result == null)
                {
                    return NotFound(new { success = false, message = "修繕申請不存在" });
                }
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 新增修繕申請
        /// </summary>
        /// <param name="vendorMaintenanceDto">修繕申請資料</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增修繕申請", Description = "新增一筆修繕申請")]
        [SwaggerResponse(200, "修繕申請新增成功")]
        [SwaggerResponse(400, "新增失敗")]
        public async Task<ActionResult<object>> CreateVendorMaintenance([FromBody] VendorMaintenanceDTO vendorMaintenanceDto)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.CreateVendorMaintenanceAsync(vendorMaintenanceDto);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 修改修繕申請
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="vendorMaintenanceDto">修繕申請資料</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "修改修繕申請", Description = "修改指定的修繕申請")]
        [SwaggerResponse(200, "修繕申請修改成功")]
        [SwaggerResponse(400, "修改失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> UpdateVendorMaintenance([Required] string maintenanceNumber, [FromBody] VendorMaintenanceDTO vendorMaintenanceDto)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.UpdateVendorMaintenanceAsync(maintenanceNumber, vendorMaintenanceDto);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除修繕申請
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除修繕申請", Description = "刪除指定的修繕申請")]
        [SwaggerResponse(200, "修繕申請刪除成功")]
        [SwaggerResponse(400, "刪除失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> DeleteVendorMaintenance([Required] string maintenanceNumber)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.DeleteVendorMaintenanceAsync(maintenanceNumber);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 審核修繕申請
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="approvalRequest">審核請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/approve")]
        [SwaggerOperation(Summary = "審核修繕申請", Description = "審核指定的修繕申請")]
        [SwaggerResponse(200, "審核成功")]
        [SwaggerResponse(400, "審核失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> ApproveVendorMaintenance([Required] string maintenanceNumber, [FromBody] VendorMaintenanceApprovalRequestDto approvalRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.ApproveVendorMaintenanceAsync(
                    maintenanceNumber, approvalRequest.IsApproved, approvalRequest.Reason,
                    approvalRequest.ApproverId, approvalRequest.ApproverName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 指派廠商
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="assignRequest">指派請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/assign-vendor")]
        [SwaggerOperation(Summary = "指派廠商", Description = "為修繕申請指派廠商")]
        [SwaggerResponse(200, "廠商指派成功")]
        [SwaggerResponse(400, "指派失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> AssignVendor([Required] string maintenanceNumber, [FromBody] VendorAssignRequestDto assignRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.AssignVendorAsync(
                    maintenanceNumber, assignRequest.VendorId, assignRequest.VendorName,
                    assignRequest.VendorContact, assignRequest.VendorPhone,
                    assignRequest.ScheduledStartDate, assignRequest.ScheduledEndDate,
                    assignRequest.OperatorId, assignRequest.OperatorName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 開始施工
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="startRequest">開始施工請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/start")]
        [SwaggerOperation(Summary = "開始施工", Description = "開始修繕施工")]
        [SwaggerResponse(200, "施工已開始")]
        [SwaggerResponse(400, "操作失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> StartMaintenance([Required] string maintenanceNumber, [FromBody] StartMaintenanceRequestDto startRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.StartMaintenanceAsync(
                    maintenanceNumber, startRequest.ActualStartDate, startRequest.OperatorId, startRequest.OperatorName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 完成修繕
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="completeRequest">完成修繕請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/complete")]
        [SwaggerOperation(Summary = "完成修繕", Description = "完成修繕施工")]
        [SwaggerResponse(200, "修繕已完成")]
        [SwaggerResponse(400, "操作失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> CompleteMaintenance([Required] string maintenanceNumber, [FromBody] CompleteMaintenanceRequestDto completeRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.CompleteMaintenanceAsync(
                    maintenanceNumber, completeRequest.ActualEndDate, completeRequest.ActualCost,
                    completeRequest.MaintenanceResult, completeRequest.OperatorId, completeRequest.OperatorName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 驗收修繕
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="inspectRequest">驗收請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/inspect")]
        [SwaggerOperation(Summary = "驗收修繕", Description = "驗收修繕結果")]
        [SwaggerResponse(200, "驗收完成")]
        [SwaggerResponse(400, "操作失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> InspectMaintenance([Required] string maintenanceNumber, [FromBody] InspectMaintenanceRequestDto inspectRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.InspectMaintenanceAsync(
                    maintenanceNumber, inspectRequest.InspectionResult, inspectRequest.InspectionNotes,
                    inspectRequest.InspectorId, inspectRequest.InspectorName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 結案
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="closeRequest">結案請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/close")]
        [SwaggerOperation(Summary = "結案", Description = "結案修繕申請")]
        [SwaggerResponse(200, "結案成功")]
        [SwaggerResponse(400, "操作失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> CloseMaintenance([Required] string maintenanceNumber, [FromBody] CloseMaintenanceRequestDto closeRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.CloseMaintenanceAsync(
                    maintenanceNumber, closeRequest.OperatorId, closeRequest.OperatorName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 取消修繕
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="cancelRequest">取消請求</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("{maintenanceNumber}/cancel")]
        [SwaggerOperation(Summary = "取消修繕", Description = "取消修繕申請")]
        [SwaggerResponse(200, "取消成功")]
        [SwaggerResponse(400, "操作失敗")]
        [SwaggerResponse(404, "修繕申請不存在")]
        public async Task<ActionResult<object>> CancelMaintenance([Required] string maintenanceNumber, [FromBody] CancelMaintenanceRequestDto cancelRequest)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.CancelMaintenanceAsync(
                    maintenanceNumber, cancelRequest.Reason, cancelRequest.OperatorId, cancelRequest.OperatorName);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 批次處理修繕申請
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>操作結果</returns>
        [HttpPost]
        [Route("batch")]
        [SwaggerOperation(Summary = "批次處理修繕申請", Description = "批次審核、取消修繕申請")]
        [SwaggerResponse(200, "批次處理完成")]
        [SwaggerResponse(400, "操作失敗")]
        public async Task<ActionResult<object>> BatchProcess([FromBody] VendorMaintenanceBatchDTO batchDto)
        {
            try
            {
                var (success, message) = await _vendorMaintenanceService.BatchProcessAsync(batchDto);
                return Ok(new { success, message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 取得修繕統計資料
        /// </summary>
        /// <param name="startDate">開始日期 (Unix 時間戳)</param>
        /// <param name="endDate">結束日期 (Unix 時間戳)</param>
        /// <returns>統計資料</returns>
        [HttpGet]
        [Route("statistics")]
        [SwaggerOperation(Summary = "取得修繕統計資料", Description = "取得修繕申請的統計資料")]
        [SwaggerResponse(200, "成功取得統計資料", typeof(VendorMaintenanceStatisticsDTO))]
        public async Task<ActionResult<object>> GetStatistics([FromQuery] long? startDate = null, [FromQuery] long? endDate = null)
        {
            try
            {
                var result = await _vendorMaintenanceService.GetStatisticsAsync(startDate, endDate);
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 檢查逾期修繕案件
        /// </summary>
        /// <returns>逾期案件列表</returns>
        [HttpGet]
        [Route("overdue")]
        [SwaggerOperation(Summary = "檢查逾期修繕案件", Description = "取得逾期未完成的修繕案件")]
        [SwaggerResponse(200, "成功取得逾期案件", typeof(List<VendorMaintenanceDTO>))]
        public async Task<ActionResult<object>> GetOverdueMaintenance()
        {
            try
            {
                var result = await _vendorMaintenanceService.GetOverdueMaintenanceAsync();
                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }

    #region 輔助 DTO 類別

    /// <summary>
    /// 廠商修繕審核請求 DTO
    /// </summary>
    public class VendorMaintenanceApprovalRequestDto
    {
        /// <summary>
        /// 是否核准
        /// </summary>
        [Required]
        public bool IsApproved { get; set; }

        /// <summary>
        /// 審核意見
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 審核人員工編號
        /// </summary>
        [Required]
        public string ApproverId { get; set; } = string.Empty;

        /// <summary>
        /// 審核人姓名
        /// </summary>
        [Required]
        public string ApproverName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 廠商指派請求 DTO
    /// </summary>
    public class VendorAssignRequestDto
    {
        /// <summary>
        /// 廠商編號
        /// </summary>
        [Required]
        public string VendorId { get; set; } = string.Empty;

        /// <summary>
        /// 廠商名稱
        /// </summary>
        [Required]
        public string VendorName { get; set; } = string.Empty;

        /// <summary>
        /// 廠商聯絡人
        /// </summary>
        public string? VendorContact { get; set; }

        /// <summary>
        /// 廠商聯絡電話
        /// </summary>
        public string? VendorPhone { get; set; }

        /// <summary>
        /// 預計開始日期 (Unix 時間戳)
        /// </summary>
        public long? ScheduledStartDate { get; set; }

        /// <summary>
        /// 預計完成日期 (Unix 時間戳)
        /// </summary>
        public long? ScheduledEndDate { get; set; }

        /// <summary>
        /// 操作人員工編號
        /// </summary>
        [Required]
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人姓名
        /// </summary>
        [Required]
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 開始施工請求 DTO
    /// </summary>
    public class StartMaintenanceRequestDto
    {
        /// <summary>
        /// 實際開始日期 (Unix 時間戳)
        /// </summary>
        [Required]
        public long ActualStartDate { get; set; }

        /// <summary>
        /// 操作人員工編號
        /// </summary>
        [Required]
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人姓名
        /// </summary>
        [Required]
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 完成修繕請求 DTO
    /// </summary>
    public class CompleteMaintenanceRequestDto
    {
        /// <summary>
        /// 實際完成日期 (Unix 時間戳)
        /// </summary>
        [Required]
        public long ActualEndDate { get; set; }

        /// <summary>
        /// 實際費用
        /// </summary>
        public decimal? ActualCost { get; set; }

        /// <summary>
        /// 修繕結果
        /// </summary>
        public string? MaintenanceResult { get; set; }

        /// <summary>
        /// 操作人員工編號
        /// </summary>
        [Required]
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人姓名
        /// </summary>
        [Required]
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 驗收請求 DTO
    /// </summary>
    public class InspectMaintenanceRequestDto
    {
        /// <summary>
        /// 驗收結果
        /// </summary>
        [Required]
        public string InspectionResult { get; set; } = string.Empty;

        /// <summary>
        /// 驗收備註
        /// </summary>
        public string? InspectionNotes { get; set; }

        /// <summary>
        /// 驗收人員工編號
        /// </summary>
        [Required]
        public string InspectorId { get; set; } = string.Empty;

        /// <summary>
        /// 驗收人姓名
        /// </summary>
        [Required]
        public string InspectorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 結案請求 DTO
    /// </summary>
    public class CloseMaintenanceRequestDto
    {
        /// <summary>
        /// 操作人員工編號
        /// </summary>
        [Required]
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人姓名
        /// </summary>
        [Required]
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 取消請求 DTO
    /// </summary>
    public class CancelMaintenanceRequestDto
    {
        /// <summary>
        /// 取消原因
        /// </summary>
        [Required]
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 操作人員工編號
        /// </summary>
        [Required]
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人姓名
        /// </summary>
        [Required]
        public string OperatorName { get; set; } = string.Empty;
    }

    #endregion
}