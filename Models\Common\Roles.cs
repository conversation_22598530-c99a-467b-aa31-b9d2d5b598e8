﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    public class RolesDTO
    {
        public string RolesId { get; set; } //角色編號
        public string Name { get; set; } //角色名稱

        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態

        public ICollection<RolesPermissionsDTO> RolesPermissions { get; set; } //角色權限

        public RolesDTO()
        {
            RolesId = "";
            Name = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;

            RolesPermissions = new List<RolesPermissionsDTO>();
        }
    }

    public class Roles : ModelBaseEntity
    {
        [Key]
        [Comment("角色編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string RolesId { get; set; } //角色編號

        [Comment("角色名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } //角色名稱

        // 導航屬性
        public virtual ICollection<RolesPermissions> RolesPermissions { get; set; }

        public Roles()
        {
            RolesId = "";
            Name = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;

            RolesPermissions = new List<RolesPermissions>();
        }
    }
}