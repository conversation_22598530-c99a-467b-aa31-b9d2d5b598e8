services:
  # 前端 (fast_erp_frontend)
  fast_erp_frontend:
    container_name: fast_erp_frontend # 設定容器名稱
    image: chengi0506/fast_erp_frontend:20250305 # 使用指定的 Docker 映像檔
    build:
      context: . # 指定 Dockerfile 的路徑
      dockerfile: Dockerfile # 指定要使用的 Dockerfile
    ports:
      - 8080:80 # 將本機的 8080 端口映射到容器內的 80 端口
    depends_on:
      - fast_erp_backend # 依賴於 fast_erp_backend 服務，確保 orderservice 先啟動

  # 後端 (fast_erp_backend)
  fast_erp_backend:
    container_name: OrderService # 設定容器名稱
    image: chengi0506/orderservice:20250305 # 使用指定的 Docker 映像檔
    build:
      context: . # 指定 Dockerfile 的路徑
      dockerfile: Dockerfile # 指定要使用的 Dockerfile
    ports:
      - "8081:80" # 將本機的 8081 端口映射到容器內的 80 端口
    depends_on:
      - db # 依賴於 db 服務，確保 db 先啟動

    # 資料庫 (Microsoft SQL Server)
    db:
      container_name: store # 設定容器名稱
      image: "mcr.microsoft.com/mssql/server:2022-latest" # 使用最新版本的 SQL Server 映像檔
      ports:
        - "15000:1433" # 將本機的 15000 端口映射到容器內的 1433 (SQL Server 預設端口)
      environment:
        SA_PASSWORD: "fast!234" # 設定 SQL Server 管理員密碼 (請替換為安全的密碼)
        ACCEPT_EULA: "Y" # 接受 Microsoft 的授權條款
        MSSQL_PID: "Express" # 使用 SQL Server Express 版本
    volumes:
      - fast_erp_backend:/var/opt/mssql # 使用命名卷來持久化 SQL Server 資料

# 定義持久化 SQL Server 資料的命名卷
volumes:
  fast_erp_backend:
