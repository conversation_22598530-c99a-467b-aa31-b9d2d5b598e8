using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 財產科目
    /// </summary>
    public class AssetAccount : ModelBaseEntity
    {
        [Key]
        [Comment("財產科目流水號")]
        public Guid AssetAccountId { get; set; } // 財產科目流水號

        [Comment("財產科目編號")]
        [RegularExpression(@"^[0-9a-zA-Z]{1,10}$", ErrorMessage = "財產科目編號必須為1至10位英數字")]
        [StringLength(10, MinimumLength = 1, ErrorMessage = "財產科目編號必須為1至10位英數字")]
        [Column(TypeName = "nvarchar(10)")]
        [Required(ErrorMessage = "財產科目編號為必填欄位")]
        public string AssetAccountNo { get; set; }//財產科目編號

        [Comment("財產科目名稱")]
        [StringLength(20, MinimumLength = 1, ErrorMessage = "財產科目名稱必須為1至100位字")]
        [Required(ErrorMessage = "財產科目名稱為必填欄位")]
        public string AssetAccountName { get; set; }//財產科目名稱

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public AssetAccount()
        {
            AssetAccountId = Guid.NewGuid();
            AssetAccountNo = "";
            AssetAccountName = "";
            SortCode = 0;
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }


    public class AssetAccountDTO : ModelBaseEntityDTO
    {
        public Guid AssetAccountId { get; set; }//財產科目流水號
        public string AssetAccountNo { get; set; }//財產科目編號
        public string AssetAccountName { get; set; }//財產科目名稱
        public int SortCode { get; set; } // 排序號碼
        public string? CreateUserName { get; set; } // 新增者姓名
        public string? UpdateUserName { get; set; } // 更新者姓名
        public string? DeleteUserName { get; set; } // 刪除者姓名

        public AssetAccountDTO()
        {
            AssetAccountId = Guid.Empty;
            AssetAccountNo = "";
            AssetAccountName = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            CreateUserName = null;
            UpdateUserName = null;
            DeleteUserName = null;
            IsDeleted = false;
        }
    }
}