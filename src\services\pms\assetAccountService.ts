import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 財產科目
export interface AssetAccount {
    assetAccountId: string;
    assetAccountNo: string;
    assetAccountName: string;
    sortCode: number;
    createTime: number | null;
    createUserId: string | null;
    createUserName: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
}

// 獲取財產科目列表
export async function getAssetAccounts(): Promise<ApiResponse<AssetAccount[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssetAccounts, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產科目列表失敗",
            data: []
        };
    }
}

// 新增財產科目信息
export async function createAssetAccount(data: Partial<AssetAccount>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAssetAccount, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增財產科目信息失敗",
        };
    }
}

// 更新財產科目信息
export async function updateAssetAccount(data: Partial<AssetAccount>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAssetAccount, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新財產科目信息失敗",
        };
    }
}

// 刪除財產科目信息
export async function deleteAssetAccount(data: Partial<AssetAccount>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAssetAccount, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除財產科目信息失敗",
        };
    }
}
