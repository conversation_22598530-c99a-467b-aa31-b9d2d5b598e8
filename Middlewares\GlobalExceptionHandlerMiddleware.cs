using System.Net;
using System.Text.Json;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace FAST_ERP_Backend.Middlewares;

/// <summary>
/// 全域例外處理中介軟體，用於捕捉所有未處理的例外。
/// </summary>
public class GlobalExceptionHandlerMiddleware(RequestDelegate next)
{
    /// <summary> 中介軟體的執行方法。 </summary>
    /// <param name="context">HTTP 上下文。</param>
    /// <param name="logger">日誌服務。</param>
    /// <param name="env">主機環境資訊。</param>
    public async Task InvokeAsync(HttpContext context, ILoggerService logger, IWebHostEnvironment env)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            // 記錄詳細的例外資訊
            await logger.LogErrorAsync("An unhandled exception has occurred",ex,"GlobalExceptionHandler");

            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

            // 根據環境決定錯誤訊息的可辨識性
            object response;
            if (env.IsDevelopment())
            {
                response = new { success = false, message = ex.Message, detail = ex.ToString() };
            }
            else
            {
                response = new { success = false, message = "伺服器發生未預期的錯誤，請聯繫系統管理員。" };
            }

            var jsonResponse = JsonSerializer.Serialize(response);
            await context.Response.WriteAsync(jsonResponse);
        }
    }
}

/// <summary>
/// 用於註冊全域例外處理中介軟體的擴充方法。
/// </summary>
public static class GlobalExceptionHandlerMiddlewareExtensions
{
    /// <summary>
    /// 將 GlobalExceptionHandlerMiddleware 加入到應用程式的請求處理管道中。
    /// </summary>
    /// <param name="builder">應用程式建構器。</param>
    /// <returns>應用程式建構器。</returns>
    public static IApplicationBuilder UseGlobalExceptionHandler(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<GlobalExceptionHandlerMiddleware>();
    }
}