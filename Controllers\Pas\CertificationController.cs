using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("檢覈資料管理")]
    public class CertificationController : ControllerBase
    {
        private readonly ICertificationService _interface;

        public CertificationController(ICertificationService certificationService)
        {
            _interface = certificationService;
        }

        [HttpGet]
        [Route("GetAll/{_userid}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有檢覈資料列表")]
        public async Task<IActionResult> GetCertificationList(string _userid)
        {
            var result = await _interface.GetCertificationListAsync(_userid);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得檢覈明細", Description = "依uid取得檢覈明細")]
        public async Task<IActionResult> GetCertificationDetail(string _uid)
        {
            var result = await _interface.GetCertificationDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增檢覈資料", Description = "新增檢覈資料")]
        public async Task<IActionResult> AddCertification([FromBody] CertificationDTO _data)
        {
            var (result, msg) = await _interface.AddCertificationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯檢覈資料", Description = "編輯檢覈資料")]
        public async Task<IActionResult> EditCertification([FromBody] CertificationDTO _data)
        {
            var (result, msg) = await _interface.EditCertificationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除檢覈資料", Description = "刪除檢覈資料")]
        public async Task<IActionResult> DeleteCertification([FromBody] string _uid)
        {
            var (result, msg) = await _interface.DeleteCertificationAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}
