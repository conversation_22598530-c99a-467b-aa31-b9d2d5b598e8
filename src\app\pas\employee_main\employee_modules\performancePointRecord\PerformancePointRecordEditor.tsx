import { useEffect, useState } from 'react';
import { Modal, Form, Row, Col, Input, DatePicker, Cascader, Button, message, Spin, Typography, Space } from 'antd';
import dayjs from 'dayjs';
import {
    getPerformancePointRecordDetail,
    addPerformancePointRecord,
    editPerformancePointRecord,
    createEmptyPerformancePointRecord,
} from '@/services/pas/PerformancePoint/PerformancePointRecordService';
import { getGroupTypeCascaderOptions, CascaderOptionDTO } from '@/services/pas/PerformancePoint/PerformancePointTypeService';
import {
    CalendarOutlined,
    NumberOutlined,
    AppstoreOutlined,
    FileTextOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

export type Mode = 'add' | 'edit';

type Props = {
    userId: string;
    mode: Mode;
    uid?: string;
    open: boolean;
    onClose: () => void;
    onSuccess: () => void;
};

const PerformancePointRecordEditor: React.FC<Props> = ({ userId, mode, uid, open, onClose, onSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<CascaderOptionDTO[]>([]);

    // 先載入選項
    useEffect(() => {
        getGroupTypeCascaderOptions().then(res => {
            if (res.success && res.data) {
                setOptions(res.data);
            } else {
                message.error(res.message || '載入點數類型選項失敗');
            }
        });
    }, []);

    // 找出完整層級路徑 (value陣列) ，用來告訴 Cascader 如何顯示文字
    const findFullPath = (opts: CascaderOptionDTO[], targetUid: string): string[] | null => {
        for (const opt of opts) {
            if (opt.value === targetUid) return [opt.value];
            if (opt.children) {
                const childPath = findFullPath(opt.children, targetUid);
                if (childPath) return [opt.value, ...childPath];
            }
        }
        return null;
    };

    // 等 options 載入完且 open, 才載入資料
    useEffect(() => {
        if (!open || options.length === 0) return;

        const loadDetail = async () => {
            if (mode === 'edit' && uid) {
                setLoading(true);
                try {
                    const res = await getPerformancePointRecordDetail(uid);
                    if (res.success && res.data) {
                        const data = res.data;
                        // 用 findFullPath 取得完整層級 path (陣列)
                        const fullPath = findFullPath(options, data.pointUid);

                        form.setFieldsValue({
                            ...data,
                            pointDate: data.pointDate ? dayjs(data.pointDate) : null,
                            pointUid: fullPath ?? [],  // 給 Cascader 正確陣列顯示文字
                        });
                    } else {
                        message.error(res.message || '載入失敗');
                    }
                } catch (error: any) {
                    message.error(error.message || '載入失敗');
                } finally {
                    setLoading(false);
                }
            } else {
                form.setFieldsValue(createEmptyPerformancePointRecord());
            }
        };

        loadDetail();
    }, [mode, uid, open, options]);

    // 送出時只要取最後一層 uid 傳後端
    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const payload = {
                ...values,
                userId,
                pointDate: values.pointDate ? values.pointDate.format('YYYY-MM-DD') : '',
                // pointUid 本身是完整路徑陣列，但後端只要最後一個 uid
                pointUid: Array.isArray(values.pointUid) ? values.pointUid[values.pointUid.length - 1] : values.pointUid,
            };

            setLoading(true);
            const res = mode === 'edit'
                ? await editPerformancePointRecord({ ...payload, uid })
                : await addPerformancePointRecord(payload);

            if (res.success && res.data?.result) {
                message.success(mode === 'edit' ? '更新成功' : '新增成功');
                onSuccess();
                onClose();
            } else {
                message.error(res.message || '儲存失敗');
            }
        } catch (err: any) {
            if (!err?.errorFields) message.error(err.message || '儲存失敗');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            title={
                <Title level={5} style={{ margin: 0 }}>
                    {mode === 'edit' ? '編輯點數紀錄' : '新增點數紀錄'}
                </Title>
            }
            open={open}
            onCancel={onClose}
            width={600}
            centered
            maskClosable={false}
            footer={
                <div style={{ textAlign: 'right', padding: '12px 0' }}>
                    <Button
                        onClick={onClose}
                        style={{
                            marginRight: 8,
                            borderRadius: '6px'
                        }}
                    >
                        取消
                    </Button>
                    <Button
                        type="primary"
                        onClick={handleSubmit}
                        loading={loading}
                        style={{ borderRadius: '6px' }}
                    >
                        儲存
                    </Button>
                </div>
            }
            destroyOnClose
            styles={{
                header: {
                    marginBottom: 0,
                    padding: '16px 24px',
                    borderBottom: '1px solid #f0f0f0'
                },
                body: {
                    padding: '24px'
                }
            }}
        >
            <Spin spinning={loading}>
                <Form
                    layout="vertical"
                    form={form}
                    className="mt-4"
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label={
                                    <Space>
                                        <CalendarOutlined style={{ color: '#1890ff' }} />
                                        <Text strong>日期</Text>
                                    </Space>
                                }
                                name="pointDate"
                                rules={[{ required: true, message: '請選擇日期' }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    placeholder="請選擇日期"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label={
                                    <Space>
                                        <NumberOutlined style={{ color: '#52c41a' }} />
                                        <Text strong>點數</Text>
                                    </Space>
                                }
                                name="point"
                                rules={[{ required: true, message: '請輸入點數' }]}
                            >
                                <Input
                                    type="number"
                                    placeholder="請輸入點數"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                label={
                                    <Space>
                                        <AppstoreOutlined style={{ color: '#722ed1' }} />
                                        <Text strong>點數類型</Text>
                                    </Space>
                                }
                                name="pointUid"
                                rules={[{ required: true, message: '請選擇點數類型' }]}
                            >
                                <Cascader
                                    options={options}
                                    placeholder="請選擇點數類型"
                                    style={{ width: '100%', borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                label={
                                    <Space>
                                        <FileTextOutlined style={{ color: '#eb2f96' }} />
                                        <Text strong>備註</Text>
                                    </Space>
                                }
                                name="remark"
                            >
                                <Input.TextArea
                                    rows={4}
                                    placeholder="請輸入備註內容"
                                    style={{ borderRadius: '6px' }}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Spin>
        </Modal>
    );
};

export default PerformancePointRecordEditor;
