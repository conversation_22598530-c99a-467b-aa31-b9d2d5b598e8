# Partner List Display Enhancement 文檔

## 概述

Partner List Display Enhancement 為 FastERP 系統的商業夥伴管理模組添加了基於夥伴屬性的標籤頁導航功能，讓用戶能夠快速切換不同的夥伴視圖，提升數據瀏覽和管理效率。

## 功能特點

### 🎯 核心功能
- **多視圖標籤頁**: 提供 6 種不同的夥伴視圖
- **智能篩選**: 結合標籤頁篩選和進階搜尋功能
- **實時統計**: 每個標籤頁顯示對應的夥伴數量
- **響應式設計**: 適配移動端和桌面端顯示

### 🏷️ 標籤頁類型
1. **全部夥伴** - 顯示所有商業夥伴
2. **個人夥伴** - 僅顯示個人類型的夥伴
3. **組織夥伴** - 僅顯示組織類型的夥伴
4. **客戶** - 僅顯示具有客戶角色的夥伴
5. **供應商** - 僅顯示具有供應商角色的夥伴
6. **客戶+供應商** - 僅顯示同時具有客戶和供應商角色的夥伴

## 技術實現

### 狀態管理
```typescript
const [activeTab, setActiveTab] = useState<string>('all');
```

### 篩選邏輯
```typescript
const filterPartnersByTab = useCallback((partners: Partner[], tab: string): Partner[] => {
  if (!Array.isArray(partners)) return [];
  
  switch (tab) {
    case 'all':
      return partners;
    case 'individual':
      return partners.filter(partner => partner.individualDetail);
    case 'organization':
      return partners.filter(partner => partner.organizationDetail);
    case 'customer':
      return partners.filter(partner => partner.customerDetail);
    case 'supplier':
      return partners.filter(partner => partner.supplierDetail);
    case 'both':
      return partners.filter(partner => partner.customerDetail && partner.supplierDetail);
    default:
      return partners;
  }
}, []);
```

### 標籤頁配置
```typescript
const tabItems = useMemo(() => {
  return [
    {
      key: 'all',
      label: (
        <Space>
          <UnorderedListOutlined />
          <span>全部夥伴</span>
          <Badge count={data.partners.length} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      ),
    },
    // ... 其他標籤頁配置
  ];
}, [data.partners, stats, activeTab, filterPartnersByTab]);
```

## UI/UX 設計

### 視覺設計
- **圖標標識**: 每個標籤頁使用語義化圖標
- **數量徽章**: 實時顯示各類型夥伴數量
- **顏色編碼**: 不同標籤頁使用不同顏色區分
- **卡片式設計**: 使用 Ant Design 的 Card 型標籤頁

### 響應式適配
```typescript
size={isMobile ? 'small' : 'middle'}
```

### 標籤頁顏色方案
- 全部夥伴: 綠色 (#52c41a)
- 個人夥伴: 藍色 (#1890ff)
- 組織夥伴: 紫色 (#722ed1)
- 客戶: 橙色 (#fa8c16)
- 供應商: 青色 (#13c2c2)
- 客戶+供應商: 粉色 (#eb2f96)

## 整合功能

### 與搜尋篩選的整合
```typescript
onFilterResult={(state) => {
  // 先根據標籤頁篩選
  const tabFiltered = filterPartnersByTab(data.partners, activeTab);
  
  // 再應用搜尋和進階篩選
  const filtered = applyPartnerFilters(
    tabFiltered,
    state.searchText,
    state.activeFilters,
    state.filterValues
  );
  setFilteredPartners(filtered);
  setCurrentPage(1); // 重置分頁
}}
```

### 統計數據更新
```typescript
stats={{
  total: filterPartnersByTab(data.partners, activeTab).length,
  filtered: filteredPartners.length
}}
```

### 分頁重置
切換標籤頁時自動重置到第一頁，確保用戶體驗的一致性。

## 使用方法

### 基本操作
1. 點擊不同的標籤頁切換視圖
2. 標籤頁會顯示對應類型的夥伴數量
3. 可以在任何標籤頁中使用搜尋和進階篩選功能
4. 切換標籤頁會保持當前的搜尋和篩選條件

### 標籤頁切換邏輯
```typescript
onChange={(key) => {
  setActiveTab(key);
  setCurrentPage(1); // 重置分頁
  
  // 重新應用篩選
  const tabFiltered = filterPartnersByTab(data.partners, key);
  setFilteredPartners(tabFiltered);
}}
```

## 性能優化

### 記憶化處理
- 使用 `useMemo` 優化標籤頁配置計算
- 使用 `useCallback` 優化篩選函數

### 篩選順序優化
1. 先執行標籤頁篩選（較快的屬性檢查）
2. 再執行搜尋和進階篩選（較複雜的字串比對）

## 數據流程

### 初始載入
```
載入夥伴資料 → 計算統計數據 → 應用預設標籤頁篩選 → 顯示結果
```

### 標籤頁切換
```
用戶點擊標籤頁 → 更新 activeTab → 應用標籤頁篩選 → 重置分頁 → 更新顯示
```

### 搜尋篩選
```
用戶輸入搜尋條件 → 先應用標籤頁篩選 → 再應用搜尋篩選 → 更新結果
```

## 測試建議

### 功能測試
1. 驗證各標籤頁的篩選邏輯正確性
2. 測試標籤頁切換時的數據更新
3. 確認搜尋功能在不同標籤頁下的表現
4. 驗證統計數據的準確性

### 響應式測試
1. 移動端標籤頁顯示效果
2. 平板端操作體驗
3. 桌面端完整功能

### 性能測試
1. 大量數據下的篩選性能
2. 標籤頁切換的響應速度
3. 記憶體使用情況

## 維護說明

### 新增標籤頁類型
1. 在 `filterPartnersByTab` 函數中添加新的 case
2. 在 `tabItems` 配置中添加新的標籤頁項目
3. 更新相關的統計計算邏輯

### 修改篩選邏輯
- 確保篩選邏輯與後端數據結構保持一致
- 更新時需要考慮向後相容性
- 添加適當的錯誤處理

## 已知限制

### 數據依賴
- 依賴於 Partner 實體的 individualDetail、organizationDetail、customerDetail、supplierDetail 屬性
- 需要確保後端數據結構的一致性

### 性能考量
- 大量數據時可能需要虛擬化或分頁優化
- 複雜篩選條件可能影響響應速度

## 版本歷史

### v1.0.0 (2024-01-07)
- 初始版本發布
- 支援 6 種標籤頁視圖
- 整合搜尋和進階篩選功能
- 響應式設計實現
- 完整的統計數據顯示
