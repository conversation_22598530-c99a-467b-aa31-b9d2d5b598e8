/**
 * GUID/UUID 工具函數
 * 提供生成標準RFC 4122規範的UUID/GUID功能
 */

/**
 * 生成符合RFC 4122版本4規範的GUID
 * 格式: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
 * 其中y取值為8、9、A或B
 * @returns 返回生成的GUID字符串
 */
export function generateGuid(): string {
    // 參考自: https://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 驗證字符串是否為有效的GUID格式
 * @param guid 待驗證的GUID字符串
 * @returns 是否為有效的GUID格式
 */
export function isValidGuid(guid: string): boolean {
    if (!guid) return false;

    // GUID格式的正則表達式
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return guidRegex.test(guid);
}

/**
 * 生成簡短格式的GUID（不含連字符）
 * @returns 返回不含連字符的GUID字符串
 */
export function generateShortGuid(): string {
    return generateGuid().replace(/-/g, '');
}

// 默認導出generateGuid函數
export default generateGuid; 