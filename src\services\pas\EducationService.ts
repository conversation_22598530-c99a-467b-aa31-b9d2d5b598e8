import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 學歷資料
export interface Education {
    uid: string;
    userId: string;
    degreeType: string;
    degreeTypeName: string;
    graduateType: string;
    graduateTypeName: string;
    schoolName: string;
    departmentName: string;
    periodDateStart: string;
    periodDateEnd: string;
    certificateDate: string;
    certificateNumber: string;
    remark: string;
}

export const createEmptyEducation = (): Education => ({
    uid: '',
    userId: '',
    degreeType: '',
    degreeTypeName: '',
    graduateType: '',
    graduateTypeName: '',
    schoolName: '',
    departmentName: '',
    periodDateStart: '',
    periodDateEnd: '',
    certificateDate: '',
    certificateNumber: '',
    remark: '',
});

// 搜尋學歷列表
export async function getEducationList(userid: string): Promise<ApiResponse<Education[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getEducationList}/${userid}`, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋員工列表失敗",
        };
    }
}

// 搜尋學歷明細
export async function getEducationDetail(uid: string): Promise<ApiResponse<Education>> {
    return await httpClient(`${apiEndpoints.getEducationDetail}/${uid}`, {
        method: "GET",
    });
}

// 改為支援 FormData 傳送 multipart/form-data
export async function editEducation(formData: FormData): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editEducation, {
            method: "POST",
            body: formData, // 不需要 stringify
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯學歷資料失敗",
        };
    }
}

export async function addEducation(formData: FormData): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addEducation, {
            method: "POST",
            body: formData,
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增學歷資料失敗",
        };
    }
}


// 刪除學歷
export async function deleteEducation(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteEducation, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除學歷資料失敗",
        };
    }
} 
