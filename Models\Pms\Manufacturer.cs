using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 廠牌型號
    /// </summary>
    public class Manufacturer : ModelBaseEntity
    {
        [Key]
        [Comment("廠牌型號編號")]
        [Column(TypeName = "nvarchar(100)")]
        public Guid ManufacturerId { get; set; } // 廠牌型號編號

        [Comment("廠牌名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string Name { get; set; } // 廠牌名稱

        [Comment("型號")]
        [Column(TypeName = "nvarchar(100)")]
        public string Model { get; set; } // 型號

        [Comment("製造商名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string ManufacturerName { get; set; } // 製造商名稱

        [Comment("供應商名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string Supplier { get; set; } // 供應商名稱

        [Comment("聯絡人")]
        [Column(TypeName = "nvarchar(50)")]
        public string ContactPerson { get; set; } // 聯絡人

        [Comment("聯絡電話")]
        [Column(TypeName = "nvarchar(20)")]
        public string ContactPhone { get; set; } // 聯絡電話

        [Comment("聯絡信箱")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContactEmail { get; set; } // 聯絡信箱

        [Comment("說明描述")]
        [Column(TypeName = "nvarchar(500)")]
        public string Description { get; set; } // 說明描述

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public Manufacturer()
        {
            ManufacturerId = Guid.NewGuid();
            Name = "";
            Model = "";
            ManufacturerName = "";
            Supplier = "";
            ContactPerson = "";
            ContactPhone = "";
            ContactEmail = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class ManufacturerDTO
    {
        public Guid ManufacturerId { get; set; } // 廠牌型號編號
        public string Name { get; set; } // 廠牌名稱
        public string Model { get; set; } // 型號
        public string ManufacturerName { get; set; } // 製造商名稱
        public string Supplier { get; set; } // 供應商名稱
        public string ContactPerson { get; set; } // 聯絡人
        public string ContactPhone { get; set; } // 聯絡電話
        public string ContactEmail { get; set; } // 聯絡信箱
        public string Description { get; set; } // 說明描述
        public int SortCode { get; set; } // 排序號碼
        public long? CreateTime { get; set; } // 新增時間
        public string CreateUserId { get; set; } // 新增者編號
        public long? UpdateTime { get; set; } // 更新時間
        public string UpdateUserId { get; set; } // 更新者編號
        public long? DeleteTime { get; set; } // 刪除時間
        public string DeleteUserId { get; set; } // 刪除者編號
        public bool IsDeleted { get; set; } // 刪除狀態

        public ManufacturerDTO()
        {
            ManufacturerId = Guid.Empty;
            Name = "";
            Model = "";
            ManufacturerName = "";
            Supplier = "";
            ContactPerson = "";
            ContactPhone = "";
            ContactEmail = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}

