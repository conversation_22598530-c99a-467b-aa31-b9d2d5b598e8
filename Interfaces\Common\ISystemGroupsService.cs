﻿using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface ISystemGroupsService
    {
        Task<List<SystemGroupsDTO>> GetSystemGroupsAsync(string systemGroupId = "");
        Task<(bool, string)> AddSystemGroupsAsync(SystemGroupsDTO systemGroup,String tokenUid="");
        Task<(bool, string)> EditSystemGroupsAsync(SystemGroupsDTO systemGroup,String tokenUid="");
        Task<(bool, string)> DeleteSystemmGroupsAsync(SystemGroupsDTO systemGroup,String tokenUid="");
        Task<bool> VerifyGroupAsync(string verifyGroupName);
    }
}
