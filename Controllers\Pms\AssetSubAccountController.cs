using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("財產子目管理")]
    public class AssetSubAccountController : ControllerBase
    {
        private readonly IAssetSubAccountService _assetSubAccountService;

        public AssetSubAccountController(IAssetSubAccountService assetSubAccountService)
        {
            _assetSubAccountService = assetSubAccountService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得財產子目列表", Description = "取得所有財產子目列表")]
        public async Task<IActionResult> GetAssetSubAccountList()
        {
            var accounts = await _assetSubAccountService.GetAllAsync();
            return Ok(accounts);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得財產子目明細", Description = "依ID取得財產子目明細")]
        public async Task<IActionResult> GetAssetSubAccountDetail(string id)
        {
            var account = await _assetSubAccountService.GetByIdAsync(id);
            if (account == null)
                return NotFound($"找不到編號為{id}的財產子目。");

            return Ok(account);
        }

        [HttpGet]
        [Route("Get/{assetAccountId}")]
        [SwaggerOperation(Summary = "依資產科目編號取得財產子目列表", Description = "依資產科目編號取得財產子目列表")]
        public async Task<IActionResult> GetAssetSubAccountsByAssetAccountId(string assetAccountId)
        {
            var accounts = await _assetSubAccountService.GetByAssetAccountIdAsync(assetAccountId);
            return Ok(accounts);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增財產子目", Description = "新增財產子目")]
        public async Task<IActionResult> AddAssetSubAccount([FromBody] AssetSubAccountDTO assetSubAccount)
        {
            var result = await _assetSubAccountService.AddAsync(assetSubAccount);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯財產子目", Description = "編輯財產子目")]
        public async Task<IActionResult> EditAssetSubAccount([FromBody] AssetSubAccountDTO assetSubAccount)
        {
            var result = await _assetSubAccountService.UpdateAsync(assetSubAccount);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除財產子目", Description = "刪除財產子目")]
        public async Task<IActionResult> DeleteAssetSubAccount([FromBody] AssetSubAccountDTO assetSubAccount)
        {
            var result = await _assetSubAccountService.DeleteAsync(assetSubAccount);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }
    }
}