using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class ExaminationService : IExaminationService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public ExaminationService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<ExaminationDTO>> GetExaminationListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Examination
                    .Where(e => e.userId == userId && e.IsDeleted != true)
                    .OrderByDescending(e => e.examEndDate)
                    .Select(e => new ExaminationDTO
                    {
                        uid = e.uid,
                        userId = e.userId,
                        examName = e.examName,
                        examType = e.examType,
                        admittanceGrade = e.admittanceGrade,
                        examInstitution = e.examInstitution,
                        examStartDate = _baseform.TimestampToDateStr(e.examStartDate),
                        examEndDate = _baseform.TimestampToDateStr(e.examEndDate),
                        certificateDate = _baseform.TimestampToDateStr(e.certificateDate),
                        certificateNumber = e.certificateNumber,
                        remark = e.remark,
                        UpdateTime = e.UpdateTime
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得考試資料錯誤", ex);
            }
        }

        public async Task<ExaminationDTO> GetExaminationDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Examination
                    .Where(e => e.uid == uid && e.IsDeleted != true)
                    .Select(e => new ExaminationDTO
                    {
                        uid = e.uid,
                        userId = e.userId,
                        examName = e.examName,
                        examType = e.examType,
                        admittanceGrade = e.admittanceGrade,
                        examInstitution = e.examInstitution,
                        examStartDate = _baseform.TimestampToDateStr(e.examStartDate),
                        examEndDate = _baseform.TimestampToDateStr(e.examEndDate),
                        certificateDate = _baseform.TimestampToDateStr(e.certificateDate),
                        certificateNumber = e.certificateNumber,
                        remark = e.remark,
                        UpdateTime = e.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得考試明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddExaminationAsync(ExaminationDTO data)
        {
            var list_msg_check = CheckExaminationInput(data, "add");
            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newExam = new Examination
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    examName = data.examName,
                    examType = data.examType,
                    admittanceGrade = data.admittanceGrade,
                    examInstitution = data.examInstitution,
                    examStartDate = _baseform.DateStrToTimestamp(data.examStartDate),
                    examEndDate = _baseform.DateStrToTimestamp(data.examEndDate),
                    certificateDate = _baseform.DateStrToTimestamp(data.certificateDate),
                    certificateNumber = data.certificateNumber,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Examination.AddAsync(newExam);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "考試資料登錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"考試資料登錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditExaminationAsync(ExaminationDTO data)
        {
            var list_msg_check = CheckExaminationInput(data, "edit");
            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingExam = await _context.Pas_Examination.FirstOrDefaultAsync(e => e.uid == data.uid && e.IsDeleted != true);
                if (existingExam == null)
                {
                    return (false, "找不到對應的考試資料");
                }

                existingExam.examName = data.examName;
                existingExam.examType = data.examType;
                existingExam.admittanceGrade = data.admittanceGrade;
                existingExam.examInstitution = data.examInstitution;
                existingExam.examStartDate = _baseform.DateStrToTimestamp(data.examStartDate);
                existingExam.examEndDate = _baseform.DateStrToTimestamp(data.examEndDate);
                existingExam.certificateDate = _baseform.DateStrToTimestamp(data.certificateDate);
                existingExam.certificateNumber = data.certificateNumber;
                existingExam.remark = data.remark;

                existingExam.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existingExam.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯考試資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯考試資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteExaminationAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingExam = await _context.Pas_Examination.FirstOrDefaultAsync(e => e.uid == uid && e.IsDeleted != true);
                if (existingExam == null)
                {
                    return (false, "資料已刪除或不存在");
                }

                existingExam.IsDeleted = true;
                existingExam.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existingExam.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除考試資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除考試資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckExaminationInput(ExaminationDTO data, string mode)
        {
            List<string> list_errorMsg = new List<string>();

            if (string.IsNullOrWhiteSpace(data.examName))
                list_errorMsg.Add("請輸入考試名稱");

            if (!string.IsNullOrEmpty(data.examStartDate) && !_baseform.IsValidDateOrEmpty(data.examStartDate))
                list_errorMsg.Add("考試起日格式輸入錯誤");

            if (!string.IsNullOrEmpty(data.examEndDate) && !_baseform.IsValidDateOrEmpty(data.examEndDate))
                list_errorMsg.Add("考試迄日格式輸入錯誤");

            if (!string.IsNullOrEmpty(data.certificateDate) && !_baseform.IsValidDateOrEmpty(data.certificateDate))
                list_errorMsg.Add("發證日期格式輸入錯誤");

            return list_errorMsg;
        }
    }
}
