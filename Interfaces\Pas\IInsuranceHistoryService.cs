using FAST_ERP_Backend.Models.Pas;

public interface IInsuranceHistoryService
{
    Task<(bool, string)> AddAsync(InsuranceHistoryDTO dto);
    Task<(bool, string)> EditAsync(InsuranceHistoryDTO dto);
    Task<(bool, string)> DeleteAsync(string uid);
    Task<InsuranceHistoryDTO?> GetByUidAsync(string uid);
    Task<InsuranceHistoryDTO?> GetEffectiveGradeAsync(string userId, int insuranceType, string targetDate);
    Task<List<InsuranceHistoryDTO>> GetByUserAndTypeAsync(string userId, int insuranceType);
    Task<Dictionary<int, InsuranceHistoryDTO?>> GetAllEffectiveGradesAsync(string userId, string targetDate);

}
