"use client";

import React, { useEffect, useState } from "react";
import {
  addConnectionStatusListener,
  removeConnectionStatusListener,
  getCurrentConnectionStatus,
} from "@/services/common/signalRService";

const ConnectionStatus = () => {
  const [connectionStatus, setConnectionStatus] = useState<string>("未連線");
  const [connectionId, setConnectionId] = useState<string | null>(null);

  // 根據連線狀態返回對應的樣式和文字
  const getStatusInfo = (status: string) => {
    switch (status.toLowerCase()) {
      case "connected":
        return {
          color: "#22c55e", // 綠色
          text: "已連線",
        };
      case "reconnecting":
      case "connecting":
        return {
          color: "#eab308", // 黃色
          text: "連線中",
        };
      case "disconnected":
      case "connection failed":
      default:
        return {
          color: "#ef4444", // 紅色
          text: "已斷線",
        };
    }
  };

  useEffect(() => {
    // 在元件初始化時，先取得當前的連線狀態
    const currentStatus = getCurrentConnectionStatus();
    setConnectionStatus(currentStatus.status);
    setConnectionId(currentStatus.connectionId);

    // 設置監聽器以實時更新連線狀態
    const handleStatusChange = (
      status: string,
      connectionId: string | null
    ) => {
      setConnectionStatus(status);
      setConnectionId(connectionId);
    };

    addConnectionStatusListener(handleStatusChange);

    return () => {
      removeConnectionStatusListener(handleStatusChange);
    };
  }, []);

  const statusInfo = getStatusInfo(connectionStatus);

  return (
    <div className="flex items-center space-x-4 text-sm">
      <div className="flex items-center space-x-2">
        <span className="font-medium">連線狀態: </span>
        <span
          style={{
            display: "inline-block",
            width: "10px",
            height: "10px",
            backgroundColor: statusInfo.color,
            borderRadius: "50%",
            border: "2px solid rgba(0,0,0,0.1)",
            transition: "background-color 0.3s ease",
          }}
        />
      </div>
      {connectionId && (
        <div className="flex items-center space-x-2">
          <span className="font-medium">連線 ID:</span>
          <span className="text-gray-600">{connectionId}</span>
        </div>
      )}
    </div>
  );
};

export default ConnectionStatus;
