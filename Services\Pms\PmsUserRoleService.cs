using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Pms;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class PmsUserRoleService : IPmsUserRoleService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public PmsUserRoleService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得所有財產系統使用者身分
        /// </summary>
        /// <returns></returns>
        public async Task<List<PmsUserRoleDTO>> GetAllPmsUserRolesAsync()
        {
            try
            {
                var roles = await _context.PmsUserRoles
                    .Where(r => !r.IsDeleted)
                    .OrderBy(r => r.SortCode)
                    .ToListAsync();

                return _mapper.Map<List<PmsUserRoleDTO>>(roles);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得所有財產系統使用者身分時發生錯誤: {ex.Message}");
                return new List<PmsUserRoleDTO>();
            }
        }

        /// <summary>
        /// 取得指定財產系統使用者身分
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PmsUserRoleDTO> GetPmsUserRoleByIdAsync(Guid id)
        {
            try
            {
                var role = await _context.PmsUserRoles
                    .FirstOrDefaultAsync(r => r.PmsUserRoleId == id && !r.IsDeleted);

                return _mapper.Map<PmsUserRoleDTO>(role);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得指定財產系統使用者身分時發生錯誤: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 新增財產系統使用者身分
        /// </summary>
        /// <param name="pmsUserRoleDTO"></param>
        /// <returns></returns>
        public async Task<PmsUserRoleDTO> CreatePmsUserRoleAsync(PmsUserRoleDTO pmsUserRoleDTO)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var role = _mapper.Map<PmsUserRole>(pmsUserRoleDTO);
                role.PmsUserRoleId = Guid.NewGuid();
                role.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                role.CreateUserId = pmsUserRoleDTO.CreateUserId;

                await _context.PmsUserRoles.AddAsync(role);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return _mapper.Map<PmsUserRoleDTO>(role);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                Console.WriteLine($"新增財產系統使用者身分時發生錯誤: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 更新財產系統使用者身分
        /// </summary>
        /// <param name="pmsUserRoleDTO"></param>
        /// <returns></returns>
        public async Task<PmsUserRoleDTO> UpdatePmsUserRoleAsync(PmsUserRoleDTO pmsUserRoleDTO)
        {
            try
            {
                var role = await _context.PmsUserRoles
                    .FirstOrDefaultAsync(r => r.PmsUserRoleId == pmsUserRoleDTO.PmsUserRoleId && !r.IsDeleted);

                if (role == null)
                    return null;

                _mapper.Map(pmsUserRoleDTO, role);
                role.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                role.UpdateUserId = pmsUserRoleDTO.UpdateUserId;

                await _context.SaveChangesAsync();
                return _mapper.Map<PmsUserRoleDTO>(role);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新財產系統使用者身分時發生錯誤: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 刪除財產系統使用者身分
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<bool> DeletePmsUserRoleAsync(Guid id)
        {
            try
            {
                var role = await _context.PmsUserRoles
                    .FirstOrDefaultAsync(r => r.PmsUserRoleId == id && !r.IsDeleted);

                if (role == null)
                    return false;

                role.IsDeleted = true;
                role.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刪除財產系統使用者身分時發生錯誤: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 取得使用者身分
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<PmsUserRoleDTO>> GetUserRolesAsync(string userId)
        {
            try
            {
                var roles = await _context.PmsUserRoleMappings
                    .Include(m => m.PmsUserRole)
                    .Where(m => m.UserId == userId && !m.IsDeleted && !m.PmsUserRole.IsDeleted)
                    .Select(m => m.PmsUserRole)
                    .OrderBy(r => r.SortCode)
                    .ToListAsync();

                return _mapper.Map<List<PmsUserRoleDTO>>(roles);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得使用者身分時發生錯誤: {ex.Message}");
                return new List<PmsUserRoleDTO>();
            }
        }

        /// <summary>
        /// 指派使用者身分
        /// </summary>
        /// <param name="mappingDTO"></param>
        /// <returns></returns>
        public async Task<PmsUserRoleMappingDTO> AssignRoleToUserAsync(PmsUserRoleMappingDTO mappingDTO)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingMapping = await _context.PmsUserRoleMappings
                    .FirstOrDefaultAsync(m => m.UserId == mappingDTO.UserId &&
                                            m.PmsUserRoleId == mappingDTO.PmsUserRoleId &&
                                            !m.IsDeleted);

                if (existingMapping != null)
                    return null;

                var mapping = _mapper.Map<PmsUserRoleMapping>(mappingDTO);
                mapping.PmsUserRoleMappingId = Guid.NewGuid();
                mapping.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                mapping.CreateUserId = mappingDTO.CreateUserId;

                await _context.PmsUserRoleMappings.AddAsync(mapping);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return _mapper.Map<PmsUserRoleMappingDTO>(mapping);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                Console.WriteLine($"指派使用者身分時發生錯誤: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 移除使用者身分
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public async Task<bool> RemoveRoleFromUserAsync(string userId, Guid roleId)
        {
            try
            {
                var mapping = await _context.PmsUserRoleMappings
                    .FirstOrDefaultAsync(m => m.UserId == userId &&
                                            m.PmsUserRoleId == roleId &&
                                            !m.IsDeleted);

                if (mapping == null)
                    return false;

                mapping.IsDeleted = true;
                mapping.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                mapping.DeleteUserId = userId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移除使用者身分時發生錯誤: {ex.Message}");
                return false;
            }
        }
    }
}