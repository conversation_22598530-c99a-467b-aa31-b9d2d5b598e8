using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AccessoryEquipmentService : IAccessoryEquipmentService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public AccessoryEquipmentService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 新增附屬設備
        /// </summary>
        /// <param name="accessoryEquipment"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddAsync(AccessoryEquipmentDTO accessoryEquipment)
        {
            // 開始交易
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 檢查財產是否存在
                var asset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetId == accessoryEquipment.AssetId && !a.IsDeleted);

                if (asset == null)
                {
                    return (false, "找不到指定的財產");
                }

                var entity = _mapper.Map<AccessoryEquipment>(accessoryEquipment);
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = accessoryEquipment.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                // 提交交易
                await transaction.CommitAsync();
                return (true, "新增附屬設備成功");
            }
            catch (Exception ex)
            {
                // 發生錯誤時回滾交易
                await transaction.RollbackAsync();
                return (false, $"新增附屬設備失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除附屬設備
        /// </summary>
        /// <param name="accessoryEquipment"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteAsync(AccessoryEquipmentDTO accessoryEquipment)
        {
            var entity = await _context.Set<AccessoryEquipment>().FindAsync(accessoryEquipment.AccessoryEquipmentId);

            if (entity == null)
            {
                return (false, "找不到資料");
            }

            entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            entity.DeleteUserId = accessoryEquipment.DeleteUserId;
            entity.IsDeleted = true;

            _context.Update(entity);
            await _context.SaveChangesAsync();

            return (true, "刪除附屬設備成功");
        }

        /// <summary>
        /// 取得所有附屬設備
        /// </summary>
        /// <returns></returns>
        public async Task<List<AccessoryEquipmentDTO>> GetAllAsync()
        {
            var entities = await _context.Set<AccessoryEquipment>()
                .Include(ae => ae.Asset)
                .AsNoTracking()
                .ToListAsync();

            var dtos = _mapper.Map<List<AccessoryEquipmentDTO>>(entities);

            // 查詢使用者資訊
            foreach (var dto in dtos)
            {
                var createUser = await _context.Set<Users>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.UserId == dto.CreateUserId);

                var updateUser = await _context.Set<Users>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.UserId == dto.UpdateUserId);

                var deleteUser = await _context.Set<Users>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.UserId == dto.DeleteUserId);

                dto.CreateUserName = createUser?.Name ?? "";
                dto.UpdateUserName = updateUser?.Name ?? "";
                dto.DeleteUserName = deleteUser?.Name ?? "";
            }

            return dtos;
        }

        /// <summary>
        /// 取得附屬設備ById
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<string> GetByIdAsync(Guid id)
        {
            var entity = await _context.Set<AccessoryEquipment>()
                .Include(ae => ae.Asset)
                .FirstOrDefaultAsync(ae => ae.AccessoryEquipmentId == id);

            if (entity == null)
            {
                return "找不到資料";
            }

            var dto = _mapper.Map<AccessoryEquipmentDTO>(entity);
            return JsonConvert.SerializeObject(dto);
        }

        /// <summary>
        /// 更新附屬設備
        /// </summary>
        /// <param name="accessoryEquipment">附屬設備資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool, string)> UpdateAsync(AccessoryEquipmentDTO accessoryEquipment)
        {
            try
            {
                // 查找現有的附屬設備
                var entity = await _context.Set<AccessoryEquipment>().FindAsync(accessoryEquipment.AccessoryEquipmentId);
                if (entity == null)
                {
                    return (false, "找不到附屬設備資料");
                }

                // 檢查 AssetId 是否有效
                if (accessoryEquipment.AssetId != Guid.Empty && accessoryEquipment.AssetId != entity.AssetId)
                {
                    // 檢查是否有對應的資產存在
                    var assetExists = await _context.Set<Asset>().AnyAsync(a =>
                        a.AssetId == accessoryEquipment.AssetId &&
                        (a.DeleteTime == null || a.DeleteTime == 0));

                    if (!assetExists)
                    {
                        return (false, "指定的資產不存在，無法更新附屬設備關聯");
                    }
                }

                // 使用 AutoMapper 更新實體
                _mapper.Map(accessoryEquipment, entity);

                //更新附屬設備
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = accessoryEquipment.UpdateUserId;

                // 保存變更
                _context.Update(entity);
                await _context.SaveChangesAsync();

                return (true, "更新附屬設備成功");
            }
            catch (DbUpdateConcurrencyException)
            {
                return (false, "資料已被其他使用者修改，請重新整理後再試");
            }
            catch (DbUpdateException ex)
            {
                // 處理外鍵約束錯誤
                if (ex.InnerException != null &&
                    ex.InnerException.Message.Contains("FOREIGN KEY constraint"))
                {
                    return (false, "更新失敗：資產關聯無效，請確認資產是否存在");
                }
                return (false, $"更新失敗：{ex.Message}");
            }
            catch (Exception ex)
            {
                // 記錄異常
                Console.WriteLine($"更新附屬設備時發生錯誤: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"內部錯誤: {ex.InnerException.Message}");
                }
                return (false, $"更新失敗：發生系統錯誤");
            }
        }
    }
}
