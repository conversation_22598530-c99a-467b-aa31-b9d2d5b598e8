//** 使用者資訊 **/
export interface UserInfo {
    userId: string;
    account: string;
    name: string;
    enterpriseGroupId: string;
    rolesId: string;
    positionId: string;
    jobRankId: string;
    eMail: string;
    address: string;
    telNo: string;
    phone: string;
    sortCode: number;
    unlockTime: string | null;
}

const USER_INFO_KEY = 'UserInfo';

export const getUserInfo = (): UserInfo | null => {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY);
    if (!userInfoStr) return null;
    try {
        return JSON.parse(userInfoStr);
    } catch (error) {
        console.error('解析使用者資訊失敗:', error);
        return null;
    }
};

export const setUserInfo = (userInfo: UserInfo): void => {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
};

export const clearUserInfo = (): void => {
    localStorage.removeItem(USER_INFO_KEY);
}; 