# 篩選組件使用指南

## 🚀 快速開始

### 方案一：FilterSearchContainer (推薦給新頁面)

**最簡單的使用方式，只需要一個組件！**

```tsx
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';

// 配置篩選選項
const filterOptions = [
  { label: "名稱", value: "name", type: "input" },
  { label: "狀態", value: "status", children: [
    { label: "啟用", value: "active" },
    { label: "停用", value: "inactive" }
  ]}
];

// 使用組件
<FilterSearchContainer
  filterOptions={filterOptions}
  onFilterResult={(state) => {
    // 處理篩選結果
    const filtered = applyFilters(data, state);
    setFilteredData(filtered);
  }}
/>
```

### 方案二：useFilterSearch Hook (推薦給現有頁面遷移)

**適合需要更多控制的場景**

```tsx
import { useFilterSearch } from '@/app/ims/hooks/useFilterSearch';
import FilterSearchPanel from '@/app/ims/components/shared/FilterSearchPanel';

const filterSearch = useFilterSearch({
  showClearMessage: true,
  onClear: () => {
    // 額外的清除邏輯
    setOtherState('');
  }
});

<FilterSearchPanel
  filterOptions={filterOptions}
  searchText={filterSearch.searchText}
  onSearchChange={filterSearch.setSearchText}
  filterData={{ 
    activeFilters: filterSearch.activeFilters, 
    filterValues: filterSearch.filterValues 
  }}
  onFilterChange={(event) => filterSearch.setFilterData(event)}
  onClearAll={filterSearch.clearAll}
/>
```

## 📋 實際範例

### Partner 頁面實際應用

Partner 頁面已經成功套用新的篩選組件，實現代碼如下：

```tsx
// Partner 頁面 - 實際生產代碼
const PartnerPage = () => {
  const [data, setData] = useState({ partners: [] });
  const [filteredPartners, setFilteredPartners] = useState([]);

  const partnerFilterOptions = [
    { label: "夥伴類型", value: "type", children: [
      { label: "個人", value: "individual" },
      { label: "組織", value: "organization" }
    ]},
    { label: "夥伴狀態", value: "status", children: [
      { label: "活躍", value: "active" },
      { label: "非活躍", value: "inactive" },
      { label: "暫停", value: "suspended" }
    ]},
    { label: "夥伴名稱", value: "name", type: "input" },
    { label: "夥伴代碼", value: "code", type: "input" },
    { label: "聯絡電話", value: "phone", type: "input" },
    { label: "電子郵件", value: "email", type: "input" }
  ];

  return (
    <div>
      {/* 🎯 只需要這一個組件實現完整篩選功能！ */}
      <FilterSearchContainer
        title="Partner 篩選與搜尋"
        filterOptions={partnerFilterOptions}
        searchPlaceholder="搜尋 Partner 名稱、代碼、電話、郵件"
        showStats={true}
        stats={{ total: data.partners.length, filtered: filteredPartners.length }}
        onFilterResult={(state) => {
          const filtered = applyPartnerFilters(data.partners, state);
          setFilteredPartners(filtered);
        }}
      />

      {/* Partner 列表 */}
      <Table dataSource={filteredPartners} />
    </div>
  );
};
```

### Item 頁面範例 (已更新)

```tsx
// Item 頁面 - 使用新的簡化方式
const ItemPage = () => {
  const [data, setData] = useState({ items: [] });
  const [filteredItems, setFilteredItems] = useState([]);

  return (
    <div>
      <FilterSearchContainer
        title="商品篩選與搜尋"
        filterOptions={itemFilterOptions}
        searchPlaceholder="搜尋商品名稱、編號或條碼"
        stats={{ total: data.items.length, filtered: filteredItems.length }}
        onFilterResult={(state) => {
          const filtered = applyItemFilters(data.items, state);
          setFilteredItems(filtered);
        }}
      />
      
      <Table dataSource={filteredItems} />
    </div>
  );
};
```

## 🔧 配置選項

### FilterOption 配置

```tsx
interface FilterOption {
  label: string;           // 顯示名稱
  value: string;           // 篩選鍵值
  type?: 'input' | 'select' | 'treeSelect';  // 篩選類型
  children?: Array<{ label: string; value: string }>;  // 選項列表
  treeData?: any[];        // 樹狀資料 (for treeSelect)
  placeholder?: string;    // 佔位符
  width?: number;          // 寬度
}
```

### 常見配置範例

```tsx
const filterOptions = [
  // 文字輸入篩選
  { 
    label: "商品名稱", 
    value: "name", 
    type: "input", 
    placeholder: "輸入商品名稱" 
  },
  
  // 下拉選擇篩選
  { 
    label: "商品狀態", 
    value: "status", 
    children: [
      { label: "啟用", value: "active" },
      { label: "停用", value: "inactive" }
    ]
  },
  
  // 樹狀選擇篩選
  { 
    label: "商品分類", 
    value: "category", 
    type: "treeSelect", 
    treeData: categoryTreeData,
    width: 250 
  }
];
```

## 📊 效益對比

### 舊方式 vs 新方式

| 項目 | 舊方式 | 新方式 (FilterSearchContainer) |
|------|--------|--------------------------------|
| 代碼行數 | 50+ 行狀態管理 | 1 個組件 |
| 狀態管理 | 手動管理多個狀態 | 自動管理 |
| 清除邏輯 | 手動實現 | 內建統一邏輯 |
| 響應式設計 | 需要手動處理 | 自動適配 |
| 類型安全 | 需要手動定義 | 內建 TypeScript |
| 維護成本 | 高 | 低 |

### 代碼量對比

**舊方式 (50+ 行)**：
```tsx
// 需要管理的狀態
const [searchText, setSearchText] = useState('');
const [advancedFilterData, setAdvancedFilterData] = useState({...});
const [categoryFilter, setCategoryFilter] = useState('');
const [statusFilter, setStatusFilter] = useState('');

// 需要實現的清除邏輯
const handleClearAllFilters = useCallback(() => {
  setAdvancedFilterData({ activeFilters: [], filterValues: {} });
  setSearchText("");
  setCategoryFilter("");
  setStatusFilter("");
  message.success('已清除所有篩選條件');
}, []);

// 需要處理的篩選變更
const handleAdvancedFilterChange = useCallback((event) => {
  setAdvancedFilterData({
    activeFilters: event.activeFilters,
    filterValues: event.filterValues
  });
}, []);

// 複雜的組件配置
<FilterSearchPanel
  filterOptions={filterOptions}
  onSearchChange={(text) => setSearchText(text)}
  onFilterChange={handleAdvancedFilterChange}
  onClearAll={handleClearAllFilters}
  initialSearchText={searchText}
  initialFilters={advancedFilterData}
  // ... 更多配置
/>
```

**新方式 (5 行)**：
```tsx
<FilterSearchContainer
  filterOptions={filterOptions}
  onFilterResult={(state) => {
    const filtered = applyFilters(data, state);
    setFilteredData(filtered);
  }}
/>
```

## ✅ 遷移檢查清單

### 新頁面開發
- [ ] 使用 `FilterSearchContainer` 組件
- [ ] 配置 `filterOptions`
- [ ] 實現 `onFilterResult` 回調
- [ ] 測試篩選功能

### 現有頁面遷移
- [ ] 移除舊的狀態管理代碼
- [ ] 移除手動實現的清除邏輯
- [ ] 替換為 `FilterSearchContainer` 或 `useFilterSearch`
- [ ] 測試功能一致性
- [ ] 確認響應式設計正常

## 🆘 常見問題

**Q: 如何添加自定義的清除邏輯？**
A: 使用 `onClear` 回調：
```tsx
<FilterSearchContainer
  onClear={() => {
    // 你的自定義清除邏輯
    setOtherState('');
  }}
/>
```

**Q: 如何處理複雜的篩選邏輯？**
A: 在 `onFilterResult` 中實現：
```tsx
onFilterResult={(state) => {
  const filtered = data.filter(item => {
    // 你的複雜篩選邏輯
    return complexFilterLogic(item, state);
  });
  setFilteredData(filtered);
}}
```

**Q: 如何保持向後兼容？**
A: 舊的組件仍然可用，可以逐步遷移：
```tsx
// 仍然可以使用舊的組件
import AdvancedFilterComponent from './AdvancedFilterComponent';
import FilterSearchPanel from './FilterSearchPanel';
```

## 📞 技術支援

如果在使用過程中遇到問題，請：
1. 查看範例代碼
2. 檢查配置是否正確
3. 確認回調函數實現
4. 查看控制台錯誤訊息
