using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 參數值處理服務介面
    /// </summary>
    public interface IParameterValueService
    {
        /// <summary>
        /// 從JSON字串中讀取指定屬性的值
        /// </summary>
        T GetValueFromJson<T>(string json, string propertyName, T defaultValue = default);

        /// <summary>
        /// 更新JSON字串中的屬性值
        /// </summary>
        string UpdateJsonProperty<T>(string json, string propertyName, T value);

        /// <summary>
        /// 檢查JSON字串中是否包含指定屬性
        /// </summary>
        bool ContainsProperty(string json, string propertyName);

        /// <summary>
        /// 轉換數值為百分比顯示（如將0.4轉為40%）
        /// </summary>
        decimal ConvertToPercentage(decimal decimalValue);

        /// <summary>
        /// 轉換百分比為小數值（如將40%轉為0.4）
        /// </summary>
        decimal ConvertFromPercentage(decimal percentageValue);
    }
}