using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("保險單位資料管理")]
    public class InsuranceUnitController : ControllerBase
    {
        private readonly IInsuranceUnitService _Interface;

        public InsuranceUnitController(IInsuranceUnitService insuranceUnitService)
        {
            _Interface = insuranceUnitService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得保險單位列表", Description = "取得所有保險單位資料")]
        public async Task<IActionResult> GetInsuranceUnitList()
        {
            var result = await _Interface.GetInsuranceUnitAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得保險單位明細", Description = "依ID取得保險單位明細")]
        public async Task<IActionResult> GetInsuranceUnitDetail(string id)
        {
            var result = await _Interface.GetInsuranceUnitDetailAsync(id);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增保險單位", Description = "新增保險單位資料")]
        public async Task<IActionResult> AddInsuranceUnit([FromBody] InsuranceUnitDTO _data)
        {
            var (result, msg) = await _Interface.AddInsuranceUnitAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯保險單位", Description = "修改已存在之保險單位資料")]
        public async Task<IActionResult> EditInsuranceUnit([FromBody] InsuranceUnitDTO _data)
        {
            var (result, msg) = await _Interface.EditInsuranceUnitAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除保險單位", Description = "刪除已存在之保險單位資料")]
        public async Task<IActionResult> DeleteInsuranceUnit([FromBody] InsuranceUnitDTO _data)
        {
            var (result, msg) = await _Interface.DeleteInsuranceUnitAsync(_data);
            return Ok(new { result, msg });
        }
    }
}