using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("眷屬健保資料管理")]
    public class HensureController : ControllerBase
    {
        private readonly IHensureService _interface;

        public HensureController(IHensureService hensureService)
        {
            _interface = hensureService;
        }

        [HttpGet]
        [Route("GetAll/{_userId}")]
        [SwaggerOperation(Summary = "取得眷屬健保列表", Description = "取得所有眷屬健保資料列表")]
        public async Task<IActionResult> GetHensureList(string _userId)
        {
            var result = await _interface.GetHensureListAsync(_userId);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得眷屬健保明細", Description = "依uid取得眷屬健保明細")]
        public async Task<IActionResult> GetHensureDetail(string _uid)
        {
            var result = await _interface.GetHensureDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增眷屬健保資料", Description = "新增眷屬健保資料")]
        public async Task<IActionResult> AddHensure([FromBody] HensureDTO _data)
        {
            var (result, msg) = await _interface.AddHensureAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯眷屬健保資料", Description = "編輯眷屬健保資料")]
        public async Task<IActionResult> EditHensure([FromBody] HensureDTO _data)
        {
            var (result, msg) = await _interface.EditHensureAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除眷屬健保資料", Description = "刪除眷屬健保資料")]
        public async Task<IActionResult> DeleteHensure([FromBody] string _uid)
        {
            var (result, msg) = await _interface.DeleteHensureAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}
