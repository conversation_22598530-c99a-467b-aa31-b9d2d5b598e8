"use client";

/*
  修改密碼
  app/settings/page.tsx
*/

import { useState } from "react";
import { Card, Form, Input, Button, Spin, Modal } from "antd";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import { apiEndpoints } from "@/config/api";
import { httpClient } from "@/services/http";
import { changePassword } from "@/services/common/userService";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { routes } from "@/config/routes";

// 修改密碼表單
interface ChangePasswordForm {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 修改密碼頁面
export default function SettingsPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [modal, contextHolder] = Modal.useModal();

  // 修改密碼
  const handleChangePassword = async (values: ChangePasswordForm) => {
    if (!user?.userId) {
      notifyError("修改失敗", "無法取得使用者編號");
      return;
    }

    if (values.newPassword !== values.confirmPassword) {
      notifyError("修改失敗", "新密碼與確認密碼不相符");
      return;
    }

    setLoading(true);
    try {
      const result = await changePassword(
        user.userId,
        values.oldPassword,
        values.newPassword
      );

      if (result.data.result) {
        notifySuccess("修改成功", "密碼已更新，請重新登入");
        form.resetFields();
        // 登出並重定向到登入頁面
        await logout();
        router.push(routes.login);
      } else {
        notifyError("修改失敗", result.data.msg || "請稍後再試");
      }
    } catch (error) {
      notifyError("修改錯誤", "發生未預期的錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 確認修改密碼
  const onFinish = (values: ChangePasswordForm) => {
    modal.confirm({
      title: "確認修改密碼",
      icon: <ExclamationCircleFilled />,
      content: "修改密碼後將自動登出，需要重新登入",
      okText: "確定",
      cancelText: "取消",
      onOk: () => handleChangePassword(values),
    });
  };

  // 載入中
  if (!user) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Card title="修改密碼" bordered={false}>
      {contextHolder}
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        style={{ maxWidth: 400, margin: "0 auto" }}
      >
        <Input
          type="text"
          name="username"
          autoComplete="username"
          value={user.account}
          style={{ display: "none" }}
          readOnly
        />

        <Form.Item
          label="目前密碼"
          name="oldPassword"
          rules={[{ required: true, message: "請輸入目前密碼" }]}
        >
          <Input.Password
            placeholder="請輸入目前密碼"
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item
          label="新密碼"
          name="newPassword"
          rules={[
            { required: true, message: "請輸入新密碼" },
            { min: 8, message: "密碼長度至少8個字元" },
          ]}
        >
          <Input.Password
            placeholder="請輸入新密碼"
            autoComplete="new-password"
          />
        </Form.Item>

        <Form.Item
          label="確認新密碼"
          name="confirmPassword"
          rules={[
            { required: true, message: "請再次輸入新密碼" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("newPassword") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error("兩次輸入的密碼不相符"));
              },
            }),
          ]}
        >
          <Input.Password
            placeholder="請再次輸入新密碼"
            autoComplete="new-password"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            修改密碼
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}
