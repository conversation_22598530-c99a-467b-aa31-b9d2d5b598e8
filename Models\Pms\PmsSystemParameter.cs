using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Pms.Enums;
using System.ComponentModel;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 財產管理系統參數設定
    /// </summary>
    public class PmsSystemParameter : ModelBaseEntity
    {
        [Key]
        [Comment("系統參數編號")]
        [Column(TypeName = "nvarchar(100)")]
        public Guid ParameterId { get; set; } // 系統參數編號

        [Comment("參數名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string ParameterName { get; set; } // 參數名稱

        [Comment("參數值")]
        [Column(TypeName = "nvarchar(500)")]
        public string ParameterValue { get; set; } // 參數值

        [Comment("參數描述")]
        [Column(TypeName = "nvarchar(500)")]
        public string ParameterDescription { get; set; } // 參數描述

        [Comment("參數類型")]
        [Column(TypeName = "nvarchar(50)")]
        public string ParameterType { get; set; } // 參數類型 (如 depreciation_method 折舊法類型)

        [Comment("是否啟用")]
        [Column(TypeName = "bit")]
        public bool IsEnabled { get; set; } // 是否啟用

        [Comment("排序順序")]
        [Column(TypeName = "int")]
        public int SortOrder { get; set; } // 排序順序

        public PmsSystemParameter()
        {
            ParameterId = Guid.NewGuid();
            ParameterName = "";
            ParameterValue = "";
            ParameterDescription = "";
            ParameterType = "";
            IsEnabled = true;
            SortOrder = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class PmsSystemParameterDTO : ModelBaseEntityDTO
    {
        [Required(ErrorMessage = "參數編號不能為空")]
        public Guid ParameterId { get; set; } // 系統參數編號

        [Required(ErrorMessage = "參數名稱不能為空")]
        public string ParameterName { get; set; } // 參數名稱

        [Required(ErrorMessage = "參數值不能為空")]
        public string ParameterValue { get; set; } // 參數值
        public string ParameterDescription { get; set; } // 參數描述

        [Required(ErrorMessage = "參數類型不能為空")]
        [EnumDataType(typeof(PmsParameterType))]
        public string ParameterType { get; set; } // 參數類型

        [Required(ErrorMessage = "是否啟用不能為空")]
        [DefaultValue(false)]
        public bool IsEnabled { get; set; } // 是否啟用

        [DefaultValue(0)]
        public int SortOrder { get; set; } // 排序順序

        public PmsSystemParameterDTO()
        {
            ParameterId = Guid.NewGuid();
            ParameterName = "";
            ParameterValue = "";
            ParameterDescription = "";
            ParameterType = "";
            IsEnabled = true;
            SortOrder = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}