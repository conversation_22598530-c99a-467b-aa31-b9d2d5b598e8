/**
 * 員工篩選容器組件
 * 
 * 使用統一的篩選架構替換原有的 FilterTypeSelect 組件，
 * 提供更好的擴展性和一致性。
 */

"use client";

import React from 'react';
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';
import { FilterOption } from '@/app/ims/types/filter';

interface EmployeeFilterContainerProps {
  /** 當前篩選類型 */
  filterType: string;
  /** 當前篩選值 */
  filterValue: string;
  /** 篩選變更回調 */
  onFilterChange: (field: 'filterType' | 'filterValue', value: string) => void;
  /** 搜尋回調 */
  onSearch: () => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否緊湊模式 */
  compact?: boolean;
}

// 員工篩選選項配置
const employeeFilterOptions: FilterOption[] = [
  {
    label: "篩選類型",
    value: "filterType",
    type: "select",
    children: [
      { label: "員工編號", value: "EmpNo" },
      { label: "身分證字號", value: "IdNo" },
      { label: "姓名", value: "Name" }
    ],
    placeholder: "選擇篩選類型",
    width: 150
  }
];

/**
 * 員工篩選容器組件
 * 
 * 提供員工搜尋和篩選功能，使用統一的篩選架構。
 * 
 * @example
 * ```tsx
 * <EmployeeFilterContainer
 *   filterType={filterType}
 *   filterValue={filterValue}
 *   onFilterChange={(field, value) => {
 *     if (field === 'filterType') setFilterType(value);
 *     if (field === 'filterValue') setFilterValue(value);
 *   }}
 *   onSearch={handleSearch}
 * />
 * ```
 */
const EmployeeFilterContainer: React.FC<EmployeeFilterContainerProps> = ({
  filterType,
  filterValue,
  onFilterChange,
  onSearch,
  disabled = false,
  compact = false
}) => {
  // 處理篩選結果
  const handleFilterResult = (state: any) => {
    // 處理篩選類型變更
    if (state.filterValues.filterType && state.filterValues.filterType !== filterType) {
      onFilterChange('filterType', state.filterValues.filterType);
    }
    
    // 處理搜尋文字變更
    if (state.searchText !== filterValue) {
      onFilterChange('filterValue', state.searchText);
    }
    
    // 如果有搜尋文字且按下 Enter，觸發搜尋
    if (state.searchText && state.searchText.trim().length > 0) {
      // 這裡可以添加防抖邏輯
      const timeoutId = setTimeout(() => {
        onSearch();
      }, 300);
      
      return () => clearTimeout(timeoutId);
    }
  };

  return (
    <div className="employee-filter-container mb-6">
      <FilterSearchContainer
        filterOptions={employeeFilterOptions}
        searchPlaceholder="請輸入關鍵字"
        title="" // 不顯示標題
        showStats={false} // 不顯示統計
        disabled={disabled}
        compact={compact}
        initialSearchText={filterValue}
        initialFilters={{
          activeFilters: filterType ? ['filterType'] : [],
          filterValues: filterType ? { filterType } : {}
        }}
        onFilterResult={handleFilterResult}
        showClearMessage={false} // 不顯示清除訊息
        className="employee-filter"
      />
      
      <style jsx>{`
        .employee-filter-container :global(.filter-search-panel) {
          background: transparent;
          border: none;
          padding: 0;
          box-shadow: none;
        }
        
        .employee-filter-container :global(.filter-search-panel .ant-card-head) {
          display: none;
        }
        
        .employee-filter-container :global(.filter-search-panel .ant-card-body) {
          padding: 0;
        }
        
        .employee-filter-container :global(.filter-controls-container) {
          gap: 12px;
          margin-bottom: 0;
        }
        
        @media (max-width: 768px) {
          .employee-filter-container :global(.filter-controls-container) {
            flex-direction: row;
            flex-wrap: wrap;
          }
        }
      `}</style>
    </div>
  );
};

export default EmployeeFilterContainer;

// 向後兼容的類型定義
export type FilterTypeSelectProps = EmployeeFilterContainerProps;

/**
 * @deprecated 請使用 EmployeeFilterContainer 替代
 * 
 * 這個組件保持向後兼容性，但建議遷移到新的 EmployeeFilterContainer。
 * 
 * 遷移範例：
 * ```tsx
 * // 舊的使用方式
 * <FilterTypeSelect
 *   filterType={filterType}
 *   filterValue={filterValue}
 *   onFilterChange={onFilterChange}
 *   onSearch={onSearch}
 * />
 * 
 * // 新的使用方式
 * <EmployeeFilterContainer
 *   filterType={filterType}
 *   filterValue={filterValue}
 *   onFilterChange={onFilterChange}
 *   onSearch={onSearch}
 * />
 * ```
 */
export const FilterTypeSelectReplacement: React.FC<EmployeeFilterContainerProps> = EmployeeFilterContainer;
