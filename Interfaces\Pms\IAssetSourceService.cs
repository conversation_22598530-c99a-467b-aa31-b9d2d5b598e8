using FAST_ERP_Backend.Models.Pms;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetSourceService
    {
        /// <summary>
        /// 取得所有資產來源
        /// </summary>
        /// <returns>所有資產來源</returns>
        Task<List<AssetSourceDTO>> GetAllAsync();

        /// <summary>
        /// 取得資產來源ById
        /// </summary>
        /// <param name="id">資產來源編號</param>
        /// <returns>資產來源</returns>
        Task<string> GetByIdAsync(int id);

        /// <summary>
        /// 新增資產來源
        /// </summary>
        /// <param name="assetSource">資產來源</param>
        /// <returns>新增的資產來源</returns>
        Task<(bool, string)> AddAsync(AssetSourceDTO assetSource);

        /// <summary>   
        /// 更新資產來源
        /// </summary>
        /// <param name="assetSource">資產來源</param>
        /// <returns>更新後的資產來源</returns>
        Task<(bool, string)> UpdateAsync(AssetSourceDTO assetSource);

        /// <summary>
        /// 刪除資產來源
        /// </summary>
        /// <param name="_data">資產來源</param>
        /// <returns>是否刪除成功</returns>
        Task<(bool, string)> DeleteAsync(AssetSourceDTO _data);
    }
}