﻿using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Extensions.Pas;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class EmployeeService : IEmployeeService
    {
        private readonly EncryptionHelper _encryptionHelper;
        private readonly ERPDbContext _context;
        private readonly EmployeeClass _employeeClass;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;
        private readonly IPromotionService _promotionService;

        public EmployeeService(
            EncryptionHelper encryptionHelper,
            Baseform baseform,
            ERPDbContext context,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService,
            IPromotionService promotionService)
        {
            _encryptionHelper = encryptionHelper;
            _baseform = baseform;
            _context = context;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
            _promotionService = promotionService;
        }

        //取得所有員工列表（包含當前升遷資料）.
        public async Task<List<EmployeeDTO>> GetEmployeeListAsync(FilterData _data)
        {
            try
            {
                var employeesQuery = _context.Common_Users.Where(x => x.IsDeleted != true)
                .ToEmployeeDTO(_context.Pas_Employee.Where(t => t.IsDeleted != true), _employeeClass)
                .Where(emp =>
                    string.IsNullOrWhiteSpace(_data.filterValue) ||
                    (_data.filterType == "EmpNo" && emp.EmpNo.Contains(_data.filterValue)) ||
                    (_data.filterType == "IdNo" && emp.IdNo.Contains(_data.filterValue)) ||
                    (_data.filterType == "Name" && emp.usersDTO.Name.Contains(_data.filterValue))
                );

                // 動態排序
                switch (_data.filterType)
                {
                    case "EmpNo":
                        employeesQuery = employeesQuery.OrderBy(emp => emp.EmpNo);
                        break;
                    case "IdNo":
                        employeesQuery = employeesQuery.OrderBy(emp => emp.IdNo);
                        break;
                    case "Name":
                        employeesQuery = employeesQuery.OrderBy(emp => emp.usersDTO.Name);
                        break;
                    default:
                        employeesQuery = employeesQuery.OrderBy(emp => emp.EmpNo); // 預設按員工編號
                        break;
                }

                var employees = await employeesQuery.ToListAsync();

                // 為每個員工附加當前升遷資料
                foreach (var employee in employees)
                {
                    if (!string.IsNullOrEmpty(employee.UserId))
                    {
                        var currentPromotion = await _promotionService.GetPromotionByEffectiveDateAsync(employee.UserId);
                        employee.CurrentPromotion = currentPromotion ?? new PromotionDTO();
                    }
                    else
                    {
                        employee.CurrentPromotion = new PromotionDTO();
                    }
                }

                return employees;
            }
            catch (Exception ex)
            {
                throw new Exception("取得員工列表錯誤", ex);
            }
        }

        // 取得員工明細（包含當前升遷資料）
        public async Task<EmployeeDTO> GetEmployeeDetailAsync(string _uid)
        {
            try
            {
                var employee = await _context.Common_Users
                              .Where(x => x.UserId.ToString() == _uid && x.IsDeleted != true)
                              .ToEmployeeDTO(_context.Pas_Employee, _employeeClass)
                              .FirstOrDefaultAsync();

                // 如果找到員工資料，則額外獲取當前生效的升遷資料
                if (employee != null)
                {
                    // 使用PromotionService獲取當天生效的升遷資料
                    var currentPromotion = await _promotionService.GetPromotionByEffectiveDateAsync(_uid);

                    // 將升遷資料附加到員工資料中
                    employee.CurrentPromotion = currentPromotion ?? new PromotionDTO();
                }

                return employee;
            }
            catch (Exception ex)
            {
                throw new Exception("取得員工明細錯誤", ex);
            }
        }

        // 新增員工及user.
        public async Task<(bool, string)> AddEmployeeAsync(EmployeeDTO _data)
        {
            string userId = Guid.NewGuid().ToString().Trim(); // 共用同一個 UserId

            try
            {
                List<string> list_msg_check = CheckEmployeeInput(_data, "add");

                if (list_msg_check.Count > 0)
                {
                    return (false, list_msg_check[0]);
                }

                // 共同更新updateorcreatetime.
                long bint_updateTime = _baseform.GetCurrentLocalTimestamp();

                await _context.Database.BeginTransactionAsync(); // 開啟交易

                // 先新增 Users
                var newUser = new Users
                {
                    UserId = userId,
                    Account = _data.usersDTO.Account,
                    Password = _encryptionHelper.EncryptString(_data.usersDTO.Password),
                    Name = _data.usersDTO.Name,
                    EnterpriseGroupId = _data.usersDTO.EnterpriseGroupId,
                    RolesId = _data.usersDTO.RolesId,
                    PositionId = _data.usersDTO.PositionId,
                    EMail = _data.usersDTO.EMail,
                    PermanentAddress = _data.usersDTO.PermanentAddress,
                    MailingAddress = _data.usersDTO.MailingAddress,
                    TelNo = _data.usersDTO.TelNo,
                    Phone = _data.usersDTO.Phone,
                    AltPhone = _data.usersDTO.AltPhone,
                    SortCode = _data.usersDTO.SortCode,
                    CreateTime = bint_updateTime,
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Common_Users.AddAsync(newUser);

                // 新增 Employee
                var newEmployee = new Employee
                {
                    UserId = userId, // 跟 user 綁同一個 userId
                    IdNo = _data.IdNo,
                    IdType = _data.IdType, // _data.IdType,
                    EmpNo = _data.EmpNo,
                    Birthday = _baseform.DateStrToTimestamp(_data.Birthday),
                    BloodType = _data.BloodType,
                    SpouseIdNo = _data.SpouseIdNo,
                    SpouseName = _data.SpouseName,
                    EduLevel = _data.EduLevel,
                    HireDate = _baseform.DateStrToTimestamp(_data.HireDate),
                    ProbStartDate = _baseform.DateStrToTimestamp(_data.ProbStartDate),
                    OfficialHireDate = _baseform.DateStrToTimestamp(_data.OfficialHireDate),
                    LeaveDate = _baseform.DateStrToTimestamp(_data.LeaveDate),
                    LaborInsStartDate = _baseform.DateStrToTimestamp(_data.LaborInsStartDate),
                    HealthInsStartDate = _baseform.DateStrToTimestamp(_data.HealthInsStartDate),
                    TransferDate = _baseform.DateStrToTimestamp(_data.TransferDate),
                    remark = _data.remark,
                    errorMark = _data.errorMark,
                    CreateTime = bint_updateTime,
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Pas_Employee.AddAsync(newEmployee);

                // 新增 Salary
                var newSalary = new Salary
                {
                    UserId = userId, // 跟 user 綁同一個 userId
                    SalaryStatus = "0",
                    EmployerContributionRate = 6.00M,
                    EmployeeContributionType = "0",
                    EmployeeContributionRate = 0.00M,
                    EmployeeContributionAmount = 0.00M,
                    TaxType = "1",
                    FixedTaxRate = 0.00M,
                    FixedTaxAmount = 0.00M,
                    TransferAccount = "",
                    Remark = "",
                    CreateTime = bint_updateTime,
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Pas_Salary.AddAsync(newSalary);

                // 一起儲存
                await _context.SaveChangesAsync();

                await _context.Database.CommitTransactionAsync(); // 提交交易

                return (true, "新增使用者與員工資料成功");
            }
            catch (Exception ex)
            {
                await _context.Database.RollbackTransactionAsync(); // 發生錯誤則回滾
                return (false, $"新增使用者與員工資料失敗: {ex.InnerException.Message}");
            }
        }

        public async Task<(bool, string)> EditEmployeeAsync(EmployeeDTO _data)
        {
            List<string> list_msg_check = CheckEmployeeInput(_data, "edit");

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            // 共同更新updateorcreatetime.
            long bint_updateTime = _baseform.GetCurrentLocalTimestamp();

            // 資料處理.
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                var existingUser = await _context.Common_Users
                .FirstOrDefaultAsync(x => x.UserId == _data.usersDTO.UserId);

                if (existingUser != null)
                {
                    UsersHelper.UpdateUserFields(existingUser, _data.usersDTO);

                    existingUser.UpdateTime = bint_updateTime;
                    existingUser.UpdateUserId = _currentUserService.UserId;
                }
                else
                {
                    return (false, "無對應user資料");
                }

                var existemployee = await _context.Pas_Employee
                .FirstOrDefaultAsync(x => x.UserId == _data.UserId);

                if (existemployee != null)
                {
                    existemployee.IdNo = _data.IdNo;
                    existemployee.IdType = _data.IdType;
                    existemployee.EmpNo = _data.EmpNo;
                    existemployee.Birthday = _baseform.DateStrToTimestamp(_data.Birthday);
                    existemployee.BloodType = _data.BloodType;
                    existemployee.SpouseIdNo = _data.SpouseIdNo;
                    existemployee.SpouseName = _data.SpouseName;
                    existemployee.EduLevel = _data.EduLevel;
                    existemployee.HireDate = _baseform.DateStrToTimestamp(_data.HireDate);
                    existemployee.ProbStartDate = _baseform.DateStrToTimestamp(_data.ProbStartDate);
                    existemployee.OfficialHireDate = _baseform.DateStrToTimestamp(_data.OfficialHireDate);
                    existemployee.LeaveDate = _baseform.DateStrToTimestamp(_data.LeaveDate);
                    existemployee.LaborInsStartDate = _baseform.DateStrToTimestamp(_data.LaborInsStartDate);
                    existemployee.HealthInsStartDate = _baseform.DateStrToTimestamp(_data.HealthInsStartDate);
                    existemployee.TransferDate = _baseform.DateStrToTimestamp(_data.TransferDate);
                    existemployee.remark = _data.remark;
                    existemployee.errorMark = _data.errorMark;
                    existemployee.UpdateTime = bint_updateTime;
                    existemployee.UpdateUserId = _currentUserService.UserId;
                }
                else
                {
                    return (false, "無對應employee資料");
                }

                // 兩張表一起存.
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "編輯使用者與員工資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增使用者與員工資料失敗: {ex.InnerException.Message}");
            }

        }

        public async Task<(bool, string)> CompleteEmployeeAsync(EmployeeDTO _data)
        {
            List<string> list_msg_check = CheckEmployeeInput(_data, "complete");

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            // 共同更新updateorcreatetime.
            long bint_updateTime = _baseform.GetCurrentLocalTimestamp();

            // 資料處理.
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                var existingUser = await _context.Common_Users
                .FirstOrDefaultAsync(x => x.UserId == _data.usersDTO.UserId);

                if (existingUser != null)
                {
                    UsersHelper.UpdateBaseUserData(existingUser, _data.usersDTO);
                    existingUser.UpdateTime = bint_updateTime;
                    existingUser.UpdateUserId = _currentUserService.UserId;
                }
                else
                {
                    return (false, "無對應user資料");
                }

                // 再新增 Employee
                var newEmployee = new Employee
                {
                    UserId = _data.usersDTO.UserId, // 跟 user 綁同一個 userId
                    IdNo = _data.IdNo,
                    IdType = _data.IdType, // _data.IdType,
                    EmpNo = _data.EmpNo,
                    Birthday = _baseform.DateStrToTimestamp(_data.Birthday),
                    BloodType = _data.BloodType,
                    SpouseIdNo = _data.SpouseIdNo,
                    SpouseName = _data.SpouseName,
                    EduLevel = _data.EduLevel,
                    HireDate = _baseform.DateStrToTimestamp(_data.HireDate),
                    ProbStartDate = _baseform.DateStrToTimestamp(_data.ProbStartDate),
                    OfficialHireDate = _baseform.DateStrToTimestamp(_data.OfficialHireDate),
                    LeaveDate = _baseform.DateStrToTimestamp(_data.LeaveDate),
                    LaborInsStartDate = _baseform.DateStrToTimestamp(_data.LaborInsStartDate),
                    HealthInsStartDate = _baseform.DateStrToTimestamp(_data.HealthInsStartDate),
                    TransferDate = _baseform.DateStrToTimestamp(_data.TransferDate),
                    remark = _data.remark,
                    errorMark = _data.errorMark,
                    CreateTime = bint_updateTime,
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Pas_Employee.AddAsync(newEmployee);

                // 新增 Salary
                var newSalary = new Salary
                {
                    UserId = _data.usersDTO.UserId, // 跟 user 綁同一個 userId
                    SalaryStatus = "0",
                    EmployerContributionRate = 6.00M,
                    EmployeeContributionType = "1",
                    EmployeeContributionRate = 0.00M,
                    EmployeeContributionAmount = 0.00M,
                    TaxType = "1",
                    FixedTaxRate = 0.00M,
                    FixedTaxAmount = 0.00M,
                    TransferAccount = "",
                    Remark = "",
                    CreateTime = bint_updateTime,
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Pas_Salary.AddAsync(newSalary);

                // 三張表一起存.
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "補全員工資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增使用者與員工資料失敗: {ex.InnerException.Message}");
            }

        }

        public async Task<(bool, string)> DeleteEmployeeAsync(string _uid)
        {
            // check.
            if (_currentUserService.UserId == _uid)
            {
                return (false, "當前登入帳號無法刪除");
            }

            // 共同更新updateorcreatetime.
            long bint_updateTime = _baseform.GetCurrentLocalTimestamp();

            // 資料處理.
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易.

            try
            {
                var existingUser = await _context.Common_Users
                                .FirstOrDefaultAsync(x => x.UserId == _uid && x.IsDeleted != true);

                if (existingUser != null)
                {
                    existingUser.DeleteTime = bint_updateTime;
                    existingUser.DeleteUserId = _currentUserService.UserId;
                    existingUser.IsDeleted = true;
                }
                else
                {
                    return (false, "找不到資料");
                }

                //如果已有employee資料 也要刪除.
                var existemployee = await _context.Pas_Employee
                .FirstOrDefaultAsync(x => x.UserId == _uid && x.IsDeleted != true);

                if (existemployee != null)
                {
                    existemployee.DeleteTime = bint_updateTime;
                    existemployee.DeleteUserId = _currentUserService.UserId;
                    existemployee.IsDeleted = true;
                }

                //如果已有salary資料 也要刪除.
                var existsalary = await _context.Pas_Salary
                .FirstOrDefaultAsync(x => x.UserId == _uid && x.IsDeleted != true);

                if (existsalary != null)
                {
                    existsalary.DeleteTime = bint_updateTime;
                    existsalary.DeleteUserId = _currentUserService.UserId;
                    existsalary.IsDeleted = true;
                }

                // 三張表一起存.
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "刪除成功");
            }
            catch
            {
                await transaction.RollbackAsync();
                return (false, "刪除員工資料錯誤");
            }


        }

        public List<string> CheckEmployeeInput(EmployeeDTO _data, string _mode)
        {
            List<string> list_errorMsg = new List<string>();

            // 檢核item.
            bool accountExists = false; // 帳號存在.
            bool idNoExists = false; // 身分證存在.
            bool empNoExists = false; // 員工編號存在.

            if (_mode == "edit")
            {
                // 編輯模式：(排除自己)
                accountExists = _context.Common_Users
                    .Any(x => x.Account == _data.usersDTO.Account && x.UserId != _data.UserId && x.IsDeleted != true);

                idNoExists = _context.Pas_Employee
                    .Any(x => x.IdNo == _data.IdNo && x.UserId != _data.UserId && x.IsDeleted != true);

                empNoExists = _context.Pas_Employee
                    .Any(x => x.EmpNo == _data.EmpNo && x.UserId != _data.UserId && x.IsDeleted != true);

            }
            else if (_mode == "add")
            {
                // 新增模式
                accountExists = _context.Common_Users
                    .Any(x => x.Account == _data.usersDTO.Account && x.IsDeleted != true);

                idNoExists = _context.Pas_Employee
                    .Any(x => x.IdNo == _data.IdNo && x.IsDeleted != true);

                empNoExists = _context.Pas_Employee
                    .Any(x => x.EmpNo == _data.EmpNo && x.IsDeleted != true);

            }
            else if (_mode == "complete")
            {
                // 補全模式
                idNoExists = _context.Pas_Employee
                    .Any(x => x.IdNo == _data.IdNo && x.IsDeleted != true);

                empNoExists = _context.Pas_Employee
                    .Any(x => x.EmpNo == _data.EmpNo && x.IsDeleted != true);
            }

            if (accountExists)
            {
                list_errorMsg.Add("使用者帳號已存在");
            }

            if (idNoExists)
            {
                list_errorMsg.Add("身分證字號已存在");
            }

            if (empNoExists)
            {
                list_errorMsg.Add("員工編號已存在");
            }

            // 單模式檢核.
            switch (_mode)
            {
                case "add":
                    if (_data.usersDTO.Account == "")
                    {
                        list_errorMsg.Add("帳號不可為空白");
                    }

                    if (_data.usersDTO.Password == "")
                    {
                        list_errorMsg.Add("密碼不可為空白");
                    }
                    break;
                case "edit":
                    break;
                case "complete":
                    break;
            }

            // 共同輸入檢核.
            if (_data.IdNo == "")
            {
                list_errorMsg.Add("身分證字號不可為空白");
            }

            if (_data.EmpNo == "")
            {
                list_errorMsg.Add("員工編號不可為空白");
            }

            if (_data.usersDTO.Name == "")
            {
                list_errorMsg.Add("姓名不可為空白");
            }

            if (!_baseform.IsValidDateOrEmpty(_data.Birthday))
            {
                list_errorMsg.Add("生日格式錯誤");
            }

            return list_errorMsg;
        }

    }
}
