using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("教育訓練資料管理")]
    public class TrainController : ControllerBase
    {
        private readonly ITrainService _Interface;

        public TrainController(ITrainService trainService)
        {
            _Interface = trainService;
        }

        [HttpGet]
        [Route("GetAll/{_userid}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有教育訓練資料列表")]
        public async Task<IActionResult> GetTrainList(string _userid)
        {
            var result = await _Interface.GetTrainListAsync(_userid);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得訓練明細", Description = "依uid取得教育訓練明細")]
        public async Task<IActionResult> GetTrainDetail(string _uid)
        {
            var result = await _Interface.GetTrainDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增訓練資料", Description = "新增教育訓練資料")]
        public async Task<IActionResult> AddTrain([FromBody] TrainDTO _data)
        {
            var (result, msg) = await _Interface.AddTrainAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯訓練資料", Description = "編輯教育訓練資料")]
        public async Task<IActionResult> EditTrain([FromBody] TrainDTO _data)
        {
            var (result, msg) = await _Interface.EditTrainAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除訓練資料", Description = "刪除教育訓練資料")]
        public async Task<IActionResult> DeleteTrain([FromBody] string _uid)
        {
            var (result, msg) = await _Interface.DeleteTrainAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}
