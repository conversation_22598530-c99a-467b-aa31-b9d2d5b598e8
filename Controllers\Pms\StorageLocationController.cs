using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("存放地點資料管理")]
    public class StorageLocationController : ControllerBase
    {
        private readonly IStorageLocationService _Interface;

        public StorageLocationController(IStorageLocationService storageLocationService)
        {
            _Interface = storageLocationService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得存放地點列表", Description = "取得所有存放地點資料")]
        public async Task<IActionResult> GetStorageLocationList()
        {
            var result = await _Interface.GetStorageLocationAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得存放地點明細", Description = "依ID取得存放地點明細")]
        public async Task<IActionResult> GetStorageLocationDetail(string id)
        {
            var result = await _Interface.GetStorageLocationDetailAsync(id);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增存放地點", Description = "新增存放地點資料")]
        public async Task<IActionResult> AddStorageLocation([FromBody] StorageLocationDTO _data)
        {
            var (result, msg) = await _Interface.AddStorageLocationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯存放地點", Description = "修改已存在之存放地點資料")]
        public async Task<IActionResult> EditStorageLocation([FromBody] StorageLocationDTO _data)
        {
            var (result, msg) = await _Interface.EditStorageLocationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除存放地點", Description = "刪除已存在之存放地點資料")]
        public async Task<IActionResult> DeleteStorageLocation([FromBody] StorageLocationDTO _data)
        {
            var (result, msg) = await _Interface.DeleteStorageLocationAsync(_data);
            return Ok(new { result, msg });
        }
    }
}