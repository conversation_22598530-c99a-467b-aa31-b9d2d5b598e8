"use client";

import React from "react";
import dayjs from "dayjs";
import { siteConfig } from "@/config/site";

interface ReportHeaderProps {
  /** 報表名稱 */
  reportTitle: string;
  /** 當前頁數 */
  currentPage?: number;
  /** 總頁數 */
  totalPages?: number;
  /** 列印日期，預設為當前日期 */
  printDate?: string;
  /** 是否顯示為列印模式 */
  isPrintMode?: boolean;
  /** 自訂樣式 */
  style?: React.CSSProperties;
  /** 自訂類別名稱 */
  className?: string;
}

/**
 * 報表抬頭組件
 * 提供統一的報表抬頭格式，包含公司名稱、報表名稱、頁次和列印日期
 */
const ReportHeader: React.FC<ReportHeaderProps> = ({
  reportTitle,
  currentPage,
  totalPages,
  printDate,
  isPrintMode = false,
  style,
  className = "",
}) => {
  const formattedPrintDate = printDate || dayjs().format("YYYY/MM/DD");
  const companyName = siteConfig.copyright;

  // 列印模式樣式
  const printModeStyle: React.CSSProperties = {
    textAlign: "center",
    fontSize: "18px",
    fontWeight: "bold",
    marginBottom: "10px",
    ...style,
  };

  // 螢幕顯示模式樣式
  const screenModeStyle: React.CSSProperties = {
    textAlign: "center",
    fontSize: "16px",
    fontWeight: "bold",
    marginBottom: "20px",
    padding: "10px",
    backgroundColor: "#f5f5f5",
    border: "1px solid #d9d9d9",
    borderRadius: "4px",
    ...style,
  };

  if (isPrintMode) {
    return (
      <div className={`report-header-print ${className}`}>
        {/* 主標題 */}
        <div className="title print-header" style={printModeStyle}>
          {companyName}
          {reportTitle}
        </div>

        {/* 列印模式下的頁次和日期信息已移至 CSS @page 規則中處理，避免重複顯示 */}
      </div>
    );
  }

  return (
    <div
      className={`report-header-screen ${className}`}
      style={screenModeStyle}
    >
      <div style={{ marginBottom: "8px" }}>
        {companyName}
        {reportTitle}
      </div>
      <div style={{ fontSize: "12px", color: "#666" }}>
        {currentPage && totalPages && (
          <span style={{ marginRight: "20px" }}>
            第 {currentPage} 頁 / 共 {totalPages} 頁
          </span>
        )}
        <span>列印日期: {formattedPrintDate}</span>
      </div>
    </div>
  );
};

export default ReportHeader;

// 匯出列印樣式，可以被其他組件使用
export const getReportPrintStyles = (reportTitle: string) => `
  @media print {
    body { 
      print-color-adjust: exact; 
      -webkit-print-color-adjust: exact;
    }
    .no-print { 
      display: none; 
    }
    @page {
      margin: 0.8in 0.2in 0.2in 0.2in;
      @top-center {
        content: "${siteConfig.copyright}${reportTitle}";
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      @top-left {
        content: "第 " counter(page) " 頁 / 共 " counter(pages) " 頁";
        font-size: 10px;
      }
      @top-right {
        content: "列印日期: ${dayjs().format("YYYY/MM/DD")}";
        font-size: 10px;
      }
    }
    .print-header {
      display: none;
    }
    thead {
      display: table-header-group;
    }
    tbody {
      display: table-row-group;
    }
    tr {
      page-break-inside: avoid;
    }
  }
  @page {
    @bottom-left {
      content: "";
    }
    @bottom-center {
      content: "";
    }
    @bottom-right {
      content: "";
    }
  }
`;
