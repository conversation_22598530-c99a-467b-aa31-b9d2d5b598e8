import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// 不需要驗證的路由
const publicRoutes = ['/login'];
// 不需要驗證的靜態資源路徑
const publicPaths = ['/images/', '/fonts/', '/assets/'];

export function middleware(request: NextRequest) {
    // 從 cookie 獲取 token
    const token = request.cookies.get("token")?.value;
    const { pathname } = request.nextUrl;

    // 檢查是否是靜態資源路徑
    const isPublicPath = publicPaths.some(path => pathname.startsWith(path));
    if (isPublicPath) {
        return NextResponse.next();
    }

    // 如果是登入頁面且已有 token，重定向到首頁
    if (publicRoutes.includes(pathname) && token) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    // 如果是根路徑，直接重定向到登入頁面
    if (pathname === '/' && !token) {
        return NextResponse.redirect(new URL('/login', request.url));
    }

    // 如果不是公開路由且沒有 token，重定向到登入頁面
    if (!publicRoutes.includes(pathname) && !token) {
        const loginUrl = new URL('/login', request.url);
        // 保存原始目標路徑，登入後可以重定向回去
        loginUrl.searchParams.set('from', pathname);
        return NextResponse.redirect(loginUrl);
    }

    return NextResponse.next();
}

// 配置需要進行中間件檢查的路徑
export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public file names (e.g. robots.txt)
         */
        '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|.*\\.(?:jpg|jpeg|gif|png|svg|ico)$).*)',
    ],
}; 