"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login");
    } else {
      router.push("/dashboard"); // 或其他已驗證後的首頁路徑
    }
  }, [isAuthenticated, router]);

  return null; // 不需要渲染任何內容，因為會立即重定向
}
