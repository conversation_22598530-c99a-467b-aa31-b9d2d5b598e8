using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AssetAccountService : IAssetAccountService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly IMapper _mapper;

        public AssetAccountService(Baseform baseform, ERPDbContext context, IMapper mapper)
        {
            _baseform = baseform;
            _context = context;
            _mapper = mapper;
        }

        /**
         * 新增財產科目
         * @param assetAccount 財產科目資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> AddAsync(AssetAccountDTO assetAccount)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 檢查財產科目編號是否已存在
                var existingAccountNo = await _context.Set<AssetAccount>()
                    .FirstOrDefaultAsync(a => a.AssetAccountNo == assetAccount.AssetAccountNo && !a.IsDeleted);

                if (existingAccountNo != null)
                {
                    return (false, "財產科目編號已存在");
                }

                // 檢查財產科目名稱是否已存在
                var existingAccountName = await _context.Set<AssetAccount>()
                    .FirstOrDefaultAsync(a => a.AssetAccountName == assetAccount.AssetAccountName && !a.IsDeleted);

                if (existingAccountName != null)
                {
                    return (false, "財產科目名稱已存在");
                }

                var entity = _mapper.Map<AssetAccount>(assetAccount);
                entity.AssetAccountId = Guid.NewGuid();
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = assetAccount.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /**
         * 刪除財產科目
         * @param _data 財產科目資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> DeleteAsync(AssetAccountDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetAccount>()
                    .FirstOrDefaultAsync(a => a.AssetAccountId == _data.AssetAccountId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /**
         * 取得所有財產科目
         * @returns 財產科目資料列表
        **/
        public async Task<List<AssetAccountDTO>> GetAllAsync()
        {
            try
            {
                var entities = await _context.Set<AssetAccount>()
                    .Where(a => !a.IsDeleted)
                    .OrderBy(a => a.CreateTime)
                    .ToListAsync();

                var result = _mapper.Map<List<AssetAccountDTO>>(entities);

                // 獲取使用者名稱
                foreach (var item in result)
                {
                    if (!string.IsNullOrEmpty(item.CreateUserId))
                    {
                        var createUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.CreateUserId);
                        item.CreateUserName = createUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.UpdateUserId))
                    {
                        var updateUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.UpdateUserId);
                        item.UpdateUserName = updateUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.DeleteUserId))
                    {
                        var deleteUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.DeleteUserId);
                        item.DeleteUserName = deleteUser?.Name ?? "";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得所有財產科目時發生錯誤: {ex.Message}");
                return new List<AssetAccountDTO>();
            }
        }

        /**
         * 取得財產科目ById
         * @param id 財產科目Id
         * @returns 財產科目資料
        **/
        public async Task<string> GetByIdAsync(int id)
        {
            try
            {
                var entity = await _context.Set<AssetAccount>()
                    .FirstOrDefaultAsync(a => a.AssetAccountId == Guid.Parse(id.ToString()) && !a.IsDeleted);

                if (entity == null)
                {
                    return "找不到資料";
                }

                var dto = _mapper.Map<AssetAccountDTO>(entity);
                return JsonConvert.SerializeObject(dto);
            }
            catch (Exception ex)
            {
                return $"取得資料失敗: {ex.Message}";
            }
        }

        /**
         * 更新財產科目
         * @param _data 財產科目資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> UpdateAsync(AssetAccountDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetAccount>()
                    .FirstOrDefaultAsync(a => a.AssetAccountId == _data.AssetAccountId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }
    }
}