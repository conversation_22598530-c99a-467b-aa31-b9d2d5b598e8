# AmortizationSource 攤提來源資料表

建立者: 邱 正宜
建立時間: March 17, 2025 17:00 PM
DB相關: DB相關-Pms

## 資料表名稱

AmortizationSource

## 資料表說明

攤提來源用法
攤提來源在固定資產系統中主要用於記錄資產的資金來源分配情況，具有以下用途：
1、資金來源追蹤：
記錄資產購置時的資金來自哪些部門或專案
當一個資產由多個部門共同出資時，可詳細記錄各部門的出資比例
2、成本分攤管理：
將資產成本按比例分攤到各個部門或預算科目
幫助準確計算每個部門的資產使用成本
3、財務報表支援：
在資產財報中反映不同來源的資金使用情況
支援按部門或資金來源統計資產分佈

## 欄位說明

| 欄位名稱 | 資料型態 | 長度 | 允許空值 | 說明 |
| --- | --- | --- | --- | --- |
| AmortizationSourceId | uniqueidentifier | - | 否 | 主鍵，攤提來源ID |
| AssetId | uniqueidentifier | - | 否 | 財產編號 |
| DepartmentId | nvarchar | 100 | 否 | 部門編號 |
| SourceName | nvarchar | 100 | 否 | 攤提來源名稱 |
| Description | nvarchar | 100 | 否 | 攤提來源描述 |
| Amount | decimal | 18,2 | 否 | 攤提來源金額 |
| CreateTime | bigint | - | 是 | 新增時間 |
| CreateUserId | nvarchar | 100 | 是 | 新增者編號 |
| UpdateTime | bigint | - | 是 | 更新時間 |
| UpdateUserId | nvarchar | 100 | 是 | 更新者編號 |
| DeleteTime | bigint | - | 是 | 刪除時間 |
| DeleteUserId | nvarchar | 100 | 是 | 刪除者編號 |
