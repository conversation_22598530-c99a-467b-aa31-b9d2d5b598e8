# 自訂儀表板

這是一個基於 React-Grid-Layout 的自訂儀表板系統，允許用戶自由調整組件的位置和大小。

## 功能特色

### 🎯 核心功能

- **拖拽調整位置**：可以拖拽組件標題來移動位置
- **調整大小**：可以拖拽組件右下角來調整大小
- **新增/移除組件**：動態新增或移除儀表板組件
- **配置儲存**：自動儲存佈局配置到本地存儲
- **重置佈局**：一鍵重置為預設佈局

### 📊 內建組件 (Widgets)

1. **歡迎訊息 (WelcomeWidget)**
   - 顯示用戶歡迎訊息
   - 顯示當前日期和星期

2. **統計數據 (StatsWidget)**
   - 顯示系統關鍵指標
   - 包含用戶數、任務數、文件數等統計

3. **系統指標 (ChartWidget)**
   - 顯示系統性能指標
   - 使用進度條展示各項指標

4. **任務列表 (TaskWidget)**
   - 顯示近期任務
   - 包含任務狀態、優先級、截止日期

5. **行事曆 (CalendarWidget)**
   - 顯示月曆視圖
   - 標記重要事件和會議

6. **通知中心 (NotificationWidget)**
   - 顯示系統通知
   - 區分已讀/未讀狀態

## 文件結構

```
src/app/dashboard/
├── page.tsx                 # 主頁面
├── dashboard.css           # 自訂樣式
├── config.ts              # 配置文件
├── types.ts               # 類型定義
├── widgets/               # 組件目錄
│   ├── index.ts          # 組件索引
│   ├── WelcomeWidget.tsx
│   ├── StatsWidget.tsx
│   ├── ChartWidget.tsx
│   ├── TaskWidget.tsx
│   ├── CalendarWidget.tsx
│   └── NotificationWidget.tsx
└── README.md             # 說明文件
```

## 使用方式

### 基本操作

1. **進入編輯模式**
   - 點擊頂部工具列的「編輯模式」按鈕
   - 組件邊框會變成藍色虛線，表示可編輯

2. **移動組件**
   - 在編輯模式下，拖拽組件標題來移動位置
   - 會顯示藍色預覽區域

3. **調整大小**
   - 在編輯模式下，拖拽組件右下角的調整手柄
   - 每個組件都有最小尺寸限制

4. **新增組件**
   - 點擊「新增組件」按鈕
   - 在對話框中選擇要新增的組件
   - 新組件會自動放置在底部

5. **移除組件**
   - 在編輯模式下，點擊組件標題右側的「移除」按鈕
   - 確認後組件會被移除

6. **儲存配置**
   - 點擊「儲存配置」按鈕
   - 配置會儲存到瀏覽器本地存儲

7. **重置佈局**
   - 點擊「重置佈局」按鈕
   - 確認後會恢復為預設佈局

### 響應式設計

- 支援桌面和平板設備
- 在小螢幕上會自動調整網格列數
- 調整手柄在觸控設備上會變大

## 技術實現

### 主要依賴

- **React-Grid-Layout**: 網格佈局和拖拽功能
- **Ant Design**: UI 組件庫
- **React**: 前端框架
- **TypeScript**: 類型安全

### 配置系統

- `config.ts`: 定義可用組件和預設佈局
- `types.ts`: 定義 TypeScript 類型
- 本地存儲: 儲存用戶自訂配置

### 樣式系統

- `dashboard.css`: 自訂樣式
- 支援編輯模式視覺反饋
- 響應式設計
- 自訂滾動條樣式

## 擴展開發

### 新增組件

1. 在 `widgets/` 目錄下創建新組件文件
2. 在 `widgets/index.ts` 中導出組件
3. 在 `config.ts` 中註冊組件配置
4. 在 `page.tsx` 中添加組件映射

### 組件開發規範

```tsx
"use client";

import { Card, Typography } from "antd";

export default function MyWidget() {
  return (
    <div style={{ height: "100%", padding: "16px" }}>
      {/* 組件內容 */}
    </div>
  );
}
```

### 配置新組件

```typescript
// config.ts
{
  id: "myWidget",
  title: "我的組件",
  component: "MyWidget",
  defaultSize: { w: 6, h: 3 },
  minSize: { w: 4, h: 2 },
  description: "組件描述",
}
```

## 注意事項

1. **性能考量**
   - 組件應該避免頻繁的重新渲染
   - 使用 React.memo 優化性能

2. **樣式規範**
   - 組件高度應該設為 100%
   - 使用 padding 而非 margin
   - 避免固定高度

3. **數據管理**
   - 組件內部管理自己的狀態
   - 共享數據可以使用 Context

4. **響應式設計**
   - 考慮不同螢幕尺寸的顯示效果
   - 使用相對單位而非絕對像素

## 瀏覽器支援

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新日誌

### v1.0.0

- 初始版本發布
- 支援 6 種內建組件
- 完整的拖拽和調整大小功能
- 配置儲存和重置功能
