import React, { useState, useEffect } from "react";
import {
  Card,
  Tag,
  Button,
  Statistic,
  Row,
  Col,
  Spin,
  Tooltip,
  Descriptions,
} from "antd";
import {
  FileTextOutlined,
  ExportOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import {
  getAssetCarryOuts,
  getCarryOutStatistics,
} from "@/services/pms/assetCarryOutService";
import {
  AssetCarryOut,
  AssetCarryOutStatistics,
  STATUS_COLORS,
} from "@/app/pms/document_maintenance/asset_carryout_form/interface";
import {
  translateStatus,
  sortCarryOutList,
} from "@/app/pms/document_maintenance/asset_carryout_form/config";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";

export interface AssetCarryOutWidgetProps {
  showViewAllButton?: boolean;
  maxItems?: number;
}

const AssetCarryOutWidget: React.FC<AssetCarryOutWidgetProps> = ({
  showViewAllButton = true,
  maxItems = 5,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [carryOuts, setCarryOuts] = useState<AssetCarryOut[]>([]);
  const [statistics, setStatistics] = useState<AssetCarryOutStatistics | null>(
    null
  );

  // 載入資料
  const loadData = async () => {
    try {
      setLoading(true);

      // 載入攜出申請資料
      const carryOutResponse = await getAssetCarryOuts({});

      if (carryOutResponse.success && carryOutResponse.data) {
        let dataArray: AssetCarryOut[] = [];
        if (
          (carryOutResponse.data as any).data &&
          Array.isArray((carryOutResponse.data as any).data)
        ) {
          dataArray = (carryOutResponse.data as any).data;
        } else if (Array.isArray(carryOutResponse.data)) {
          dataArray = carryOutResponse.data;
        }

        // 從實際資料計算統計
        const stats = {
          totalCount: dataArray.length,
          pendingCount: dataArray.filter(
            (item) => item.status === "PENDING" || item.status === "待審核"
          ).length,
          approvedCount: dataArray.filter(
            (item) => item.status === "APPROVED" || item.status === "已核准"
          ).length,
          rejectedCount: dataArray.filter(
            (item) => item.status === "REJECTED" || item.status === "已駁回"
          ).length,
          carriedOutCount: dataArray.filter(
            (item) => item.status === "CARRIED_OUT" || item.status === "已攜出"
          ).length,
          returnedCount: dataArray.filter(
            (item) => item.status === "RETURNED" || item.status === "已歸還"
          ).length,
          overdueCount: dataArray.filter(
            (item) => item.status === "OVERDUE" || item.status === "逾期"
          ).length,
        };
        setStatistics(stats);

        // 按申請日期排序
        dataArray.sort(sortCarryOutList);

        // 只取前 maxItems 項
        const limitedData = dataArray.slice(0, maxItems);
        setCarryOuts(limitedData);
      }
    } catch (error) {
      console.error("載入資產攜出資料失敗:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [maxItems]);

  // 跳轉到完整頁面
  const handleViewAll = () => {
    router.push("/pms/document_maintenance/asset_carryout_form");
  };

  return (
    <Card
      extra={
        showViewAllButton && (
          <Button
            type="link"
            size="small"
            onClick={handleViewAll}
            icon={<FileTextOutlined />}
          >
            查看全部
          </Button>
        )
      }
      size="small"
      style={{ height: "100%" }}
      styles={{
        body: {
          padding: "8px",
          height: "100%",
          overflow: "hidden",
        },
      }}
    >
      <Spin spinning={loading}>
        <div
          style={{ height: "100%", display: "flex", flexDirection: "column" }}
        >
          {/* 統計資訊區域 */}
          {statistics && (
            <Row gutter={[4, 4]} style={{ marginBottom: "8px" }}>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#f0f9ff" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.totalCount || 0}
                    title="總計"
                    valueStyle={{ fontSize: "14px", color: "#1890ff" }}
                    prefix={<FileTextOutlined style={{ fontSize: "12px" }} />}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#fff7e6" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.pendingCount || 0}
                    title="待審核"
                    valueStyle={{ fontSize: "14px", color: "#fa8c16" }}
                    prefix={
                      <ClockCircleOutlined style={{ fontSize: "12px" }} />
                    }
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#f6ffed" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.carriedOutCount || 0}
                    title="已攜出"
                    valueStyle={{ fontSize: "14px", color: "#52c41a" }}
                    prefix={<ExportOutlined style={{ fontSize: "12px" }} />}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#fff1f0" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.overdueCount || 0}
                    title="逾期"
                    valueStyle={{ fontSize: "14px", color: "#ff4d4f" }}
                    prefix={
                      <ExclamationCircleOutlined style={{ fontSize: "12px" }} />
                    }
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* 最新申請列表 */}
          <div
            style={{
              flex: 1,
              minHeight: 0,
              border: "1px solid #f0f0f0",
              borderRadius: "6px",
            }}
          >
            <div
              style={{
                padding: "6px 8px",
                background: "#fafafa",
                borderBottom: "1px solid #f0f0f0",
                fontSize: "12px",
                fontWeight: "bold",
              }}
            >
              最新申請
            </div>
            <div
              style={{
                height: "calc(100% - 33px)",
                overflow: "hidden",
                position: "relative",
              }}
            >
              {carryOuts.length === 0 && !loading ? (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    color: "#8c8c8c",
                    fontSize: "12px",
                  }}
                >
                  暫無攜出申請資料
                </div>
              ) : (
                <div
                  style={{
                    height: "100%",
                    overflowY: carryOuts.length > 3 ? "auto" : "hidden",
                    overflowX: "hidden",
                  }}
                >
                  {carryOuts.map((item, index) => (
                    <div
                      key={item.carryOutId}
                      style={{
                        padding: "6px 8px",
                        borderBottom:
                          index < carryOuts.length - 1
                            ? "1px solid #f0f0f0"
                            : "none",
                        fontSize: "11px",
                        minHeight: "50px",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          marginBottom: "4px",
                        }}
                      >
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div
                            style={{
                              fontWeight: "bold",
                              color: "#1890ff",
                              fontSize: "12px",
                              marginBottom: "2px",
                            }}
                          >
                            <Tooltip
                              title={
                                <div style={{ padding: "8px" }}>
                                  <Descriptions
                                    size="small"
                                    column={1}
                                    bordered
                                  >
                                    <Descriptions.Item label="攜出單號">
                                      {item.carryOutNo}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="財產編號">
                                      {item.assetNo}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="財產名稱">
                                      {item.assetName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申請人">
                                      {item.applicantName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申請日期">
                                      {item.applicationDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.applicationDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="攜出目的">
                                      {item.purpose}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="攜出地點">
                                      {item.destination}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="狀態">
                                      <Tag
                                        color={
                                          STATUS_COLORS[
                                            item.status as keyof typeof STATUS_COLORS
                                          ] || "default"
                                        }
                                      >
                                        {translateStatus(item.status)}
                                      </Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="預計攜出日期">
                                      {item.plannedCarryOutDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.plannedCarryOutDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="預計歸還日期">
                                      {item.plannedReturnDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.plannedReturnDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="實際攜出日期">
                                      {item.actualCarryOutDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.actualCarryOutDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="實際歸還日期">
                                      {item.actualReturnDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.actualReturnDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="審核人">
                                      {item.approverName || "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="審核日期">
                                      {item.approvalDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.approvalDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="審核意見">
                                      {item.approvalComment || "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="備註">
                                      {item.notes || "-"}
                                    </Descriptions.Item>
                                  </Descriptions>
                                </div>
                              }
                              color="#fff"
                              placement="right"
                              styles={{
                                root: { maxWidth: "500px" },
                                body: { padding: "0" },
                              }}
                            >
                              {item.carryOutNo}
                            </Tooltip>
                          </div>
                          <div
                            style={{
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              color: "#333",
                              fontSize: "11px",
                              fontWeight: "500",
                            }}
                            title={item.assetName || item.assetNo}
                          >
                            {item.assetName || item.assetNo}
                          </div>
                        </div>
                        <div
                          style={{
                            display: "flex",
                            gap: "4px",
                            flexShrink: 0,
                            marginLeft: "8px",
                          }}
                        >
                          <Tag
                            color={
                              STATUS_COLORS[
                                item.status as keyof typeof STATUS_COLORS
                              ] || "default"
                            }
                            style={{
                              fontSize: "10px",
                              margin: 0,
                              padding: "0 4px",
                            }}
                          >
                            {translateStatus(item.status)}
                          </Tag>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </Spin>
    </Card>
  );
};

export { AssetCarryOutWidget };
export default AssetCarryOutWidget;
