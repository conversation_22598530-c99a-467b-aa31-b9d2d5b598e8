﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("公司圖片管理")]
    public class EnterpriseImageController : ControllerBase
    {
        private readonly IEnterpriseImageService _service;

        public EnterpriseImageController(IEnterpriseImageService service)
        {
            _service = service;
        }

        /// <summary>
        /// 取得公司圖片列表
        /// </summary>
        /// <returns>公司圖片列表</returns>
        [HttpGet("GetList")]
        [SwaggerOperation(Summary = "取得公司圖片列表", Description = "取得公司商標/印章/CI等圖片列表")]
        public async Task<IActionResult> GetImageList()
        {
            var result = await _service.GetImageListAsync();
            return Ok(result);
        }

        /// <summary>
        /// 取得公司圖片
        /// </summary>
        /// <param name="ImageId">圖片ID</param>
        /// <returns>公司圖片</returns>
        [HttpGet("Get/{ImageId}")]
        [SwaggerOperation(Summary = "取得公司圖片", Description = "取得公司商標/印章/CI等圖片")]
        public async Task<IActionResult> GetImage(string ImageId)
        {
            var result = await _service.GetImageAsync(ImageId);
            if (result == null)
            {
                return NotFound();
            }
            return File(result.ImagePath, "image/png"); // 預設回傳PNG格式
        }

        /// <summary>
        /// 上傳公司圖片
        /// </summary>
        /// <param name="request">上傳公司圖片資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost("Upload")]
        [SwaggerOperation(Summary = "上傳公司圖片", Description = "上傳公司商標/印章/CI等圖片")]
        public async Task<IActionResult> UploadImage([FromForm] EnterpriseImageUploadDTO request)
        {
            var (success, message) = await _service.UploadImageAsync(request);
            if (success)
            {
                return Ok(new { success, message });
            }
            return BadRequest(new { success, message });
        }

        [HttpPost("Delete")]
        [SwaggerOperation(Summary = "刪除公司圖片", Description = "刪除公司商標/印章/CI等圖片")]
        public async Task<IActionResult> DeleteImage([FromBody] EnterpriseImageDTO data)
        {
            var (success, message) = await _service.DeleteImageAsync(data);
            if (success)
            {
                return Ok(message);
            }
            return BadRequest(message);
        }
    }
}