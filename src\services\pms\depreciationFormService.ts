import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 折舊紀錄介面
export interface Depreciation {
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    depreciationId: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    depreciationYear: number;
    depreciationMonth: number;
    originalAmount: number;
    accumulatedDepreciation: number;
    currentDepreciation: number;
    depreciationRate: number;
    depreciationMethod: string;
    serviceLifeRemaining: number;
    isAdjustment: boolean;
    adjustmentReason: string;
    notes: string;
    beginningBookValue: number;
    endingBookValue: number;
    depreciationDate: number;
}

// 固定資產折舊單介面
export interface DepreciationForm {
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    depreciationFormId: string;
    depreciationId: string;
    depreciationDate: number;
    depreciationYear: number;
    depreciationMonth: number;
    notes: string;
    depreciationFormDetail?: Depreciation[];
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 獲取所有折舊單
export async function getDepreciationForms(): Promise<ApiResponse<DepreciationForm[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDepreciationForms, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單列表失敗",
            data: [],
        };
    }
}

// 新增折舊單
export async function createDepreciationForm(data: Partial<DepreciationForm>): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.addDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增折舊單失敗",
            data: "",
        };
    }
}

// 更新折舊單
export async function updateDepreciationForm(data: Partial<DepreciationForm>): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.editDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新折舊單失敗",
            data: "",
        };
    }
}

// 刪除折舊單
export async function deleteDepreciationForm(data: Partial<DepreciationForm>): Promise<ApiResponse<string>> {
    try {
        const response = await httpClient(apiEndpoints.deleteDepreciationForm, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除折舊單失敗",
            data: "",
        };
    }
}

// 獲取折舊單明細
export async function getDepreciationFormDetail(depreciationId: string): Promise<ApiResponse<Depreciation>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepreciationFormDetail}/${depreciationId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單明細失敗",
            data: undefined,
        };
    }
}

// 檢查折舊紀錄使用狀態
export async function checkDepreciationUsage(depreciationId: string): Promise<ApiResponse<boolean>> {
    try {
        const response = await httpClient(`${apiEndpoints.checkDepreciationFormUsage}/${depreciationId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "檢查折舊紀錄使用狀態失敗",
            data: false,
        };
    }
}

// 獲取所有折舊單 (包含明細)
export async function getAllDepreciationForms(): Promise<ApiResponse<DepreciationForm[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAllDepreciationForms, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取折舊單列表失敗",
            data: [],
        };
    }
}
