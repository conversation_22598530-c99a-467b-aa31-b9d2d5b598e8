"use client";

/* 角色管理
  /app/common/security/page.tsx
*/
import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Tag,
  Select,
  Tree,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import { notifySuccess, notifyError } from "@/utils/notification";
import { deleteRole, getRoles, Role, AddRole, editRole } from "@/services/common/RolesService";
import { getAllMenuTree, SystemMenu } from "@/services/common/systemMenuService";


const SecurityPage: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<Role[]>([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [form] = Form.useForm();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [menuTree, setMenuTree] = useState<SystemMenu[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  
  // 加載角色列表
  const loadRoles = async () => {
    setLoading(true);
    try {
      const [rolesResponse] = await Promise.all([
        getRoles(),
      ]);

      if (rolesResponse.success && rolesResponse.data) {
        setRoles(rolesResponse.data);
      } else {
        notifyError("獲取角色列表失敗", "請稍後再試");
      }
    } catch (error) {
      notifyError("獲取角色列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 獲取選單結構
  const loadMenuTree = async () => {
    try {
      const response = await getAllMenuTree();
      if (response.success && response.data) {
        // 將選單結構轉換為樹狀結構
        const treeData = response.data.flatMap(group => 
          group.menus.map(menu => ({
            ...menu,
            title: menu.label,
            key: menu.systemMenuId,
            children: menu.children?.map(child => ({
              ...child,
              title: child.label,
              key: child.systemMenuId,
            }))
          }))
        );
        setMenuTree(treeData);
        
        // 設置所有節點為展開狀態
        const allKeys = treeData.reduce((keys: React.Key[], node) => {
          keys.push(node.key);
          if (node.children) {
            node.children.forEach(child => keys.push(child.key));
          }
          return keys;
        }, []);
        setExpandedKeys(allKeys);
      }
    } catch (error) {
      notifyError("獲取選單結構失敗", "請稍後再試");
    }
  };
  
  useEffect(() => {
    loadRoles();
    loadMenuTree();
  }, []);

  // 處理搜尋和篩選
  useEffect(() => {
    let filtered = roles;
    if (searchText) {
      filtered = filtered.filter((role) =>
        role.name.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    setFilteredRoles(filtered);
  }, [searchText, roles]);

  // 表格列定義
  const columns: ColumnsType<Role> = [
    {
      title: "角色名稱",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 找出所有父層節點
      const parentKeys = new Set<string>();
      const findParentKeys = (nodes: SystemMenu[]) => {
        nodes.forEach(node => {
          if (node.children) {
            node.children.forEach(child => {
              if (selectedPermissions.includes(child.systemMenuId)) {
                parentKeys.add(node.systemMenuId);
              }
            });
            findParentKeys(node.children);
          }
        });
      };
      findParentKeys(menuTree);

      // 合併所有選中的權限（包括父層）
      const allSelectedKeys = [...new Set([...selectedPermissions, ...parentKeys])];

      const submittingValues = {
        rolesId: editingRole?.rolesId || "",
        name: values.name,
        rolesPermissions: allSelectedKeys.map(systemMenuId => ({
          rolesPermissionsId: "",
          rolesId: "",
          systemMenuId: systemMenuId
        }))
      };

      const [response] = await Promise.all([
        editingRole
          ? editRole(submittingValues)
          : AddRole(submittingValues)
      ]);

      if (response.success) {
        notifySuccess(
          editingRole ? "更新成功" : "新增成功",
          `角色已${editingRole ? "更新" : "新增"}`
        );
        await Promise.all([
          loadRoles(),
          loadMenuTree()
        ]);
        setIsModalVisible(false);
        form.resetFields();
        setSelectedPermissions([]);
      } else {
        notifyError(
          editingRole ? "更新失敗" : "新增失敗",
          response.message
        );
      }
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (role: Role) => {
    setEditingRole(role);
    form.setFieldsValue(role);
    // 設置已選中的權限
    if (role.rolesPermissions) {
      // 找出所有父節點
      const parentNodes = new Set<string>();
      const findParentNodes = (nodes: SystemMenu[]) => {
        nodes.forEach(node => {
          if (node.children) {
            node.children.forEach(child => {
              if (role.rolesPermissions?.some(p => p.systemMenuId === child.systemMenuId)) {
                parentNodes.add(node.systemMenuId);
              }
            });
            findParentNodes(node.children);
          }
        });
      };
      findParentNodes(menuTree);

      // 過濾掉父節點，只保留實際被選中的節點
      const selectedKeys = role.rolesPermissions
        .map(p => p.systemMenuId)
        .filter(menuId => !parentNodes.has(menuId));

      setSelectedPermissions(selectedKeys);
    } else {
      setSelectedPermissions([]);
    }
    setIsModalVisible(true);
  };

  // 處理節點點擊
  const handleNodeClick = (node: SystemMenu) => {
    // 只處理當前節點的選擇狀態
    const isCurrentlySelected = selectedPermissions.includes(node.systemMenuId);
    const newChecked = isCurrentlySelected
      ? selectedPermissions.filter(key => key !== node.systemMenuId)
      : [...selectedPermissions, node.systemMenuId];

    setSelectedPermissions(newChecked);
  };

  // 處理權限選擇
  const handleCheck = (checked: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    const checkedKeys = Array.isArray(checked) ? checked : checked.checked;
    const halfCheckedKeys = Array.isArray(checked) ? [] : checked.halfChecked;
    
    // 合併全選和半選的節點
    const allCheckedKeys = [...new Set([...checkedKeys, ...halfCheckedKeys])];
    setSelectedPermissions(allCheckedKeys as string[]);
  };

  // 處理節點展開/收起
  const handleExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
  };

  // 處理刪除
  const handleDelete = (role: Role) => {
    setRoleToDelete(role);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!roleToDelete) return;
    try {
        const response = await deleteRole(roleToDelete);
        if (response.success) {
            notifySuccess("刪除成功", "角色已刪除");
            loadRoles();
            setDeleteModalVisible(false);
            setRoleToDelete(null);
            setDeleteConfirmText("");
        } else {
            notifyError("刪除失敗", response.message);
        }
    } catch (error) {
        notifyError("刪除失敗", "請稍後再試");
    }
  };
  
  return (
    <Card title="角色管理">
      <Space style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingRole(null);
            form.resetFields();
            setIsModalVisible(true);
          }}
        >
          新增角色
        </Button>
        <Input
          placeholder="搜尋角色名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </Space>

      <Table
        columns={columns}
        dataSource={filteredRoles}
        rowKey="rolesId"
        loading={loading}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
        }}
      />

      {/* 新增/編輯表單 */}
      <Modal
        title={editingRole ? "編輯角色" : "新增角色"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
            setIsModalVisible(false);
            form.resetFields();
            setSelectedPermissions([]);
        }}
        okText="確認"
        cancelText="取消"
        width={800}
        >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
            <Form.Item
            name="name"
            label="角色名稱"
            rules={[{ required: true, message: "請輸入角色名稱" }]}
            >
            <Input placeholder="請輸入角色名稱" />
            </Form.Item>
            <Form.Item
            label="權限設定"
            required
            >
            <Tree
                checkable
                checkedKeys={selectedPermissions}
                expandedKeys={expandedKeys}
                onExpand={handleExpand}
                onCheck={handleCheck}
                defaultExpandAll={true}
                treeData={menuTree}
                height={400}
                style={{ overflow: 'auto' }}
                titleRender={(node) => (
                  <span
                    style={{ cursor: 'pointer' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleNodeClick(node);
                    }}
                  >
                    {node.label}
                  </span>
                )}
                fieldNames={{
                  title: 'label',
                  key: 'systemMenuId',
                  children: 'children'
                }}
            />
            </Form.Item>
        </Form>
        </Modal>

      {/* 刪除確認對話框 */}
      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setRoleToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled: deleteConfirmText !== (roleToDelete?.name || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{roleToDelete?.name}」</strong>以確認刪除：
          </p>
          <Input
            placeholder="請輸入角色名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default SecurityPage;
