using FAST_ERP_Backend.Models.Pms;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    /// <summary>
    /// 廠商修繕作業服務介面
    /// </summary>
    public interface IVendorMaintenanceService
    {
        /// <summary>
        /// 取得修繕申請列表
        /// </summary>
        /// <param name="status">狀態篩選</param>
        /// <param name="maintenanceType">修繕類型篩選</param>
        /// <param name="urgencyLevel">緊急程度篩選</param>
        /// <param name="startDate">開始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>修繕申請列表</returns>
        Task<List<VendorMaintenanceDTO>> GetVendorMaintenanceListAsync(
            string? status = null,
            string? maintenanceType = null,
            string? urgencyLevel = null,
            long? startDate = null,
            long? endDate = null);

        /// <summary>
        /// 根據修繕單號取得詳細資料
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <returns>修繕申請詳細資料</returns>
        Task<VendorMaintenanceDTO?> GetVendorMaintenanceByNumberAsync(string maintenanceNumber);

        /// <summary>
        /// 新增修繕申請
        /// </summary>
        /// <param name="vendorMaintenanceDto">修繕申請資料</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> CreateVendorMaintenanceAsync(VendorMaintenanceDTO vendorMaintenanceDto);

        /// <summary>
        /// 修改修繕申請
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="vendorMaintenanceDto">修繕申請資料</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> UpdateVendorMaintenanceAsync(string maintenanceNumber, VendorMaintenanceDTO vendorMaintenanceDto);

        /// <summary>
        /// 刪除修繕申請
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> DeleteVendorMaintenanceAsync(string maintenanceNumber);

        /// <summary>
        /// 審核修繕申請
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="isApproved">是否核准</param>
        /// <param name="reason">審核意見</param>
        /// <param name="approverId">審核人員工編號</param>
        /// <param name="approverName">審核人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> ApproveVendorMaintenanceAsync(string maintenanceNumber, bool isApproved, string? reason, string approverId, string approverName);

        /// <summary>
        /// 指派廠商
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="vendorId">廠商編號</param>
        /// <param name="vendorName">廠商名稱</param>
        /// <param name="vendorContact">廠商聯絡人</param>
        /// <param name="vendorPhone">廠商聯絡電話</param>
        /// <param name="scheduledStartDate">預計開始日期</param>
        /// <param name="scheduledEndDate">預計完成日期</param>
        /// <param name="operatorId">操作人員工編號</param>
        /// <param name="operatorName">操作人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> AssignVendorAsync(string maintenanceNumber, string vendorId, string vendorName,
            string? vendorContact, string? vendorPhone, long? scheduledStartDate, long? scheduledEndDate,
            string operatorId, string operatorName);

        /// <summary>
        /// 開始施工
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="actualStartDate">實際開始日期</param>
        /// <param name="operatorId">操作人員工編號</param>
        /// <param name="operatorName">操作人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> StartMaintenanceAsync(string maintenanceNumber, long actualStartDate, string operatorId, string operatorName);

        /// <summary>
        /// 完成修繕
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="actualEndDate">實際完成日期</param>
        /// <param name="actualCost">實際費用</param>
        /// <param name="maintenanceResult">修繕結果</param>
        /// <param name="operatorId">操作人員工編號</param>
        /// <param name="operatorName">操作人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> CompleteMaintenanceAsync(string maintenanceNumber, long actualEndDate,
            decimal? actualCost, string? maintenanceResult, string operatorId, string operatorName);

        /// <summary>
        /// 驗收修繕
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="inspectionResult">驗收結果</param>
        /// <param name="inspectionNotes">驗收備註</param>
        /// <param name="inspectorId">驗收人員工編號</param>
        /// <param name="inspectorName">驗收人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> InspectMaintenanceAsync(string maintenanceNumber, string inspectionResult,
            string? inspectionNotes, string inspectorId, string inspectorName);

        /// <summary>
        /// 結案
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="operatorId">操作人員工編號</param>
        /// <param name="operatorName">操作人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> CloseMaintenanceAsync(string maintenanceNumber, string operatorId, string operatorName);

        /// <summary>
        /// 取消修繕
        /// </summary>
        /// <param name="maintenanceNumber">修繕單號</param>
        /// <param name="reason">取消原因</param>
        /// <param name="operatorId">操作人員工編號</param>
        /// <param name="operatorName">操作人姓名</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> CancelMaintenanceAsync(string maintenanceNumber, string reason, string operatorId, string operatorName);

        /// <summary>
        /// 批次處理修繕申請
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>操作結果</returns>
        Task<(bool success, string message)> BatchProcessAsync(VendorMaintenanceBatchDTO batchDto);

        /// <summary>
        /// 取得修繕統計資料
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>統計資料</returns>
        Task<VendorMaintenanceStatisticsDTO> GetStatisticsAsync(long? startDate = null, long? endDate = null);

        /// <summary>
        /// 產生修繕單號
        /// </summary>
        /// <returns>修繕單號</returns>
        Task<string> GenerateMaintenanceNumberAsync();

        /// <summary>
        /// 檢查逾期修繕案件
        /// </summary>
        /// <returns>逾期案件列表</returns>
        Task<List<VendorMaintenanceDTO>> GetOverdueMaintenanceAsync();
    }
}