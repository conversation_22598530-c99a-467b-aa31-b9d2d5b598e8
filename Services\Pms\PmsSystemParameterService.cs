using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pms.Enums;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class PmsSystemParameterService : IPmsSystemParameterService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;
        private readonly IParameterValueService _parameterValueService;

        public PmsSystemParameterService(ERPDbContext context, IMapper mapper, IParameterValueService parameterValueService)
        {
            _context = context;
            _mapper = mapper;
            _parameterValueService = parameterValueService;
        }

        /// <summary>
        /// 取得所有系統參數
        /// </summary>
        public async Task<List<PmsSystemParameterDTO>> GetAllParametersAsync()
        {
            var parameters = await _context.Pms_SystemParameters
                .Where(p => !p.IsDeleted)
                .OrderBy(p => p.SortOrder)
                .ToListAsync();

            return _mapper.Map<List<PmsSystemParameterDTO>>(parameters);
        }

        /// <summary>
        /// 依照參數類型取得系統參數
        /// </summary>
        public async Task<List<PmsSystemParameterDTO>> GetParametersByTypeAsync(PmsParameterType parameterType)
        {
            var parameters = await _context.Pms_SystemParameters
                .Where(p => !p.IsDeleted && p.ParameterType == parameterType.ToString())
                .OrderBy(p => p.SortOrder)
                .ToListAsync();

            return _mapper.Map<List<PmsSystemParameterDTO>>(parameters);
        }

        /// <summary>
        /// 依照參數ID取得系統參數
        /// </summary>
        public async Task<PmsSystemParameterDTO> GetParameterByIdAsync(string parameterId)
        {
            // 檢查參數ID是否有效
            if (!Guid.TryParse(parameterId, out Guid id))
            {
                return null;
            }

            var parameter = await _context.Pms_SystemParameters
                .FirstOrDefaultAsync(p => p.ParameterId == id && !p.IsDeleted);

            return _mapper.Map<PmsSystemParameterDTO>(parameter);
        }

        /// <summary>
        /// 新增系統參數
        /// </summary>
        public async Task<(bool success, string message)> AddParameterAsync(PmsSystemParameterDTO parameter)
        {
            // 開始交易
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 驗證參數
                if (string.IsNullOrEmpty(parameter.ParameterName))
                {
                    return (false, "參數名稱不能為空");
                }

                if (string.IsNullOrEmpty(parameter.ParameterValue))
                {
                    return (false, "參數值不能為空");
                }

                if (string.IsNullOrEmpty(parameter.ParameterType))
                {
                    return (false, "參數類型不能為空");
                }

                // 驗證參數類型是否有效
                if (!Enum.TryParse<PmsParameterType>(parameter.ParameterType, out _))
                {
                    return (false, $"無效的參數類型: {parameter.ParameterType}");
                }

                // 檢查參數是否已存在
                var existingParameter = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterName == parameter.ParameterName && !p.IsDeleted);

                if (existingParameter != null)
                {
                    return (false, "參數名稱已存在");
                }

                // 新增參數
                parameter.ParameterId = Guid.NewGuid();
                parameter.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                parameter.IsDeleted = false;

                var newParameter = _mapper.Map<PmsSystemParameter>(parameter);
                await _context.Pms_SystemParameters.AddAsync(newParameter);
                await _context.SaveChangesAsync();

                // 提交交易
                await transaction.CommitAsync();
                return (true, "新增系統參數成功");
            }
            catch (Exception ex)
            {
                // 回復交易
                await transaction.RollbackAsync();
                return (false, $"新增系統參數失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯系統參數
        /// </summary>
        public async Task<(bool success, string message)> EditParameterAsync(PmsSystemParameterDTO parameter)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 驗證參數
                if (parameter.ParameterId == Guid.Empty)
                {
                    return (false, "無效的參數ID");
                }


                var existingParameter = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterId == parameter.ParameterId && !p.IsDeleted);

                if (existingParameter == null)
                {
                    return (false, "找不到指定的系統參數");
                }

                // 檢查是否有相同名稱但不同ID的參數
                var duplicateParameter = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterName == parameter.ParameterName &&
                                            p.ParameterId != parameter.ParameterId &&
                                            !p.IsDeleted);

                if (duplicateParameter != null)
                {
                    return (false, "已存在相同名稱的參數");
                }

                // 保留原始的建立時間和建立者
                parameter.CreateTime = existingParameter.CreateTime;
                parameter.CreateUserId = existingParameter.CreateUserId;

                // 設定更新時間
                parameter.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // 保留刪除狀態
                parameter.IsDeleted = existingParameter.IsDeleted;
                parameter.DeleteTime = existingParameter.DeleteTime;
                parameter.DeleteUserId = existingParameter.DeleteUserId;

                _mapper.Map(parameter, existingParameter);

                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "更新系統參數成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新系統參數失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除系統參數
        /// </summary>
        public async Task<(bool success, string message)> DeleteParameterAsync(PmsSystemParameterDTO parameter)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 驗證參數
                if (parameter.ParameterId == Guid.Empty)
                {
                    return (false, "無效的參數ID");
                }

                var existingParameter = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterId == parameter.ParameterId && !p.IsDeleted);

                if (existingParameter == null)
                {
                    return (false, "找不到指定的系統參數");
                }

                // 標記為已刪除
                existingParameter.IsDeleted = true;
                existingParameter.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                existingParameter.DeleteUserId = parameter.DeleteUserId;

                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "刪除系統參數成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除系統參數失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得折舊法設定
        /// </summary>
        public async Task<List<PmsSystemParameterDTO>> GetDepreciationMethodsAsync()
        {
            return await GetParametersByTypeAsync(PmsParameterType.depreciation_method);
        }

        /// <summary>
        /// 設定默認折舊法
        /// </summary>
        public async Task<(bool success, string message)> SetDefaultDepreciationMethodAsync(string methodId)
        {
            try
            {
                if (!Guid.TryParse(methodId, out Guid id))
                {
                    return (false, "無效的參數ID");
                }

                // 檢查折舊法是否存在
                var depreciationMethod = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterId == id &&
                                            p.ParameterType == PmsParameterType.depreciation_method.ToString() &&
                                            !p.IsDeleted);

                if (depreciationMethod == null)
                {
                    return (false, "找不到指定的折舊法設定");
                }

                // 先將所有折舊法的設定取消預設
                var allDepreciationMethods = await _context.Pms_SystemParameters
                    .Where(p => p.ParameterType == PmsParameterType.depreciation_method.ToString() && !p.IsDeleted)
                    .ToListAsync();

                foreach (var method in allDepreciationMethods)
                {
                    if (method.ParameterId != id && _parameterValueService.ContainsProperty(method.ParameterValue, "isDefault") &&
                        _parameterValueService.GetValueFromJson<bool>(method.ParameterValue, "isDefault", false))
                    {
                        // 更新參數值，將isDefault設為false
                        method.ParameterValue = _parameterValueService.UpdateJsonProperty(method.ParameterValue, "isDefault", false);
                        method.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    }
                }

                // 設定選定的折舊法為預設
                if (!_parameterValueService.ContainsProperty(depreciationMethod.ParameterValue, "isDefault") ||
                    !_parameterValueService.GetValueFromJson<bool>(depreciationMethod.ParameterValue, "isDefault", false))
                {
                    depreciationMethod.ParameterValue = _parameterValueService.UpdateJsonProperty(depreciationMethod.ParameterValue, "isDefault", true);
                    depreciationMethod.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                await _context.SaveChangesAsync();
                return (true, "設定默認折舊法成功");
            }
            catch (Exception ex)
            {
                return (false, $"設定默認折舊法失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 為特定財產科目設定餘額遞減法折舊率
        /// </summary>
        /// <param name="assetAccountId">財產科目ID</param>
        /// <param name="rate">折舊率（百分比）</param>
        /// <param name="userId">操作用戶ID</param>
        /// <returns>成功與否及訊息</returns>
        public async Task<(bool success, string message)> SetDecliningBalanceRateForAssetAccountAsync(string assetAccountId, decimal rate, string userId)
        {
            try
            {
                if (!Guid.TryParse(assetAccountId, out Guid accountId))
                {
                    return (false, "無效的財產科目ID");
                }

                if (!decimal.TryParse(rate.ToString(), out decimal rateValue))
                {
                    return (false, "折舊率必須為數字");
                }

                if (rateValue < 0)
                {
                    return (false, "折舊率必須大於0%");
                }

                // 將百分比轉換為小數（例如：40% -> 0.4）
                decimal decimalRate = _parameterValueService.ConvertFromPercentage(rate);

                var assetAccount = await _context.Pms_AssetAccounts
                    .FirstOrDefaultAsync(a => a.AssetAccountId == accountId && !a.IsDeleted);

                if (assetAccount == null)
                {
                    return (false, "找不到指定的財產科目");
                }

                var decliningBalanceMethod = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterType == PmsParameterType.depreciation_method.ToString() &&
                                            p.ParameterValue.Contains("declining_balance") &&
                                            !p.IsDeleted);

                if (decliningBalanceMethod == null)
                {
                    return (false, "找不到餘額遞減法設定");
                }

                var accountRateSetting = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterType == PmsParameterType.declining_balance_rate.ToString() &&
                                            p.ParameterValue.Contains($"\"assetAccountId\":\"{assetAccountId}\"") &&
                                            !p.IsDeleted);

                string parameterValue = JsonConvert.SerializeObject(new
                {
                    assetAccountId = assetAccountId,
                    assetAccountName = assetAccount.AssetAccountName,
                    rate = decimalRate
                });

                if (accountRateSetting == null)
                {
                    var newSetting = new PmsSystemParameter
                    {
                        ParameterName = $"餘額遞減法折舊率-{assetAccount.AssetAccountName}",
                        ParameterValue = parameterValue,
                        ParameterDescription = $"針對財產科目 '{assetAccount.AssetAccountName}' 的餘額遞減法折舊率設定",
                        ParameterType = PmsParameterType.declining_balance_rate.ToString(),
                        IsEnabled = true,
                        SortOrder = 0,
                        CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                        CreateUserId = userId
                    };

                    await _context.Pms_SystemParameters.AddAsync(newSetting);
                }
                else
                {
                    accountRateSetting.ParameterValue = parameterValue;
                    accountRateSetting.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    accountRateSetting.UpdateUserId = userId;
                }

                await _context.SaveChangesAsync();
                return (true, $"已成功設定財產科目 '{assetAccount.AssetAccountName}' 的餘額遞減法折舊率為 {rate}%");
            }
            catch (Exception ex)
            {
                return (false, $"設定餘額遞減法折舊率失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得特定財產科目的餘額遞減法折舊率
        /// </summary>
        /// <param name="assetAccountId">財產科目ID</param>
        /// <returns>折舊率設定（百分比）</returns>
        public async Task<(bool success, decimal rate, string message)> GetDecliningBalanceRateForAssetAccountAsync(string assetAccountId)
        {
            try
            {
                if (!Guid.TryParse(assetAccountId, out Guid accountId))
                {
                    return (false, 0, "無效的財產科目ID");
                }

                var assetAccount = await _context.Pms_AssetAccounts
                    .FirstOrDefaultAsync(a => a.AssetAccountId == accountId && !a.IsDeleted);

                if (assetAccount == null)
                {
                    return (false, 0, "找不到指定的財產科目");
                }

                var accountRateSetting = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterType == PmsParameterType.declining_balance_rate.ToString() &&
                                            p.ParameterValue.Contains($"\"assetAccountId\":\"{assetAccountId}\"") &&
                                            !p.IsDeleted);

                if (accountRateSetting == null)
                {
                    var defaultMethod = await _context.Pms_SystemParameters
                        .FirstOrDefaultAsync(p => p.ParameterType == PmsParameterType.depreciation_method.ToString() &&
                                                p.ParameterValue.Contains("declining_balance") &&
                                                !p.IsDeleted);

                    if (defaultMethod == null)
                    {
                        return (false, 0, "找不到餘額遞減法預設設定");
                    }

                    try
                    {
                        var defaultRate = _parameterValueService.GetValueFromJson<decimal>(defaultMethod.ParameterValue, "rate", 2.0m);
                        // 將小數轉換為百分比
                        var percentageRate = _parameterValueService.ConvertToPercentage(defaultRate);
                        return (true, percentageRate, $"使用預設餘額遞減法折舊率: {percentageRate}%");
                    }
                    catch
                    {
                        return (false, 0, "無法解析餘額遞減法預設折舊率");
                    }
                }
                else
                {
                    try
                    {
                        var specificRate = _parameterValueService.GetValueFromJson<decimal>(accountRateSetting.ParameterValue, "rate", 2.0m);
                        // 將小數轉換為百分比
                        var percentageRate = _parameterValueService.ConvertToPercentage(specificRate);
                        return (true, percentageRate, $"財產科目 '{assetAccount.AssetAccountName}' 的餘額遞減法折舊率: {percentageRate}%");
                    }
                    catch
                    {
                        return (false, 0, "無法解析財產科目的餘額遞減法折舊率");
                    }
                }
            }
            catch (Exception ex)
            {
                return (false, 0, $"取得餘額遞減法折舊率失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得所有財產科目的餘額遞減法折舊率設定
        /// </summary>
        /// <returns>折舊率設定列表</returns>
        public async Task<List<PmsSystemParameterDTO>> GetAllDecliningBalanceRatesAsync()
        {
            var parameters = await _context.Pms_SystemParameters
                .Where(p => p.ParameterType == PmsParameterType.declining_balance_rate.ToString() && !p.IsDeleted)
                .OrderBy(p => p.SortOrder)
                .ToListAsync();

            var result = new List<PmsSystemParameterDTO>();

            foreach (var p in parameters)
            {
                try
                {
                    string assetAccountId = _parameterValueService.GetValueFromJson<string>(p.ParameterValue, "assetAccountId", "");
                    decimal rate = _parameterValueService.GetValueFromJson<decimal>(p.ParameterValue, "rate", 0);
                    string assetAccountName = "";

                    var assetAccount = await _context.Pms_AssetAccounts
                        .FirstOrDefaultAsync(a => a.AssetAccountId == Guid.Parse(assetAccountId) && !a.IsDeleted);

                    if (assetAccount != null)
                    {
                        assetAccountName = assetAccount.AssetAccountName;
                    }

                    // 建立新的物件，使用百分比轉換
                    var updatedValue = new
                    {
                        assetAccountId,
                        assetAccountName,
                        rate = _parameterValueService.ConvertToPercentage(rate)
                    };

                    p.ParameterValue = JsonConvert.SerializeObject(updatedValue);
                }
                catch (Exception)
                {
                    // 如果解析失敗，保持原始值不變
                }

                var dto = _mapper.Map<PmsSystemParameterDTO>(p);
                result.Add(dto);
            }

            return result;
        }

        /// <summary>
        /// 取得初始化狀態
        /// </summary>
        /// <returns>成功與否、是否已初始化、訊息</returns>
        public async Task<(bool success, bool isInitialized, string message)> GetInitializationStatusAsync()
        {
            try
            {
                var initializationStatus = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterType == PmsParameterType.initialization.ToString() && !p.IsDeleted);

                if (initializationStatus == null)
                {
                    return (false, false, "找不到初始化狀態");
                }

                bool isInitialized = _parameterValueService.GetValueFromJson<bool>(initializationStatus.ParameterValue, "isInitialized", false);
                string initializationDate = _parameterValueService.GetValueFromJson<string>(initializationStatus.ParameterValue, "initializationDate", null);

                string message = isInitialized
                    ? string.IsNullOrEmpty(initializationDate)
                        ? "系統已完成初始化"
                        : $"系統已於 {DateTimeOffset.Parse(initializationDate).ToString("yyyy/MM/dd HH:mm:ss")} 完成初始化"
                    : "系統尚未初始化";

                return (true, isInitialized, message);
            }
            catch (Exception ex)
            {
                return (false, false, $"解析初始化狀態失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 設定初始化狀態
        /// </summary>
        /// <param name="isInitialized">是否已初始化</param>
        /// <returns>成功與否及訊息</returns>
        public async Task<(bool success, string message)> SetInitializationStatusAsync(bool isInitialized)
        {
            try
            {
                var initializationStatus = await _context.Pms_SystemParameters
                    .FirstOrDefaultAsync(p => p.ParameterType == PmsParameterType.initialization.ToString() && !p.IsDeleted);

                if (initializationStatus == null)
                {
                    return (false, "找不到初始化狀態設定");
                }

                // 如果初始化狀態為true時不允許變更狀態
                bool currentInitStatus = _parameterValueService.GetValueFromJson<bool>(initializationStatus.ParameterValue, "isInitialized", false);
                if (currentInitStatus)
                {
                    return (false, "系統已經初始化，不允許變更狀態");
                }

                string parameterValue = JsonConvert.SerializeObject(new
                {
                    isInitialized = isInitialized,
                    initializationDate = isInitialized ? DateTimeOffset.UtcNow.ToString("o") : null
                });

                initializationStatus.ParameterValue = parameterValue;
                initializationStatus.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                string message = isInitialized
                    ? $"系統已於 {DateTimeOffset.UtcNow.ToString("yyyy/MM/dd HH:mm:ss")} 完成初始化"
                    : "系統初始化狀態已重置";

                return (true, message);
            }
            catch (Exception ex)
            {
                return (false, $"設定初始化狀態失敗: {ex.Message}");
            }
        }

    }
}