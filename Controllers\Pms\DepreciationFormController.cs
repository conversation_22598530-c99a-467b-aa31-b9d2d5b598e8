using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("固定資產折舊單")]
    public class DepreciationFormController : ControllerBase
    {
        private readonly IDepreciationFormService _depreciationFormService;

        public DepreciationFormController(IDepreciationFormService depreciationFormService)
        {
            _depreciationFormService = depreciationFormService;
        }

        /// <summary>
        /// 取得固定資產折舊單資料
        /// </summary>
        /// <returns>固定資產折舊單資料列表</returns>
        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得固定資產折舊單資料列表", Description = "取得所有固定資產折舊單資料")]
        public async Task<ActionResult<List<DepreciationFormDTO>>> GetDepreciationForms()
        {
            var result = await _depreciationFormService.GetDepreciationFormsAsync();
            return Ok(result);
        }

        /// <summary>
        /// 新增固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增固定資產折舊單", Description = "新增固定資產折舊單")]
        public async Task<ActionResult<string>> AddDepreciationForm([FromBody] DepreciationFormDTO _data)
        {
            var (success, message) = await _depreciationFormService.AddDepreciationFormAsync(_data);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 編輯固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯固定資產折舊單", Description = "編輯固定資產折舊單")]
        public async Task<ActionResult<string>> EditDepreciationForm([FromBody] DepreciationFormDTO _data)
        {
            var (success, message) = await _depreciationFormService.EditDepreciationFormAsync(_data);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 刪除固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除固定資產折舊單", Description = "刪除固定資產折舊單")]
        public async Task<ActionResult<string>> DeleteDepreciationForm([FromBody] DepreciationFormDTO _data)
        {
            var (success, message) = await _depreciationFormService.DeleteDepreciationFormAsync(_data);
            if (success)
                return Ok(message);
            return BadRequest(message);
        }

        /// <summary>
        /// 取得固定資產折舊單明細
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>固定資產折舊單明細</returns>
        [HttpGet("detail/{_depreciationId}")]
        [SwaggerOperation(Summary = "取得固定資產折舊單明細", Description = "取得固定資產折舊單明細")]
        public async Task<ActionResult<DepreciationFormDetailDTO>> GetDepreciationFormDetail(string _depreciationId)
        {
            var result = await _depreciationFormService.GetDepreciationFormDetailAsync(_depreciationId);
            if (result == null)
                return NotFound("找不到資料");
            return Ok(result);
        }

        /// <summary>
        /// 檢查折舊紀錄是否已被使用
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>是否已被使用</returns>
        [HttpGet("check/{_depreciationId}")]
        [SwaggerOperation(Summary = "檢查折舊紀錄是否已被使用", Description = "檢查折舊紀錄是否已被使用")]
        public async Task<ActionResult<bool>> IsDepreciationUsed(string _depreciationId)
        {
            var result = await _depreciationFormService.IsDepreciationUsedAsync(_depreciationId);
            return Ok(result);
        }
    }
}