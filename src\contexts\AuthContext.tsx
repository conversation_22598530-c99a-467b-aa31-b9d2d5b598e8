import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { routes } from "@/config/routes";
import { UserInfo, setUserInfo, clearUserInfo } from "@/utils/userInfo";
import { setToken, getToken, removeToken } from "@/utils/cookies";
import * as authService from "@/services/authService";
import {
  createSignalRConnection,
  stopSignalRConnection,
} from "@/services/common/signalRService";
import {
  startIdleTimeout,
  stopIdleTimeout,
  continueSession,
  IDLE_EVENTS,
} from "@/utils/idleTimeout";
import { siteConfig } from "@/config/site";
import { eventBus } from "@/utils/eventBus";
import dynamic from "next/dynamic";

// 動態引入閒置對話框組件
const IdleTimeoutDialog = dynamic(
  () => import("@/app/components/common/IdleTimeoutDialog"),
  { ssr: false }
);

// 閒置超時時間
const IDLE_TIMEOUT = siteConfig.auth.idleTimeout;

// AuthContext 類型
export interface AuthContextType {
  user: UserInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  getMyInfo: () => Promise<any>;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: async () => {},
  getMyInfo: async () => {},
});

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [showIdleWarning, setShowIdleWarning] = useState(false);
  const router = useRouter();

  // 獲取用戶資訊
  const getMyInfo = async () => {
    const result = await authService.getMyInfo();

    if (
      result.success &&
      result.data &&
      Array.isArray(result.data) &&
      result.data.length > 0
    ) {
      const userInfo = result.data[0];
      setUserInfo(userInfo);
      setUser(userInfo);
      return { success: true, data: userInfo };
    }

    return {
      success: false,
      message: result.message || "獲取用戶資訊失敗",
    };
  };

  // 啟動閒置超時監控
  const startIdleMonitoring = () => {
    startIdleTimeout(IDLE_TIMEOUT, handleIdleTimeout);
  };

  // 處理閒置超時
  const handleIdleTimeout = () => {
    // 閒置超時時，自動登出
    logout();
  };

  // 處理閒置警告對話框 - 繼續使用
  const handleContinueSession = () => {
    continueSession();
    setShowIdleWarning(false);
  };

  // 處理閒置警告對話框 - 登出
  const handleLogout = () => {
    setShowIdleWarning(false);
    logout();
  };

  // 監聽閒置事件
  useEffect(() => {
    // 監聽顯示警告對話框事件
    eventBus.on(IDLE_EVENTS.SHOW_WARNING, () => {
      setShowIdleWarning(true);
    });

    // 監聽隱藏警告對話框事件
    eventBus.on(IDLE_EVENTS.HIDE_WARNING, () => {
      setShowIdleWarning(false);
    });

    // 監聽閒置超時事件
    eventBus.on(IDLE_EVENTS.TIMEOUT, handleIdleTimeout);

    return () => {
      // 清理事件監聽器
      eventBus.off(IDLE_EVENTS.SHOW_WARNING);
      eventBus.off(IDLE_EVENTS.HIDE_WARNING);
      eventBus.off(IDLE_EVENTS.TIMEOUT);
    };
  }, []);

  // 檢查是否已登入
  useEffect(() => {
    const checkAuth = async () => {
      const token = getToken();
      if (token) {
        try {
          const result = await getMyInfo();
          if (result.success) {
            setIsAuthenticated(true);
            // 登入成功後啟動閒置監控
            startIdleMonitoring();
          } else {
            removeToken();
            clearUserInfo();
          }
        } catch (error) {
          removeToken();
          clearUserInfo();
        }
      }
      setIsLoading(false);
    };

    checkAuth();

    // 組件卸載時停止閒置監控
    return () => {
      stopIdleTimeout();
    };
  }, []);

  // 登入
  const login = async (account: string, password: string) => {
    try {
      const loginResult = await authService.login(account, password);

      if (!loginResult.success) {
        return loginResult;
      }

      const { token } = loginResult.data;
      if (!token) {
        return {
          success: false,
          message: "未收到有效的認證資訊",
        };
      }

      // 儲存 token
      setToken(token);

      // 獲取用戶資訊
      const userResult = await getMyInfo();
      if (userResult.success) {
        setIsAuthenticated(true);

        // 登入成功連線signalR
        createSignalRConnection();

        // 啟動閒置監控
        startIdleMonitoring();

        return {
          success: true,
          data: userResult.data,
        };
      }

      // 如果獲取用戶資訊失敗，清除 token
      removeToken();
      clearUserInfo();
      return {
        success: false,
        message: userResult.message || "獲取用戶資訊失敗",
      };
    } catch (error) {
      console.error("登入錯誤:", error);
      return {
        success: false,
        message: "網路連線異常",
      };
    }
  };

  // 登出
  const logout = async () => {
    // 登出中斷signalR連線
    stopSignalRConnection();

    // 停止閒置監控
    stopIdleTimeout();

    removeToken();
    clearUserInfo();
    setUser(null);
    setIsAuthenticated(false);
    router.push(routes.login);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        getMyInfo,
      }}
    >
      {children}

      {/* 閒置警告對話框 */}
      {isAuthenticated && (
        <IdleTimeoutDialog
          open={showIdleWarning}
          onLogout={handleLogout}
          onContinue={handleContinueSession}
          countdownDuration={siteConfig.auth.idleCountdownDuration}
        />
      )}
    </AuthContext.Provider>
  );
}

// 自定義 hook 用於在元件中使用 AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
