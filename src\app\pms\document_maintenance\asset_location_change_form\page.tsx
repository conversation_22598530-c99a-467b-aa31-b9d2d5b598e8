"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  Col,
  message,
  DatePicker,
  Modal,
  Tag,
  Typography,
  Statistic,
  Tooltip,
  Dropdown,
  Badge,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DownloadOutlined,
  FilterOutlined,
  SwapOutlined,
  MoreOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { getCookie } from "@/utils/cookies";

// 引入服務和介面
import {
  getAssetLocationTransfers,
  createAssetLocationTransfer,
  updateAssetLocationTransfer,
  deleteAssetLocationTransfer,
  approveAssetLocationTransfer,
  executeAssetLocationTransfer,
  AssetLocationTransfer,
  AssetLocationTransferWithDetails,
  AssetLocationTransferQuery,
} from "@/services/pms/assetLocationTransferService";

// 引入配置和介面
import {
  APPROVAL_STATUS_OPTIONS,
  EXECUTION_STATUS_OPTIONS,
  TABLE_CONFIG,
  DEFAULT_VALUES,
  MESSAGES,
  STATUS_COLORS,
} from "./config";

import {
  AssetLocationChangeFormQuery,
  TransferStatistics,
  getApprovalStatusText,
  getExecutionStatusText,
  getApprovalStatusColor,
  getExecutionStatusColor,
} from "./interface";

import { getMainTableColumns } from "./columns";
import TransferForm from "./Form";
import styles from "./page.module.css";

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { confirm } = Modal;

// 主要組件
const AssetLocationChangeFormPage: React.FC = () => {
  // 狀態管理
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<AssetLocationTransfer[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(DEFAULT_VALUES.pageSize);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<AssetLocationTransfer[]>([]);
  const [statistics, setStatistics] = useState<TransferStatistics>({
    totalCount: 0,
    pendingApprovalCount: 0,
    approvedCount: 0,
    rejectedCount: 0,
    pendingExecutionCount: 0,
    completedCount: 0,
    cancelledCount: 0,
    thisMonthCount: 0,
    thisYearCount: 0,
  });

  // 表單和模態框狀態
  const [formVisible, setFormVisible] = useState(false);
  const [editingTransfer, setEditingTransfer] =
    useState<AssetLocationTransferWithDetails | null>(null);
  const [isViewMode, setIsViewMode] = useState(false);
  const [approvalVisible, setApprovalVisible] = useState(false);
  const [executionVisible, setExecutionVisible] = useState(false);
  const [selectedTransfer, setSelectedTransfer] =
    useState<AssetLocationTransfer | null>(null);

  // 搜尋表單
  const [searchForm] = Form.useForm();
  const [approvalForm] = Form.useForm();
  const [executionForm] = Form.useForm();
  const [transferForm] = Form.useForm();

  // 當前用戶資訊
  const currentUserId = getCookie("userId") || "";
  const currentUserName = getCookie("userName") || "";

  // 載入資料
  const loadData = useCallback(
    async (query?: AssetLocationChangeFormQuery) => {
      setLoading(true);
      try {
        const searchValues = searchForm.getFieldsValue();
        const params: AssetLocationTransferQuery = {
          searchTerm: query?.keyword || searchValues.keyword,
          approvalStatus: query?.approvalStatus || searchValues.approvalStatus,
          executionStatus:
            query?.executionStatus || searchValues.executionStatus,
          startDate:
            query?.startDate ||
            (searchValues.dateRange?.[0]
              ? dayjs(searchValues.dateRange[0]).valueOf()
              : undefined),
          endDate:
            query?.endDate ||
            (searchValues.dateRange?.[1]
              ? dayjs(searchValues.dateRange[1]).valueOf()
              : undefined),
        };

        const response = await getAssetLocationTransfers(params);

        if (response.success && response.data) {
          setData(response.data);
          setTotal(response.data.length);

          // 計算統計資料
          const stats = calculateStatistics(response.data);
          setStatistics(stats);
        } else {
          message.error(response.message || MESSAGES.ERROR.NETWORK);
          setData([]);
          setTotal(0);
        }
      } catch (error) {
        console.error("載入資料失敗:", error);
        message.error(MESSAGES.ERROR.NETWORK);
      } finally {
        setLoading(false);
      }
    },
    [searchForm]
  );

  // 計算統計資料
  const calculateStatistics = (
    transfers: AssetLocationTransfer[]
  ): TransferStatistics => {
    const now = dayjs();
    const thisMonth = now.startOf("month");
    const thisYear = now.startOf("year");

    return {
      totalCount: transfers.length,
      pendingApprovalCount: transfers.filter(
        (t) => t.approvalStatus === "PENDING"
      ).length,
      approvedCount: transfers.filter((t) => t.approvalStatus === "APPROVED")
        .length,
      rejectedCount: transfers.filter((t) => t.approvalStatus === "REJECTED")
        .length,
      pendingExecutionCount: transfers.filter(
        (t) => t.executionStatus === "PENDING"
      ).length,
      completedCount: transfers.filter((t) => t.executionStatus === "COMPLETED")
        .length,
      cancelledCount: transfers.filter((t) => t.executionStatus === "CANCELLED")
        .length,
      thisMonthCount: transfers.filter((t) =>
        dayjs(t.transferDate).isAfter(thisMonth)
      ).length,
      thisYearCount: transfers.filter((t) =>
        dayjs(t.transferDate).isAfter(thisYear)
      ).length,
    };
  };

  // 初始載入
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 搜尋處理
  const handleSearch = () => {
    setCurrent(1);
    loadData();
  };

  // 重置搜尋
  const handleReset = () => {
    searchForm.resetFields();
    setCurrent(1);
    loadData();
  };

  // 新增
  const handleAdd = () => {
    setEditingTransfer(null);
    setIsViewMode(false);
    setFormVisible(true);
  };

  // 編輯
  const handleEdit = (record: AssetLocationTransfer) => {
    // 這裡需要獲取完整的資料包含明細
    setEditingTransfer({ transfer: record, details: [] });
    setIsViewMode(false);
    setFormVisible(true);
  };

  // 檢視
  const handleView = (record: AssetLocationTransfer) => {
    setEditingTransfer({ transfer: record, details: [] });
    setIsViewMode(true);
    setFormVisible(true);
  };

  // 刪除
  const handleDelete = (record: AssetLocationTransfer) => {
    confirm({
      title: MESSAGES.CONFIRM.DELETE,
      content: `變動單號：${record.transferNo}`,
      okText: "確定",
      cancelText: "取消",
      okType: "danger",
      onOk: async () => {
        try {
          const response = await deleteAssetLocationTransfer(record.transferNo);
          if (response.success) {
            message.success(MESSAGES.SUCCESS.DELETE);
            loadData();
          } else {
            message.error(response.message || MESSAGES.ERROR.DELETE);
          }
        } catch (error) {
          console.error("刪除失敗:", error);
          message.error(MESSAGES.ERROR.DELETE);
        }
      },
    });
  };

  // 審核
  const handleApprove = (record: AssetLocationTransfer) => {
    setSelectedTransfer(record);
    approvalForm.setFieldsValue({
      transferNo: record.transferNo,
      approvalStatus: "APPROVED",
      approvalComments: "",
      approverId: currentUserId,
    });
    setApprovalVisible(true);
  };

  // 駁回
  const handleReject = (record: AssetLocationTransfer) => {
    setSelectedTransfer(record);
    approvalForm.setFieldsValue({
      transferNo: record.transferNo,
      approvalStatus: "REJECTED",
      approvalComments: "",
      approverId: currentUserId,
    });
    setApprovalVisible(true);
  };

  // 執行
  const handleExecute = (record: AssetLocationTransfer) => {
    setSelectedTransfer(record);
    executionForm.setFieldsValue({
      transferNo: record.transferNo,
      executionStatus: "COMPLETED",
      executorId: currentUserId,
    });
    setExecutionVisible(true);
  };

  // 取消執行
  const handleCancel = (record: AssetLocationTransfer) => {
    setSelectedTransfer(record);
    executionForm.setFieldsValue({
      transferNo: record.transferNo,
      executionStatus: "CANCELLED",
      executorId: currentUserId,
    });
    setExecutionVisible(true);
  };

  // 審核提交
  const handleApprovalSubmit = async () => {
    try {
      const values = await approvalForm.validateFields();
      if (!selectedTransfer) return;

      const response = await approveAssetLocationTransfer(
        selectedTransfer.transferNo,
        values
      );
      if (response.success) {
        message.success(MESSAGES.SUCCESS.APPROVE);
        setApprovalVisible(false);
        loadData();
      } else {
        message.error(response.message || MESSAGES.ERROR.APPROVE);
      }
    } catch (error) {
      console.error("審核失敗:", error);
      message.error(MESSAGES.ERROR.APPROVE);
    }
  };

  // 執行提交
  const handleExecutionSubmit = async () => {
    try {
      const values = await executionForm.validateFields();
      if (!selectedTransfer) return;

      const response = await executeAssetLocationTransfer(
        selectedTransfer.transferNo,
        values
      );
      if (response.success) {
        message.success(MESSAGES.SUCCESS.EXECUTE);
        setExecutionVisible(false);
        loadData();
      } else {
        message.error(response.message || MESSAGES.ERROR.EXECUTE);
      }
    } catch (error) {
      console.error("執行失敗:", error);
      message.error(MESSAGES.ERROR.EXECUTE);
    }
  };

  // 表單成功回調
  const handleFormSuccess = () => {
    setFormVisible(false);
    loadData();
  };

  // 表格選擇
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: AssetLocationTransfer[]) => {
      setSelectedRowKeys(keys as string[]);
      setSelectedRows(rows);
    },
  };

  // 表格欄位
  const columns = getMainTableColumns(
    handleEdit,
    handleView,
    handleDelete,
    handleApprove,
    handleReject,
    handleExecute,
    handleCancel,
    currentUserId
  );

  // 分頁配置
  const pagination = {
    ...TABLE_CONFIG.pagination,
    current,
    pageSize,
    total,
    onChange: (page: number, size: number) => {
      setCurrent(page);
      setPageSize(size);
    },
  };

  return (
    <div className={styles.container}>
      {/* 頁面標題 */}
      <div className={styles.pageHeader}>
        <Title level={3} className={styles.pageTitle}>
          <SwapOutlined /> 財產位置變動單
        </Title>
      </div>

      {/* 統計區域 */}
      <div className={styles.statisticsSection}>
        <Card className={styles.statisticCard}>
          <Statistic
            title="總計"
            value={statistics.totalCount}
            valueStyle={{ color: "#1890ff" }}
          />
        </Card>
        <Card className={styles.statisticCard}>
          <Statistic
            title="待審核"
            value={statistics.pendingApprovalCount}
            valueStyle={{ color: "#fa8c16" }}
          />
        </Card>
        <Card className={styles.statisticCard}>
          <Statistic
            title="已審核"
            value={statistics.approvedCount}
            valueStyle={{ color: "#52c41a" }}
          />
        </Card>
        <Card className={styles.statisticCard}>
          <Statistic
            title="待執行"
            value={statistics.pendingExecutionCount}
            valueStyle={{ color: "#1890ff" }}
          />
        </Card>
        <Card className={styles.statisticCard}>
          <Statistic
            title="已完成"
            value={statistics.completedCount}
            valueStyle={{ color: "#52c41a" }}
          />
        </Card>
      </div>

      {/* 搜尋區域 */}
      <div className={styles.searchSection}>
        <Form form={searchForm} className={styles.searchForm}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="keyword" label="關鍵字">
                <Input
                  placeholder="變動單號、申請人等"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="approvalStatus" label="審核狀態">
                <Select placeholder="請選擇" allowClear>
                  {APPROVAL_STATUS_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="executionStatus" label="執行狀態">
                <Select placeholder="請選擇" allowClear>
                  {EXECUTION_STATUS_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="dateRange" label="日期範圍">
                <RangePicker format="YYYY-MM-DD" />
              </Form.Item>
            </Col>
          </Row>
          <div className={styles.searchActions}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </div>
        </Form>
      </div>

      {/* 表格區域 */}
      <div className={styles.tableSection}>
        <div className={styles.tableHeader}>
          <Title level={5} className={styles.tableTitle}>
            變動單列表
          </Title>
          <div className={styles.tableActions}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新增變動單
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => loadData()}>
                重新載入
              </Button>
            </Space>
          </div>
        </div>

        {/* 批次操作 */}
        {selectedRowKeys.length > 0 && (
          <div className={styles.batchActions}>
            <div className={styles.batchInfo}>
              已選擇 {selectedRowKeys.length} 項
            </div>
            <div className={styles.batchButtons}>
              <Space>
                <Button type="primary" icon={<CheckOutlined />} size="small">
                  批次審核
                </Button>
                <Button danger icon={<CloseOutlined />} size="small">
                  批次駁回
                </Button>
                <Button icon={<PlayCircleOutlined />} size="small">
                  批次執行
                </Button>
              </Space>
            </div>
          </div>
        )}

        <div className={styles.tableContent}>
          <Table
            {...TABLE_CONFIG}
            columns={columns}
            dataSource={data}
            loading={loading}
            rowSelection={rowSelection}
            rowKey="transferId"
            pagination={pagination}
          />
        </div>
      </div>

      {/* 表單模態框 */}
      <Modal
        title={
          isViewMode
            ? "檢視財產位置變動單"
            : editingTransfer
            ? "編輯財產位置變動單"
            : "新增財產位置變動單"
        }
        open={formVisible}
        onCancel={() => setFormVisible(false)}
        footer={null}
        width={1200}
        destroyOnHidden
        className={styles.formModal}
      >
        <TransferForm
          editingTransfer={editingTransfer}
          isViewMode={isViewMode}
          onCancel={() => setFormVisible(false)}
          onSuccess={handleFormSuccess}
          form={transferForm}
        />
      </Modal>

      {/* 審核模態框 */}
      <Modal
        title="審核財產位置變動單"
        open={approvalVisible}
        onOk={handleApprovalSubmit}
        onCancel={() => setApprovalVisible(false)}
        okText="確定"
        cancelText="取消"
      >
        <Form form={approvalForm} layout="vertical">
          <Form.Item name="transferNo" label="變動單號">
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="approvalStatus"
            label="審核結果"
            rules={[{ required: true, message: "請選擇審核結果" }]}
          >
            <Select>
              <Option value="APPROVED">通過</Option>
              <Option value="REJECTED">駁回</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="approvalComments"
            label="審核意見"
            rules={[
              { required: true, message: "請輸入審核意見" },
              { max: 500, message: "審核意見不得超過 500 字" },
            ]}
          >
            <Input.TextArea rows={4} placeholder="請輸入審核意見" />
          </Form.Item>
          <Form.Item name="approverId" hidden>
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 執行模態框 */}
      <Modal
        title="執行財產位置變動"
        open={executionVisible}
        onOk={handleExecutionSubmit}
        onCancel={() => setExecutionVisible(false)}
        okText="確定"
        cancelText="取消"
      >
        <Form form={executionForm} layout="vertical">
          <Form.Item name="transferNo" label="變動單號">
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="executionStatus"
            label="執行狀態"
            rules={[{ required: true, message: "請選擇執行狀態" }]}
          >
            <Select>
              <Option value="COMPLETED">執行完成</Option>
              <Option value="CANCELLED">取消執行</Option>
            </Select>
          </Form.Item>
          <Form.Item name="executorId" hidden>
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AssetLocationChangeFormPage;
