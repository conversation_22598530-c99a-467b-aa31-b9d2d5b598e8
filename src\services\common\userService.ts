import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 用戶介面
export interface User {
    userId: string;
    account: string;
    password: string;
    name: string;
    enterpriseGroupId: string;
    enterpriseGroupName: string | null;
    rolesId: string;
    rolesName: string | null;
    positionId: string;
    positionName: string | null;
    eMail: string;
    permanentAddress: string;
    mailingAddress: string;
    telNo: string;
    phone: string;
    altPhone: string;
    sortCode: number;
    unlockTime: string | null;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
}

export const createEmptyUser = (): User => ({
    userId: '',
    account: '',
    password: '',
    name: '',
    enterpriseGroupId: '',
    enterpriseGroupName: null,
    rolesId: '',
    rolesName: null,
    positionId: '',
    positionName: null,
    eMail: '',
    permanentAddress: '',
    mailingAddress: '',
    telNo: '',
    phone: '',
    altPhone: '',
    sortCode: 0,
    unlockTime: null,
    createTime: 0,
    createUserId: '',
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
});

// 更新用戶資料
export async function updateUser(userInfo: Partial<User>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editUsers, {
            method: "POST",
            body: JSON.stringify(userInfo),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新用戶信息失敗",
        };
    }
}

// 修改密碼
export async function changePassword(userId: string, oldPassword: string, newPassword: string): Promise<ApiResponse> {
    return await httpClient(apiEndpoints.changePassword, {
        method: "POST",
        body: JSON.stringify({ userId, oldPassword, newPassword }),
    });
}

// 獲取用戶列表
export async function getUsers(): Promise<ApiResponse<User[]>> {
    try {
        const response = await httpClient(apiEndpoints.getUsers, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取用戶列表失敗",
            data: []
        };
    }
}

// 新增用戶
export async function createUser(data: Partial<User>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addUser, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增用戶失敗",
        };
    }
}

// 刪除用戶
export async function deleteUser(userId: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteUser, {
            method: "POST",
            body: JSON.stringify({ userId }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除用戶失敗",
        };
    }
} 
