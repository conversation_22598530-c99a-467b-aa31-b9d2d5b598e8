import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 訊息介面
export interface Message {
    messageId: string;
    title: string;
    content: string;
    isRead: boolean;
    createTime: number;
    createUserId: string;
    readTime?: number;
}

// 獲取未讀訊息
export async function getUnreadMessages(): Promise<ApiResponse<Message[]>> {
    try {
        const response = await httpClient(apiEndpoints.getUnreadMessages, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取未讀訊息失敗",
            data: []
        };
    }
}

// 獲取已讀訊息
export async function getReadMessages(): Promise<ApiResponse<Message[]>> {
    try {
        const response = await httpClient(apiEndpoints.getReadMessages, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取已讀訊息失敗",
            data: []
        };
    }
}

// 標記訊息為已讀
export async function markMessageAsRead(messageId: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.markMessageAsRead, {
            method: "POST",
            body: JSON.stringify({ messageId }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "標記訊息已讀失敗",
        };
    }
} 