import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

export interface Unit {
    unitId: string;
    unitNo: string;
    name: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId: string | null;
    deleteTime?: number;
    deleteUserId: string | null;
}

// 獲取單位列表
export async function getUnits(): Promise<ApiResponse<Unit[]>> {
    try {
        const response = await httpClient(apiEndpoints.getUnits, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取單位列表失敗",
            data: []
        };
    }
}

// 獲取單位明細
export async function getUnitById(unitId: string): Promise<ApiResponse<Unit>> {
    try {
        const response = await httpClient(`${apiEndpoints.getUnitById}/${unitId}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取單位明細失敗",
        };
    }
}

// 新增單位
export async function createUnit(data: Partial<Unit>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addUnit, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增單位失敗",
        };
    }
}

// 更新單位
export async function updateUnit(data: Partial<Unit>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editUnit, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新單位失敗",
        };
    }
}

// 刪除單位
export async function deleteUnit(unitId: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteUnit, {
            method: "POST",
            body: JSON.stringify({ unitId }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除單位失敗",
        };
    }
}

