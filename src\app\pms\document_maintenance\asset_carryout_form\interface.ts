export interface AssetCarryOut {
    carryOutId: string;
    carryOutNo: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    applicantId: string;
    applicantName: string;
    applicationDate: number;
    plannedCarryOutDate: number;
    plannedReturnDate: number;
    actualCarryOutDate?: number;
    actualReturnDate?: number;
    purpose: string;
    destination: string;
    status: string;
    statusName: string;
    approverId?: string;
    approverName?: string;
    approvalDate?: number;
    approvalComment?: string;
    notes?: string;
    isDeleted: boolean;
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime?: number;
    deleteUserId?: string;
}

export interface AssetCarryOutQuery {
    status?: string;
    applicantId?: string;
    assetId?: string;
    startDate?: number;
    endDate?: number;
}

export interface CreateAssetCarryOutRequest {
    assetId: string;
    assetNo: string;
    assetName: string;
    applicantId: string;
    applicantName: string;
    applicationDate: number;
    plannedCarryOutDate: number;
    plannedReturnDate: number;
    purpose: string;
    destination: string;
    notes?: string;
}

export interface UpdateAssetCarryOutRequest {
    carryOutId: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    applicantId: string;
    applicantName: string;
    applicationDate: number;
    plannedCarryOutDate: number;
    plannedReturnDate: number;
    purpose: string;
    destination: string;
    notes?: string;
}

export interface ApprovalRequest {
    isApproved: boolean;
    approverId: string;
    comment: string;
}

export interface CarryOutRequest {
    actualCarryOutDate: number;
    operatorId: string;
}

export interface ReturnRequest {
    actualReturnDate: number;
    operatorId: string;
}

export interface BatchProcessRequest {
    carryOutIds: string[];
    action: string;
    approvalComment?: string;
    operatorId?: string;
}

export interface AssetCarryOutStatistics {
    totalCount: number;
    pendingCount: number;
    approvedCount: number;
    rejectedCount: number;
    carriedOutCount: number;
    returnedCount: number;
    overdueCount: number;
}

// 狀態顏色配置（參考廠商修繕申請）
export const STATUS_COLORS = {
    'PENDING': 'orange',        // 待審核
    '待審核': 'orange',
    'APPROVED': 'green',        // 已核准
    '已核准': 'green',
    'REJECTED': 'red',          // 已駁回
    '已駁回': 'red',
    'CARRIED_OUT': 'blue',      // 已攜出
    '已攜出': 'blue',
    'RETURNED': 'green',        // 已歸還
    '已歸還': 'green',
    'OVERDUE': 'red',           // 逾期
    '逾期': 'red',
    'CANCELLED': 'gray',        // 已取消
    '已取消': 'gray'
} as const;

export type StatusColorKey = keyof typeof STATUS_COLORS; 