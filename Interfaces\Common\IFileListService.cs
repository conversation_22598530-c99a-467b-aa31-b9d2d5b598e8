﻿using FAST_ERP_Backend.Models.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary> 檔案上傳服務介面 </summary>
    public interface IFileListService
    {
        /// <summary> 取得檔案列表 </summary>
        Task<List<FileListDTO>> GetFileListAsync(string fileListId);
        /// <summary> 上傳檔案 </summary>
        Task<(bool, string)> UploadFilesAsync(List<FileListUploadDTO> files, String tokenUid = "");
        /// <summary> 檔案異動 </summary>
        /// <remarks>
        /// 這個方法用於檔案的異動，包含修改和刪除檔案的操作。
        /// </remarks>
        Task<(bool, string)> ChangesFileAsync(List<FileListDTO> fileList, String tokenUid = "");
    }
}
