using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    /// <summary>
    /// 財產位置變動單服務介面
    /// </summary>
    public interface IAssetLocationTransferService
    {
        /// <summary>
        /// 取得財產位置變動單列表
        /// </summary>
        /// <param name="searchTerm">搜尋關鍵字</param>
        /// <param name="approvalStatus">審核狀態</param>
        /// <param name="executionStatus">執行狀態</param>
        /// <param name="startDate">開始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>變動單列表</returns>
        Task<List<AssetLocationTransferDTO>> GetTransfersAsync(
            string? searchTerm,
            string? approvalStatus,
            string? executionStatus,
            long? startDate,
            long? endDate);

        /// <summary>
        /// 根據變動單號取得財產位置變動單詳細資料
        /// </summary>
        /// <param name="transferNo">位置變動單編號</param>
        /// <returns>完整的變動單資料</returns>
        Task<AssetLocationTransferWithDetailsDTO?> GetTransferByTransferNoAsync(string transferNo);

        /// <summary>
        /// 新增財產位置變動單
        /// </summary>
        /// <param name="data">變動單資料</param>
        /// <returns>新增結果</returns>
        Task<(bool success, string message, string? transferNo)> AddTransferAsync(AssetLocationTransferWithDetailsDTO data);

        /// <summary>
        /// 更新財產位置變動單
        /// </summary>
        /// <param name="data">變動單資料</param>
        /// <returns>更新結果</returns>
        Task<(bool success, string message)> UpdateTransferAsync(AssetLocationTransferWithDetailsDTO data);

        /// <summary>
        /// 刪除財產位置變動單
        /// </summary>
        /// <param name="transferNo">位置變動單編號</param>
        /// <param name="deleteUserId">刪除者編號</param>
        /// <returns>刪除結果</returns>
        Task<(bool success, string message)> DeleteTransferAsync(string transferNo, string deleteUserId);

        /// <summary>
        /// 審核財產位置變動單
        /// </summary>
        /// <param name="approvalData">審核資料</param>
        /// <returns>審核結果</returns>
        Task<(bool success, string message)> ApproveTransferAsync(AssetLocationTransferApprovalDTO approvalData);

        /// <summary>
        /// 執行財產位置變動
        /// </summary>
        /// <param name="executionData">執行資料</param>
        /// <returns>執行結果</returns>
        Task<(bool success, string message)> ExecuteTransferAsync(AssetLocationTransferExecutionDTO executionData);

        /// <summary>
        /// 產生變動單號
        /// </summary>
        /// <param name="transferDate">變動日期</param>
        /// <returns>變動單號</returns>
        Task<string> GenerateTransferNoAsync(long transferDate);

        /// <summary>
        /// 驗證財產是否可以進行位置變動
        /// </summary>
        /// <param name="assetNo">財產編號</param>
        /// <returns>驗證結果</returns>
        Task<(bool isValid, string message)> ValidateAssetForTransferAsync(string assetNo);

        /// <summary>
        /// 取得財產目前的位置資訊
        /// </summary>
        /// <param name="assetNo">財產編號</param>
        /// <returns>財產位置資訊</returns>
        Task<AssetLocationTransferDetailDTO?> GetAssetCurrentLocationAsync(string assetNo);
    }
}