using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Services.Ims;
/// <summary> 庫存品分類服務 </summary>
public class ItemCategoryService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : IItemCategoryService
{
    private const int MaxAllowedDepth = 3;

    /// <summary> 庫存品分類列表 </summary>
    public async Task<List<ItemCategoryDTO>> GetAllAsync()
    {
        var entity = await _context.Ims_ItemCategory
            .Where(c => !c.IsDeleted)
            .Select(c => new ItemCategoryDTO
            {
                ItemCategoryID = c.ItemCategoryID,
                Name = c.Name,
                Description = c.Description,
                ParentID = c.ParentID,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId,
                IsDeleted = c.IsDeleted,
                Items = c.Items.Select(i => new ItemDTO
                {
                    ItemID = i.ItemID,
                    CustomNO = i.CustomNO,
                    Name = i.Name
                }).ToList()
            })
            .OrderBy(c => c.SortCode)
            .ThenBy(c => c.Name)
            .ToListAsync();

        // 返回平面列表，讓前端自己建構樹狀結構
        return entity;
    }

    /// <summary> 庫存品分類取得 </summary>
    public async Task<ItemCategoryDTO> GetAsync(Guid ItemCategoryID)
    {
        var entity = await _context.Ims_ItemCategory
            .Select(c => new ItemCategoryDTO
            {
                ItemCategoryID = c.ItemCategoryID,
                Name = c.Name,
                ParentID = c.ParentID,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId,
                IsDeleted = c.IsDeleted,
                Children = c.Children.Select(child => new ItemCategoryDTO
                {
                    ItemCategoryID = child.ItemCategoryID,
                    Name = child.Name,
                    SortCode = child.SortCode,
                }).ToList(),
                Items = c.Items.Select(i => new ItemDTO
                {
                    ItemID = i.ItemID,
                    CustomNO = i.CustomNO,
                    Name = i.Name
                }).ToList()
            })
            .FirstOrDefaultAsync();
        return entity;
    }

    /// <summary> 庫存品分類新增 </summary>
    public async Task<(bool, string)> AddAsync(ItemCategoryDTO DTO)
    {
        if (DTO == null)
        {
            return (false, "庫存品分類資料不可為空");
        }

        // 新增名稱重複檢查
        if (await _context.Ims_ItemCategory.AnyAsync(c => c.Name == DTO.Name))
        {
            return (false, "分類名稱已存在");
        }

        // 驗證 ParentID 有效性
        if (DTO.ParentID.HasValue)
        {
            if (!await _context.Ims_ItemCategory.AnyAsync(c => c.ItemCategoryID == DTO.ParentID.Value))
            {
                return (false, "指定的父分類不存在");
            }

            if (await CheckLevel(DTO.ParentID.Value, MaxAllowedDepth))
            {
                return (false, $"庫存品分類層級超過最大允許層級 {MaxAllowedDepth} 層!");
            }
        }

        var entity = new ItemCategory
        {
            ItemCategoryID = Guid.NewGuid(),
            Name = DTO.Name,
            Description = DTO.Description ?? string.Empty,
            ParentID = DTO.ParentID,
            SortCode = DTO.SortCode,
            CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId
        };

        _context.Ims_ItemCategory.Add(entity);
        await _context.SaveChangesAsync();
        return (true, $"ItemCategory {entity.ItemCategoryID} added successfully");
    }

    /// <summary> 庫存品分類更新 </summary>
    public async Task<(bool, string)> UpdateAsync(ItemCategoryDTO DTO)
    {
        try
        {
            var entity = await _context.Ims_ItemCategory
                .FirstOrDefaultAsync(p => p.ItemCategoryID == DTO.ItemCategoryID);
            if (entity == null)
            {
                return (false, $"ItemCategory {DTO.ItemCategoryID} not found");
            }

            // 檢查名稱重複（排除自己）
            if (await _context.Ims_ItemCategory.AnyAsync(c => c.Name == DTO.Name && c.ItemCategoryID != DTO.ItemCategoryID))
            {
                return (false, "分類名稱已存在");
            }

            // 驗證 ParentID 有效性
            if (DTO.ParentID.HasValue)
            {
                // 不能將自己設為父分類
                if (DTO.ParentID.Value == DTO.ItemCategoryID)
                {
                    return (false, "不能將自己設為父分類");
                }

                // 檢查父分類是否存在
                if (!await _context.Ims_ItemCategory.AnyAsync(c => c.ItemCategoryID == DTO.ParentID.Value))
                {
                    return (false, "指定的父分類不存在");
                }

                // 檢查是否會造成循環引用（不能將父分類設為自己的子分類）
                if (await IsDescendant(DTO.ItemCategoryID, DTO.ParentID.Value))
                {
                    return (false, "不能將子分類設為父分類，這會造成循環引用");
                }
                
                // 檢查層級限制
                if (await CheckLevel(DTO.ParentID.Value, MaxAllowedDepth))
                {
                    return (false, $"庫存品分類層級超過最大允許層級 {MaxAllowedDepth} 層!");
                }
            }

            entity.Name = DTO.Name;
            entity.Description = DTO.Description ?? string.Empty;
            entity.ParentID = DTO.ParentID;
            entity.SortCode = DTO.SortCode;
            entity.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
            entity.UpdateUserId = _currentUserService.UserId;

            _context.Ims_ItemCategory.Update(entity);
            await _context.SaveChangesAsync();
            return (true, $"ItemCategory {entity.ItemCategoryID} updated successfully");
        }
        catch (Exception ex)
        {
            await _logger.LogErrorAsync($"更新庫存品分類發生錯誤 - ItemCategoryID: {DTO.ItemCategoryID}", ex, "ItemCategoryService");
            return (false, $"Update ItemCategory failed: {ex.Message}");
        }
    }

    /// <summary> 庫存品分類刪除 </summary>
    public async Task<(bool, string)> DeleteAsync(ItemCategoryDTO DTO)
    {
        try
        {
            var entity = await _context.Ims_ItemCategory
                .FirstOrDefaultAsync(p => p.ItemCategoryID == DTO.ItemCategoryID);
            if (entity == null)
            {
                return (false, "ItemCategory not found");
            }
            entity.IsDeleted = true;
            entity.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
            entity.DeleteUserId = _currentUserService.UserId;

            _context.Ims_ItemCategory.Update(entity);
            await _context.SaveChangesAsync();
            return (true, $"ItemCategory {entity.ItemCategoryID} deleted successfully");
        }
        catch (Exception ex)
        {
            await _logger.LogErrorAsync($"刪除庫存品分類發生錯誤 - ItemCategoryID: {DTO.ItemCategoryID}", ex, "ItemCategoryService");
            return (false, $"Delete ItemCategory failed: {ex.Message}");
        }
    }

    /// <summary> 遞迴構建庫存品分類樹狀結構 </summary>
    private List<ItemCategoryDTO> BuildCategoryTree(List<ItemCategoryDTO> categories)
    {
        // 建立 ID 到分類的查找表
        var lookup = categories.ToDictionary(c => c.ItemCategoryID, c => c);

        // 初始化頂層分類
        var tree = new List<ItemCategoryDTO>();

        foreach (var category in categories)
        {
            if (!category.ParentID.HasValue)
            {
                // 頂層分類直接加入樹
                tree.Add(category);
            }
            else if (lookup.TryGetValue(category.ParentID.Value, out var parent))
            {
                // 將當前分類加入父分類的 Children
                parent.Children.Add(category);
            }
        }

        return tree.OrderBy(c => c.SortCode).ToList();
    }

    /// <summary> 檢查新增子分類後是否超過最大層級限制 </summary>
    /// <param name="ParentID">要新增子分類的父分類ID</param>
    /// <param name="maxAllowedDepth">系統允許的最大深度</param>
    /// <returns>true表示超過限制</returns>
    private async Task<bool> CheckLevel(Guid ParentID, int maxAllowedDepth)
    {
        var current = await _context.Ims_ItemCategory
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.ItemCategoryID == ParentID);

        if (current == null) return false;

        // 從當前節點向上計算現有深度
        int existingDepth = 1;
        while (current.ParentID.HasValue)
        {
            current = await _context.Ims_ItemCategory
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.ItemCategoryID == current.ParentID.Value);

            if (current == null) break;
            existingDepth++;
        }

        // 新增子節點後的總深度 = 現有深度 + 1
        // 當允許最大3層時，現有深度必須 <= 2 才能新增
        return existingDepth >= maxAllowedDepth;
    }

    /// <summary> 檢查指定分類是否為另一個分類的子分類 </summary>
    /// <param name="ancestorId">祖先分類ID</param>
    /// <param name="descendantId">後代分類ID</param>
    /// <returns>true表示是子分類</returns>
    private async Task<bool> IsDescendant(Guid ancestorId, Guid descendantId)
    {
        var current = await _context.Ims_ItemCategory
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.ItemCategoryID == descendantId);

        while (current?.ParentID.HasValue == true)
        {
            if (current.ParentID.Value == ancestorId)
            {
                return true;
            }

            current = await _context.Ims_ItemCategory
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.ItemCategoryID == current.ParentID.Value);
        }

        return false;
    }
}
