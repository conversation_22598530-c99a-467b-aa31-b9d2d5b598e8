"use client";

import React, { useState } from 'react';
import { DatePicker, Select, Space, Button, Tooltip, Alert } from 'antd';
import { CalendarOutlined, NumberOutlined, WarningOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';

interface SettlementDayPickerProps {
  value?: number;
  onChange?: (value: number | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
}

/**
 * 結算日期選擇器組件
 * 提供兩種選擇方式：日曆選擇和下拉選擇
 * 最終儲存為月份中的日期數字 (1-31)
 */
const SettlementDayPicker: React.FC<SettlementDayPickerProps> = ({
  value,
  onChange,
  placeholder = "請選擇結算日",
  disabled = false,
  style
}) => {
  const [mode, setMode] = useState<'calendar' | 'dropdown'>('dropdown');

  // 獲取月份的天數
  const getDaysInMonth = (month: number, year: number = new Date().getFullYear()): number => {
    return new Date(year, month, 0).getDate();
  };

  // 檢查結算日在特定月份是否有效
  const isValidSettlementDay = (day: number, month: number, year: number = new Date().getFullYear()): boolean => {
    const daysInMonth = getDaysInMonth(month, year);
    return day <= daysInMonth;
  };

  // 獲取結算日警告訊息
  const getSettlementDayWarning = (day: number): string | null => {
    if (!day || day <= 28) return null; // 28日以內所有月份都有效

    const problematicMonths: string[] = [];

    // 檢查每個月份
    for (let month = 1; month <= 12; month++) {
      if (!isValidSettlementDay(day, month)) {
        const monthNames = ['', '一月', '二月', '三月', '四月', '五月', '六月',
                           '七月', '八月', '九月', '十月', '十一月', '十二月'];
        problematicMonths.push(monthNames[month]);
      }
    }

    if (problematicMonths.length > 0) {
      return `注意：${problematicMonths.join('、')}沒有${day}日，系統將自動使用該月最後一日作為結算日。`;
    }

    return null;
  };

  // 獲取有效的結算日（如果選定日期在某月不存在，則使用該月最後一日）
  const getValidSettlementDay = (day: number, month: number, year: number = new Date().getFullYear()): number => {
    const daysInMonth = getDaysInMonth(month, year);
    return Math.min(day, daysInMonth);
  };

  // 處理日曆選擇
  const handleCalendarChange = (date: Dayjs | null) => {
    if (date) {
      const day = date.date(); // 獲取日期中的天數 (1-31)
      onChange?.(day);
    } else {
      onChange?.(undefined);
    }
  };

  // 處理下拉選擇
  const handleDropdownChange = (day: number | undefined) => {
    onChange?.(day);
  };

  // 根據當前值創建日期對象用於日曆顯示
  const getDateValue = (): Dayjs | null => {
    if (!value) return null;
    // 使用當前月份和選定的日期創建日期對象
    const currentMonth = dayjs();
    return currentMonth.date(value);
  };

  // 禁用日期函數 - 只允許選擇當前月份的有效日期
  const disabledDate = (current: Dayjs) => {
    const currentMonth = dayjs();
    // 禁用非當前月份的日期
    return current.month() !== currentMonth.month() || current.year() !== currentMonth.year();
  };

  return (
    <div style={style}>
      <Space.Compact style={{ width: '100%' }}>
        {mode === 'calendar' ? (
          <DatePicker
            value={getDateValue()}
            onChange={handleCalendarChange}
            placeholder={placeholder}
            disabled={disabled}
            style={{ flex: 1 }}
            format="DD"
            disabledDate={disabledDate}
            picker="date"

            suffixIcon={<CalendarOutlined />}
          />
        ) : (
          <Select
            value={value}
            onChange={handleDropdownChange}
            placeholder={placeholder}
            disabled={disabled}
            style={{ flex: 1 }}
            allowClear
            suffixIcon={<NumberOutlined />}
          >
            {Array.from({ length: 31 }, (_, i) => {
              const day = i + 1;
              const warning = getSettlementDayWarning(day);
              return (
                <Select.Option key={day} value={day}>
                  <Space>
                    <span>每月 {day} 日</span>
                    {warning && (
                      <Tooltip title={warning}>
                        <WarningOutlined style={{ color: '#faad14' }} />
                      </Tooltip>
                    )}
                  </Space>
                </Select.Option>
              );
            })}
          </Select>
        )}
        
        <Tooltip title={mode === 'calendar' ? '切換到下拉選擇' : '切換到日曆選擇'}>
          <Button
            icon={mode === 'calendar' ? <NumberOutlined /> : <CalendarOutlined />}
            onClick={() => setMode(mode === 'calendar' ? 'dropdown' : 'calendar')}
            disabled={disabled}
          />
        </Tooltip>
      </Space.Compact>

      {/* 顯示結算日警告訊息 */}
      {value && getSettlementDayWarning(value) && (
        <Alert
          message="結算日提醒"
          description={getSettlementDayWarning(value)}
          type="warning"
          showIcon
          style={{ marginTop: 8 }}
          icon={<WarningOutlined />}
        />
      )}

      {value && (
        <div style={{
          fontSize: '12px',
          color: '#666',
          marginTop: '4px',
          textAlign: 'center'
        }}>
          每月 {value} 日結算
        </div>
      )}
    </div>
  );
};

export default SettlementDayPicker;
