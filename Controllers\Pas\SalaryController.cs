using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("薪資主檔管理")]
    public class SalaryController : ControllerBase
    {
        private readonly ISalaryService _Interface;

        public SalaryController(ISalaryService salaryService)
        {
            _Interface = salaryService;
        }

        [HttpGet]
        [Route("Get/{_userid}")]
        [SwaggerOperation(Summary = "取得員工明細", Description = "依UserID取得員工明細")]
        public async Task<IActionResult> GetSalaryDetail(string _userid)
        {
            var result = await _Interface.GetSalaryDetailAsync(_userid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯員工主檔", Description = "修改已存在之員工資料")]
        public async Task<IActionResult> EditSalary([FromBody] SalaryDTO _data)
        {
            var (result, msg) = await _Interface.EditSalaryAsync(_data);
            return Ok(new { result, msg });
        }

    }
}