﻿using System;
using System.Data;
using System.Security.Cryptography;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace FAST_ERP_Backend.Services.Common
{
    public class DepartmentService : IDepartmentService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;


        public DepartmentService(Baseform baseform, ERPDbContext context)
        {
            _baseform = baseform;
            _context = context;
        }

        /// <summary>
        /// 取得部門資料
        /// </summary>
        /// <param name="_departmentId"></param>
        /// <returns></returns>
        public async Task<List<DepartmentDTO>> GetDepartmentAsync(string _departmentId)
        {
            IQueryable<Department> query = _context.Common_Departments;

            // 如果 _departmentId 不為空，則加上篩選條件
            if (!string.IsNullOrEmpty(_departmentId))
            {
                query = query.Where(e => e.DepartmentId == _departmentId);
            }

            var result = await query
                .OrderBy(e => e.Name)
                .Select(t => new DepartmentDTO
                {
                    DepartmentId = t.DepartmentId,
                    SortCode = t.SortCode,
                    Name = t.Name,
                    EnterpriseGroupId = t.EnterpriseGroupId
                })
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// 新增部門資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddDepartmentAsync(DepartmentDTO _data)
        {
            string uid = Guid.NewGuid().ToString().Trim();
            try
            {
                var newDepartment = new Department
                {
                    DepartmentId = uid,
                    SortCode = _data.SortCode,
                    Name = _data.Name,
                    EnterpriseGroupId = _data.EnterpriseGroupId,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = _data.CreateUserId,
                };

                await _context.Database.BeginTransactionAsync();

                await _context.Common_Departments.AddAsync(newDepartment);
                await _context.SaveChangesAsync();

                await _context.Database.CommitTransactionAsync();

                return (true, "新增部門成功");
            }
            catch (Exception ex)
            {
                return (true, $"新增部門失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 變更部門資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditDepartmentAsync(DepartmentDTO _data)
        {
            var existdepartment = await _context.Common_Departments
            .FirstOrDefaultAsync(e => e.DepartmentId == _data.DepartmentId);

            if (existdepartment != null)
            {
                try
                {
                    await _context.Database.BeginTransactionAsync();

                    existdepartment.SortCode = _data.SortCode;
                    existdepartment.Name = _data.Name;
                    existdepartment.EnterpriseGroupId = _data.EnterpriseGroupId;
                    existdepartment.UpdateUserId = "token-更新者uid";
                    existdepartment.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();

                    await _context.SaveChangesAsync();
                    await _context.Database.CommitTransactionAsync();

                    return (true, "更新資料成功");
                }
                catch (Exception ex)
                {
                    await _context.Database.RollbackTransactionAsync();
                    return (false, $"更新資料失敗: {ex.Message}");
                }
            }
            else
            {
                return (false, "更新資料失敗");
            }
        }

        /// <summary>
        /// 刪除部門資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteDepartmentAsync(DepartmentDTO _data)
        {
            var existdepartment = await _context.Common_Departments
            .FirstOrDefaultAsync(e => e.DepartmentId == _data.DepartmentId);

            if (existdepartment != null)
            {
                try
                {
                    await _context.Database.BeginTransactionAsync();

                    existdepartment.DeleteUserId = _data.DeleteUserId;
                    existdepartment.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();

                    await _context.SaveChangesAsync();
                    await _context.Database.CommitTransactionAsync();

                    return (true, "刪除部門成功");
                }
                catch (Exception ex)
                {
                    await _context.Database.RollbackTransactionAsync();
                    return (false, $"刪除部門失敗: {ex.Message}");
                }
            }
            else
            {
                return (false, "刪除部門失敗");
            }
        }
    }
}