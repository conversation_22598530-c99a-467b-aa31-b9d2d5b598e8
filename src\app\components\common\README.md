# 報表抬頭組件 (ReportHeader)

## 概述

`ReportHeader` 是一個共用的報表抬頭組件，提供統一的報表格式，包含公司名稱、報表名稱、頁次和列印日期等信息。

## 功能特點

- 支援螢幕顯示模式和列印模式
- 自動讀取公司名稱 (從 `siteConfig.copyright`)
- 自動格式化列印日期
- 提供列印樣式的CSS函數
- 響應式設計

## 基本用法

### 引入組件

```typescript
import ReportHeader, { getReportPrintStyles } from "@/app/components/common/ReportHeader";
```

### 螢幕顯示模式

```tsx
<ReportHeader
  reportTitle="財產清冊"
  currentPage={1}
  totalPages={5}
  isPrintMode={false}
/>
```

### 列印模式

```tsx
<ReportHeader
  reportTitle="財產清冊"
  currentPage={currentPage}
  totalPages={totalPages}
  isPrintMode={true}
/>
```

### 在列印函數中使用樣式

```typescript
const handlePrint = () => {
  if (printRef.current) {
    const printContent = printRef.current.innerHTML;
    const printWindow = window.open("", "_blank");

    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${siteConfig.copyright}${reportTitle}</title>
            <style>
              /* 基本樣式 */
              body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 2px; 
                background: white;
                font-size: 12px;
              }
              
              /* 使用共用的列印樣式 */
              ${getReportPrintStyles(reportTitle)}
            </style>
          </head>
          <body>
            ${printContent}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  }
};
```

## Props 屬性

| 屬性名 | 類型 | 必填 | 預設值 | 說明 |
|--------|------|------|--------|------|
| `reportTitle` | `string` | ✅ | - | 報表名稱 |
| `currentPage` | `number` | ❌ | - | 當前頁數 |
| `totalPages` | `number` | ❌ | - | 總頁數 |
| `printDate` | `string` | ❌ | 當前日期 | 列印日期 (YYYY/MM/DD格式) |
| `isPrintMode` | `boolean` | ❌ | `false` | 是否為列印模式 |
| `style` | `React.CSSProperties` | ❌ | - | 自訂樣式 |
| `className` | `string` | ❌ | `""` | 自訂類別名稱 |

## 樣式自訂

### 螢幕模式樣式

- 預設有邊框和背景色
- 文字置中對齊
- 包含頁次和日期信息

### 列印模式樣式

- 無邊框，適合列印
- 使用 CSS @page 規則在頁首顯示信息（頁次、列印日期）
- 避免在內容區域重複顯示頁次和日期信息
- 自動處理分頁

## 使用範例

### 完整的報表頁面範例

```tsx
"use client";

import React, { useState, useRef } from "react";
import { Button, Card } from "antd";
import { PrinterOutlined, EyeOutlined } from "@ant-design/icons";
import ReportHeader, { getReportPrintStyles } from "@/app/components/common/ReportHeader";
import { siteConfig } from "@/config/site";

const MyReportPage: React.FC = () => {
  const [isPrintMode, setIsPrintMode] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const printWindow = window.open("", "_blank");

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${siteConfig.copyright}我的報表</title>
              <style>
                body { 
                  font-family: Arial, sans-serif; 
                  margin: 0; 
                  padding: 2px; 
                  background: white;
                  font-size: 12px;
                }
                ${getReportPrintStyles("我的報表")}
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  return (
    <div>
      <Card>
        <Button
          icon={<EyeOutlined />}
          onClick={() => setIsPrintMode(!isPrintMode)}
        >
          {isPrintMode ? "返回列表" : "預覽報表"}
        </Button>
        {isPrintMode && (
          <Button
            type="primary"
            icon={<PrinterOutlined />}
            onClick={handlePrint}
          >
            列印
          </Button>
        )}
      </Card>

      <div ref={printRef}>
        {isPrintMode ? (
          <div style={{ background: "white", padding: "5px" }}>
            <ReportHeader
              reportTitle="我的報表"
              currentPage={currentPage}
              totalPages={totalPages}
              isPrintMode={true}
            />
            {/* 報表內容 */}
            <div>報表內容在這裡...</div>
          </div>
        ) : (
          <Card>
            <ReportHeader
              reportTitle="我的報表"
              currentPage={currentPage}
              totalPages={totalPages}
              isPrintMode={false}
            />
            {/* 一般顯示的內容 */}
            <div>一般顯示的內容在這裡...</div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default MyReportPage;
```

## 注意事項

1. **公司名稱配置**: 確保 `siteConfig.copyright` 已正確設定
2. **列印樣式**: 使用 `getReportPrintStyles()` 函數可確保列印樣式的一致性
3. **響應式**: 組件會根據 `isPrintMode` 屬性自動調整顯示樣式
4. **日期格式**: 預設使用 `YYYY/MM/DD` 格式，可透過 `printDate` 屬性自訂
5. **列印模式頁次顯示**: 在列印模式下，頁次和列印日期僅透過 CSS @page 規則在頁首顯示，內容區域不會重複顯示，避免與瀏覽器的列印頁首衝突

## 更新紀錄

- v1.0.0: 初始版本，支援基本的報表抬頭功能
- 從原始的資產列表報表抽取共用組件
