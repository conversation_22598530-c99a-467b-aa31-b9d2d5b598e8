// Suspend 留職停薪資料管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 留職停薪資料型別
export interface Suspend {
    uid: string;
    userId: string;
    suspendType: string;
    suspendTypeName: string;
    suspendKind: string;
    suspendKindName: string;
    suspendReason: string;
    suspendDate: string; // timestamp 格式字串
    approveDate: string; // timestamp 格式字串
    approveNumber: string;
    remark: string;
}

// 建立空的留職停薪資料
export const createEmptySuspend = (): Suspend => ({
    uid: '',
    userId: '',
    suspendType: '',
    suspendTypeName: '',
    suspendKind: '',
    suspendKindName: '',
    suspendReason: '',
    suspendDate: '',
    approveDate: '',
    approveNumber: '',
    remark: '',
});

// 取得留職停薪資料列表
export async function getSuspendList(userId: string): Promise<ApiResponse<Suspend[]>> {
    return await httpClient(`${apiEndpoints.getSuspendList}/${userId}`, {
        method: "GET",
    });
}

// 取得留職停薪資料明細
export async function getSuspendDetail(uid: string): Promise<ApiResponse<Suspend>> {
    return await httpClient(`${apiEndpoints.getSuspendDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增留職停薪資料
export async function addSuspend(data: Partial<Suspend>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addSuspend, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增留職停薪資料失敗",
        };
    }
}

// 編輯留職停薪資料
export async function editSuspend(data: Partial<Suspend>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editSuspend, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯留職停薪資料失敗",
        };
    }
}

// 刪除留職停薪資料
export async function deleteSuspend(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteSuspend, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除留職停薪資料失敗",
        };
    }
}
