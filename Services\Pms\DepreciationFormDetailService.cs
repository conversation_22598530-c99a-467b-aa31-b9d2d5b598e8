﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pms.Enums;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using FAST_ERP_Backend.Interfaces.Common;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class DepreciationFormDetailService : IDepreciationFormDetailService
    {
        private readonly ERPDbContext _context;
        private readonly IPmsSystemParameterService _systemParameterService;
        private readonly IParameterValueService _parameterValueService;
        private readonly IMapper _mapper;

        public DepreciationFormDetailService(ERPDbContext context, IPmsSystemParameterService systemParameterService,
            IParameterValueService parameterValueService, IMapper mapper)
        {
            _context = context;
            _systemParameterService = systemParameterService;
            _parameterValueService = parameterValueService;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得折舊紀錄資料
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepreciationFormDetailDTO>> GetDepreciationAsync()
        {
            var query = from d in _context.Set<DepreciationFormDetail>().Where(d => !d.IsDeleted)
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()
                        select new { Depreciation = d, AssetNo = asset != null ? asset.AssetNo : "", AssetName = asset != null ? asset.AssetName : "" };

            var result = await query.OrderBy(item => item.Depreciation.DepreciationYear).ThenBy(item => item.Depreciation.DepreciationMonth).ToListAsync();

            return result.Select(item =>
            {
                var dto = _mapper.Map<DepreciationFormDetailDTO>(item.Depreciation);
                dto.AssetNo = item.AssetNo;
                dto.AssetName = item.AssetName;
                return dto;
            }).ToList();
        }

        /// <summary>
        /// 新增折舊紀錄
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddDepreciationAsync(DepreciationFormDetailDTO _data)
        {
            try
            {
                var entity = _mapper.Map<DepreciationFormDetail>(_data);
                entity.DepreciationId = string.IsNullOrEmpty(_data.DepreciationId) ? Guid.NewGuid() : Guid.Parse(_data.DepreciationId);
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.IsDeleted = false;

                _context.Add(entity);
                await _context.SaveChangesAsync();
                Console.WriteLine($"新增折舊紀錄成功: {entity.DepreciationId}");
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"新增折舊紀錄失敗: {ex.Message}");
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯折舊紀錄
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditDepreciationAsync(DepreciationFormDetailDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationFormDetail>().FindAsync(Guid.Parse(_data.DepreciationId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除折舊紀錄
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteDepreciationAsync(DepreciationFormDetailDTO _data)
        {
            try
            {
                var entity = await _context.Set<DepreciationFormDetail>().FindAsync(Guid.Parse(_data.DepreciationId));
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得折舊紀錄資料
        /// </summary>
        /// <param name="_assetId"></param>
        /// <returns></returns>
        public async Task<List<DepreciationFormDetailDTO>> GetDepreciationByAssetAsync(string _assetId)
        {
            if (!Guid.TryParse(_assetId, out Guid assetId))
            {
                return new List<DepreciationFormDetailDTO>();
            }

            var query = from d in _context.Set<DepreciationFormDetail>().Where(d => d.AssetId == assetId && !d.IsDeleted)
                        join a in _context.Pms_Assets on d.AssetId equals a.AssetId into assetGroup
                        from asset in assetGroup.DefaultIfEmpty()
                        select new { Depreciation = d, AssetNo = asset != null ? asset.AssetNo : "", AssetName = asset != null ? asset.AssetName : "" };

            var result = await query.OrderBy(item => item.Depreciation.DepreciationYear).ThenBy(item => item.Depreciation.DepreciationMonth).ToListAsync();

            return result.Select(item =>
            {
                var dto = _mapper.Map<DepreciationFormDetailDTO>(item.Depreciation);
                dto.AssetNo = item.AssetNo;
                dto.AssetName = item.AssetName;
                return dto;
            }).ToList();
        }

        /// <summary>
        /// 取得可用折舊方法
        /// </summary>
        public async Task<(bool success, List<object> methods, string message)> GetAvailableDepreciationMethodsAsync()
        {
            try
            {
                var methods = await _systemParameterService.GetDepreciationMethodsAsync();
                if (methods == null || !methods.Any())
                {
                    return (false, null, "找不到可用的折舊方法");
                }

                var result = new List<object>();
                foreach (var method in methods)
                {
                    try
                    {
                        var jObject = JObject.Parse(method.ParameterValue);
                        var methodInfo = new
                        {
                            MethodId = method.ParameterId.ToString(),
                            MethodName = method.ParameterName,
                            MethodKey = jObject["key"]?.ToString(),
                            IsDefault = jObject["isDefault"]?.ToObject<bool>() ?? false,
                            Description = method.ParameterDescription
                        };
                        result.Add(methodInfo);
                    }
                    catch
                    {
                        // 忽略無法解析的方法
                    }
                }

                return (true, result, "成功取得折舊方法");
            }
            catch (Exception ex)
            {
                return (false, null, $"取得折舊方法失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得指定財產科目餘額遞減法折舊率
        /// </summary>
        public async Task<(bool success, decimal rate, string message)> GetDecliningBalanceRateForAssetAccountAsync(string assetAccountId)
        {
            if (string.IsNullOrEmpty(assetAccountId))
            {
                return (false, 0, "財產科目ID不能為空");
            }

            return await _systemParameterService.GetDecliningBalanceRateForAssetAccountAsync(assetAccountId);
        }

        /// <summary>
        /// 取得預設折舊方法
        /// </summary>
        /// <param name="depreciationMethods">折舊方法列表</param>
        /// <param name="assetStatus">財產狀態</param>
        /// <returns>(折舊方法代碼, 折舊方法名稱)</returns>
        private (string methodKey, string methodName) GetDefaultDepreciationMethod(List<PmsSystemParameterDTO> depreciationMethods, string assetStatus = null)
        {
            // 預設直線法
            string depreciationMethodKey = "straight_line";
            string depreciationMethodName = "直線法";

            // 取得預設折舊方法
            var defaultMethod = depreciationMethods.FirstOrDefault(m =>
                _parameterValueService.ContainsProperty(m.ParameterValue, "isDefault") &&
                _parameterValueService.GetValueFromJson<bool>(m.ParameterValue, "isDefault", false));

            if (defaultMethod != null)
            {
                try
                {
                    depreciationMethodKey = _parameterValueService.GetValueFromJson<string>(defaultMethod.ParameterValue, "key", "straight_line");
                    depreciationMethodName = defaultMethod.ParameterName ?? "直線法";
                }
                catch
                {
                    // 使用預設方法
                    Console.WriteLine("解析預設折舊方法失敗，使用直線法");
                }
            }

            Console.WriteLine($"預設折舊方法: {depreciationMethodName}-{depreciationMethodKey}");

            // 如果財產已指定折舊方法，則使用該方法
            if (!string.IsNullOrEmpty(assetStatus))
            {
                var assetMethod = depreciationMethods.FirstOrDefault(m =>
                    _parameterValueService.ContainsProperty(m.ParameterValue, "key") &&
                    _parameterValueService.GetValueFromJson<string>(m.ParameterValue, "key", "") == assetStatus);

                if (assetMethod != null)
                {
                    try
                    {
                        depreciationMethodKey = _parameterValueService.GetValueFromJson<string>(assetMethod.ParameterValue, "key", depreciationMethodKey);
                        depreciationMethodName = assetMethod.ParameterName ?? depreciationMethodName;
                    }
                    catch
                    {
                        // 保持原有方法
                        Console.WriteLine($"解析財產指定折舊方法失敗，使用{depreciationMethodName}");
                    }
                }
            }

            return (depreciationMethodKey, depreciationMethodName);
        }

        /// <summary>
        /// 產生整期折舊紀錄
        /// </summary>
        /// <param name="assetId">財產編號</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>成功與否、訊息</returns>
        public async Task<(bool success, string message)> GenerateDepreciationScheduleAsync(string assetId, string userId)
        {
            try
            {
                if (!Guid.TryParse(assetId, out Guid parsedAssetId))
                {
                    return (false, "財產編號無效");
                }

                // 取得財產資訊
                var asset = await _context.Pms_Assets
                    .FirstOrDefaultAsync(a => a.AssetId == parsedAssetId && !a.IsDeleted);

                if (asset == null)
                {
                    return (false, "找不到指定的財產");
                }

                // 檢查是否為不計算折舊的科目 (1: 土地, 9: 未完工程)
                if (asset.AssetAccountId != null)
                {
                    var assetAccount = await _context.Pms_AssetAccounts
                        .FirstOrDefaultAsync(a => a.AssetAccountId == asset.AssetAccountId);

                    if (assetAccount != null && (assetAccount.AssetAccountNo == "1" || assetAccount.AssetAccountNo == "9"))
                    {
                        return (true, $"科目為「{assetAccount.AssetAccountName}」的財產不需計算折舊");
                    }
                }

                // 檢查財產是否已報廢
                if (asset.ScrapDate > 0)
                {
                    var message = asset.ScrapDate > 0 ? "該財產已報廢，無法計算折舊" : "該財產已預備報廢，無法計算折舊";
                    return (false, message);
                }

                // 檢查使用年限
                if (asset.ServiceLife <= 0)
                {
                    return (false, "財產使用年限必須大於0");
                }

                // 取得起始計算日期 (從取得日期開始)
                var startDate = asset.AcquisitionDate > 0 ?
                    DateTimeOffset.FromUnixTimeSeconds(asset.AcquisitionDate) :
                    DateTimeOffset.UtcNow;

                // 取得預設折舊方法
                var depreciationMethods = await _systemParameterService.GetDepreciationMethodsAsync();
                var (depreciationMethodKey, depreciationMethodName) = GetDefaultDepreciationMethod(depreciationMethods, asset.AssetStatusId.ToString());

                // 計算每期折舊記錄
                var deprecationRecords = new List<DepreciationFormDetailDTO>();//折舊紀錄清單
                decimal originalAmount = asset.DepreciationAmount; // 折舊金額
                decimal salvageValue = asset.EstimatedResidualValue; // 殘值
                decimal accumulatedDepreciation = 0; // 累計折舊
                decimal remainingValue = originalAmount; // 剩餘價值
                int remainingLifeYears = asset.ServiceLife; // 剩餘使用年限
                decimal depreciationRate = 0; // 折舊率
                decimal beginningBookValue = originalAmount; // 期初帳面價值
                decimal endingBookValue = originalAmount; // 期末帳面價值

                // 依折舊方法計算
                if (depreciationMethodKey == "straight_line") // 直線法
                {
                    // 年折舊 = (原始成本 - 殘值) / 使用年限
                    decimal yearlyDepreciation = (originalAmount - salvageValue) / asset.ServiceLife;

                    // 月折舊 = 年折舊 / 12
                    decimal monthlyDepreciation = Math.Round(yearlyDepreciation / 12, 2);

                    // 生成每期折舊記錄
                    for (int year = 0; year < asset.ServiceLife; year++)
                    {
                        for (int month = 1; month <= 12; month++)
                        {
                            // 計算日期
                            var depreciationDate = new DateTimeOffset(
                                startDate.Year + year,
                                startDate.Month + month > 12 ?
                                    (startDate.Month + month) % 12 == 0 ? 12 : (startDate.Month + month) % 12 :
                                    startDate.Month + month,
                                1, 0, 0, 0, TimeSpan.Zero);

                            // 修正年份
                            if (startDate.Month + month > 12)
                            {
                                depreciationDate = depreciationDate.AddYears((startDate.Month + month - 1) / 12);
                            }

                            // 本期折舊
                            decimal currentDepreciation = monthlyDepreciation;

                            // 最後一期可能不同
                            if (year == asset.ServiceLife - 1 && month == 12)
                            {
                                // 確保不低於殘值
                                currentDepreciation = remainingValue - salvageValue;
                                if (currentDepreciation < 0) currentDepreciation = 0;
                            }

                            // 防止剩餘價值低於殘值
                            if (remainingValue - currentDepreciation < salvageValue)
                            {
                                // 本期折舊=剩餘價值-殘值
                                currentDepreciation = remainingValue - salvageValue;
                                // 防止本期折舊小於0
                                if (currentDepreciation < 0) currentDepreciation = 0;
                            }

                            // 更新計算值
                            accumulatedDepreciation += currentDepreciation;
                            remainingValue -= currentDepreciation;

                            // 期初帳面價值=原始金額-累計折舊+本期折舊
                            beginningBookValue = originalAmount - (accumulatedDepreciation - currentDepreciation);
                            // 期末帳面價值=原始金額-累計折舊
                            endingBookValue = originalAmount - accumulatedDepreciation;

                            // 更新剩餘年限
                            if (month == 12)
                            {
                                remainingLifeYears--;
                            }

                            // 建立折舊記錄
                            var depreciation = new DepreciationFormDetailDTO
                            {
                                DepreciationId = Guid.NewGuid().ToString(),//折舊紀錄編號
                                AssetId = parsedAssetId,//財產編號
                                AssetNo = asset.AssetNo,//財產編號
                                AssetName = asset.AssetName,//財產名稱
                                DepreciationYear = depreciationDate.Year,//折舊年度
                                DepreciationMonth = depreciationDate.Month,//折舊月份
                                OriginalAmount = originalAmount,//原始金額
                                AccumulatedDepreciation = accumulatedDepreciation,//累計折舊
                                CurrentDepreciation = currentDepreciation,//本期折舊
                                DepreciationRate = 0, // 直線法無折舊率
                                DepreciationMethod = depreciationMethodName,//折舊方法
                                ServiceLifeRemaining = remainingLifeYears,//剩餘年限
                                BeginningBookValue = beginningBookValue,//期初帳面價值
                                EndingBookValue = endingBookValue,//期末帳面價值
                                CreateUserId = userId,//建立者
                                CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds()//建立時間
                            };

                            deprecationRecords.Add(depreciation);
                        }
                    }
                }
                else if (depreciationMethodKey == "declining_balance") // 餘額遞減法
                {
                    // 取得財產科目的遞減法折舊率
                    bool rateSuccess;
                    decimal rateValue;
                    string rateMessage;
                    // 取得財產科目的遞減法折舊率
                    (rateSuccess, rateValue, rateMessage) = await _systemParameterService.GetDecliningBalanceRateForAssetAccountAsync(
                        asset.AssetAccountId.ToString());

                    if (!rateSuccess)
                    {
                        // 使用預設折舊率
                        rateValue = 10; // 預設10%
                    }

                    // 折舊率轉為小數 (10% -> 0.1)
                    depreciationRate = rateValue / 100;

                    Console.WriteLine($"折舊率: {depreciationRate}");

                    // 生成每期折舊記錄
                    for (int year = 0; year < asset.ServiceLife; year++)
                    {
                        // 年折舊 = 期初帳面價值 × 折舊率
                        decimal yearlyDepreciation = remainingValue * depreciationRate;

                        // 月折舊 = 年折舊 / 12
                        decimal monthlyDepreciation = Math.Round(yearlyDepreciation / 12, 2);

                        for (int month = 1; month <= 12; month++)
                        {
                            // 計算日期
                            var depreciationDate = new DateTimeOffset(
                                startDate.Year + year,
                                startDate.Month + month > 12 ?
                                    (startDate.Month + month) % 12 == 0 ? 12 : (startDate.Month + month) % 12 :
                                    startDate.Month + month,
                                1, 0, 0, 0, TimeSpan.Zero);

                            // 修正年份
                            if (startDate.Month + month > 12)
                            {
                                depreciationDate = depreciationDate.AddYears((startDate.Month + month - 1) / 12);
                            }

                            // 本期折舊
                            decimal currentDepreciation = monthlyDepreciation;

                            // 防止剩餘價值低於殘值
                            if (remainingValue - currentDepreciation < salvageValue)
                            {
                                currentDepreciation = remainingValue - salvageValue;
                                if (currentDepreciation < 0) currentDepreciation = 0;
                            }

                            // 更新計算值
                            accumulatedDepreciation += currentDepreciation;
                            remainingValue -= currentDepreciation;

                            // 期初帳面價值=原始金額-累計折舊+本期折舊
                            beginningBookValue = originalAmount - (accumulatedDepreciation - currentDepreciation);
                            // 期末帳面價值=原始金額-累計折舊
                            endingBookValue = originalAmount - accumulatedDepreciation;

                            // 更新剩餘年限
                            if (month == 12)
                            {
                                remainingLifeYears--;
                            }

                            // 建立折舊記錄
                            var depreciation = new DepreciationFormDetailDTO
                            {
                                DepreciationId = Guid.NewGuid().ToString(),//折舊紀錄編號
                                AssetId = parsedAssetId,//財產編號
                                AssetNo = asset.AssetNo,//財產編號
                                AssetName = asset.AssetName,//財產名稱
                                DepreciationYear = depreciationDate.Year,//折舊年度
                                DepreciationMonth = depreciationDate.Month,//折舊月份
                                OriginalAmount = originalAmount,//原始金額
                                AccumulatedDepreciation = accumulatedDepreciation,//累計折舊
                                CurrentDepreciation = currentDepreciation,//本期折舊
                                DepreciationRate = depreciationRate,//折舊率
                                DepreciationMethod = depreciationMethodName,//折舊方法
                                ServiceLifeRemaining = remainingLifeYears,//剩餘年限
                                BeginningBookValue = beginningBookValue,//期初帳面價值
                                EndingBookValue = endingBookValue,//期末帳面價值
                                CreateUserId = userId,//建立者
                                CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds()//建立時間
                            };

                            deprecationRecords.Add(depreciation);
                        }
                    }
                }

                // 儲存折舊記錄
                foreach (var depreciation in deprecationRecords)
                {
                    var entity = _mapper.Map<DepreciationFormDetail>(depreciation);
                    entity.DepreciationId = Guid.Parse(depreciation.DepreciationId);
                    entity.IsAdjustment = false;
                    entity.AdjustmentReason = "";
                    entity.Notes = "系統自動產生";
                    entity.BeginningBookValue = depreciation.BeginningBookValue;
                    entity.EndingBookValue = depreciation.EndingBookValue;
                    entity.IsDeleted = false;

                    _context.Add(entity);
                }

                await _context.SaveChangesAsync();

                return (true, $"已成功產生 {deprecationRecords.Count} 筆折舊紀錄");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"產生折舊紀錄失敗: {ex.Message}");
                return (false, $"產生折舊紀錄失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 計算折舊
        /// </summary>
        /// <param name="depreciationIds">折舊紀錄編號清單</param>
        /// <param name="userId">使用者編號</param>
        /// <returns>成功與否、訊息</returns>
        public async Task<(bool success, List<string> messages)> CalculateDepreciationAsync(List<string> depreciationIds, string userId)
        {
            var messages = new List<string>();
            var successCount = 0;
            using (var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    foreach (var depId in depreciationIds)
                    {
                        if (!Guid.TryParse(depId, out Guid depreciationGuid))
                        {
                            messages.Add($"折舊紀錄ID格式錯誤: {depId}");
                            continue;
                        }
                        var depreciation = await _context.Set<DepreciationFormDetail>().FirstOrDefaultAsync(d => d.DepreciationId == depreciationGuid && !d.IsDeleted);
                        if (depreciation == null)
                        {
                            messages.Add($"找不到折舊紀錄: {depId}");
                            continue;
                        }
                        var asset = await _context.Pms_Assets.FirstOrDefaultAsync(a => a.AssetId == depreciation.AssetId && !a.IsDeleted);
                        if (asset == null)
                        {
                            messages.Add($"找不到對應財產主檔: {depreciation.AssetId}");
                            continue;
                        }

                        // 期初帳面價值
                        depreciation.BeginningBookValue = depreciation.OriginalAmount - (depreciation.AccumulatedDepreciation - depreciation.CurrentDepreciation);
                        // 期末帳面價值
                        depreciation.EndingBookValue = depreciation.OriginalAmount - depreciation.AccumulatedDepreciation;

                        // 執行折舊
                        depreciation.Notes = (depreciation.Notes ?? "") + "[已執行本期折舊]";//備註
                        depreciation.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();//更新時間
                        depreciation.UpdateUserId = userId;//更新者
                        _context.Update(depreciation);//更新折舊紀錄

                        // 更新財產主檔
                        asset.AccumulatedDepreciationAmount = depreciation.AccumulatedDepreciation;//累計折舊
                        asset.DepreciationAmount = depreciation.OriginalAmount;//原始金額
                        asset.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();//更新時間
                        asset.UpdateUserId = userId;//更新者
                        _context.Update(asset);//更新財產主檔
                        successCount++;//成功筆數
                        messages.Add($"折舊紀錄 {depId} 執行成功，財產 {asset.AssetNo} 已更新");
                    }
                    // 儲存變更
                    await _context.SaveChangesAsync();
                    // 提交交易
                    await transaction.CommitAsync();
                    // 回傳結果
                    if (successCount == 0)
                        return (false, messages);
                    return (true, messages);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    messages.Add($"執行過程發生錯誤: {ex.Message}");
                    return (false, messages);
                }
            }
        }

    }
}

