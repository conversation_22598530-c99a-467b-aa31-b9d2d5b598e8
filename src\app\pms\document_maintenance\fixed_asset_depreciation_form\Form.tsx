import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  InputNumber,
  Card,
  Typography,
  Switch,
  Divider,
  Descriptions,
  Tag,
  Alert,
  FormInstance,
} from "antd";
import { FormProps, DepreciationMethod, formInitialValues } from "./interface";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import dayjs from "dayjs";
import { Depreciation } from "@/services/pms/depreciationFormDetailService";
import guidUtils from "@/utils/guidUtils";
import {
  formatTWCurrency,
  formatThousands,
  parseAmount,
} from "@/utils/formatUtils";

const { Title } = Typography;
const { TextArea } = Input;

// 表單按鈕組件
const FormButtons: React.FC<{
  onCancel: () => void;
  isViewMode: boolean;
  isMobile?: boolean;
  form: FormInstance;
}> = ({ onCancel, isViewMode, isMobile, form }) => {
  return (
    <div
      style={{
        position: "sticky",
        bottom: 0,
        left: 0,
        width: "100%",
        background: "#fff",
        zIndex: 10,
        padding: isMobile ? "12px 0 0 0" : "20px 0 0 0",
        textAlign: "right",
        boxShadow: "0 -2px 8px #f0f1f2",
        marginTop: "20px",
      }}
    >
      <Space>
        <Button onClick={onCancel}>取消</Button>
        <Button
          type="primary"
          onClick={() => form.submit()}
          disabled={isViewMode}
        >
          {form.getFieldValue("depreciationId") ? "儲存" : "新增"}
        </Button>
      </Space>
    </div>
  );
};

const AssetDepreciationForm: React.FC<FormProps> = ({
  editingDepreciation,
  isViewMode,
  selectedAsset,
  onCancel,
  onSuccess,
  departments,
  assetAccounts,
  assetSubAccounts,
  depreciationMethods,
  isMobile,
  form,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [isAdjustment, setIsAdjustment] = useState(false);

  // 當編輯折舊記錄或選擇資產變更時，更新表單數據
  useEffect(() => {
    if (editingDepreciation) {
      // 設置表單初始值
      const formData: any = {
        ...editingDepreciation,
        depreciationDate: editingDepreciation.depreciationDate
          ? dayjs.unix(editingDepreciation.depreciationDate)
          : null,
      };

      setIsAdjustment(editingDepreciation.isAdjustment);
      form.setFieldsValue(formData);
    } else if (selectedAsset) {
      // 新增模式，設置所選擇資產的相關信息
      form.setFieldsValue({
        ...formInitialValues,
        assetId: selectedAsset.assetId,
        assetNo: selectedAsset.assetNo,
        assetName: selectedAsset.assetName,
        depreciationYear: new Date().getFullYear(),
        depreciationMonth: new Date().getMonth() + 1,
        originalAmount: selectedAsset.purchaseAmount,
        departmentId: selectedAsset.departmentId,
        assetAccountId: selectedAsset.assetAccountId,
        assetSubAccountId: selectedAsset.assetSubAccountId,
        serviceLifeRemaining: selectedAsset.serviceLife || 0,
        remainingValue:
          selectedAsset.purchaseAmount -
          (selectedAsset.accumulatedDepreciationAmount || 0),
        accumulatedDepreciation:
          selectedAsset.accumulatedDepreciationAmount || 0,
      });
    } else {
      form.resetFields();
      form.setFieldsValue(formInitialValues);
      setIsAdjustment(false);
    }
  }, [editingDepreciation, selectedAsset, form]);

  // 處理調整狀態變更
  const handleAdjustmentChange = (checked: boolean) => {
    setIsAdjustment(checked);
    if (!checked) {
      form.setFieldsValue({ adjustmentReason: "" });
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      const submitData = { ...values };

      // 將日期轉換為 Unix 時間戳（秒）
      if (submitData.depreciationDate && submitData.depreciationDate.unix) {
        submitData.depreciationDate = submitData.depreciationDate.unix();
      } else {
        // 如果未設置折舊日期，使用當前日期
        submitData.depreciationDate = Math.floor(Date.now() / 1000);
      }

      // 設置基本信息
      const depreciationId = editingDepreciation?.depreciationId || guidUtils();
      const currentTime = Math.floor(Date.now() / 1000);
      const userId = user?.userId || "";

      // 格式化折舊紀錄
      const formattedDepreciation: Depreciation = {
        ...submitData,
        depreciationId: depreciationId,
        createTime: editingDepreciation
          ? editingDepreciation.createTime
          : currentTime,
        createUserId: editingDepreciation
          ? editingDepreciation.createUserId || ""
          : userId,
        updateTime: editingDepreciation ? currentTime : 0,
        updateUserId: editingDepreciation ? userId : "",
        deleteTime: 0,
        deleteUserId: "",
        isDeleted: false,
      };

      // 將數據傳遞給父組件
      onSuccess(formattedDepreciation);
    } catch (error) {
      console.error("準備數據失敗:", error);
      notifyError("準備數據失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 生成年份選項
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear; i <= currentYear + 5; i++) {
      years.push({ value: i, label: `${i}年` });
    }
    return years;
  };

  // 生成月份選項
  const getMonthOptions = () => {
    const months = [];
    for (let i = 1; i <= 12; i++) {
      months.push({ value: i, label: `${i}月` });
    }
    return months;
  };

  // 渲染資產信息卡片
  const renderAssetInfo = () => {
    if (!selectedAsset && !editingDepreciation) return null;

    const asset = selectedAsset || {
      assetId: editingDepreciation?.assetId || "",
      assetNo: "-",
      assetName: "-",
      departmentId: "",
      assetAccountId: "",
      assetSubAccountId: "",
      purchaseAmount: 0,
    };

    const department = departments.find(
      (d) => d.departmentId === asset.departmentId
    );
    const assetAccount = assetAccounts.find(
      (a) => a.assetAccountId === asset.assetAccountId
    );
    const assetSubAccount = assetSubAccounts.find(
      (s) => s.assetSubAccountId === asset.assetSubAccountId
    );

    return (
      <Card style={{ marginBottom: 16 }}>
        <Descriptions
          title="資產資訊"
          size="small"
          column={isMobile ? 1 : 2}
          bordered
        >
          <Descriptions.Item label="資產編號">
            {asset.assetNo || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="資產名稱">
            {asset.assetName || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="部門">
            {department?.name || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="財產科目">
            {assetAccount?.assetAccountName || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="財產子目">
            {assetSubAccount?.assetSubAccountName || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="購入金額">
            <Tag color="red">{formatTWCurrency(asset.purchaseAmount || 0)}</Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  };

  return (
    <div>
      {/* 資產信息卡片 */}
      {renderAssetInfo()}

      {/* 折舊表單 */}
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={
          editingDepreciation
            ? {
                ...editingDepreciation,
                depreciationDate: editingDepreciation.depreciationDate
                  ? dayjs.unix(editingDepreciation.depreciationDate)
                  : null,
              }
            : formInitialValues
        }
        preserve={false}
      >
        {!selectedAsset && !editingDepreciation && (
          <Alert
            message="請先選擇資產"
            description="您需要先選擇一個資產才能計算折舊"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 隱藏欄位 */}
        <Form.Item name="assetId" hidden>
          <Input />
        </Form.Item>

        <Form.Item name="depreciationId" hidden>
          <Input />
        </Form.Item>

        {/* 折舊年月選擇 */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
            columnGap: "16px",
          }}
        >
          <Form.Item
            name="depreciationYear"
            label="折舊年度"
            rules={[{ required: true, message: "請選擇折舊年度" }]}
          >
            <Select
              disabled={isViewMode}
              placeholder="請選擇折舊年度"
              options={getYearOptions()}
            />
          </Form.Item>

          <Form.Item
            name="depreciationMonth"
            label="折舊月份"
            rules={[{ required: true, message: "請選擇折舊月份" }]}
          >
            <Select
              disabled={isViewMode}
              placeholder="請選擇折舊月份"
              options={getMonthOptions()}
            />
          </Form.Item>
        </div>

        {/* 折舊方法與日期 */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
            columnGap: "16px",
          }}
        >
          <Form.Item
            name="depreciationMethod"
            label="折舊方法"
            rules={[{ required: true, message: "請選擇折舊方法" }]}
          >
            <Select
              disabled={isViewMode}
              placeholder="請選擇折舊方法"
              options={depreciationMethods.map((method) => ({
                value: method,
                label: method,
              }))}
            />
          </Form.Item>

          <Form.Item name="depreciationDate" label="折舊計算日期">
            {isViewMode ? (
              <div style={{ paddingTop: "5px" }}>
                {form.getFieldValue("depreciationDate")
                  ? form.getFieldValue("depreciationDate").format("YYYY-MM-DD")
                  : "-"}
              </div>
            ) : (
              <DatePicker
                placeholder="請選擇折舊計算日期"
                style={{ width: "100%" }}
                format="YYYY-MM-DD"
              />
            )}
          </Form.Item>
        </div>

        {/* 財務金額 */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr 1fr",
            columnGap: "16px",
          }}
        >
          <Form.Item
            name="originalAmount"
            label="原始金額"
            rules={[{ required: true, message: "請輸入原始金額" }]}
          >
            <InputNumber
              disabled={isViewMode}
              placeholder="請輸入原始金額"
              min={0}
              style={{ width: "100%" }}
              formatter={(value) => formatThousands(value)}
              parser={(value) => parseAmount(value) as any}
            />
          </Form.Item>

          <Form.Item
            name="currentDepreciation"
            label="本次折舊"
            rules={[{ required: true, message: "請輸入本次折舊金額" }]}
          >
            <InputNumber
              disabled={isViewMode}
              placeholder="請輸入本次折舊金額"
              min={0}
              style={{ width: "100%" }}
              formatter={(value) => formatThousands(value)}
              parser={(value) => parseAmount(value) as any}
            />
          </Form.Item>

          <Form.Item
            name="accumulatedDepreciation"
            label="累計折舊"
            rules={[{ required: true, message: "請輸入累計折舊金額" }]}
          >
            <InputNumber
              disabled={isViewMode}
              placeholder="請輸入累計折舊金額"
              min={0}
              style={{ width: "100%" }}
              formatter={(value) => formatThousands(value)}
              parser={(value) => parseAmount(value) as any}
            />
          </Form.Item>
        </div>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr 1fr",
            columnGap: "16px",
          }}
        >
          <Form.Item
            name="remainingValue"
            label="剩餘價值"
            rules={[{ required: true, message: "請輸入剩餘價值" }]}
          >
            <InputNumber
              disabled={isViewMode}
              placeholder="請輸入剩餘價值"
              min={0}
              style={{ width: "100%" }}
              formatter={(value) => formatThousands(value)}
              parser={(value) => parseAmount(value) as any}
            />
          </Form.Item>

          <Form.Item
            name="depreciationRate"
            label="折舊率"
            rules={[{ required: true, message: "請輸入折舊率" }]}
          >
            <InputNumber
              disabled={isViewMode}
              placeholder="請輸入折舊率"
              min={0}
              max={1}
              step={0.01}
              style={{ width: "100%" }}
              formatter={(value) => {
                if (value === null || value === undefined) return "0.00%";
                return `${(Number(value) * 100).toFixed(2)}%`;
              }}
              parser={(displayValue: string | undefined): any => {
                if (!displayValue) return 0;
                const parsed = parseFloat(displayValue.replace("%", "")) / 100;
                return parsed >= 0 && parsed <= 1 ? parsed : 0;
              }}
            />
          </Form.Item>

          <Form.Item
            name="serviceLifeRemaining"
            label="剩餘年限"
            rules={[{ required: true, message: "請輸入剩餘年限" }]}
          >
            <InputNumber
              disabled={isViewMode}
              placeholder="請輸入剩餘年限"
              min={0}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </div>

        {/* 調整相關 */}
        <Divider style={{ margin: "16px 0" }} />
        <div
          style={{
            display: "grid",
            gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
            columnGap: "16px",
          }}
        >
          <Form.Item
            name="isAdjustment"
            label="是否為調整"
            valuePropName="checked"
          >
            <Switch
              disabled={isViewMode}
              checked={isAdjustment}
              onChange={handleAdjustmentChange}
              checkedChildren="是"
              unCheckedChildren="否"
            />
          </Form.Item>
        </div>

        {isAdjustment && (
          <Form.Item
            name="adjustmentReason"
            label="調整原因"
            rules={[
              {
                required: true,
                message: "請輸入調整原因",
              },
            ]}
          >
            <TextArea
              disabled={isViewMode}
              placeholder="請輸入調整原因"
              autoSize={{ minRows: 2, maxRows: 4 }}
              maxLength={200}
              showCount
            />
          </Form.Item>
        )}

        <Form.Item name="notes" label="備註">
          <TextArea
            disabled={isViewMode}
            placeholder="請輸入備註"
            autoSize={{ minRows: 2, maxRows: 4 }}
            maxLength={200}
            showCount
          />
        </Form.Item>

        {/* 表單按鈕 */}
        <FormButtons
          onCancel={onCancel}
          isViewMode={isViewMode}
          isMobile={isMobile}
          form={form}
        />
      </Form>
    </div>
  );
};

export default AssetDepreciationForm;
