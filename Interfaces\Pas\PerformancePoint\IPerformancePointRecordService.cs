using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IPerformancePointRecordService
    {
        /// <summary>
        /// 根據使用者ID取得點數紀錄列表
        /// </summary>
        /// <param name="userId">使用者ID</param>
        /// <returns>點數紀錄列表</returns>
        Task<List<PerformancePointRecordDTO>> GetByUserIdAsync(string userId);

        /// <summary>
        /// 取得單一點數紀錄明細
        /// </summary>
        /// <param name="uid">點數紀錄UID</param>
        /// <returns>點數紀錄明細</returns>
        Task<PerformancePointRecordDTO?> GetDetailAsync(string uid);

        /// <summary>
        /// 新增點數紀錄資料
        /// </summary>
        /// <param name="dto">點數紀錄資料</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> AddAsync(PerformancePointRecordDTO dto);

        /// <summary>
        /// 編輯點數紀錄資料
        /// </summary>
        /// <param name="dto">點數紀錄資料</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> EditAsync(PerformancePointRecordDTO dto);

        /// <summary>
        /// 刪除點數紀錄資料（軟刪除）
        /// </summary>
        /// <param name="uid">點數紀錄UID</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> DeleteAsync(string uid);


        /// <summary>
        /// 獲取點數日期彙總
        /// </summary>
        /// <param name="dto">點數範圍搜尋條件結構</param>
        /// <returns>點數日期彙總列表</returns>
        Task<List<PerformancePointSummaryResult>> GetPerformancePointSummaryAsync(PerformancePointSummaryQuery dto);

    }
}
