"use client";

import { Calendar, Badge, Typography, Spin } from "antd";
import { CalendarOutlined } from "@ant-design/icons";
import type { Dayjs } from "dayjs";
import { useState, useEffect } from "react";

const { Title } = Typography;

export function CalendarWidget() {
  const [loading, setLoading] = useState(true);

  // 模擬事件數據
  const events = [
    { date: "2024-01-15", type: "success", content: "專案會議" },
    { date: "2024-01-18", type: "warning", content: "系統維護" },
    { date: "2024-01-22", type: "error", content: "重要截止日" },
    { date: "2024-01-25", type: "default", content: "團隊聚餐" },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const getListData = (value: Dayjs) => {
    const dateStr = value.format("YYYY-MM-DD");
    return events.filter((event) => event.date === dateStr);
  };

  const cellRender = (current: Dayjs, info: any) => {
    if (info.type !== "date") return info.originNode;

    const listData = getListData(current);
    return (
      <div className="events">
        <ul style={{ margin: 0, padding: 0, listStyle: "none" }}>
          {listData.map((item, index) => (
            <li key={index}>
              <Badge
                status={item.type as any}
                text={item.content}
                style={{ fontSize: "12px" }}
              />
            </li>
          ))}
        </ul>
      </div>
    );
  };

  if (loading) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large">
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
            }}
          >
            載入行事曆...
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: "100%" }}>
      <div
        style={{ display: "flex", alignItems: "center", marginBottom: "16px" }}
      ></div>
      <Calendar
        fullscreen={false}
        cellRender={cellRender}
        style={{ fontSize: "12px" }}
      />
    </div>
  );
}

export default CalendarWidget;
