using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("保險級距歷程管理")]
    public class InsuranceHistoryController : ControllerBase
    {
        private readonly IInsuranceHistoryService _interface;

        public InsuranceHistoryController(IInsuranceHistoryService insuranceHistoryService)
        {
            _interface = insuranceHistoryService;
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得保險級距歷程明細", Description = "依uid取得保險級距歷程明細")]
        public async Task<IActionResult> GetInsuranceHistoryDetail(string _uid)
        {
            try
            {
                var result = await _interface.GetByUidAsync(_uid);
                if (result == null)
                {
                    return NotFound(new { result = false, msg = "找不到對應的保險級距歷程資料" });
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }

        [HttpGet]
        [Route("GetByUserAndType/{userId}/{insuranceType}")]
        [SwaggerOperation(Summary = "取得員工保險歷程列表", Description = "依員工ID和保險類型取得保險歷程列表")]
        public async Task<IActionResult> GetByUserAndType(string userId, int insuranceType)
        {
            try
            {
                var result = await _interface.GetByUserAndTypeAsync(userId, insuranceType);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }

        [HttpPost]
        [Route("GetEffectiveGrade")]
        [SwaggerOperation(Summary = "取得有效保險級距", Description = "取得特定日期的有效保險級距資料")]
        public async Task<IActionResult> GetEffectiveGrade([FromBody] GetEffectiveGradeRequest request)
        {
            try
            {
                var result = await _interface.GetEffectiveGradeAsync(request.UserId, request.InsuranceType, request.TargetDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }

        [HttpPost]
        [Route("GetAllEffectiveGrades")]
        [SwaggerOperation(Summary = "取得所有有效保險級距", Description = "取得特定日期的所有保險類型有效級距資料")]
        public async Task<IActionResult> GetAllEffectiveGrades([FromBody] GetAllEffectiveGradesRequest request)
        {
            try
            {
                var result = await _interface.GetAllEffectiveGradesAsync(request.UserId, request.TargetDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增保險級距歷程", Description = "新增保險級距歷程資料")]
        public async Task<IActionResult> AddInsuranceHistory([FromBody] InsuranceHistoryDTO _data)
        {
            try
            {
                var (result, msg) = await _interface.AddAsync(_data);
                return Ok(new { result, msg });
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯保險級距歷程", Description = "編輯保險級距歷程資料")]
        public async Task<IActionResult> EditInsuranceHistory([FromBody] InsuranceHistoryDTO _data)
        {
            try
            {
                var (result, msg) = await _interface.EditAsync(_data);
                return Ok(new { result, msg });
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除保險級距歷程", Description = "刪除保險級距歷程資料")]
        public async Task<IActionResult> DeleteInsuranceHistory([FromBody] string _uid)
        {
            try
            {
                var (result, msg) = await _interface.DeleteAsync(_uid);
                return Ok(new { result, msg });
            }
            catch (Exception ex)
            {
                return BadRequest(new { result = false, msg = ex.Message });
            }
        }
    }
}