using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("財產狀態管理")]
    public class AssetStatusController : ControllerBase
    {
        private readonly IAssetStatusService _Interface;

        public AssetStatusController(IAssetStatusService assetStatusService)
        {
            _Interface = assetStatusService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得財產狀態列表", Description = "取得所有財產狀態列表")]
        public async Task<IActionResult> GetAssetStatusList()
        {
            var statuses = await _Interface.GetAssetStatusAsync();
            return Ok(statuses);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得財產狀態明細", Description = "依ID取得財產狀態明細")]
        public async Task<IActionResult> GetAssetStatusDetail(string id)
        {
            var status = await _Interface.GetAssetStatusDetailAsync(id);
            return Ok(status);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增財產狀態", Description = "新增財產狀態資料")]
        public async Task<IActionResult> AddAssetStatus([FromBody] AssetStatusDTO _data)
        {
            var (result, msg) = await _Interface.AddAssetStatusAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯財產狀態", Description = "修改已存在之財產狀態資料")]
        public async Task<IActionResult> EditAssetStatus([FromBody] AssetStatusDTO _data)
        {
            var (result, msg) = await _Interface.EditAssetStatusAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除財產狀態", Description = "刪除已存在之財產狀態資料")]
        public async Task<IActionResult> DeleteAssetStatus([FromBody] AssetStatusDTO _data)
        {
            var (result, msg) = await _Interface.DeleteAssetStatusAsync(_data);
            return Ok(new { result, msg });
        }
    }
}