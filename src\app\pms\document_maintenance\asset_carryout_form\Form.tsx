"use client";

import React, { useEffect, useState } from "react";
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Button,
  Row,
  Col,
  message,
  Space,
  Divider,
  Card,
  Typography,
  Descriptions,
} from "antd";
import dayjs from "dayjs";
import {
  AssetCarryOut,
  CreateAssetCarryOutRequest,
  UpdateAssetCarryOutRequest,
} from "./interface";
import {
  PURPOSE_OPTIONS,
  STATUS_OPTIONS,
  generateCarryOutNumber,
} from "./config";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { getCookie } from "@/utils/cookies";
import { getUserInfo } from "@/utils/userInfo";
import { getAssets } from "@/services/pms/assetService";
import { getUsers, User } from "@/services/common/userService";

// 移除 Option 解構，直接使用 Select.Option
const { TextArea } = Input;
const { Title } = Typography;

interface AssetCarryOutFormProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (
    data: CreateAssetCarryOutRequest | UpdateAssetCarryOutRequest
  ) => Promise<void>;
  editData?: AssetCarryOut | null;
  mode: "add" | "edit" | "view";
}

const AssetCarryOutForm: React.FC<AssetCarryOutFormProps> = ({
  visible,
  onClose,
  onSubmit,
  editData,
  mode,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [autoCarryOutNo, setAutoCarryOutNo] = useState<string>("");
  const [assets, setAssets] = useState<any[]>([]);
  const [employees, setEmployees] = useState<User[]>([]);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [submitData, setSubmitData] = useState<any>(null);

  // 載入下拉選單數據
  const loadSelectData = async () => {
    setLoadingData(true);
    try {
      const [assetsResponse, employeesResponse] = await Promise.all([
        getAssets(),
        getUsers(),
      ]);

      if (assetsResponse.success) {
        setAssets(assetsResponse.data || []);
      }

      if (employeesResponse.success) {
        const validEmployees = (employeesResponse.data || []).filter(
          (employee: any) => employee?.userId
        );
        setEmployees(validEmployees);
      }
    } catch (error) {
      console.error("載入下拉選單數據失敗:", error);
      message.error("載入選單數據失敗");
    } finally {
      setLoadingData(false);
    }
  };

  // 處理申請人選擇變化
  const handleApplicantChange = (userId: string) => {
    const selectedEmployee = employees.find((emp) => emp.userId === userId);
    if (selectedEmployee) {
      // 自動填充申請人姓名
      form.setFieldsValue({
        applicantName: selectedEmployee.name || selectedEmployee.account,
      });
    }
  };

  // 處理財產選擇變化
  const handleAssetChange = (assetId: string) => {
    const selectedAsset = assets.find(
      (asset) => asset.asset?.assetId === assetId
    );
    if (selectedAsset) {
      // 自動填充財產相關資訊
      form.setFieldsValue({
        assetNo: selectedAsset.asset.assetNo,
        assetName: selectedAsset.asset.assetName,
      });
    }
  };

  // 載入選單數據
  useEffect(() => {
    if (visible) {
      loadSelectData();
    }
  }, [visible]);

  // 初始化表單
  useEffect(() => {
    if (visible) {
      if (mode === "add") {
        // 新增模式 - 重置表單並設置預設值
        form.resetFields();

        // 自動生成攜出單號
        const newCarryOutNo = generateCarryOutNumber();
        setAutoCarryOutNo(newCarryOutNo);

        // 設置預設值
        const userInfo = getUserInfo();
        const currentUserId = userInfo?.userId || "";
        const currentUserName = userInfo?.name || "";
        const today = dayjs(); // 使用今日日期

        form.setFieldsValue({
          carryOutNo: newCarryOutNo,
          applicantId: currentUserId,
          applicantName: currentUserName,
          applicationDate: today, // 預設為今日
          purpose: "業務使用",
          status: "待審核",
        });
      } else if (mode === "edit" || mode === "view") {
        // 編輯/查看模式 - 填入現有資料
        if (editData) {
          form.setFieldsValue({
            ...editData,
            applicationDate: editData.applicationDate
              ? dayjs.unix(editData.applicationDate)
              : null,
            plannedCarryOutDate: editData.plannedCarryOutDate
              ? dayjs.unix(editData.plannedCarryOutDate)
              : null,
            plannedReturnDate: editData.plannedReturnDate
              ? dayjs.unix(editData.plannedReturnDate)
              : null,
            actualCarryOutDate: editData.actualCarryOutDate
              ? dayjs.unix(editData.actualCarryOutDate)
              : null,
            actualReturnDate: editData.actualReturnDate
              ? dayjs.unix(editData.actualReturnDate)
              : null,
          });
        }
      }
    }
  }, [visible, mode, editData, form]);

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 轉換日期格式
      const formattedData = {
        ...values,
        applicationDate: values.applicationDate
          ? DateTimeExtensions.toTimestamp(values.applicationDate.toDate())
          : DateTimeExtensions.getCurrentTimestamp(),
        plannedCarryOutDate: values.plannedCarryOutDate
          ? DateTimeExtensions.toTimestamp(values.plannedCarryOutDate.toDate())
          : 0,
        plannedReturnDate: values.plannedReturnDate
          ? DateTimeExtensions.toTimestamp(values.plannedReturnDate.toDate())
          : 0,
        actualCarryOutDate: values.actualCarryOutDate
          ? DateTimeExtensions.toTimestamp(values.actualCarryOutDate.toDate())
          : undefined,
        actualReturnDate: values.actualReturnDate
          ? DateTimeExtensions.toTimestamp(values.actualReturnDate.toDate())
          : undefined,
      };

      // 添加模式特定的資料
      if (mode === "edit" && editData) {
        // 編輯模式需要包含完整的系統欄位
        formattedData.carryOutId = editData.carryOutId;
        formattedData.carryOutNo = editData.carryOutNo;
        formattedData.status = editData.status;
        formattedData.statusName = editData.statusName;
        formattedData.approverId = editData.approverId;
        formattedData.approverName = editData.approverName;
        formattedData.approvalDate = editData.approvalDate;
        formattedData.approvalComment = editData.approvalComment;
        formattedData.isDeleted = editData.isDeleted;
        formattedData.createTime = editData.createTime;
        formattedData.createUserId = editData.createUserId;
        formattedData.updateTime = DateTimeExtensions.getCurrentTimestamp();
        const currentUserInfo = getUserInfo();
        formattedData.updateUserId = currentUserInfo?.userId || "";
        formattedData.deleteTime = editData.deleteTime;
        formattedData.deleteUserId = editData.deleteUserId;
      }

      // 如果是新增模式，顯示確認對話框
      if (mode === "add") {
        setSubmitData(formattedData);
        setConfirmModalVisible(true);
      } else {
        // 編輯模式直接提交
        await performSubmit(formattedData);
      }
    } catch (error) {
      console.error("表單驗證失敗:", error);
    }
  };

  // 實際執行提交
  const performSubmit = async (data: any) => {
    try {
      setLoading(true);
      await onSubmit(data);
      handleClose();
    } catch (error) {
      console.error("表單提交失敗:", error);
    } finally {
      setLoading(false);
    }
  };

  // 確認提交
  const handleConfirmSubmit = async () => {
    setConfirmModalVisible(false);
    await performSubmit(submitData);
  };

  // 關閉表單
  const handleClose = () => {
    form.resetFields();
    setAutoCarryOutNo("");
    setConfirmModalVisible(false);
    setSubmitData(null);
    onClose();
  };

  // 判斷欄位是否只讀
  const isReadOnly = mode === "view";

  // 模態框標題
  const getModalTitle = () => {
    switch (mode) {
      case "add":
        return "新增攜出申請";
      case "edit":
        return "編輯攜出申請";
      case "view":
        return "查看攜出申請";
      default:
        return "攜出申請";
    }
  };

  return (
    <>
      <Modal
        title={getModalTitle()}
        open={visible}
        onCancel={handleClose}
        width={900}
        footer={
          mode === "view" ? (
            <Button onClick={handleClose}>關閉</Button>
          ) : (
            <Space>
              <Button onClick={handleClose}>取消</Button>
              <Button type="primary" onClick={handleSubmit} loading={loading}>
                {mode === "add" ? "建立" : "更新"}
              </Button>
            </Space>
          )
        }
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          style={{ maxHeight: "70vh", overflowY: "auto" }}
        >
          {/* 基本資訊 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5} style={{ marginBottom: 16 }}>
              基本資訊
            </Title>

            <Row gutter={[16, 0]}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="攜出單號"
                  name="carryOutNo"
                  rules={[{ required: true, message: "請輸入攜出單號" }]}
                >
                  <Input
                    placeholder="攜出單號"
                    disabled={true}
                    style={{
                      backgroundColor: "#f5f5f5",
                      color: "#666",
                      cursor: "not-allowed",
                    }}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  label="申請日期"
                  name="applicationDate"
                  rules={[{ required: true, message: "請選擇申請日期" }]}
                >
                  <DatePicker
                    style={{ width: "100%" }}
                    disabled={isReadOnly}
                    format="YYYY/MM/DD"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="申請人"
                  name="applicantId"
                  rules={[{ required: true, message: "請選擇申請人" }]}
                >
                  <Select
                    placeholder="請選擇申請人"
                    loading={loadingData}
                    showSearch
                    allowClear={false}
                    disabled={isReadOnly}
                    notFoundContent={loadingData ? "載入中..." : "無相關資料"}
                    filterOption={(input: string, option: any) => {
                      const label = option?.children?.toString() || "";
                      return label.toLowerCase().includes(input.toLowerCase());
                    }}
                    onChange={handleApplicantChange}
                  >
                    {employees.map((employee, index) => (
                      <Select.Option
                        key={`applicant-${employee.userId}-${index}`}
                        value={employee.userId}
                        title={employee.name || employee.account}
                      >
                        {employee.name || employee.account}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item label="申請人姓名" name="applicantName" hidden>
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 財產資訊 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5} style={{ marginBottom: 16 }}>
              財產資訊
            </Title>

            <Form.Item
              label="攜出財產"
              name="assetId"
              rules={[{ required: true, message: "請選擇財產" }]}
            >
              <Select
                placeholder="請選擇財產"
                loading={loadingData}
                showSearch
                disabled={isReadOnly}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option?.children
                    ?.toString()
                    ?.toLowerCase()
                    .includes(input.toLowerCase()) || false
                }
                onChange={handleAssetChange}
              >
                {assets.map((asset) => (
                  <Select.Option
                    key={asset.asset?.assetId}
                    value={asset.asset?.assetId}
                  >
                    {asset.asset?.assetNo} - {asset.asset?.assetName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            {/* 隱藏的財產相關欄位，用於存儲 */}
            <Form.Item name="assetNo" hidden>
              <Input />
            </Form.Item>
            <Form.Item name="assetName" hidden>
              <Input />
            </Form.Item>
          </Card>

          {/* 攜出資訊 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5} style={{ marginBottom: 16 }}>
              攜出資訊
            </Title>

            <Row gutter={[16, 0]}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="攜出目的"
                  name="purpose"
                  rules={[{ required: true, message: "請選擇攜出目的" }]}
                >
                  <Select placeholder="請選擇攜出目的" disabled={isReadOnly}>
                    {PURPOSE_OPTIONS.map((option) => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  label="攜出地點"
                  name="destination"
                  rules={[{ required: true, message: "請輸入攜出地點" }]}
                >
                  <Input placeholder="攜出地點" disabled={isReadOnly} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="預計攜出日期"
                  name="plannedCarryOutDate"
                  rules={[{ required: true, message: "請選擇預計攜出日期" }]}
                >
                  <DatePicker
                    style={{ width: "100%" }}
                    disabled={isReadOnly}
                    format="YYYY/MM/DD"
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  label="預計歸還日期"
                  name="plannedReturnDate"
                  rules={[{ required: true, message: "請選擇預計歸還日期" }]}
                >
                  <DatePicker
                    style={{ width: "100%" }}
                    disabled={isReadOnly}
                    format="YYYY/MM/DD"
                  />
                </Form.Item>
              </Col>

              <Col xs={24}>
                <Form.Item label="備註" name="notes">
                  <TextArea
                    rows={4}
                    placeholder="請輸入備註"
                    maxLength={1000}
                    disabled={isReadOnly}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 實際日期 (僅在編輯/查看時顯示) */}
          {mode !== "add" && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ marginBottom: 16 }}>
                實際日期
              </Title>

              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item label="實際攜出日期" name="actualCarryOutDate">
                    <DatePicker
                      style={{ width: "100%" }}
                      disabled={isReadOnly}
                      format="YYYY/MM/DD"
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item label="實際歸還日期" name="actualReturnDate">
                    <DatePicker
                      style={{ width: "100%" }}
                      disabled={isReadOnly}
                      format="YYYY/MM/DD"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {/* 狀態與審核資訊 (僅在編輯/查看時顯示) */}
          {mode !== "add" && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ marginBottom: 16 }}>
                狀態與審核資訊
              </Title>

              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item label="狀態" name="status">
                    <Select disabled>
                      {STATUS_OPTIONS.map((option) => (
                        <Select.Option key={option.value} value={option.value}>
                          {option.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item label="審核人" name="approverName">
                    <Input disabled />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 0]}>
                <Col xs={24}>
                  <Form.Item label="審核意見" name="approvalComment">
                    <TextArea rows={3} disabled />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}
        </Form>
      </Modal>

      {/* 確認提交對話框 */}
      <Modal
        title="確認新增攜出申請"
        open={confirmModalVisible}
        onCancel={() => {
          setConfirmModalVisible(false);
          setSubmitData(null);
        }}
        onOk={handleConfirmSubmit}
        okText="確認提交"
        cancelText="取消"
        confirmLoading={loading}
        width={700}
      >
        {submitData && (
          <div>
            <p style={{ marginBottom: 16, color: "#666" }}>
              請確認以下資訊無誤後提交：
            </p>

            <Descriptions
              bordered
              size="small"
              column={2}
              style={{ marginBottom: 16 }}
            >
              <Descriptions.Item label="攜出單號" span={2}>
                {submitData.carryOutNo}
              </Descriptions.Item>

              <Descriptions.Item label="申請人">
                {submitData.applicantName}
              </Descriptions.Item>

              <Descriptions.Item label="申請日期">
                {submitData.applicationDate
                  ? dayjs(submitData.applicationDate * 1000).format(
                      "YYYY/MM/DD"
                    )
                  : "未設定"}
              </Descriptions.Item>

              <Descriptions.Item label="財產編號">
                {submitData.assetNo || "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="財產名稱">
                {submitData.assetName || "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="攜出目的">
                {submitData.purpose || "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="攜出地點">
                {submitData.destination || "未填寫"}
              </Descriptions.Item>

              <Descriptions.Item label="預計攜出日期">
                {submitData.plannedCarryOutDate
                  ? dayjs(submitData.plannedCarryOutDate * 1000).format(
                      "YYYY/MM/DD"
                    )
                  : "未設定"}
              </Descriptions.Item>

              <Descriptions.Item label="預計歸還日期">
                {submitData.plannedReturnDate
                  ? dayjs(submitData.plannedReturnDate * 1000).format(
                      "YYYY/MM/DD"
                    )
                  : "未設定"}
              </Descriptions.Item>

              {submitData.notes && (
                <Descriptions.Item label="備註" span={2}>
                  {submitData.notes}
                </Descriptions.Item>
              )}
            </Descriptions>

            <div
              style={{
                backgroundColor: "#f6ffed",
                border: "1px solid #b7eb8f",
                borderRadius: "6px",
                padding: "12px",
                marginTop: "16px",
              }}
            >
              <p style={{ margin: 0, color: "#389e0d", fontSize: "14px" }}>
                <strong>提醒：</strong>
              </p>
              <p
                style={{
                  margin: "4px 0 0 0",
                  color: "#389e0d",
                  fontSize: "14px",
                }}
              >
                • 確認提交後，申請將進入審核流程
                <br />
                • 請確保財產資訊和攜出日期正確
                <br />• 攜出單號依系統自動生成為主
              </p>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default AssetCarryOutForm;
