using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("資產來源管理")]
    public class AssetSourceController : ControllerBase
    {
        private readonly IAssetSourceService _assetSourceService;

        public AssetSourceController(IAssetSourceService assetSourceService)
        {
            _assetSourceService = assetSourceService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得資產來源列表", Description = "取得所有資產來源列表")]
        public async Task<IActionResult> GetAssetSourceList()
        {
            var sources = await _assetSourceService.GetAllAsync();
            return Ok(sources);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得資產來源明細", Description = "依ID取得資產來源明細")]
        public async Task<IActionResult> GetAssetSourceDetail(int id)
        {
            var source = await _assetSourceService.GetByIdAsync(id);
            if (source == null)
                return NotFound($"找不到編號為{id}的資產來源。");

            return Ok(source);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增資產來源", Description = "新增資產來源")]
        public async Task<IActionResult> AddAssetSource([FromBody] AssetSourceDTO assetSource)
        {
            var result = await _assetSourceService.AddAsync(assetSource);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯資產來源", Description = "編輯資產來源")]
        public async Task<IActionResult> EditAssetSource([FromBody] AssetSourceDTO assetSource)
        {
            var result = await _assetSourceService.UpdateAsync(assetSource);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除資產來源", Description = "刪除資產來源")]
        public async Task<IActionResult> DeleteAssetSource([FromBody] AssetSourceDTO assetSource)
        {
            var result = await _assetSourceService.DeleteAsync(assetSource);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }
    }
}