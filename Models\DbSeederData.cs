﻿using System.Reflection;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;


namespace FAST_ERP_Backend.Models
{
    public class DbSeederData
    {
        private readonly IConfiguration _configuration;

        public DbSeederData()
        {
            // 確保僅鎖定 defaultData.json
            _configuration = new ConfigurationBuilder()
                .AddJsonFile("defaultData.json", optional: false, reloadOnChange: false)
                .Build();
        }

        public void SeedData(ModelBuilder modelBuilder)
        {
            
            try 
            {
                var rootSection = _configuration.GetChildren();
                if (rootSection == null) return;
                

                foreach (var section in rootSection)
                {
                    var keyName = section.Key;
                    // 動態取得對應的型別
                    var entityType = GetEntityTypeByName(keyName);

                    // 使用泛型方法來處理資料
                    var methodInfo = typeof(DbSeederData).GetMethod(nameof(SeedEntityData), 
                        BindingFlags.NonPublic | BindingFlags.Instance);
                    var genericMethod = methodInfo?.MakeGenericMethod(entityType);
                    genericMethod?.Invoke(this, new object[] { modelBuilder, keyName });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"填充資料時發生錯誤: {ex.Message}");
                throw;
            }

        }
    

        private Type? GetEntityTypeByName(string typeName)
        {
            // 假設所有實體都在同一個組件中
            // 你需要根據實際的命名空間調整
            return Assembly.GetExecutingAssembly()
                .GetTypes()
                .FirstOrDefault(t => t.Name == typeName);
        }

        private void SeedEntityData<T>(ModelBuilder modelBuilder, string sectionName) where T : class
        {
            var data = _configuration.GetSection(sectionName).Get<List<T>>();
            if (data == null) return;

            foreach (var item in data)
            {
                modelBuilder.Entity<T>().HasData(item);
            }
        }
    }  
}
