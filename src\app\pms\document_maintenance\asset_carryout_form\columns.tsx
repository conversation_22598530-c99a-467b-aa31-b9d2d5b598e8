import React from "react";
import { ColumnsType } from "antd/es/table";
import { Tag, Button, Space, Tooltip, Typography, Descriptions } from "antd";
import {
  EditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
} from "@ant-design/icons";

const { Text } = Typography;
import { AssetCarryOut } from "./interface";
import { getStatusColor, translateStatus } from "./config";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";

// 操作按鈕介面
interface ActionButtonsProps {
  record: AssetCarryOut;
  onEdit: (record: AssetCarryOut) => void;
  onView: (record: AssetCarryOut) => void;
  onApprove: (record: AssetCarryOut) => void;
  onReject: (record: AssetCarryOut) => void;
  onCarryOut: (record: AssetCarryOut) => void;
  onReturn: (record: AssetCarryOut) => void;
}

// 操作按鈕組件
const ActionButtons: React.FC<ActionButtonsProps> = ({
  record,
  onEdit,
  onView,
  onApprove,
  onReject,
  onCarryOut,
  onReturn,
}) => {
  const status = record.status || record.statusName;
  const translatedStatus = translateStatus(status);

  const actions = [];

  // 查看按鈕 - 所有狀態都可查看
  actions.push(
    <Button
      key="view"
      type="link"
      icon={<EyeOutlined />}
      onClick={() => onView(record)}
    >
      查看
    </Button>
  );

  // 編輯按鈕 - 待審核狀態可編輯
  if (translatedStatus === "待審核") {
    actions.push(
      <Button
        key="edit"
        type="link"
        icon={<EditOutlined />}
        onClick={() => onEdit(record)}
      >
        編輯
      </Button>
    );
  }

  // 核准按鈕 - 待審核狀態
  if (translatedStatus === "待審核") {
    actions.push(
      <Button
        key="approve"
        type="link"
        icon={<CheckCircleOutlined />}
        onClick={() => onApprove(record)}
      >
        核准
      </Button>
    );
  }

  // 駁回按鈕 - 待審核狀態
  if (translatedStatus === "待審核") {
    actions.push(
      <Button
        key="reject"
        type="link"
        danger
        icon={<CloseCircleOutlined />}
        onClick={() => onReject(record)}
      >
        駁回
      </Button>
    );
  }

  // 登記攜出按鈕 - 已核准狀態
  if (translatedStatus === "已核准") {
    actions.push(
      <Button
        key="carryOut"
        type="link"
        icon={<ExportOutlined />}
        onClick={() => onCarryOut(record)}
      >
        登記攜出
      </Button>
    );
  }

  // 登記歸還按鈕 - 已攜出狀態
  if (translatedStatus === "已攜出") {
    actions.push(
      <Button
        key="return"
        type="link"
        icon={<ImportOutlined />}
        onClick={() => onReturn(record)}
      >
        登記歸還
      </Button>
    );
  }

  return (
    <Space size="small" wrap>
      {actions}
    </Space>
  );
};

// 生成表格欄位
export const createColumns = (
  actions: Omit<ActionButtonsProps, "record">
): ColumnsType<AssetCarryOut> => [
  {
    key: "index",
    width: 80,
    render: (_, __, index) => <span>{index + 1}</span>,
    sorter: (a, b) => (a.carryOutNo || "").localeCompare(b.carryOutNo || ""),
  },
  {
    title: "攜出單號",
    dataIndex: "carryOutNo",
    key: "carryOutNo",
    render: (text: string, record: AssetCarryOut) => (
      <Tooltip
        title={
          <div style={{ padding: "8px" }}>
            <Descriptions size="small" column={1} bordered>
              <Descriptions.Item label="攜出單號">
                {record.carryOutNo}
              </Descriptions.Item>
              <Descriptions.Item label="財產編號">
                {record.assetNo}
              </Descriptions.Item>
              <Descriptions.Item label="財產名稱">
                {record.assetName}
              </Descriptions.Item>
              <Descriptions.Item label="申請人">
                {record.applicantName}
              </Descriptions.Item>
              <Descriptions.Item label="申請日期">
                {record.applicationDate
                  ? DateTimeExtensions.formatDateFromTimestamp(
                      record.applicationDate
                    )
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="攜出目的">
                {record.purpose}
              </Descriptions.Item>
              <Descriptions.Item label="攜出地點">
                {record.destination}
              </Descriptions.Item>
              <Descriptions.Item label="狀態">
                <Tag color={getStatusColor(record.status || record.statusName)}>
                  {translateStatus(record.status || record.statusName)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="預計攜出日期">
                {record.plannedCarryOutDate
                  ? DateTimeExtensions.formatDateFromTimestamp(
                      record.plannedCarryOutDate
                    )
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="預計歸還日期">
                {record.plannedReturnDate
                  ? DateTimeExtensions.formatDateFromTimestamp(
                      record.plannedReturnDate
                    )
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="實際攜出日期">
                {record.actualCarryOutDate
                  ? DateTimeExtensions.formatDateFromTimestamp(
                      record.actualCarryOutDate
                    )
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="實際歸還日期">
                {record.actualReturnDate
                  ? DateTimeExtensions.formatDateFromTimestamp(
                      record.actualReturnDate
                    )
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="審核人">
                {record.approverName || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="審核日期">
                {record.approvalDate
                  ? DateTimeExtensions.formatDateFromTimestamp(
                      record.approvalDate
                    )
                  : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="審核意見">
                {record.approvalComment || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="備註">
                {record.notes || "-"}
              </Descriptions.Item>
            </Descriptions>
          </div>
        }
        color="#fff"
        placement="right"
        styles={{
          root: { maxWidth: "500px" },
          body: { padding: "0" },
        }}
      >
        <span style={{ fontWeight: "bold", color: "#1890ff" }}>{text}</span>
      </Tooltip>
    ),
    sorter: (a, b) => (a.carryOutNo || "").localeCompare(b.carryOutNo || ""),
  },
  {
    title: "財產編號",
    dataIndex: "assetNo",
    key: "assetNo",
    ellipsis: true,
    render: (text: string) => (
      <span style={{ fontFamily: "monospace" }}>{text}</span>
    ),
    sorter: (a, b) => (a.assetNo || "").localeCompare(b.assetNo || ""),
  },
  {
    title: "財產名稱",
    dataIndex: "assetName",
    key: "assetName",
    ellipsis: {
      showTitle: false,
    },
    sorter: (a, b) => (a.assetName || "").localeCompare(b.assetName || ""),
    render: (text: string) => (
      <Tooltip placement="topLeft" title={text}>
        {text}
      </Tooltip>
    ),
  },
  {
    title: "申請人",
    dataIndex: "applicantName",
    key: "applicantName",
    sorter: (a, b) =>
      (a.applicantName || "").localeCompare(b.applicantName || ""),
  },
  {
    title: "申請日期",
    dataIndex: "applicationDate",
    key: "applicationDate",
    render: (timestamp: number) => {
      if (!timestamp) return "-";
      return DateTimeExtensions.formatDateFromTimestamp(timestamp);
    },
    sorter: (a, b) => (a.applicationDate || 0) - (b.applicationDate || 0),
  },
  {
    title: "攜出目的",
    dataIndex: "purpose",
    key: "purpose",
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip placement="topLeft" title={text}>
        {text}
      </Tooltip>
    ),
    sorter: (a, b) => (a.purpose || "").localeCompare(b.purpose || ""),
  },
  {
    title: "攜出地點",
    dataIndex: "destination",
    key: "destination",
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip placement="topLeft" title={text}>
        {text}
      </Tooltip>
    ),
    sorter: (a, b) => (a.destination || "").localeCompare(b.destination || ""),
  },
  {
    title: "狀態",
    dataIndex: "status",
    key: "status",
    render: (status: string, record: AssetCarryOut) => {
      const statusValue = status || record.statusName;
      const translatedStatus = translateStatus(statusValue);
      const color = getStatusColor(statusValue);

      return (
        <Tag color={color} style={{ borderRadius: "4px" }}>
          {translatedStatus}
        </Tag>
      );
    },
    filters: [
      { text: "待審核", value: "PENDING" },
      { text: "已核准", value: "APPROVED" },
      { text: "已駁回", value: "REJECTED" },
      { text: "已攜出", value: "CARRIED_OUT" },
      { text: "已歸還", value: "RETURNED" },
      { text: "逾期", value: "OVERDUE" },
      { text: "已取消", value: "CANCELLED" },
    ],
    onFilter: (value: any, record: AssetCarryOut) => {
      const status = record.status || record.statusName;
      return status === value || translateStatus(status) === value;
    },
    sorter: (a, b) => (a.status || "").localeCompare(b.status || ""),
  },
  {
    title: "操作",
    key: "action",
    width: 300,
    render: (_, record: AssetCarryOut) => (
      <ActionButtons record={record} {...actions} />
    ),
  },
];

// 匯出用的欄位映射
export const EXPORT_COLUMNS = {
  carryOutNo: "攜出單號",
  assetNo: "財產編號",
  assetName: "財產名稱",
  applicantName: "申請人",
  applicationDate: "申請日期",
  purpose: "攜出目的",
  destination: "攜出地點",
  plannedCarryOutDate: "預計攜出日期",
  plannedReturnDate: "預計歸還日期",
  actualCarryOutDate: "實際攜出日期",
  actualReturnDate: "實際歸還日期",
  statusName: "狀態",
  approverName: "審核人",
  approvalDate: "審核日期",
  approvalComment: "審核意見",
  notes: "備註",
};

// 預設顯示的欄位（用於響應式設計）
export const DEFAULT_COLUMNS = [
  "carryOutNo",
  "assetName",
  "applicantName",
  "applicationDate",
  "purpose",
  "plannedReturnDate",
  "statusName",
  "action",
];

// 檢查狀態是否匹配（支援中文和英文）
const isStatusMatch = (
  currentStatus: string,
  targetStatus: string
): boolean => {
  return translateStatus(currentStatus) === targetStatus;
};

// 獲取手機版表格列定義
export const getMobileColumns = (
  actions: Omit<ActionButtonsProps, "record">
): ColumnsType<AssetCarryOut> => [
  {
    title: "攜出申請",
    key: "mobile",
    render: (_, record) => (
      <div>
        <div style={{ marginBottom: "8px" }}>
          <Text strong>{record.carryOutNo}</Text>
          <Tag
            color={getStatusColor(record.status || record.statusName)}
            style={{ marginLeft: "8px" }}
          >
            {translateStatus(record.status || record.statusName)}
          </Tag>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">財產：</Text>
          <Text>{record.assetName}</Text>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">申請人：</Text>
          <Text>{record.applicantName}</Text>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">攜出目的：</Text>
          <Text>{record.purpose}</Text>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">申請日期：</Text>
          <Text>
            {DateTimeExtensions.formatDateFromTimestamp(record.applicationDate)}
          </Text>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">預計歸還：</Text>
          <Text>
            {DateTimeExtensions.formatDateFromTimestamp(
              record.plannedReturnDate
            )}
          </Text>
        </div>
        <div>
          <Button
            type="link"
            size="small"
            onClick={() => actions.onView(record)}
          >
            查看詳情
          </Button>
          {isStatusMatch(record.status || record.statusName, "待審核") && (
            <Button
              type="link"
              size="small"
              onClick={() => actions.onEdit(record)}
            >
              編輯
            </Button>
          )}
          {isStatusMatch(record.status || record.statusName, "待審核") && (
            <>
              <Button
                type="link"
                size="small"
                onClick={() => actions.onApprove(record)}
                style={{ color: "#52c41a" }}
              >
                核准
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => actions.onReject(record)}
                style={{ color: "#ff4d4f" }}
              >
                駁回
              </Button>
            </>
          )}
          {isStatusMatch(record.status || record.statusName, "已核准") && (
            <Button
              type="link"
              size="small"
              onClick={() => actions.onCarryOut(record)}
              style={{ color: "#1890ff" }}
            >
              登記攜出
            </Button>
          )}
          {isStatusMatch(record.status || record.statusName, "已攜出") && (
            <Button
              type="link"
              size="small"
              onClick={() => actions.onReturn(record)}
              style={{ color: "#52c41a" }}
            >
              登記歸還
            </Button>
          )}
        </div>
      </div>
    ),
  },
];
