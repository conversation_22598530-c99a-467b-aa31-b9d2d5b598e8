﻿using FAST_ERP_Backend.Models.Common;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IRolesPermissionsService
    {        
        Task<List<RolesPermissionsDTO>> GetRolesPermissionsAsync(string rolesId = "");
        Task<(bool, string)> AddRolesPermissionsAsync(RolesPermissionsDTO dto, string tokenUid, string rolesId, long CreateTime);
        Task<(bool, string)> DeleteRolesPermissionsAsync(RolesPermissions rolesPermissions, string tokenUid, string rolesId, long DeleteTime);
    }
}
