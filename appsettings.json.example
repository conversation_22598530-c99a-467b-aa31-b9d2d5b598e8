{
  "ConnectionStrings": {
//"DefaultConnection": "Data Source=172.16.1.10;Initial Catalog=FAST_ERP;Persist Security Info=True;User ID=fasterp_root;Password=fasterproot!!!123;Encrypt=True;TrustServerCertificate=True;",
    "DefaultConnection": "Data Source=.\\SQLEXPRESS2022;Initial Catalog=FAST_ERP;Persist Security Info=True;User ID=sa;Password=********;Encrypt=True;TrustServerCertificate=True;"
  },
  "AuthToken": {
    "TokenKey": "12345678901234567890123456789012", //32字元up.
    "TokenIssuer": "sfaic",
    "TokenAudience": "fasterp"
  },
  "UserPasswordKey": "12345678901234567890123456789012", //32字元.
  "UserPasswordIV": "1234567890123456", //16字元.  
  "log4net": {
    "log4netConfigFileName": "log4net.config",
    "log4netWatch": true
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "MongoDB": {
    "ConnectionString": "mongodb://sa:<EMAIL>:19000/?authMechanism=SCRAM-SHA-256",
    "DatabaseName": "FAST_ERP",
    "CollectionName": "Logger"
  },
  "AllowedHosts": "*"
}
