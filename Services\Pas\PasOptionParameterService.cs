﻿using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Extensions.Pas;
using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class PasOptionParameterService : IPasOptionParameterService
    {
        private readonly ERPDbContext _context;

        private readonly EmployeeClass _employeeClass;

        public PasOptionParameterService(
            ERPDbContext context,
            EmployeeClass employeeClass)
        {
            _context = context;
            _employeeClass = employeeClass;
        }

        //取得職稱選項.
        public async Task<List<SelectOption>> GetJobtitleOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_Jobtitledata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得職稱選項錯誤", ex);
            }
        }

        //取得證號別選項.
        public async Task<List<SelectOption>> GetIdTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_idtypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得證號別選項錯誤", ex);
            }
        }

        //取得證號錯誤註記選項.
        public async Task<List<SelectOption>> GetIdErrorOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_iderrdata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得證號錯誤註記選項錯誤", ex);
            }
        }

        //取得血型選項.
        public async Task<List<SelectOption>> GetBloodTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_bloodtypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得血型選項錯誤", ex);
            }
        }

        //取得學位選項.
        public async Task<List<SelectOption>> GetDegreeTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_DegreeTypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得學位選項錯誤", ex);
            }
        }

        //取得結業選項.
        public async Task<List<SelectOption>> GetGraduateOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_graduatedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得結業選項錯誤", ex);
            }
        }

        //取得留停類型選項.
        public async Task<List<SelectOption>> GetSuspendTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_SuspendType.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得留停類型選項錯誤", ex);
            }
        }

        //取得留復職種類選項.
        public async Task<List<SelectOption>> GetSuspendKindOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_SuspendKind.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得留復職種類選項錯誤", ex);
            }
        }

        //取得員工自提類型選項.
        public async Task<List<SelectOption>> GetEmployeeContributionOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_EmployeeContributionTypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得員工自提類型選項錯誤", ex);
            }
        }

        //取得計稅型式類型選項列表.
        public async Task<List<SelectOption>> GetIncomeTaxTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_IncomeTaxdata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得計稅型式類型選項錯誤", ex);
            }
        }

        //取得發薪狀況選項列表.
        public async Task<List<SelectOption>> GetPayoffTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_payoffdata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得發薪狀況選項錯誤", ex);
            }
        }

        //取得關係類型選項列表.
        public async Task<List<SelectOption>> GetDepTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_deptypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得關係類型選項錯誤", ex);
            }
        }

        //取得薪資項目類型選項列表.
        public async Task<List<SelectOption>> GetRegularSalaryCreaseTypeOptionsAsync()
        {
            try
            {
                var options = new List<SelectOption>
                {
                    new SelectOption { OptionValue = "1", OptionText = "加項" },
                    new SelectOption { OptionValue = "2", OptionText = "減項" }
                };

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得薪資項目類型選項錯誤", ex);
            }
        }

        //取得任用資格選項列表.
        public async Task<List<SelectOption>> GetJobroleTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_JobroleTypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得任用資格選項錯誤", ex);
            }
        }

        //取得薪俸類型選項列表.
        public async Task<List<SelectOption>> GetSalaryTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_salaryTypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得薪俸類型選項錯誤", ex);
            }
        }

        //取得錄用類別選項列表.
        public async Task<List<SelectOption>> GetCategoryTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_categorydata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得錄用類別選項錯誤", ex);
            }
        }

        //取得職等選項列表.
        public async Task<List<SelectOption>> GetJobLevelOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_leveldata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得職等選項錯誤", ex);
            }
        }

        //取得級數選項列表.
        public async Task<List<SelectOption>> GetJobRankOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_rankdata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得級數選項錯誤", ex);
            }
        }

        //取得升遷類型選項列表.
        public async Task<List<SelectOption>> GetPromotionTypeOptionsAsync()
        {
            try
            {
                var options = _employeeClass.list_promotionTypedata.Select(j => new SelectOption
                {
                    OptionValue = j.code,
                    OptionText = j.name
                }).ToList();

                return options;
            }
            catch (Exception ex)
            {
                throw new Exception("取得升遷類型選項錯誤", ex);
            }
        }

    }
}
