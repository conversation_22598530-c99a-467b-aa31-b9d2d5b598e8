﻿using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IEnterpriseImageService
    {
        /// <summary>
        /// 上傳公司圖片
        /// </summary>
        Task<(bool, string)> UploadImageAsync(EnterpriseImageUploadDTO request, String tokenUid = "");

        /// <summary>
        /// 取得公司圖片列表
        /// </summary>
        /// <returns>公司圖片列表</returns>
        Task<List<EnterpriseImageDTO>> GetImageListAsync();

        /// <summary>
        /// 取得公司圖片
        /// </summary>
        Task<EnterpriseImageDTO> GetImageAsync(string ImageId);

        /// <summary>
        /// 刪除公司圖片
        /// </summary>
        Task<(bool, string)> DeleteImageAsync(EnterpriseImageDTO request, String tokenUid = "");
    }
}