﻿using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface ISalaryService
    {

        /// <summary>
        /// 取得薪資明細資料
        /// </summary>
        /// <param name="_userid">UserId</param>
        /// <returns>薪資明細資料</returns>
        Task<SalaryDTO> GetSalaryDetailAsync(string _userid);

        /// <summary>
        /// 編輯薪資資料
        /// </summary>
        /// <param name="_data">薪資資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditSalaryAsync(SalaryDTO _data);
    }
}
