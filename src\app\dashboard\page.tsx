"use client";

/* 儀錶板
  /app/dashboard/page.tsx
 */

import { useEffect, useState } from "react";
import {
  Card,
  Spin,
  Button,
  Modal,
  Row,
  Col,
  message,
  Space,
  Tooltip,
} from "antd";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { routes } from "@/config/routes";
import {
  Responsive as ResponsiveGridLayout,
  WidthProvider,
  Layout,
  Layouts,
} from "react-grid-layout";
import {
  SettingOutlined,
  PlusOutlined,
  ReloadOutlined,
  SaveOutlined,
  DragOutlined,
  DeleteOutlined,
} from "@ant-design/icons";

// 導入樣式
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import "./dashboard.css";

// 導入組件和配置
import {
  availableWidgets,
  defaultLayouts,
  gridSettings,
  breakpoints,
  cols,
  responsiveMargin,
  responsiveContainerPadding,
} from "./config";
import { DashboardLayout, ResponsiveLayouts, Breakpoint } from "./types";
import { getWidgetComponent, getWidgetConfig } from "./widgets/registry";

// Create responsive grid layout with width provider
const ResponsiveReactGridLayout = WidthProvider(ResponsiveGridLayout);

// Utility functions for intelligent layout adaptation with relative positioning
const scaleLayoutForBreakpoint = (
  layout: Layout[],
  fromCols: number,
  toCols: number,
  targetBreakpoint: Breakpoint
): DashboardLayout[] => {
  if (!layout || layout.length === 0) return [];

  return layout.map((item) => {
    // Calculate relative position (0-1) to maintain spatial relationships
    const relativeX = (item.x || 0) / fromCols;
    const relativeW = (item.w || 1) / fromCols;

    // Scale width proportionally, but ensure minimum constraints
    let newW = Math.max(1, Math.round(relativeW * toCols));

    // Apply minimum constraints based on breakpoint
    const minW =
      targetBreakpoint === "xxs"
        ? 2
        : targetBreakpoint === "xs"
        ? 2
        : targetBreakpoint === "sm"
        ? 2
        : 2;

    newW = Math.max(minW, newW);

    // Ensure width doesn't exceed available columns
    newW = Math.min(newW, toCols);

    // Calculate new X position maintaining relative positioning
    let newX = Math.round(relativeX * toCols);

    // Adjust X position if widget would overflow
    if (newX + newW > toCols) {
      newX = Math.max(0, toCols - newW);
    }

    // For widgets that were positioned at the right edge, maintain right alignment
    const wasAtRightEdge = (item.x || 0) + (item.w || 1) === fromCols;
    if (wasAtRightEdge) {
      newX = toCols - newW;
    }

    // For widgets that were centered, try to maintain center alignment
    const wasCentered =
      Math.abs((item.x || 0) + (item.w || 1) / 2 - fromCols / 2) < 1;
    if (wasCentered && !wasAtRightEdge) {
      const centerX = Math.round((toCols - newW) / 2);
      newX = Math.max(0, Math.min(centerX, toCols - newW));
    }

    return {
      i: item.i,
      x: newX,
      w: newW,
      y: item.y, // Maintain Y position for vertical relationships
      h: item.h, // Maintain height
      minW: Math.max(item.minW || 2, minW),
      minH: item.minH || 3,
    } as DashboardLayout;
  });
};

const getLayoutForBreakpoint = (
  layouts: ResponsiveLayouts,
  targetBreakpoint: Breakpoint,
  currentBreakpoint: Breakpoint
): DashboardLayout[] => {
  // If we have a layout for the target breakpoint, use it
  if (layouts[targetBreakpoint] && layouts[targetBreakpoint].length > 0) {
    return layouts[targetBreakpoint];
  }

  // Otherwise, try to adapt from the current breakpoint
  const currentLayout = layouts[currentBreakpoint];
  if (!currentLayout || currentLayout.length === 0) {
    // Fall back to default layout for the target breakpoint
    return defaultLayouts[targetBreakpoint] || [];
  }

  // Scale the current layout to the target breakpoint
  const fromCols = cols[currentBreakpoint];
  const toCols = cols[targetBreakpoint];

  return scaleLayoutForBreakpoint(
    currentLayout,
    fromCols,
    toCols,
    targetBreakpoint
  );
};

// Widget components are now managed by the registry system

// 儀錶板頁面
export default function DashboardPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  // 狀態管理
  const [layouts, setLayouts] = useState<ResponsiveLayouts>(defaultLayouts);
  const [activeWidgets, setActiveWidgets] = useState<string[]>([]);
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>("lg");
  const [containerWidth, setContainerWidth] = useState<number>(1200);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSettingsVisible, setIsSettingsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [lastSavedTime, setLastSavedTime] = useState<string | null>(null);
  const [dragMode, setDragMode] = useState<"free" | "smart">("free"); // 拖拽模式：自由拖拽 vs 智能推送

  // 認證檢查
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace(routes.login);
    }
  }, [isLoading, isAuthenticated, router]);

  // 載入儲存的配置
  useEffect(() => {
    const loadConfiguration = () => {
      try {
        const savedLayouts = localStorage.getItem(
          "dashboard-responsive-layouts"
        );
        const savedWidgets = localStorage.getItem("dashboard-widgets");
        const savedTime = localStorage.getItem("dashboard-last-saved");
        const savedDragMode = localStorage.getItem("dashboard-drag-mode") as
          | "free"
          | "smart"
          | null;

        if (savedLayouts && savedWidgets) {
          // 解析儲存的資料
          const parsedLayouts = JSON.parse(savedLayouts);
          const parsedWidgets = JSON.parse(savedWidgets);

          // 驗證儲存的資料是否有效
          if (
            parsedLayouts &&
            typeof parsedLayouts === "object" &&
            Array.isArray(parsedWidgets)
          ) {
            // 確保所有必要的斷點都存在
            const validLayouts = {
              lg: parsedLayouts.lg || defaultLayouts.lg,
              md: parsedLayouts.md || defaultLayouts.md,
              sm: parsedLayouts.sm || defaultLayouts.sm,
              xs: parsedLayouts.xs || defaultLayouts.xs,
              xxs: parsedLayouts.xxs || defaultLayouts.xxs,
            };
            setLayouts(validLayouts);
            setActiveWidgets(parsedWidgets);
            setLastSavedTime(savedTime);

            // 載入拖拽模式設定
            if (
              savedDragMode &&
              (savedDragMode === "free" || savedDragMode === "smart")
            ) {
              setDragMode(savedDragMode);
            }
          } else {
            throw new Error("儲存的配置格式無效");
          }
        } else {
          // 使用預設配置
          setLayouts(defaultLayouts);
          setActiveWidgets(
            defaultLayouts.lg.map((item: DashboardLayout) => item.i)
          );
        }
      } catch (error) {
        // 載入失敗時使用預設配置
        setLayouts(defaultLayouts);
        setActiveWidgets(
          defaultLayouts.lg.map((item: DashboardLayout) => item.i)
        );
        message.warning("載入儲存配置失敗，已使用預設配置");
      } finally {
        setIsLoaded(true);
      }
    };

    // 確保在認證完成後才載入配置
    if (!isLoading && isAuthenticated) {
      loadConfiguration();
    }
  }, [isLoading, isAuthenticated]);

  // 儲存配置
  const saveConfiguration = async () => {
    try {
      // 驗證要儲存的資料
      if (
        !layouts ||
        typeof layouts !== "object" ||
        !Array.isArray(activeWidgets)
      ) {
        throw new Error("配置資料格式無效");
      }

      const currentTime = new Date().toLocaleString("zh-TW");

      localStorage.setItem(
        "dashboard-responsive-layouts",
        JSON.stringify(layouts)
      );
      localStorage.setItem("dashboard-widgets", JSON.stringify(activeWidgets));
      localStorage.setItem("dashboard-last-saved", currentTime);
      localStorage.setItem("dashboard-drag-mode", dragMode);

      setLastSavedTime(currentTime);

      message.success("儀表板配置已儲存");
    } catch (error) {
      message.error("儲存配置失敗");
    }
  };

  // 佈局變更處理 - 只更新當前斷點的佈局，保留其他斷點的用戶自定義
  const handleLayoutChange = (
    _currentLayout: Layout[],
    allLayouts: Layouts
  ) => {
    if (!isLoaded) return; // 確保已載入完成才處理變更

    // 只更新當前斷點的佈局，保留其他斷點的用戶自定義
    const updatedLayouts: ResponsiveLayouts = { ...layouts };

    // 更新當前斷點的佈局
    if (allLayouts[currentBreakpoint]) {
      const newLayout = allLayouts[currentBreakpoint].map((item: Layout) => ({
        i: item.i,
        x: item.x,
        y: item.y,
        w: item.w,
        h: item.h,
        minW: item.minW || 2,
        minH: item.minH || 3,
      })) as DashboardLayout[];

      // 檢查並修復超出邊界的組件
      const maxCols = cols[currentBreakpoint];
      let hasOutOfBounds = false;
      const validatedLayout = newLayout.map((item: Layout) => {
        const x = item.x || 0;
        const y = item.y || 0;
        const w = item.w || 1;
        const h = item.h || 1;
        const rightEdge = x + w;

        if (rightEdge > maxCols || x < 0 || y < 0) {
          hasOutOfBounds = true;
          const fixedX = Math.max(0, Math.min(x, maxCols - w));
          const fixedY = Math.max(0, y);

          console.warn(`Auto-fixing out-of-bounds widget ${item.i}:`, {
            original: { x, y, w, h },
            fixed: { x: fixedX, y: fixedY, w, h },
            maxCols,
          });

          return { ...item, x: fixedX, y: fixedY };
        }

        return item;
      }) as DashboardLayout[];

      // 如果有修復，使用修復後的佈局
      if (hasOutOfBounds) {
        updatedLayouts[currentBreakpoint] = validatedLayout;
        message.info("已自動修復超出邊界的組件位置");
      } else {
        updatedLayouts[currentBreakpoint] = newLayout;
      }
    }

    setLayouts(updatedLayouts);

    // 自動儲存到 localStorage
    localStorage.setItem(
      "dashboard-responsive-layouts",
      JSON.stringify(updatedLayouts)
    );
    localStorage.setItem("dashboard-last-saved", new Date().toISOString());
    setLastSavedTime(new Date().toLocaleString("zh-TW"));
  };

  // 斷點變更處理 - 智能適應佈局
  const handleBreakpointChange = (newBreakpoint: string, _newCols: number) => {
    const targetBreakpoint = newBreakpoint as Breakpoint;
    const previousBreakpoint = currentBreakpoint;

    setCurrentBreakpoint(targetBreakpoint);

    // 如果目標斷點沒有佈局，嘗試從當前斷點適應
    if (!layouts[targetBreakpoint] || layouts[targetBreakpoint].length === 0) {
      const adaptedLayout = getLayoutForBreakpoint(
        layouts,
        targetBreakpoint,
        previousBreakpoint
      );

      if (adaptedLayout.length > 0) {
        const updatedLayouts = { ...layouts };
        updatedLayouts[targetBreakpoint] = adaptedLayout;
        setLayouts(updatedLayouts);
      }
    }
  };

  // 容器寬度變更處理
  const handleWidthChange = (
    containerWidth: number,
    _margin: [number, number],
    _cols: number,
    _containerPadding: [number, number]
  ) => {
    setContainerWidth(containerWidth);
  };

  // 新增組件
  const addWidget = (widgetId: string) => {
    if (activeWidgets.includes(widgetId)) {
      message.warning("該組件已存在");
      return;
    }

    const widget = availableWidgets.find((w) => w.id === widgetId);
    if (!widget) return;

    // 為每個斷點創建新的佈局項目
    const newLayouts: ResponsiveLayouts = {
      lg: [...layouts.lg],
      md: [...layouts.md],
      sm: [...layouts.sm],
      xs: [...layouts.xs],
      xxs: [...layouts.xxs],
    };

    // 為每個斷點計算新組件的位置
    Object.keys(newLayouts).forEach((breakpoint) => {
      const bp = breakpoint as Breakpoint;
      const currentLayout = newLayouts[bp];

      // 計算新組件的位置
      const maxY =
        currentLayout.length > 0
          ? Math.max(
              ...currentLayout.map(
                (item: Layout) => (item.y || 0) + (item.h || 3)
              )
            )
          : 0;

      // 根據斷點調整組件大小
      let widgetWidth = widget.defaultSize.w;
      let widgetHeight = widget.defaultSize.h;

      if (bp === "xxs") {
        widgetWidth = Math.min(widgetWidth, 2);
      } else if (bp === "xs") {
        widgetWidth = Math.min(widgetWidth, 4);
      } else if (bp === "sm") {
        widgetWidth = Math.min(widgetWidth, 6);
      }

      const newLayoutItem: DashboardLayout = {
        i: widgetId,
        x: 0,
        y: maxY,
        w: widgetWidth,
        h: widgetHeight,
        minW: widget.minSize.w,
        minH: widget.minSize.h,
      } as DashboardLayout;

      newLayouts[bp].push(newLayoutItem);
    });

    const newActiveWidgets = [...activeWidgets, widgetId];

    setLayouts(newLayouts);
    setActiveWidgets(newActiveWidgets);

    message.success(`已新增 ${widget.title}`);
  };

  // 移除組件 - 帶確認對話框
  const removeWidget = (widgetId: string) => {
    const widget = availableWidgets.find((w) => w.id === widgetId);
    const widgetTitle = widget?.title || "組件";

    Modal.confirm({
      title: "移除組件",
      content: `確定要移除 ${widgetTitle} 嗎？`,
      okText: "確定",
      cancelText: "取消",
      okType: "danger",
      onOk: () => {
        const newLayouts: ResponsiveLayouts = {
          lg: layouts.lg.filter((item) => item.i !== widgetId),
          md: layouts.md.filter((item) => item.i !== widgetId),
          sm: layouts.sm.filter((item) => item.i !== widgetId),
          xs: layouts.xs.filter((item) => item.i !== widgetId),
          xxs: layouts.xxs.filter((item) => item.i !== widgetId),
        };
        const newActiveWidgets = activeWidgets.filter((id) => id !== widgetId);

        setLayouts(newLayouts);
        setActiveWidgets(newActiveWidgets);

        message.success(`已移除 ${widgetTitle}`);
      },
    });
  };

  // 重置為預設佈局
  const resetLayout = () => {
    Modal.confirm({
      title: "重置儀表板",
      content: "確定要重置為預設佈局嗎？這將清除所有自訂設定。",
      okText: "確定",
      cancelText: "取消",
      onOk: () => {
        setLayouts(defaultLayouts);
        setActiveWidgets(
          defaultLayouts.lg.map((item: DashboardLayout) => item.i)
        );
        setLastSavedTime(null);

        // 清除儲存的資料
        localStorage.removeItem("dashboard-responsive-layouts");
        localStorage.removeItem("dashboard-widgets");
        localStorage.removeItem("dashboard-last-saved");

        message.success("已重置為預設佈局");
      },
    });
  };

  // 切換拖拽模式
  const toggleDragMode = () => {
    const newMode = dragMode === "free" ? "smart" : "free";
    setDragMode(newMode);
    localStorage.setItem("dashboard-drag-mode", newMode);

    const modeText = newMode === "free" ? "自由拖拽" : "智能推送";
    message.info(`已切換至${modeText}模式`);
  };

  // 完成編輯模式時自動儲存
  const toggleEditMode = () => {
    if (isEditMode) {
      // 退出編輯模式時自動儲存
      saveConfiguration();
    }
    setIsEditMode(!isEditMode);
  };

  // 渲染組件 - 使用新的 widget registry 系統
  const renderWidget = (widgetId: string) => {
    const widget = getWidgetConfig(widgetId);
    if (!widget) {
      console.warn(`Widget config not found for ID: ${widgetId}`);
      return null;
    }

    const WidgetComponent = getWidgetComponent(widget.component);
    if (!WidgetComponent) {
      console.warn(`Widget component not found: ${widget.component}`);
      return null;
    }

    return (
      <Card
        key={widgetId}
        className="dashboard-widget-card"
        title={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <span>{widget.title}</span>
            {isEditMode && (
              <div>
                <Tooltip title="拖拽移動">
                  <DragOutlined
                    style={{
                      cursor: "move",
                      marginRight: "8px",
                      color: "#1890ff",
                    }}
                  />
                </Tooltip>
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    removeWidget(widgetId);
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  style={{
                    minWidth: "44px",
                    minHeight: "44px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  移除
                </Button>
              </div>
            )}
          </div>
        }
        style={{
          border: isEditMode ? "2px dashed #1890ff" : undefined,
        }}
      >
        <WidgetComponent />
      </Card>
    );
  };

  // 加載狀態
  if (isLoading || !isAuthenticated || !isLoaded) {
    return (
      <div className="dashboard-loading">
        <Spin size="large">
          <div style={{ padding: "20px", textAlign: "center" }}>
            {isLoading
              ? "正在載入..."
              : !isAuthenticated
              ? "檢查認證..."
              : "載入儀表板配置..."}
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* 頂部工具列 */}
      <div
        className="dashboard-toolbar"
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexWrap: "wrap",
          gap: "16px",
        }}
      >
        <div style={{ flex: 1, minWidth: "300px" }}>
          <h1>儀表板</h1>
          <p>
            歡迎回來，{user?.name || "使用者"}！您可以自由調整組件的位置和大小。
          </p>
          {lastSavedTime && (
            <p style={{ fontSize: "12px", color: "#999", margin: "4px 0 0 0" }}>
              最後儲存：{lastSavedTime}
            </p>
          )}
          {/* {process.env.NODE_ENV === 'development' && (
            <p style={{ fontSize: "11px", color: "#666", margin: "2px 0 0 0" }}>
              當前斷點：{currentBreakpoint} ({cols[currentBreakpoint]}列) | 活躍組件：{activeWidgets.length} | 編輯模式：{isEditMode ? '開啟' : '關閉'} | 容器寬度：{containerWidth}px
            </p>
          )} */}
        </div>

        <Space wrap>
          <Button
            icon={<PlusOutlined />}
            onClick={() => setIsSettingsVisible(true)}
            size="middle"
          >
            新增組件
          </Button>
          <Button icon={<ReloadOutlined />} onClick={resetLayout} size="middle">
            重置佈局
          </Button>
          {isEditMode && (
            <Button
              icon={
                dragMode === "free" ? <DragOutlined /> : <SettingOutlined />
              }
              onClick={toggleDragMode}
              size="middle"
              type="primary"
              className={`drag-mode-toggle ${
                dragMode === "smart" ? "smart-mode" : "free-mode"
              }`}
            >
              {dragMode === "free" ? "自由拖拽" : "智能推送"}
            </Button>
          )}
          <Button
            type={isEditMode ? "primary" : "default"}
            icon={<SettingOutlined />}
            onClick={toggleEditMode}
            size="middle"
          >
            {isEditMode ? "完成編輯" : "編輯模式"}
          </Button>
        </Space>
      </div>

      {/* 編輯模式提示 */}
      {isEditMode && (
        <div className="edit-mode-banner">
          <strong>編輯模式已啟用：</strong>{" "}
          您可以拖拽組件標題來移動位置，拖拽右下角來調整大小，點擊「移除」按鈕來刪除組件。
          <br />
          <strong>當前拖拽模式：</strong>
          <span
            style={{
              color: dragMode === "smart" ? "#52c41a" : "#1890ff",
              fontWeight: "bold",
              marginLeft: "8px",
            }}
          >
            {dragMode === "free"
              ? "自由拖拽 - 組件可自由排列"
              : "智能推送 - 自動避免重疊"}
          </span>
        </div>
      )}

      {/* 網格佈局 */}
      {layouts && activeWidgets.length > 0 ? (
        <div
          className={`dashboard-grid-container ${
            isEditMode ? "edit-mode" : ""
          }`}
          style={{
            background: "white",
            padding: "20px",
            borderRadius: "8px",
            overflow: isEditMode ? "visible" : "hidden", // 編輯模式時允許拖拽超出邊界，否則隱藏
            position: "relative", // 確保定位上下文正確
            width: "100%",
            minWidth: "100%",
            minHeight: "400px", // 確保最小高度
            boxSizing: "border-box",
          }}
        >
          <ResponsiveReactGridLayout
            className="layout"
            layouts={layouts}
            onLayoutChange={handleLayoutChange}
            onBreakpointChange={handleBreakpointChange}
            onWidthChange={handleWidthChange}
            breakpoints={breakpoints}
            cols={cols}
            rowHeight={gridSettings.rowHeight}
            margin={responsiveMargin}
            containerPadding={responsiveContainerPadding}
            isDraggable={isEditMode}
            isResizable={isEditMode}
            draggableHandle=".ant-card-head"
            onDragStart={() => {}}
            onDragStop={() => {}}
            compactType={dragMode === "smart" ? "horizontal" : null}
            preventCollision={dragMode === "smart"} // 智能推送模式防止碰撞
            useCSSTransforms={false} // 改為false以使用left/top定位，避免transform問題
            measureBeforeMount={false} // 改為false以避免測量問題
            isBounded={false} // 移除邊界限制以允許完整的網格使用
            autoSize={true} // 自動調整容器高度
            transformScale={1} // 確保縮放比例正確
            style={{
              minHeight: "400px",
              width: "100%", // 確保佔滿容器寬度
              position: "relative", // 確保定位上下文
              overflow: "visible", // 允許拖拽時的移動
            }}
          >
            {activeWidgets.map((widgetId) => (
              <div key={widgetId}>{renderWidget(widgetId)}</div>
            ))}
          </ResponsiveReactGridLayout>
        </div>
      ) : (
        <div
          style={{
            textAlign: "center",
            padding: "60px 20px",
            background: "white",
            borderRadius: "12px",
            border: "1px solid #f0f0f0",
          }}
        >
          <div style={{ fontSize: "48px", marginBottom: "16px" }}>📊</div>
          <h3 style={{ color: "#666", marginBottom: "8px" }}>暫無組件</h3>
          <p style={{ color: "#999", marginBottom: "24px" }}>
            點擊「新增組件」開始自訂您的儀表板
          </p>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsSettingsVisible(true)}
          >
            新增第一個組件
          </Button>
        </div>
      )}

      {/* 新增組件對話框 */}
      <Modal
        title={
          <div style={{ fontSize: "18px", fontWeight: 600 }}>新增組件</div>
        }
        open={isSettingsVisible}
        onCancel={() => setIsSettingsVisible(false)}
        footer={null}
        width={700}
        className="add-widget-modal"
      >
        <div style={{ marginBottom: "16px", color: "#666" }}>
          選擇您想要新增到儀表板的組件：
        </div>
        <Row gutter={[20, 20]}>
          {availableWidgets.map((widget) => (
            <Col span={12} key={widget.id}>
              <Card
                size="small"
                hoverable={!activeWidgets.includes(widget.id)}
                className={`widget-card ${
                  activeWidgets.includes(widget.id) ? "disabled" : ""
                }`}
                actions={[
                  <Button
                    key="add"
                    type="primary"
                    size="small"
                    disabled={activeWidgets.includes(widget.id)}
                    onClick={() => {
                      addWidget(widget.id);
                      setIsSettingsVisible(false);
                    }}
                    style={{ width: "80px" }}
                  >
                    {activeWidgets.includes(widget.id) ? "已新增" : "新增"}
                  </Button>,
                ]}
              >
                <Card.Meta
                  title={
                    <div style={{ fontSize: "15px", fontWeight: 600 }}>
                      {widget.title}
                    </div>
                  }
                  description={
                    <div style={{ fontSize: "13px", color: "#666" }}>
                      {widget.description}
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>
    </div>
  );
}
