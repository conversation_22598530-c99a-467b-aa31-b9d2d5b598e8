﻿using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IEmployeeService
    {
        /// <summary>
        /// 取得所有員工UID列表
        /// </summary>
        /// <returns>所有員工UID列表</returns>
        Task<List<EmployeeDTO>> GetEmployeeListAsync(FilterData _data);

        /// <summary>
        /// 取得員工明細資料
        /// </summary>
        /// <param name="_uid">userID</param>
        /// <returns>員工明細資料</returns>
        Task<EmployeeDTO> GetEmployeeDetailAsync(string _uid);

        /// <summary>
        /// 新增員工資料
        /// </summary>
        /// <param name="_data">員工資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddEmployeeAsync(EmployeeDTO _data);

        /// <summary>
        /// 編輯員工資料
        /// </summary>
        /// <param name="_data">員工資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditEmployeeAsync(EmployeeDTO _data);

        /// <summary>
        /// 補全員工資料
        /// </summary>
        /// <param name="_data">員工資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> CompleteEmployeeAsync(EmployeeDTO _data);

        /// <summary>
        /// 刪除員工資料
        /// </summary>
        /// <param name="_data">員工資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteEmployeeAsync(string _uid);
    }
}
