using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AssetSourceService : IAssetSourceService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly IMapper _mapper;

        public AssetSourceService(Baseform baseform, ERPDbContext context, IMapper mapper)
        {
            _baseform = baseform;
            _context = context;
            _mapper = mapper;
        }

        // 新增財產來源
        public async Task<(bool, string)> AddAsync(AssetSourceDTO assetSource)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = _mapper.Map<AssetSource>(assetSource);
                entity.AssetSourceId = Guid.NewGuid();
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = assetSource.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        // 刪除財產來源
        public async Task<(bool, string)> DeleteAsync(AssetSourceDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetSource>()
                    .FirstOrDefaultAsync(a => a.AssetSourceId == _data.AssetSourceId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        // 取得所有財產來源
        public async Task<List<AssetSourceDTO>> GetAllAsync()
        {
            try
            {
                var entities = await _context.Set<AssetSource>()
                    .Where(a => !a.IsDeleted)
                    .OrderBy(a => a.SortCode)
                    .ToListAsync();

                var result = _mapper.Map<List<AssetSourceDTO>>(entities);

                // 獲取使用者名稱
                foreach (var item in result)
                {
                    if (!string.IsNullOrEmpty(item.CreateUserId))
                    {
                        var createUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.CreateUserId);
                        item.CreateUserName = createUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.UpdateUserId))
                    {
                        var updateUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.UpdateUserId);
                        item.UpdateUserName = updateUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.DeleteUserId))
                    {
                        var deleteUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.DeleteUserId);
                        item.DeleteUserName = deleteUser?.Name ?? "";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得所有財產來源時發生錯誤: {ex.Message}");
                return new List<AssetSourceDTO>();
            }
        }

        // 取得財產來源ById
        public async Task<string> GetByIdAsync(int id)
        {
            try
            {
                var entity = await _context.Set<AssetSource>()
                    .FirstOrDefaultAsync(a => a.AssetSourceId == Guid.Parse(id.ToString()) && !a.IsDeleted);

                if (entity == null)
                {
                    return "找不到資料";
                }

                var dto = _mapper.Map<AssetSourceDTO>(entity);
                return JsonConvert.SerializeObject(dto);
            }
            catch (Exception ex)
            {
                return $"取得資料失敗: {ex.Message}";
            }
        }

        // 更新財產來源
        public async Task<(bool, string)> UpdateAsync(AssetSourceDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetSource>()
                    .FirstOrDefaultAsync(a => a.AssetSourceId == _data.AssetSourceId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }
    }
}