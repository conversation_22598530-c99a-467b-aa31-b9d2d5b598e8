"use client";

import { Card, Typography, Avatar, Spin } from "antd";
import { UserOutlined, CalendarOutlined } from "@ant-design/icons";
import { useAuth } from "@/contexts/AuthContext";
import { useState, useEffect } from "react";

const { Title, Text } = Typography;

export function WelcomeWidget() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);

  const currentDate = new Date().toLocaleDateString("zh-TW", {
    year: "numeric",
    month: "long",
    day: "numeric",
    weekday: "long",
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large">
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
            }}
          >
            載入中...
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: "100%" }}>
      <div
        style={{ display: "flex", alignItems: "center", marginBottom: "16px" }}
      >
        <Avatar
          size={48}
          icon={<UserOutlined />}
          style={{ marginRight: "12px" }}
        />
        <div>
          <Title level={4} style={{ margin: 0, color: "#1890ff" }}>
            歡迎回來，{user?.name || "使用者"}！
          </Title>
          <Text type="secondary">
            <CalendarOutlined style={{ marginRight: "8px" }} />
            {currentDate}
          </Text>
        </div>
      </div>
      <Text>今天是美好的一天，讓我們開始工作吧！</Text>
    </div>
  );
}

export default WelcomeWidget;
