﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 組別基本資料表
    /// </summary>
    public class Division : ModelBaseEntity
    {
        [Key]
        [Comment("組別編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string DivisionId { get; set; } // 組別編號

        [Comment("組別名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } // 組別名稱

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        [Comment("部門編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string DepartmentId { get; set; } //部門編號

        public Division()
        {
            DivisionId = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            DepartmentId = "";
            IsDeleted = false;
        }
    }

    public class DivisionDTO : ModelBaseEntityDTO
    {
        public string DivisionId { get; set; } // 組別編號
        public string Name { get; set; } // 組別名稱
        public int SortCode { get; set; } // 排序號碼
        public string DepartmentId { get; set; } //部門編號

        public DivisionDTO()
        {
            DivisionId = "";
            DepartmentId = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}