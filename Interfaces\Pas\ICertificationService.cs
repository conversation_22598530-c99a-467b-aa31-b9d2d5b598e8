using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface ICertificationService
    {
        /// <summary>
        /// 取得所有檢覈認證資料列表
        /// </summary>
        /// <param name="_userid">使用者編號</param>
        /// <returns>檢覈認證資料列表</returns>
        Task<List<CertificationDTO>> GetCertificationListAsync(string _userid);

        /// <summary>
        /// 取得檢覈認證資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>檢覈認證資料明細</returns>
        Task<CertificationDTO> GetCertificationDetailAsync(string _uid);

        /// <summary>
        /// 新增檢覈認證資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddCertificationAsync(CertificationDTO _data);

        /// <summary>
        /// 編輯檢覈認證資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditCertificationAsync(CertificationDTO _data);

        /// <summary>
        /// 刪除檢覈認證資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteCertificationAsync(string _uid);
    }
}
