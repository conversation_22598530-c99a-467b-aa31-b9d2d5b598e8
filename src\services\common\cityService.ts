import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 城市
export interface City {
    cityId: string;
    name: string;
    englishName: string;
    description: string;
    sortCode: number;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
}

// 獲取城市列表
export const getCities = async (): Promise<ApiResponse<City[]>> => {
    try {
        const response = await httpClient(apiEndpoints.getCities, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取城市列表失敗",
            data: []
        };
    }
};
