﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    public class SystemParametersDTO
    {
        public string SystemParametersId { get; set; } //參數編號
        public string SystemGroupId { get; set; } //系統群組編號
        public string ParameterCode { get; set; } //參數代號
        public string Description { get; set; } //參數說明

        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態


        public SystemParametersDTO()
        {
            SystemParametersId = "";
            SystemGroupId = "";
            ParameterCode = "";
            Description = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
        }
    }

    public class SystemParameters : ModelBaseEntity
    {
        [Key]
        [Comment("參數編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemParametersId { get; set; } //選單編號

        [Comment("系統群組編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemGroupId { get; set; } //系統群組編號

        [Comment("參數代號")]
        [Column(TypeName = "nvarchar(50)")]
        public string ParameterCode { get; set; } //參數代號

        [Comment("參數說明")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Description { get; set; } //參數說明

        public SystemParameters()
        {
            SystemParametersId = "";
            SystemGroupId = "";
            ParameterCode = "";
            Description = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}
