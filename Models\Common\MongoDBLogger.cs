using System;
using System.Collections.Generic;
using FAST_ERP_Backend.Interfaces.Common;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// MongoDB日誌記錄模型
    /// </summary>
    public class MongoDBLogEntry : ILogEntry
    {
        #region Core Properties
        /// <summary> 日誌ID </summary>
        [BsonId]
        [BsonRepresentation(BsonType.String)]
        public Guid Id { get; set; }

        /// <summary> 日誌級別 </summary>
        [BsonElement("LogLevel")]
        public string Level { get; set; }

        /// <summary> 交易ID </summary>
        [BsonElement("TransactionId")]
        public string TransactionId { get; set; }

        /// <summary> 記錄時間 </summary>
        [BsonElement("CreateTime")]
        public DateTime CreateTime { get; set; }
        #endregion

        #region Content Properties
        /// <summary> 日誌消息 </summary>
        [BsonElement("Message")]
        public string? Message { get; set; }

        /// <summary> 異動資料 </summary>
        [BsonElement("Data")]
        public Dictionary<string, object>? Data { get; set; }

        /// <summary> 來源 </summary>
        [BsonElement("Source")]
        public string? Source { get; set; }
        #endregion

        #region Exception Properties
        /// <summary> 例外詳情 </summary>
        [BsonElement("Exception")]
        public string? Exception { get; set; }

        /// <summary> 堆疊跟踪 </summary>
        [BsonElement("StackTrace")]
        public string? StackTrace { get; set; }
        #endregion

        #region Context Properties
        /// <summary> 用戶ID </summary>
        [BsonElement("UserId")]
        public string? UserId { get; set; }

        /// <summary> IP地址 </summary>
        [BsonElement("IpAddress")]
        public string? IpAddress { get; set; }

        /// <summary> 瀏覽器信息 </summary>
        [BsonElement("UserAgent")]
        public string? UserAgent { get; set; }

        /// <summary> 請求URL </summary>
        [BsonElement("RequestUrl")]
        public string? RequestUrl { get; set; }
        #endregion

        #region Constructor
        /// <summary>
        /// 日誌構造初始化
        /// </summary>
        public MongoDBLogEntry()
        {
            Id = Guid.NewGuid();
            Level = LogLevelEnum.Information.ToString();
            TransactionId = Guid.NewGuid().ToString();
            CreateTime = DateTime.UtcNow.AddHours(8); // 調整為台灣時區

            // 初始化可選屬性為 null，避免不必要的空字符串
            Message = null;
            Data = null;
            Source = null;
            Exception = null;
            StackTrace = null;
            UserId = null;
            IpAddress = null;
            UserAgent = null;
            RequestUrl = null;
        }
        #endregion
    }

    /// <summary>
    /// 日誌級別枚舉
    /// </summary>
    /// <remarks>
    /// 日誌級別按嚴重程度排序：Debug = 0, Information = 1, Warning = 2, Error = 3, Critical = 4
    /// </remarks>
    public enum LogLevelEnum
    {
        /// <summary> 調試級別 - 詳細的調試信息 </summary>
        Debug = 0,

        /// <summary> 信息級別 - 一般信息記錄 </summary>
        Information = 1,

        /// <summary> 警告級別 - 潛在問題警告 </summary>
        Warning = 2,

        /// <summary> 錯誤級別 - 錯誤但不影響系統運行 </summary>
        Error = 3,

        /// <summary> 嚴重級別 - 嚴重錯誤可能導致系統崩潰 </summary>
        Critical = 4
    }
}
