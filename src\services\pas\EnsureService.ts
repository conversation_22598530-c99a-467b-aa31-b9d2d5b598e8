// Ensure 保證資料管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 保證資料型別
export interface Ensure {
    uid: string;
    userId: string;
    ensureNumber: string;
    guarantorName: string;
    guarantorPersonalId: string;
    guarantorBirthday: string; // timestamp 格式字串
    guarantorAddress: string;
    guarantorPhone: string;
    relationship: string;
    guarantorProperty: string;
    propertyValue: string;
    remark: string;
}

// 建立空的保證資料
export const createEmptyEnsure = (): Ensure => ({
    uid: '',
    userId: '',
    ensureNumber: '',
    guarantorName: '',
    guarantorPersonalId: '',
    guarantorBirthday: '',
    guarantorAddress: '',
    guarantorPhone: '',
    relationship: '',
    guarantorProperty: '',
    propertyValue: '',
    remark: '',
});

// 取得保證資料列表
export async function getEnsureList(userid: string): Promise<ApiResponse<Ensure[]>> {
    return await httpClient(`${apiEndpoints.getEnsureList}/${userid}`, {
        method: "GET",
    });
}

// 取得保證資料明細
export async function getEnsureDetail(uid: string): Promise<ApiResponse<Ensure>> {
    return await httpClient(`${apiEndpoints.getEnsureDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增保證資料
export async function addEnsure(data: Partial<Ensure>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addEnsure, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增保證資料失敗",
        };
    }
}

// 編輯保證資料
export async function editEnsure(data: Partial<Ensure>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editEnsure, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯保證資料失敗",
        };
    }
}

// 刪除保證資料
export async function deleteEnsure(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteEnsure, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除保證資料失敗",
        };
    }
}
