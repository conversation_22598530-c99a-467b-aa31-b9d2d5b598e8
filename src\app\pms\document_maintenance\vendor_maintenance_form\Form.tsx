"use client";

import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Button,
  Card,
  Divider,
  message,
  Space,
  Descriptions,
  Tag,
  Typography,
  FormInstance,
  Modal,
} from "antd";
import { SaveOutlined, ReloadOutlined } from "@ant-design/icons";
import { FormProps, formInitialValues, VendorMaintenance } from "./interface";
import {
  MAINTENANCE_TYPES,
  URGENCY_LEVELS,
  STATUS_OPTIONS,
  getMaintenanceTypeLabel,
  getUrgencyLevelLabel,
  getUrgencyColor,
  getStatusLabel,
  getStatusColor,
  generateMaintenanceNumber,
} from "./config";
import {
  createVendorMaintenance,
  updateVendorMaintenance,
} from "@/services/pms/vendorMaintenanceService";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import dayjs from "dayjs";
import { getAssets, AssetDetail } from "@/services/pms/assetService";
import { getUsers, User } from "@/services/common/userService";
import {
  getDepartments,
  Department,
} from "@/services/common/departmentService";
import { formatTWCurrency } from "@/utils/formatUtils";

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

// 表單按鈕組件
const FormButtons: React.FC<{
  onCancel: () => void;
  isViewMode: boolean;
  isMobile?: boolean;
  form: FormInstance;
}> = ({ onCancel, isViewMode, isMobile, form }) => {
  return (
    <div
      style={{
        position: "sticky",
        bottom: 0,
        left: 0,
        width: "100%",
        background: "#fff",
        zIndex: 10,
        padding: isMobile ? "12px 0 0 0" : "20px 0 0 0",
        textAlign: "right",
        boxShadow: "0 -2px 8px #f0f1f2",
        marginTop: "20px",
      }}
    >
      <Space>
        <Button onClick={onCancel}>取消</Button>
        <Button
          type="primary"
          onClick={() => form.submit()}
          disabled={isViewMode}
        >
          {form.getFieldValue("maintenanceNumber") ? "儲存" : "新增"}
        </Button>
      </Space>
    </div>
  );
};

const VendorMaintenanceForm: React.FC<FormProps> = ({
  editingMaintenance,
  isViewMode,
  onCancel,
  onSuccess,
  isMobile = false,
  form,
}) => {
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [assets, setAssets] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [submitData, setSubmitData] = useState<any>(null);

  // 如果是查看模式且有資料，直接顯示純文字組件
  if (isViewMode && editingMaintenance) {
    return (
      <ViewOnlyDisplay maintenance={editingMaintenance} isMobile={isMobile} />
    );
  }

  // 載入下拉選單數據
  const loadSelectData = async () => {
    setLoadingData(true);
    try {
      const [assetsResponse, employeesResponse, departmentsResponse] =
        await Promise.all([getAssets(), getUsers(), getDepartments()]);

      if (assetsResponse.success) {
        setAssets(assetsResponse.data || []);
      }

      if (employeesResponse.success) {
        const validEmployees = (employeesResponse.data || []).filter(
          (employee: any) => employee?.userId
        );
        setEmployees(validEmployees);
      }

      if (departmentsResponse.success) {
        setDepartments(departmentsResponse.data || []);
      }
    } catch (error) {
      console.error("載入下拉選單數據失敗:", error);
      message.error("載入選單數據失敗");
    } finally {
      setLoadingData(false);
    }
  };

  // 組件載入時載入下拉選單數據
  useEffect(() => {
    loadSelectData();
  }, []);

  // 初始化表單數據
  useEffect(() => {
    console.log("表單初始化 useEffect 觸發:", {
      editingMaintenance: editingMaintenance?.maintenanceNumber,
      isViewMode,
      hasEditingMaintenance: !!editingMaintenance,
    });

    if (editingMaintenance) {
      console.log("編輯/查看資料:", editingMaintenance);

      // 確保所有必要的欄位都被設定
      // API 回傳的是 Unix timestamp（秒），需要轉換為 dayjs 物件
      const formValues = {
        ...editingMaintenance,
        applicationDate: editingMaintenance.applicationDate
          ? dayjs.unix(editingMaintenance.applicationDate)
          : dayjs(),
        scheduledStartDate:
          editingMaintenance.scheduledStartDate &&
          editingMaintenance.scheduledStartDate > 0
            ? dayjs.unix(editingMaintenance.scheduledStartDate)
            : undefined,
        scheduledEndDate:
          editingMaintenance.scheduledEndDate &&
          editingMaintenance.scheduledEndDate > 0
            ? dayjs.unix(editingMaintenance.scheduledEndDate)
            : undefined,
        actualStartDate:
          editingMaintenance.actualStartDate &&
          editingMaintenance.actualStartDate > 0
            ? dayjs.unix(editingMaintenance.actualStartDate)
            : undefined,
        actualEndDate:
          editingMaintenance.actualEndDate &&
          editingMaintenance.actualEndDate > 0
            ? dayjs.unix(editingMaintenance.actualEndDate)
            : undefined,
        inspectionDate:
          editingMaintenance.inspectionDate &&
          editingMaintenance.inspectionDate > 0
            ? dayjs.unix(editingMaintenance.inspectionDate)
            : undefined,
      };

      console.log("設定編輯表單值:", formValues);
      // 設定表單值
      form.setFieldsValue(formValues);

      // 如果有申請人ID，清除可能的錯誤狀態
      if (editingMaintenance.applicantId) {
        form.setFields([
          {
            name: "applicantId",
            errors: [],
          },
        ]);
      }
    } else {
      // 新增模式時，確保有修繕單號
      const currentMaintenanceNumber = form.getFieldValue("maintenanceNumber");
      if (!currentMaintenanceNumber) {
        const generatedMaintenanceNumber = generateMaintenanceNumber();
        console.log("生成新的修繕單號:", generatedMaintenanceNumber);
        form.setFieldValue("maintenanceNumber", generatedMaintenanceNumber);
      }
    }
  }, [editingMaintenance]); // 移除 form 依賴項

  // 處理資產選擇變化
  const handleAssetChange = (assetId: string) => {
    const selectedAsset = assets.find(
      (asset) => asset.asset.assetId === assetId
    );
    if (selectedAsset) {
      // 自動填充資產相關資訊
      form.setFieldsValue({
        assetName: selectedAsset.asset.assetName,
      });
    }
  };

  // 處理申請人選擇變化
  const handleApplicantChange = (userId: string) => {
    if (!userId) {
      return;
    }

    const selectedEmployee = employees.find(
      (employee) => employee.userId === userId
    );

    if (selectedEmployee) {
      // 只設定申請人姓名，避免循環引用
      setTimeout(() => {
        form.setFieldValue(
          "applicantName",
          selectedEmployee.name || selectedEmployee.account
        );
      }, 0);
    }
  };

  // 處理部門選擇變化
  const handleDepartmentChange = (departmentId: string) => {
    const selectedDepartment = departments.find(
      (dept) => dept.departmentId === departmentId
    );
    if (selectedDepartment) {
      form.setFieldsValue({
        departmentName: selectedDepartment.name,
      });
    }
  };

  // 處理檢驗人員選擇變化
  const handleInspectorChange = (userId: string) => {
    const selectedEmployee = employees.find(
      (employee) => employee.userId === userId
    );

    if (selectedEmployee) {
      form.setFieldsValue({
        inspectorName: selectedEmployee.name || selectedEmployee.account,
      });
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 處理日期格式轉換 - API 需要 Unix timestamp（秒）
      const formattedData = { ...values };

      // 轉換日期為 Unix timestamp
      const dateFields = [
        "applicationDate",
        "scheduledStartDate",
        "scheduledEndDate",
        "actualStartDate",
        "actualEndDate",
        "inspectionDate",
      ];

      dateFields.forEach((field) => {
        if (formattedData[field]) {
          if (formattedData[field].unix) {
            // dayjs 物件
            formattedData[field] = formattedData[field].unix();
          } else if (formattedData[field].valueOf) {
            // Date 物件
            formattedData[field] = Math.floor(
              formattedData[field].valueOf() / 1000
            );
          } else if (typeof formattedData[field] === "number") {
            // 已經是 timestamp，保持不變
          } else {
            // 其他情況，嘗試轉換為 Date 後轉為 timestamp
            const date = new Date(formattedData[field]);
            if (!isNaN(date.getTime())) {
              formattedData[field] = Math.floor(date.getTime() / 1000);
            } else {
              // 如果無法轉換，設為 0
              formattedData[field] = 0;
            }
          }
        } else {
          // 如果欄位為空，設為 0
          formattedData[field] = 0;
        }
      });

      // 確保所有必要欄位存在並有正確的預設值
      const requiredStringFields = [
        "maintenanceNumber",
        "assetId",
        "applicantId",
        "departmentId",
        "faultDescription",
        "maintenanceType",
        "urgencyLevel",
        "vendorName",
        "vendorContact",
        "vendorPhone",
        "maintenanceResult",
        "inspectorId",
        "inspectionResult",
        "inspectionNotes",
        "status",
        "notes",
        "createUserId",
        "updateUserId",
      ];

      requiredStringFields.forEach((field) => {
        if (!formattedData[field]) {
          formattedData[field] = "";
        }
      });

      // 處理可選的 vendorId
      if (!formattedData.vendorId) {
        formattedData.vendorId = "";
      }

      // 確保數字欄位正確
      const numberFields = [
        "estimatedCost",
        "actualCost",
        "createTime",
        "updateTime",
      ];
      numberFields.forEach((field) => {
        if (
          formattedData[field] !== undefined &&
          formattedData[field] !== null
        ) {
          formattedData[field] = Number(formattedData[field]) || 0;
        } else {
          formattedData[field] = 0;
        }
      });

      // 設置系統欄位
      const currentTime = Math.floor(Date.now() / 1000);

      if (editingMaintenance?.maintenanceNumber) {
        // 更新模式
        formattedData.maintenanceNumber = editingMaintenance.maintenanceNumber;
        formattedData.createTime = editingMaintenance.createTime || currentTime;
        formattedData.createUserId = editingMaintenance.createUserId || "";
        formattedData.updateTime = currentTime;
        formattedData.updateUserId = ""; // 這裡應該從當前用戶獲取
      } else {
        // 新增模式
        formattedData.createTime = currentTime;
        formattedData.createUserId = ""; // 這裡應該從當前用戶獲取
        formattedData.updateTime = 0;
        formattedData.updateUserId = "";
      }

      // 如果是新增模式，顯示確認對話框
      if (!editingMaintenance?.maintenanceNumber) {
        console.log("確認對話框數據:", {
          assetName: formattedData.assetName,
          departmentName: formattedData.departmentName,
          applicantName: formattedData.applicantName,
        });
        setSubmitData(formattedData);
        setConfirmModalVisible(true);
      } else {
        // 編輯模式直接提交
        await performSubmit(formattedData);
      }
    } catch (error: any) {
      console.error("表單驗證失敗:", error);
    }
  };

  // 實際執行提交
  const performSubmit = async (data: any) => {
    try {
      setLoading(true);

      console.log("提交的表單數據 (格式化後):", data);

      let response;
      if (editingMaintenance?.maintenanceNumber) {
        // 更新模式
        response = await updateVendorMaintenance(
          editingMaintenance.maintenanceNumber,
          data
        );
      } else {
        // 新增模式
        response = await createVendorMaintenance(data);
      }

      if (response?.success) {
        message.success(
          editingMaintenance ? "修繕申請更新成功" : "修繕申請建立成功"
        );
        onSuccess(response.data);
      } else {
        throw new Error(
          response?.message || "操作失敗，請稍後再試或聯繫系統管理員"
        );
      }
    } catch (error: any) {
      console.error("表單提交失敗:", error);
      message.error(error.message || "操作失敗，請稍後再試或聯繫系統管理員");
    } finally {
      setLoading(false);
    }
  };

  // 確認提交
  const handleConfirmSubmit = async () => {
    setConfirmModalVisible(false);
    await performSubmit(submitData);
  };

  // 重置表單
  const handleReset = () => {
    if (editingMaintenance) {
      const formValues = {
        ...editingMaintenance,
        applicationDate: editingMaintenance.applicationDate
          ? dayjs.unix(editingMaintenance.applicationDate)
          : dayjs(),
        scheduledStartDate:
          editingMaintenance.scheduledStartDate &&
          editingMaintenance.scheduledStartDate > 0
            ? dayjs.unix(editingMaintenance.scheduledStartDate)
            : undefined,
        scheduledEndDate:
          editingMaintenance.scheduledEndDate &&
          editingMaintenance.scheduledEndDate > 0
            ? dayjs.unix(editingMaintenance.scheduledEndDate)
            : undefined,
        actualStartDate:
          editingMaintenance.actualStartDate &&
          editingMaintenance.actualStartDate > 0
            ? dayjs.unix(editingMaintenance.actualStartDate)
            : undefined,
        actualEndDate:
          editingMaintenance.actualEndDate &&
          editingMaintenance.actualEndDate > 0
            ? dayjs.unix(editingMaintenance.actualEndDate)
            : undefined,
        inspectionDate:
          editingMaintenance.inspectionDate &&
          editingMaintenance.inspectionDate > 0
            ? dayjs.unix(editingMaintenance.inspectionDate)
            : undefined,
      };
      form.setFieldsValue(formValues);
    } else {
      // 生成新的修繕單號
      const generatedMaintenanceNumber = generateMaintenanceNumber();

      form.setFieldsValue({
        ...formInitialValues,
        applicationDate: dayjs(),
        maintenanceNumber: generatedMaintenanceNumber,
      });
    }
  };

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={isViewMode}
        preserve={false}
        initialValues={
          editingMaintenance
            ? undefined
            : {
                ...formInitialValues,
                applicationDate: dayjs(),
                maintenanceNumber: generateMaintenanceNumber(), // 確保修繕單號在初始化時就生成
              }
        }
      >
        {/* 基本資訊 */}
        <Card title="基本資訊" style={{ marginBottom: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item
              label="修繕單號"
              name="maintenanceNumber"
              rules={[
                { required: true, message: "修繕單號不能為空" },
                { max: 50, message: "修繕單號不能超過50個字符" },
              ]}
            >
              <Input
                disabled={true}
                style={{
                  backgroundColor: "#f5f5f5",
                  color: "#000000d9",
                  cursor: "not-allowed",
                }}
              />
            </Form.Item>

            <Form.Item
              label="申請日期"
              name="applicationDate"
              rules={[{ required: true, message: "請選擇申請日期" }]}
            >
              <DatePicker
                style={{ width: "100%" }}
                placeholder="請選擇申請日期"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                disabled={!!editingMaintenance}
              />
            </Form.Item>
          </div>

          <Form.Item
            label="修繕財產"
            name="assetId"
            rules={[{ required: true, message: "請選擇財產" }]}
          >
            <Select
              placeholder="請選擇財產"
              loading={loadingData}
              showSearch
              optionFilterProp="children"
              disabled={!!editingMaintenance}
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  ?.toLowerCase()
                  .includes(input.toLowerCase()) || false
              }
              onChange={handleAssetChange}
            >
              {assets.map((asset) => (
                <Option key={asset.asset.assetId} value={asset.asset.assetId}>
                  {asset.asset.assetNo} - {asset.asset.assetName}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 隱藏的財產名稱欄位 */}
          <Form.Item name="assetName" hidden>
            <Input />
          </Form.Item>
        </Card>

        {/* 申請人資訊 */}
        <Card title="申請人資訊" style={{ marginBottom: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item
              label="申請人"
              name="applicantId"
              rules={[
                {
                  required: true,
                  message: "請選擇申請人",
                  whitespace: true,
                },
              ]}
              hasFeedback
              validateStatus=""
            >
              <Select
                placeholder="請選擇申請人"
                loading={loadingData}
                showSearch
                allowClear={false}
                notFoundContent={loadingData ? "載入中..." : "無相關資料"}
                filterOption={(input: string, option: any) => {
                  const label = option?.children?.toString() || "";
                  return label.toLowerCase().includes(input.toLowerCase());
                }}
                onChange={handleApplicantChange}
                value={form.getFieldValue("applicantId")}
              >
                {employees.map((employee, index) => (
                  <Option
                    key={`applicant-${employee.userId}-${index}`}
                    value={employee.userId}
                    title={employee.name || employee.account}
                  >
                    {employee.name || employee.account}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="申請部門"
              name="departmentId"
              rules={[{ required: true, message: "請選擇申請部門" }]}
            >
              <Select
                placeholder="請選擇申請部門"
                loading={loadingData}
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option?.children
                    ?.toString()
                    ?.toLowerCase()
                    .includes(input.toLowerCase()) || false
                }
                onChange={handleDepartmentChange}
              >
                {departments.map((department, index) => (
                  <Option
                    key={`department-${department.departmentId}-${index}`}
                    value={department.departmentId}
                  >
                    {department.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          {/* 隱藏的申請人姓名欄位，用於存儲顯示名稱 */}
          <Form.Item name="applicantName" hidden>
            <Input />
          </Form.Item>

          {/* 隱藏的部門名稱欄位 */}
          <Form.Item name="departmentName" hidden>
            <Input />
          </Form.Item>
        </Card>

        {/* 修繕資訊 */}
        <Card title="修繕資訊" style={{ marginBottom: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item
              label="修繕類型"
              name="maintenanceType"
              rules={[{ required: true, message: "請選擇修繕類型" }]}
            >
              <Select placeholder="請選擇修繕類型">
                {MAINTENANCE_TYPES.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="緊急程度"
              name="urgencyLevel"
              rules={[{ required: true, message: "請選擇緊急程度" }]}
            >
              <Select placeholder="請選擇緊急程度">
                {URGENCY_LEVELS.map((level) => (
                  <Option key={level.value} value={level.value}>
                    <Tag color={level.color} style={{ marginRight: 8 }}>
                      {level.label}
                    </Tag>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            label="故障描述"
            name="faultDescription"
            rules={[
              { required: true, message: "請輸入故障描述" },
              { max: 1000, message: "故障描述不能超過1000個字符" },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="請詳細描述故障情況及現象"
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item
            label="預估費用"
            name="estimatedCost"
            rules={[{ required: true, message: "請輸入預估費用" }]}
          >
            <InputNumber
              placeholder="請輸入預估費用"
              min={0}
              style={{ width: "100%" }}
              formatter={(value) =>
                `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              parser={(value: any) => value.replace(/\$\s?|(,*)/g, "")}
            />
          </Form.Item>
        </Card>

        {/* 廠商資訊 */}
        <Card title="廠商資訊" style={{ marginBottom: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item
              label="廠商名稱"
              name="vendorName"
              rules={[{ max: 100, message: "廠商名稱不能超過100個字符" }]}
            >
              <Input placeholder="請輸入廠商名稱" />
            </Form.Item>
            <Form.Item
              label="聯絡人"
              name="vendorContact"
              rules={[{ max: 50, message: "聯絡人不能超過50個字符" }]}
            >
              <Input placeholder="請輸入聯絡人姓名" />
            </Form.Item>

            <Form.Item
              label="聯絡電話"
              name="vendorPhone"
              rules={[{ max: 20, message: "聯絡電話不能超過20個字符" }]}
            >
              <Input placeholder="請輸入聯絡電話" />
            </Form.Item>
          </div>
        </Card>

        {/* 時程管理 */}
        <Card title="時程管理" style={{ marginBottom: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item label="預計開始日期" name="scheduledStartDate">
              <DatePicker
                style={{ width: "100%" }}
                placeholder="請選擇預計開始日期"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>

            <Form.Item label="預計結束日期" name="scheduledEndDate">
              <DatePicker
                style={{ width: "100%" }}
                placeholder="請選擇預計結束日期"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </div>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item label="實際開始日期" name="actualStartDate">
              <DatePicker
                style={{ width: "100%" }}
                placeholder="請選擇實際開始日期"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>

            <Form.Item label="實際結束日期" name="actualEndDate">
              <DatePicker
                style={{ width: "100%" }}
                placeholder="請選擇實際結束日期"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </div>
        </Card>

        {/* 驗收資訊 */}
        <Card title="驗收資訊" style={{ marginBottom: 16 }}>
          <Form.Item
            label="實際費用"
            name="actualCost"
            rules={[{ type: "number", min: 0, message: "實際費用不能為負數" }]}
          >
            <InputNumber
              placeholder="請輸入實際費用"
              min={0}
              style={{ width: "100%" }}
              formatter={(value) =>
                `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              parser={(value: any) => value.replace(/\$\s?|(,*)/g, "")}
            />
          </Form.Item>

          <Form.Item
            label="修繕結果"
            name="maintenanceResult"
            rules={[{ max: 1000, message: "修繕結果不能超過1000個字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="請描述修繕結果及完成情況"
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item
            label="驗收結果"
            name="inspectionResult"
            rules={[{ max: 1000, message: "驗收結果不能超過1000個字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="請描述驗收結果"
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <div
            style={{
              display: "grid",
              gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
              columnGap: "16px",
            }}
          >
            <Form.Item label="檢驗人員" name="inspectorId">
              <Select
                placeholder="請選擇檢驗人員"
                loading={loadingData}
                showSearch
                allowClear
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option?.children
                    ?.toString()
                    ?.toLowerCase()
                    .includes(input.toLowerCase()) || false
                }
                onChange={handleInspectorChange}
              >
                {employees.map((employee, index) => (
                  <Option
                    key={`inspector-${employee.userId}-${index}`}
                    value={employee.userId}
                  >
                    {employee.name || employee.account}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="驗收日期" name="inspectionDate">
              <DatePicker
                style={{ width: "100%" }}
                placeholder="請選擇驗收日期"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </div>

          {/* 隱藏的檢驗人員姓名欄位 */}
          <Form.Item name="inspectorName" hidden>
            <Input />
          </Form.Item>
        </Card>

        {/* 狀態管理 */}
        <Card title="狀態管理" style={{ marginBottom: 16 }}>
          <Form.Item
            label="狀態"
            name="status"
            rules={[{ required: true, message: "請選擇狀態" }]}
          >
            <Select placeholder="請選擇狀態">
              {STATUS_OPTIONS.map((status) => (
                <Option key={status.value} value={status.value}>
                  <Tag color={status.color} style={{ marginRight: 8 }}>
                    {status.label}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="備註"
            name="notes"
            rules={[{ max: 1000, message: "備註不能超過1000個字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="請輸入備註資訊"
              maxLength={1000}
              showCount
            />
          </Form.Item>
        </Card>
      </Form>

      {/* 表單按鈕 */}
      <FormButtons
        onCancel={onCancel}
        isViewMode={isViewMode}
        isMobile={isMobile}
        form={form}
      />

      {/* 確認提交對話框 */}
      <Modal
        title="確認新增修繕申請"
        open={confirmModalVisible}
        onCancel={() => {
          setConfirmModalVisible(false);
          setSubmitData(null);
        }}
        onOk={handleConfirmSubmit}
        okText="確認提交"
        cancelText="取消"
        confirmLoading={loading}
        width={800}
      >
        {submitData && (
          <div>
            <p style={{ marginBottom: 16, color: "#666" }}>
              請確認以下資訊無誤後提交：
            </p>

            <Descriptions
              bordered
              size="small"
              column={2}
              style={{ marginBottom: 16 }}
            >
              <Descriptions.Item label="修繕單號" span={2}>
                {submitData.maintenanceNumber}
              </Descriptions.Item>

              <Descriptions.Item label="申請人">
                {submitData.applicantName || "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="申請日期">
                {submitData.applicationDate
                  ? dayjs.unix(submitData.applicationDate).format("YYYY/MM/DD")
                  : "未設定"}
              </Descriptions.Item>

              <Descriptions.Item label="財產名稱">
                {submitData.assetName || "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="申請部門">
                {submitData.departmentName || "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="修繕類型">
                {submitData.maintenanceType
                  ? submitData.maintenanceType
                  : "未選擇"}
              </Descriptions.Item>

              <Descriptions.Item label="緊急程度">
                {submitData.urgencyLevel ? (
                  <Tag color={getUrgencyColor(submitData.urgencyLevel)}>
                    {getUrgencyLevelLabel(submitData.urgencyLevel)}
                  </Tag>
                ) : (
                  "未選擇"
                )}
              </Descriptions.Item>

              <Descriptions.Item label="故障描述" span={2}>
                {submitData.faultDescription || "未填寫"}
              </Descriptions.Item>

              <Descriptions.Item label="預估費用">
                {formatTWCurrency(submitData.estimatedCost || 0)}
              </Descriptions.Item>

              <Descriptions.Item label="狀態">
                {submitData.status ? (
                  <Tag color={getStatusColor(submitData.status)}>
                    {getStatusLabel(submitData.status)}
                  </Tag>
                ) : (
                  "未設定"
                )}
              </Descriptions.Item>

              {submitData.notes && (
                <Descriptions.Item label="備註" span={2}>
                  {submitData.notes}
                </Descriptions.Item>
              )}
            </Descriptions>

            <div
              style={{
                backgroundColor: "#f6ffed",
                border: "1px solid #b7eb8f",
                borderRadius: "6px",
                padding: "12px",
                marginTop: "16px",
              }}
            >
              <p style={{ margin: 0, color: "#389e0d", fontSize: "14px" }}>
                <strong>提醒：</strong>
              </p>
              <p
                style={{
                  margin: "4px 0 0 0",
                  color: "#389e0d",
                  fontSize: "14px",
                }}
              >
                • 確認提交後，申請將進入審核流程
                <br />
                • 請確保財產資訊和修繕類型正確
                <br />• 修繕單號依系統自動生成為主
              </p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

// 純文字顯示組件
const ViewOnlyDisplay: React.FC<{
  maintenance: VendorMaintenance;
  isMobile?: boolean;
}> = ({ maintenance, isMobile = false }) => {
  // 添加調試資訊
  console.log("ViewOnlyDisplay 接收到的資料:", maintenance);

  // 工具函數已從 config.ts 導入，無需重複定義

  // 確保顯示名稱存在，否則顯示 ID
  const displayAssetName =
    maintenance.assetName || `資產ID: ${maintenance.assetId}` || "-";
  const displayApplicantName =
    maintenance.applicantName || `申請人ID: ${maintenance.applicantId}` || "-";
  const displayDepartmentName =
    maintenance.applicantDepartment ||
    `部門ID: ${maintenance.departmentId}` ||
    "-";
  const displayInspectorName =
    maintenance.inspectorName ||
    (maintenance.inspectorId ? `檢驗人員ID: ${maintenance.inspectorId}` : "-");

  console.log("顯示名稱處理結果:", {
    assetName: displayAssetName,
    applicantName: displayApplicantName,
    departmentName: displayDepartmentName,
    inspectorName: displayInspectorName,
  });

  return (
    <div>
      <Card title="基本資訊" className="mb-4">
        <Descriptions column={isMobile ? 1 : 2} bordered size="small">
          <Descriptions.Item label="修繕單號">
            <Text strong>{maintenance.maintenanceNumber}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="財產名稱">
            <Text>{displayAssetName}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="申請日期">
            <Text>
              {DateTimeExtensions.formatDateFromTimestamp(
                maintenance.applicationDate
              )}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="狀態">
            <Tag color={getStatusColor(maintenance.status)}>
              {getStatusLabel(maintenance.status)}
            </Tag>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="申請人資訊" className="mb-4">
        <Descriptions column={isMobile ? 1 : 2} bordered size="small">
          <Descriptions.Item label="申請人">
            <Text>{displayApplicantName}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="申請部門">
            <Text>{displayDepartmentName}</Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="修繕資訊" className="mb-4">
        <Descriptions column={isMobile ? 1 : 2} bordered size="small">
          <Descriptions.Item label="修繕類型">
            <Tag color="blue">
              {getMaintenanceTypeLabel(maintenance.maintenanceType)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="緊急程度">
            <Tag color={getUrgencyColor(maintenance.urgencyLevel)}>
              {getUrgencyLevelLabel(maintenance.urgencyLevel)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="故障描述" span={isMobile ? 1 : 2}>
            <Text>{maintenance.faultDescription}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="預估費用">
            <Text strong>{formatTWCurrency(maintenance.estimatedCost)}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="實際費用">
            <Text
              strong
              style={{ color: maintenance.actualCost ? "#52c41a" : "#d9d9d9" }}
            >
              {maintenance.actualCost
                ? formatTWCurrency(maintenance.actualCost)
                : "-"}
            </Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="廠商資訊" className="mb-4">
        <Descriptions column={isMobile ? 1 : 2} bordered size="small">
          <Descriptions.Item label="廠商名稱">
            <Text>{maintenance.vendorName || "-"}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="廠商聯絡人">
            <Text>{maintenance.vendorContact || "-"}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="廠商電話">
            <Text>{maintenance.vendorPhone || "-"}</Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="施工時程" className="mb-4">
        <Descriptions column={isMobile ? 1 : 2} bordered size="small">
          <Descriptions.Item label="預計開始日期">
            <Text>
              {maintenance.scheduledStartDate
                ? DateTimeExtensions.formatDateFromTimestamp(
                    maintenance.scheduledStartDate
                  )
                : "-"}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="預計結束日期">
            <Text>
              {maintenance.scheduledEndDate
                ? DateTimeExtensions.formatDateFromTimestamp(
                    maintenance.scheduledEndDate
                  )
                : "-"}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="實際開始日期">
            <Text>
              {maintenance.actualStartDate
                ? DateTimeExtensions.formatDateFromTimestamp(
                    maintenance.actualStartDate
                  )
                : "-"}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="實際結束日期">
            <Text>
              {maintenance.actualEndDate
                ? DateTimeExtensions.formatDateFromTimestamp(
                    maintenance.actualEndDate
                  )
                : "-"}
            </Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="修繕結果" className="mb-4">
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label="修繕結果">
            <Text>{maintenance.maintenanceResult || "-"}</Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="驗收資訊" className="mb-4">
        <Descriptions column={isMobile ? 1 : 2} bordered size="small">
          <Descriptions.Item label="檢驗人員">
            <Text>{displayInspectorName}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="檢驗日期">
            <Text>
              {maintenance.inspectionDate
                ? DateTimeExtensions.formatDateFromTimestamp(
                    maintenance.inspectionDate
                  )
                : "-"}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="檢驗結果" span={isMobile ? 1 : 2}>
            <Text>{maintenance.inspectionResult || "-"}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="檢驗備註" span={isMobile ? 1 : 2}>
            <Text>{maintenance.inspectionNotes || "-"}</Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {maintenance.notes && (
        <Card title="備註" className="mb-4">
          <Text>{maintenance.notes}</Text>
        </Card>
      )}
    </div>
  );
};

export default VendorMaintenanceForm;
