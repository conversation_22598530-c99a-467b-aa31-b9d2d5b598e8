using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Attributes;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 自然人 </summary>
public class IndividualDetail
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }
    
    /// <summary> 姓氏 </summary>
    [Comment("姓氏")]
    [Column(TypeName = "nvarchar(50)")]
    public string? LastName { get; set; }

    /// <summary> 名字 </summary>
    [Comment("名字")]
    [Column(TypeName = "nvarchar(50)")]
    public string? FirstName { get; set; }

    /// <summary> 身分證字號 </summary>
    [Comment("身分證字號")]
    [Column(TypeName = "nvarchar(20)")]
    public string? IdentificationNumber { get; set; }

    /// <summary> 出生日期 </summary>
    [Comment("出生日期")]
    public DateTime? BirthDate { get; set; }

    /// <summary> 建構式 </summary>
    public IndividualDetail() {
        PartnerID = Guid.NewGuid();
    }
}

/// <summary> 自然人DTO </summary>
public class IndividualDetailDTO
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }
    
    /// <summary> 姓氏 </summary>
    [Comment("姓氏")]
    [Column(TypeName = "nvarchar(50)")]
    public string? LastName { get; set; }

    /// <summary> 名字 </summary>
    [Comment("名字")]
    [Column(TypeName = "nvarchar(50)")]
    public string? FirstName { get; set; }

    /// <summary> 身分證字號 </summary>
    [Comment("身分證字號")]
    [Column(TypeName = "nvarchar(20)")]
    public string? IdentificationNumber { get; set; }

    /// <summary> 出生日期 </summary>
    [Comment("出生日期")]
    public DateTime? BirthDate { get; set; }

    /// <summary> 建構式 </summary>
    public IndividualDetailDTO() {
        PartnerID = Guid.Empty;
    }
}