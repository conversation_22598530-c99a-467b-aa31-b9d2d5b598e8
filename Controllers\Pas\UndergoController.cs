using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("歷任經歷資料管理")]
    public class UndergoController : ControllerBase
    {
        private readonly IUndergoService _interface;

        public UndergoController(IUndergoService undergoService)
        {
            _interface = undergoService;
        }

        [HttpGet]
        [Route("GetAll/{_userid}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有歷任經歷資料列表")]
        public async Task<IActionResult> GetUndergoList(string _userid)
        {
            var result = await _interface.GetUndergoListAsync(_userid);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得歷任經歷明細", Description = "依uid取得歷任經歷明細")]
        public async Task<IActionResult> GetUndergoDetail(string _uid)
        {
            var result = await _interface.GetUndergoDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增歷任經歷資料", Description = "新增歷任經歷資料")]
        public async Task<IActionResult> AddUndergo([FromBody] UndergoDTO _data)
        {
            var (result, msg) = await _interface.AddUndergoAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯歷任經歷資料", Description = "編輯歷任經歷資料")]
        public async Task<IActionResult> EditUndergo([FromBody] UndergoDTO _data)
        {
            var (result, msg) = await _interface.EditUndergoAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除歷任經歷資料", Description = "刪除歷任經歷資料")]
        public async Task<IActionResult> DeleteUndergo([FromBody] string _uid)
        {
            var (result, msg) = await _interface.DeleteUndergoAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}
