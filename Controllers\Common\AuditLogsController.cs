using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("審核日誌管理")]
    public class AuditLogsController : ControllerBase
    {
        private readonly IAuditLogsService _auditLogsService;

        public AuditLogsController(IAuditLogsService auditLogsService)
        {
            _auditLogsService = auditLogsService;
        }

        [HttpGet]
        [Route("GetAuditLogs")]
        [SwaggerOperation(Summary = "取得日誌列表", Description = "取得所有審核日誌列表")]
        public async Task<IActionResult> GetAuditLogsList()
        {
            var logs = await _auditLogsService.GetAuditLogsAsync();
            return Ok(logs);
        }

        [HttpGet]
        [Route("GetAuditLogs/{_auditLogsId?}")]
        [SwaggerOperation(Summary = "取得日誌明細", Description = "依ID取得單筆日誌明細")]
        public async Task<IActionResult> GetAuditLogsDetail(string _auditLogsId)
        {
            var log = await _auditLogsService.GetAuditLogsAsync(_auditLogsId);
            return Ok(log);
        }

        [HttpPost]
        [Route("AddAuditLogs")]
        [SwaggerOperation(Summary = "新增審核日誌", Description = "新增審核日誌")]
        public async Task<IActionResult> AddAuditLogs([FromBody] AuditLogsDTO logData)
        {
            var (result, msg) = await _auditLogsService.AddAuditLogsAsync(logData);
            return Ok(new { result, msg });
        }
    }
}
