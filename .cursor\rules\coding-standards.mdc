---
description: 
globs: 
alwaysApply: false
---
# 程式碼標準與最佳實踐

## TypeScript 使用規範
1. 始終使用 TypeScript 類型標註
2. 避免使用 `any` 類型，優先使用 `unknown`
3. 使用 interface 定義對象類型，除非需要使用 union 類型
4. 所有的類型定義應放在 `/src/types` 目錄下

## React 組件規範
1. 使用函數組件和 Hooks
2. 組件文件使用 `.tsx` 擴展名
3. 每個組件一個文件
4. 組件命名使用 PascalCase
5. Props 接口命名格式為 `[組件名]Props`

## 狀態管理
1. 使用 React Context 進行全局狀態管理
2. Context 相關文件放在 `/src/contexts` 目錄
3. 複雜狀態邏輯使用 Redux Toolkit

## API 整合
1. 所有 API 調用應通過 service 層處理
2. 使用 Axios 實例進行 HTTP 請求
3. API 錯誤處理應統一管理

## UI 組件與樣式規範
1. 使用 Ant Design (antd) 作為主要 UI 組件庫
   - 優先使用 antd 提供的組件
   - 遵循 antd 的設計規範和最佳實踐
   - 使用 antd 的主題定制功能進行品牌化設計
   - 組件引入採用按需加載方式
2. 使用 Tailwind CSS 作為輔助樣式解決方案
   - 用於自定義佈局和間距
   - 用於 antd 組件無法滿足的樣式需求
3. 自定義樣式應使用 CSS Modules
4. 全局樣式定義在 `/src/styles` 目錄
5. antd 主題配置文件放在 `/src/config/theme.ts`

## 錯誤處理
1. 使用 try-catch 處理異步操作
2. 統一的錯誤處理機制
3. 適當的錯誤日誌記錄

## 測試規範
1. 組件測試使用 React Testing Library
2. 單元測試使用 Jest
3. 測試文件使用 `.test.ts` 或 `.test.tsx` 擴展名

## Git 提交規範
1. 使用語義化的提交信息
2. 遵循 conventional commits 規範
3. 提交前進行代碼格式化

## 表單模組化設計原則

### 檔案結構與職責
1. `interface.ts`
   - 定義所有類型和介面
   - 定義狀態顏色映射常量
   - 定義表單初始值
   - 集中管理所有類型定義，避免循環依賴

2. `columns.tsx`
   - 定義表格列配置
   - 處理數據展示邏輯
   - 提供桌面版和移動版列定義
   - 保持列定義的一致性和可維護性

3. `Form.tsx`
   - 處理表單邏輯和驗證
   - 實現表單字段的動態聯動
   - 處理自定義表單項（如財產來源、攤提來源）
   - 實現響應式表單佈局

4. `page.tsx`
   - 管理頁面狀態和生命週期
   - 處理數據加載和更新
   - 實現篩選和搜索功能
   - 處理移動端適配

### 設計原則
1. 狀態管理
   - 使用 React Hooks 管理局部狀態
   - 將複雜狀態邏輯抽離為自定義 Hooks
   - 避免不必要的狀態更新

2. 數據處理
   - 統一的數據格式轉換
   - 集中的錯誤處理
   - 優化數據加載性能

3. UI/UX 設計
   - 實現響應式設計
   - 保持一致的視覺風格
   - 優化表單交互體驗

4. 代碼複用
   - 抽取共用組件和邏輯
   - 使用 TypeScript 確保類型安全
   - 保持代碼的可測試性

5. 性能優化
   - 使用 React.memo 優化渲染
   - 實現分頁加載
   - 優化大數據列表性能

6. 安全性
   - 輸入驗證和消毒
   - 權限控制
   - 敏感數據處理

7. 可維護性
   - 清晰的代碼組織
   - 完整的註釋文檔
   - 模組化的設計結構

   
### 響應式設計規範
1. 使用 Ant Design 的響應式組件
2. 自定義斷點處理
3. 移動端優化
4. 適配不同屏幕尺寸

