"use client";
/* AntdRegistry用途：
1、解決 SSR 樣式問題
  *確保服務端渲染時 Ant Design 的樣式能正確注入
  *防止頁面初次加載時出現樣式閃爍（FOUC）
2、樣式緩存管理：
  *創建樣式緩存，避免重複渲染
  *提升性能，減少不必要的樣式計算
3、樣式同步：
  *確保客戶端和服務端的樣式保持一致
  *處理樣式的 hydration客戶端過程
  *如果不使用 StyledComponentsRegistry，可能會遇到：
  樣式閃爍
  SSR 樣式不一致
  性能問題
  組件樣式渲染異常
 */
import React from "react";
import { createCache, extractStyle, StyleProvider } from "@ant-design/cssinjs";
import { useServerInsertedHTML } from "next/navigation";
const StyledComponentsRegistry = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const cache = React.useMemo(() => createCache(), []);

  useServerInsertedHTML(() => (
    <style
      id="antd"
      dangerouslySetInnerHTML={{ __html: extractStyle(cache, true) }}
    />
  ));

  return <StyleProvider cache={cache}>{children}</StyleProvider>;
};

export default StyledComponentsRegistry;
