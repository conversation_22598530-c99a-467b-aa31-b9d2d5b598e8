using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 財產狀態
    /// </summary>
    public class AssetStatus : ModelBaseEntity
    {
        [Key]
        [Comment("財產狀態流水號")]
        public Guid AssetStatusId { get; set; }

        [Comment("財產狀態編號")]
        [RegularExpression(@"^[0-9a-zA-Z]{1,10}$", ErrorMessage = "財產狀態編號必須為1至10位英數字")]
        [StringLength(10, MinimumLength = 1, ErrorMessage = "財產狀態編號必須為1至10位英數字")]
        [Column(TypeName = "nvarchar(10)")]
        [Required(ErrorMessage = "財產狀態編號為必填欄位")]
        public string AssetStatusNo { get; set; }

        [Comment("財產狀態名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; }


        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; }

        public AssetStatus()
        {
            AssetStatusId = Guid.NewGuid();
            AssetStatusNo = "";
            Name = "";
            SortCode = 0;
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class AssetStatusDTO : ModelBaseEntityDTO
    {
        public Guid AssetStatusId { get; set; }
        public string AssetStatusNo { get; set; }
        public string Name { get; set; }
        public int SortCode { get; set; }

        public AssetStatusDTO()
        {
            AssetStatusId = Guid.Empty;
            AssetStatusNo = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}
