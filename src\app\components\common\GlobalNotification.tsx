// components/GlobalNotification.tsx
import React, { useEffect, useState } from "react";
import { eventBus } from "@/utils/eventBus";

// 定義事件類型
interface NotificationEvent {
  action: string;
  data?: any;
}

interface GlobalNotificationItem {
  id: number;
  action: string;
  data?: any;
}

interface NoticeNotificationItemProps {
  notification: GlobalNotificationItem;
  index: number;
  removeNotification: (id: number) => void;
}

const NoticeNotificationItem: React.FC<NoticeNotificationItemProps> = ({
  notification,
  index,
  removeNotification,
}) => {
  const [entered, setEntered] = useState(false);

  useEffect(() => {
    // 延遲觸發讓 CSS transition 有效果
    const timer = setTimeout(() => setEntered(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      style={{
        transform: entered ? "translateX(0)" : "translateX(100%)",
        transition: "transform 0.3s ease, margin-top 0.3s ease",
        marginTop: index > 0 ? "10px" : "0",
        backgroundColor: "#fff",
        color: "#333",
        padding: "15px 20px",
        borderRadius: "8px",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        minWidth: "250px",
        fontSize: "0.95rem",
        display: "flex",
        alignItems: "center",
      }}
    >
      <span style={{ flex: 1 }}>{notification.data?.message || "通知"}</span>
      <button
        onClick={() => removeNotification(notification.id)}
        style={{
          background: "transparent",
          border: "none",
          fontSize: "1.2rem",
          color: "#333",
          cursor: "pointer",
          marginLeft: "10px",
        }}
      >
        &#215;
      </button>
    </div>
  );
};

const GlobalNotification: React.FC = () => {
  const [notifications, setNotifications] = useState<GlobalNotificationItem[]>(
    []
  );

  const removeNotification = (id: number) => {
    setNotifications((prev) => prev.filter((item) => item.id !== id));
  };

  useEffect(() => {
    const handleNotificationEvent = (event: NotificationEvent) => {
      const newNotification: GlobalNotificationItem = {
        id: Date.now(),
        action: event.action,
        data: event.data,
      };

      setNotifications((prev) => [...prev, newNotification]);

      // 除了 maintenance 之外，其他通知自動 5 秒後關閉
      if (event.action !== "maintenance") {
        setTimeout(() => {
          removeNotification(newNotification.id);
        }, 5000);
      }
    };

    // 使用新的事件名稱 "GlobalNotificationUI"
    eventBus.on("GlobalNotificationUI", handleNotificationEvent as any);
    return () => {
      eventBus.off("GlobalNotificationUI", handleNotificationEvent as any);
    };
  }, []);

  // 將 maintenance 與其他通知分開處理
  const maintenanceNotifications = notifications.filter(
    (n) => n.action === "maintenance"
  );
  const otherNotifications = notifications.filter(
    (n) => n.action !== "maintenance"
  );

  return (
    <>
      {/* maintenance 通知：置中且距離螢幕頂部 20px，不會緊貼頂部，寬度根據頁面自適應 */}
      {maintenanceNotifications.map((notification) => (
        <div
          key={notification.id}
          style={{
            position: "fixed",
            top: "20px", // 與螢幕頂部有 20px 的間距
            left: "50%",
            transform: "translateX(-50%)",
            width: "calc(100% - 40px)", // 左右各留 20px 間距
            maxWidth: "600px",
            background: "linear-gradient(135deg, #ff6b6b 0%, #f06595 100%)", // 現代感漸層背景
            color: "#fff",
            padding: "15px 20px",
            zIndex: 1100,
            display: "flex",
            alignItems: "center",
            borderRadius: "12px",
            boxShadow: "0 8px 16px rgba(0, 0, 0, 0.2)",
            fontSize: "1rem",
            backdropFilter: "blur(8px)", // 添加玻璃感效果
            border: "1px solid rgba(255,255,255,0.3)",
          }}
        >
          <span style={{ flex: 1 }}>
            {notification.data?.message || "系統維護中，請稍後再試"}
          </span>
          <button
            onClick={() => removeNotification(notification.id)}
            style={{
              background: "transparent",
              border: "none",
              fontSize: "1.4rem",
              color: "#fff",
              cursor: "pointer",
              marginLeft: "15px",
            }}
          >
            知道了
          </button>
        </div>
      ))}

      {/* 其他通知：置於右下角，並具有滑入及推疊動畫效果 */}
      <div
        style={{
          position: "fixed",
          bottom: "30px",
          right: "30px",
          zIndex: 1100,
        }}
      >
        {[...otherNotifications].reverse().map((notification, index) => (
          <NoticeNotificationItem
            key={notification.id}
            notification={notification}
            index={index}
            removeNotification={removeNotification}
          />
        ))}
      </div>
    </>
  );
};

export default GlobalNotification;
