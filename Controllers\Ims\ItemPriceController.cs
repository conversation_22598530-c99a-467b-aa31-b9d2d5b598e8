using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Ims;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Ims;
/// <summary> 庫存品價格管理 </summary>
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("庫存品價格管理")]
public class ItemPriceController(IItemPriceService _Interface) : ControllerBase
{
    /// <summary> 庫存品價格列表取得 </summary>
    [HttpGet]
    [Route("GetAll")]
    [SwaggerOperation(Summary = "庫存品價格列表取得", Description = "庫存品價格列表取得")]
    public async Task<IActionResult> GetAll()
    {
        var items = await _Interface.GetAllAsync();
        return Ok(new { success = true, message = "庫存品價格列表取得成功", data = items });
    }

    /// <summary> 庫存品價格取得 </summary>
    [HttpGet]
    [Route("{ItemPriceID}")]
    [SwaggerOperation(Summary = "庫存品價格取得", Description = "根據商品價格ID取得單一商品價格詳細資料")]
    public async Task<IActionResult> Get([FromRoute] Guid ItemPriceID)
    {
        if (ItemPriceID == Guid.Empty)
        {
            return BadRequest(new { success = false, message = "商品價格ID不能為空", data = (object?)null });
        }

        var itemPrice = await _Interface.GetAsync(ItemPriceID);
        if (itemPrice == null)
        {
            return NotFound(new { success = false, message = "找不到指定的商品價格", data = (object?)null });
        }

        return Ok(new { success = true, message = "庫存品價格取得成功", data = itemPrice });
    }

    /// <summary> 庫存品價格新增 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "庫存品價格新增", Description = "庫存品價格新增")]
    public async Task<IActionResult> Add([FromBody] ItemPriceDTO _data)
    {
        var (result, msg) = await _Interface.AddAsync(_data);
        if (!result)
        {
            return BadRequest(new { success = false, message = msg, data = (object?)null });
        }
        return Ok(new { success = true, message = msg, data = (object?)null });
    }

    /// <summary> 庫存品價格更新 </summary>
    [HttpPatch]
    [SwaggerOperation(Summary = "庫存品價格更新", Description = "庫存品價格更新")]
    public async Task<IActionResult> Update([FromBody] ItemPriceDTO _data)
    {
        var (result, msg) = await _Interface.UpdateAsync(_data);
        if (!result)
        {
            return BadRequest(new { success = false, message = msg, data = (object?)null });
        }
        return Ok(new { success = true, message = msg, data = (object?)null });
    }

    /// <summary> 庫存品價格刪除 </summary>
    [HttpDelete]
    [SwaggerOperation(Summary = "庫存品價格刪除", Description = "庫存品價格刪除")]
    public async Task<IActionResult> Delete([FromBody] ItemPriceDTO _data)
    {
        var (result, msg) = await _Interface.DeleteAsync(_data);
        if (!result)
        {
            return BadRequest(new { success = false, message = msg, data = (object?)null });
        }
        return Ok(new { success = true, message = msg, data = (object?)null });
    }
}