// EmployeeRegularSalary 常態薪資管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "@/services/http";
import { ApiResponse } from "@/config/api";

// 常態薪資資料型別
export interface EmployeeRegularSalary {
    uid: string;
    userId: string;
    salaryItemUid: string;
    salaryItemName: string;
    amount: string; // 金額字串，例如 "3000"
    remark: string;
    isEnable: boolean;
    itemType: string; // 1: 加項, 2: 減項
    itemTypeName: string; // "加項" | "減項"
}

// 建立空的常態薪資資料
export const createEmptyEmployeeRegularSalary = (): EmployeeRegularSalary => ({
    uid: '',
    userId: '',
    salaryItemUid: '',
    salaryItemName: '',
    amount: '',
    remark: '',
    isEnable: true,
    itemType: '',
    itemTypeName: '',
});

// 根據 userId 取得該員工的常態薪資清單
export async function getEmployeeRegularSalaryList(userId: string): Promise<ApiResponse<EmployeeRegularSalary[]>> {
    return await httpClient(`${apiEndpoints.getEmployeeRegularSalaryList}/${userId}`, {
        method: "GET",
    });
}

// 取得單筆常態薪資明細
export async function getEmployeeRegularSalaryDetail(uid: string): Promise<ApiResponse<EmployeeRegularSalary>> {
    return await httpClient(`${apiEndpoints.getEmployeeRegularSalaryDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增常態薪資
export async function addEmployeeRegularSalary(data: Partial<EmployeeRegularSalary>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addEmployeeRegularSalary, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增常態薪資失敗",
        };
    }
}

// 編輯常態薪資
export async function editEmployeeRegularSalary(data: Partial<EmployeeRegularSalary>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editEmployeeRegularSalary, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯常態薪資失敗",
        };
    }
}

// 刪除常態薪資
export async function deleteEmployeeRegularSalary(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteEmployeeRegularSalary, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除常態薪資失敗",
        };
    }
}
