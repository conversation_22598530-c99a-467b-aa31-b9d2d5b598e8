﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <log4net>
    <!-- 定義記錄器 -->
    <root>
      <level value="DEBUG" />
      <appender-ref ref="ConsoleAppender" />
      <appender-ref ref="RollingFileAppender" />
    </root>

    <!-- Console 輸出 -->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level %logger - %message%newline" />
      </layout>
    </appender>

    <!-- 滾動日誌檔案 -->
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="Logs/log.txt" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <datePattern value="yyyyMMdd'.log'" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level %logger - %message%newline" />
      </layout>
    </appender>
  </log4net>
</configuration>
