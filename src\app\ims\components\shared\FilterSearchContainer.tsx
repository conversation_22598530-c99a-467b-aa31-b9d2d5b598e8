"use client";

import React from 'react';
import { useFilterSearch, type UseFilterSearchOptions } from '@/app/ims/hooks/useFilterSearch';
import FilterSearchPanel from './FilterSearchPanel';
import { type FilterOption } from './AdvancedFilterComponent';

// TypeScript 介面定義
export interface FilterSearchContainerProps extends UseFilterSearchOptions {
  /** 篩選選項配置 */
  filterOptions: FilterOption[];
  /** 搜尋框佔位符 */
  searchPlaceholder?: string;
  /** 標題 */
  title?: string;
  /** 是否顯示統計資訊 */
  showStats?: boolean;
  /** 統計資訊 */
  stats?: {
    total: number;
    filtered: number;
  };
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否緊湊模式 */
  compact?: boolean;
  /** 自定義樣式 */
  className?: string;
  /** 篩選結果變更回調 */
  onFilterResult?: (state: {
    searchText: string;
    activeFilters: string[];
    filterValues: Record<string, any>;
    hasActiveFilters: boolean;
    filterCount: number;
  }) => void;
}

/**
 * 篩選搜尋容器組件 - 最簡化的使用方式
 * 
 * 這個組件封裝了所有的狀態管理邏輯，提供最簡單的使用介面。
 * 新頁面只需要配置篩選選項，就可以獲得完整的篩選搜尋功能。
 * 
 * @example
 * ```tsx
 * // 最簡單的使用方式
 * <FilterSearchContainer
 *   filterOptions={[
 *     { label: "名稱", value: "name", type: "input" },
 *     { label: "狀態", value: "status", children: [
 *       { label: "啟用", value: "active" },
 *       { label: "停用", value: "inactive" }
 *     ]}
 *   ]}
 *   onFilterResult={(state) => {
 *     // 處理篩選結果
 *     const filtered = applyFilters(data, state);
 *     setFilteredData(filtered);
 *   }}
 * />
 * ```
 * 
 * @example
 * ```tsx
 * // 完整配置的使用方式
 * <FilterSearchContainer
 *   title="Partner 篩選"
 *   filterOptions={partnerFilterOptions}
 *   searchPlaceholder="搜尋 Partner 名稱或編號"
 *   showStats={true}
 *   stats={{ total: partners.length, filtered: filteredPartners.length }}
 *   showClearMessage={true}
 *   clearMessage="已清除所有 Partner 篩選條件"
 *   onClear={() => {
 *     // Partner 頁面特有的清除邏輯
 *     setPartnerType('');
 *     setRegion('');
 *   }}
 *   onFilterResult={(state) => {
 *     // 處理 Partner 篩選結果
 *     setFilteredPartners(applyPartnerFilters(partners, state));
 *   }}
 * />
 * ```
 */
const FilterSearchContainer: React.FC<FilterSearchContainerProps> = ({
  filterOptions,
  searchPlaceholder = "搜尋...",
  title = "篩選與搜尋",
  showStats = true,
  stats,
  disabled = false,
  compact = false,
  className = '',
  onFilterResult,
  ...hookOptions
}) => {
  // 使用篩選搜尋 Hook
  const filterSearch = useFilterSearch({
    ...hookOptions,
    onFilterChange: (state) => {
      // 調用原有的回調
      hookOptions.onFilterChange?.(state);
      
      // 調用篩選結果回調
      onFilterResult?.({
        searchText: state.searchText,
        activeFilters: state.activeFilters,
        filterValues: state.filterValues,
        hasActiveFilters: filterSearch.hasActiveFilters,
        filterCount: filterSearch.filterCount
      });
    }
  });

  // 檢測移動端
  const [isMobile, setIsMobile] = React.useState(false);
  React.useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <FilterSearchPanel
      title={title}
      filterOptions={filterOptions}
      searchPlaceholder={searchPlaceholder}
      onSearchChange={filterSearch.setSearchText}
      onFilterChange={(event) => {
        filterSearch.setFilterData({
          activeFilters: event.activeFilters,
          filterValues: event.filterValues
        });
      }}
      onClearAll={filterSearch.clearAll}
      showStats={showStats}
      stats={stats}
      initialSearchText={filterSearch.searchText}
      initialFilters={{
        activeFilters: filterSearch.activeFilters,
        filterValues: filterSearch.filterValues
      }}
      disabled={disabled}
      compact={compact || isMobile}
      className={className}
    />
  );
};

export default FilterSearchContainer;
