﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 歷史經歷資料表
    /// </summary>
    public class Undergo : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 使用者編號

        [Comment("服務機關名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string agencyName { get; set; } // 服務機關名稱

        [Comment("服務部門名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string? departmentName { get; set; } // 服務部門名稱

        [Comment("職稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string? jobTitle { get; set; } // 職稱

        [Comment("職務")]
        [Column(TypeName = "nvarchar(100)")]
        public string? duty { get; set; } // 職務

        [Comment("薪級")]
        [Column(TypeName = "nvarchar(100)")]
        public string? jobGrade { get; set; } // 薪級

        [Comment("到職年月")]
        [Column(TypeName = "nvarchar(100)")]
        public string? hireDate { get; set; } // 到職年月

        [Comment("卸職年月")]
        [Column(TypeName = "nvarchar(100)")]
        public string? terminationDate { get; set; } // 卸職年月

        [Comment("主管姓名")]
        [Column(TypeName = "nvarchar(100)")]
        public string? supervisorName { get; set; } // 主管姓名

        [Comment("發證日期")]
        [Column(TypeName = "bigint")]
        public long? certificateDate { get; set; } // 發證日期

        [Comment("證書文號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? certificateNumber { get; set; } // 證書文號

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? remark { get; set; } // 備註

        public Undergo()
        {
            uid = "";
            userId = "";
            agencyName = "";
            departmentName = "";
            jobTitle = "";
            duty = "";
            jobGrade = "";
            hireDate = "";
            terminationDate = "";
            supervisorName = "";
            certificateDate = null;
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 歷史經歷資料 DTO
    /// </summary>
    public class UndergoDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string agencyName { get; set; } // 服務機關名稱
        public string departmentName { get; set; } // 服務部門名稱
        public string jobTitle { get; set; } // 職稱
        public string duty { get; set; } // 職務
        public string jobGrade { get; set; } // 薪級
        public string hireDate { get; set; } // 到職年月（timestamp）
        public string terminationDate { get; set; } // 卸職年月（timestamp）
        public string supervisorName { get; set; } // 主管姓名
        public string certificateDate { get; set; } // 發證日期（timestamp）
        public string certificateNumber { get; set; } // 證書文號
        public string remark { get; set; } // 備註

        public UndergoDTO()
        {
            uid = "";
            userId = "";
            agencyName = "";
            departmentName = "";
            jobTitle = "";
            duty = "";
            jobGrade = "";
            hireDate = "";
            terminationDate = "";
            supervisorName = "";
            certificateDate = "";
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

