using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("資產類別管理")]
    public class AssetCategoryController : ControllerBase
    {
        private readonly IAssetCategoryService _assetCategoryService;

        public AssetCategoryController(IAssetCategoryService assetCategoryService)
        {
            _assetCategoryService = assetCategoryService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得資產類別列表", Description = "取得所有資產類別列表")]
        public async Task<IActionResult> GetAssetCategoryList()
        {
            var categories = await _assetCategoryService.GetAllAsync();
            return Ok(categories);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得資產類別明細", Description = "依ID取得資產類別明細")]
        public async Task<IActionResult> GetAssetCategoryDetail(string id)
        {
            var category = await _assetCategoryService.GetByIdAsync(id);
            if (category == null)
                return NotFound($"找不到編號為{id}的資產類別。");

            return Ok(category);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增資產類別", Description = "新增資產類別")]
        public async Task<IActionResult> AddAssetCategory([FromBody] AssetCategoryDTO assetCategory)
        {
            var result = await _assetCategoryService.AddAsync(assetCategory);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯資產類別", Description = "編輯資產類別")]
        public async Task<IActionResult> EditAssetCategory([FromBody] AssetCategoryDTO assetCategory)
        {
            var result = await _assetCategoryService.UpdateAsync(assetCategory);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除資產類別", Description = "刪除資產類別")]
        public async Task<IActionResult> DeleteAssetCategory([FromBody] AssetCategoryDTO assetCategory)
        {
            var result = await _assetCategoryService.DeleteAsync(assetCategory);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }
    }
}