import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 資產類別介面定義
export interface AssetCategory {
    assetCategoryId: string;
    assetCategoryName: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

/**
 * 取得所有資產類別列表
 */
export async function getAssetCategories(): Promise<ApiResponse<AssetCategory[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssetCategories, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取資產類別列表失敗",
            data: [],
        };
    }
}

/**
 * 依ID取得資產類別明細
 */
export async function getAssetCategoryById(id: string): Promise<ApiResponse<AssetCategory>> {
    try {
        const response = await httpClient(`${apiEndpoints.getAssetCategoryDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取資產類別明細失敗",
            data: undefined,
        };
    }
}

/**
 * 新增資產類別
 */
export async function addAssetCategory(data: Partial<AssetCategory>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAssetCategory, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增資產類別失敗",
        };
    }
}

/**
 * 編輯資產類別
 */
export async function editAssetCategory(data: Partial<AssetCategory>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAssetCategory, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯資產類別失敗",
        };
    }
}

/**
 * 刪除資產類別
 */
export async function deleteAssetCategory(data: Partial<AssetCategory>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAssetCategory, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除資產類別失敗",
        };
    }
}
