/* Material Icons */
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

.material-icons {
    font-family            : 'Material Icons';
    font-weight            : normal;
    font-style             : normal;
    font-size              : 24px;
    line-height            : 1;
    letter-spacing         : normal;
    text-transform         : none;
    display                : inline-block;
    white-space            : nowrap;
    word-wrap              : normal;
    direction              : ltr;
    -webkit-font-smoothing : antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering         : optimizeLegibility;
    font-feature-settings  : 'liga';
}

/* 響應式樣式 */
@media screen and (max-width: 992px) {
    .function-menu-card {
        margin-top: 16px;
    }
}

/* 卡片網格系統樣式 */
.ant-row {
    transition: all 0.3s ease;
}

.ant-col {
    transition: all 0.3s ease;
}

/* 卡片容器樣式 */
.card-container {
    transition: all 0.3s ease;
    height    : 100%;
}

/* 功能選單卡片樣式 */
.function-menu-card {
    transition: all 0.3s ease;
}

/* 確保按鈕在不同尺寸下的對齊 */
@media screen and (max-width: 992px) {
    .function-menu-card .ant-btn {
        justify-content: center;
    }

    .function-menu-card .ant-card-body {
        padding: 16px;
    }
}

/* 優化小螢幕下的間距 */
@media screen and (max-width: 576px) {
    .function-menu-card {
        margin-top: 12px;
    }

    .function-menu-card .ant-card-body {
        padding: 12px;
    }

    .ant-row {
        margin-left : -8px !important;
        margin-right: -8px !important;
    }

    .ant-col {
        padding-left : 8px !important;
        padding-right: 8px !important;
    }
}