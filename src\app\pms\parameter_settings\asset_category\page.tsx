"use client";

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Descriptions,
  List,
  Typography,
  Dropdown,
  Select,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  FilterOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { MenuProps } from "antd";
import {
  AssetCategory,
  getAssetCategories,
  addAssetCategory,
  editAssetCategory,
  deleteAssetCategory,
} from "@/services/pms/assetCategoryService";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import guidUtils from "@/utils/guidUtils";

const { Text } = Typography;

// 定義篩選選項
const filterOptions = [
  { label: "類別代碼", value: "assetCategoryId", type: "input" },
  { label: "類別名稱", value: "assetCategoryName", type: "input" },
];

const AssetCategoryPage = () => {
  const [assetCategories, setAssetCategories] = useState<AssetCategory[]>([]);
  const [filteredAssetCategories, setFilteredAssetCategories] = useState<
    AssetCategory[]
  >([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAssetCategory, setEditingAssetCategory] =
    useState<AssetCategory | null>(null);
  const [form] = Form.useForm();
  const { user } = useAuth();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [assetCategoryToDelete, setAssetCategoryToDelete] =
    useState<AssetCategory | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // 載入資產類別資料
  const loadAssetCategories = async () => {
    setLoading(true);
    try {
      const response = await getAssetCategories();
      if (response.success && response.data) {
        setAssetCategories(response.data);
        setFilteredAssetCategories(response.data);
      } else {
        notifyError("獲取資產類別列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取資產類別列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAssetCategories();
  }, []);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 清除篩選條件
  const handleClearFilters = () => {
    setActiveFilters([]);
    setFilterValues({});
  };

  // 新增篩選條件
  const handleAddFilter = (filterKey: string) => {
    if (!activeFilters.includes(filterKey)) {
      setActiveFilters([...activeFilters, filterKey]);
    }
  };

  // 移除篩選條件
  const handleRemoveFilter = (filterKey: string) => {
    setActiveFilters(activeFilters.filter((key) => key !== filterKey));
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];
    setFilterValues(newFilterValues);
  };

  // 更新篩選值
  const handleFilterValueChange = (filterKey: string, value: any) => {
    setFilterValues({
      ...filterValues,
      [filterKey]: value,
    });
  };

  // 更新篩選選單
  const filterMenu: MenuProps = {
    items: [
      ...filterOptions.map((option) => ({
        key: option.value,
        label: (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: "120px",
            }}
          >
            <span>{option.label}</span>
            {activeFilters.includes(option.value) && (
              <CheckOutlined style={{ color: "#1890ff" }} />
            )}
          </div>
        ),
        onClick: () => {
          if (activeFilters.includes(option.value)) {
            handleRemoveFilter(option.value);
          } else {
            handleAddFilter(option.value);
          }
        },
      })),
      ...(activeFilters.length > 0
        ? [
            { type: "divider" as const },
            {
              key: "clear",
              label: "清除所有篩選",
              onClick: handleClearFilters,
              danger: true,
            },
          ]
        : []),
    ],
  };

  // 產生篩選控制項
  const renderFilterControl = (filterKey: string) => {
    const option = filterOptions.find((opt) => opt.value === filterKey);
    if (!option) return null;

    // 取得對應欄位的唯一值作為選項
    const uniqueValues = Array.from(
      new Set(
        assetCategories
          .map((m) => (m as any)[filterKey] as string | undefined)
          .filter((v) => v)
      )
    ) as string[];

    return (
      <Select
        value={filterValues[filterKey]}
        onChange={(value) => handleFilterValueChange(filterKey, value)}
        style={{ minWidth: 160 }}
        placeholder={`選擇${option.label}`}
        allowClear
        showSearch
        optionFilterProp="children"
      >
        {uniqueValues.map((val) => (
          <Select.Option key={val} value={val}>
            {val}
          </Select.Option>
        ))}
      </Select>
    );
  };

  // 更新搜尋 & 篩選邏輯
  useEffect(() => {
    let filtered = [...assetCategories];

    // 搜尋文字
    if (searchText) {
      filtered = filtered.filter(
        (m) =>
          m.assetCategoryId.toLowerCase().includes(searchText.toLowerCase()) ||
          m.assetCategoryName.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 套用自訂篩選
    filtered = filtered.filter((m) => {
      return activeFilters.every((key) => {
        const value = filterValues[key];
        if (!value) return true;
        const target = (m as any)[key] as string | undefined;
        if (target === undefined || target === null) return false;
        return String(target).toLowerCase() === String(value).toLowerCase();
      });
    });

    setFilteredAssetCategories(filtered);
  }, [searchText, assetCategories, activeFilters, filterValues]);

  // 表格欄位定義
  const columns: ColumnsType<AssetCategory> = [
    {
      key: "index",
      width: 80,
      render: (_, record, index) => {
        return <span>{index + 1}</span>;
      },
    },
    {
      title: "類別代碼",
      dataIndex: "assetCategoryId",
      key: "assetCategoryId",
      width: 120,
    },
    {
      title: "類別名稱",
      dataIndex: "assetCategoryName",
      key: "assetCategoryName",
      width: 200,
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          {/* <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button> */}
        </Space>
      ),
    },
  ];

  // 處理新增
  const handleAdd = () => {
    setEditingAssetCategory(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 處理編輯
  const handleEdit = (record: AssetCategory) => {
    setEditingAssetCategory(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  // 處理刪除
  const handleDelete = (record: AssetCategory) => {
    setAssetCategoryToDelete(record);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!assetCategoryToDelete) return;

    try {
      const response = await deleteAssetCategory(assetCategoryToDelete);
      if (response.success) {
        notifySuccess("刪除成功", "資產類別已成功刪除");
        loadAssetCategories();
        setDeleteModalVisible(false);
        setAssetCategoryToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message);
      }
    } catch (error) {
      notifyError("刪除失敗", "請稍後再試");
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        assetCategoryId:
          editingAssetCategory?.assetCategoryId || values.assetCategoryId,
        createUserId: editingAssetCategory
          ? editingAssetCategory.createUserId
          : user?.userId || "",
      };

      const response = editingAssetCategory
        ? await editAssetCategory(submitData)
        : await addAssetCategory(submitData);

      if (response.success) {
        notifySuccess(
          editingAssetCategory ? "編輯成功" : "新增成功",
          `資產類別已成功${editingAssetCategory ? "編輯" : "新增"}`
        );
        setIsModalVisible(false);
        form.resetFields();
        loadAssetCategories();
      } else {
        notifyError(
          editingAssetCategory ? "編輯失敗" : "新增失敗",
          response.message
        );
      }
    } catch (error) {
      notifyError(editingAssetCategory ? "編輯失敗" : "新增失敗", "請稍後再試");
    }
  };

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredAssetCategories}
        renderItem={(assetCategory) => (
          <List.Item
            key={assetCategory.assetCategoryId}
            actions={[
              <Button
                type="link"
                key="edit"
                icon={<EditOutlined />}
                onClick={() => handleEdit(assetCategory)}
              >
                編輯
              </Button> /* ,
              <Button
                type="link"
                danger
                key="delete"
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(assetCategory)}
              >
                刪除
              </Button>, */,
            ]}
          >
            <List.Item.Meta
              title={
                <Space>
                  <Text strong>{assetCategory.assetCategoryId}</Text>
                  <Text type="secondary">
                    ({assetCategory.assetCategoryName})
                  </Text>
                </Space>
              }
              /*               description={
                <Space direction="vertical" size={2}>
                  <Text type="secondary">排序：{assetCategory.sortCode}</Text>
                </Space>
              } */
            />
          </List.Item>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
        }}
      />
    );
  };

  return (
    <Card title="財產類別管理">
      <Space style={{ marginBottom: 16 }} wrap>
        {/* <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增財產類別
        </Button> */}
        <Dropdown menu={filterMenu} trigger={["click"]}>
          <Button icon={<FilterOutlined />}>新增篩選條件</Button>
        </Dropdown>
        {activeFilters.map((filterKey) => (
          <Space key={filterKey} style={{ marginRight: 8 }}>
            {renderFilterControl(filterKey)}
          </Space>
        ))}
        {/* 搜尋 */}
        <Input
          placeholder="搜尋類別代碼、類別名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: isMobile ? "100%" : 260 }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredAssetCategories}
          rowKey="assetCategoryId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingAssetCategory ? "編輯財產類別" : "新增財產類別"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={600}
        okText="確認"
        cancelText="取消"
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="assetCategoryId"
            label="類別代碼"
            rules={[{ required: true, message: "請輸入類別代碼" }]}
          >
            <Input
              placeholder="請輸入類別代碼"
              disabled={!!editingAssetCategory}
              maxLength={1}
            />
          </Form.Item>

          <Form.Item
            name="assetCategoryName"
            label="類別名稱"
            rules={[{ required: true, message: "請輸入類別名稱" }]}
          >
            <Input placeholder="請輸入類別名稱" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 刪除確認 Modal */}
      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setAssetCategoryToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled:
            deleteConfirmText !==
            (assetCategoryToDelete?.assetCategoryName || ""),
        }}
      >
        <div>
          <p>
            請輸入
            <strong>「{assetCategoryToDelete?.assetCategoryName}」</strong>
            以確認刪除：
          </p>
          <Input
            placeholder="請輸入類別名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default AssetCategoryPage;
