using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IManufacturerService
    {
        /// <summary>
        /// 取得製造商資料
        /// </summary>
        /// <param name="_manufacturerId">製造商編號</param>
        /// <returns>製造商資料列表</returns>
        Task<List<ManufacturerDTO>> GetManufacturerAsync(string _manufacturerId = "");

        /// <summary>
        /// 新增製造商資料
        /// </summary>
        /// <param name="_data">製造商資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddManufacturerAsync(ManufacturerDTO _data);

        /// <summary>
        /// 編輯製造商資料
        /// </summary>
        /// <param name="_data">製造商資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditManufacturerAsync(ManufacturerDTO _data);

        /// <summary>
        /// 刪除製造商資料
        /// </summary>
        /// <param name="_data">製造商資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteManufacturerAsync(ManufacturerDTO _data);

        /// <summary>
        /// 取得製造商詳細資料
        /// </summary>
        /// <param name="_manufacturerId">製造商編號</param>
        /// <returns>製造商詳細資料</returns>
        Task<ManufacturerDTO> GetManufacturerDetailAsync(string _manufacturerId);
    }
}