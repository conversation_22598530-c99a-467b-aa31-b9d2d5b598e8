﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 考試資料表
    /// </summary>
    public class Examination : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 使用者編號

        [Comment("考試名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string examName { get; set; } // 考試名稱

        [Comment("種類科別")]
        [Column(TypeName = "nvarchar(100)")]
        public string? examType { get; set; } // 種類科別

        [Comment("錄取等第")]
        [Column(TypeName = "nvarchar(100)")]
        public string? admittanceGrade { get; set; } // 錄取等第

        [Comment("考試機關")]
        [Column(TypeName = "nvarchar(100)")]
        public string? examInstitution { get; set; } // 考試機關

        [Comment("考試起日")]
        [Column(TypeName = "bigint")]
        public long? examStartDate { get; set; } // 考試起日

        [Comment("考試迄日")]
        [Column(TypeName = "bigint")]
        public long? examEndDate { get; set; } // 考試迄日

        [Comment("發證日期")]
        [Column(TypeName = "bigint")]
        public long? certificateDate { get; set; } // 發證日期

        [Comment("證書文號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? certificateNumber { get; set; } // 證書文號

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? remark { get; set; } // 備註

        public Examination()
        {
            uid = "";
            userId = "";
            examName = "";
            examType = "";
            admittanceGrade = "";
            examInstitution = "";
            examStartDate = null;
            examEndDate = null;
            certificateDate = null;
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 考試資料 DTO
    /// </summary>
    public class ExaminationDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string examName { get; set; } // 考試名稱
        public string examType { get; set; } // 種類科別
        public string admittanceGrade { get; set; } // 錄取等第
        public string examInstitution { get; set; } // 考試機關
        public string examStartDate { get; set; } // 考試起日（timestamp）
        public string examEndDate { get; set; } // 考試迄日（timestamp）
        public string certificateDate { get; set; } // 發證日期（timestamp）
        public string certificateNumber { get; set; } // 證書文號
        public string remark { get; set; } // 備註

        public ExaminationDTO()
        {
            uid = "";
            userId = "";
            examName = "";
            examType = "";
            admittanceGrade = "";
            examInstitution = "";
            examStartDate = "";
            examEndDate = "";
            certificateDate = "";
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

