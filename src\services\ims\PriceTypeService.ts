import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";

// 價格類型介面 - 根據實際API回應更新
export interface PriceType {
    priceTypeID: string;
    name: string;
    description: string;
    sortCode: number;
    allowStop: boolean;
    isStop: boolean;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

export const createEmptyPriceType = (): PriceType => ({
    priceTypeID: '',
    name: '',
    description: '',
    sortCode: 0,
    allowStop: true,
    isStop: false,
    createTime: null,
    createUserId: null,
    updateTime: null,
    updateUserId: null,
    deleteTime: null,
    deleteUserId: null,
    isDeleted: false,
});

// 取得價格類型列表
export async function getPriceTypeList(): Promise<ApiResponse<PriceType[]>> {
    try {
        console.log('🔄 PriceTypeService: 開始載入價格類型列表...');
        const response = await httpClient(apiEndpoints.getPriceTypeList, {
            method: "GET",
        });

        console.log(`✅ PriceTypeService: API回應完成`, response);
        return response;
    } catch (error: any) {
        console.error('❌ PriceTypeService: 載入價格類型列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得價格類型列表失敗",
            data: [],
        };
    }
}

// 取得單一價格類型
export async function getPriceType(priceTypeId: string): Promise<ApiResponse<PriceType[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getPriceType}?PriceTypeID=${priceTypeId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得價格類型失敗",
            data: [],
        };
    }
}

// 新增價格類型
export async function addPriceType(priceType: Partial<PriceType>): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addPriceType, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(priceType),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增價格類型失敗",
        };
    }
}

// 修改價格類型
export async function editPriceType(priceType: Partial<PriceType>): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editPriceType, {
            method: "PATCH",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(priceType),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "修改價格類型失敗",
        };
    }
}

// 刪除價格類型
export async function deletePriceType(priceTypeId: string): Promise<ApiResponse<any>> {
    try {
        const priceType = { priceTypeID: priceTypeId };
        const response = await httpClient(apiEndpoints.deletePriceType, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(priceType),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除價格類型失敗",
        };
    }
}
