using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IEnsureService
    {
        /// <summary>
        /// 取得所有保證書資料列表
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <returns>保證書資料列表</returns>
        Task<List<EnsureDTO>> GetEnsureListAsync(string userId);

        /// <summary>
        /// 取得保證書資料明細
        /// </summary>
        /// <param name="uid">資料編號</param>
        /// <returns>保證書資料明細</returns>
        Task<EnsureDTO> GetEnsureDetailAsync(string uid);

        /// <summary>
        /// 新增保證書資料
        /// </summary>
        /// <param name="data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddEnsureAsync(EnsureDTO data);

        /// <summary>
        /// 編輯保證書資料
        /// </summary>
        /// <param name="data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditEnsureAsync(EnsureDTO data);

        /// <summary>
        /// 刪除保證書資料
        /// </summary>
        /// <param name="uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteEnsureAsync(string uid);
    }
}
