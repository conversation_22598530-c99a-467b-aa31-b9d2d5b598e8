using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("設備類型管理")]
    public class EquipmentTypeController : ControllerBase
    {
        private readonly IEquipmentTypeService _Interface;

        public EquipmentTypeController(IEquipmentTypeService equipmentTypeService)
        {
            _Interface = equipmentTypeService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得設備類型列表", Description = "取得所有設備類型列表")]
        public async Task<IActionResult> GetEquipmentTypeList()
        {
            var types = await _Interface.GetEquipmentTypeAsync();
            return Ok(types);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得設備類型明細", Description = "依ID取得設備類型明細")]
        public async Task<IActionResult> GetEquipmentTypeDetail(string id)
        {
            var type = await _Interface.GetEquipmentTypeDetailAsync(id);
            return Ok(type);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增設備類型", Description = "新增設備類型資料")]
        public async Task<IActionResult> AddEquipmentType([FromBody] EquipmentTypeDTO _data)
        {
            var (result, msg) = await _Interface.AddEquipmentTypeAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯設備類型", Description = "修改已存在之設備類型資料")]
        public async Task<IActionResult> EditEquipmentType([FromBody] EquipmentTypeDTO _data)
        {
            var (result, msg) = await _Interface.EditEquipmentTypeAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除設備類型", Description = "刪除已存在之設備類型資料")]
        public async Task<IActionResult> DeleteEquipmentType([FromBody] EquipmentTypeDTO _data)
        {
            var (result, msg) = await _Interface.DeleteEquipmentTypeAsync(_data);
            return Ok(new { result, msg });
        }
    }
}