"use client";

/*主佈局
/app/components/layout/MainLayout.tsx
*/
import React, { useEffect, useState } from "react";
import { Layout } from "antd";
import { useSiderStore } from "@/store/siderStore";
import Header from "./Header";
import Content from "./Content";
import Footer from "./Footer";
import Sider from "./Sider";
import { Inter } from "next/font/google";

import {
  createSignalRConnection,
  stopSignalRConnection,
} from "@/services/common/signalRService";
import GlobalNotification from "@/app/components/common/GlobalNotification";

const MainLayout = ({ children }: { children: React.ReactNode }) => {
  const { collapsed, setCollapsed } = useSiderStore();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, [setCollapsed]);

  useEffect(() => {
    // 建立 SignalR 連線
    createSignalRConnection();

    const handleSignalRUnload = () => {
      stopSignalRConnection();
    };

    // 當頁面關閉或重新整理時，停止 SignalR 連線
    window.addEventListener("beforeunload", handleSignalRUnload);

    return () => {
      window.removeEventListener("beforeunload", handleSignalRUnload);
    };
  }, []);

  return (
    <>
      {/* 在最外層加入 GlobalNotification，讓所有頁面都能接收廣播通知 */}
      <GlobalNotification />
      <Layout style={{ minHeight: "100vh", overflow: "hidden" }}>
        <Sider isMobile={isMobile} />
        <Layout
          style={{
            marginLeft: isMobile ? 0 : collapsed ? 80 : 240,
            transition: "margin-left 0.2s",
            minHeight: "100vh",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Header />
          <Content>{children}</Content>
          <Footer />
        </Layout>
      </Layout>
    </>
  );
};

export default MainLayout;
