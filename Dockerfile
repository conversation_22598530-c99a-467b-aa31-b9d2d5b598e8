# 使用官方 .NET 8 SDK 建置應用程式
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# 複製 csproj 並還原 NuGet 套件
COPY *.csproj ./
RUN dotnet restore

# 複製專案檔案並建置應用程式
COPY . ./
RUN dotnet publish -c Release -o /out

# 使用 .NET 8 執行環境作為最終映像
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /out .

# 設定環境變數
ENV ASPNETCORE_URLS=https://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# 設定應用程式啟動
ENTRYPOINT ["dotnet", "FAST_ERP_Backend.dll"]

# 開放應用程式埠口
EXPOSE 80