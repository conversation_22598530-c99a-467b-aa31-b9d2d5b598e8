# Partner API 測試文件
# 用於驗證 Partner 新增功能修復

### 測試環境設定
@baseUrl = http://localhost:7136
@contentType = application/json

### 1. 測試新增個人客戶夥伴
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "partnerName": "陳小明",
  "partnerType": "Individual",
  "individualDetail": {
    "lastName": "陳",
    "firstName": "小明",
    "birthDate": "1990-05-15T00:00:00.000Z",
    "gender": "Male",
    "idNumber": "A123456789"
  },
  "customerDetail": {
    "customerLevel": "VIP",
    "settlementDay": 15,
    "creditLimit": 100000,
    "paymentTerms": "Net30"
  }
}

### 2. 測試新增組織供應商夥伴
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "partnerName": "ABC科技有限公司",
  "partnerType": "Organization",
  "organizationDetail": {
    "companyName": "ABC科技有限公司",
    "businessRegistrationNumber": "12345678",
    "taxId": "87654321",
    "establishedDate": "2010-01-01T00:00:00.000Z",
    "legalRepresentative": "王大華"
  },
  "supplierDetail": {
    "supplierLevel": "A級",
    "settlementDay": 31,
    "paymentTerms": "Net45",
    "qualityRating": 95
  }
}

### 3. 測試新增個人客戶+供應商夥伴
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "partnerName": "李小華",
  "partnerType": "Individual",
  "individualDetail": {
    "lastName": "李",
    "firstName": "小華",
    "birthDate": "1985-03-20T00:00:00.000Z",
    "gender": "Female",
    "idNumber": "B987654321"
  },
  "customerDetail": {
    "customerLevel": "一般",
    "settlementDay": 10,
    "creditLimit": 50000,
    "paymentTerms": "Net15"
  },
  "supplierDetail": {
    "supplierLevel": "B級",
    "settlementDay": 25,
    "paymentTerms": "Net30",
    "qualityRating": 88
  }
}

### 4. 測試新增組織客戶夥伴（測試結算日警告）
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "partnerName": "XYZ貿易股份有限公司",
  "partnerType": "Organization",
  "organizationDetail": {
    "companyName": "XYZ貿易股份有限公司",
    "businessRegistrationNumber": "98765432",
    "taxId": "13579246",
    "establishedDate": "2015-06-01T00:00:00.000Z",
    "legalRepresentative": "張三豐"
  },
  "customerDetail": {
    "customerLevel": "VIP",
    "settlementDay": 29,
    "creditLimit": 200000,
    "paymentTerms": "Net60"
  }
}

### 5. 獲取所有夥伴列表（驗證新增成功）
GET {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

### 6. 測試錯誤情況 - 缺少必要欄位
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "partnerName": "",
  "partnerType": "Individual"
}

### 7. 測試錯誤情況 - 無效的夥伴類型
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "partnerName": "測試夥伴",
  "partnerType": "InvalidType",
  "individualDetail": {
    "lastName": "測試",
    "firstName": "夥伴"
  }
}

### 測試說明
# 1. 確保後端容器正在運行 (端口 7136)
# 2. 如果需要 JWT 驗證，請先獲取 token 並添加到 Authorization header
# 3. 測試 1-4 應該返回 200 OK 並包含新建的夥伴資料
# 4. 測試 5 應該返回包含新建夥伴的列表
# 5. 測試 6-7 應該返回 400 Bad Request 並包含驗證錯誤訊息

### 預期結果驗證
# ✅ 新增個人夥伴成功，individualDetail.partnerID 由後端自動生成
# ✅ 新增組織夥伴成功，organizationDetail.partnerID 由後端自動生成
# ✅ 客戶和供應商詳細資料正確關聯到夥伴
# ✅ 結算日設定正確保存（包括 29、31 日等特殊情況）
# ✅ 錯誤情況正確返回驗證錯誤訊息
