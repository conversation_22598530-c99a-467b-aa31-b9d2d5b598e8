using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("薪點金額管理")]
    public class SalaryPointController : ControllerBase
    {
        private readonly ISalaryPointService _interface;

        public SalaryPointController(ISalaryPointService service)
        {
            _interface = service;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得所有薪點資料", Description = "取得所有薪點金額記錄")]
        public async Task<IActionResult> GetSalaryPointList()
        {
            var result = await _interface.GetListAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得薪點明細", Description = "依 uid 取得單筆薪點金額資料")]
        public async Task<IActionResult> GetSalaryPointDetail(string uid)
        {
            var result = await _interface.GetDetailAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增薪點金額", Description = "新增薪點金額資料")]
        public async Task<IActionResult> AddSalaryPoint([FromBody] SalaryPointDTO data)
        {
            var (result, msg) = await _interface.AddAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯薪點金額", Description = "編輯薪點金額資料")]
        public async Task<IActionResult> EditSalaryPoint([FromBody] SalaryPointDTO data)
        {
            var (result, msg) = await _interface.EditAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除薪點金額", Description = "刪除薪點金額資料")]
        public async Task<IActionResult> DeleteSalaryPoint([FromBody] string uid)
        {
            var (result, msg) = await _interface.DeleteAsync(uid);
            return Ok(new { result, msg });
        }

        [HttpGet("GetAmountByDate")]
        [SwaggerOperation(Summary = "查詢指定日期的薪點金額", Description = "取得指定日期當下生效的唯一薪點金額")]
        public async Task<IActionResult> GetAmountByDate(string dateStr)
        {
            var result = await _interface.GetAmountByDateAsync(dateStr);
            return Ok(result);
        }

    }
}
