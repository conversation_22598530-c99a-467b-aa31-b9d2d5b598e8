/**
 * 通用API測試工具
 * 用於驗證前端-後端資料流程的正確性
 * 重構為更通用、可重用的工具函數
 */

// 通用類型定義
type ApiFunction = () => Promise<any>;
type ApiTestConfig = {
  func: ApiFunction;
  name: string;
  type: string;
  expectedFields?: string[];
  validator?: (data: any) => { isValid: boolean; errors: string[] };
};

// API測試結果介面
interface ApiTestResult {
  endpoint: string;
  success: boolean;
  message: string;
  dataType: string;
  dataCount: number;
  responseTime: number;
  rawResponse?: any;
  validationErrors?: string[];
}

// 通用API端點測試函數
export const testApiEndpoint = async (
  config: ApiTestConfig
): Promise<ApiTestResult> => {
  const { func: apiFunction, name: endpointName, type: expectedDataType, expectedFields, validator } = config;
  const startTime = Date.now();

  try {
    console.log(`🧪 測試 ${endpointName}...`);

    const response = await apiFunction();
    const responseTime = Date.now() - startTime;

    console.log(`📊 ${endpointName} 回應:`, response);

    // 基本格式驗證
    const validationErrors: string[] = [];

    if (!response || typeof response !== 'object') {
      validationErrors.push('回應不是物件格式');
    } else {
      // 檢查必要欄位
      if (typeof response.success !== 'boolean') {
        validationErrors.push('缺少或無效的 success 欄位');
      }

      if (typeof response.message !== 'string' && response.message !== undefined) {
        validationErrors.push('message 欄位格式錯誤');
      }

      // 檢查資料欄位
      if (response.success) {
        if (response.data === undefined) {
          validationErrors.push('成功回應缺少 data 欄位');
        } else if (!Array.isArray(response.data)) {
          validationErrors.push('data 欄位不是陣列格式');
        }
      }

      // 使用自定義驗證器
      if (validator && response.success && response.data) {
        const customValidation = validator(response.data);
        if (!customValidation.isValid) {
          validationErrors.push(...customValidation.errors);
        }
      }

      // 檢查預期欄位
      if (expectedFields && response.success && Array.isArray(response.data) && response.data.length > 0) {
        const firstItem = response.data[0];
        expectedFields.forEach(field => {
          if (!(field in firstItem)) {
            validationErrors.push(`資料項目缺少預期欄位: ${field}`);
          }
        });
      }
    }

    const dataCount = Array.isArray(response.data) ? response.data.length : 0;

    return {
      endpoint: endpointName,
      success: response.success && validationErrors.length === 0,
      message: validationErrors.length > 0
        ? `驗證失敗: ${validationErrors.join(', ')}`
        : response.message || '成功',
      dataType: expectedDataType,
      dataCount,
      responseTime,
      rawResponse: response,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined
    };

  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    console.error(`❌ ${endpointName} 測試失敗:`, error);

    return {
      endpoint: endpointName,
      success: false,
      message: `請求失敗: ${error.message || '未知錯誤'}`,
      dataType: expectedDataType,
      dataCount: 0,
      responseTime,
      validationErrors: [`請求異常: ${error.message || '未知錯誤'}`]
    };
  }
};

// 通用批量API測試函數
export const testMultipleApis = async (
  testConfigs: ApiTestConfig[],
  options: {
    parallel?: boolean;
    includeDetailTest?: boolean;
    detailTestConfig?: {
      listEndpointName: string;
      detailApiFunction: (id: string) => Promise<any>;
      detailEndpointName: string;
      idField: string;
    };
  } = {}
): Promise<{
  success: boolean;
  results: ApiTestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    totalResponseTime: number;
  };
}> => {
  const { parallel = true, includeDetailTest = false, detailTestConfig } = options;

  console.log('🚀 開始批量API測試...');

  const results: ApiTestResult[] = [];

  // 執行主要API測試
  if (parallel) {
    // 並行測試所有API
    const testPromises = testConfigs.map(config => testApiEndpoint(config));
    const testResults = await Promise.all(testPromises);
    results.push(...testResults);
  } else {
    // 順序測試所有API
    for (const config of testConfigs) {
      const result = await testApiEndpoint(config);
      results.push(result);
    }
  }

  // 如果需要，執行詳細測試
  if (includeDetailTest && detailTestConfig) {
    const { listEndpointName, detailApiFunction, detailEndpointName, idField } = detailTestConfig;
    const listResult = results.find(r => r.endpoint === listEndpointName);

    if (listResult?.success && listResult.dataCount > 0 && listResult.rawResponse?.data?.[0]?.[idField]) {
      const firstItemId = listResult.rawResponse.data[0][idField];
      console.log(`🧪 測試詳細查詢 (ID: ${firstItemId})...`);

      const detailResult = await testApiEndpoint({
        func: () => detailApiFunction(firstItemId),
        name: detailEndpointName,
        type: '詳細資料'
      });
      results.push(detailResult);
    }
  }

  // 計算摘要
  const summary = {
    total: results.length,
    passed: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    totalResponseTime: results.reduce((sum, r) => sum + r.responseTime, 0)
  };
  
  const overallSuccess = summary.failed === 0;
  
  console.log('📋 API測試摘要:', summary);
  console.log('📊 詳細結果:', results);
  
  if (overallSuccess) {
    console.log('✅ 所有API測試通過！');
  } else {
    console.log('❌ 部分API測試失敗，請檢查詳細結果');
  }
  
  return {
    success: overallSuccess,
    results,
    summary
  };
};

// 通用資料驗證函數
export const validateApiData = (
  sampleData: any,
  dataType: string,
  requiredFields: string[] = [],
  optionalFields: string[] = [],
  customValidator?: (item: any, index: number) => { errors: string[]; warnings: string[] }
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  console.log(`🔍 測試${dataType}資料驗證...`, sampleData);

  // 基本格式檢查
  if (!sampleData) {
    errors.push('資料為空');
    return { isValid: false, errors, warnings };
  }

  if (!Array.isArray(sampleData)) {
    errors.push('資料不是陣列格式');
    return { isValid: false, errors, warnings };
  }

  if (sampleData.length === 0) {
    warnings.push('資料陣列為空');
  }

  // 檢查陣列項目
  sampleData.forEach((item, index) => {
    if (!item || typeof item !== 'object') {
      errors.push(`項目 ${index} 不是有效物件`);
      return;
    }

    // 檢查必要欄位
    requiredFields.forEach(field => {
      if (!item[field]) {
        errors.push(`項目 ${index} 缺少必要欄位: ${field}`);
      }
    });

    // 檢查可選欄位（如果缺少則警告）
    optionalFields.forEach(field => {
      if (!item[field]) {
        warnings.push(`項目 ${index} 缺少可選欄位: ${field}`);
      }
    });

    // 使用自定義驗證器
    if (customValidator) {
      const customResult = customValidator(item, index);
      errors.push(...customResult.errors);
      warnings.push(...customResult.warnings);
    }
  });

  const isValid = errors.length === 0;

  console.log(`${isValid ? '✅' : '❌'} ${dataType}資料驗證${isValid ? '通過' : '失敗'}`, {
    errors,
    warnings,
    itemCount: sampleData.length
  });

  return { isValid, errors, warnings };
};

// 創建預設驗證器
export const createDefaultValidators = () => ({
  商品: (item: any, index: number) => ({
    errors: [
      ...(!item.ItemID ? [`項目 ${index} 缺少 ItemID`] : []),
    ],
    warnings: [
      ...(!item.Name ? [`項目 ${index} 缺少 Name`] : []),
      ...(!item.CustomNO ? [`項目 ${index} 缺少 CustomNO`] : []),
    ]
  }),
  分類: (item: any, index: number) => ({
    errors: [
      ...(!item.ItemCategoryID ? [`項目 ${index} 缺少 ItemCategoryID`] : []),
    ],
    warnings: [
      ...(!item.Name ? [`項目 ${index} 缺少 Name`] : []),
    ]
  }),
  價格類型: (item: any, index: number) => ({
    errors: [
      ...(!item.PriceTypeID ? [`項目 ${index} 缺少 PriceTypeID`] : []),
    ],
    warnings: [
      ...(!item.Name ? [`項目 ${index} 缺少 Name`] : []),
    ]
  }),
  商品價格: (item: any, index: number) => ({
    errors: [
      ...(!item.ItemPriceID ? [`項目 ${index} 缺少 ItemPriceID`] : []),
      ...(!item.ItemID ? [`項目 ${index} 缺少 ItemID`] : []),
      ...(!item.PriceTypeID ? [`項目 ${index} 缺少 PriceTypeID`] : []),
    ],
    warnings: []
  })
});

// 工具函數：創建測試配置
export const createTestConfig = (
  func: ApiFunction,
  name: string,
  type: string,
  expectedFields?: string[],
  validator?: (data: any) => { isValid: boolean; errors: string[] }
): ApiTestConfig => ({
  func,
  name,
  type,
  expectedFields,
  validator
});

// 在開發環境中自動測試
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // 將測試函數掛載到全域物件，方便在控制台中呼叫
  (window as any).testMultipleApis = testMultipleApis;
  (window as any).testApiEndpoint = testApiEndpoint;
  (window as any).validateApiData = validateApiData;
  (window as any).createTestConfig = createTestConfig;
  (window as any).createDefaultValidators = createDefaultValidators;

  console.log('🛠️ 開發模式：通用API測試工具已載入');
  console.log('💡 在控制台中執行 testMultipleApis(configs) 來測試多個API');
  console.log('💡 在控制台中執行 testApiEndpoint(config) 來測試單個API');
  console.log('💡 在控制台中執行 validateApiData(data, type, fields) 來測試資料驗證');
}
