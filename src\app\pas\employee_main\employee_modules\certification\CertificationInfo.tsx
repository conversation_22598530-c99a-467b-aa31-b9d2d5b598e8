import { useEffect, useState } from 'react';
import { Modal, Button, Spin, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Typography, Space, Divider } from 'antd';
import dayjs from 'dayjs';
import {
    getCertificationDetail,
    getCertificationList,
    addCertification,
    editCertification,
    deleteCertification,
    Certification,
    createEmptyCertification,
} from '@/services/pas/CertificationService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    SafetyCertificateOutlined,
    CalendarOutlined,
    FileTextOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    PlusOutlined,
    BankOutlined,
    FileProtectOutlined
} from '@ant-design/icons';

import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type CertificationInfoProps = {
    userId: string;
    active: boolean;
};

const CertificationInfo: React.FC<CertificationInfoProps> = ({ userId, active }) => {
    const [list, setList] = useState<Certification[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [detail, setDetail] = useState<Certification | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) {
            fetchList();
        }
    }, [active, userId]);

    const fetchList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const { success, data, message: msg } = await getCertificationList(userId);
            if (success && data) {
                setList(data);
            } else {
                message.error(msg || '載入資料失敗');
            }
        } catch (error: any) {
            setErrorMsg(error.message || '未知錯誤');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getCertificationDetail(uid);
            if (success && data) {
                setDetail(data);
                form.resetFields();
                form.setFieldsValue({
                    ...createEmptyCertification(),
                    certificateName: data.certificateName,
                    certificateYearMonth: data.certificateYearMonth ? dayjs(data.certificateYearMonth) : null,
                    certificateInstitution: data.certificateInstitution,
                    certificateDate: data.certificateDate ? dayjs(data.certificateDate) : null,
                    certificateNumber: data.certificateNumber,
                    remark: data.remark,
                });
                setIsModalOpen(true);
            } else {
                message.error(msg || '載入檢覈資料失敗');
            }
        } catch (error: any) {
            message.error(error.message || '載入錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleAddNew = () => {
        setDetail(null);
        form.resetFields();
        form.setFieldsValue(createEmptyCertification());
        setIsModalOpen(true);
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            setModalLoading(true);
            const payload = {
                ...values,
                userId,
                certificateYearMonth: values.certificateYearMonth ? values.certificateYearMonth.format('YYYY-MM') : '',
                certificateDate: values.certificateDate ? values.certificateDate.format('YYYY-MM-DD') : '',
            };

            let res;
            if (detail) {
                res = await editCertification({ ...payload, uid: detail.uid });
            } else {
                res = await addCertification(payload);
            }

            if (res.success && res.data?.result) {
                message.success(detail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchList();
            } else {
                message.error(res.data?.msg || res.message || '儲存失敗');
            }
        } catch (error: any) {
            if (!error?.errorFields) {
                message.error(error.message || '儲存失敗');
            }
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteCertification(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchList();
            } else {
                message.error(res.data?.msg || '刪除失敗');
            }
        } catch (error: any) {
            message.error(error.message || '刪除錯誤');
        }
    };

    if (!active) return null;
    if (errorMsg) return <div style={{ color: 'red', textAlign: 'center', padding: 20 }}>錯誤：{errorMsg}</div>;

    return (
        <>
            <Card
                title={
                    <Space>
                        <SafetyCertificateOutlined />
                        <Title level={4} style={{ margin: 0 }}>檢覈資料</Title>
                    </Space>
                }
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{ borderRadius: '6px' }}
                    >
                        新增檢覈資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={list}
                    columns={[
                        {
                            title: '檢覈名稱',
                            dataIndex: 'certificateName',
                            render: (text) => (
                                <Space>
                                    <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '檢覈年月',
                            dataIndex: 'certificateYearMonth',
                            render: (text) => (
                                <Space>
                                    <CalendarOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text ? dayjs(text).format('YYYY-MM') : '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '檢覈機構',
                            dataIndex: 'certificateInstitution',
                            render: (text) => (
                                <Space>
                                    <BankOutlined style={{ color: '#722ed1' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '發證日期',
                            dataIndex: 'certificateDate',
                            render: (text) => (
                                <Space>
                                    <FileProtectOutlined style={{ color: '#eb2f96' }} />
                                    <Text>{text ? dayjs(text).format('YYYY-MM-DD') : '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '證書文號',
                            dataIndex: 'certificateNumber',
                            render: (text) => (
                                <Space>
                                    <FileTextOutlined style={{ color: '#fa8c16' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            ),
                        },
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space>
                                    <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>備註：</Text>
                                    <Text>{record.remark || '-'}</Text>
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!record.remark,
                    }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                    })}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    pagination={{ pageSize: 10 }}
                />
            </Card>

            <Modal
                title={
                    <Space>
                        <SafetyCertificateOutlined />
                        <Title level={5} style={{ margin: 0 }}>
                            {detail ? '編輯檢覈資料' : '新增檢覈資料'}
                        </Title>
                    </Space>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                onOk={handleModalOk}
                confirmLoading={modalLoading}
                width={800}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form form={form} layout="vertical" className="mt-4">
                    {/* 檢覈資訊 */}
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <SafetyCertificateOutlined />
                                檢覈資訊
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24}>
                                <Form.Item
                                    label={<Text strong>檢覈名稱</Text>}
                                    name="certificateName"
                                    rules={[{ required: true, message: '請輸入檢覈名稱' }]}
                                >
                                    <Input
                                        placeholder="請輸入檢覈名稱"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>檢覈年月</Text>}
                                    name="certificateYearMonth"
                                >
                                    <DatePicker
                                        picker="month"
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        placeholder="請選擇年月"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>檢覈機構</Text>}
                                    name="certificateInstitution"
                                >
                                    <Input
                                        placeholder="請輸入檢覈機構"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 證書資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <CalendarOutlined />
                                證書資訊
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>發證日期</Text>}
                                    name="certificateDate"
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        placeholder="請選擇發證日期"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>證書文號</Text>}
                                    name="certificateNumber"
                                >
                                    <Input
                                        placeholder="請輸入證書文號"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 其他資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileTextOutlined />
                                其他資訊
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col span={24}>
                                <Form.Item
                                    label={<Text strong>備註</Text>}
                                    name="remark"
                                >
                                    <Input.TextArea
                                        rows={3}
                                        placeholder="請輸入備註"
                                        style={{ resize: 'none', borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default CertificationInfo;
