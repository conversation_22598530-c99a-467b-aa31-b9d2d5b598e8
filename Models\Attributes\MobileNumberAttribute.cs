using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Attributes
{
    /// <summary>
    /// 驗證台灣手機號碼格式的屬性
    /// 
    /// 基本使用
    /// [MobileNumber]
    /// public string MobileNumber { get; set; }
    /// 
    /// 只允許09開頭
    /// [MobileNumber(allow09Only: true)]
    /// public string MobileNumber { get; set; }
    /// 
    /// 有效的手機號碼格式範例：
    /// 0912345678
    /// 09123456789
    /// 091234567890
    /// 0912345678901
    /// 09123456789012
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class MobileNumberAttribute : ValidationAttribute
    {
        public MobileNumberAttribute() : base("手機號碼格式不正確")
        {
        }

        /// <summary>
        /// 驗證方法
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success; // 若要求必填，請搭配 [Required] 屬性
            }

            string phoneNumber = value.ToString();

            // 移除可能的空格和特殊符號
            phoneNumber = Regex.Replace(phoneNumber, @"[\s-]", "");

            // 台灣手機號碼格式：09開頭，共10位數字
            var regex = new Regex(@"^09\d{8}$");
            if (!regex.IsMatch(phoneNumber))
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }
}