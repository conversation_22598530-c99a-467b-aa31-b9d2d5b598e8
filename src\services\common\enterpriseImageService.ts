import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { getToken } from "@/utils/cookies";
import { ApiResponse } from "@/config/api";

// 企業圖片
export interface EnterpriseImage {
    imageId: string;
    imageName: string;
    imageType: string;
    imagePath: string;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
}

// 獲取公司圖片列表
export async function getEnterpriseImages(): Promise<ApiResponse<EnterpriseImage[]>> {
    try {
        const response = await httpClient(apiEndpoints.getEnterpriseImages, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取公司圖片列表失敗",
            data: []
        };
    }
}

// 獲取單個公司圖片
export async function getEnterpriseImage(id: string): Promise<ApiResponse<EnterpriseImage>> {
    try {
        const response = await httpClient(`${apiEndpoints.getEnterpriseImage}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取單個公司圖片失敗",
        };
    }
}

// 上傳公司圖片
export async function updateEnterpriseImage(formData: FormData): Promise<ApiResponse> {
    try {
        const response = await fetch(apiEndpoints.uploadEnterpriseImage, {
            method: "POST",
            headers: {
                'Authorization': `Bearer ${getToken()}`
            },
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            return {
                success: false,
                message: errorData.message || `上傳失敗: ${response.status}`,
            };
        }

        const data = await response.json();
        console.log("Upload response:", data);

        return {
            success: data.success,
            message: data.message,
            data: data.data
        };
    } catch (error: any) {
        console.error("Upload error:", error);
        return {
            success: false,
            message: error.message || "上傳公司圖片失敗",
        };
    }
}

// 刪除公司圖片
export async function deleteEnterpriseImage(data: Partial<EnterpriseImage>): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.deleteEnterpriseImage}`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除公司資訊失敗",
        };
    }
}

