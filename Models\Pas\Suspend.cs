﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 留職停薪資料表
    /// </summary>
    public class Suspend : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 使用者編號

        [Comment("留停類型")]
        [Column(TypeName = "nvarchar(3)")]
        public string suspendType { get; set; } // 留停類型

        [Comment("留停種類")]
        [Column(TypeName = "nvarchar(3)")]
        public string suspendKind { get; set; } // 留停種類

        [Comment("留停原因")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? suspendReason { get; set; } // 留停原因

        [Comment("留停日期")]
        [Column(TypeName = "bigint")]
        public long? suspendDate { get; set; } // 留停日期

        [Comment("核准日期")]
        [Column(TypeName = "bigint")]
        public long? approveDate { get; set; } // 核准日期

        [Comment("核准文號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? approveNumber { get; set; } // 核准文號

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? remark { get; set; } // 備註

        public Suspend()
        {
            uid = "";
            userId = "";
            suspendType = "";
            suspendKind = "";
            suspendReason = "";
            suspendDate = null;
            approveDate = null;
            approveNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 留職停薪資料 DTO
    /// </summary>
    public class SuspendDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string suspendType { get; set; } // 留停類型
        public string? suspendTypeName { get; set; } // 留停類型名稱
        public string suspendKind { get; set; } // 留停種類
        public string? suspendKindName { get; set; } // 留停種類名稱
        public string suspendReason { get; set; } // 留停原因
        public string suspendDate { get; set; } // 留停日期（timestamp）
        public string approveDate { get; set; } // 核准日期（timestamp）
        public string approveNumber { get; set; } // 核准文號
        public string remark { get; set; } // 備註

        public SuspendDTO()
        {
            uid = "";
            userId = "";
            suspendType = "";
            suspendKind = "";
            suspendReason = "";
            suspendDate = "";
            approveDate = "";
            approveNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

