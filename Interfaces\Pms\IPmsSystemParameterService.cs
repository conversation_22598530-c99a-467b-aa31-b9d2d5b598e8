using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pms.Enums;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IPmsSystemParameterService
    {
        /// <summary>
        /// 取得所有系統參數
        /// </summary>
        /// <returns>系統參數列表</returns>
        Task<List<PmsSystemParameterDTO>> GetAllParametersAsync();

        /// <summary>
        /// 依照參數類型取得系統參數
        /// </summary>
        /// <param name="parameterType">參數類型</param>
        /// <returns>符合類型的系統參數列表</returns>
        Task<List<PmsSystemParameterDTO>> GetParametersByTypeAsync(PmsParameterType parameterType);

        /// <summary>
        /// 依照參數ID取得系統參數
        /// </summary>
        /// <param name="parameterId">參數ID</param>
        /// <returns>系統參數</returns>
        Task<PmsSystemParameterDTO> GetParameterByIdAsync(string parameterId);

        /// <summary>
        /// 新增系統參數
        /// </summary>
        /// <param name="parameter">系統參數資料</param>
        /// <returns>成功與否及訊息</returns>
        Task<(bool success, string message)> AddParameterAsync(PmsSystemParameterDTO parameter);

        /// <summary>
        /// 編輯系統參數
        /// </summary>
        /// <param name="parameter">系統參數資料</param>
        /// <returns>成功與否及訊息</returns>
        Task<(bool success, string message)> EditParameterAsync(PmsSystemParameterDTO parameter);

        /// <summary>
        /// 刪除系統參數
        /// </summary>
        /// <param name="parameter">系統參數資料</param>
        /// <returns>成功與否及訊息</returns>
        Task<(bool success, string message)> DeleteParameterAsync(PmsSystemParameterDTO parameter);

        /// <summary>
        /// 取得折舊法設定
        /// </summary>
        /// <returns>折舊法設定列表</returns>
        Task<List<PmsSystemParameterDTO>> GetDepreciationMethodsAsync();

        /// <summary>
        /// 設定默認折舊法
        /// </summary>
        /// <param name="methodId">折舊法參數ID</param>
        /// <returns>成功與否及訊息</returns>
        Task<(bool success, string message)> SetDefaultDepreciationMethodAsync(string methodId);

        /// <summary>
        /// 為特定財產科目設定餘額遞減法折舊率
        /// </summary>
        /// <param name="assetAccountId">財產科目ID</param>
        /// <param name="rate">折舊率</param>
        /// <param name="userId">操作用戶ID</param>
        /// <returns>成功與否及訊息</returns>
        Task<(bool success, string message)> SetDecliningBalanceRateForAssetAccountAsync(string assetAccountId, decimal rate, string userId);

        /// <summary>
        /// 取得特定財產科目的餘額遞減法折舊率
        /// </summary>
        /// <param name="assetAccountId">財產科目ID</param>
        /// <returns>折舊率設定</returns>
        Task<(bool success, decimal rate, string message)> GetDecliningBalanceRateForAssetAccountAsync(string assetAccountId);

        /// <summary>
        /// 取得所有財產科目的餘額遞減法折舊率設定
        /// </summary>
        /// <returns>折舊率設定列表</returns>
        Task<List<PmsSystemParameterDTO>> GetAllDecliningBalanceRatesAsync();

        /// <summary>
        /// 取得初始化狀態
        /// </summary>
        /// <returns>初始化狀態</returns>
        Task<(bool success, bool isInitialized, string message)> GetInitializationStatusAsync();

        /// <summary>
        /// 設定初始化狀態
        /// </summary>
        /// <param name="isInitialized">初始化狀態</param>
        /// <returns>成功與否及訊息</returns>
        Task<(bool success, string message)> SetInitializationStatusAsync(bool isInitialized);
    }
}