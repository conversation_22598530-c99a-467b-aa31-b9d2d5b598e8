﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Common
{
    public class PositionService : IPositionService
    {
        private readonly ERPDbContext _context;

        public PositionService(ERPDbContext context)
        {
            _context = context;
        }

        // 取得職稱資料
        public async Task<List<PositionDTO>> GetPositionAsync(string _positionId)
        {
            var query = _context.Common_Positions
                .Where(e => e.DeleteTime == null);

            // 如果 _positionId 不為空，則加上篩選條件
            if (!string.IsNullOrEmpty(_positionId))
            {
                query = query.Where(e => e.PositionId == _positionId);
            }

            var result = await query
                .OrderBy(e => e.SortCode)
                .Select(t => new PositionDTO
                {
                    PositionId = t.PositionId,
                    SortCode = t.SortCode,
                    Name = t.Name,
                })
                .ToListAsync();

            return result;
        }

        // 新增職稱資料
        public async Task<(bool, string)> AddPositionAsync(PositionDTO _data)
        {
            string uid = Guid.NewGuid().ToString().Trim();
            try
            {
                var newPosition = new Position
                {
                    PositionId = uid,
                    SortCode = _data.SortCode,
                    Name = _data.Name,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = "token-登錄者uid",
                };

                await _context.Database.BeginTransactionAsync();

                await _context.Common_Positions.AddAsync(newPosition);
                await _context.SaveChangesAsync();

                await _context.Database.CommitTransactionAsync();

                return (true, "新增職稱成功");
            }
            catch (Exception ex)
            {
                await _context.Database.RollbackTransactionAsync();
                return (false, $"新增職稱失敗: {ex.Message}");
            }
        }

        // 更新職稱資料
        public async Task<(bool, string)> EditPositionAsync(PositionDTO _data)
        {
            var existPosition = await _context.Common_Positions
                .FirstOrDefaultAsync(e => e.PositionId == _data.PositionId);

            if (existPosition != null)
            {
                try
                {
                    await _context.Database.BeginTransactionAsync();

                    existPosition.SortCode = _data.SortCode;
                    existPosition.Name = _data.Name;
                    existPosition.UpdateUserId = "token-更新者uid";
                    existPosition.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();

                    await _context.SaveChangesAsync();
                    await _context.Database.CommitTransactionAsync();

                    return (true, "更新職稱成功");
                }
                catch (Exception ex)
                {
                    await _context.Database.RollbackTransactionAsync();
                    return (false, $"更新職稱失敗: {ex.Message}");
                }
            }
            else
            {
                return (false, "找不到對應的職稱");
            }
        }

        // 刪除職稱資料
        public async Task<(bool, string)> DeletePositionAsync(PositionDTO _data)
        {
            var existPosition = await _context.Common_Positions
                .FirstOrDefaultAsync(e => e.PositionId == _data.PositionId);

            if (existPosition != null)
            {
                try
                {
                    await _context.Database.BeginTransactionAsync();

                    existPosition.DeleteUserId = "token-刪除者uid";
                    existPosition.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existPosition.IsDeleted = true;

                    await _context.SaveChangesAsync();
                    await _context.Database.CommitTransactionAsync();

                    return (true, "刪除職稱成功");
                }
                catch (Exception ex)
                {
                    await _context.Database.RollbackTransactionAsync();
                    return (false, $"刪除職稱失敗: {ex.Message}");
                }
            }
            else
            {
                return (false, "找不到對應的職稱");
            }
        }
    }
}
