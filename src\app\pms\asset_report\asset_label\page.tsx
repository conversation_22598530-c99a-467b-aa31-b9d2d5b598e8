"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  Col,
  message,
  DatePicker,
  Typography,
  Badge,
  Tag,
  Empty,
  Checkbox,
  InputNumber,
  Radio,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PrinterOutlined,
  EyeOutlined,
  TagOutlined,
} from "@ant-design/icons";
import { Grid } from "antd";
import dayjs from "dayjs";
import { QRCodeSVG } from "qrcode.react";
import Barcode from "react-barcode";
import { getAssets, AssetDetail, Asset } from "@/services/pms/assetService";
import { getAssetAccounts } from "@/services/pms/assetAccountService";
import { getAssetCategories } from "@/services/pms/assetCategoryService";
import { getDepartments } from "@/services/common/departmentService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import { getStorageLocations } from "@/services/pms/storageLocationService";
import { getUsers } from "@/services/common/userService";
import { siteConfig } from "@/config/site";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import ReportHeader, {
  getReportPrintStyles,
} from "@/app/components/common/ReportHeader";

const { Option } = Select;
const { RangePicker } = DatePicker;
const { useBreakpoint } = Grid;
const { Title, Text } = Typography;

// 查詢參數介面
interface AssetLabelQuery {
  keyword?: string;
  assetAccountId?: string;
  assetStatusId?: string;
  departmentId?: string;
  assetCategoryId?: string;
  storageLocationId?: string;
  acquisitionDateRange?: [string, string];
}

// 標籤設定介面
interface LabelSettings {
  barcodeType: "qrcode" | "barcode";
  showBarcode: boolean;
  showDepartment: boolean;
  showLocation: boolean;
  showAcquisitionDate: boolean;
  showCustodian: boolean;
  labelsPerRow: number;
  labelWidth: number;
  labelHeight: number;
}

// 主組件
const AssetLabelPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<Asset[]>([]);
  const [filteredData, setFilteredData] = useState<Asset[]>([]);
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);

  // 表單和選項
  const [searchForm] = Form.useForm();
  const [assetAccounts, setAssetAccounts] = useState<any[]>([]);
  const [assetCategories, setAssetCategories] = useState<any[]>([]);
  const [assetStatuses, setAssetStatuses] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [storageLocations, setStorageLocations] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);

  // 列印相關
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrintMode, setIsPrintMode] = useState(false);
  const [labelSettings, setLabelSettings] = useState<LabelSettings>({
    barcodeType: "qrcode",
    showBarcode: true,
    showDepartment: true,
    showLocation: true,
    showAcquisitionDate: false,
    showCustodian: true,
    labelsPerRow: 3,
    labelWidth: 8,
    labelHeight: 4,
  });

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 載入資產數據
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await getAssets();
      if (result.success && Array.isArray(result.data)) {
        // 從 AssetDetail 中提取 Asset
        const assets = result.data.map((item: AssetDetail) => item.asset);
        setData(assets);
        setFilteredData(assets);
      } else {
        message.error("載入資產數據失敗");
        setData([]);
        setFilteredData([]);
      }
    } catch (error) {
      console.error("載入資產數據錯誤:", error);
      message.error("載入資產數據失敗");
      setData([]);
      setFilteredData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 載入選項數據
  const loadOptions = useCallback(async () => {
    try {
      const [
        accountsResult,
        categoriesResult,
        statusesResult,
        departmentsResult,
        locationsResult,
        usersResult,
      ] = await Promise.all([
        getAssetAccounts(),
        getAssetCategories(),
        getAssetStatuses(),
        getDepartments(),
        getStorageLocations(),
        getUsers(),
      ]);

      if (accountsResult.success) setAssetAccounts(accountsResult.data || []);
      if (categoriesResult.success)
        setAssetCategories(categoriesResult.data || []);
      if (statusesResult.success) setAssetStatuses(statusesResult.data || []);
      if (departmentsResult.success)
        setDepartments(departmentsResult.data || []);
      if (locationsResult.success)
        setStorageLocations(locationsResult.data || []);
      if (usersResult.success) setUsers(usersResult.data || []);
    } catch (error) {
      console.error("載入選項數據錯誤:", error);
      message.error("載入選項數據失敗");
    }
  }, []);

  // =========================== 事件處理 ===========================

  // 搜尋處理 - 使用客戶端過濾
  const handleSearch = () => {
    const values = searchForm.getFieldsValue();
    let filtered = [...data];

    // 關鍵字搜尋
    if (values.keyword?.trim()) {
      const keyword = values.keyword.trim().toLowerCase();
      filtered = filtered.filter(
        (asset) =>
          (asset.assetNo || "").toLowerCase().includes(keyword) ||
          (asset.assetName || "").toLowerCase().includes(keyword)
      );
    }

    // 其他過濾條件
    if (values.assetAccountId) {
      filtered = filtered.filter(
        (asset) => asset.assetAccountId === values.assetAccountId
      );
    }
    if (values.assetStatusId) {
      filtered = filtered.filter(
        (asset) => asset.assetStatusId === values.assetStatusId
      );
    }
    if (values.departmentId) {
      filtered = filtered.filter(
        (asset) => asset.departmentId === values.departmentId
      );
    }
    if (values.assetCategoryId) {
      filtered = filtered.filter(
        (asset) => asset.assetCategoryId === values.assetCategoryId
      );
    }
    if (values.storageLocationId) {
      filtered = filtered.filter(
        (asset) => asset.storageLocationId === values.storageLocationId
      );
    }
    if (
      values.acquisitionDateRange &&
      values.acquisitionDateRange[0] &&
      values.acquisitionDateRange[1]
    ) {
      const startDate = values.acquisitionDateRange[0].startOf("day");
      const endDate = values.acquisitionDateRange[1].endOf("day");
      filtered = filtered.filter((asset) => {
        if (!asset.acquisitionDate) return false;
        const assetDate = dayjs(asset.acquisitionDate);
        return assetDate.isAfter(startDate) && assetDate.isBefore(endDate);
      });
    }

    setFilteredData(filtered);
    setSelectedAssets([]); // 重置選擇
    message.success(`找到 ${filtered.length} 筆資料`);
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    setFilteredData([...data]);
    setSelectedAssets([]);
  };

  // 列印處理
  const handlePrint = () => {
    if (selectedAssets.length === 0) {
      message.warning("請先選擇要列印標籤的資產");
      return;
    }

    if (!printRef.current) {
      message.error("列印內容未準備就緒");
      return;
    }

    const printContent = printRef.current.innerHTML;
    const printWindow = window.open("", "_blank");

    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${siteConfig.copyright}財產標籤</title>
            <style>
              body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 10px; 
                background: white;
              }
              .label {
                border: 1px solid #000;
                margin: 5px;
                padding: 10px;
                display: inline-block;
                width: ${labelSettings.labelWidth}cm;
                height: ${labelSettings.labelHeight}cm;
                box-sizing: border-box;
                page-break-inside: avoid;
                vertical-align: top;
              }
              .label-container {
                display: flex;
                height: 100%;
              }
              .label-header {
                font-size: 12px;
                font-weight: bold;
                text-align: center;
                margin-bottom: 5px;
                border-bottom: 1px solid #ccc;
                padding-bottom: 3px;
              }
              .label-left {
                flex: 1;
                padding-right: 5px;
              }
              .label-right {
                display: flex;
                align-items: center;
                justify-content: center;
                padding-left: 5px;
              }
              .label-content {
                font-size: 10px;
              }
              .label-row {
                margin: 2px 0;
                display: flex;
                align-items: flex-start;
              }
              .label-field {
                font-weight: bold;
                margin-right: 5px;
                min-width: 40px;
              }
              .label-value {
                word-break: break-all;
                flex: 1;
              }
              .qr-code {
                text-align: center;
              }
              .qr-code svg {
                width: 80px !important;
                height: 80px !important;
              }
              @media print {
                body { 
                  print-color-adjust: exact; 
                  -webkit-print-color-adjust: exact;
                }
                .label {
                  page-break-inside: avoid;
                }
              }
              ${getReportPrintStyles("財產標籤")}
            </style>
          </head>
          <body>
            ${printContent}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // 選擇資產
  const handleSelectAsset = (asset: Asset, checked: boolean) => {
    if (checked) {
      setSelectedAssets((prev) => [...prev, asset]);
    } else {
      setSelectedAssets((prev) =>
        prev.filter((item) => item.assetId !== asset.assetId)
      );
    }
  };

  // 全選/取消全選
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAssets([...filteredData]);
    } else {
      setSelectedAssets([]);
    }
  };

  // =========================== 表格列定義 ===========================

  const columns = [
    {
      title: (
        <Checkbox
          checked={
            selectedAssets.length === filteredData.length &&
            filteredData.length > 0
          }
          indeterminate={
            selectedAssets.length > 0 &&
            selectedAssets.length < filteredData.length
          }
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          全選
        </Checkbox>
      ),
      dataIndex: "select",
      key: "select",
      width: 80,
      render: (_: any, record: Asset) => {
        const isSelected = selectedAssets.some(
          (item) => item.assetId === record.assetId
        );
        return (
          <Checkbox
            checked={isSelected}
            onChange={(e) => handleSelectAsset(record, e.target.checked)}
          />
        );
      },
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
      width: 120,
      render: (text: string) => text || "-",
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
      width: 200,
      render: (text: string) => text || "-",
    },
    {
      title: "所屬部門",
      dataIndex: "departmentId",
      key: "departmentId",
      width: 120,
      render: (departmentId: string) => {
        const department = departments.find(
          (d) => d.departmentId === departmentId
        );
        return department?.name || "-";
      },
    },
    {
      title: "存放地點",
      dataIndex: "storageLocationId",
      key: "storageLocationId",
      width: 120,
      render: (locationId: string) => {
        const location = storageLocations.find(
          (l) => l.storageLocationId === locationId
        );
        return location?.name || "-";
      },
    },
    {
      title: "使用狀態",
      dataIndex: "assetStatusId",
      key: "assetStatusId",
      width: 100,
      render: (statusId: string) => {
        const status = assetStatuses.find((s) => s.assetStatusId === statusId);
        const color =
          STATUS_COLORS[status?.name as keyof typeof STATUS_COLORS] ||
          "default";
        return status ? <Tag color={color}>{status.name}</Tag> : "-";
      },
    },
    {
      title: "取得日期",
      dataIndex: "acquisitionDate",
      key: "acquisitionDate",
      width: 100,
      render: (date: number) => {
        return date ? dayjs(date).format("YYYY/MM/DD") : "-";
      },
    },
  ];

  // =========================== 生命週期 ===========================

  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([loadOptions(), loadData()]);
    };

    initializeData();
  }, [loadData, loadOptions]);

  // =========================== 渲染標籤 ===========================

  const renderLabels = () => {
    return selectedAssets.map((asset, index) => {
      const department = departments.find(
        (d) => d.departmentId === asset.departmentId
      );
      const location = storageLocations.find(
        (l) => l.storageLocationId === asset.storageLocationId
      );
      const custodian = users.find((u) => u.userId === asset.custodianId);

      // 生成 QR Code 的內容
      const qrCodeData = JSON.stringify({
        assetNo: asset.assetNo,
        assetName: asset.assetName,
        department: department?.name,
        location: location?.name,
        custodian: custodian?.name,
        acquisitionDate: asset.acquisitionDate
          ? dayjs(asset.acquisitionDate).format("YYYY/MM/DD")
          : null,
      });

      return (
        <div key={asset.assetId || index} className="label">
          <div className="label-header">{siteConfig.copyright}</div>
          <div className="label-container">
            <div className="label-left">
              <div className="label-content">
                <div className="label-row">
                  <span className="label-field">財產編號:</span>
                  <span className="label-value">
                    <strong>{asset.assetNo || "-"}</strong>
                  </span>
                </div>

                <div className="label-row">
                  <span className="label-field">財產名稱:</span>
                  <span className="label-value">
                    <strong>{asset.assetName || "-"}</strong>
                  </span>
                </div>

                {labelSettings.showDepartment && (
                  <div className="label-row">
                    <span className="label-field">所屬部門:</span>
                    <span className="label-value">
                      {department?.name || "-"}
                    </span>
                  </div>
                )}

                {labelSettings.showLocation && (
                  <div className="label-row">
                    <span className="label-field">存放地點:</span>
                    <span className="label-value">{location?.name || "-"}</span>
                  </div>
                )}

                {labelSettings.showCustodian && (
                  <div className="label-row">
                    <span className="label-field">保管人:</span>
                    <span className="label-value">
                      {custodian?.name || "-"}
                    </span>
                  </div>
                )}

                {labelSettings.showAcquisitionDate && asset.acquisitionDate && (
                  <div className="label-row">
                    <span className="label-field">取得日期:</span>
                    <span className="label-value">
                      {dayjs(asset.acquisitionDate).format("YYYY/MM/DD")}
                    </span>
                  </div>
                )}
              </div>
            </div>
            {labelSettings.showBarcode && (
              <div className="label-right">
                <div className="barcode-container">
                  {labelSettings.barcodeType === "qrcode" ? (
                    <QRCodeSVG value={qrCodeData} size={80} level="M" />
                  ) : (
                    <Barcode
                      value={asset.assetNo || ""}
                      width={1}
                      height={40}
                      fontSize={10}
                      margin={0}
                      displayValue={false}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      );
    });
  };

  // =========================== 主要渲染 ===========================

  return (
    <div style={{ padding: "20px" }}>
      <Card title="財產標籤">
        {/* 搜尋表單 */}
        <Card title="搜尋條件" style={{ marginBottom: "24px" }}>
          <Form form={searchForm} layout="vertical">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="keyword" label="關鍵字">
                  <Input placeholder="財產編號、財產名稱、規格等" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetAccountId" label="財產科目">
                  <Select placeholder="請選擇" allowClear>
                    {assetAccounts.map((item: any) => (
                      <Option
                        key={item.assetAccountId}
                        value={item.assetAccountId}
                      >
                        {item.assetAccountName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetStatusId" label="使用狀態">
                  <Select placeholder="請選擇" allowClear>
                    {assetStatuses.map((item: any) => (
                      <Option
                        key={item.assetStatusId}
                        value={item.assetStatusId}
                      >
                        <Tag
                          color={STATUS_COLORS[item.name] || "default"}
                          style={{ marginRight: 8 }}
                        >
                          {item.name}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="departmentId" label="所屬部門">
                  <Select placeholder="請選擇" allowClear>
                    {departments.map((item: any) => (
                      <Option key={item.departmentId} value={item.departmentId}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetCategoryId" label="財產分類">
                  <Select placeholder="請選擇" allowClear>
                    {assetCategories.map((item: any) => (
                      <Option
                        key={item.assetCategoryId}
                        value={item.assetCategoryId}
                      >
                        {item.assetCategoryName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="storageLocationId" label="存放地點">
                  <Select placeholder="請選擇" allowClear>
                    {storageLocations.map((item: any) => (
                      <Option
                        key={item.storageLocationId}
                        value={item.storageLocationId}
                      >
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="acquisitionDateRange" label="取得日期範圍">
                  <RangePicker style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                  >
                    搜尋
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
                    重置
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 標籤設定 */}
        <Card title="標籤設定" style={{ marginBottom: "24px" }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Space direction="vertical">
                <Checkbox
                  checked={labelSettings.showBarcode}
                  onChange={(e) =>
                    setLabelSettings({
                      ...labelSettings,
                      showBarcode: e.target.checked,
                    })
                  }
                >
                  顯示條碼
                </Checkbox>
                {labelSettings.showBarcode && (
                  <Radio.Group
                    value={labelSettings.barcodeType}
                    onChange={(e) =>
                      setLabelSettings({
                        ...labelSettings,
                        barcodeType: e.target.value,
                      })
                    }
                    style={{ marginLeft: "20px" }}
                  >
                    <Radio value="qrcode">QR Code</Radio>
                    <Radio value="barcode">一維條碼</Radio>
                  </Radio.Group>
                )}
                <Checkbox
                  checked={labelSettings.showDepartment}
                  onChange={(e) =>
                    setLabelSettings({
                      ...labelSettings,
                      showDepartment: e.target.checked,
                    })
                  }
                >
                  顯示部門
                </Checkbox>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Space direction="vertical">
                <Checkbox
                  checked={labelSettings.showLocation}
                  onChange={(e) =>
                    setLabelSettings({
                      ...labelSettings,
                      showLocation: e.target.checked,
                    })
                  }
                >
                  顯示存放地點
                </Checkbox>
                <Checkbox
                  checked={labelSettings.showCustodian}
                  onChange={(e) =>
                    setLabelSettings({
                      ...labelSettings,
                      showCustodian: e.target.checked,
                    })
                  }
                >
                  顯示保管人
                </Checkbox>
                <Checkbox
                  checked={labelSettings.showAcquisitionDate}
                  onChange={(e) =>
                    setLabelSettings({
                      ...labelSettings,
                      showAcquisitionDate: e.target.checked,
                    })
                  }
                >
                  顯示取得日期
                </Checkbox>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <div>
                <Text>每行標籤數:</Text>
                <InputNumber
                  min={1}
                  max={5}
                  value={labelSettings.labelsPerRow}
                  onChange={(value) =>
                    setLabelSettings({
                      ...labelSettings,
                      labelsPerRow: value || 3,
                    })
                  }
                  style={{ width: "100%", marginTop: "4px" }}
                />
              </div>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <div>
                <Text>標籤寬度(cm):</Text>
                <InputNumber
                  min={5}
                  max={15}
                  value={labelSettings.labelWidth}
                  onChange={(value) =>
                    setLabelSettings({
                      ...labelSettings,
                      labelWidth: value || 8,
                    })
                  }
                  style={{ width: "100%", marginTop: "4px" }}
                />
              </div>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <div>
                <Text>標籤高度(cm):</Text>
                <InputNumber
                  min={3}
                  max={10}
                  value={labelSettings.labelHeight}
                  onChange={(value) =>
                    setLabelSettings({
                      ...labelSettings,
                      labelHeight: value || 4,
                    })
                  }
                  style={{ width: "100%", marginTop: "4px" }}
                />
              </div>
            </Col>
          </Row>
        </Card>

        {/* 操作區域 */}
        <Card style={{ marginBottom: "24px" }}>
          <Row>
            <Col>
              <Space>
                <Button
                  icon={<EyeOutlined />}
                  onClick={() => setIsPrintMode(!isPrintMode)}
                >
                  {isPrintMode ? "返回列表" : "預覽標籤"}
                </Button>
                {isPrintMode && (
                  <Button
                    type="primary"
                    icon={<PrinterOutlined />}
                    onClick={handlePrint}
                    disabled={selectedAssets.length === 0}
                  >
                    列印標籤
                  </Button>
                )}
                <Badge count={selectedAssets.length} showZero>
                  <Button size="small">已選擇</Button>
                </Badge>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 列印/預覽區域 */}
        <div ref={printRef}>
          {isPrintMode ? (
            // 標籤預覽模式
            <div style={{ background: "white", padding: "20px" }}>
              <ReportHeader reportTitle="財產標籤" isPrintMode={true} />

              {selectedAssets.length > 0 ? (
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: `repeat(${labelSettings.labelsPerRow}, 1fr)`,
                    gap: "10px",
                    padding: "10px",
                    background: "#fff",
                  }}
                >
                  {renderLabels()}
                </div>
              ) : (
                <Empty
                  description="請先選擇要列印標籤的資產"
                  style={{ margin: "40px 0" }}
                />
              )}
            </div>
          ) : (
            // 列表模式
            <Card title={`財產列表 (${filteredData.length} 筆資料)`}>
              <Table
                columns={columns}
                dataSource={filteredData}
                rowKey={(record) => record.assetId}
                loading={loading}
                scroll={{ x: 800 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span style={{ color: "#999" }}>
                          查無符合條件的資料
                          <br />
                          請調整搜尋條件後重新查詢
                        </span>
                      }
                    />
                  ),
                }}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                  pageSizeOptions: ["10", "20", "50", "100"],
                }}
              />
            </Card>
          )}
        </div>
      </Card>

      {/* 標籤預覽樣式 */}
      <style jsx global>{`
        .label {
          border: 1px solid #000;
          margin: 5px;
          padding: 10px;
          display: inline-block;
          width: ${labelSettings.labelWidth}cm;
          height: ${labelSettings.labelHeight}cm;
          box-sizing: border-box;
          page-break-inside: avoid;
          vertical-align: top;
          background: white;
        }
        .label-container {
          display: flex;
          height: calc(100% - 25px); /* 扣除標題高度 */
        }
        .label-header {
          font-size: 12px;
          font-weight: bold;
          text-align: center;
          margin-bottom: 5px;
          border-bottom: 1px solid #ccc;
          padding-bottom: 3px;
          height: 20px; /* 固定標題高度 */
        }
        .label-left {
          flex: 1;
          padding-right: 5px;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
        }
        .label-right {
          width: 90px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding-left: 5px;
        }
        .label-content {
          font-size: 10px;
        }
        .label-row {
          margin: 2px 0;
          display: flex;
          align-items: flex-start;
        }
        .label-field {
          font-weight: bold;
          margin-right: 5px;
          min-width: 40px;
        }
        .label-value {
          word-break: break-all;
          flex: 1;
        }
        .qr-code {
          text-align: center;
        }
        .qr-code svg {
          width: 80px !important;
          height: 80px !important;
        }
        @media print {
          body {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
          }
          .label {
            page-break-inside: avoid;
          }
          .no-print {
            display: none;
          }
        }
        .barcode-container {
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        .barcode-container svg {
          max-width: 100%;
          height: auto;
        }
      `}</style>
    </div>
  );
};

export default AssetLabelPage;
