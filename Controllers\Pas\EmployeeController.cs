using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("員工主檔管理")]
    public class EmployeeController : ControllerBase
    {
        private readonly IEmployeeService _Interface;

        public EmployeeController(IEmployeeService employeeService)
        {
            _Interface = employeeService;
        }

        [HttpPost]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得員工列表", Description = "取得所有員工卡片陣列")]
        public async Task<IActionResult> GetEmployeeList([FromBody] FilterData _data)
        {
            var result = await _Interface.GetEmployeeListAsync(_data);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_userid}")]
        [SwaggerOperation(Summary = "取得員工明細", Description = "依UserID取得員工明細")]
        public async Task<IActionResult> GetEmployeeDetail(string _userid)
        {
            var result = await _Interface.GetEmployeeDetailAsync(_userid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增員工主檔", Description = "新增員工主檔資料")]
        public async Task<IActionResult> AddEmployee([FromBody] EmployeeDTO _data)
        {
            var (result, msg) = await _Interface.AddEmployeeAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯員工主檔", Description = "修改已存在之員工資料")]
        public async Task<IActionResult> EditEmployee([FromBody] EmployeeDTO _data)
        {
            var (result, msg) = await _Interface.EditEmployeeAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Complete")]
        [SwaggerOperation(Summary = "補全員工主檔", Description = "補全已存在之員工資料")]
        public async Task<IActionResult> CompleteEmployee([FromBody] EmployeeDTO _data)
        {
            var (result, msg) = await _Interface.CompleteEmployeeAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除員工主檔", Description = "刪除已存在之員工主檔資料")]
        public async Task<IActionResult> DeleteEmployee([FromBody] string _userid)
        {
            var (result, msg) = await _Interface.DeleteEmployeeAsync(_userid);
            return Ok(new { result, msg });
        }
    }
}