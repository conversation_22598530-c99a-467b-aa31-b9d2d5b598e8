/**
 * IMS模組統一樣式組件
 * 基於 PriceTypeManagement.tsx 的設計風格
 */

import React from 'react';
import { Card, Button, Tag, Badge } from 'antd';
import { CardProps, ButtonProps, TagProps } from 'antd';

// 統一的間距系統
export const IMSSpacing = {
  base: 16,        // 基礎間距 16px
  small: 8,        // 小間距 8px
  large: 24,       // 大間距 24px
  tiny: 4,         // 微小間距 4px
  borderRadius: 6, // 統一的圓角半徑
} as const;

// 統一的顏色系統
export const IMSColors = {
  primary: {
    main: '#1890ff',
    light: '#40a9ff',
    dark: '#096dd9',
  },
  secondary: {
    main: '#722ed1',
    light: '#9254de',
    dark: '#531dab',
  },
  success: {
    main: '#52c41a',
    light: '#73d13d',
    dark: '#389e0d',
  },
  warning: {
    main: '#faad14',
    light: '#ffc53d',
    dark: '#d48806',
  },
  error: {
    main: '#ff4d4f',
    light: '#ff7875',
    dark: '#cf1322',
  },
  info: {
    main: '#13c2c2',
    light: '#36cfc9',
    dark: '#08979c',
  },
  text: {
    primary: '#262626',
    secondary: '#8c8c8c',
    disabled: '#bfbfbf',
  },
  background: {
    page: '#f5f5f5',
    card: '#ffffff',
    content: '#fafafa',
    disabled: '#f5f5f5',
  },
  border: {
    light: '#f0f0f0',
    main: '#d9d9d9',
    dark: '#bfbfbf',
  },
  shadow: {
    card: '0 2px 8px rgba(0,0,0,0.06)',
    modal: '0 4px 12px rgba(0,0,0,0.15)',
  },
  // 向後兼容的扁平屬性
  selectedBorder: '#1890ff',
  selectedBackground: '#f6ffed',
} as const;

// 統一的字體大小
export const IMSFontSizes = {
  small: '11px',
  normal: '12px',
  medium: '14px',
  large: '16px',
} as const;

// 統一的Card組件
interface IMSCardProps extends Omit<CardProps, 'variant'> {
  variant?: 'default' | 'form' | 'list' | 'selected';
}

export const IMSCard: React.FC<IMSCardProps> = ({ 
  variant = 'default', 
  style, 
  children, 
  ...props 
}) => {
  const getVariantStyle = () => {
    switch (variant) {
      case 'form':
        return {
          marginBottom: IMSSpacing.base,
          borderWidth: 2,
          borderColor: IMSColors.primary.main,
        };
      case 'list':
        return {
          marginBottom: IMSSpacing.small,
          border: `1px solid ${IMSColors.border.light}`,
          backgroundColor: IMSColors.background.content,
        };
      case 'selected':
        return {
          marginBottom: IMSSpacing.small,
          border: `2px solid ${IMSColors.selectedBorder}`,
          backgroundColor: IMSColors.selectedBackground,
        };
      default:
        return {
          marginBottom: IMSSpacing.base,
        };
    }
  };

  return (
    <Card
      size="small"
      style={{
        ...getVariantStyle(),
        ...style,
      }}
      styles={{ body: { padding: '12px' } }}
      {...props}
    >
      {children}
    </Card>
  );
};

// 統一的Button組件
interface IMSButtonProps extends Omit<ButtonProps, 'variant'> {
  variant?: 'action' | 'operation';
}

export const IMSButton: React.FC<IMSButtonProps> = ({ 
  variant = 'action', 
  style, 
  children, 
  ...props 
}) => {
  const getVariantStyle = () => {
    switch (variant) {
      case 'operation':
        return {
          padding: '0 8px',
          height: '24px',
          fontSize: IMSFontSizes.normal,
        };
      default:
        return {};
    }
  };

  return (
    <Button
      style={{
        ...getVariantStyle(),
        ...style,
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

// 統一的Tag組件
interface IMSTagProps extends TagProps {
  variant?: 'status' | 'info' | 'hierarchy';
}

export const IMSTag: React.FC<IMSTagProps> = ({ 
  variant = 'info', 
  style, 
  children, 
  ...props 
}) => {
  const getVariantStyle = () => {
    return {
      margin: 0,
      fontSize: IMSFontSizes.small,
    };
  };

  return (
    <Tag
      style={{
        ...getVariantStyle(),
        ...style,
      }}
      {...props}
    >
      {children}
    </Tag>
  );
};

// 統一的操作按鈕容器
interface IMSActionContainerProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
}

export const IMSActionContainer: React.FC<IMSActionContainerProps> = ({ 
  children, 
  style 
}) => {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'flex-end',
      gap: IMSSpacing.small,
      marginTop: IMSSpacing.tiny,
      ...style,
    }}>
      {children}
    </div>
  );
};

// 統一的標籤容器
interface IMSTagContainerProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
}

export const IMSTagContainer: React.FC<IMSTagContainerProps> = ({ 
  children, 
  style 
}) => {
  return (
    <div style={{
      display: 'flex',
      flexWrap: 'wrap',
      gap: IMSSpacing.tiny,
      alignItems: 'center',
      ...style,
    }}>
      {children}
    </div>
  );
};

// 統一的內容容器
interface IMSContentContainerProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
}

export const IMSContentContainer: React.FC<IMSContentContainerProps> = ({ 
  children, 
  style 
}) => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: IMSSpacing.small,
      ...style,
    }}>
      {children}
    </div>
  );
};

// 統一的標題容器
interface IMSTitleContainerProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
}

export const IMSTitleContainer: React.FC<IMSTitleContainerProps> = ({ 
  children, 
  style 
}) => {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: IMSSpacing.tiny,
      ...style,
    }}>
      {children}
    </div>
  );
};

// 統一的響應式配置 - 優化版
export const IMSResponsive = {
  gutter: [IMSSpacing.base, IMSSpacing.base] as [number, number],
  mobileGutter: [8, 8] as [number, number],
  leftCol: { xs: 24, sm: 24, md: 12, lg: 12, xl: 12 },
  rightCol: { xs: 24, sm: 24, md: 12, lg: 12, xl: 12 },
  formCol: { xs: 24, sm: 12, md: 8, lg: 6, xl: 6 },
  formColMedium: { xs: 24, sm: 24, md: 12, lg: 8, xl: 8 },
  formColLarge: { xs: 24, sm: 24, md: 24, lg: 12, xl: 12 },
  fullCol: { xs: 24 },
  threeCol: { xs: 24, sm: 12, md: 8, lg: 8, xl: 8 },
  fourCol: { xs: 24, sm: 12, md: 6, lg: 6, xl: 6 },
} as const;

// 響應式斷點配置
export const IMSBreakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
} as const;

// 移動端檢測工具
export const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < IMSBreakpoints.md;
};

// 響應式間距配置
export const getResponsiveSpacing = (isMobile: boolean) => ({
  padding: isMobile ? IMSSpacing.tiny : IMSSpacing.base,
  margin: isMobile ? IMSSpacing.tiny : IMSSpacing.base,
  gap: isMobile ? IMSSpacing.tiny : IMSSpacing.small,
});

// 統一的滾動容器樣式
export const IMSScrollContainer = {
  maxHeight: 400,
  overflow: 'auto' as const,
  padding: IMSSpacing.tiny,
};

// 統一的空狀態樣式
export const IMSEmptyState = {
  textAlign: 'center' as const,
  padding: '40px 20px',
  color: '#8c8c8c',
};
