﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{   
    
    public class FileListService : IFileListService
    {
        private readonly ERPDbContext _context;

        public FileListService(ERPDbContext context)
        {
            _context = context;
        }
        // <summary>
        // 刪除檔案
        // </summary>
        // <remarks>
        // 軟刪除,將 DeleteTime 設為當前時間戳，並將 DeleteUserId 設為當前使用者的 ID。
        // 不做SaveChanges()，讓呼叫者自行決定何時儲存變更。
        // </remarks>
        private async Task<(bool, string)> DeleteFilesAsync(List<FileListDTO> fileList, long changeTime, string tokenUid = "")
        {
            try
            {
                // 1. 取得所有傳入的 FileId
                var fileIds = fileList.Select(f => f.FileId).ToList();

                var existingFile = _context.Common_FileList
                    .Where(f => fileIds.Contains(f.FileId) && f.DeleteTime == null)
                    .ToList();
                if (existingFile.Count == 0)
                {
                    throw new Exception("檔案不存在或已被刪除");
                }
                else
                {
                    foreach (var file in existingFile)
                    {
                        file.DeleteTime = changeTime;
                        file.DeleteUserId = tokenUid;
                    }

                    return (true, "刪除檔案成功");
                }
            }
            catch (Exception ex)
            {
                return (false, $"刪除檔案失敗: {ex.Message}");
            }
        }

        public async Task<List<FileListDTO>> GetFileListAsync(string fileListId)
        {
            var query = _context.Common_FileList.AsQueryable();
            if (!string.IsNullOrEmpty(fileListId))
            {
                query = query.Where(e => e.FileListId == fileListId);
            }

            return await query.OrderBy(e => e.SortOrder).Select(e => new FileListDTO
            {
                FileId = e.FileId,
                FileName = e.FileName,
                FilePath = e.FilePath,
                FileType = e.FileType,
                Description = e.Description,
                SortOrder = e.SortOrder,
                CreateTime = e.CreateTime,
                CreateUserId = e.CreateUserId,
                UpdateTime = e.UpdateTime,
                UpdateUserId = e.UpdateUserId
            }).ToListAsync();
        }

        public async Task<(bool, string)> UploadFilesAsync(List<FileListUploadDTO> files, string tokenUid = "")
        {
            var changeTime = DateTimeOffset.Now.ToUnixTimeSeconds();

            return (true, "更新檔案成功"+changeTime.ToString());
        }

        // <summary>
        // 更新檔案
        // </summary>
        // <remarks>
        // 不做SaveChanges()，讓呼叫者自行決定何時儲存變更。
        // </remarks>
        private async Task<(bool, string)> UpdateFileAsync(List<FileListDTO> fileList, long changeTime, string tokenUid = "")
        {
             try
            {
                // 1. 取得所有傳入的 FileId
                var idList = fileList.Select(f => f.FileId).ToList();

                var existingFile = _context.Common_FileList
                    .Where(f => idList.Contains(f.FileId) && f.DeleteTime == null)
                    .ToList();
                if (existingFile.Count == 0)
                {
                    throw new Exception("檔案不存在或已被刪除");
                }
                else
                {
                    foreach (var file in existingFile)
                    {
                        var updatedFile = fileList.FirstOrDefault(f => f.FileId == file.FileId);
                        if (updatedFile != null)
                        {
                            file.FileName = updatedFile.FileName;
                            file.Description = updatedFile.Description;
                            file.SortOrder = updatedFile.SortOrder;
                            file.UpdateTime = changeTime;
                            file.UpdateUserId = tokenUid;
                        }
                    }
                    return (true, "更新檔案成功");
                }
            }
            catch (Exception ex)
            {
                return (false, $"更新檔案失敗: {ex.Message}");
            }
        }
        

        public async Task<(bool, string)> ChangesFileAsync(List<FileListDTO> fileList, string tokenUid = "")
        {
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開始交易
            try
            {
                var toDelete = fileList.Where(f => f.isDelete).ToList();
                var toUpdate = fileList.Where(f => !f.isDelete && f.isEdit).ToList();
                var changeTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                if (toDelete.Count > 0)
                {
                    var (deleteSuccess, deleteMsg) = await DeleteFilesAsync(toDelete, changeTime, tokenUid);
                    if (!deleteSuccess) {
                        throw new Exception(deleteMsg);
                    }
                }

                if (toUpdate.Count > 0)
                {
                    var (updateSuccess, updateMsg) = await UpdateFileAsync(toUpdate, changeTime, tokenUid);
                    if (!updateSuccess) {
                        throw new Exception(updateMsg);
                    }
                }
                await _context.SaveChangesAsync(); // 儲存變更
                await transaction.CommitAsync();   // 提交交易
                return (true, "檔案異動成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync(); // 回滾交易
                return (false, $"處理檔案異動失敗: {ex.Message}");
            }
        }
    }
}
