using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Pas;

namespace FAST_ERP_Backend.Services.Pas
{
    public class EmployeeRegularSalaryService : IEmployeeRegularSalaryService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public EmployeeRegularSalaryService(ERPDbContext context, Baseform baseform, ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<EmployeeRegularSalaryDTO>> GetByUserIdAsync(string userId)
        {
            return await _context.Pas_EmployeeRegularSalary
                .Where(x => !x.IsDeleted && x.userId == userId)
                .Join(_context.Pas_RegularSalaryItem,
                      emp => emp.salaryItemUid,
                      item => item.uid,
                      (emp, item) => new EmployeeRegularSalaryDTO
                      {
                          uid = emp.uid,
                          userId = emp.userId,
                          salaryItemUid = emp.salaryItemUid,
                          salaryItemName = item.itemName,
                          amount = emp.amount.ToString("0"),
                          remark = emp.remark,
                          isEnable = emp.isEnable,
                          itemType = item.itemType,
                          CreateTime = emp.CreateTime,
                          UpdateTime = emp.UpdateTime
                      })
                .OrderBy(x => x.itemType)
                .ThenBy(x => x.salaryItemName)
                .ToListAsync();
        }

        public async Task<EmployeeRegularSalaryDTO?> GetByIdAsync(string uid)
        {
            return await _context.Pas_EmployeeRegularSalary
                .Where(x => x.uid == uid && !x.IsDeleted)
                .Join(_context.Pas_RegularSalaryItem,
                      emp => emp.salaryItemUid,
                      item => item.uid,
                      (emp, item) => new EmployeeRegularSalaryDTO
                      {
                          uid = emp.uid,
                          userId = emp.userId,
                          salaryItemUid = emp.salaryItemUid,
                          salaryItemName = item.itemName,
                          amount = emp.amount.ToString("0"),
                          remark = emp.remark,
                          isEnable = emp.isEnable,
                          CreateTime = emp.CreateTime,
                          UpdateTime = emp.UpdateTime
                      })
                .FirstOrDefaultAsync();
        }

        public async Task<(bool, string)> AddAsync(EmployeeRegularSalaryDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = new EmployeeRegularSalary
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = dto.userId,
                    salaryItemUid = dto.salaryItemUid,
                    amount = decimal.TryParse(dto.amount, out var amt) ? amt : 0,
                    remark = dto.remark,
                    isEnable = dto.isEnable,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                _context.Pas_EmployeeRegularSalary.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增員工常態薪資成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增員工常態薪資失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditAsync(EmployeeRegularSalaryDTO dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_EmployeeRegularSalary
                    .FirstOrDefaultAsync(x => x.uid == dto.uid && !x.IsDeleted);

                if (entity == null)
                    throw new Exception("找不到指定資料");

                entity.salaryItemUid = dto.salaryItemUid;
                entity.amount = decimal.TryParse(dto.amount, out var amt) ? amt : 0;
                entity.remark = dto.remark;
                entity.isEnable = dto.isEnable;
                entity.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                entity.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "更新員工常態薪資成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"更新員工常態薪資失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_EmployeeRegularSalary
                    .FirstOrDefaultAsync(x => x.uid == uid && !x.IsDeleted);

                if (entity == null)
                    throw new Exception("資料不存在");

                entity.IsDeleted = true;
                entity.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                entity.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除員工常態薪資成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除員工常態薪資失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

    }
}