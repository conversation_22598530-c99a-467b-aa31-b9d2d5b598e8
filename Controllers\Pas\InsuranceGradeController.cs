using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    public class InsuranceGradeController : ControllerBase
    {
        private readonly IInsuranceGradeService _insuranceGradeService;

        public InsuranceGradeController(IInsuranceGradeService insuranceGradeService)
        {
            _insuranceGradeService = insuranceGradeService;
        }

        /// <summary>
        /// 取得保險級距列表
        /// </summary>
        /// <param name="_typeid">保險類型 (1=勞保, 2=健保, 3=職災)，可選</param>
        /// <returns>保險級距列表</returns>
        [HttpGet]
        [Route("GetInsuranceGradeList/{_typeid}")]
        [SwaggerOperation(Summary = "取得保險級距列表", Description = "取得保險級距列表")]
        public async Task<ActionResult<List<InsuranceGradeDTO>>> GetInsuranceGradeList(int _typeid)
        {
            var result = await _insuranceGradeService.GetInsuranceGradeListAsync(_typeid);
            return Ok(result);
        }

        /// <summary>
        /// 取得保險級距明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>保險級距明細</returns>
        [HttpGet]
        [Route("GetInsuranceGradeDetail/{_uid}")]
        [SwaggerOperation(Summary = "取得保險級距明細", Description = "取得保險級距明細")]
        public async Task<ActionResult<InsuranceGradeDTO>> GetInsuranceGradeDetail(string _uid)
        {
            var result = await _insuranceGradeService.GetInsuranceGradeDetailAsync(_uid);
            return Ok(result);
        }

        /// <summary>
        /// 新增保險級距
        /// </summary>
        /// <param name="data">保險級距資料</param>
        /// <returns>執行結果</returns>
        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增保險級距", Description = "新增保險級距")]
        public async Task<ActionResult> AddInsuranceGrade([FromBody] InsuranceGradeDTO data)
        {
            var (result, msg) = await _insuranceGradeService.AddInsuranceGradeAsync(data);
            return Ok(new { result, msg });
        }

        /// <summary>
        /// 編輯保險級距
        /// </summary>
        /// <param name="data">保險級距資料</param>
        /// <returns>執行結果</returns>
        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯保險級距", Description = "編輯保險級距")]
        public async Task<ActionResult> EditInsuranceGrade([FromBody] InsuranceGradeDTO data)
        {
            var (result, msg) = await _insuranceGradeService.EditInsuranceGradeAsync(data);
            return Ok(new { result, msg });
        }

        /// <summary>
        /// 刪除保險級距
        /// </summary>
        /// <param name="uid">資料編號</param>
        /// <returns>執行結果</returns>
        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除保險級距", Description = "刪除保險級距")]
        public async Task<ActionResult> DeleteInsuranceGrade([FromBody] string uid)
        {
            var (result, msg) = await _insuranceGradeService.DeleteInsuranceGradeAsync(uid);
            return Ok(new { result, msg });
        }

        /// <summary>
        /// 依據月投保薪資取得對應的保險級距
        /// </summary>
        /// <param name="monthlySalary">月投保薪資</param>
        /// <param name="insuranceType">保險類型</param>
        /// <returns>保險級距資料</returns>
        [HttpGet("by-salary")]
        public async Task<ActionResult<InsuranceGradeDTO>> GetInsuranceGradeBySalary([FromQuery] int monthlySalary, [FromQuery] int insuranceType)
        {
            try
            {
                var result = await _insuranceGradeService.GetInsuranceGradeBySalaryAsync(monthlySalary, insuranceType);
                if (result == null)
                {
                    return NotFound(new { message = "找不到對應的保險級距" });
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得保險級距的員工詳細列表
        /// </summary>
        /// <param name="gradeUid">級距UID</param>
        /// <param name="status">員工狀態 (current=目前生效, pending=待生效)</param>
        /// <returns>員工詳細列表</returns>
        [HttpGet]
        [Route("GetEmployeeInsuranceGradeDetail/{gradeUid}/{status}")]
        [SwaggerOperation(Summary = "取得保險級距員工詳細列表", Description = "取得指定級距和狀態的員工詳細資訊")]
        public async Task<ActionResult<List<InsuranceGradeEmployeeDetailDTO>>> GetEmployeeInsuranceGradeDetail(string gradeUid, string status)
        {
            try
            {
                var result = await _insuranceGradeService.GetEmployeeInsuranceGradeDetailAsync(gradeUid, status);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }
}