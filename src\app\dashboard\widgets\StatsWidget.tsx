"use client";

import { Card, Statistic, Row, Col, Spin } from "antd";
import {
  UserOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useState, useEffect } from "react";

export function StatsWidget() {
  const [loading, setLoading] = useState(true);

  // 模擬數據，實際應用中應從API獲取
  const stats = [
    {
      title: "總用戶數",
      value: 1234,
      icon: <UserOutlined style={{ color: "#1890ff" }} />,
      color: "#1890ff",
    },
    {
      title: "待處理任務",
      value: 56,
      icon: <ClockCircleOutlined style={{ color: "#faad14" }} />,
      color: "#faad14",
    },
    {
      title: "已完成任務",
      value: 789,
      icon: <CheckCircleOutlined style={{ color: "#52c41a" }} />,
      color: "#52c41a",
    },
    {
      title: "總文件數",
      value: 2345,
      icon: <FileTextOutlined style={{ color: "#722ed1" }} />,
      color: "#722ed1",
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large">
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
            }}
          >
            載入統計數據...
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: "100%" }}>
      <Row gutter={[16, 16]}>
        {stats.map((stat, index) => (
          <Col span={12} key={index}>
            <Card size="small" style={{ textAlign: "center" }}>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.color, fontSize: "18px" }}
              />
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
}

export default StatsWidget;
