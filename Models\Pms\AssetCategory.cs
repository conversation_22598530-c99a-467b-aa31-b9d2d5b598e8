using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 資產類別
    /// </summary>
    public class AssetCategory : ModelBaseEntity
    {
        [Key]
        [Comment("資產類別編號")]
        [Column(TypeName = "nvarchar(1)")]
        public string AssetCategoryId { get; set; } // 資產類別編號

        [Comment("資產類別名稱")]
        public string AssetCategoryName { get; set; } // 資產類別名稱

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public AssetCategory()
        {
            AssetCategoryId = "";
            AssetCategoryName = "";
            SortCode = 0;
            CreateTime = 0;
            CreateUserId = "";
            UpdateTime = 0;
            UpdateUserId = "";
            DeleteTime = 0;
            DeleteUserId = "";
            IsDeleted = false;
        }
    }

    public class AssetCategoryDTO : ModelBaseEntityDTO
    {
        public string AssetCategoryId { get; set; } // 資產類別編號
        public string AssetCategoryName { get; set; } // 資產類別名稱

        public int SortCode { get; set; } // 排序號碼

        public AssetCategoryDTO()
        {
            AssetCategoryId = "";
            AssetCategoryName = "";
            SortCode = 0;
            CreateTime = 0;
            CreateUserId = "";
            UpdateTime = 0;
            UpdateUserId = "";
            DeleteTime = 0;
            DeleteUserId = "";
            IsDeleted = false;
        }
    }
}