"use client";

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Modal, Form, Input, Button, Space, Tag, Row, Col, TreeSelect, Popconfirm, message, Card, Badge, Alert } from 'antd';
import type { InputRef } from 'antd';
import { PlusOutlined, EditOutlined, SaveOutlined, UndoOutlined, ApartmentOutlined, NodeIndexOutlined, ExpandOutlined, CompressOutlined, CaretRightOutlined, CaretDownOutlined, DeleteOutlined, InboxOutlined } from '@ant-design/icons';
import { ItemCategory } from '@/services/ims/ItemCategoryService';
import { addItemCategory, editItemCategory, deleteItemCategory, buildCategoryTree } from '@/services/ims/ItemCategoryService';

interface ItemCategoryManagementProps {
  visible: boolean;
  onClose: () => void;
  categories: ItemCategory[];
  onDataChange: () => void;
}

const ItemCategoryManagement: React.FC<ItemCategoryManagementProps> = ({
  visible,
  onClose,
  categories,
  onDataChange
}) => {
  // 狀態管理
  const [selectedCategory, setSelectedCategory] = useState<ItemCategory | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [operationMode, setOperationMode] = useState<'add' | 'edit' | 'addChild'>('add');
  const [parentCategoryForChild, setParentCategoryForChild] = useState<ItemCategory | null>(null);

  // 折疊功能狀態管理
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isAllExpanded, setIsAllExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 輸入框引用
  const nameInputRef = useRef<InputRef>(null);

  // 響應式設計檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 建立樹狀選擇器資料
  const categoryTreeData = useMemo(() => {
    if (!Array.isArray(categories)) {
      return [];
    }

    const buildTreeSelectData = (cats: ItemCategory[]): any[] => {
      return cats.map(category => ({
        title: category.name,
        value: category.itemCategoryID,
        key: category.itemCategoryID,
        children: category.children && category.children.length > 0 
          ? buildTreeSelectData(category.children) 
          : undefined
      }));
    };

    try {
      const tree = buildCategoryTree(categories);
      return buildTreeSelectData(tree);
    } catch (error) {
      console.error('❌ ItemCategoryManagement: 建立樹狀資料時發生錯誤:', error);
      return [];
    }
  }, [categories]);

  // 全部展開/折疊功能
  const toggleAllExpansion = () => {
    if (isAllExpanded) {
      setExpandedCategories(new Set());
    } else {
      const allIds = new Set<string>();
      const collectIds = (cats: ItemCategory[]) => {
        cats.forEach(cat => {
          if (cat && cat.itemCategoryID) {
            allIds.add(cat.itemCategoryID);
            if (cat.children && cat.children.length > 0) {
              collectIds(cat.children);
            }
          }
        });
      };
      collectIds(categories);
      setExpandedCategories(allIds);
    }
    setIsAllExpanded(!isAllExpanded);
  };

  // 排序後的分類顯示資料
  const sortedCategoriesForDisplay = useMemo(() => {
    if (!Array.isArray(categories)) {
      return [];
    }

    const sortedCategories = [...categories].sort((a, b) => {
      // 先按 sortCode 排序，再按名稱排序
      if (a.sortCode !== b.sortCode) {
        return a.sortCode - b.sortCode;
      }
      return a.name.localeCompare(b.name);
    });

    return sortedCategories;
  }, [categories]);

  // 重置表單
  const resetForm = () => {
    form.resetFields();
    setSelectedCategory(null);
    setOperationMode('add');
    setParentCategoryForChild(null);
  };

  // 處理新增分類
  const handleAdd = () => {
    resetForm();
    setOperationMode('add');
    
    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理編輯
  const handleEdit = (category: ItemCategory) => {
    setSelectedCategory(category);
    setParentCategoryForChild(null);
    setOperationMode('edit');
    
    form.setFieldsValue({
      name: category.name,
      description: category.description,
      parentID: category.parentID,
      sortCode: category.sortCode
    });

    setTimeout(() => {
      nameInputRef.current?.focus();
      nameInputRef.current?.select();
    }, 100);
  };

  // 處理新增子分類
  const handleAddChild = (parentCategory: ItemCategory) => {
    resetForm();
    setParentCategoryForChild(parentCategory);
    setOperationMode('addChild');
    
    form.setFieldsValue({
      parentID: parentCategory.itemCategoryID
    });

    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理刪除
  const handleDelete = async (category: ItemCategory) => {
    try {
      setLoading(true);
      const response = await deleteItemCategory(category.itemCategoryID);
      
      if (response.success) {
        message.success('商品分類刪除成功');
        onDataChange();
        
        // 如果刪除的是當前選中的分類，重置表單
        if (selectedCategory?.itemCategoryID === category.itemCategoryID) {
          resetForm();
        }
      } else {
        message.error(response.message || '刪除失敗');
      }
    } catch (error) {
      console.error('❌ 刪除商品分類時發生錯誤:', error);
      message.error('刪除失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      let response;

      if (operationMode === 'edit' && selectedCategory) {
        // 編輯模式
        response = await editItemCategory({
          itemCategoryID: selectedCategory.itemCategoryID,
          name: values.name,
          description: values.description || '',
          parentID: values.parentID || null,
          sortCode: values.sortCode || 0,
        });
      } else {
        // 新增模式 (包括新增子分類)
        response = await addItemCategory({
          name: values.name,
          description: values.description || '',
          parentID: values.parentID || null,
          sortCode: values.sortCode || 0,
        });
      }

      if (response.success) {
        const action = operationMode === 'edit' ? '更新' : '新增';
        message.success(`商品分類${action}成功`);
        onDataChange();
        resetForm();
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('❌ 提交商品分類時發生錯誤:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 切換分類展開/折疊
  const toggleCategoryExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // 初始化展開狀態（預設展開第一層分類）
  useEffect(() => {
    if (visible && categories && Array.isArray(categories) && categories.length > 0) {
      try {
        const topLevelCategories = categories.filter(cat =>
          cat && typeof cat === 'object' && cat.itemCategoryID && !cat.parentID
        );
        const topLevelIds = new Set(topLevelCategories.map(cat => cat.itemCategoryID));
        setExpandedCategories(topLevelIds);
        setIsAllExpanded(false);
      } catch (error) {
        console.error('❌ ItemCategoryManagement: 初始化展開狀態時發生錯誤:', error);
        setExpandedCategories(new Set());
        setIsAllExpanded(false);
      }
    }
  }, [visible, categories]);

  // 當互動視窗打開時，重置表單並設置焦點
  useEffect(() => {
    if (visible) {
      resetForm();
      setTimeout(() => {
        nameInputRef.current?.focus();
      }, 100);
    }
  }, [visible]);

  // 渲染分類項目
  const renderCategoryItem = (category: ItemCategory, level: number = 0): React.ReactNode => {
    if (!category || !category.itemCategoryID) {
      return null;
    }

    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories.has(category.itemCategoryID);
    const indentStyle = { paddingLeft: `${level * 20}px` };

    return (
      <div key={category.itemCategoryID} style={{ marginBottom: '4px' }}>
        <div
          style={{
            ...indentStyle,
            padding: '8px 12px',
            backgroundColor: selectedCategory?.itemCategoryID === category.itemCategoryID ? '#e6f7ff' : '#fafafa',
            border: selectedCategory?.itemCategoryID === category.itemCategoryID ? '1px solid #1890ff' : '1px solid #d9d9d9',
            borderRadius: '4px',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          onClick={() => handleEdit(category)}
        >
          <Row justify="space-between" align="middle">
            <Col flex="auto">
              <Space size="small">
                {hasChildren && (
                  <Button
                    type="text"
                    size="small"
                    icon={isExpanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleCategoryExpansion(category.itemCategoryID);
                    }}
                    style={{ padding: '0', width: '16px', height: '16px' }}
                  />
                )}
                {!hasChildren && <span style={{ width: '16px', display: 'inline-block' }} />}
                
                <span style={{ fontWeight: level === 0 ? 'bold' : 'normal' }}>
                  {category.name}
                </span>
                
                {category.description && (
                  <Tag color="blue" style={{ fontSize: '11px', padding: '0 4px', lineHeight: '16px' }}>
                    {category.description}
                  </Tag>
                )}
                
                <Badge count={category.sortCode} style={{ backgroundColor: '#52c41a' }} />
              </Space>
            </Col>
            
            <Col>
              <Space size="small">
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddChild(category)}
                  title="新增子分類"
                />
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(category)}
                  title="編輯分類"
                />
                <Popconfirm
                  title="確定要刪除此商品分類嗎？"
                  description="刪除後無法復原，請確認。"
                  onConfirm={() => handleDelete(category)}
                  okText="確定"
                  cancelText="取消"
                >
                  <Button
                    type="link"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                    title="刪除分類"
                  />
                </Popconfirm>
              </Space>
            </Col>
          </Row>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {category.children!.map(child => renderCategoryItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <InboxOutlined style={{ color: '#1890ff' }} />
          <span>商品分類管理</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={isMobile ? '95%' : 1200}
      style={{ top: isMobile ? 20 : 50 }}
      destroyOnClose
    >
      <Row gutter={[16, 16]}>
        {/* 左側：分類樹狀結構 */}
        <Col span={isMobile ? 24 : 14}>
          <Card
            title={
              <Space>
                <ApartmentOutlined />
                <span>分類結構</span>
                <Badge count={categories.length} style={{ backgroundColor: '#1890ff' }} />
              </Space>
            }
            extra={
              <Button
                type="text"
                size="small"
                icon={isAllExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                onClick={toggleAllExpansion}
              >
                {isAllExpanded ? '全部折疊' : '全部展開'}
              </Button>
            }
            bodyStyle={{ padding: '12px', maxHeight: '60vh', overflowY: 'auto' }}
          >
            {categories.length === 0 ? (
              <Alert
                message="尚無商品分類"
                description="點擊右側「新增分類」按鈕開始建立商品分類結構。"
                type="info"
                showIcon
              />
            ) : (
              <div>
                {buildCategoryTree(categories).map(category => renderCategoryItem(category))}
              </div>
            )}
          </Card>
        </Col>

        {/* 右側：操作表單 */}
        <Col span={isMobile ? 24 : 10}>
          <Card
            title={
              <Space>
                <NodeIndexOutlined />
                <span>
                  {operationMode === 'edit' ? '編輯分類' : 
                   operationMode === 'addChild' ? '新增子分類' : '新增分類'}
                </span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                size="small"
              >
                新增分類
              </Button>
            }
            bodyStyle={{ padding: '16px' }}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              autoComplete="off"
            >
              {operationMode === 'addChild' && parentCategoryForChild && (
                <Alert
                  message={`正在為「${parentCategoryForChild.name}」新增子分類`}
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}

              <Form.Item
                label="分類名稱"
                name="name"
                rules={[
                  { required: true, message: '請輸入分類名稱' },
                  { max: 100, message: '分類名稱不能超過100個字元' }
                ]}
              >
                <Input
                  ref={nameInputRef}
                  placeholder="請輸入分類名稱"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="分類描述"
                name="description"
                rules={[
                  { max: 500, message: '分類描述不能超過500個字元' }
                ]}
              >
                <Input.TextArea
                  placeholder="請輸入分類描述（選填）"
                  rows={3}
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="父分類"
                name="parentID"
                tooltip="選擇父分類可建立階層結構，留空則為頂層分類"
              >
                <TreeSelect
                  placeholder="請選擇父分類（選填）"
                  allowClear
                  showSearch
                  treeData={categoryTreeData}
                  treeDefaultExpandAll={false}
                  treeNodeFilterProp="title"
                  size="large"
                  disabled={operationMode === 'addChild'}
                />
              </Form.Item>

              <Form.Item
                label="排序代碼"
                name="sortCode"
                tooltip="數字越小排序越前面，相同時按名稱排序"
                initialValue={0}
              >
                <Input
                  type="number"
                  placeholder="請輸入排序代碼"
                  size="large"
                  min={0}
                  max={999999}
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                    size="large"
                  >
                    {operationMode === 'edit' ? '更新分類' : '新增分類'}
                  </Button>
                  <Button
                    onClick={resetForm}
                    icon={<UndoOutlined />}
                    size="large"
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default ItemCategoryManagement;
