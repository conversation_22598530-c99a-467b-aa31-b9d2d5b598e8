using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class SuspendService : ISuspendService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly EmployeeClass _employeeClass;
        private readonly ICurrentUserService _currentUserService;

        public SuspendService(
            ERPDbContext context,
            Baseform baseform,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
        }

        public async Task<List<SuspendDTO>> GetSuspendListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Suspend
                    .Where(s => s.userId == userId && s.IsDeleted != true)
                    .OrderByDescending(s => s.suspendDate)
                    .Select(s => new SuspendDTO
                    {
                        uid = s.uid,
                        userId = s.userId,
                        suspendType = s.suspendType,
                        suspendTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_SuspendType, s.suspendType),
                        suspendKind = s.suspendKind,
                        suspendKindName = _employeeClass.GetlistCompareName(_employeeClass.list_SuspendKind, s.suspendKind),
                        suspendReason = s.suspendReason,
                        suspendDate = _baseform.TimestampToDateStr(s.suspendDate),
                        approveDate = _baseform.TimestampToDateStr(s.approveDate),
                        approveNumber = s.approveNumber,
                        remark = s.remark,
                        UpdateTime = s.UpdateTime
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得留職停薪資料錯誤", ex);
            }
        }

        public async Task<SuspendDTO> GetSuspendDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Suspend
                    .Where(s => s.uid == uid && s.IsDeleted != true)
                    .Select(s => new SuspendDTO
                    {
                        uid = s.uid,
                        userId = s.userId,
                        suspendType = s.suspendType,
                        suspendTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_SuspendType, s.suspendType),
                        suspendKind = s.suspendKind,
                        suspendKindName = _employeeClass.GetlistCompareName(_employeeClass.list_SuspendKind, s.suspendKind),
                        suspendReason = s.suspendReason,
                        suspendDate = _baseform.TimestampToDateStr(s.suspendDate),
                        approveDate = _baseform.TimestampToDateStr(s.approveDate),
                        approveNumber = s.approveNumber,
                        remark = s.remark,
                        UpdateTime = s.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得留職停薪明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddSuspendAsync(SuspendDTO data)
        {
            var list_msg_check = CheckSuspendInput(data, "add");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newSuspend = new Suspend
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    suspendType = data.suspendType,
                    suspendKind = data.suspendKind,
                    suspendReason = data.suspendReason,
                    suspendDate = _baseform.DateStrToTimestamp(data.suspendDate),
                    approveDate = _baseform.DateStrToTimestamp(data.approveDate),
                    approveNumber = data.approveNumber,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Suspend.AddAsync(newSuspend);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "留職停薪資料登錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"留職停薪資料登錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditSuspendAsync(SuspendDTO data)
        {
            var list_msg_check = CheckSuspendInput(data, "edit");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Suspend.FirstOrDefaultAsync(s => s.uid == data.uid && s.IsDeleted != true);
                if (exist == null)
                    return (false, "找不到對應的留職停薪資料");

                exist.suspendType = data.suspendType;
                exist.suspendKind = data.suspendKind;
                exist.suspendReason = data.suspendReason;
                exist.suspendDate = _baseform.DateStrToTimestamp(data.suspendDate);
                exist.approveDate = _baseform.DateStrToTimestamp(data.approveDate);
                exist.approveNumber = data.approveNumber;
                exist.remark = data.remark;
                exist.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                exist.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯留職停薪資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯留職停薪資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteSuspendAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Suspend.FirstOrDefaultAsync(s => s.uid == uid && s.IsDeleted != true);
                if (exist == null)
                    return (false, "資料已刪除或不存在");

                exist.IsDeleted = true;
                exist.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                exist.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除留職停薪資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除留職停薪資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckSuspendInput(SuspendDTO data, string mode)
        {
            var list = new List<string>();

            if (string.IsNullOrWhiteSpace(data.suspendType))
                list.Add("請選擇留職停薪類型");

            if (string.IsNullOrWhiteSpace(data.suspendKind))
                list.Add("請選擇留職停薪種類");

            if (!_baseform.IsValidDateOrEmpty(data.suspendDate))
                list.Add("留停日期格式錯誤");

            if (!string.IsNullOrEmpty(data.approveDate) && !_baseform.IsValidDateOrEmpty(data.approveDate))
                list.Add("核准日期格式錯誤");

            return list;
        }
    }
}
