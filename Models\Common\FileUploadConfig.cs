using System;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 檔案上傳配置類別
    /// </summary>
    public static class FileUploadConfig
    {
        /// <summary>
        /// 檔案分類常數
        /// </summary>
        public static class FileCategories
        {
            public const string Document = "document";      // 文件
            public const string Image = "image";           // 圖片
            public const string Attachment = "attachment"; // 附件
            public const string Archive = "archive";       // 壓縮檔
            public const string Media = "media";           // 媒體檔案
            public const string Backup = "backup";         // 備份檔案
            public const string Template = "template";     // 範本檔案
            public const string Report = "report";         // 報表檔案
        }

        /// <summary>
        /// 存取權限常數
        /// </summary>
        public static class AccessLevels
        {
            public const string Public = "Public";         // 公開
            public const string Private = "Private";       // 私人
            public const string Restricted = "Restricted"; // 限制存取
            public const string Internal = "Internal";     // 內部使用
        }

        /// <summary>
        /// 檔案狀態常數
        /// </summary>
        public static class FileStatuses
        {
            public const string Active = "Active";         // 啟用
            public const string Archived = "Archived";     // 封存
            public const string Deleted = "Deleted";       // 已刪除
            public const string Processing = "Processing"; // 處理中
            public const string Error = "Error";           // 錯誤
        }

        /// <summary>
        /// 來源模組常數
        /// </summary>
        public static class SourceModules
        {
            public const string IMS = "IMS";               // 進銷存管理系統
            public const string PMS = "PMS";               // 財產管理系統
            public const string PAS = "PAS";               // 人事薪資管理系統
            public const string Common = "Common";         // 共用模組
            public const string System = "System";         // 系統模組
        }

        /// <summary>
        /// 預設檔案類型限制
        /// </summary>
        public static class DefaultFileTypes
        {
            /// <summary>
            /// 文件類型
            /// </summary>
            public static readonly string[] Documents = {
                ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                ".txt", ".rtf", ".odt", ".ods", ".odp"
            };

            /// <summary>
            /// 圖片類型
            /// </summary>
            public static readonly string[] Images = {
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif",
                ".webp", ".svg", ".ico"
            };

            /// <summary>
            /// 壓縮檔類型
            /// </summary>
            public static readonly string[] Archives = {
                ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2"
            };

            /// <summary>
            /// 媒體檔案類型
            /// </summary>
            public static readonly string[] Media = {
                ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv",
                ".mp3", ".wav", ".flac", ".aac", ".ogg"
            };

            /// <summary>
            /// 資料檔案類型
            /// </summary>
            public static readonly string[] Data = {
                ".csv", ".xml", ".json", ".sql", ".db", ".mdb"
            };

            /// <summary>
            /// 所有允許的檔案類型
            /// </summary>
            public static readonly string[] All = Documents
                .Concat(Images)
                .Concat(Archives)
                .Concat(Media)
                .Concat(Data)
                .ToArray();
        }

        /// <summary>
        /// 檔案大小限制（MB）
        /// </summary>
        public static class FileSizeLimits
        {
            public const int Document = 10;    // 文件：10MB
            public const int Image = 5;        // 圖片：5MB
            public const int Archive = 50;     // 壓縮檔：50MB
            public const int Media = 100;      // 媒體檔案：100MB
            public const int Default = 10;     // 預設：10MB
        }

        /// <summary>
        /// 檔案儲存路徑配置
        /// </summary>
        public static class StoragePaths
        {
            public const string BaseUploadPath = "uploads";
            public const string TempPath = "temp";
            public const string BackupPath = "backup";
            public const string ArchivePath = "archive";
        }

        /// <summary>
        /// 根據檔案分類取得允許的檔案類型
        /// </summary>
        /// <param name="fileCategory">檔案分類</param>
        /// <returns>允許的檔案類型陣列</returns>
        public static string[] GetAllowedFileTypes(string fileCategory)
        {
            return fileCategory?.ToLowerInvariant() switch
            {
                FileCategories.Document => DefaultFileTypes.Documents,
                FileCategories.Image => DefaultFileTypes.Images,
                FileCategories.Archive => DefaultFileTypes.Archives,
                FileCategories.Media => DefaultFileTypes.Media,
                _ => DefaultFileTypes.All
            };
        }

        /// <summary>
        /// 根據檔案分類取得檔案大小限制
        /// </summary>
        /// <param name="fileCategory">檔案分類</param>
        /// <returns>檔案大小限制（MB）</returns>
        public static int GetFileSizeLimit(string fileCategory)
        {
            return fileCategory?.ToLowerInvariant() switch
            {
                FileCategories.Document => FileSizeLimits.Document,
                FileCategories.Image => FileSizeLimits.Image,
                FileCategories.Archive => FileSizeLimits.Archive,
                FileCategories.Media => FileSizeLimits.Media,
                _ => FileSizeLimits.Default
            };
        }

        /// <summary>
        /// 根據副檔名判斷檔案分類
        /// </summary>
        /// <param name="fileExtension">副檔名</param>
        /// <returns>檔案分類</returns>
        public static string GetFileCategoryByExtension(string fileExtension)
        {
            var ext = fileExtension?.ToLowerInvariant();
            
            if (DefaultFileTypes.Documents.Contains(ext))
                return FileCategories.Document;
            
            if (DefaultFileTypes.Images.Contains(ext))
                return FileCategories.Image;
            
            if (DefaultFileTypes.Archives.Contains(ext))
                return FileCategories.Archive;
            
            if (DefaultFileTypes.Media.Contains(ext))
                return FileCategories.Media;
            
            return FileCategories.Document; // 預設為文件類型
        }

        /// <summary>
        /// 檢查檔案類型是否被允許
        /// </summary>
        /// <param name="fileExtension">副檔名</param>
        /// <param name="allowedTypes">允許的檔案類型</param>
        /// <returns>是否被允許</returns>
        public static bool IsFileTypeAllowed(string fileExtension, string[] allowedTypes = null)
        {
            var ext = fileExtension?.ToLowerInvariant();
            var allowed = allowedTypes ?? DefaultFileTypes.All;
            return allowed.Contains(ext);
        }

        /// <summary>
        /// 格式化檔案大小顯示
        /// </summary>
        /// <param name="sizeInBytes">檔案大小（bytes）</param>
        /// <returns>格式化後的檔案大小字串</returns>
        public static string FormatFileSize(long sizeInBytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = sizeInBytes;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 產生檔案儲存的相對路徑
        /// </summary>
        /// <param name="enterpriseGroupsId">企業群組編號</param>
        /// <param name="fileCategory">檔案分類</param>
        /// <param name="sourceModule">來源模組</param>
        /// <returns>相對路徑</returns>
        public static string GenerateStorageRelativePath(
            string enterpriseGroupsId, 
            string fileCategory, 
            string sourceModule = "")
        {
            var year = DateTime.Now.Year.ToString();
            var month = DateTime.Now.Month.ToString("D2");
            
            var pathParts = new List<string>
            {
                StoragePaths.BaseUploadPath,
                enterpriseGroupsId,
                fileCategory
            };
            
            if (!string.IsNullOrEmpty(sourceModule))
            {
                pathParts.Add(sourceModule.ToLowerInvariant());
            }
            
            pathParts.AddRange(new[] { year, month });
            
            return string.Join("/", pathParts);
        }
    }
}
