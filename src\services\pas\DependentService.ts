// Dependent 眷屬健保資料管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 眷屬健保資料型別
export interface Dependent {
    uid: string;
    userId: string;
    dependentRocId: string;
    dependentName: string;
    dependentBirthday: string; // timestamp 格式字串
    dependentRelationType: string;
    dependentRelationTypeName: string;
    remark: string;
}

// 建立空的眷屬健保資料
export const createEmptyDependent = (): Dependent => ({
    uid: '',
    userId: '',
    dependentRocId: '',
    dependentName: '',
    dependentBirthday: '',
    dependentRelationType: '',
    dependentRelationTypeName: '',
    remark: '',
});

// 取得眷屬健保資料列表
export async function getDependentList(userId: string): Promise<ApiResponse<Dependent[]>> {
    return await httpClient(`${apiEndpoints.getDependentList}/${userId}`, {
        method: "GET",
    });
}

// 取得眷屬健保資料明細
export async function getDependentDetail(uid: string): Promise<ApiResponse<Dependent>> {
    return await httpClient(`${apiEndpoints.getDependentDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增眷屬健保資料
export async function addDependent(data: Partial<Dependent>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addDependent, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增扶養資料失敗",
        };
    }
}

// 編輯眷屬健保資料
export async function editDependent(data: Partial<Dependent>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editDependent, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯扶養資料失敗",
        };
    }
}

// 刪除眷屬健保資料
export async function deleteDependent(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteDependent, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除扶養資料失敗",
        };
    }
}
