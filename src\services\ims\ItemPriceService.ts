import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";

// 庫存品價格介面 - 根據實際API回應更新
export interface ItemPrice {
    itemPriceID: string;
    itemID: string;
    priceTypeID: string;
    price: number;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

export const createEmptyItemPrice = (): Partial<ItemPrice> => ({
    itemID: '',
    priceTypeID: '',
    price: 0,
});

// 取得庫存品價格列表
export async function getItemPriceList(): Promise<ApiResponse<ItemPrice[]>> {
    try {
        console.log('🔄 ItemPriceService: 開始載入商品價格列表...');
        const response = await httpClient(apiEndpoints.getItemPriceList, {
            method: "GET",
        });

        console.log(`✅ ItemPriceService: API回應完成`, response);
        return response;
    } catch (error: any) {
        console.error('❌ ItemPriceService: 載入商品價格列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得庫存品價格列表失敗",
            data: [],
        };
    }
}

// 取得單一庫存品價格
export async function getItemPrice(itemPriceId: string): Promise<ApiResponse<ItemPrice>> {
    try {
        const response = await httpClient(`${apiEndpoints.getItemPrice}?ItemPriceID=${itemPriceId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得庫存品價格失敗",
            data: undefined,
        };
    }
}

// 新增庫存品價格
export async function addItemPrice(itemPrice: Partial<ItemPrice>): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addItemPrice, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(itemPrice),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增庫存品價格失敗",
        };
    }
}

// 修改庫存品價格
export async function editItemPrice(itemPrice: Partial<ItemPrice>): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editItemPrice, {
            method: "PATCH",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(itemPrice),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "修改庫存品價格失敗",
        };
    }
}

// 刪除庫存品價格
export async function deleteItemPrice(itemPriceId: string): Promise<ApiResponse<any>> {
    try {
        const itemPrice = { itemPriceID: itemPriceId };
        const response = await httpClient(apiEndpoints.deleteItemPrice, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(itemPrice),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除庫存品價格失敗",
        };
    }
}
