using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetStatusService
    {
        /// <summary>
        /// 取得財產狀態資料
        /// </summary>
        /// <param name="_assetStatusId">財產狀態編號</param>
        /// <returns>財產狀態資料列表</returns>
        Task<List<AssetStatusDTO>> GetAssetStatusAsync(string _assetStatusId = "");

        /// <summary>
        /// 新增財產狀態
        /// </summary>
        /// <param name="_data">財產狀態資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddAssetStatusAsync(AssetStatusDTO _data);

        /// <summary>
        /// 編輯財產狀態
        /// </summary>
        /// <param name="_data">財產狀態資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditAssetStatusAsync(AssetStatusDTO _data);

        /// <summary>
        /// 刪除財產狀態
        /// </summary>
        /// <param name="_data">財產狀態資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteAssetStatusAsync(AssetStatusDTO _data);

        /// <summary>
        /// 取得財產狀態詳細資料
        /// </summary>
        /// <param name="_assetStatusId">財產狀態編號</param>
        /// <returns>財產狀態詳細資料</returns>
        Task<AssetStatusDTO> GetAssetStatusDetailAsync(string _assetStatusId);
    }
}