/**
 * 使用重構後的API測試工具的範例
 * 展示如何使用通用、可重用的API測試和驗證工具
 */

import { 
  testApiEndpoint, 
  testMultipleApis, 
  createTestConfig, 
  createDefaultValidators 
} from './apiTestUtils';

import { 
  processApiResponse, 
  createValidationConfig, 
  createExtractionConfig,
  processBatchApiResponses 
} from './dataValidation';

// 範例：如何使用重構後的工具測試IMS API
export const testImsApisExample = async () => {
  // 動態導入API函數（避免硬編碼依賴）
  const { getItemList, getItem } = await import('@/services/ims/ItemService');
  const { getItemCategoryList } = await import('@/services/ims/ItemCategoryService');
  const { getPriceTypeList } = await import('@/services/ims/PriceTypeService');
  const { getItemPriceList } = await import('@/services/ims/ItemPriceService');

  // 創建測試配置
  const testConfigs = [
    createTestConfig(
      getItemList,
      '商品列表',
      '商品',
      ['ItemID', 'Name', 'CustomNO'],
      (data) => {
        const errors: string[] = [];
        if (!Array.isArray(data)) errors.push('資料不是陣列');
        return { isValid: errors.length === 0, errors };
      }
    ),
    createTestConfig(
      getItemCategoryList,
      '商品分類列表',
      '分類',
      ['ItemCategoryID', 'Name']
    ),
    createTestConfig(
      getPriceTypeList,
      '價格類型列表',
      '價格類型',
      ['PriceTypeID', 'Name']
    ),
    createTestConfig(
      getItemPriceList,
      '商品價格列表',
      '商品價格',
      ['ItemPriceID', 'ItemID', 'PriceTypeID']
    )
  ];

  // 執行批量測試
  const result = await testMultipleApis(testConfigs, {
    parallel: true,
    includeDetailTest: true,
    detailTestConfig: {
      listEndpointName: '商品列表',
      detailApiFunction: getItem,
      detailEndpointName: '單個商品查詢',
      idField: 'ItemID'
    }
  });

  console.log('🎯 IMS API測試結果:', result);
  return result;
};

// 範例：如何使用重構後的資料驗證工具
export const processImsDataExample = async () => {
  // 動態導入API函數
  const { getItemList } = await import('@/services/ims/ItemService');

  try {
    // 獲取API回應
    const response = await getItemList();

    // 創建驗證配置
    const validationConfig = createValidationConfig({
      required: ['ItemID', 'Name'],
      optional: ['CustomNO', 'Description'],
      customRules: (item, index) => ({
        isValid: true,
        errors: [],
        warnings: item.Name?.length < 2 ? [`項目 ${index} 名稱過短`] : []
      }),
      allowEmpty: true,
      strict: false
    });

    // 創建提取配置
    const extractionConfig = createExtractionConfig({
      mainPath: ['data'],
      fallbacks: [['data', 'data'], ['result']],
      allowNonArray: false
    });

    // 處理API回應
    const result = processApiResponse(response, '商品', {
      validationConfig,
      extractionConfig,
      errorHandling: {
        logErrors: true,
        throwOnError: false,
        customErrorMessage: '處理商品資料時發生錯誤'
      }
    });

    console.log('🎯 商品資料處理結果:', result);
    return result;

  } catch (error) {
    console.error('❌ 處理商品資料失敗:', error);
    return { success: false, data: [], message: '處理失敗' };
  }
};

// 範例：批量處理多個API回應
export const processBatchImsDataExample = async () => {
  // 動態導入API函數
  const { getItemList } = await import('@/services/ims/ItemService');
  const { getItemCategoryList } = await import('@/services/ims/ItemCategoryService');
  const { getPriceTypeList } = await import('@/services/ims/PriceTypeService');

  // 創建API請求陣列
  const apiRequests = [
    getItemList(),
    getItemCategoryList(),
    getPriceTypeList()
  ];

  // 創建資料類型陣列
  const dataTypes = ['商品', '分類', '價格類型'];

  // 創建驗證配置陣列
  const validationConfigs: any[] = [
    createValidationConfig({ required: ['ItemID', 'Name'] }),
    createValidationConfig({ required: ['ItemCategoryID', 'Name'] }),
    createValidationConfig({ required: ['PriceTypeID', 'Name'] })
  ];

  // 批量處理
  const results = await processBatchApiResponses(
    apiRequests,
    dataTypes,
    {
      validationConfigs,
      parallel: true
    }
  );

  console.log('🎯 批量處理結果:', results);
  return results;
};

// 範例：如何為新的API創建測試配置
export const createCustomApiTestExample = () => {
  // 假設有一個新的API函數
  const customApiFunction = async () => {
    // 模擬API回應
    return {
      success: true,
      data: [
        { id: 1, name: '測試項目1', status: 'active' },
        { id: 2, name: '測試項目2', status: 'inactive' }
      ]
    };
  };

  // 創建自定義驗證器
  const customValidator = (data: any) => {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(data)) {
      errors.push('資料不是陣列格式');
      return { isValid: false, errors };
    }

    data.forEach((item, index) => {
      if (!item.id) errors.push(`項目 ${index} 缺少 id`);
      if (!item.name) errors.push(`項目 ${index} 缺少 name`);
      if (item.status && !['active', 'inactive'].includes(item.status)) {
        warnings.push(`項目 ${index} 狀態值異常: ${item.status}`);
      }
    });

    return { isValid: errors.length === 0, errors };
  };

  // 創建測試配置
  const testConfig = createTestConfig(
    customApiFunction,
    '自定義API測試',
    '自定義資料',
    ['id', 'name'],
    customValidator
  );

  return testConfig;
};

// 導出所有範例函數
export const apiTestExamples = {
  testImsApis: testImsApisExample,
  processImsData: processImsDataExample,
  processBatchImsData: processBatchImsDataExample,
  createCustomApiTest: createCustomApiTestExample
};

// 在開發環境中掛載範例函數
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).apiTestExamples = apiTestExamples;
  console.log('🛠️ API測試範例已載入，可在控制台中使用 apiTestExamples');
}
