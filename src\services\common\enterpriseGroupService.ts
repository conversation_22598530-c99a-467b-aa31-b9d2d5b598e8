import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 公司資訊
export interface EnterpriseGroup {
    enterpriseGroupsId: string;
    name: string;
    unifiedNumber: string;
    representative: string;
    establishDate: number;
    accountingPeriod: string;
    companyPhone: string;
    phone: string;
    email: string;
    mobilePhone: string;
    fax: string;
    website: string;
    address1: string;
    address2: string;
    englishName: string;
    englishAddress: string;
    sortCode: number;
    createTime: number | null;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string;
    deleteTime: number | null;
    deleteUserId: string;
}

// 獲取公司列表
export async function getEnterpriseGroups(): Promise<ApiResponse<EnterpriseGroup[]>> {
    try {
        const response = await httpClient(apiEndpoints.getEnterpriseGroups, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取公司列表失敗",
            data: []
        };
    }
}

// 獲取單一公司資訊
export async function getEnterpriseGroupDetail(id: string): Promise<ApiResponse<EnterpriseGroup>> {
    try {
        const response = await httpClient(`${apiEndpoints.getEnterpriseGroupDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取公司資訊失敗",
        };
    }
}

// 新增公司資訊
export async function createEnterpriseGroup(data: Partial<EnterpriseGroup>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addEnterpriseGroup, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增公司資訊失敗",
        };
    }
}

// 更新公司資訊
export async function updateEnterpriseGroup(data: Partial<EnterpriseGroup>): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.editEnterpriseGroup}`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新公司資訊失敗",
        };
    }
}

// 刪除公司資訊
export async function deleteEnterpriseGroup(data: Partial<EnterpriseGroup>): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.deleteEnterpriseGroup}`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除公司資訊失敗",
        };
    }
}