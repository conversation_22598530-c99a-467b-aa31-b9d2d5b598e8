'use client';

import { Table, Spin, message, DatePicker, Button, Space, Modal } from 'antd';
import { useEffect, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { getPerformancePointSummary, PerformancePointSummaryItem } from '@/services/pas/PerformancePoint/PerformancePointRecordService';
import PerformancePointRecordInfo from '@/app/pas/employee_main/employee_modules/performancePointRecord/PerformancePointRecordInfo'; // 匯入

const { RangePicker } = DatePicker;

const PerformancePointSummary = () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<PerformancePointSummaryItem[]>([]);

    const currentYear = dayjs().year();
    const defaultRange: [Dayjs, Dayjs] = [
        dayjs(`${currentYear}-01-01`),
        dayjs(`${currentYear}-12-31`)
    ];
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>(defaultRange);

    // 分頁狀態
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [selectedUserName, setSelectedUserName] = useState<string | null>(null);

    const handleRowClick = (record: PerformancePointSummaryItem) => {
        setSelectedUserId(record.userId);
        setSelectedUserName(record.userName); // 儲存員工姓名
        setModalOpen(true);
    };

    useEffect(() => {
        fetchSummary();
    }, []);

    // 每次 modal 關閉都重新抓資料
    useEffect(() => {
        if (!modalOpen) {
            fetchSummary();
        }
    }, [modalOpen]);

    const fetchSummary = async () => {
        try {
            setLoading(true);
            const startDate = dateRange[0].format('YYYY-MM-DD');
            const endDate = dateRange[1].format('YYYY-MM-DD');
            const groupUid = '';

            const res = await getPerformancePointSummary(groupUid, startDate, endDate);
            if (res.success && res.data) {
                setData(res.data);
            } else {
                message.error(res.message || '讀取點數總表失敗');
            }
        } catch (error) {
            console.error('fetchSummary error:', error);
            message.error('無法連線至伺服器，請稍後再試');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Space style={{ marginBottom: 16 }}>
                <RangePicker
                    value={dateRange}
                    onChange={(dates) => {
                        if (dates) setDateRange(dates as [Dayjs, Dayjs]);
                    }}
                    format="YYYY-MM-DD"
                />
                <Button type="primary" onClick={fetchSummary}>
                    查詢
                </Button>
            </Space>
            <Spin spinning={loading}>
                <Table
                    columns={[
                        {
                            title: '名次',
                            key: 'rank',
                            render: (_text, _record, index) => index + 1, // 直接用 index 顯示名次
                        },
                        { title: '員工姓名', dataIndex: 'userName', key: 'userName' },
                        { title: '總點數', dataIndex: 'totalPoint', key: 'totalPoint' }
                    ]}
                    dataSource={data}
                    rowKey="userId"
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record),
                    })}
                />
            </Spin>

            <Modal
                title={`${selectedUserName ?? ''}`}
                open={modalOpen}
                onCancel={() => {
                    setModalOpen(false);
                    setSelectedUserId(null);
                    setSelectedUserName(null);
                }}
                footer={null}
                width={1080} // 可調整寬度
                destroyOnClose
            >
                {selectedUserId && (
                    <PerformancePointRecordInfo userId={selectedUserId} active={modalOpen} />
                )}
            </Modal>

        </>
    );
};

export default PerformancePointSummary;
