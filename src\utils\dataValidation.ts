/**
 * 通用資料驗證和處理工具函數
 * 重構為更通用、可重用的工具函數，不依賴特定API結構
 */

import { ApiResponse } from "@/config/api";

// 通用驗證配置介面
export interface ValidationConfig<T = any> {
  requiredFields?: (keyof T)[];
  optionalFields?: (keyof T)[];
  customValidator?: (item: T, index: number) => ValidationResult;
  allowEmpty?: boolean;
  strictMode?: boolean;
}

// 驗證結果介面
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 通用資料驗證函數 - 重構版
export const validateApiData = <T>(
  data: any,
  dataType: string,
  config: ValidationConfig<T> = {}
): T[] => {
  const {
    requiredFields = [],
    optionalFields = [],
    customValidator,
    allowEmpty = true,
    strictMode = false
  } = config;

  if (!data) {
    console.warn(`⚠️ ${dataType} 資料為空:`, data);
    return [];
  }

  if (!Array.isArray(data)) {
    console.warn(`⚠️ ${dataType} 資料不是陣列:`, typeof data, data);
    return [];
  }

  if (data.length === 0 && !allowEmpty) {
    console.warn(`⚠️ ${dataType} 資料陣列為空且不允許空陣列`);
    return [];
  }

  // 過濾和驗證資料項目
  const validData: T[] = [];
  const invalidItems: { index: number; reason: string }[] = [];

  data.forEach((item, index) => {
    if (!item || typeof item !== 'object' || Array.isArray(item)) {
      invalidItems.push({ index, reason: '不是有效物件' });
      return;
    }

    // 檢查必要欄位
    const missingRequired = requiredFields.filter(field =>
      item[field] === undefined || item[field] === null || item[field] === ''
    );

    if (missingRequired.length > 0) {
      invalidItems.push({
        index,
        reason: `缺少必要欄位: ${missingRequired.join(', ')}`
      });
      if (strictMode) return;
    }

    // 檢查可選欄位（僅警告）
    const missingOptional = optionalFields.filter(field =>
      item[field] === undefined || item[field] === null || item[field] === ''
    );

    if (missingOptional.length > 0) {
      console.warn(`⚠️ ${dataType} 項目 ${index} 缺少可選欄位: ${missingOptional.join(', ')}`);
    }

    // 自定義驗證
    if (customValidator) {
      const customResult = customValidator(item, index);
      if (!customResult.isValid) {
        invalidItems.push({
          index,
          reason: `自定義驗證失敗: ${customResult.errors.join(', ')}`
        });
        if (strictMode) return;
      }
      if (customResult.warnings.length > 0) {
        console.warn(`⚠️ ${dataType} 項目 ${index} 警告: ${customResult.warnings.join(', ')}`);
      }
    }

    validData.push(item as T);
  });

  if (invalidItems.length > 0) {
    console.warn(`⚠️ ${dataType} 包含 ${invalidItems.length} 個無效項目:`, invalidItems);
  }

  console.log(`✅ ${dataType} 驗證完成: ${validData.length} 個有效項目`);
  return validData;
};

// 通用API回應資料提取器
export const extractApiData = (
  response: any,
  dataType: string,
  extractionConfig: {
    dataPath?: string[];
    fallbackPaths?: string[][];
    allowNonArray?: boolean;
  } = {}
): any => {
  const { dataPath = ['data'], fallbackPaths = [['data', 'data']], allowNonArray = false } = extractionConfig;

  if (!response.success) {
    console.warn(`⚠️ ${dataType} API 失敗:`, response.message);
    return [];
  }

  // 嘗試主要路徑
  let extractedData = response;
  for (const path of dataPath) {
    if (extractedData && typeof extractedData === 'object' && path in extractedData) {
      extractedData = extractedData[path];
    } else {
      extractedData = null;
      break;
    }
  }

  // 如果主要路徑失敗，嘗試備用路徑
  if (extractedData === null) {
    for (const fallbackPath of fallbackPaths) {
      let tempData = response;
      let pathValid = true;

      for (const path of fallbackPath) {
        if (tempData && typeof tempData === 'object' && path in tempData) {
          tempData = tempData[path];
        } else {
          pathValid = false;
          break;
        }
      }

      if (pathValid) {
        extractedData = tempData;
        break;
      }
    }
  }

  // 驗證提取的資料
  if (extractedData === null || extractedData === undefined) {
    console.warn(`⚠️ ${dataType} 無法提取資料，所有路徑都失敗`);
    return [];
  }

  if (Array.isArray(extractedData)) {
    return extractedData;
  }

  if (allowNonArray) {
    return extractedData;
  }

  console.warn(`⚠️ ${dataType} 提取的資料不是陣列格式:`, extractedData);
  return [];
};

// 通用API回應處理函數
export const processApiResponse = <T>(
  response: ApiResponse<T[] | any>,
  dataType: string,
  options: {
    validationConfig?: ValidationConfig<T>;
    extractionConfig?: {
      dataPath?: string[];
      fallbackPaths?: string[][];
      allowNonArray?: boolean;
    };
    errorHandling?: {
      logErrors?: boolean;
      throwOnError?: boolean;
      customErrorMessage?: string;
    };
  } = {}
): { success: boolean; data: T[]; message?: string; errors?: string[] } => {
  const {
    validationConfig = {},
    extractionConfig = {},
    errorHandling = { logErrors: true, throwOnError: false }
  } = options;

  try {
    if (!response.success) {
      const errorMessage = response.message || `載入${dataType}失敗`;
      if (errorHandling.logErrors) {
        console.warn(`⚠️ API回應失敗: ${errorMessage}`);
      }
      return {
        success: false,
        data: [],
        message: errorMessage,
        errors: [errorMessage]
      };
    }

    const extractedData = extractApiData(response, dataType, extractionConfig);
    const validatedData = validateApiData<T>(extractedData, dataType, validationConfig);

    return {
      success: true,
      data: validatedData,
      message: `成功載入 ${validatedData.length} 個${dataType}`
    };
  } catch (error: any) {
    const errorMessage = errorHandling.customErrorMessage || `處理${dataType}資料時發生錯誤`;

    if (errorHandling.logErrors) {
      console.error(`❌ ${errorMessage}:`, error);
    }

    if (errorHandling.throwOnError) {
      throw error;
    }

    return {
      success: false,
      data: [],
      message: errorMessage,
      errors: [error.message || errorMessage]
    };
  }
};

// 檢查物件是否有必要的屬性
export const hasRequiredProperties = (obj: any, requiredProps: string[]): boolean => {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    return requiredProps.every(prop => 
        obj.hasOwnProperty(prop) && 
        obj[prop] !== null && 
        obj[prop] !== undefined
    );
};

// 安全的字串處理
export const safeString = (value: any, defaultValue: string = '-'): string => {
    if (value === null || value === undefined || value === '') {
        return defaultValue;
    }
    return String(value);
};

// 安全的數字處理
export const safeNumber = (value: any, defaultValue: number = 0): number => {
    if (value === null || value === undefined || value === '') {
        return defaultValue;
    }
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
};

// 安全的布林值處理
export const safeBoolean = (value: any, defaultValue: boolean = false): boolean => {
    if (value === null || value === undefined) {
        return defaultValue;
    }
    if (typeof value === 'boolean') {
        return value;
    }
    if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
    }
    return Boolean(value);
};

// 格式化時間戳
export const formatTimestamp = (timestamp: number | null, defaultValue: string = '-'): string => {
    if (!timestamp) {
        return defaultValue;
    }
    
    try {
        const date = new Date(timestamp * 1000); // 假設是Unix時間戳
        return date.toLocaleString('zh-TW');
    } catch (error) {
        console.warn('時間格式化失敗:', timestamp, error);
        return defaultValue;
    }
};

// 深度複製物件
export const deepClone = <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime()) as unknown as T;
    }
    
    if (Array.isArray(obj)) {
        return obj.map(item => deepClone(item)) as unknown as T;
    }
    
    const cloned = {} as T;
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    
    return cloned;
};

// 防抖函數
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};

// 節流函數
export const throttle = <T extends (...args: any[]) => any>(
    func: T,
    limit: number
): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

// 錯誤邊界處理
export const withErrorBoundary = <T>(
    operation: () => T,
    fallback: T,
    errorMessage?: string
): T => {
    try {
        return operation();
    } catch (error) {
        console.error(errorMessage || '操作失敗:', error);
        return fallback;
    }
};

// 異步操作的錯誤處理 - 增強版
export const withAsyncErrorBoundary = async <T>(
  operation: () => Promise<T>,
  fallback: T,
  options: {
    errorMessage?: string;
    logError?: boolean;
    retryCount?: number;
    retryDelay?: number;
    onError?: (error: any, attempt: number) => void;
  } = {}
): Promise<T> => {
  const {
    errorMessage = '異步操作失敗',
    logError = true,
    retryCount = 0,
    retryDelay = 1000,
    onError
  } = options;

  let lastError: any;

  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (onError) {
        onError(error, attempt);
      }

      if (attempt < retryCount) {
        if (logError) {
          console.warn(`⚠️ ${errorMessage} (嘗試 ${attempt + 1}/${retryCount + 1}):`, error);
        }
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  if (logError) {
    console.error(`❌ ${errorMessage} (所有重試都失敗):`, lastError);
  }

  return fallback;
};

// 創建驗證配置的工具函數
export const createValidationConfig = <T>(config: {
  required?: (keyof T)[];
  optional?: (keyof T)[];
  customRules?: (item: T, index: number) => ValidationResult;
  allowEmpty?: boolean;
  strict?: boolean;
}): ValidationConfig<T> => ({
  requiredFields: config.required,
  optionalFields: config.optional,
  customValidator: config.customRules,
  allowEmpty: config.allowEmpty,
  strictMode: config.strict
});

// 創建提取配置的工具函數
export const createExtractionConfig = (config: {
  mainPath?: string[];
  fallbacks?: string[][];
  allowNonArray?: boolean;
}) => ({
  dataPath: config.mainPath,
  fallbackPaths: config.fallbacks,
  allowNonArray: config.allowNonArray
});

// 批量處理API回應
export const processBatchApiResponses = async <T>(
  responses: Promise<ApiResponse<T[] | any>>[],
  dataTypes: string[],
  options: {
    validationConfigs?: ValidationConfig<T>[];
    extractionConfigs?: any[];
    parallel?: boolean;
  } = {}
): Promise<Array<{ success: boolean; data: T[]; message?: string; errors?: string[] }>> => {
  const { validationConfigs = [], extractionConfigs = [], parallel = true } = options;

  if (parallel) {
    const resolvedResponses = await Promise.allSettled(responses);
    return resolvedResponses.map((result, index) => {
      if (result.status === 'fulfilled') {
        return processApiResponse<T>(
          result.value,
          dataTypes[index] || `資料${index}`,
          {
            validationConfig: validationConfigs[index],
            extractionConfig: extractionConfigs[index]
          }
        );
      } else {
        return {
          success: false,
          data: [],
          message: `處理${dataTypes[index] || `資料${index}`}時發生錯誤`,
          errors: [result.reason?.message || '未知錯誤']
        };
      }
    });
  } else {
    const results = [];
    for (let i = 0; i < responses.length; i++) {
      try {
        const response = await responses[i];
        results.push(processApiResponse<T>(
          response,
          dataTypes[i] || `資料${i}`,
          {
            validationConfig: validationConfigs[i],
            extractionConfig: extractionConfigs[i]
          }
        ));
      } catch (error: any) {
        results.push({
          success: false,
          data: [],
          message: `處理${dataTypes[i] || `資料${i}`}時發生錯誤`,
          errors: [error.message || '未知錯誤']
        });
      }
    }
    return results;
  }
};
