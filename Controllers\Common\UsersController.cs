﻿using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("使用者資料管理")]
    public class UsersController : ControllerBase
    {
        private readonly IUsersService _Interface;

        // 在建構子中注入多個依賴
        public UsersController(IUsersService common_Users_Interface)
        {
            _Interface = common_Users_Interface;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        [HttpGet]
        [Route("GetUsers")]
        [SwaggerOperation(Summary = "取得使用者列表", Description = "取得所有使用者資料")]
        public async Task<IActionResult> GetUsersList()
        {
            var users = await _Interface.GetUsersAsync();
            return Ok(users);
        }

        [HttpGet]
        [Route("GetMyInfo")]
        [SwaggerOperation(Summary = "取得個人資訊", Description = "取得個人資訊")]
        public async Task<IActionResult> GetMyInfo()
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var users = await _Interface.GetUsersAsync(tokenUid);
            return Ok(users);
        }

        [HttpGet]
        [Route("GetUsers/{_uid?}")]
        [SwaggerOperation(Summary = "取得使用者明細", Description = "依ID取得使用者明細")]
        public async Task<IActionResult> GetUsersDetail(string _uid)
        {
            var users = await _Interface.GetUsersAsync(_uid);
            return Ok(users);
        }

        [HttpPost]
        [Route("AddUsers")]
        [SwaggerOperation(Summary = "新增使用者", Description = "新增使用者資料")]
        public async Task<IActionResult> AddUsers([FromBody] UsersDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.AddUsersAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditUsers")]
        [SwaggerOperation(Summary = "編輯使用者", Description = "修改已存在之使用者資料")]
        public async Task<IActionResult> EditUsersDetail([FromBody] UsersDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.EditUsersAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteUsers")]
        [SwaggerOperation(Summary = "刪除使用者", Description = "刪除已存在之使用者資料")]
        public async Task<IActionResult> DeleteUsers([FromBody] UsersDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.DeleteUsersAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("ChangeUsersPassword")]
        [SwaggerOperation(Summary = "變更使用者密碼", Description = "變更使用者密碼")]
        public async Task<IActionResult> ChangeUsersPassword([FromBody] ChangeUsersPasswordDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.ChangeUsersPasswordAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }

    }

}
