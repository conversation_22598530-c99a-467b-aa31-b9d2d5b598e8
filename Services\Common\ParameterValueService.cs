using FAST_ERP_Backend.Interfaces.Common;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 參數值處理服務
    /// </summary>
    public class ParameterValueService : IParameterValueService
    {
        /// <summary>
        /// 從JSON字串中讀取指定屬性的值
        /// </summary>
        public T GetValueFromJson<T>(string json, string propertyName, T defaultValue = default)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                {
                    return defaultValue;
                }

                var jsonObject = JObject.Parse(json);
                var property = jsonObject[propertyName];

                if (property == null)
                {
                    return defaultValue;
                }

                return property.ToObject<T>();
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 更新JSON字串中的屬性值
        /// </summary>
        public string UpdateJsonProperty<T>(string json, string propertyName, T value)
        {
            try
            {
                JObject jsonObject;

                if (string.IsNullOrEmpty(json) || json == "{}")
                {
                    jsonObject = new JObject();
                }
                else
                {
                    jsonObject = JObject.Parse(json);
                }

                // 如果屬性存在，則更新
                if (jsonObject[propertyName] != null)
                {
                    jsonObject[propertyName] = JToken.FromObject(value);
                }
                // 否則新增屬性
                else
                {
                    jsonObject.Add(propertyName, JToken.FromObject(value));
                }

                return jsonObject.ToString(Formatting.None);
            }
            catch (Exception)
            {
                // 如果解析失敗，建立新的JSON物件
                var jsonObject = new JObject
                {
                    { propertyName, JToken.FromObject(value) }
                };

                return jsonObject.ToString(Formatting.None);
            }
        }

        /// <summary>
        /// 檢查JSON字串中是否包含指定屬性
        /// </summary>
        public bool ContainsProperty(string json, string propertyName)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                {
                    return false;
                }

                var jsonObject = JObject.Parse(json);
                return jsonObject[propertyName] != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 轉換數值為百分比顯示（如將0.4轉為40%）
        /// </summary>
        public decimal ConvertToPercentage(decimal decimalValue)
        {
            return decimalValue * 100m;
        }

        /// <summary>
        /// 轉換百分比為小數值（如將40%轉為0.4）
        /// </summary>
        public decimal ConvertFromPercentage(decimal percentageValue)
        {
            return percentageValue / 100m;
        }
    }
}