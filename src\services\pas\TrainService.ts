import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 訓練資料
export interface Train {
    uid: string;
    userId: string;
    courseName: string;
    cost: number;
    ranking: string;
    score: string;
    instructor: string;
    durationHours: string;
    trainingInstitute: string;
    courseStartDate: string;
    courseEndDate: string;
    certificateDate: string;
    certificateNumber: string;
    remark: string;
}

export const createEmptyTrain = (): Train => ({
    uid: '',
    userId: '',
    courseName: '',
    cost: 0,
    ranking: '',
    score: '',
    instructor: '',
    durationHours: '',
    trainingInstitute: '',
    courseStartDate: '',
    courseEndDate: '',
    certificateDate: '',
    certificateNumber: '',
    remark: '',
});

// 搜尋訓練列表
export async function getTrainList(userid: string): Promise<ApiResponse<Train[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getTrainList}/${userid}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋訓練資料失敗",
        };
    }
}

// 搜尋訓練明細
export async function getTrainDetail(uid: string): Promise<ApiResponse<Train>> {
    return await httpClient(`${apiEndpoints.getTrainDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增訓練資料
export async function addTrain(data: Partial<Train>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addTrain, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增訓練資料失敗",
        };
    }
}

// 編輯訓練資料
export async function editTrain(data: Partial<Train>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editTrain, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯訓練資料失敗",
        };
    }
}

// 刪除訓練資料
export async function deleteTrain(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteTrain, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除訓練資料失敗",
        };
    }
}
