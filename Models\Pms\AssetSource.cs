using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 資產來源
    /// </summary>
    public class AssetSource : ModelBaseEntity
    {
        [Key]
        [Comment("資產來源編號")]
        public Guid AssetSourceId { get; set; }//資產來源編號

        [Comment("資產來源名稱")]
        public string AssetSourceName { get; set; }//資產來源名稱

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        // 資產來源
        public virtual ICollection<AssetAssetSourceMapping> AssetAssetSources { get; set; }

        public AssetSource()
        {
            AssetSourceId = Guid.NewGuid();
            AssetSourceName = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            AssetAssetSources = new List<AssetAssetSourceMapping>();
        }
    }

    public class AssetSourceDTO
    {
        public Guid AssetSourceId { get; set; }//資產來源編號
        public string AssetSourceName { get; set; }//資產來源名稱
        public int SortCode { get; set; } // 排序號碼
        public long? CreateTime { get; set; } // 新增時間
        public string? CreateUserId { get; set; } // 新增者編號
        public string? CreateUserName { get; set; } // 新增者姓名
        public long? UpdateTime { get; set; } // 更新時間
        public string? UpdateUserId { get; set; } // 更新者編號
        public string? UpdateUserName { get; set; } // 更新者姓名
        public long? DeleteTime { get; set; } // 刪除時間
        public string? DeleteUserId { get; set; } // 刪除者編號
        public string? DeleteUserName { get; set; } // 刪除者姓名

        // 資產來源
        public List<AssetAssetSourceMapping> AssetAssetSources { get; set; }

        public AssetSourceDTO()
        {
            AssetSourceId = Guid.Empty;
            AssetSourceName = "";
            SortCode = 0;
            CreateTime = 0;
            CreateUserId = "";
            UpdateTime = 0;
            UpdateUserId = "";
            DeleteTime = 0;
            DeleteUserId = "";
            CreateUserName = "";
            UpdateUserName = "";
            DeleteUserName = "";
            AssetAssetSources = new List<AssetAssetSourceMapping>();
        }
    }
}