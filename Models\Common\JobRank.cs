﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FAST_ERP_Backend.Models.Common
{
    public class JobRankDTO
    {
        public string JobRankId { get; set; } //職級編號
        public string Name { get; set; } //職級名稱
        public int SortCode { get; set; } //排序編碼

        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態

        public JobRankDTO()
        {
            JobRankId = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class JobRank : ModelBaseEntity
    {
        [Key]
        [Comment("職級編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string JobRankId { get; set; } //職級編號

        [Comment("職級名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } //職級名稱

        [Comment("排序編碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } //排序編碼

        public JobRank()
        {
            JobRankId = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}