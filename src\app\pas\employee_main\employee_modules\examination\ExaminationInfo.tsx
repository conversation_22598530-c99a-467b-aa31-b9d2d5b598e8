import { useEffect, useState } from 'react';
import { Modal, Button, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Typography, Space, Divider } from 'antd';
import dayjs from 'dayjs';
import { getExaminationDetail, getExaminationList, addExamination, editExamination, deleteExamination, Examination, createEmptyExamination } from '@/services/pas/ExaminationService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    FileSearchOutlined,
    CalendarOutlined,
    BankOutlined,
    FileTextOutlined,
    TrophyOutlined,
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    AuditOutlined,
    FileProtectOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type ExaminationInfoProps = {
    userId: string;
    active: boolean;
};

const ExaminationInfo: React.FC<ExaminationInfoProps> = ({ userId, active }) => {
    const [list, setList] = useState<Examination[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [detail, setDetail] = useState<Examination | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) {
            fetchList();
        }
    }, [active, userId]);

    const fetchList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const { success, data, message: msg } = await getExaminationList(userId);
            if (success && data) {
                setList(data);
            } else {
                message.error(msg || '載入資料失敗');
            }
        } catch (error: any) {
            setErrorMsg(error.message || '未知錯誤');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getExaminationDetail(uid);
            if (success && data) {
                setDetail(data);
                form.resetFields();
                form.setFieldsValue({
                    ...createEmptyExamination(),
                    examName: data.examName,
                    examType: data.examType,
                    admittanceGrade: data.admittanceGrade,
                    examInstitution: data.examInstitution,
                    examStartDate: data.examStartDate ? dayjs(data.examStartDate) : null,
                    examEndDate: data.examEndDate ? dayjs(data.examEndDate) : null,
                    certificateDate: data.certificateDate ? dayjs(data.certificateDate) : null,
                    certificateNumber: data.certificateNumber,
                    remark: data.remark,
                });
                setIsModalOpen(true);
            } else {
                message.error(msg || '載入考試資料失敗');
            }
        } catch (error: any) {
            message.error(error.message || '載入錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleAddNew = () => {
        setDetail(null);
        form.resetFields();
        form.setFieldsValue({ ...createEmptyExamination() });
        setIsModalOpen(true);
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            setModalLoading(true);
            const payload = {
                ...values,
                userId,
                // 額外須處理的欄位.
                examStartDate: values.examStartDate ? values.examStartDate.format('YYYY-MM-DD') : '',
                examEndDate: values.examEndDate ? values.examEndDate.format('YYYY-MM-DD') : '',
                certificateDate: values.certificateDate ? values.certificateDate.format('YYYY-MM-DD') : '',
            };

            let res;
            if (detail) {
                res = await editExamination({ ...payload, uid: detail.uid });
            } else {
                res = await addExamination(payload);
            }

            if (res.success && res.data?.result) {
                message.success(detail ? '更新成功' : '新增成功');
                setIsModalOpen(false);
                fetchList();
            } else {
                message.error(res.data?.msg || res.message || '儲存失敗');
            }
        } catch (error: any) {
            if (!error?.errorFields) {
                message.error(error.message || '儲存失敗');
            }
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteExamination(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchList();
            } else {
                message.error(res.data?.msg || '刪除失敗');
            }
        } catch (error: any) {
            message.error(error.message || '刪除錯誤');
        }
    };

    if (!active) return null;
    if (errorMsg) return (
        <div style={{
            color: '#ff4d4f',
            textAlign: 'center',
            padding: '20px',
            background: '#fff1f0',
            border: '1px solid #ffccc7',
            borderRadius: '8px',
            margin: '20px 0'
        }}>
            <Text type="danger">錯誤：{errorMsg}</Text>
        </div>
    );

    return (
        <>
            <Card
                title={
                    <Space>
                        <TrophyOutlined style={{ color: '#1890ff' }} />
                        <Title level={4} style={{ margin: 0 }}>考試資料</Title>
                    </Space>
                }
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{
                            borderRadius: '6px',
                            boxShadow: '0 2px 4px rgba(24,144,255,0.2)'
                        }}
                    >
                        新增考試資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={list}
                    columns={[
                        {
                            title: '考試名稱',
                            dataIndex: 'examName',
                            render: (text) => (
                                <Space>
                                    <TrophyOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '種類科別',
                            dataIndex: 'examType',
                            render: (text) => (
                                <Space>
                                    <AuditOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '錄取等第',
                            dataIndex: 'admittanceGrade',
                            render: (text) => (
                                <Space>
                                    <FileSearchOutlined style={{ color: '#722ed1' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '考試機關',
                            dataIndex: 'examInstitution',
                            render: (text) => (
                                <Space>
                                    <BankOutlined style={{ color: '#eb2f96' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '考試期間',
                            render: (_, record) => {
                                const start = record.examStartDate || '-';
                                const end = record.examEndDate || '-';
                                return (
                                    <Space>
                                        <CalendarOutlined style={{ color: '#fa8c16' }} />
                                        <Text>{start} ~ {end}</Text>
                                    </Space>
                                );
                            }
                        },
                        {
                            title: '發證日期',
                            dataIndex: 'certificateDate',
                            render: (text) => (
                                <Space>
                                    <FileProtectOutlined style={{ color: '#13c2c2' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '證書文號',
                            dataIndex: 'certificateNumber',
                            render: (text) => (
                                <Space>
                                    <FileTextOutlined style={{ color: '#2f54eb' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            )
                        }
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space>
                                    <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>備註：</Text>
                                    <Text>{record.remark || '-'}</Text>
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!record.remark,
                    }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                        style: { cursor: 'pointer' }
                    })}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: false,
                        showTotal: (total) => `共 ${total} 筆資料`
                    }}
                />
            </Card>

            <Modal
                title={
                    <Space>
                        <TrophyOutlined style={{ color: '#1890ff' }} />
                        <Title level={5} style={{ margin: 0 }}>
                            {detail ? '編輯考試資料' : '新增考試資料'}
                        </Title>
                    </Space>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                width={800}
                destroyOnClose
                centered
                maskClosable={false}
                footer={[
                    <Button
                        key="cancel"
                        onClick={() => setIsModalOpen(false)}
                        style={{ borderRadius: '6px' }}
                    >
                        取消
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={handleModalOk}
                        loading={modalLoading}
                        style={{
                            borderRadius: '6px',
                            boxShadow: '0 2px 4px rgba(24,144,255,0.2)'
                        }}
                    >
                        確認
                    </Button>
                ]}
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    className="mt-4"
                    style={{
                        width: '100%'
                    }}
                >
                    {/* 考試資訊 */}
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileSearchOutlined style={{ color: '#1890ff' }} />
                                考試資訊
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24}>
                                <Form.Item
                                    label={<Text strong>考試名稱</Text>}
                                    name="examName"
                                    rules={[{ required: true, message: '請輸入考試名稱' }]}
                                >
                                    <Input
                                        placeholder="請輸入考試名稱"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    label={<Text strong>種類科別</Text>}
                                    name="examType"
                                >
                                    <Input
                                        placeholder="請輸入種類科別"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    label={<Text strong>錄取等第</Text>}
                                    name="admittanceGrade"
                                >
                                    <Input
                                        placeholder="請輸入錄取等第"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 考試期間 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <CalendarOutlined style={{ color: '#1890ff' }} />
                                考試期間
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    label={<Text strong>考試起日</Text>}
                                    name="examStartDate"
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        placeholder="請選擇起日"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    label={<Text strong>考試迄日</Text>}
                                    name="examEndDate"
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        placeholder="請選擇迄日"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    label={<Text strong>發證日期</Text>}
                                    name="certificateDate"
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        placeholder="請選擇發證日期"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    label={<Text strong>證書文號</Text>}
                                    name="certificateNumber"
                                >
                                    <Input
                                        placeholder="請輸入證書文號"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 考試機關 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <BankOutlined style={{ color: '#1890ff' }} />
                                考試機關
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24}>
                                <Form.Item
                                    label={<Text strong>考試機關</Text>}
                                    name="examInstitution"
                                >
                                    <Input
                                        placeholder="請輸入考試機關"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 其他資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileTextOutlined style={{ color: '#1890ff' }} />
                                其他資訊
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col span={24}>
                                <Form.Item
                                    label={<Text strong>備註</Text>}
                                    name="remark"
                                >
                                    <Input.TextArea
                                        rows={3}
                                        placeholder="請輸入備註"
                                        style={{
                                            resize: 'none',
                                            borderRadius: '6px',
                                            backgroundColor: '#fafafa'
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default ExaminationInfo;
