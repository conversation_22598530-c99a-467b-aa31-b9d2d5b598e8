using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AmortizationSourceService : IAmortizationSourceService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public AmortizationSourceService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 新增攤提來源
        /// </summary>
        /// <param name="amortizationSource"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddAsync(AmortizationSourceDTO amortizationSource)
        {
            // 開始交易
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 檢查部門是否存在
                var department = await _context.Set<Department>()
                    .FirstOrDefaultAsync(d => d.DepartmentId == amortizationSource.DepartmentId && !d.IsDeleted);

                if (department == null)
                {
                    return (false, "找不到指定的部門");
                }

                var entity = _mapper.Map<AmortizationSource>(amortizationSource);
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = amortizationSource.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                // 提交交易
                await transaction.CommitAsync();
                return (true, "新增攤提來源成功");
            }
            catch (Exception ex)
            {
                // 發生錯誤時回滾交易
                await transaction.RollbackAsync();
                return (false, $"新增攤提來源失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除攤提來源
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteAsync(AmortizationSourceDTO _data)
        {
            var entity = await _context.Set<AmortizationSource>().FindAsync(_data.AmortizationSourceId);

            if (entity == null)
            {
                return (false, "找不到資料");
            }

            entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            entity.DeleteUserId = _data.DeleteUserId;
            entity.IsDeleted = true;

            _context.Update(entity);
            await _context.SaveChangesAsync();
            return (true, "刪除成功");
        }

        /// <summary>
        /// 取得所有攤提來源
        /// </summary>
        /// <returns></returns>
        public async Task<List<AmortizationSourceDTO>> GetAllAsync()
        {
            var entities = await _context.Set<AmortizationSource>()
                .AsNoTracking()
                .ToListAsync();

            var dtos = _mapper.Map<List<AmortizationSourceDTO>>(entities);

            // 查詢使用者和部門資訊
            foreach (var dto in dtos)
            {
                // 查詢部門資訊
                var department = await _context.Set<Department>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(d => d.DepartmentId == dto.DepartmentId);

                dto.DepartmentName = department?.Name ?? "";

                var createUser = await _context.Set<Users>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.UserId == dto.CreateUserId);

                var updateUser = await _context.Set<Users>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.UserId == dto.UpdateUserId);

                var deleteUser = await _context.Set<Users>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.UserId == dto.DeleteUserId);

                dto.CreateUserName = createUser?.Name ?? "";
                dto.UpdateUserName = updateUser?.Name ?? "";
                dto.DeleteUserName = deleteUser?.Name ?? "";
            }

            return dtos;
        }

        /// <summary>
        /// 取得攤提來源 by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<string> GetByIdAsync(Guid id)
        {
            var entity = await _context.Set<AmortizationSource>()
                .FirstOrDefaultAsync(a => a.AmortizationSourceId == id);

            if (entity == null)
            {
                return "找不到資料";
            }

            var dto = _mapper.Map<AmortizationSourceDTO>(entity);

            // 查詢部門資訊
            var department = await _context.Set<Department>()
                .AsNoTracking()
                .FirstOrDefaultAsync(d => d.DepartmentId == entity.DepartmentId);

            dto.DepartmentName = department?.Name ?? "";

            return JsonConvert.SerializeObject(dto);
        }

        /// <summary>
        /// 更新攤提來源
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> UpdateAsync(AmortizationSourceDTO _data)
        {
            var entity = await _context.Set<AmortizationSource>().FindAsync(_data.AmortizationSourceId);
            if (entity == null)
            {
                return (false, "找不到資料");
            }

            _mapper.Map(_data, entity);
            entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            _context.Update(entity);
            await _context.SaveChangesAsync();
            return (true, "編輯成功");
        }
    }
}