﻿using FAST_ERP_Backend.Models.Common;
using System;
using System.Collections.Generic;
using System.Data;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IUsersService
    {
        /// <summary>
        /// 取得使用者資料
        /// </summary>
        /// <param name="_userId"></param>
        /// <returns></returns>
        Task<List<UsersDTO>> GetUsersAsync(string _userId = "");

        /// <summary>
        /// 新增使用者資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        Task<(bool, string)> AddUsersAsync(UsersDTO _data,String tokenUid="");

        /// <summary>
        /// 編輯使用者資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        Task<(bool, string)> EditUsersAsync(UsersDTO _data,String tokenUid="");

        /// <summary>
        /// 刪除使用者資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        Task<(bool, string)> DeleteUsersAsync(UsersDTO _data,String tokenUid="");

        /// <summary>
        /// 變更使用者密碼
        /// </summary>
        /// <param name="_data">UserId、Password</param>
        /// <returns></returns>
        Task<(bool, string)> ChangeUsersPasswordAsync(ChangeUsersPasswordDTO _data,String tokenUid="");

        /// <summary>
        /// 登入資訊驗證
        /// </summary>
        /// <param name="_data">Account、Password</param>
        /// <returns></returns>
        Task<(bool, string,string)> VerifyLoginAsync(UsersDTO _data);


    }

}
