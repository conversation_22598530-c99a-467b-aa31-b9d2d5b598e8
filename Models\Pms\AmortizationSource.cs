using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 攤提來源
    /// </summary>
    public class AmortizationSource : ModelBaseEntity
    {
        [Key]
        [Comment("攤提來源編號")]
        public Guid AmortizationSourceId { get; set; } // 攤提來源ID

        [Comment("部門編號")]
        public string DepartmentId { get; set; } // 部門編號

        [Comment("攤提來源名稱")]
        public string SourceName { get; set; } // 攤提來源名稱

        [Comment("攤提來源描述")]
        public string Description { get; set; } // 攤提來源描述

        [Comment("攤提來源金額")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } // 攤提來源金額

        // 資產攤提來源
        public virtual ICollection<AssetAmortizationSourceMapping> AssetAmortizationSources { get; set; }

        public AmortizationSource()
        {
            AmortizationSourceId = Guid.NewGuid();
            DepartmentId = "";
            SourceName = "";
            Description = "";
            Amount = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            AssetAmortizationSources = new List<AssetAmortizationSourceMapping>();
        }
    }

    public class AmortizationSourceDTO : ModelBaseEntityDTO
    {
        public Guid AmortizationSourceId { get; set; } // 攤提來源ID
        public string DepartmentId { get; set; } // 部門編號
        public string DepartmentName { get; set; } // 部門名稱
        public string SourceName { get; set; } // 攤提來源名稱
        public string Description { get; set; } // 攤提來源描述
        public decimal Amount { get; set; } // 攤提來源金額
        public long? CreateTime { get; set; } // 建立時間
        public string CreateUserId { get; set; } // 建立者編號
        public string CreateUserName { get; set; } // 建立者名稱
        public long? UpdateTime { get; set; } // 更新時間
        public string UpdateUserId { get; set; } // 更新者編號
        public string UpdateUserName { get; set; } // 更新者名稱
        public long? DeleteTime { get; set; } // 刪除時間
        public string DeleteUserId { get; set; } // 刪除者編號
        public string DeleteUserName { get; set; } // 刪除者名稱

        // 資產攤提來源
        public List<AssetAmortizationSourceMapping> AssetAmortizationSources { get; set; }

        public AmortizationSourceDTO()
        {
            AmortizationSourceId = Guid.NewGuid();
            DepartmentId = "";
            DepartmentName = "";
            SourceName = "";
            Description = "";
            Amount = 0;
            CreateTime = null;
            CreateUserId = "";
            CreateUserName = "";
            UpdateTime = null;
            UpdateUserId = "";
            UpdateUserName = "";
            DeleteTime = null;
            DeleteUserId = "";
            DeleteUserName = "";
            AssetAmortizationSources = new List<AssetAmortizationSourceMapping>();
        }
    }
}
