import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 附屬設備
export interface AccessoryEquipment {
    accessoryEquipmentId: string;
    equipmentNo: string;
    equipmentName: string;
    equipmentType: string;
    specification: string;
    purchaseDate: number;
    purchasePrice: number;
    usageStatus: string;
    remarks: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime: number;
    deleteUserId: string;
    createUserName: string;
    updateUserName: string;
    deleteUserName: string;
}

// 獲取附屬設備列表
export async function getAccessoryEquipments(): Promise<ApiResponse<AccessoryEquipment[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAccessoryEquipments, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取附屬設備列表失敗",
            data: [],
        };
    }
}

// 新增附屬設備
export async function createAccessoryEquipment(data: Partial<AccessoryEquipment>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAccessoryEquipment, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增附屬設備失敗",
        };
    }
}

// 更新附屬設備
export async function updateAccessoryEquipment(data: Partial<AccessoryEquipment>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAccessoryEquipment, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(JSON.stringify(data));
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新附屬設備失敗",
        };
    }
}

// 刪除附屬設備
export async function deleteAccessoryEquipment(data: Partial<AccessoryEquipment>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAccessoryEquipment, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除附屬設備失敗",
        };
    }
}
