import React from "react";
import { AssetCarryOut, STATUS_COLORS } from "./interface";

export interface Option {
    label: string;
    value: string;
}

export interface ColorOption extends Option {
    color: string;
}

export interface StepItem {
    title: string;
    description: string | React.ReactNode;
    count?: number;
}

// 狀態選項
export const STATUS_OPTIONS: ColorOption[] = [
    { label: "待審核", value: "PENDING", color: "orange" },
    { label: "已核准", value: "APPROVED", color: "green" },
    { label: "已駁回", value: "REJECTED", color: "red" },
    { label: "已攜出", value: "CARRIED_OUT", color: "blue" },
    { label: "已歸還", value: "RETURNED", color: "green" },
    { label: "逾期", value: "OVERDUE", color: "red" },
    { label: "已取消", value: "CANCELLED", color: "gray" },
];


// 攜出目的選項
export const PURPOSE_OPTIONS: Option[] = [
    { label: "業務使用", value: "業務使用" },
    { label: "維修檢測", value: "維修檢測" },
    { label: "展示演示", value: "展示演示" },
    { label: "外訓使用", value: "外訓使用" },
    { label: "其他", value: "其他" },
];

// 處理流程步驟
export const STEP_ITEMS: StepItem[] = [
    {
        title: "申請",
        description: "提出攜出申請",
    },
    {
        title: "待審核",
        description: "等待審核",
    },
    {
        title: "已核准",
        description: "審核通過",
    },
    {
        title: "已攜出",
        description: "資產已攜出",
    },
    {
        title: "已歸還",
        description: "資產已歸還",
    },
];

// =========================== 工具函數 ===========================

/**
 * 獲取狀態標籤文字
 * @param value 狀態值
 * @returns 狀態標籤
 */
export const getStatusLabel = (value: string): string => {
    const status = STATUS_OPTIONS.find(s => s.value === value);
    return status?.label || value;
};

/**
 * 獲取狀態顏色
 * @param value 狀態值
 * @returns 狀態顏色
 */
export const getStatusColor = (value: string): string => {
    return STATUS_COLORS[value as keyof typeof STATUS_COLORS] || "default";
};

/**
 * 翻譯狀態（英文轉中文）
 * @param englishStatus 英文狀態
 * @returns 中文狀態
 */
export const translateStatus = (englishStatus: string): string => {
    const statusMap: Record<string, string> = {
        "PENDING": "待審核",
        "APPROVED": "已核准",
        "REJECTED": "已駁回",
        "CARRIED_OUT": "已攜出",
        "RETURNED": "已歸還",
        "OVERDUE": "逾期",
        "CANCELLED": "已取消"
    };
    return statusMap[englishStatus] || englishStatus;
};

/**
 * 反向翻譯狀態（中文轉英文）
 * @param chineseStatus 中文狀態
 * @returns 英文狀態
 */
export const reverseTranslateStatus = (chineseStatus: string): string => {
    const statusMap: Record<string, string> = {
        "待審核": "PENDING",
        "已核准": "APPROVED",
        "已駁回": "REJECTED",
        "已攜出": "CARRIED_OUT",
        "已歸還": "RETURNED",
        "逾期": "OVERDUE",
        "已取消": "CANCELLED"
    };
    return statusMap[chineseStatus] || chineseStatus;
};

/**
 * 根據狀態獲取對應的步驟索引
 * @param status 當前狀態
 * @returns 步驟索引
 */
export const getStepByStatus = (status: string): number => {
    const stepMap: Record<string, number> = {
        "PENDING": 1,     // 待審核
        "待審核": 1,
        "APPROVED": 2,    // 已核准
        "已核准": 2,
        "REJECTED": 1,    // 已駁回（回到審核步驟）
        "已駁回": 1,
        "CARRIED_OUT": 3, // 已攜出
        "已攜出": 3,
        "RETURNED": 4,    // 已歸還
        "已歸還": 4,
        "OVERDUE": 3,     // 逾期（在攜出狀態）
        "逾期": 3,
        "CANCELLED": 0,   // 已取消（回到申請步驟）
        "已取消": 0
    };
    return stepMap[status] || 0;
};

/**
 * 檢查狀態是否為已取消
 * @param status 狀態
 * @returns 是否已取消
 */
export const isStatusCancelled = (status: string): boolean => {
    return status === "已取消" || status === "CANCELLED";
};

/**
 * 檢查狀態是否為已駁回
 * @param status 狀態
 * @returns 是否已駁回
 */
export const isStatusRejected = (status: string): boolean => {
    return status === "已駁回" || status === "REJECTED";
};

/**
 * 獲取步驟狀態（用於 Steps 組件）
 * @param status 當前狀態
 * @returns Steps 組件的狀態
 */
export const getStepStatus = (status: string): "error" | "process" => {
    return isStatusCancelled(status) || isStatusRejected(status) ? "error" : "process";
};

/**
 * 根據統計數據創建帶件數的步驟項目
 * @param statistics 統計數據
 * @returns 帶件數的步驟項目陣列
 */
export const createStepItemsWithCount = (statistics: any): StepItem[] => {
    if (!statistics) {
        return STEP_ITEMS;
    }

    return STEP_ITEMS.map((item, index) => {
        let count = 0;

        switch (index) {
            case 0: // 申請
                count = statistics.totalCount || 0;
                break;
            case 1: // 待審核
                count = statistics.pendingCount || 0;
                break;
            case 2: // 已核准
                count = statistics.approvedCount || 0;
                break;
            case 3: // 已攜出
                count = statistics.carriedOutCount || 0;
                break;
            case 4: // 已歸還
                count = statistics.returnedCount || 0;
                break;
            default:
                count = 0;
        }

        return {
            ...item,
            title: `${item.title}`,
            description: `${item.description} · 共 ${count} 件`,
            count
        };
    });
};

/**
 * 根據統計數據計算整體進度的當前步驟
 * @param statistics 統計數據
 * @returns 當前步驟索引
 */
export const calculateOverallProgress = (statistics: any): number => {
    if (!statistics) {
        return 0;
    }

    // 如果有歸還的申請，顯示為最後一步
    if (statistics.returnedCount > 0) {
        return 4; // 已歸還
    }

    // 如果有攜出的申請，顯示為攜出步驟
    if (statistics.carriedOutCount > 0) {
        return 3; // 已攜出
    }

    // 如果有核准的申請，顯示為核准步驟
    if (statistics.approvedCount > 0) {
        return 2; // 已核准
    }

    // 如果有待審核的申請，顯示為審核步驟
    if (statistics.pendingCount > 0) {
        return 1; // 待審核
    }

    // 否則顯示為申請步驟
    return 0; // 申請
};

/**
 * 根據統計數據獲取整體進度狀態
 * @param statistics 統計數據
 * @returns 進度狀態
 */
export const getOverallProgressStatus = (statistics: any): "wait" | "process" | "finish" | "error" => {
    if (!statistics || statistics.totalCount === 0) {
        return "wait";
    }

    // 如果所有申請都已歸還
    if (statistics.returnedCount === statistics.totalCount) {
        return "finish";
    }

    // 如果有申請在進行中
    if (statistics.carriedOutCount > 0 ||
        statistics.approvedCount > 0 ||
        statistics.pendingCount > 0) {
        return "process";
    }

    return "wait";
};

/**
 * 生成攜出申請單號
 * 格式：COYYYYMMDD (CO + 西元年 + 月 + 日)
 * @param date 可選的日期，預設為當前日期
 * @returns 生成的攜出單號
 */
export const generateCarryOutNumber = (date?: Date): string => {
    const targetDate = date || new Date();

    const year = targetDate.getFullYear();
    const month = String(targetDate.getMonth() + 1).padStart(2, '0');
    const day = String(targetDate.getDate()).padStart(2, '0');

    return `CO${year}${month}${day}`;
};

/**
 * 攜出申請單排序函數
 * 排序規則：申請日期(新->舊) > 狀態
 * @param a 攜出申請單A
 * @param b 攜出申請單B
 * @returns 排序結果
 */
export const sortCarryOutList = (a: any, b: any): number => {
    // 首先按申請日期排序 (新的在前)
    const dateA = a.applicationDate || 0;
    const dateB = b.applicationDate || 0;

    if (dateA !== dateB) {
        return dateB - dateA; // 降序：新日期在前
    }

    // 申請日期相同時，按狀態排序
    const statusOrder = {
        "PENDING": 1,
        "待審核": 1,
        "APPROVED": 2,
        "已核准": 2,
        "CARRIED_OUT": 3,
        "已攜出": 3,
        "OVERDUE": 4,
        "逾期": 4,
        "RETURNED": 5,
        "已歸還": 5,
        "REJECTED": 6,
        "已駁回": 6,
        "CANCELLED": 7,
        "已取消": 7
    };

    const orderA = statusOrder[a.status as keyof typeof statusOrder] || 99;
    const orderB = statusOrder[b.status as keyof typeof statusOrder] || 99;

    return orderA - orderB;
};

// =========================== 預設值配置 ===========================

/** 表單預設值 */
export const DEFAULT_VALUES = {
    purpose: "業務使用",
    status: "待審核"
}; 