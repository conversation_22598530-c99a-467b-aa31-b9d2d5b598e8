// PerformancePointType 點數類型管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "@/services/http";
import { ApiResponse } from "@/config/api";

// 點數類型資料型別
export interface PerformancePointType {
    uid: string;
    pointName: string;
    groupUid: string;
    groupName: string;
    weightRatio: string; // 範例值 "1.00"
}

export interface CascaderOptionDTO {
    value: string;
    label: string;
    children?: CascaderOptionDTO[];
}

// 建立空的點數類型資料
export const createEmptyPerformancePointType = (): PerformancePointType => ({
    uid: '',
    pointName: '',
    groupUid: '',
    groupName: '',
    weightRatio: '1.00',
});

// 根據群組 UID 取得點數類型清單
export async function getPerformancePointTypeList(groupUid: string): Promise<ApiResponse<PerformancePointType[]>> {
    return await httpClient(`${apiEndpoints.getPerformancePointTypeList}/${groupUid}`, {
        method: "GET",
    });
}

// 取得單一點數類型明細
export async function getPerformancePointTypeDetail(uid: string): Promise<ApiResponse<PerformancePointType>> {
    return await httpClient(`${apiEndpoints.getPerformancePointTypeDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增點數類型
export async function addPerformancePointType(data: Partial<PerformancePointType>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addPerformancePointType, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增點數類型失敗",
        };
    }
}

// 編輯點數類型
export async function editPerformancePointType(data: Partial<PerformancePointType>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editPerformancePointType, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯點數類型失敗",
        };
    }
}

// 刪除點數類型
export async function deletePerformancePointType(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deletePerformancePointType, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除點數類型失敗",
        };
    }
}


// 取得點數群組類型
export async function getGroupTypeCascaderOptions(): Promise<ApiResponse<CascaderOptionDTO[]>> {
    return await httpClient(apiEndpoints.getGroupTypeCascaderOptions, {
        method: "GET",
    });
}