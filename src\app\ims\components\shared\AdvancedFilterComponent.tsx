"use client";

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  Button,
  Input,
  Select,
  TreeSelect,
  Tag,
  Tooltip,
  Dropdown
} from 'antd';
import {
  FilterOutlined,
  CheckOutlined,
  ClearOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

// TypeScript 介面定義
export interface FilterOption {
  label: string;
  value: string;
  type?: 'input' | 'select' | 'treeSelect';
  children?: Array<{ label: string; value: string }>;
  treeData?: any[];
  placeholder?: string;
  width?: number;
}

export interface FilterChangeEvent {
  filterKey: string;
  value: any;
  activeFilters: string[];
  filterValues: Record<string, any>;
}

export interface AdvancedFilterProps {
  /** 篩選選項配置 */
  filterOptions: FilterOption[];
  /** 篩選值變更回調 */
  onFilterChange?: (event: FilterChangeEvent) => void;
  /** 清除所有篩選回調 */
  onClearFilters?: () => void;
  /** 初始活躍篩選條件 */
  initialActiveFilters?: string[];
  /** 初始篩選值 */
  initialFilterValues?: Record<string, any>;
  /** 是否顯示清除按鈕 */
  showClearButton?: boolean;
  /** 自定義樣式 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 佈局方向 */
  layout?: 'horizontal' | 'vertical';
  /** 是否緊湊模式 */
  compact?: boolean;
  /** 是否只顯示篩選按鈕（不顯示篩選條件） */
  buttonOnly?: boolean;
}

/**
 * 進階篩選組件 - 可重用的篩選控制器
 * 
 * 支援多種篩選類型：
 * - input: 文字輸入篩選
 * - select: 下拉選擇篩選（支援多選）
 * - treeSelect: 樹狀選擇篩選（支援多選和階層）
 * 
 * @example
 * ```tsx
 * const filterOptions = [
 *   { label: "商品名稱", value: "name", type: "input" },
 *   { label: "商品狀態", value: "status", children: [
 *     { label: "啟用", value: "active" },
 *     { label: "停用", value: "inactive" }
 *   ]},
 *   { label: "商品分類", value: "category", type: "treeSelect", treeData: categoryTree }
 * ];
 * 
 * <AdvancedFilterComponent
 *   filterOptions={filterOptions}
 *   onFilterChange={(event) => console.log('Filter changed:', event)}
 *   onClearFilters={() => console.log('Filters cleared')}
 * />
 * ```
 */
const AdvancedFilterComponent: React.FC<AdvancedFilterProps> = ({
  filterOptions,
  onFilterChange,
  onClearFilters,
  initialActiveFilters = [],
  initialFilterValues = {},
  showClearButton = true,
  className = '',
  disabled = false,
  layout = 'horizontal',
  compact = false,
  buttonOnly = false
}) => {
  // 內部狀態管理
  const [activeFilters, setActiveFilters] = useState<string[]>(initialActiveFilters);
  const [filterValues, setFilterValues] = useState<Record<string, any>>(initialFilterValues);

  // 同步外部狀態變更 - 當外部清除時，內部狀態也要同步
  useEffect(() => {
    setActiveFilters(initialActiveFilters);
  }, [initialActiveFilters]);

  useEffect(() => {
    setFilterValues(initialFilterValues);
  }, [initialFilterValues]);

  /**
   * 檢查篩選值是否有效（非空）
   * @param value 要檢查的值
   * @returns 是否為有效的篩選值
   */
  const isValidFilterValue = useCallback((value: any): boolean => {
    if (value === undefined || value === null || value === '') {
      return false;
    }

    // 對於陣列類型（如多選分類），檢查是否為非空陣列
    if (Array.isArray(value)) {
      return value.length > 0;
    }

    // 對於字串類型，檢查是否為非空字串
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }

    return true;
  }, []);

  /**
   * 處理新增篩選條件
   * @param filterKey 篩選鍵值
   */
  const handleAddFilter = useCallback((filterKey: string) => {
    if (!activeFilters.includes(filterKey)) {
      const newActiveFilters = [...activeFilters, filterKey];
      setActiveFilters(newActiveFilters);
      
      // 觸發回調
      onFilterChange?.({
        filterKey,
        value: filterValues[filterKey],
        activeFilters: newActiveFilters,
        filterValues
      });
    }
  }, [activeFilters, filterValues, onFilterChange]);

  /**
   * 處理移除篩選條件
   * @param filterKey 篩選鍵值
   */
  const handleRemoveFilter = useCallback((filterKey: string) => {
    const newActiveFilters = activeFilters.filter(key => key !== filterKey);
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];
    
    setActiveFilters(newActiveFilters);
    setFilterValues(newFilterValues);
    
    // 觸發回調
    onFilterChange?.({
      filterKey,
      value: undefined,
      activeFilters: newActiveFilters,
      filterValues: newFilterValues
    });
  }, [activeFilters, filterValues, onFilterChange]);

  /**
   * 處理清除所有篩選
   */
  const handleClearFilters = useCallback(() => {
    // 只清除內部狀態，不觸發回調
    // 因為這個函數主要用於下拉選單中的清除，
    // 實際的清除邏輯應該由外部的 onClearFilters 處理

    // 直接調用外部清除函數，讓父組件統一處理所有清除邏輯
    onClearFilters?.();
  }, [onClearFilters]);

  /**
   * 處理篩選值變更
   * @param filterKey 篩選鍵值
   * @param value 篩選值（可能是字串、陣列或其他類型）
   */
  const handleFilterValueChange = useCallback((filterKey: string, value: any) => {
    console.log(`🔄 篩選值變更: ${filterKey} =`, value);

    // 檢查是否為空值
    const isEmptyValue = value === undefined ||
      value === null ||
      value === '' ||
      (Array.isArray(value) && value.length === 0);

    let newActiveFilters = [...activeFilters];
    let newFilterValues = { ...filterValues };

    if (isEmptyValue) {
      console.log(`🧹 清空篩選值: ${filterKey}`);
      // 清空篩選值，但保持篩選器在介面上（不移除 activeFilters）
      delete newFilterValues[filterKey];
      setFilterValues(newFilterValues);
    } else {
      console.log(`✅ 設定篩選條件: ${filterKey} =`, value);
      // 設定篩選值
      newFilterValues[filterKey] = value;
      setFilterValues(newFilterValues);

      // 確保該篩選條件在 activeFilters 中
      if (!activeFilters.includes(filterKey)) {
        newActiveFilters = [...activeFilters, filterKey];
        setActiveFilters(newActiveFilters);
      }
    }

    // 觸發回調
    onFilterChange?.({
      filterKey,
      value,
      activeFilters: newActiveFilters,
      filterValues: newFilterValues
    });
  }, [activeFilters, filterValues, onFilterChange]);

  /**
   * 渲染篩選控制元素
   * @param filterKey 篩選鍵值
   * @returns 篩選控制組件
   */
  const renderFilterControl = useCallback((filterKey: string) => {
    const filterOption = filterOptions.find((opt) => opt.value === filterKey);
    if (!filterOption) return null;

    // 使用統一的有效值檢查邏輯
    const hasValue = isValidFilterValue(filterValues[filterKey]);
    const width = filterOption.width || (filterOption.type === 'treeSelect' ? 250 : 200);

    const commonStyle = {
      width,
      borderColor: hasValue ? '#1890ff' : undefined,
      boxShadow: hasValue ? '0 0 0 2px rgba(24, 144, 255, 0.2)' : undefined
    };

    return (
      <div className="filter-control" key={filterKey}>
        {filterOption.type === "input" ? (
          <Tooltip title={`輸入${filterOption.label}進行篩選`}>
            <Input
              style={commonStyle}
              onChange={(e) => handleFilterValueChange(filterKey, e.target.value)}
              placeholder={filterOption.placeholder || `輸入${filterOption.label}`}
              allowClear
              value={filterValues[filterKey] || ''}
              disabled={disabled}
            />
          </Tooltip>
        ) : filterOption.type === "treeSelect" ? (
          <Tooltip title={`選擇${filterOption.label}進行篩選（支援多選和階層篩選）`}>
            <TreeSelect
              style={commonStyle}
              multiple // 啟用多選模式
              treeData={filterOption.treeData}
              onChange={(value) => {
                console.log(`🌳 TreeSelect 值變更 (${filterKey}):`, value);
                handleFilterValueChange(filterKey, value);
              }}
              onClear={() => {
                console.log(`🧹 TreeSelect 清空 (${filterKey})`);
                handleFilterValueChange(filterKey, []);
              }}
              placeholder={filterOption.placeholder || `選擇${filterOption.label}（可多選）`}
              allowClear
              showSearch
              treeDefaultExpandAll={true}
              treeNodeFilterProp="title"
              treeNodeLabelProp="title"
              maxTagCount="responsive"
              value={filterValues[filterKey] || []} // 確保值為陣列
              className={hasValue ? 'filter-select-active' : ''}
              suffixIcon={<ApartmentOutlined />}
              disabled={disabled}
              // 優化搜尋體驗
              filterTreeNode={(inputValue, treeNode) => {
                const title = treeNode.title as string;
                return title.toLowerCase().includes(inputValue.toLowerCase());
              }}
            />
          </Tooltip>
        ) : (
          <Tooltip title={`選擇${filterOption.label}進行篩選`}>
            <Select
              style={commonStyle}
              mode="multiple"
              onChange={(value) => handleFilterValueChange(filterKey, value)}
              options={filterOption.children}
              placeholder={filterOption.placeholder || `選擇${filterOption.label}`}
              allowClear
              value={filterValues[filterKey] || []}
              className={hasValue ? 'filter-select-active' : ''}
              disabled={disabled}
            />
          </Tooltip>
        )}
      </div>
    );
  }, [filterOptions, filterValues, isValidFilterValue, handleFilterValueChange, disabled]);

  // 篩選選單配置
  const filterMenu: MenuProps = useMemo(() => ({
    items: [
      ...filterOptions.map((option) => ({
        key: option.value,
        label: (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: "120px",
            }}
          >
            <span>{option.label}</span>
            {activeFilters.includes(option.value) && (
              <CheckOutlined style={{ color: "#1890ff" }} />
            )}
          </div>
        ),
        onClick: () => {
          if (activeFilters.includes(option.value)) {
            handleRemoveFilter(option.value);
          } else {
            handleAddFilter(option.value);
          }
        },
        disabled
      })),
      ...(activeFilters.length > 0
        ? [
          { type: "divider" as const },
          {
            key: "clear",
            label: "清除所有篩選",
            onClick: handleClearFilters,
            danger: true,
            disabled
          },
        ]
        : []),
    ],
  }), [filterOptions, activeFilters, handleAddFilter, handleRemoveFilter, handleClearFilters, disabled]);

  return (
    <div className={`advanced-filter-component ${className}`}>
      <style jsx>{`
        .filter-controls-container {
          display: flex;
          flex-direction: ${layout === 'vertical' ? 'column' : 'row'};
          gap: ${compact ? '8px' : '16px'};
          align-items: ${layout === 'vertical' ? 'stretch' : 'flex-start'};
          flex-wrap: wrap;
        }

        .filter-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: ${compact ? '8px' : '12px'};
          flex-wrap: wrap;
        }

        .filter-item-tag {
          margin: 0;
          padding: 2px 8px;
          font-size: 12px;
          white-space: nowrap;
        }

        @media (max-width: 768px) {
          .filter-controls-container {
            flex-direction: column;
            gap: 12px;
          }

          .filter-item {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
          }

          .filter-item-tag {
            align-self: flex-start;
          }
        }
      `}</style>

      {/* 篩選按鈕和控制項 */}
      <div className="filter-controls-container">
        <Dropdown menu={filterMenu} trigger={["click"]} placement="bottomLeft" disabled={disabled}>
          <Button
            icon={<FilterOutlined />}
            type={activeFilters.length > 0 ? "primary" : "default"}
            disabled={disabled}
            size={compact ? "small" : "middle"}
            style={{
              borderRadius: '6px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
              minWidth: compact ? 'auto' : '140px'
            }}
          >
            新增篩選條件
            {activeFilters.length > 0 && (
              <span style={{ marginLeft: 4, fontSize: '12px' }}>
                ({activeFilters.length})
              </span>
            )}
          </Button>
        </Dropdown>

        {showClearButton && activeFilters.length > 0 && (
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearFilters}
            disabled={disabled}
            size={compact ? "small" : "middle"}
            danger
            type="dashed"
            style={{
              borderRadius: '6px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
            }}
          >
            清除篩選
          </Button>
        )}
      </div>

      {/* 活躍篩選條件顯示區域 - 只在非 buttonOnly 模式下顯示 */}
      {!buttonOnly && activeFilters.length > 0 && (
        <div style={{
          marginTop: compact ? 12 : 16,
          borderTop: '1px solid #f0f0f0',
          paddingTop: compact ? 12 : 16
        }}>
          <div className="filter-controls-container">
            {activeFilters.map((filterKey) => {
              const filterOption = filterOptions.find(
                (opt) => opt.value === filterKey
              );
              if (!filterOption) return null;
              return (
                <div key={filterKey} className="filter-item">
                  <Tag
                    color="blue"
                    className="filter-item-tag"
                  >
                    {filterOption.label}
                  </Tag>
                  {renderFilterControl(filterKey)}
                  <Button
                    type="text"
                    size="small"
                    icon={<ClearOutlined />}
                    onClick={() => handleRemoveFilter(filterKey)}
                    style={{ color: '#ff4d4f', flexShrink: 0 }}
                    disabled={disabled}
                    title={`移除 ${filterOption.label} 篩選`}
                  />
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilterComponent;
