﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 人事基本資料表
    /// </summary>
    public class Employee : ModelBaseEntity
    {
        [Key]
        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用者編號

        [Comment("身分證字號")]
        [Column(TypeName = "nvarchar(10)")]
        public string IdNo { get; set; } // 身分證字號

        [Comment("證號別")]
        [Column(TypeName = "nvarchar(1)")]
        public string IdType { get; set; } // 證號別

        [Comment("錯誤註記")]
        [Column(TypeName = "nvarchar(1)")]
        public string errorMark { get; set; } // 錯誤註記

        [Comment("員工編號")]
        [Column(TypeName = "nvarchar(10)")]
        public string EmpNo { get; set; } // 員工編號

        [Comment("出生日期")]
        [Column(TypeName = "bigint")]
        public long? Birthday { get; set; }  // 出生日期

        [Comment("血型類別")]
        [Column(TypeName = "nvarchar(3)")]
        public string BloodType { get; set; } // 血型類別

        [Comment("配偶身分證")]
        [Column(TypeName = "nvarchar(10)")]
        public string SpouseIdNo { get; set; } // 配偶身分證

        [Comment("配偶姓名")]

        [Column(TypeName = "nvarchar(12)")]
        public string SpouseName { get; set; } // 配偶姓名

        [Comment("最高學歷")]
        [Column(TypeName = "nvarchar(1)")]
        public string EduLevel { get; set; } // 最高學歷

        [Comment("到職日")]
        [Column(TypeName = "bigint")]
        public long? HireDate { get; set; } // 到職日

        [Comment("試用日")]
        [Column(TypeName = "bigint")]
        public long? ProbStartDate { get; set; } // 試用日

        [Comment("正式任用日")]
        [Column(TypeName = "bigint")]
        public long? OfficialHireDate { get; set; } // 正式任用日

        [Comment("離職日")]
        [Column(TypeName = "bigint")]
        public long? LeaveDate { get; set; } // 離職日

        [Comment("勞保加保日")]
        [Column(TypeName = "bigint")]
        public long? LaborInsStartDate { get; set; } // 勞保加保日

        [Comment("健保加保日")]
        [Column(TypeName = "bigint")]
        public long? HealthInsStartDate { get; set; } // 健保加保日

        [Comment("轉任日")]
        [Column(TypeName = "bigint")]
        public long? TransferDate { get; set; } // 轉任日

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string remark { get; set; } // 備註


        public Employee()
        {
            UserId = "";
            IdNo = "";
            IdType = "";
            errorMark = "";
            EmpNo = "";
            Birthday = null;
            BloodType = "";
            SpouseIdNo = "";
            SpouseName = "";
            EduLevel = "";
            HireDate = null;
            ProbStartDate = null;
            OfficialHireDate = null;
            LeaveDate = null;
            LaborInsStartDate = null;
            HealthInsStartDate = null;
            TransferDate = null;
            remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class EmployeeDTO : ModelBaseEntityDTO
    {
        public string UserId { get; set; } // 使用者編號
        public string IdNo { get; set; } // 身分證字號
        public string IdType { get; set; } // 證號別
        public string IdTypeName { get; set; } // 證號別名稱
        public string errorMark { get; set; } // 錯誤註記
        public string errorMarkName { get; set; } // 錯誤註記名稱
        public string EmpNo { get; set; } // 員工編號
        public string Birthday { get; set; }  // 出生日期
        public string BloodType { get; set; } // 血型類別
        public string BloodTypeName { get; set; } // 血型類別名稱
        public string SpouseIdNo { get; set; } // 配偶身分證
        public string SpouseName { get; set; } // 配偶姓名
        public string EduLevel { get; set; } // 最高學歷
        public string EduLevelName { get; set; } // 最高學歷名稱
        public string HireDate { get; set; } // 到職日
        public string ProbStartDate { get; set; } // 試用日
        public string OfficialHireDate { get; set; } // 正式任用日
        public string LeaveDate { get; set; } // 離職日
        public string LaborInsStartDate { get; set; } // 勞保加保日
        public string HealthInsStartDate { get; set; } // 健保加保日
        public string TransferDate { get; set; } // 轉任日
        public string remark { get; set; } // 備註
        public UsersDTO usersDTO { get; set; }

        // 新增：當前生效的升遷資料
        public PromotionDTO CurrentPromotion { get; set; }

        public EmployeeDTO()
        {
            UserId = "";
            IdNo = "";
            IdType = "";
            IdTypeName = "";
            errorMark = "";
            errorMarkName = "";
            EmpNo = "";
            Birthday = "";
            BloodType = "";
            BloodTypeName = "";
            SpouseIdNo = "";
            SpouseName = "";
            EduLevel = "";
            EduLevelName = "";
            HireDate = "";
            ProbStartDate = "";
            OfficialHireDate = "";
            LeaveDate = "";
            LaborInsStartDate = "";
            HealthInsStartDate = "";
            TransferDate = "";
            remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;

            usersDTO = new UsersDTO();
            CurrentPromotion = new PromotionDTO(); // 初始化當前升遷資料
        }
    }

    public class FilterData
    {
        public string filterType { get; set; } // 搜尋條件
        public string filterValue { get; set; } // 搜尋資料

        public FilterData()
        {
            filterType = "";
            filterValue = "";
        }
    }

}

