﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class CityService : ICityService
    {
        private readonly ERPDbContext _context;

        public CityService(ERPDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得縣市資料
        /// </summary>
        /// <param name="_cityId"></param>
        /// <returns></returns>
        public async Task<List<CityDTO>> GetCityAsync(string _cityId = "")
        {
            var query = _context.Set<City>().AsQueryable();

            if (!string.IsNullOrEmpty(_cityId))
            {
                query = query.Where(c => c.CityId == _cityId);
            }

            return await query.Select(c => new CityDTO
            {
                CityId = c.CityId,
                Name = c.Name,
                EnglishName = c.EnglishName,
                Description = c.Description,
                SortCode = c.SortCode,
                CreateTime = c.CreateTime,
                CreateUserId = c.CreateUserId,
                UpdateTime = c.UpdateTime,
                UpdateUserId = c.UpdateUserId,
                DeleteTime = c.DeleteTime,
                DeleteUserId = c.DeleteUserId
            }).ToListAsync();
        }

        /// <summary>
        /// 新增縣市
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddCityAsync(CityDTO _data)
        {
            try
            {
                var entity = new City
                {
                    CityId = Guid.NewGuid().ToString().Trim(),
                    Name = _data.Name,
                    EnglishName = _data.EnglishName,
                    Description = _data.Description,
                    SortCode = _data.SortCode,
                    CreateTime = _data.CreateTime,
                    CreateUserId = _data.CreateUserId,
                    UpdateTime = _data.UpdateTime,
                    UpdateUserId = _data.UpdateUserId,
                    DeleteTime = _data.DeleteTime,
                    DeleteUserId = _data.DeleteUserId
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯縣市
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditCityAsync(CityDTO _data)
        {
            try
            {
                var entity = await _context.Set<City>().FindAsync(_data.CityId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.Name = _data.Name;
                entity.EnglishName = _data.EnglishName;
                entity.Description = _data.Description;
                entity.SortCode = _data.SortCode;
                entity.CreateTime = _data.CreateTime;
                entity.CreateUserId = _data.CreateUserId;
                entity.UpdateTime = _data.UpdateTime;
                entity.UpdateUserId = _data.UpdateUserId;
                entity.DeleteTime = _data.DeleteTime;
                entity.DeleteUserId = _data.DeleteUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除縣市
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteCityAsync(CityDTO _data)
        {
            try
            {
                var entity = await _context.Set<City>().FindAsync(_data.CityId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得縣市詳細資料
        /// </summary>
        /// <param name="_cityId"></param>
        /// <returns></returns>
        public async Task<CityDTO> GetCityDetailAsync(string _cityId)
        {
            var entity = await _context.Set<City>().FindAsync(_cityId);
            if (entity == null)
            {
                return null;
            }

            return new CityDTO
            {
                CityId = entity.CityId,
                Name = entity.Name,
                EnglishName = entity.EnglishName,
                Description = entity.Description,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }
    }
}