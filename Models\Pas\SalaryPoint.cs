using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    [Comment("薪點金額紀錄")]
    public class SalaryPoint : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; }

        [MaxLength(20)]
        [Comment("薪點名稱")]
        [Column(TypeName = "nvarchar(20)")]
        public string PointLevel { get; set; }

        [Comment("薪點金額（每點對應金額）")]
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Amount { get; set; }

        [Required]
        [Comment("生效日期（timestamp）")]
        public long EffectiveDate { get; set; }

        [MaxLength(200)]
        [Comment("調整原因")]
        public string? AdjustmentReason { get; set; }

        public SalaryPoint()
        {
            uid = Guid.NewGuid().ToString();

            // 若未來只會有一個薪點等級，可設定為固定值（例如 A1）
            PointLevel = "A1";

            Amount = 0;

            // 預設為當下時間戳
            EffectiveDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            AdjustmentReason = string.Empty;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class SalaryPointDTO : ModelBaseEntityDTO
    {
        public string Uid { get; set; }

        public string PointLevel { get; set; }

        public decimal Amount { get; set; }

        public string EffectiveDate { get; set; }

        public string? AdjustmentReason { get; set; }

        public SalaryPointDTO()
        {
            Uid = string.Empty;
            // 同樣固定值，如果前端會傳也可忽略
            PointLevel = "A1";
            Amount = 0;
            EffectiveDate = string.Empty;
            AdjustmentReason = string.Empty;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}
