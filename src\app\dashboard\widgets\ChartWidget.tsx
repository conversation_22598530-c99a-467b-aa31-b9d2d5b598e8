"use client";

import { Card, Progress, Typography, Spin } from "antd";
import { BarChartOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";

const { Title, Text } = Typography;

export function ChartWidget() {
  const [loading, setLoading] = useState(true);

  // 模擬數據
  const chartData = [
    { label: "系統使用率", value: 85, color: "#1890ff" },
    { label: "任務完成率", value: 92, color: "#52c41a" },
    { label: "用戶滿意度", value: 78, color: "#faad14" },
    { label: "系統穩定性", value: 96, color: "#722ed1" },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large">
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
            }}
          >
            載入系統指標...
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: "100%" }}>
      <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
        {chartData.map((item, index) => (
          <div key={index}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                marginBottom: "4px",
              }}
            >
              <Text>{item.label}</Text>
              <Text strong>{item.value}%</Text>
            </div>
            <Progress
              percent={item.value}
              strokeColor={item.color}
              showInfo={false}
              size="small"
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export default ChartWidget;
