using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 聯絡人 </summary>
public class Contact : ModelBaseEntity
{
    /// <summary> 聯絡人編號 </summary>
    [Key]
    [Comment("聯絡人編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ContactID { get; set; }

    /// <summary> 聯絡人姓名 </summary>
    [Required]
    [MaxLength(100)]
    [Comment("聯絡人姓名")]
    [Column(TypeName = "nvarchar(100)")]
    public string Name { get; set; }

    /// <summary> 職位 </summary>
    [MaxLength(50)]
    [Comment("職位")]
    [Column(TypeName = "nvarchar(50)")]
    public string? Position { get; set; }

    /// <summary> 電子郵件 </summary>
    [MaxLength(100)]
    [Comment("電子郵件")]
    [Column(TypeName = "nvarchar(100)")]
    public string? Email { get; set; }

    /// <summary> 電話 </summary>
    [MaxLength(20)]
    [Comment("電話")]
    [Column(TypeName = "nvarchar(20)")]
    public string? Phone { get; set; }

    /// <summary> 是否為主要聯絡人 </summary>
    [Comment("是否為主要聯絡人")]
    public bool IsPrimary { get; set; }

    /// <summary> 導覽屬性 - 關聯的商業夥伴映射 </summary>
    public ICollection<PartnerContact> PartnerContacts { get; set; }

    /// <summary> 建構式 </summary>
    public Contact()
    {
        ContactID = Guid.NewGuid();
        Name = string.Empty;
        IsPrimary = false;
        PartnerContacts = new List<PartnerContact>();
    }
}

/// <summary> 聯絡人 DTO </summary>
public class ContactDTO : ModelBaseEntityDTO
{
    /// <summary> 聯絡人編號 </summary>
    public Guid ContactID { get; set; }

    /// <summary> 聯絡人姓名 </summary>
    public string Name { get; set; }

    /// <summary> 職位 </summary>
    public string? Position { get; set; }

    /// <summary> 電子郵件 </summary>
    public string? Email { get; set; }

    /// <summary> 電話 </summary>
    public string? Phone { get; set; }

    /// <summary> 是否為主要聯絡人 </summary>
    public bool IsPrimary { get; set; }

    /// <summary> 建構式 </summary>
    public ContactDTO()
    {
        ContactID = Guid.NewGuid();
        Name = string.Empty;
        IsPrimary = false;
    }
}
