﻿using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class StorageLocationService : IStorageLocationService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public StorageLocationService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得存放地點資料
        /// </summary>
        /// <param name="_locationId"></param>
        /// <returns></returns>
        public async Task<List<StorageLocationDTO>> GetStorageLocationAsync(string _locationId = "")
        {
            try
            {
                var query = _context.Set<StorageLocation>()
                    .Where(s => !s.IsDeleted)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(_locationId))
                {
                    query = query.Where(s => s.StorageLocationId == Guid.Parse(_locationId));
                }

                var entities = await query.ToListAsync();
                return _mapper.Map<List<StorageLocationDTO>>(entities);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得存放地點資料時發生錯誤: {ex.Message}");
                return new List<StorageLocationDTO>();
            }
        }

        /// <summary>
        /// 新增存放地點
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddStorageLocationAsync(StorageLocationDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = _mapper.Map<StorageLocation>(_data);
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = _data.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯存放地點
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditStorageLocationAsync(StorageLocationDTO _data)
        {
            try
            {
                var entity = await _context.Set<StorageLocation>()
                    .FirstOrDefaultAsync(s => s.StorageLocationId == _data.StorageLocationId && !s.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除存放地點
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteStorageLocationAsync(StorageLocationDTO _data)
        {
            try
            {
                var entity = await _context.Set<StorageLocation>()
                    .FirstOrDefaultAsync(s => s.StorageLocationId == _data.StorageLocationId && !s.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 檢查是否有財產資料正在使用此存放地點
                bool isInUse = await _context.Set<Asset>()
                    .AnyAsync(a => a.StorageLocationId == _data.StorageLocationId.ToString() && !a.IsDeleted);

                if (isInUse)
                {
                    return (false, "此存放地點正被財產資料使用中，無法刪除");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得存放地點詳細資料
        /// </summary>
        /// <param name="_locationId"></param>
        /// <returns></returns>
        public async Task<StorageLocationDTO> GetStorageLocationDetailAsync(string _locationId)
        {
            try
            {
                var entity = await _context.Set<StorageLocation>()
                    .FirstOrDefaultAsync(s => s.StorageLocationId == Guid.Parse(_locationId) && !s.IsDeleted);

                if (entity == null)
                {
                    return null;
                }

                return _mapper.Map<StorageLocationDTO>(entity);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得存放地點詳細資料時發生錯誤: {ex.Message}");
                return null;
            }
        }
    }
}

