import dayjs from 'dayjs';

export class DateTimeExtensions {
    /**
     * 將 Unix timestamp (秒) 轉換為指定格式的日期時間字串
     * @param timestamp Unix timestamp (秒)
     * @param format 日期格式，預設為 'YYYY-MM-DD HH:mm:ss'
     * @returns 格式化後的日期時間字串，如果輸入無效則返回空字串
     */
    static formatFromTimestamp(timestamp: number | null | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
        if (!timestamp) return '';
        return dayjs.unix(timestamp).format(format);
    }

    /**
     * 將 Unix timestamp (秒) 轉換為日期字串 (YYYY-MM-DD)
     * @param timestamp Unix timestamp (秒)
     * @returns 格式化後的日期字串，如果輸入無效則返回空字串
     */
    static formatDateFromTimestamp(timestamp: number | null | undefined): string {
        return this.formatFromTimestamp(timestamp, 'YYYY-MM-DD');
    }

    /**
     * 將日期時間轉換為 Unix timestamp (秒)
     * @param date Date 物件或可以被 dayjs 解析的日期字串
     * @returns Unix timestamp (秒)，如果輸入無效則返回 0
     */
    static toTimestamp(date: Date | string | null | undefined): number {
        if (!date) return 0;
        const dayjsObj = dayjs(date);
        return dayjsObj.isValid() ? dayjsObj.unix() : 0;
    }

    /**
     * 取得目前時間的 Unix timestamp (秒)
     * @returns 目前時間的 Unix timestamp (秒)
     */
    static getCurrentTimestamp(): number {
        return Math.floor(Date.now() / 1000);
    }
} 