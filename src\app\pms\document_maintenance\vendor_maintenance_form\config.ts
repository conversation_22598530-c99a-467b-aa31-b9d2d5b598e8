/**
 * 廠商修繕單配置檔案
 * 統一管理修繕類型、緊急程度、狀態的選項配置
 */

// =========================== 類型定義 ===========================

/** 選項介面 */
export interface Option {
    label: string;
    value: string;
}

/** 帶顏色的選項介面 */
export interface ColorOption extends Option {
    color: string;
}

/** 統計選項介面 */
export interface StatisticOption {
    type: string;
    count: number;
    totalCost?: number;
    averageCompletionDays?: number;
}

// =========================== 修繕類型配置 ===========================

/** 修繕類型選項 */
export const MAINTENANCE_TYPES: Option[] = [
    { label: "設備維修", value: "設備維修" },
    { label: "建築修繕", value: "建築修繕" },
    { label: "電力系統", value: "電力系統" },
    { label: "水電管路", value: "水電管路" },
    { label: "空調系統", value: "空調系統" },
    { label: "消防設備", value: "消防設備" },
    { label: "其他", value: "其他" }
];

/** 英文到中文的修繕類型映射 */
export const MAINTENANCE_TYPE_MAPPING: Record<string, string> = {
    "REPAIR": "設備維修",
    "BUILDING": "建築修繕",
    "ELECTRICAL": "電力系統",
    "PLUMBING": "水電管路",
    "HVAC": "空調系統",
    "FIRE": "消防設備",
    "OTHER": "其他"
};

/** 中文到英文的修繕類型映射 */
export const MAINTENANCE_TYPE_REVERSE_MAPPING: Record<string, string> = {
    "設備維修": "REPAIR",
    "建築修繕": "BUILDING",
    "電力系統": "ELECTRICAL",
    "水電管路": "PLUMBING",
    "空調系統": "HVAC",
    "消防設備": "FIRE",
    "其他": "OTHER"
};

// =========================== 緊急程度配置 ===========================

/** 緊急程度選項 */
export const URGENCY_LEVELS: ColorOption[] = [
    { label: "緊急", value: "緊急", color: "red" },
    { label: "高", value: "高", color: "orange" },
    { label: "中", value: "中", color: "blue" },
    { label: "低", value: "低", color: "green" }
];

/** 英文到中文的緊急程度映射 */
export const URGENCY_MAPPING: Record<string, string> = {
    "CRITICAL": "緊急",
    "HIGH": "高",
    "MEDIUM": "中",
    "LOW": "低"
};

/** 中文到英文的緊急程度映射 */
export const URGENCY_REVERSE_MAPPING: Record<string, string> = {
    "緊急": "CRITICAL",
    "高": "HIGH",
    "中": "MEDIUM",
    "低": "LOW"
};

/** 緊急程度顏色映射（包含中英文） */
export const URGENCY_COLORS: Record<string, string> = {
    // 中文映射
    "緊急": "red",
    "高": "orange",
    "中": "blue",
    "低": "green",
    // 英文映射
    "CRITICAL": "red",
    "HIGH": "orange",
    "MEDIUM": "blue",
    "LOW": "green"
};

// =========================== 狀態配置 ===========================

/** 狀態選項 */
export const STATUS_OPTIONS: ColorOption[] = [
    { label: "待審核", value: "待審核", color: "orange" },
    { label: "已審核", value: "已審核", color: "blue" },
    { label: "已指派", value: "已指派", color: "cyan" },
    { label: "施工中", value: "施工中", color: "purple" },
    { label: "已完成", value: "已完成", color: "green" },
    { label: "已驗收", value: "已驗收", color: "lime" },
    { label: "已結案", value: "已結案", color: "gray" },
    { label: "已取消", value: "已取消", color: "red" }
];

/** 英文到中文的狀態映射 */
export const STATUS_MAPPING: Record<string, string> = {
    "PENDING": "待審核",
    "APPROVED": "已審核",
    "ASSIGNED": "已指派",
    "IN_PROGRESS": "施工中",
    "COMPLETED": "已完成",
    "INSPECTED": "已驗收",
    "CLOSED": "已結案",
    "CANCELLED": "已取消"
};

/** 中文到英文的狀態映射 */
export const STATUS_REVERSE_MAPPING: Record<string, string> = {
    "待審核": "PENDING",
    "已審核": "APPROVED",
    "已指派": "ASSIGNED",
    "施工中": "IN_PROGRESS",
    "已完成": "COMPLETED",
    "已驗收": "INSPECTED",
    "已結案": "CLOSED",
    "已取消": "CANCELLED"
};

/** 狀態顏色映射（包含中英文） */
export const STATUS_COLORS: Record<string, string> = {
    // 中文映射
    "待審核": "orange",
    "已審核": "blue",
    "已指派": "cyan",
    "施工中": "purple",
    "已完成": "green",
    "已驗收": "lime",
    "已結案": "gray",
    "已取消": "red",
    // 英文映射
    "PENDING": "orange",
    "APPROVED": "blue",
    "ASSIGNED": "cyan",
    "IN_PROGRESS": "purple",
    "COMPLETED": "green",
    "INSPECTED": "lime",
    "CLOSED": "gray",
    "CANCELLED": "red"
};

// =========================== 工具函數 ===========================

/**
 * 根據值獲取修繕類型標籤
 * @param value 修繕類型值
 * @returns 修繕類型標籤
 */
export const getMaintenanceTypeLabel = (value: string): string => {
    const type = MAINTENANCE_TYPES.find(t => t.value === value);
    return type ? type.label : value;
};

/**
 * 根據值獲取緊急程度標籤
 * @param value 緊急程度值
 * @returns 緊急程度標籤
 */
export const getUrgencyLevelLabel = (value: string): string => {
    const level = URGENCY_LEVELS.find(l => l.value === value);
    return level ? level.label : value;
};

/**
 * 根據值獲取緊急程度顏色
 * @param value 緊急程度值
 * @returns 緊急程度顏色
 */
export const getUrgencyColor = (value: string): string => {
    return URGENCY_COLORS[value] || "default";
};

/**
 * 根據值獲取狀態標籤
 * @param value 狀態值
 * @returns 狀態標籤
 */
export const getStatusLabel = (value: string): string => {
    const status = STATUS_OPTIONS.find(s => s.value === value);
    return status ? status.label : value;
};

/**
 * 根據值獲取狀態顏色
 * @param value 狀態值
 * @returns 狀態顏色
 */
export const getStatusColor = (value: string): string => {
    return STATUS_COLORS[value] || "default";
};

/**
 * 轉換英文修繕類型為中文
 * @param englishType 英文修繕類型
 * @returns 中文修繕類型
 */
export const translateMaintenanceType = (englishType: string): string => {
    return MAINTENANCE_TYPE_MAPPING[englishType] || englishType;
};

/**
 * 轉換英文緊急程度為中文
 * @param englishLevel 英文緊急程度
 * @returns 中文緊急程度
 */
export const translateUrgencyLevel = (englishLevel: string): string => {
    return URGENCY_MAPPING[englishLevel] || englishLevel;
};

/**
 * 轉換英文狀態為中文
 * @param englishStatus 英文狀態
 * @returns 中文狀態
 */
export const translateStatus = (englishStatus: string): string => {
    return STATUS_MAPPING[englishStatus] || englishStatus;
};

/**
 * 轉換中文修繕類型為英文
 * @param chineseType 中文修繕類型
 * @returns 英文修繕類型
 */
export const reverseTranslateMaintenanceType = (chineseType: string): string => {
    return MAINTENANCE_TYPE_REVERSE_MAPPING[chineseType] || chineseType;
};

/**
 * 轉換中文緊急程度為英文
 * @param chineseLevel 中文緊急程度
 * @returns 英文緊急程度
 */
export const reverseTranslateUrgencyLevel = (chineseLevel: string): string => {
    return URGENCY_REVERSE_MAPPING[chineseLevel] || chineseLevel;
};

/**
 * 轉換中文狀態為英文
 * @param chineseStatus 中文狀態
 * @returns 英文狀態
 */
export const reverseTranslateStatus = (chineseStatus: string): string => {
    return STATUS_REVERSE_MAPPING[chineseStatus] || chineseStatus;
};

// =========================== 步驟流程配置 ===========================

/** 步驟項目介面 */
export interface StepItem {
    title: string;
    description: string | React.ReactNode;
    count?: number;
}

/** 處理步驟配置 */
export const STEP_ITEMS: StepItem[] = [
    {
        title: "待審核",
        description: "等待審核",
    },
    {
        title: "已審核",
        description: "審核通過",
    },
    {
        title: "已指派",
        description: "指派廠商",
    },
    {
        title: "施工中",
        description: "進行修繕",
    },
    {
        title: "已完成",
        description: "修繕完成",
    },
    {
        title: "已驗收",
        description: "驗收通過",
    },
    {
        title: "已結案",
        description: "案件結案",
    },
];

/** 狀態到步驟的映射 */
export const STATUS_STEP_MAPPING: Record<string, number> = {
    // 中文狀態映射
    "待審核": 0,
    "已審核": 1,
    "已指派": 2,
    "施工中": 3,
    "已完成": 4,
    "已驗收": 5,
    "已結案": 6,
    "已取消": -1,
    // 英文狀態映射
    "PENDING": 0,
    "APPROVED": 1,
    "ASSIGNED": 2,
    "IN_PROGRESS": 3,
    "COMPLETED": 4,
    "INSPECTED": 5,
    "CLOSED": 6,
    "CANCELLED": -1,
};

/**
 * 根據狀態獲取當前步驟
 * @param status 狀態值
 * @returns 步驟索引，-1 表示已取消
 */
export const getStepByStatus = (status: string): number => {
    return STATUS_STEP_MAPPING[status] ?? 0;
};

/**
 * 檢查狀態是否為已取消
 * @param status 狀態值
 * @returns 是否已取消
 */
export const isStatusCancelled = (status: string): boolean => {
    return status === "已取消" || status === "CANCELLED";
};

/**
 * 獲取步驟狀態（用於 Steps 組件）
 * @param status 當前狀態
 * @returns Steps 組件的狀態
 */
export const getStepStatus = (status: string): "error" | "process" => {
    return isStatusCancelled(status) ? "error" : "process";
};

/**
 * 根據統計數據創建帶件數的步驟項目
 * @param statistics 統計數據
 * @returns 帶件數的步驟項目陣列
 */
export const createStepItemsWithCount = (statistics: any): StepItem[] => {
    if (!statistics) {
        return STEP_ITEMS;
    }

    return STEP_ITEMS.map((item, index) => {
        let count = 0;

        switch (index) {
            case 0: // 待審核
                count = statistics.pendingCount || 0;
                break;
            case 1: // 已審核
                count = statistics.approvedCount || 0;
                break;
            case 2: // 已指派
                count = statistics.assignedCount || 0;
                break;
            case 3: // 施工中
                count = statistics.inProgressCount || 0;
                break;
            case 4: // 已完成
                count = statistics.completedCount || 0;
                break;
            case 5: // 已驗收
                count = statistics.inspectedCount || 0;
                break;
            case 6: // 已結案
                count = statistics.closedCount || 0;
                break;
            default:
                count = 0;
        }

        return {
            ...item,
            title: `${item.title}`,
            description: `${item.description} · 共 ${count} 件`,
            count
        };
    });
};

// =========================== 預設值配置 ===========================

/** 表單預設值 */
export const DEFAULT_VALUES = {
    maintenanceType: "設備維修",
    urgencyLevel: "中",
    status: "待審核"
};

/**
 * 根據統計數據計算整體進度的當前步驟
 * @param statistics 統計數據
 * @returns 當前步驟索引
 */
export const calculateOverallProgress = (statistics: any): number => {
    if (!statistics) {
        return 0;
    }

    // 如果有結案的案件，顯示為最後一步
    if (statistics.closedCount > 0) {
        return 6; // 已結案
    }

    // 如果有驗收的案件，顯示為驗收步驟
    if (statistics.inspectedCount > 0) {
        return 5; // 已驗收
    }

    // 如果有完成的案件，顯示為完成步驟
    if (statistics.completedCount > 0) {
        return 4; // 已完成
    }

    // 如果有施工中的案件，顯示為施工步驟
    if (statistics.inProgressCount > 0) {
        return 3; // 施工中
    }

    // 如果有指派的案件，顯示為指派步驟
    if (statistics.assignedCount > 0) {
        return 2; // 已指派
    }

    // 如果有審核通過的案件，顯示為審核步驟
    if (statistics.approvedCount > 0) {
        return 1; // 已審核
    }

    // 否則顯示為待審核步驟
    return 0; // 待審核
};

/**
 * 根據統計數據獲取整體進度狀態
 * @param statistics 統計數據
 * @returns 進度狀態
 */
export const getOverallProgressStatus = (statistics: any): "wait" | "process" | "finish" | "error" => {
    if (!statistics || statistics.totalCount === 0) {
        return "wait";
    }

    // 如果所有案件都已結案
    if (statistics.closedCount === statistics.totalCount) {
        return "finish";
    }

    // 如果有案件在進行中
    if (statistics.inProgressCount > 0 ||
        statistics.assignedCount > 0 ||
        statistics.approvedCount > 0 ||
        statistics.completedCount > 0 ||
        statistics.inspectedCount > 0) {
        return "process";
    }

    return "wait";
};

/**
 * 生成修繕申請單號
 * 格式：VMYYYYMMDD (VM + 西元年 + 月 + 日)
 * @param date 可選的日期，預設為當前日期
 * @returns 生成的修繕單號
 */
export const generateMaintenanceNumber = (date?: Date): string => {
    const targetDate = date || new Date();

    const year = targetDate.getFullYear();
    const month = String(targetDate.getMonth() + 1).padStart(2, '0');
    const day = String(targetDate.getDate()).padStart(2, '0');

    return `VM${year}${month}${day}`;
};

/**
 * 獲取緊急程度的排序權重（數字越大越緊急）
 * @param urgencyLevel 緊急程度值
 * @returns 排序權重
 */
export const getUrgencyPriority = (urgencyLevel: string): number => {
    const priorityMap: Record<string, number> = {
        // 中文映射
        "緊急": 4,
        "高": 3,
        "中": 2,
        "低": 1,
        // 英文映射
        "CRITICAL": 4,
        "HIGH": 3,
        "MEDIUM": 2,
        "LOW": 1
    };

    return priorityMap[urgencyLevel] || 0;
};

/**
 * 修繕申請單排序函數
 * 排序規則：申請日期(新->舊) > 緊急程度(高->低)
 * @param a 修繕申請單A
 * @param b 修繕申請單B
 * @returns 排序結果
 */
export const sortMaintenanceList = (a: any, b: any): number => {
    // 按申請日期排序 (新的在前)
    const dateA = a.applicationDate || 0;
    const dateB = b.applicationDate || 0;

    // 如果日期不同，按日期排序（新的在前）
    if (dateA !== dateB) {
        return dateB - dateA; // 降序：新日期在前
    }

    // 同一日期時，按緊急程度排序 (高優先級在前)
    const urgencyA = getUrgencyPriority(a.urgencyLevel || "");
    const urgencyB = getUrgencyPriority(b.urgencyLevel || "");

    return urgencyB - urgencyA; // 降序：高優先級在前
}; 