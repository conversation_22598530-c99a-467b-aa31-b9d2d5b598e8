using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface ITrainService
    {
        /// <summary>
        /// 取得所有教育訓練資料列表
        /// </summary>
        /// <param name="_userid">使用者編號</param>
        /// <returns>教育訓練資料列表</returns>
        Task<List<TrainDTO>> GetTrainListAsync(string _userid);

        /// <summary>
        /// 取得教育訓練資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>教育訓練資料明細</returns>
        Task<TrainDTO> GetTrainDetailAsync(string _uid);

        /// <summary>
        /// 新增教育訓練資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddTrainAsync(TrainDTO _data);

        /// <summary>
        /// 編輯教育訓練資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditTrainAsync(TrainDTO _data);

        /// <summary>
        /// 刪除教育訓練資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteTrainAsync(string _uid);
    }
}
