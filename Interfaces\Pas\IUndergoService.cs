using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IUndergoService
    {
        /// <summary>
        /// 取得所有歷任經歷資料列表
        /// </summary>
        /// <param name="_userid">使用者編號</param>
        /// <returns>歷任經歷資料列表</returns>
        Task<List<UndergoDTO>> GetUndergoListAsync(string _userid);

        /// <summary>
        /// 取得歷任經歷資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>歷任經歷資料明細</returns>
        Task<UndergoDTO> GetUndergoDetailAsync(string _uid);

        /// <summary>
        /// 新增歷任經歷資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddUndergoAsync(UndergoDTO _data);

        /// <summary>
        /// 編輯歷任經歷資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditUndergoAsync(UndergoDTO _data);

        /// <summary>
        /// 刪除歷任經歷資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteUndergoAsync(string _uid);
    }
}
