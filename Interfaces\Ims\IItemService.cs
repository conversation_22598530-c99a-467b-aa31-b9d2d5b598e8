using FAST_ERP_Backend.Models.Ims;
using System;
using System.Collections.Generic;
using System.Data;

namespace FAST_ERP_Backend.Interfaces.Ims;
/// <summary> 庫存品服務介面 </summary>
public interface IItemService
{
    /// <summary> 庫存品列表 </summary>
    Task<List<ItemDTO>> GetAllAsync();

    /// <summary> 庫存品取得 </summary>
    Task<List<ItemDTO>> GetAsync(Guid ItemID);

    /// <summary> 庫存品新增 </summary>
    Task<(bool, string)> AddAsync(ItemDTO item);

    /// <summary> 庫存品更新 </summary>
    Task<(bool, string)> UpdateAsync(ItemDTO item);

    /// <summary> 庫存品刪除 </summary>
    Task<(bool, string)> DeleteAsync(ItemDTO item);
}
