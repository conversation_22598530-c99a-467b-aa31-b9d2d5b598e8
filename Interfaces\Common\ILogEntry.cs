using System;
using System.Collections.Generic;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 日誌記錄接口
    /// </summary>
    public interface ILogEntry
    {
        #region Core Properties
        /// <summary> 日誌ID </summary>
        Guid Id { get; set; }

        /// <summary> 日誌級別 </summary>
        string Level { get; set; }

        /// <summary> 交易ID </summary>
        string TransactionId { get; set; }

        /// <summary> 記錄時間 </summary>
        DateTime CreateTime { get; set; }
        #endregion

        #region Content Properties
        /// <summary> 日誌消息 </summary>
        string? Message { get; set; }

        /// <summary> 異動資料 </summary>
        Dictionary<string, object>? Data { get; set; }

        /// <summary> 來源 </summary>
        string? Source { get; set; }
        #endregion

        #region Exception Properties
        /// <summary> 例外詳情 </summary>
        string? Exception { get; set; }

        /// <summary> 堆疊跟踪 </summary>
        string? StackTrace { get; set; }
        #endregion

        #region Context Properties
        /// <summary> 用戶ID </summary>
        string? UserId { get; set; }

        /// <summary> IP地址 </summary>
        string? IpAddress { get; set; }

        /// <summary> 瀏覽器信息 </summary>
        string? UserAgent { get; set; }

        /// <summary> 請求URL </summary>
        string? RequestUrl { get; set; }
        #endregion
    }
}