using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Interfaces.Ims;

/// <summary> 供應商分類服務介面 </summary>
public interface ISupplierCategoryService
{
    /// <summary> 取得所有供應商分類 </summary>
    Task<List<SupplierCategoryDTO>> GetAllAsync();

    /// <summary> 根據ID取得供應商分類 </summary>
    Task<SupplierCategoryDTO?> GetByIdAsync(Guid supplierCategoryId);

    /// <summary> 新增供應商分類 </summary>
    Task<(bool Success, string Message)> AddAsync(SupplierCategoryDTO dto);

    /// <summary> 更新供應商分類 </summary>
    Task<(bool Success, string Message)> UpdateAsync(SupplierCategoryDTO dto);

    /// <summary> 刪除供應商分類 </summary>
    Task<(bool Success, string Message)> DeleteAsync(Guid supplierCategoryId);

    /// <summary> 檢查分類層級 </summary>
    Task<bool> CheckLevelAsync(Guid? parentId, int maxDepth);

    /// <summary> 取得子分類 </summary>
    Task<List<SupplierCategoryDTO>> GetChildrenAsync(Guid parentId);

    /// <summary> 取得根分類 </summary>
    Task<List<SupplierCategoryDTO>> GetRootCategoriesAsync();
}
