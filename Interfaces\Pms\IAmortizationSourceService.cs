using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAmortizationSourceService
    {
        Task<List<AmortizationSourceDTO>> GetAllAsync();
        Task<string> GetByIdAsync(Guid id);
        Task<(bool, string)> AddAsync(AmortizationSourceDTO amortizationSource);
        Task<(bool, string)> UpdateAsync(AmortizationSourceDTO amortizationSource);
        Task<(bool, string)> DeleteAsync(AmortizationSourceDTO amortizationSource);
    }
}