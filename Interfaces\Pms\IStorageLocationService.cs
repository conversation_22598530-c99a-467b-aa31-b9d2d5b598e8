using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IStorageLocationService
    {
        /// <summary>
        /// 取得存放地點資料
        /// </summary>
        /// <param name="_locationId">存放地點編號</param>
        /// <returns>存放地點資料列表</returns>
        Task<List<StorageLocationDTO>> GetStorageLocationAsync(string _locationId = "");

        /// <summary>
        /// 新增存放地點資料
        /// </summary>
        /// <param name="_data">存放地點資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddStorageLocationAsync(StorageLocationDTO _data);

        /// <summary>
        /// 編輯存放地點資料
        /// </summary>
        /// <param name="_data">存放地點資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditStorageLocationAsync(StorageLocationDTO _data);

        /// <summary>
        /// 刪除存放地點資料
        /// </summary>
        /// <param name="_data">存放地點資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteStorageLocationAsync(StorageLocationDTO _data);

        /// <summary>
        /// 取得存放地點詳細資料
        /// </summary>
        /// <param name="_locationId">存放地點編號</param>
        /// <returns>存放地點詳細資料</returns>
        Task<StorageLocationDTO> GetStorageLocationDetailAsync(string _locationId);
    }
}