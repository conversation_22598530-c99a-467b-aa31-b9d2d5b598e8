﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Common
{
    public class SignalRMessageService : ISignalRMessageService
    {
        private readonly IHubContext<SignalRHub> _hubContext;

        public SignalRMessageService(IHubContext<SignalRHub> hubContext)
        {
            _hubContext = hubContext;
        }

        // 使用 SignalR 將訊息發送給所有客戶端
        public async Task<(bool, string)> SendToAllAsync(NotificationDto notification)
        {
            try
            {
                await _hubContext.Clients.All.SendAsync("ReceiveNotification", notification.Type, notification.Action, notification.Data);
                return (true, "發送成功。");
            }
            catch (Exception ex)
            {
                return (false, $"發送失敗: {ex.Message}");
            }
        }

        // 使用 SignalR 將訊息發送指定userId群組
        public async Task<(bool, string)> SendToUserIdGroupAsync(NotificationDto notification, string _userId)
        {
            try
            {
                await _hubContext.Clients.Group(_userId).SendAsync("ReceiveNotification", notification.Type, notification.Action, notification.Data);
                return (true, "發送成功。");
            }
            catch (Exception ex)
            {
                return (false, $"發送失敗: {ex.Message}");
            }
        }

        // 使用 SignalR 將訊息發送指定connectionId
        public async Task<(bool, string)> SendToConnectionIdAsync(NotificationDto notification, string _connectionId)
        {
            try
            {
                await _hubContext.Clients.Client(_connectionId).SendAsync("ReceiveNotification", notification.Type, notification.Action, notification.Data);

                return (true, "發送成功。");
            }
            catch (Exception ex)
            {
                return (false, $"發送失敗: {ex.Message}");
            }
        }

        // 測試範例
        public async Task<(bool, string)> SendTestMessageAsync(string _user,string _message,string _connectionId = "")
        {
            var notification = new NotificationDto
            {
                Type = "msgUI",           // 設定事件類型為 "msgUI"
                Action = "newmsg",        // 設定動作為 "newmsg"
                Data = new { User = _user, Message = _message }  // 設定額外資料 (例如訊息內容和發送者)
            };

            try
            {
                if (_connectionId != "")
                {
                    // 通知單一使用者
                    return await SendToConnectionIdAsync(notification, _connectionId);
                }
                else
                {
                    // 通知所有使用者
                    return await SendToAllAsync(notification);
                }
            }
            catch (Exception ex)
            {
                return (false, $"發送失敗: {ex.Message}");
            }
        }


        
    }
}
