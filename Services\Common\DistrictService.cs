﻿using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class DistrictService : IDistrictService
    {
        private readonly ERPDbContext _context;

        public DistrictService(ERPDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得區鄉鎮
        /// </summary>
        /// <param name="_districtId"></param>
        /// <returns></returns>
        public async Task<List<DistrictDTO>> GetDistrictAsync(string _districtId = "")
        {
            var query = _context.Set<District>().AsQueryable();

            if (!string.IsNullOrEmpty(_districtId))
            {
                query = query.Where(d => d.DistrictId == _districtId);
            }

            return await query.Select(d => new DistrictDTO
            {
                DistrictId = d.DistrictId,
                CityId = d.CityId,
                Name = d.Name,
                ZipCode = d.ZipCode,
                SortCode = d.SortCode,
                CreateTime = d.CreateTime,
                CreateUserId = d.CreateUserId,
                UpdateTime = d.UpdateTime,
                UpdateUserId = d.UpdateUserId,
                DeleteTime = d.DeleteTime,
                DeleteUserId = d.DeleteUserId
            }).ToListAsync();
        }

        /// <summary>
        /// 新增區鄉鎮
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddDistrictAsync(DistrictDTO _data)
        {
            try
            {
                var entity = new District
                {
                    DistrictId = _data.DistrictId,
                    CityId = _data.CityId,
                    Name = _data.Name,
                    ZipCode = _data.ZipCode,
                    SortCode = _data.SortCode,
                    CreateTime = _data.CreateTime,
                    CreateUserId = _data.CreateUserId,
                    UpdateTime = _data.UpdateTime,
                    UpdateUserId = _data.UpdateUserId,
                    DeleteTime = _data.DeleteTime,
                    DeleteUserId = _data.DeleteUserId
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯區鄉鎮
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditDistrictAsync(DistrictDTO _data)
        {
            try
            {
                var entity = await _context.Set<District>().FindAsync(_data.DistrictId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.CityId = _data.CityId;
                entity.Name = _data.Name;
                entity.ZipCode = _data.ZipCode;
                entity.SortCode = _data.SortCode;
                entity.CreateTime = _data.CreateTime;
                entity.CreateUserId = _data.CreateUserId;
                entity.UpdateTime = _data.UpdateTime;
                entity.UpdateUserId = _data.UpdateUserId;
                entity.DeleteTime = _data.DeleteTime;
                entity.DeleteUserId = _data.DeleteUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除區鄉鎮
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteDistrictAsync(DistrictDTO _data)
        {
            try
            {
                var entity = await _context.Set<District>().FindAsync(_data.DistrictId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得區鄉鎮詳細資料
        /// </summary>
        /// <param name="_districtId"></param>
        /// <returns></returns>
        public async Task<DistrictDTO> GetDistrictDetailAsync(string _districtId)
        {
            var entity = await _context.Set<District>().FindAsync(_districtId);
            if (entity == null)
            {
                return null;
            }

            return new DistrictDTO
            {
                DistrictId = entity.DistrictId,
                CityId = entity.CityId,
                Name = entity.Name,
                ZipCode = entity.ZipCode,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }

        /// <summary>
        /// 透過縣市取得區鄉鎮
        /// </summary>
        /// <param name="_cityId"></param>
        /// <returns></returns>
        public async Task<List<DistrictDTO>> GetDistrictByCityAsync(string _cityId)
        {
            var query = _context.Set<District>().AsQueryable();

            if (!string.IsNullOrEmpty(_cityId))
            {
                query = query.Where(d => d.CityId == _cityId);
            }

            return await query.Select(d => new DistrictDTO
            {
                DistrictId = d.DistrictId,
                CityId = d.CityId,
                Name = d.Name,
                ZipCode = d.ZipCode,
                SortCode = d.SortCode,
                CreateTime = d.CreateTime,
                CreateUserId = d.CreateUserId,
                UpdateTime = d.UpdateTime,
                UpdateUserId = d.UpdateUserId,
                DeleteTime = d.DeleteTime,
                DeleteUserId = d.DeleteUserId
            }).ToListAsync();
        }
    }
}


