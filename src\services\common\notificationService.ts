import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 通知類型枚舉
export enum NotificationType {
    System = "System",
    Personal = "Personal"
}

// 通知介面
export interface Notification {
    notificationId: string;
    type: NotificationType;
    title: string;
    content: string;
    isRead: boolean;
    createTime: number;
    createUserId: string;
    readTime?: number;
}

// 獲取系統通知
export async function getSystemNotifications(): Promise<ApiResponse<Notification[]>> {
    try {
        const response = await httpClient(apiEndpoints.getSystemNotifications, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統通知失敗",
            data: []
        };
    }
}

// 獲取個人通知
export async function getPersonalNotifications(): Promise<ApiResponse<Notification[]>> {
    try {
        const response = await httpClient(apiEndpoints.getPersonalNotifications, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取個人通知失敗",
            data: []
        };
    }
}

// 標記通知為已讀
export async function markNotificationAsRead(notificationId: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.markNotificationAsRead, {
            method: "POST",
            body: JSON.stringify({ notificationId }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "標記通知已讀失敗",
        };
    }
} 