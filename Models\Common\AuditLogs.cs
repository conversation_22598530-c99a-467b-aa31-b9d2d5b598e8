﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FAST_ERP_Backend.Models.Common
{
    public class AuditLogsDTO
    {
        public string AuditLogsId { get; set; } //日誌編號
        public string LogContent { get; set; } //操作內容
        public string? IP { get; set; } //操作者IP
        public string? RequestUrl { get; set; } //連線路徑
        public string? Agent { get; set; } //操作資訊
        public AuditLogsDTO()
        {
            AuditLogsId = "";
            LogContent = "";
            IP = null;
            RequestUrl = null;
            Agent = null;
        }
    }

    public class AuditLogs
    {
        [Key]
        [Comment("日誌編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string AuditLogsId { get; set; } //日誌編號

        [Comment("操作內容")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string LogContent { get; set; } //操作內容

        [Comment("操作者IP")]
        [Column(TypeName = "nvarchar(50)")]
        public string? IP { get; set; } //操作者IP

        [Comment("連線路徑")]
        [Column(TypeName = "nvarchar(50)")]
        public string? RequestUrl { get; set; } //連線路徑

        [Comment("操作資訊")]
        [Column(TypeName = "nvarchar(50)")]
        public string? Agent { get; set; } //操作資訊

        [Comment("新增時間")]
        [Column(TypeName = "bigint")]
        public long? CreateTime { get; set; } //新增時間

        [Comment("新增者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? CreateUserId { get; set; } //新增者編號

        public AuditLogs()
        {
            AuditLogsId = "";
            LogContent = "";
            IP = null;
            RequestUrl = null;
            Agent = null;
            CreateTime = null;
            CreateUserId = null;
        }
    }
}
