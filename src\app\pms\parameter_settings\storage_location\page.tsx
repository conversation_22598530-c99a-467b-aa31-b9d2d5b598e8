"use client";

/* 存放地點管理
  /app/pms/parameter_settings/storage_location/page.tsx
*/

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Tag,
  Tooltip,
  List,
  Typography,
  Popover,
  Descriptions,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  EnvironmentOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  StorageLocation,
  getStorageLocations,
  createStorageLocation,
  updateStorageLocation,
  deleteStorageLocation,
} from "@/services/pms/storageLocationService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { useAuth } from "@/contexts/AuthContext";
import GoogleMapsPreview from "@/app/components/common/GoogleMapsPreview";

const { Text } = Typography;

const StorageLocationPage: React.FC = () => {
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>(
    []
  );
  const [filteredLocations, setFilteredLocations] = useState<StorageLocation[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingLocation, setEditingLocation] =
    useState<StorageLocation | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [locationToDelete, setLocationToDelete] =
    useState<StorageLocation | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // 加載存放位置列表
  const loadStorageLocations = async () => {
    setLoading(true);
    try {
      const response = await getStorageLocations();
      if (response.success && response.data) {
        // 依照 sortCode 排序
        const sortedData = response.data.sort(
          (a, b) => a.sortCode - b.sortCode
        );
        setStorageLocations(sortedData);
        setFilteredLocations(sortedData);
      } else {
        notifyError("獲取存放位置列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取存放位置列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStorageLocations();
  }, []);

  // 處理搜尋
  useEffect(() => {
    if (searchText) {
      const filtered = storageLocations.filter(
        (location) =>
          location.name.toLowerCase().includes(searchText.toLowerCase()) ||
          location.address.toLowerCase().includes(searchText.toLowerCase()) ||
          location.description.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredLocations(filtered);
    } else {
      setFilteredLocations(storageLocations);
    }
  }, [searchText, storageLocations]);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 表格列定義
  const columns: ColumnsType<StorageLocation> = [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "名稱",
      dataIndex: "name",
      key: "name",
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="名稱">
                  {record.name}
                </Descriptions.Item>
                <Descriptions.Item label="地址">
                  {record.address}
                </Descriptions.Item>
                <Descriptions.Item label="說明">
                  {record.description}
                </Descriptions.Item>
                <Descriptions.Item label="地址">
                  <Popover title="位置預覽">
                    <GoogleMapsPreview address={record.address} />
                  </Popover>
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
          styles={{
            root: { maxWidth: "400px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "地址",
      dataIndex: "address",
      key: "address",
      render: (text) => (
        <span>
          <Popover
            content={<GoogleMapsPreview address={text} />}
            title="位置預覽"
            trigger="hover"
          >
            <EnvironmentOutlined style={{ marginRight: 5, color: "#1890ff" }} />
          </Popover>
          {text}
        </span>
      ),
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (text) => (
        <span>{DateTimeExtensions.formatFromTimestamp(text)}</span>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  // 處理編輯
  const handleEdit = (location: StorageLocation) => {
    setEditingLocation(location);
    form.setFieldsValue(location);
    setIsModalVisible(true);
  };

  // 處理刪除
  const handleDelete = (location: StorageLocation) => {
    setLocationToDelete(location);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!locationToDelete || !user?.userId) return;

    try {
      const response = await deleteStorageLocation({
        storageLocationId: locationToDelete.storageLocationId,
        deleteUserId: user.userId,
      });
      if (response.success) {
        notifySuccess("刪除成功", "存放位置已刪除");
        loadStorageLocations();
        setDeleteModalVisible(false);
        setLocationToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      notifyError("刪除失敗", "請稍後再試");
    }
  };

  // 打開新增存放位置表單
  const showAddModal = () => {
    setEditingLocation(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 確保 sortCode 有值，如果表單中不存在則使用預設值
      const sortCode =
        values.sortCode !== undefined
          ? Number(values.sortCode)
          : editingLocation
          ? editingLocation.sortCode
          : storageLocations.length > 0
          ? Math.max(...storageLocations.map((item) => item.sortCode)) + 1
          : 1;

      const submitData = {
        ...values,
        sortCode,
      };

      if (editingLocation) {
        // 更新存放位置
        const response = await updateStorageLocation({
          ...submitData,
          storageLocationId: editingLocation.storageLocationId,
          updateUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("更新成功", "存放位置已更新");
          loadStorageLocations();
        } else {
          notifyError("更新失敗", response.message || "請稍後再試");
        }
      } else {
        // 新增存放位置
        const response = await createStorageLocation({
          ...submitData,
          createUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("新增成功", "存放位置已新增");
          loadStorageLocations();
        } else {
          notifyError("新增失敗", response.message || "請稍後再試");
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error("操作失敗:", error);
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredLocations}
        renderItem={(location) => (
          <List.Item
            key={location.storageLocationId}
            style={{
              padding: "12px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <div style={{ width: "100%" }}>
              {/* 標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <div
                  style={{
                    width: 24,
                    height: 24,
                    background: "#f0f0f0",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "14px",
                    flexShrink: 0,
                  }}
                >
                  {filteredLocations.indexOf(location) + 1}
                </div>
                <Text strong style={{ fontSize: "16px" }}>
                  {location.name}
                </Text>
              </div>

              {/* 地址列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <Popover
                  content={<GoogleMapsPreview address={location.address} />}
                  title="位置預覽"
                  trigger="hover"
                >
                  <EnvironmentOutlined
                    style={{
                      color: "#1890ff",
                      fontSize: "16px",
                      marginTop: "4px",
                      flexShrink: 0,
                    }}
                  />
                </Popover>
                <Text style={{ fontSize: "15px", lineHeight: "1.5" }}>
                  {location.address}
                </Text>
              </div>

              {/* 說明列 */}
              {location.description && (
                <div style={{ marginBottom: "8px" }}>
                  <Text style={{ fontSize: "14px", color: "#666" }}>
                    {location.description}
                  </Text>
                </div>
              )}

              {/* 時間列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "13px" }}>
                  建立時間：
                  {DateTimeExtensions.formatFromTimestamp(location.createTime)}
                </Text>
              </div>

              {/* 操作按鈕列 */}
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginTop: "12px",
                }}
              >
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(location)}
                  style={{ padding: 0 }}
                >
                  編輯
                </Button>
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(location)}
                  style={{ padding: 0 }}
                >
                  刪除
                </Button>
              </div>
            </div>
          </List.Item>
        )}
        pagination={{
          onChange: (page) => {
            console.log(page);
          },
          pageSize: 10,
          size: "small",
          style: { marginTop: "16px" },
        }}
      />
    );
  };

  return (
    <Card
      title="存放地點管理"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
        wrap
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增存放位置
        </Button>
        {/* 搜尋 */}
        <Input
          placeholder="搜尋位置名稱、地址或說明"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: "100%" }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredLocations}
          rowKey="storageLocationId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingLocation ? "編輯存放位置" : "新增存放位置"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        maskClosable={false}
        okText="確認"
        cancelText="取消"
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="位置名稱："
            rules={[{ required: true, message: "請輸入位置名稱" }]}
          >
            <Input placeholder="請輸入位置名稱" maxLength={50} />
          </Form.Item>

          <Form.Item
            name="address"
            label="地址："
            rules={[{ required: true, message: "請輸入地址" }]}
          >
            <Input placeholder="請輸入地址" maxLength={100} />
          </Form.Item>

          {/* <Form.Item
            name="sortCode"
            label="排序碼："
            rules={[{ required: true, message: "請輸入排序碼" }]}
            tooltip="數字越小排序越前面"
          >
            <InputNumber
              placeholder="請輸入排序碼"
              style={{ width: "100%" }}
              min={0}
            />
          </Form.Item> */}

          <Form.Item name="description" label="說明：">
            <Input.TextArea
              placeholder="請輸入說明"
              autoSize={{ minRows: 3, maxRows: 6 }}
              maxLength={200}
              showCount
            />
          </Form.Item>

          {editingLocation && (
            <>
              <Form.Item label="建立時間：">
                <span>
                  {DateTimeExtensions.formatFromTimestamp(
                    editingLocation.createTime
                  )}
                </span>
              </Form.Item>

              {editingLocation.updateTime && (
                <Form.Item label="更新時間：">
                  <span>
                    {DateTimeExtensions.formatFromTimestamp(
                      editingLocation.updateTime
                    )}
                  </span>
                </Form.Item>
              )}
            </>
          )}
        </Form>
      </Modal>

      {/* 刪除確認 Modal */}
      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setLocationToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled: deleteConfirmText !== (locationToDelete?.name || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{locationToDelete?.name}」</strong>以確認刪除：
          </p>
          <Input
            placeholder="請輸入位置名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default StorageLocationPage;
