.loginContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    background-image: url("/background.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.loginCard {
    width: 100%;
    max-width: 400px;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loginCard :global(.ant-card-head-title) {
    padding: 16px 0;
}

.cardTitle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
}

.loginButton {
    height: 40px;
    font-size: 16px;
}