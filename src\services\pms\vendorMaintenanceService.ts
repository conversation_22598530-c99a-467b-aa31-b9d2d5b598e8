import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";
import {
    VendorMaintenance,
    VendorMaintenanceQuery,
    ApprovalRequest,
    AssignVendorRequest,
    StartWorkRequest,
    CompleteMaintenanceRequest,
    InspectionRequest,
    CloseRequest,
    CancelRequest,
    BatchRequest,
    MaintenanceStatistics
} from "@/app/pms/document_maintenance/vendor_maintenance_form/interface";

// 取得修繕申請列表
export async function getVendorMaintenances(query?: VendorMaintenanceQuery): Promise<ApiResponse<VendorMaintenance[]>> {
    try {
        const params = new URLSearchParams();
        if (query?.status) params.append('status', query.status);
        if (query?.maintenanceType) params.append('maintenanceType', query.maintenanceType);
        if (query?.urgencyLevel) params.append('urgencyLevel', query.urgencyLevel);
        if (query?.startDate) params.append('startDate', query.startDate.toString());
        if (query?.endDate) params.append('endDate', query.endDate.toString());

        const url = params.toString() ? `${apiEndpoints.getVendorMaintenances}?${params}` : apiEndpoints.getVendorMaintenances;

        const response = await httpClient(url, {
            method: "GET",
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取修繕申請列表失敗",
            data: []
        };
    }
}

// 根據修繕單號取得詳細資料
export async function getVendorMaintenanceById(maintenanceNumber: string): Promise<ApiResponse<VendorMaintenance>> {
    try {
        const response = await httpClient(`${apiEndpoints.getVendorMaintenanceById}/${maintenanceNumber}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取修繕申請詳細資料失敗",
        };
    }
}

// 新增修繕申請
export async function createVendorMaintenance(data: Partial<VendorMaintenance>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addVendorMaintenance, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增修繕申請失敗",
        };
    }
}

// 修改修繕申請
export async function updateVendorMaintenance(maintenanceNumber: string, data: Partial<VendorMaintenance>): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.editVendorMaintenance}?maintenanceNumber=${maintenanceNumber}`, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "修改修繕申請失敗",
        };
    }
}

// 刪除修繕申請
export async function deleteVendorMaintenance(maintenanceNumber: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.deleteVendorMaintenance}?maintenanceNumber=${maintenanceNumber}`, {
            method: "POST",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除修繕申請失敗",
        };
    }
}

// 審核修繕申請
export async function approveVendorMaintenance(maintenanceNumber: string, request: ApprovalRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.approveVendorMaintenance}/${maintenanceNumber}/approve`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "審核修繕申請失敗",
        };
    }
}

// 指派廠商
export async function assignVendor(maintenanceNumber: string, request: AssignVendorRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.assignVendor}/${maintenanceNumber}/assign-vendor`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "指派廠商失敗",
        };
    }
}

// 開始施工
export async function startWork(maintenanceNumber: string, request: StartWorkRequest): Promise<ApiResponse> {
    try {
        console.log(`開始施工 API 請求路徑: ${apiEndpoints.startWork}/${maintenanceNumber}/start`);
        console.log('開始施工 API 請求資料:', request);

        const response = await httpClient(`${apiEndpoints.startWork}/${maintenanceNumber}/start`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });

        console.log('開始施工 API 原始回應:', response);
        return response;
    } catch (error: any) {
        console.error('開始施工 API 請求失敗:', error);
        return {
            success: false,
            message: error.message || "開始施工失敗",
        };
    }
}

// 完成修繕
export async function completeMaintenance(maintenanceNumber: string, request: CompleteMaintenanceRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.completeMaintenance}/${maintenanceNumber}/complete`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "完成修繕失敗",
        };
    }
}

// 驗收修繕
export async function inspectMaintenance(maintenanceNumber: string, request: InspectionRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.inspectMaintenance}/${maintenanceNumber}/inspect`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "驗收修繕失敗",
        };
    }
}

// 結案
export async function closeMaintenance(maintenanceNumber: string, request: CloseRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.closeMaintenance}/${maintenanceNumber}/close`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "結案失敗",
        };
    }
}

// 取消修繕
export async function cancelMaintenance(maintenanceNumber: string, request: CancelRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(`${apiEndpoints.cancelMaintenance}/${maintenanceNumber}/cancel`, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取消修繕失敗",
        };
    }
}

// 批次處理修繕申請
export async function batchProcessMaintenance(request: BatchRequest): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.batchProcessMaintenance, {
            method: "POST",
            body: JSON.stringify(request),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "批次處理失敗",
        };
    }
}

// 取得修繕統計資料
export async function getMaintenanceStatistics(startDate?: number, endDate?: number): Promise<ApiResponse<MaintenanceStatistics>> {
    try {
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate.toString());
        if (endDate) params.append('endDate', endDate.toString());

        const url = params.toString() ? `${apiEndpoints.getMaintenanceStatistics}?${params}` : apiEndpoints.getMaintenanceStatistics;

        const response = await httpClient(url, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取統計資料失敗",
        };
    }
}

// 取得逾期修繕案件
export async function getOverdueMaintenance(): Promise<ApiResponse<VendorMaintenance[]>> {
    try {
        const response = await httpClient(apiEndpoints.getOverdueMaintenance, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取逾期案件失敗",
            data: []
        };
    }
} 