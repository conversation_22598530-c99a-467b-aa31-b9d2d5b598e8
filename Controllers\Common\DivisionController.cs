using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("組別資料管理")]
    public class DivisionController : ControllerBase
    {
        private readonly IDivisionService _Interface;

        public DivisionController(IDivisionService divisionService)
        {
            _Interface = divisionService;
        }

        [HttpGet]
        [Route("GetDivision")]
        [SwaggerOperation(Summary = "取得組別列表", Description = "取得所有組別列表")]
        public async Task<IActionResult> GetDivisionList()
        {
            var divisions = await _Interface.GetDivisionAsync();
            return Ok(divisions);
        }

        [HttpGet]
        [Route("GetDivision/{_divisionId?}")]
        [SwaggerOperation(Summary = "取得組別明細", Description = "依ID取得組別明細")]
        public async Task<IActionResult> GetDivisionDetail(string _divisionId)
        {
            var division = await _Interface.GetDivisionDetailAsync(_divisionId);
            return Ok(division);
        }

        [HttpPost]
        [Route("AddDivision")]
        [SwaggerOperation(Summary = "新增組別", Description = "新增組別資料")]
        public async Task<IActionResult> AddDivision([FromBody] DivisionDTO _data)
        {
            var (result, msg) = await _Interface.AddDivisionAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditDivision")]
        [SwaggerOperation(Summary = "編輯組別", Description = "修改已存在之組別資料")]
        public async Task<IActionResult> EditDivision([FromBody] DivisionDTO _data)
        {
            var (result, msg) = await _Interface.EditDivisionAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteDivision")]
        [SwaggerOperation(Summary = "刪除組別", Description = "刪除已存在之組別資料")]
        public async Task<IActionResult> DeleteDivision([FromBody] DivisionDTO _data)
        {
            var (result, msg) = await _Interface.DeleteDivisionAsync(_data);
            return Ok(new { result, msg });
        }
    }
}