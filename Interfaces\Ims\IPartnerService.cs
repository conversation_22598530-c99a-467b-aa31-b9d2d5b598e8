using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Interfaces.Ims;

/// <summary> 商業夥伴服務介面 </summary>
public interface IPartnerService
{
    /// <summary> 商業夥伴列表取得 </summary>
    Task<List<PartnerDTO>> GetAllAsync();
    /// <summary> 商業夥伴取得 </summary>
    Task<PartnerDTO> GetAsync(Guid PartnerID);
    /// <summary> 商業夥伴新增 </summary>
    Task<(bool, string)> AddAsync(PartnerDTO dto);
    /// <summary> 商業夥伴更新 </summary>
    Task<(bool, string)> UpdateAsync(PartnerDTO dto);
    /// <summary> 商業夥伴刪除 </summary>
    Task<(bool, string)> DeleteAsync(Guid PartnerID);
}