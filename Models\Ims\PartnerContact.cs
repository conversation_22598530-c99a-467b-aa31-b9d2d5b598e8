using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 商業夥伴聯絡人聯結表 </summary>
public class PartnerContact
{
    /// <summary> 商業夥伴編號 </summary>
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 聯絡人編號 </summary>
    [Comment("聯絡人編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ContactID { get; set; }

    /// <summary> 聯絡人角色編號 </summary>
    [Comment("聯絡人角色編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ContactRoleID { get; set; }

    /// <summary> 是否為主要聯絡人 (針對此夥伴) </summary>
    [Comment("是否為主要聯絡人 (針對此夥伴)")]
    public bool IsPrimary { get; set; }

    /// <summary> 導覽屬性 - 商業夥伴 </summary>
    public Partner Partner { get; set; } = null!;

    /// <summary> 導覽屬性 - 聯絡人 </summary>
    public Contact Contact { get; set; } = null!;

    /// <summary> 導覽屬性 - 聯絡人角色 </summary>
    public ContactRole ContactRole { get; set; } = null!;

    /// <summary> 建構式 </summary>
    public PartnerContact()
    {
        IsPrimary = false;
    }
}

/// <summary> 商業夥伴聯絡人聯結表 DTO </summary>
public class PartnerContactDTO
{
    /// <summary> 商業夥伴編號 </summary>
    public Guid PartnerID { get; set; }

    /// <summary> 聯絡人編號 </summary>
    public Guid ContactID { get; set; }

    /// <summary> 聯絡人角色編號 </summary>
    public Guid ContactRoleID { get; set; }

    /// <summary> 是否為主要聯絡人 (針對此夥伴) </summary>
    public bool IsPrimary { get; set; }

    /// <summary> 建構式 </summary>
    public PartnerContactDTO()
    {
        IsPrimary = false;
    }
}