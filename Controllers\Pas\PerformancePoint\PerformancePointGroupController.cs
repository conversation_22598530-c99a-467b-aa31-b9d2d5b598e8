using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("點數群組管理")]
    public class PerformancePointGroupController : ControllerBase
    {
        private readonly IPerformancePointGroupService _service;

        public PerformancePointGroupController(IPerformancePointGroupService service)
        {
            _service = service;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得點數群組列表", Description = "取得所有點數群組資料列表")]
        public async Task<IActionResult> GetAll()
        {
            var result = await _service.GetAllAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得點數群組明細", Description = "依UID取得點數群組明細資料")]
        public async Task<IActionResult> GetDetail(string uid)
        {
            var result = await _service.GetDetailAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增點數群組資料", Description = "新增點數群組資料")]
        public async Task<IActionResult> Add([FromBody] PerformancePointGroupDTO data)
        {
            var (result, msg) = await _service.AddAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯點數群組資料", Description = "編輯點數群組資料")]
        public async Task<IActionResult> Edit([FromBody] PerformancePointGroupDTO data)
        {
            var (result, msg) = await _service.EditAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除點數群組資料", Description = "刪除點數群組資料")]
        public async Task<IActionResult> Delete([FromBody] string uid)
        {
            var (result, msg) = await _service.DeleteAsync(uid);
            return Ok(new { result, msg });
        }
    }
}
