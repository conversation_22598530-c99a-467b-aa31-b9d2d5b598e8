using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class TrainService : ITrainService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public TrainService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<TrainDTO>> GetTrainListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Train
                    .Where(t => t.userId == userId && t.IsDeleted != true)
                    .OrderByDescending(t => t.courseEndDate)
                    .Select(t => new TrainDTO
                    {
                        uid = t.uid,
                        userId = t.userId,
                        courseName = t.courseName,
                        cost = t.cost,
                        ranking = t.ranking,
                        score = t.score,
                        instructor = t.instructor,
                        durationHours = t.durationHours,
                        trainingInstitute = t.trainingInstitute,
                        courseStartDate = _baseform.TimestampToDateStr(t.courseStartDate),
                        courseEndDate = _baseform.TimestampToDateStr(t.courseEndDate),
                        certificateDate = _baseform.TimestampToDateStr(t.certificateDate),
                        certificateNumber = t.certificateNumber,
                        remark = t.remark,
                        UpdateTime = t.UpdateTime
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得訓練資料錯誤", ex);
            }
        }

        public async Task<TrainDTO> GetTrainDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Train
                    .Where(t => t.uid == uid && t.IsDeleted != true)
                    .Select(t => new TrainDTO
                    {
                        uid = t.uid,
                        userId = t.userId,
                        courseName = t.courseName,
                        cost = t.cost,
                        ranking = t.ranking,
                        score = t.score,
                        instructor = t.instructor,
                        durationHours = t.durationHours,
                        trainingInstitute = t.trainingInstitute,
                        courseStartDate = _baseform.TimestampToDateStr(t.courseStartDate),
                        courseEndDate = _baseform.TimestampToDateStr(t.courseEndDate),
                        certificateDate = _baseform.TimestampToDateStr(t.certificateDate),
                        certificateNumber = t.certificateNumber,
                        remark = t.remark,
                        UpdateTime = t.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得訓練明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddTrainAsync(TrainDTO data)
        {
            List<string> list_msg_check = CheckTrainInput(data, "add");

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newTrain = new Train
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    courseName = data.courseName,
                    cost = data.cost,
                    ranking = data.ranking,
                    score = data.score,
                    instructor = data.instructor,
                    durationHours = data.durationHours,
                    trainingInstitute = data.trainingInstitute,
                    courseStartDate = _baseform.DateStrToTimestamp(data.courseStartDate),
                    courseEndDate = _baseform.DateStrToTimestamp(data.courseEndDate),
                    certificateDate = _baseform.DateStrToTimestamp(data.certificateDate),
                    certificateNumber = data.certificateNumber,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Train.AddAsync(newTrain);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增訓練資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增訓練資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditTrainAsync(TrainDTO data)
        {
            List<string> list_msg_check = CheckTrainInput(data, "edit");

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingTrain = await _context.Pas_Train.FirstOrDefaultAsync(t => t.uid == data.uid && t.IsDeleted != true);
                if (existingTrain == null)
                {
                    return (false, "找不到對應的訓練資料");
                }

                existingTrain.courseName = data.courseName;
                existingTrain.cost = data.cost;
                existingTrain.ranking = data.ranking;
                existingTrain.score = data.score;
                existingTrain.instructor = data.instructor;
                existingTrain.durationHours = data.durationHours;
                existingTrain.trainingInstitute = data.trainingInstitute;
                existingTrain.courseStartDate = _baseform.DateStrToTimestamp(data.courseStartDate);
                existingTrain.courseEndDate = _baseform.DateStrToTimestamp(data.courseEndDate);
                existingTrain.certificateDate = _baseform.DateStrToTimestamp(data.certificateDate);
                existingTrain.certificateNumber = data.certificateNumber;
                existingTrain.remark = data.remark;

                existingTrain.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                existingTrain.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯訓練資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯訓練資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteTrainAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingTrain = await _context.Pas_Train.FirstOrDefaultAsync(t => t.uid == uid && t.IsDeleted != true);
                if (existingTrain == null)
                {
                    return (false, "資料已刪除或不存在");
                }

                existingTrain.IsDeleted = true;
                existingTrain.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                existingTrain.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除訓練資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除訓練資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckTrainInput(TrainDTO _data, string _mode)
        {
            List<string> list_errorMsg = new List<string>();

            // 檢核item.
            if (_data.courseName == "")
            {
                list_errorMsg.Add("請輸入課程名稱");
            }

            if (_data.trainingInstitute == "")
            {
                list_errorMsg.Add("請輸入訓練機構名稱");
            }

            if (_data.courseStartDate != "" && !_baseform.IsValidDateOrEmpty(_data.courseStartDate))
            {
                list_errorMsg.Add("課程起日格式輸入錯誤");
            }

            if (_data.courseEndDate != "" && !_baseform.IsValidDateOrEmpty(_data.courseEndDate))
            {
                list_errorMsg.Add("課程迄日格式輸入錯誤");
            }

            if (_data.certificateDate != "" && !_baseform.IsValidDateOrEmpty(_data.certificateDate))
            {
                list_errorMsg.Add("發證日期格式輸入錯誤");
            }

            return list_errorMsg;
        }
    }
}
