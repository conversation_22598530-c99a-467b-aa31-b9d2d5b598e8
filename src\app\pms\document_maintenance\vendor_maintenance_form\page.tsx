"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  Card,
  Table,
  Button,
  Modal,
  message,
  Row,
  Col,
  Select,
  DatePicker,
  Input,
  Space,
  Statistic,
  Form,
  InputNumber,
  Popconfirm,
  Tag,
  Tooltip,
  Badge,
  Steps,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  Bar<PERSON>hartOutlined,
  WarningOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SafetyCertificateOutlined,
  FileProtectOutlined,
  StopOutlined,
  UserOutlined,
  TeamOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useForm } from "antd/es/form/Form";
import {
  VendorMaintenance,
  VendorMaintenanceQuery,
  ApprovalRequest,
  AssignVendorRequest,
  StartWorkRequest,
  CompleteMaintenanceRequest,
  InspectionRequest,
  CloseRequest,
  CancelRequest,
  MaintenanceStatistics,
  STATUS_COLORS,
} from "./interface";
import {
  MAINTENANCE_TYPES,
  URGENCY_LEVELS,
  STATUS_OPTIONS,
  STEP_ITEMS,
  StepItem,
  getStepByStatus,
  isStatusCancelled,
  getStepStatus,
  createStepItemsWithCount,
  calculateOverallProgress,
  getOverallProgressStatus,
  sortMaintenanceList,
} from "./config";
import {
  getVendorMaintenances,
  getVendorMaintenanceById,
  deleteVendorMaintenance,
  approveVendorMaintenance,
  assignVendor,
  startWork,
  completeMaintenance,
  inspectMaintenance,
  closeMaintenance,
  cancelMaintenance,
  getMaintenanceStatistics,
  getOverdueMaintenance,
} from "@/services/pms/vendorMaintenanceService";
import { getAssets } from "@/services/pms/assetService";
import { getUsers, User } from "@/services/common/userService";
import { getDepartments } from "@/services/common/departmentService";
import { getColumns, getMobileColumns } from "./columns";
import VendorMaintenanceForm from "./Form";
import { Grid } from "antd";
import styles from "./page.module.css";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { siteConfig } from "@/config/site";

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

// getStepByStatus 函數和 stepItems 配置已移至 config.ts 統一管理

const VendorMaintenancePage: React.FC = () => {
  // 狀態管理
  const [maintenances, setMaintenances] = useState<VendorMaintenance[]>([]);
  const [loading, setLoading] = useState(false);
  const [startWorkLoading, setStartWorkLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [statistics, setStatistics] = useState<MaintenanceStatistics | null>(
    null
  );
  const [overdueList, setOverdueList] = useState<VendorMaintenance[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // Modal 狀態
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [startWorkModalVisible, setStartWorkModalVisible] = useState(false);
  const [completeModalVisible, setCompleteModalVisible] = useState(false);
  const [inspectModalVisible, setInspectModalVisible] = useState(false);
  const [closeModalVisible, setCloseModalVisible] = useState(false);
  const [cancelModalVisible, setCancelModalVisible] = useState(false);
  const [statisticsModalVisible, setStatisticsModalVisible] = useState(false);

  // 當前操作的資料
  const [editingMaintenance, setEditingMaintenance] =
    useState<VendorMaintenance | null>(null);
  const [currentMaintenance, setCurrentMaintenance] =
    useState<VendorMaintenance | null>(null);
  const [isViewMode, setIsViewMode] = useState(false);

  // 查詢參數
  const [searchQuery, setSearchQuery] = useState<VendorMaintenanceQuery>({});

  // 表單實例
  const [form] = useForm(); // 為新增/編輯創建表單實例
  const [detailForm] = useForm(); // 為詳情查看創建獨立的表單實例
  const [searchForm] = useForm();
  const [approvalForm] = useForm();
  const [assignForm] = useForm();
  const [startWorkForm] = useForm();
  const [completeForm] = useForm();
  const [inspectForm] = useForm();
  const [closeForm] = useForm();
  const [cancelForm] = useForm();

  // 響應式設計
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // 確保只在客戶端執行
    if (typeof window !== "undefined") {
      setIsMobile(!screens.md);
    }
  }, [screens.md]);

  // 載入用戶資料
  const loadUsers = async () => {
    try {
      const response = await getUsers();

      if (response.success && response.data) {
        setUsers(response.data);
      } else {
        console.error("用戶資料載入失敗:", response.message);
      }
    } catch (error) {
      console.error("載入用戶資料失敗:", error);
    }
  };

  // 載入資料
  const loadData = async () => {
    setLoading(true);
    try {
      const response = await getVendorMaintenances(searchQuery);

      if (response.success && response.data) {
        // 處理嵌套的 data 結構
        let dataArray: VendorMaintenance[] = [];
        if (
          (response.data as any).data &&
          Array.isArray((response.data as any).data)
        ) {
          dataArray = (response.data as any).data;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        }

        // 補充顯示名稱
        await enrichMaintenanceData(dataArray);

        // 按緊急程度(高->低)、申請日期排序
        dataArray.sort(sortMaintenanceList);

        setMaintenances(dataArray);
      } else {
        setMaintenances([]); // 設置為空陣列而不是保持原狀態
        message.error(response.message || "載入資料失敗");
      }
    } catch (error) {
      setMaintenances([]); // 出錯時也設置為空陣列
      message.error("載入資料失敗");
    } finally {
      setLoading(false);
    }
  };

  // 補充資料的顯示名稱
  const enrichMaintenanceData = async (dataArray: VendorMaintenance[]) => {
    try {
      // 並行獲取所有需要的資料
      const [assetsResponse, usersResponse, departmentsResponse] =
        await Promise.all([getAssets(), getUsers(), getDepartments()]);

      const assets = assetsResponse.success ? assetsResponse.data || [] : [];
      const users = usersResponse.success ? usersResponse.data || [] : [];
      const departments = departmentsResponse.success
        ? departmentsResponse.data || []
        : [];

      // 為每筆維修資料補充顯示名稱
      dataArray.forEach((maintenance) => {
        // 財產名稱
        if (maintenance.assetId && !maintenance.assetName) {
          const assetDetail = assets.find(
            (a) => a.asset.assetId === maintenance.assetId
          );
          if (assetDetail) {
            maintenance.assetName =
              assetDetail.asset.assetName || assetDetail.asset.assetNo || "";
          }
        }

        // 補充申請人姓名
        if (maintenance.applicantId && !maintenance.applicantName) {
          const applicant = users.find(
            (u) => u.userId === maintenance.applicantId
          );
          if (applicant) {
            maintenance.applicantName =
              applicant.name || applicant.account || "";
          }
        }

        // 補充申請部門名稱
        if (maintenance.departmentId && !maintenance.applicantDepartment) {
          const department = departments.find(
            (d) => d.departmentId === maintenance.departmentId
          );
          if (department) {
            maintenance.applicantDepartment = department.name || "";
          }
        }

        // 補充檢驗人員姓名
        if (maintenance.inspectorId && !maintenance.inspectorName) {
          const inspector = users.find(
            (u) => u.userId === maintenance.inspectorId
          );
          if (inspector) {
            maintenance.inspectorName =
              inspector.name || inspector.account || "";
          }
        }
      });
    } catch (error) {}
  };

  // 載入統計資料
  const loadStatistics = async () => {
    try {
      // 獲取所有修繕資料來進行統計（排除已取消的案件）
      const response = await getVendorMaintenances({});

      if (response.success && response.data) {
        // 處理嵌套的 data 結構
        let dataArray: VendorMaintenance[] = [];
        if (
          (response.data as any).data &&
          Array.isArray((response.data as any).data)
        ) {
          dataArray = (response.data as any).data;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        }

        // 過濾掉已取消的案件
        const filteredData = dataArray.filter(
          (item) => item.status !== "已取消" && item.status !== "CANCELLED"
        );

        // 計算統計數據
        const totalCount = filteredData.length;
        const pendingCount = filteredData.filter(
          (item) => item.status === "待審核" || item.status === "PENDING"
        ).length;
        const approvedCount = filteredData.filter(
          (item) => item.status === "已審核" || item.status === "APPROVED"
        ).length;
        const assignedCount = filteredData.filter(
          (item) => item.status === "已指派" || item.status === "ASSIGNED"
        ).length;
        const inProgressCount = filteredData.filter(
          (item) => item.status === "施工中" || item.status === "IN_PROGRESS"
        ).length;
        const completedCount = filteredData.filter(
          (item) => item.status === "已完成" || item.status === "COMPLETED"
        ).length;
        const inspectedCount = filteredData.filter(
          (item) => item.status === "已驗收" || item.status === "INSPECTED"
        ).length;
        const closedCount = filteredData.filter(
          (item) => item.status === "已結案" || item.status === "CLOSED"
        ).length;

        // 計算費用統計
        const totalEstimatedCost = filteredData.reduce(
          (sum, item) => sum + (item.estimatedCost || 0),
          0
        );
        const totalActualCost = filteredData.reduce(
          (sum, item) => sum + (item.actualCost || 0),
          0
        );

        // 計算類型統計
        const typeStats = new Map<
          string,
          { count: number; totalCost: number }
        >();
        filteredData.forEach((item) => {
          const type = item.maintenanceType || "未分類";
          const current = typeStats.get(type) || { count: 0, totalCost: 0 };
          typeStats.set(type, {
            count: current.count + 1,
            totalCost:
              current.totalCost + (item.actualCost || item.estimatedCost || 0),
          });
        });

        const typeStatistics = Array.from(typeStats.entries()).map(
          ([type, stats]) => ({
            type,
            count: stats.count,
            totalCost: stats.totalCost,
          })
        );

        // 計算緊急程度統計
        const urgencyStats = new Map<
          string,
          { count: number; totalDays: number }
        >();
        filteredData.forEach((item) => {
          const level = item.urgencyLevel || "未分類";
          const current = urgencyStats.get(level) || { count: 0, totalDays: 0 };

          // 計算完成天數（如果有實際結束日期）
          let completionDays = 0;
          if (item.actualEndDate && item.actualStartDate) {
            completionDays = Math.ceil(
              (item.actualEndDate - item.actualStartDate) /
                (24 * 60 * 60 * 1000)
            );
          } else if (item.actualEndDate && item.applicationDate) {
            completionDays = Math.ceil(
              (item.actualEndDate - item.applicationDate) /
                (24 * 60 * 60 * 1000)
            );
          }

          urgencyStats.set(level, {
            count: current.count + 1,
            totalDays: current.totalDays + completionDays,
          });
        });

        const urgencyStatistics = Array.from(urgencyStats.entries()).map(
          ([level, stats]) => ({
            level,
            count: stats.count,
            averageCompletionDays:
              stats.count > 0 ? Math.round(stats.totalDays / stats.count) : 0,
          })
        );

        // 計算平均完成天數
        const completedItems = filteredData.filter(
          (item) => item.actualEndDate && item.actualStartDate
        );
        const averageCompletionDays =
          completedItems.length > 0
            ? Math.round(
                completedItems.reduce((sum, item) => {
                  const days = Math.ceil(
                    (item.actualEndDate - item.actualStartDate) /
                      (24 * 60 * 60 * 1000)
                  );
                  return sum + days;
                }, 0) / completedItems.length
              )
            : 0;

        const completeStatistics: MaintenanceStatistics = {
          totalCount,
          pendingCount,
          approvedCount,
          assignedCount,
          inProgressCount,
          completedCount,
          inspectedCount,
          closedCount,
          totalEstimatedCost,
          totalActualCost,
          averageCompletionDays,
          typeStatistics,
          urgencyStatistics,
        };

        setStatistics(completeStatistics);
      } else {
        // 如果API失敗，設置默認統計數據
        console.warn("載入資料失敗:", response.message);
        const defaultStats = {
          totalCount: 0,
          pendingCount: 0,
          approvedCount: 0,
          assignedCount: 0,
          inProgressCount: 0,
          completedCount: 0,
          inspectedCount: 0,
          closedCount: 0,
          totalEstimatedCost: 0,
          totalActualCost: 0,
          averageCompletionDays: 0,
          typeStatistics: [],
          urgencyStatistics: [],
        };
        setStatistics(defaultStats);
      }
    } catch (error) {
      console.error("載入統計數據失敗:", error);
      // 設置默認統計數據
      const defaultStats = {
        totalCount: 0,
        pendingCount: 0,
        approvedCount: 0,
        assignedCount: 0,
        inProgressCount: 0,
        completedCount: 0,
        inspectedCount: 0,
        closedCount: 0,
        totalEstimatedCost: 0,
        totalActualCost: 0,
        averageCompletionDays: 0,
        typeStatistics: [],
        urgencyStatistics: [],
      };
      setStatistics(defaultStats);
    }
  };

  // 載入逾期資料
  const loadOverdueData = async () => {
    try {
      const response = await getOverdueMaintenance();
      if (response.success && response.data) {
        // 處理嵌套的 data 結構
        let dataArray: VendorMaintenance[] = [];
        if (
          (response.data as any).data &&
          Array.isArray((response.data as any).data)
        ) {
          dataArray = (response.data as any).data;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        }
        setOverdueList(dataArray);
      } else {
        setOverdueList([]);
      }
    } catch (error) {
      setOverdueList([]);
    }
  };

  useEffect(() => {
    loadData();
    loadStatistics();
    loadOverdueData();
    loadUsers();
  }, []);

  // 搜尋處理
  const handleSearch = async (values: any) => {
    const query: VendorMaintenanceQuery = {
      ...values,
      startDate: values.dateRange?.[0]?.valueOf(),
      endDate: values.dateRange?.[1]?.valueOf(),
    };
    delete (query as any).dateRange;
    setSearchQuery(query);

    // 立即搜尋
    setLoading(true);
    try {
      const response = await getVendorMaintenances(query);
      if (response.success && response.data) {
        // 處理嵌套的 data 結構
        let dataArray: VendorMaintenance[] = [];
        if (
          (response.data as any).data &&
          Array.isArray((response.data as any).data)
        ) {
          dataArray = (response.data as any).data;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        }

        // 補充顯示名稱
        await enrichMaintenanceData(dataArray);

        // 按緊急程度(高->低)、申請日期排序
        dataArray.sort(sortMaintenanceList);

        setMaintenances(dataArray);
      } else {
        setMaintenances([]);
        message.error(response.message || "搜尋失敗");
      }
    } catch (error) {
      setMaintenances([]);
      message.error("搜尋失敗");
    } finally {
      setLoading(false);
    }
  };

  // 重置搜尋
  const handleResetSearch = async () => {
    searchForm.resetFields();
    setSearchQuery({});

    // 重新載入所有資料
    setLoading(true);
    try {
      const response = await getVendorMaintenances({});
      if (response.success && response.data) {
        // 處理嵌套的 data 結構
        let dataArray: VendorMaintenance[] = [];
        if (
          (response.data as any).data &&
          Array.isArray((response.data as any).data)
        ) {
          dataArray = (response.data as any).data;
        } else if (Array.isArray(response.data)) {
          dataArray = response.data;
        }

        // 補充顯示名稱
        await enrichMaintenanceData(dataArray);

        // 按緊急程度(高->低)、申請日期排序
        dataArray.sort(sortMaintenanceList);

        setMaintenances(dataArray);
      } else {
        setMaintenances([]);
        message.error(response.message || "載入資料失敗");
      }
    } catch (error) {
      setMaintenances([]);
      message.error("載入資料失敗");
    } finally {
      setLoading(false);
    }
  };

  // 新增
  const handleAdd = () => {
    setEditingMaintenance(null);
    setIsViewMode(false);
    setFormModalVisible(true);
  };

  // 查看詳情
  const handleView = async (record: VendorMaintenance) => {
    try {
      const response = await getVendorMaintenanceById(record.maintenanceNumber);

      if (response.success && response.data) {
        // 處理可能的嵌套資料結構
        let detailData: VendorMaintenance;
        if ((response.data as any).data) {
          detailData = (response.data as any).data;
        } else {
          detailData = response.data;
        }

        // 動態補充顯示名稱
        await enrichDetailData(detailData);

        setCurrentMaintenance(detailData);
        detailForm.resetFields();
        setDetailModalVisible(true);
      } else {
        // API 失敗時，直接使用列表中的資料並補充顯示名稱
        const detailData = { ...record };
        await enrichDetailData(detailData);
        setCurrentMaintenance(detailData);
        detailForm.resetFields();
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error("查看詳情錯誤，使用列表資料:", error);
      // 發生錯誤時，也使用列表中的資料並補充顯示名稱
      const detailData = { ...record };
      await enrichDetailData(detailData);
      setCurrentMaintenance(detailData);
      detailForm.resetFields();
      setDetailModalVisible(true);
    }
  };

  // 補充詳情資料的顯示名稱
  const enrichDetailData = async (detailData: VendorMaintenance) => {
    try {
      // 並行獲取所有需要的資料
      const [assetsResponse, usersResponse, departmentsResponse] =
        await Promise.all([getAssets(), getUsers(), getDepartments()]);

      const assets = assetsResponse.success ? assetsResponse.data || [] : [];
      const users = usersResponse.success ? usersResponse.data || [] : [];
      const departments = departmentsResponse.success
        ? departmentsResponse.data || []
        : [];

      // 財產名稱
      if (detailData.assetId && !detailData.assetName) {
        const assetDetail = assets.find(
          (a) => a.asset.assetId === detailData.assetId
        );
        if (assetDetail) {
          detailData.assetName =
            assetDetail.asset.assetName || assetDetail.asset.assetNo || "";
        }
      }

      // 補充申請人姓名
      if (detailData.applicantId && !detailData.applicantName) {
        const applicant = users.find(
          (u) => u.userId === detailData.applicantId
        );
        if (applicant) {
          detailData.applicantName = applicant.name || applicant.account || "";
        }
      }

      // 補充申請部門名稱
      if (detailData.departmentId && !detailData.applicantDepartment) {
        const department = departments.find(
          (d) => d.departmentId === detailData.departmentId
        );
        if (department) {
          detailData.applicantDepartment = department.name || "";
        }
      }

      // 補充檢驗人員姓名
      if (detailData.inspectorId && !detailData.inspectorName) {
        const inspector = users.find(
          (u) => u.userId === detailData.inspectorId
        );
        if (inspector) {
          detailData.inspectorName = inspector.name || inspector.account || "";
        }
      }
    } catch (error) {}
  };

  // 編輯
  const handleEdit = (record: VendorMaintenance) => {
    setEditingMaintenance(record);
    setIsViewMode(false);
    setFormModalVisible(true);
  };

  // 刪除
  const handleDelete = async (maintenanceNumber: string) => {
    try {
      const response = await deleteVendorMaintenance(maintenanceNumber);
      if (response.success) {
        message.success("刪除成功");
        loadData();
      } else {
        message.error(response.message || "刪除失敗");
      }
    } catch (error) {
      message.error("刪除失敗");
    }
  };

  // 審核
  const handleApprove = (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    approvalForm.resetFields();
    setApprovalModalVisible(true);
  };

  // 指派廠商
  const handleAssignVendor = (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    assignForm.resetFields();
    setAssignModalVisible(true);
  };

  // 開始施工
  const handleStartWork = (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    startWorkForm.resetFields();
    startWorkForm.setFieldsValue({
      actualStartDate: dayjs(),
      operatorId: "",
      operatorName: "",
    });
    setStartWorkModalVisible(true);
  };

  // 完成修繕
  const handleComplete = (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    completeForm.resetFields();
    completeForm.setFieldsValue({
      actualEndDate: dayjs(),
      operatorId: "",
      operatorName: "",
    });
    setCompleteModalVisible(true);
  };

  // 驗收
  const handleInspect = async (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    inspectForm.resetFields();

    // 載入用戶資料
    await loadUsers();

    setInspectModalVisible(true);
  };

  // 結案
  const handleClose = async (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    closeForm.resetFields();

    // 載入用戶資料
    await loadUsers();

    setCloseModalVisible(true);
  };

  // 取消
  const handleCancel = async (record: VendorMaintenance) => {
    setCurrentMaintenance(record);
    cancelForm.resetFields();

    // 載入用戶資料
    await loadUsers();

    setCancelModalVisible(true);
  };

  // 重新載入資料
  const handleReload = () => {
    loadData();
    loadStatistics();
    loadOverdueData();
  };

  // 匯出選中項目
  const handleExport = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("請先選擇要匯出的項目");
      return;
    }

    try {
      // 獲取選中的資料
      const selectedData = maintenances.filter((item) =>
        selectedRowKeys.includes(item.maintenanceNumber)
      );

      // 準備匯出的資料
      const exportData = selectedData.map((item: VendorMaintenance) => ({
        修繕單號: item.maintenanceNumber,
        資產編號: item.assetId,
        資產名稱: item.assetName,
        申請人: item.applicantName,
        申請部門: item.applicantDepartment,
        申請日期: DateTimeExtensions.formatDateFromTimestamp(
          item.applicationDate
        ),
        故障描述: item.faultDescription,
        修繕類型: item.maintenanceType,
        緊急程度: item.urgencyLevel,
        預估費用: item.estimatedCost,
        廠商名稱: item.vendorName || "",
        廠商聯絡人: item.vendorContact || "",
        廠商電話: item.vendorPhone || "",
        預計開始日期: DateTimeExtensions.formatDateFromTimestamp(
          item.scheduledStartDate
        ),
        預計結束日期: DateTimeExtensions.formatDateFromTimestamp(
          item.scheduledEndDate
        ),
        實際開始日期: DateTimeExtensions.formatDateFromTimestamp(
          item.actualStartDate
        ),
        實際結束日期: DateTimeExtensions.formatDateFromTimestamp(
          item.actualEndDate
        ),
        實際費用: item.actualCost || 0,
        修繕結果: item.maintenanceResult || "",
        檢驗人員: item.inspectorName || "",
        檢驗日期: DateTimeExtensions.formatDateFromTimestamp(
          item.inspectionDate
        ),
        檢驗結果: item.inspectionResult || "",
        狀態: item.status,
        備註: item.notes || "",
        建立時間: DateTimeExtensions.formatDateFromTimestamp(item.createTime),
      }));

      // 轉換為 CSV 格式，加入公司名稱表頭
      const csvContent = convertToCSV(exportData, true);

      // 下載檔案
      downloadCSV(
        csvContent,
        `廠商修繕單_${DateTimeExtensions.formatDateFromTimestamp(
          DateTimeExtensions.getCurrentTimestamp()
        ).replace(/\//g, "")}.csv`
      );

      message.success(`已匯出 ${selectedData.length} 筆資料`);
    } catch (error) {
      console.error("匯出失敗:", error);
      message.error("匯出失敗");
    }
  };

  // 轉換為 CSV 格式
  const convertToCSV = (data: any[], includeCompanyHeader: boolean = false) => {
    if (data.length === 0) return "";

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(",");

    const csvRows = data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // 處理包含逗號、換行符或雙引號的值
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes("\n") || value.includes('"'))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        })
        .join(",")
    );

    // 如果需要包含公司表頭
    if (includeCompanyHeader) {
      const companyName = siteConfig.copyright; // 從系統設定取得公司名稱
      const companyHeader = `${companyName} - 廠商修繕申請單`;
      const emptyRow = ""; // 空行分隔
      const exportDate = `匯出日期：${DateTimeExtensions.formatDateFromTimestamp(
        DateTimeExtensions.getCurrentTimestamp()
      )}`;

      return [companyHeader, exportDate, emptyRow, csvHeaders, ...csvRows].join(
        "\n"
      );
    }

    return [csvHeaders, ...csvRows].join("\n");
  };

  // 下載 CSV 檔案
  const downloadCSV = (csvContent: string, filename: string) => {
    // 添加 BOM 以支援中文
    const BOM = "\uFEFF";
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });

    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  };

  // 表單提交成功回調
  const handleFormSuccess = () => {
    setFormModalVisible(false);
    setEditingMaintenance(null);
    setIsViewMode(false);
    handleReload();
  };

  // 審核提交
  const handleApprovalSubmit = async (values: ApprovalRequest) => {
    if (!currentMaintenance) return;

    try {
      const response = await approveVendorMaintenance(
        currentMaintenance.maintenanceNumber,
        values
      );
      if (response.success) {
        message.success("審核成功");
        setApprovalModalVisible(false);
        loadData();
        loadStatistics();
      } else {
        message.error(response.message || "審核失敗");
      }
    } catch (error) {
      message.error("審核失敗");
    }
  };

  // 指派廠商提交
  const handleAssignSubmit = async (values: AssignVendorRequest) => {
    if (!currentMaintenance) return;

    try {
      const formData = {
        ...values,
        vendorId: values.vendorId || `VENDOR_${Date.now()}`, // 生成臨時 vendorId
        scheduledStartDate: values.scheduledStartDate
          ? Math.floor(values.scheduledStartDate.valueOf() / 1000)
          : 0,
        scheduledEndDate: values.scheduledEndDate
          ? Math.floor(values.scheduledEndDate.valueOf() / 1000)
          : 0,
      };

      const response = await assignVendor(
        currentMaintenance.maintenanceNumber,
        formData
      );
      if (response.success) {
        message.success("指派成功");
        setAssignModalVisible(false);
        loadData();
        loadStatistics();
      } else {
        message.error(response.message || "指派失敗");
      }
    } catch (error) {
      console.error("指派廠商錯誤:", error);
      message.error("指派失敗");
    }
  };

  // 開始施工提交
  const handleStartWorkSubmit = async (values: StartWorkRequest) => {
    if (!currentMaintenance) {
      console.error("沒有當前修繕記錄");
      message.error("沒有選擇修繕記錄");
      return;
    }

    setStartWorkLoading(true);
    try {
      if (!values.operatorId || values.operatorId === "") {
        console.error("operatorId 為空");
        message.error("請選擇操作人員");
        return;
      }

      if (!values.operatorName || values.operatorName === "") {
        console.error("operatorName 為空");
        message.error("確認人員姓名不能為空");
        return;
      }

      const formData = {
        actualStartDate: values.actualStartDate
          ? Math.floor(values.actualStartDate.valueOf() / 1000)
          : Math.floor(Date.now() / 1000),
        operatorId: values.operatorId.toString(),
        operatorName: values.operatorName.toString(),
      };

      const response = await startWork(
        currentMaintenance.maintenanceNumber,
        formData
      );

      if (response.success) {
        message.success("開始施工成功");
        setStartWorkModalVisible(false);
        startWorkForm.resetFields();
        loadData();
        loadStatistics();
      } else {
        console.error("開始施工失敗 - API 錯誤:", response.message);
        message.error(response.message || "操作失敗");
      }
    } catch (error) {
      console.error("開始施工失敗 - 例外錯誤:", error);
      message.error("操作失敗");
    } finally {
      setStartWorkLoading(false);
    }
  };

  // 完成修繕提交
  const handleCompleteSubmit = async (values: CompleteMaintenanceRequest) => {
    if (!currentMaintenance) {
      console.error("沒有當前修繕記錄");
      message.error("沒有選擇修繕記錄");
      return;
    }

    try {
      if (!values.operatorId || values.operatorId === "") {
        console.error("operatorId 為空");
        message.error("請選擇操作人員");
        return;
      }

      if (!values.operatorName || values.operatorName === "") {
        console.error("operatorName 為空");
        message.error("確認人員姓名不能為空");
        return;
      }

      const formData = {
        actualEndDate: values.actualEndDate
          ? Math.floor(values.actualEndDate.valueOf() / 1000)
          : Math.floor(Date.now() / 1000),
        actualCost: Number(values.actualCost) || 0,
        maintenanceResult: values.maintenanceResult?.toString() || "",
        operatorId: values.operatorId.toString(),
        operatorName: values.operatorName.toString(),
      };

      const response = await completeMaintenance(
        currentMaintenance.maintenanceNumber,
        formData
      );

      if (response.success) {
        message.success("完成修繕成功");
        setCompleteModalVisible(false);
        completeForm.resetFields();
        loadData();
        loadStatistics();
      } else {
        console.error("完成修繕失敗 - API 錯誤:", response.message);
        message.error(response.message || "操作失敗");
      }
    } catch (error) {
      console.error("完成修繕失敗 - 例外錯誤:", error);
      message.error("操作失敗");
    }
  };

  // 驗收提交
  const handleInspectSubmit = async (values: InspectionRequest) => {
    if (!currentMaintenance) return;

    // 驗證檢驗人員姓名
    if (!values.inspectorName || values.inspectorName === "") {
      console.error("inspectorName 為空");
      message.error("檢驗人員姓名不能為空");
      return;
    }

    // 驗證檢驗人員ID (從隱藏欄位取得)
    const inspectorId = inspectForm.getFieldValue("inspectorId");
    if (!inspectorId || inspectorId === "") {
      console.error("inspectorId 為空");
      message.error("請選擇檢驗人員");
      return;
    }

    try {
      const formData = {
        ...values,
        inspectorId,
        inspectionDate: DateTimeExtensions.getCurrentTimestamp(),
      };

      const response = await inspectMaintenance(
        currentMaintenance.maintenanceNumber,
        formData
      );

      if (response.success) {
        message.success("驗收完成");
        setInspectModalVisible(false);
        inspectForm.resetFields();
        loadData();
        loadStatistics();
      } else {
        console.error("驗收修繕 - API 錯誤:", response.message);
        message.error(response.message || "操作失敗");
      }
    } catch (error) {
      console.error("驗收修繕 - 請求失敗:", error);
      message.error("操作失敗");
    }
  };

  // 結案提交
  const handleCloseSubmit = async (values: CloseRequest) => {
    if (!currentMaintenance) return;

    // 驗證確認人員姓名
    if (!values.operatorName || values.operatorName === "") {
      console.error("operatorName 為空");
      message.error("確認人員姓名不能為空");
      return;
    }

    // 驗證確認人員ID (從隱藏欄位取得)
    const operatorId = closeForm.getFieldValue("operatorId");
    if (!operatorId || operatorId === "") {
      console.error("operatorId 為空");
      message.error("請選擇確認人員");
      return;
    }

    try {
      const formData = {
        ...values,
        operatorId,
        closeDate: DateTimeExtensions.getCurrentTimestamp(),
      };

      const response = await closeMaintenance(
        currentMaintenance.maintenanceNumber,
        formData
      );

      if (response.success) {
        message.success("結案成功");
        setCloseModalVisible(false);
        closeForm.resetFields();
        loadData();
        loadStatistics();
      } else {
        console.error("結案確認 - API 錯誤:", response.message);
        message.error(response.message || "操作失敗");
      }
    } catch (error) {
      console.error("結案確認 - 請求失敗:", error);
      message.error("操作失敗");
    }
  };

  // 取消提交
  const handleCancelSubmit = async (values: CancelRequest) => {
    if (!currentMaintenance) return;

    // 驗證確認人員姓名
    if (!values.operatorName || values.operatorName === "") {
      console.error("operatorName 為空");
      message.error("確認人員姓名不能為空");
      return;
    }

    // 驗證確認人員ID (從隱藏欄位取得)
    const operatorId = cancelForm.getFieldValue("operatorId");
    if (!operatorId || operatorId === "") {
      console.error("operatorId 為空");
      message.error("請選擇確認人員");
      return;
    }

    try {
      const formData = {
        ...values,
        operatorId,
        cancelDate: DateTimeExtensions.getCurrentTimestamp(),
      };

      const response = await cancelMaintenance(
        currentMaintenance.maintenanceNumber,
        formData
      );

      if (response.success) {
        message.success("取消成功");
        setCancelModalVisible(false);
        cancelForm.resetFields();
        loadData();
        loadStatistics();
      } else {
        console.error("取消修繕 - API 錯誤:", response.message);
        message.error(response.message || "取消失敗");
      }
    } catch (error) {
      console.error("取消修繕 - 請求失敗:", error);
      message.error("取消失敗");
    }
  };

  // 表格列配置
  const columns = useMemo(
    () =>
      isMobile
        ? getMobileColumns(handleView, handleEdit)
        : getColumns(
            handleView,
            handleEdit,
            handleApprove,
            handleAssignVendor,
            handleStartWork,
            handleComplete,
            handleInspect,
            handleClose,
            handleCancel
          ),
    [isMobile]
  );

  // 選擇行配置
  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onChange: setSelectedRowKeys,
    }),
    [selectedRowKeys]
  );

  return (
    <div style={{ padding: "20px" }}>
      <Card title="廠商修繕單管理">
        {/* 統計卡片區域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="總申請數"
                value={statistics?.totalCount || 0}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="總預估費用"
                value={statistics?.totalEstimatedCost || 0}
                valueStyle={{ color: "#1890ff" }}
                formatter={(value) => `$${Number(value).toLocaleString()}`}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="總實際費用"
                value={statistics?.totalActualCost || 0}
                valueStyle={{ color: "#1890ff" }}
                formatter={(value) => `$${Number(value).toLocaleString()}`}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="平均完成天數"
                value={statistics?.averageCompletionDays || 0}
                valueStyle={{ color: "#1890ff" }}
                suffix="天"
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="待審核"
                value={statistics?.pendingCount || 0}
                valueStyle={{ color: "#faad14" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已審核"
                value={statistics?.approvedCount || 0}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已指派"
                value={statistics?.assignedCount || 0}
                valueStyle={{ color: "#13c2c2" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="施工中"
                value={statistics?.inProgressCount || 0}
                valueStyle={{ color: "#722ed1" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已完成"
                value={statistics?.completedCount || 0}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已驗收"
                value={statistics?.inspectedCount || 0}
                valueStyle={{ color: "#a0d911" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="已結案"
                value={statistics?.closedCount || 0}
                valueStyle={{ color: "#8c8c8c" }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <Card>
              <Statistic
                title="逾期案件"
                value={overdueList?.length || 0}
                valueStyle={{ color: "#ff4d4f" }}
              />
            </Card>
          </Col>
        </Row>

        {/* 進度步驟展示 */}
        <Card
          title={
            <div className={styles.processHeader}>
              <span className={styles.processTitle}>
                案件處理流程 - 總計{" "}
                <span
                  style={{
                    fontWeight: "bold",
                    textDecoration: "underline",
                    color: "#1890ff",
                  }}
                >
                  {statistics?.totalCount || 0}
                </span>{" "}
                件案件
              </span>
            </div>
          }
          className={styles.processSection}
          size="small"
          style={{ marginBottom: "24px" }}
        >
          <Steps
            current={calculateOverallProgress(statistics)}
            status={getOverallProgressStatus(statistics)}
            items={createStepItemsWithCount(statistics).map((item) => ({
              ...item,
              description: (
                <span>
                  {typeof item.description === "string" ? (
                    item.description.split("共 ").length > 1 ? (
                      <>
                        {item.description.split("共 ")[0]}共{" "}
                        <span
                          style={{
                            fontWeight: "bold",
                            textDecoration: "underline",
                          }}
                        >
                          {item.description.split("共 ")[1].split(" 件")[0]}
                        </span>{" "}
                        件
                      </>
                    ) : (
                      item.description
                    )
                  ) : (
                    item.description
                  )}
                </span>
              ),
            }))}
            direction={isMobile ? "vertical" : "horizontal"}
            size="small"
            style={{ marginBottom: "16px" }}
          />
          <div
            style={{ textAlign: "center", color: "#8c8c8c", fontSize: "14px" }}
          >
            上方顯示標準處理流程及各階段當前件數，實際案件進度請查看列表中的詳細狀態
          </div>
        </Card>

        {/* 搜尋區域 */}
        <Card
          title={
            <div style={{ display: "flex", alignItems: "center" }}>
              <SearchOutlined
                style={{ marginRight: "8px", color: "#1890ff" }}
              />
              <span>搜尋條件</span>
            </div>
          }
          style={{ marginBottom: "24px" }}
        >
          <Form form={searchForm} layout="vertical" onFinish={handleSearch}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  name="status"
                  label="狀態"
                  style={{ marginBottom: "16px" }}
                >
                  <Select
                    placeholder="請選擇狀態"
                    allowClear
                    style={{ width: "100%" }}
                    optionRender={(option) => {
                      const status = STATUS_OPTIONS.find(
                        (s) => s.value === option.value
                      );
                      return status ? (
                        <Tag color={status.color} style={{ margin: 0 }}>
                          {status.label}
                        </Tag>
                      ) : (
                        option.label
                      );
                    }}
                    labelRender={(props) => {
                      const status = STATUS_OPTIONS.find(
                        (s) => s.value === props.value
                      );
                      return status ? (
                        <Tag
                          color={status.color}
                          style={{ margin: 0, fontSize: "14px" }}
                        >
                          {status.label}
                        </Tag>
                      ) : (
                        props.label
                      );
                    }}
                  >
                    {STATUS_OPTIONS.map((status) => (
                      <Option
                        key={status.value}
                        value={status.value}
                        label={status.label}
                      >
                        <Tag color={status.color} style={{ margin: 0 }}>
                          {status.label}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  name="maintenanceType"
                  label="修繕類型"
                  style={{ marginBottom: "16px" }}
                >
                  <Select
                    placeholder="請選擇修繕類型"
                    allowClear
                    style={{ width: "100%" }}
                  >
                    {MAINTENANCE_TYPES.map((type) => (
                      <Option key={type.value} value={type.value}>
                        {type.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  name="urgencyLevel"
                  label="緊急程度"
                  style={{ marginBottom: "16px" }}
                >
                  <Select
                    placeholder="請選擇緊急程度"
                    allowClear
                    style={{ width: "100%" }}
                    optionRender={(option) => {
                      const level = URGENCY_LEVELS.find(
                        (l) => l.value === option.value
                      );
                      return level ? (
                        <Tag color={level.color} style={{ margin: 0 }}>
                          {level.label}
                        </Tag>
                      ) : (
                        option.label
                      );
                    }}
                    labelRender={(props) => {
                      const level = URGENCY_LEVELS.find(
                        (l) => l.value === props.value
                      );
                      return level ? (
                        <Tag
                          color={level.color}
                          style={{ margin: 0, fontSize: "14px" }}
                        >
                          {level.label}
                        </Tag>
                      ) : (
                        props.label
                      );
                    }}
                  >
                    {URGENCY_LEVELS.map((level) => (
                      <Option
                        key={level.value}
                        value={level.value}
                        label={level.label}
                      >
                        <Tag color={level.color} style={{ margin: 0 }}>
                          {level.label}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item
                  name="dateRange"
                  label="申請日期"
                  style={{ marginBottom: "16px" }}
                >
                  <RangePicker
                    style={{ width: "100%" }}
                    placeholder={["開始日期", "結束日期"]}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ marginTop: "16px" }}>
              <Col span={24}>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "12px" }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                  >
                    搜尋
                  </Button>
                  <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                  <Button
                    onClick={() => setStatisticsModalVisible(true)}
                    icon={<BarChartOutlined />}
                  >
                    統計分析
                  </Button>
                  {(overdueList?.length || 0) > 0 && (
                    <Tooltip
                      title={`有 ${overdueList?.length || 0} 筆逾期案件`}
                    >
                      <Button danger icon={<WarningOutlined />}>
                        逾期提醒 ({overdueList?.length || 0})
                      </Button>
                    </Tooltip>
                  )}
                </div>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 操作按鈕區域 */}
        <div style={{ marginBottom: "24px" }}>
          <div
            style={{
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              alignItems: isMobile ? "stretch" : "center",
              justifyContent: "space-between",
              gap: "16px",
            }}
          >
            <div style={{ display: "flex", flexWrap: "wrap", gap: "12px" }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新增修繕申請
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReload}>
                重新載入
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
                disabled={selectedRowKeys.length === 0}
              >
                匯出選中項目
              </Button>
            </div>
            {selectedRowKeys.length > 0 && (
              <div
                style={{
                  color: "#1890ff",
                  fontSize: "14px",
                }}
              >
                已選擇{" "}
                <span style={{ fontWeight: "600" }}>
                  {selectedRowKeys.length}
                </span>{" "}
                筆資料
              </div>
            )}
          </div>
        </div>

        {/* 表格區域 */}
        <Card
          title={
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                <span style={{ fontWeight: "600", fontSize: "16px" }}>
                  修繕申請列表
                </span>
                <span
                  style={{
                    marginLeft: "12px",
                    fontSize: "14px",
                    color: "#8c8c8c",
                  }}
                >
                  共 {Array.isArray(maintenances) ? maintenances.length : 0}{" "}
                  筆資料
                </span>
              </div>
            </div>
          }
          styles={{ body: { padding: "0" } }}
        >
          <Table
            columns={columns}
            dataSource={Array.isArray(maintenances) ? maintenances : []}
            rowKey="maintenanceNumber"
            loading={loading}
            rowSelection={rowSelection}
            scroll={{ x: 1500 }}
            pagination={{
              total: Array.isArray(maintenances) ? maintenances.length : 0,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `顯示第 ${range[0]}-${range[1]} 筆，共 ${total} 筆資料`,
              pageSizeOptions: ["10", "20", "50", "100"],
            }}
            size="small"
          />
        </Card>

        {/* 新增/編輯 Modal */}
        <Modal
          title={editingMaintenance ? "編輯修繕申請" : "新增修繕申請"}
          open={formModalVisible}
          onCancel={() => {
            setFormModalVisible(false);
            setEditingMaintenance(null);
            setIsViewMode(false);
          }}
          afterOpenChange={(open) => {
            if (open && !editingMaintenance) {
              // 新增模式時重置表單
              setTimeout(() => {
                form.resetFields();
              }, 0);
            }
          }}
          footer={null}
          width={isMobile ? "100%" : 1200}
          styles={{ body: { maxHeight: "70vh", overflow: "auto" } }}
          destroyOnClose={true}
        >
          <div>
            {/* 編輯模式下顯示進度 */}
            {editingMaintenance && (
              <Card
                title="案件處理進度"
                style={{ marginBottom: "16px" }}
                size="small"
              >
                <Steps
                  current={
                    isStatusCancelled(editingMaintenance.status || "")
                      ? 0
                      : getStepByStatus(editingMaintenance.status || "")
                  }
                  status={getStepStatus(editingMaintenance.status || "")}
                  items={STEP_ITEMS.map((item: StepItem, index: number) => ({
                    ...item,
                    description: isStatusCancelled(
                      editingMaintenance.status || ""
                    )
                      ? index === 0
                        ? "案件已取消"
                        : item.description
                      : item.description,
                  }))}
                  direction={isMobile ? "vertical" : "horizontal"}
                  size="small"
                />
                <div
                  style={{
                    textAlign: "center",
                    color: "#8c8c8c",
                    fontSize: "12px",
                    marginTop: "8px",
                  }}
                >
                  當前狀態：
                  <Tag
                    color={
                      isStatusCancelled(editingMaintenance.status || "")
                        ? "red"
                        : "blue"
                    }
                  >
                    {editingMaintenance.status}
                  </Tag>
                </div>
              </Card>
            )}

            {/* 表單內容 */}
            <VendorMaintenanceForm
              editingMaintenance={editingMaintenance}
              isViewMode={isViewMode}
              onCancel={() => {
                setFormModalVisible(false);
                setEditingMaintenance(null);
                setIsViewMode(false);
              }}
              onSuccess={handleFormSuccess}
              isMobile={isMobile}
              form={form}
            />
          </div>
        </Modal>

        {/* 詳情查看 Modal */}
        <Modal
          title="修繕申請詳情"
          open={detailModalVisible}
          onCancel={() => {
            setDetailModalVisible(false);
            setCurrentMaintenance(null);
            detailForm.resetFields();
          }}
          footer={[
            <Button
              key="close"
              onClick={() => {
                setDetailModalVisible(false);
                setCurrentMaintenance(null);
                detailForm.resetFields();
              }}
            >
              關閉
            </Button>,
          ]}
          width={isMobile ? "100%" : 1200}
          destroyOnClose={true}
        >
          {(() => {
            return currentMaintenance ? (
              <div>
                {/* 案件進度顯示 */}
                <Card
                  title="案件處理進度"
                  style={{ marginBottom: "16px" }}
                  size="small"
                >
                  <Steps
                    current={
                      isStatusCancelled(currentMaintenance.status || "")
                        ? 0
                        : getStepByStatus(currentMaintenance.status || "")
                    }
                    status={getStepStatus(currentMaintenance.status || "")}
                    items={STEP_ITEMS.map((item: StepItem, index: number) => ({
                      ...item,
                      description: isStatusCancelled(
                        currentMaintenance.status || ""
                      )
                        ? index === 0
                          ? "案件已取消"
                          : item.description
                        : item.description,
                    }))}
                    direction={isMobile ? "vertical" : "horizontal"}
                    size="small"
                  />
                  <div
                    style={{
                      textAlign: "center",
                      color: "#8c8c8c",
                      fontSize: "12px",
                      marginTop: "8px",
                    }}
                  >
                    當前狀態：
                    <Tag
                      color={
                        isStatusCancelled(currentMaintenance.status || "")
                          ? "red"
                          : "blue"
                      }
                    >
                      {currentMaintenance.status}
                    </Tag>
                  </div>
                </Card>

                {/* 表單詳情 */}
                <VendorMaintenanceForm
                  editingMaintenance={currentMaintenance}
                  isViewMode={true}
                  onCancel={() => {
                    setDetailModalVisible(false);
                    setCurrentMaintenance(null);
                    detailForm.resetFields();
                  }}
                  onSuccess={() => {}}
                  isMobile={isMobile}
                  form={detailForm}
                />
              </div>
            ) : (
              <div>載入中...</div>
            );
          })()}
        </Modal>

        {/* 審核 Modal */}
        <Modal
          title="審核修繕申請"
          open={approvalModalVisible}
          onCancel={() => setApprovalModalVisible(false)}
          onOk={() => approvalForm.submit()}
          okText="確認審核"
          cancelText="取消"
        >
          <Form
            form={approvalForm}
            layout="vertical"
            onFinish={handleApprovalSubmit}
          >
            <Form.Item
              name="isApproved"
              label="審核結果"
              rules={[{ required: true, message: "請選擇審核結果" }]}
            >
              <Select placeholder="請選擇審核結果">
                <Option value={true}>
                  <Space>
                    <CheckCircleOutlined style={{ color: "#52c41a" }} />
                    通過
                  </Space>
                </Option>
                <Option value={false}>
                  <Space>
                    <CloseCircleOutlined style={{ color: "#ff4d4f" }} />
                    不通過
                  </Space>
                </Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="reason"
              label="審核意見"
              rules={[{ required: true, message: "請輸入審核意見" }]}
            >
              <TextArea rows={4} placeholder="請輸入審核意見" />
            </Form.Item>
            <Form.Item
              name="approverName"
              label="審核人員姓名"
              rules={[{ required: true, message: "請選擇審核人員" }]}
            >
              <Select
                placeholder="請選擇審核人員"
                showSearch
                filterOption={(input, option) => {
                  const label = String(option?.label || option?.value || "");
                  return label.toLowerCase().includes(input.toLowerCase());
                }}
                onChange={(value, option) => {
                  // 當選擇用戶時，自動設置 approverId
                  const selectedUser = users.find(
                    (u) => u.name === value || u.account === value
                  );
                  if (selectedUser) {
                    approvalForm.setFieldsValue({
                      approverId: selectedUser.userId,
                    });
                  }
                }}
              >
                {users.map((user) => (
                  <Option key={user.userId} value={user.name || user.account}>
                    {user.name || user.account}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="approverId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
          </Form>
        </Modal>

        {/* 指派廠商 Modal */}
        <Modal
          title="指派廠商"
          open={assignModalVisible}
          onCancel={() => setAssignModalVisible(false)}
          onOk={() => assignForm.submit()}
          okText="確認指派"
          cancelText="取消"
        >
          <Form
            form={assignForm}
            layout="vertical"
            onFinish={handleAssignSubmit}
          >
            <Form.Item
              name="vendorName"
              label="廠商名稱"
              rules={[{ required: true, message: "請輸入廠商名稱" }]}
            >
              <Input placeholder="請輸入廠商名稱" />
            </Form.Item>
            <Form.Item name="vendorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
            <Form.Item
              name="vendorContact"
              label="廠商聯絡人"
              rules={[{ required: true, message: "請輸入廠商聯絡人" }]}
            >
              <Input placeholder="請輸入廠商聯絡人" />
            </Form.Item>
            <Form.Item
              name="vendorPhone"
              label="廠商電話"
              rules={[{ required: true, message: "請輸入廠商電話" }]}
            >
              <Input placeholder="請輸入廠商電話" />
            </Form.Item>
            <Form.Item
              name="scheduledStartDate"
              label="預計開始日期"
              rules={[{ required: true, message: "請選擇預計開始日期" }]}
            >
              <DatePicker style={{ width: "100%" }} showTime />
            </Form.Item>
            <Form.Item
              name="scheduledEndDate"
              label="預計結束日期"
              rules={[{ required: true, message: "請選擇預計結束日期" }]}
            >
              <DatePicker style={{ width: "100%" }} showTime />
            </Form.Item>
            <Form.Item
              name="operatorName"
              label="確認人員姓名"
              rules={[{ required: true, message: "請選擇操作人員" }]}
            >
              <Select
                placeholder="請選擇操作人員"
                showSearch
                filterOption={(input, option) => {
                  const label = String(option?.label || option?.value || "");
                  return label.toLowerCase().includes(input.toLowerCase());
                }}
                onChange={(value, option) => {
                  // 當選擇用戶時，自動設置 operatorId
                  const selectedUser = users.find(
                    (u) => u.name === value || u.account === value
                  );
                  if (selectedUser) {
                    assignForm.setFieldsValue({
                      operatorId: selectedUser.userId,
                    });
                  }
                }}
              >
                {users.map((user) => (
                  <Option key={user.userId} value={user.name || user.account}>
                    {user.name || user.account}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="operatorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
          </Form>
        </Modal>

        {/* 開始施工 Modal */}
        <Modal
          title="開始施工"
          open={startWorkModalVisible}
          onCancel={() => {
            if (!startWorkLoading) {
              setStartWorkModalVisible(false);
            }
          }}
          onOk={() => {
            startWorkForm.submit();
          }}
          okText="確認開始"
          cancelText="取消"
          confirmLoading={startWorkLoading}
          afterOpenChange={(open) => {
            if (open) {
              loadUsers();
            }
          }}
        >
          <Form
            form={startWorkForm}
            layout="vertical"
            onFinish={handleStartWorkSubmit}
            onFinishFailed={(errorInfo) => {
              console.error("表單驗證失敗:", errorInfo);
              message.error("請檢查表單填寫是否完整");
            }}
          >
            <Form.Item
              name="actualStartDate"
              label="實際開始日期"
              rules={[{ required: true, message: "請選擇實際開始日期" }]}
            >
              <DatePicker style={{ width: "100%" }} showTime />
            </Form.Item>
            <Form.Item name="operatorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
            <Form.Item
              name="operatorName"
              label="確認人員姓名"
              rules={[{ required: true, message: "請選擇確認人員姓名" }]}
            >
              <Select
                placeholder="請選擇確認人員姓名"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={users.map((user) => ({
                  value: user.name,
                  label: user.name,
                  userId: user.userId, // 使用正確的 userId 欄位
                }))}
                onChange={(value, option) => {
                  if (value && option) {
                    // 直接從用戶列表中找到對應的用戶 ID
                    const selectedUser = users.find(
                      (user) => user.name === value
                    );
                    if (selectedUser) {
                      startWorkForm.setFieldValue(
                        "operatorId",
                        selectedUser.userId
                      );
                    } else {
                      console.error("找不到對應的用戶:", value);
                      startWorkForm.setFieldValue("operatorId", "");
                    }
                  } else {
                    startWorkForm.setFieldValue("operatorId", "");
                  }
                }}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 完成修繕 Modal */}
        <Modal
          title="完成修繕"
          open={completeModalVisible}
          onCancel={() => setCompleteModalVisible(false)}
          onOk={() => {
            completeForm.submit();
          }}
          okText="確認完成"
          cancelText="取消"
          afterOpenChange={(open) => {
            if (open) {
              loadUsers();
            }
          }}
        >
          <Form
            form={completeForm}
            layout="vertical"
            onFinish={handleCompleteSubmit}
            onFinishFailed={(errorInfo) => {
              console.error("完成修繕表單驗證失敗:", errorInfo);
              message.error("請檢查表單填寫是否完整");
            }}
          >
            <Form.Item
              name="actualEndDate"
              label="實際結束日期"
              rules={[{ required: true, message: "請選擇實際結束日期" }]}
            >
              <DatePicker style={{ width: "100%" }} showTime />
            </Form.Item>
            <Form.Item
              name="actualCost"
              label="實際費用"
              rules={[{ required: true, message: "請輸入實際費用" }]}
            >
              <InputNumber
                style={{ width: "100%" }}
                placeholder="請輸入實際費用"
                min={0}
                formatter={(value) =>
                  `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                }
                parser={(value) => value?.replace(/\$\s?|(,*)/g, "") as any}
              />
            </Form.Item>
            <Form.Item
              name="maintenanceResult"
              label="修繕結果"
              rules={[{ required: true, message: "請輸入修繕結果" }]}
            >
              <TextArea rows={4} placeholder="請描述修繕結果" />
            </Form.Item>
            <Form.Item name="operatorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
            <Form.Item
              name="operatorName"
              label="確認人員姓名"
              rules={[{ required: true, message: "請選擇確認人員姓名" }]}
            >
              <Select
                placeholder="請選擇確認人員姓名"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={users.map((user) => ({
                  value: user.name,
                  label: user.name,
                  userId: user.userId,
                }))}
                onChange={(value, option) => {
                  if (value && option) {
                    const selectedUser = users.find(
                      (user) => user.name === value
                    );
                    if (selectedUser) {
                      completeForm.setFieldValue(
                        "operatorId",
                        selectedUser.userId
                      );
                    } else {
                      console.error("完成修繕 - 找不到對應的用戶:", value);
                      completeForm.setFieldValue("operatorId", "");
                    }
                  } else {
                    completeForm.setFieldValue("operatorId", "");
                  }
                }}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 驗收 Modal */}
        <Modal
          title="驗收修繕"
          open={inspectModalVisible}
          onCancel={() => setInspectModalVisible(false)}
          onOk={() => inspectForm.submit()}
          okText="確認驗收"
          cancelText="取消"
        >
          <Form
            form={inspectForm}
            layout="vertical"
            onFinish={handleInspectSubmit}
          >
            <Form.Item
              name="inspectionResult"
              label="驗收結果"
              rules={[{ required: true, message: "請輸入驗收結果" }]}
            >
              <TextArea rows={4} placeholder="請描述驗收結果" />
            </Form.Item>
            <Form.Item name="inspectionNotes" label="驗收備註">
              <TextArea rows={3} placeholder="請輸入驗收備註" />
            </Form.Item>
            <Form.Item name="inspectorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
            <Form.Item
              name="inspectorName"
              label="檢驗人員姓名"
              rules={[{ required: true, message: "請選擇檢驗人員姓名" }]}
            >
              <Select
                placeholder="請選擇檢驗人員姓名"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={users.map((user) => ({
                  value: user.name,
                  label: user.name,
                  userId: user.userId,
                }))}
                onChange={(value, option) => {
                  if (value && option) {
                    const selectedUser = users.find(
                      (user) => user.name === value
                    );
                    if (selectedUser) {
                      inspectForm.setFieldValue(
                        "inspectorId",
                        selectedUser.userId
                      );
                    } else {
                      console.error("驗收修繕 - 找不到對應的用戶:", value);
                      inspectForm.setFieldValue("inspectorId", "");
                    }
                  } else {
                    inspectForm.setFieldValue("inspectorId", "");
                  }
                }}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 結案 Modal */}
        <Modal
          title="結案確認"
          open={closeModalVisible}
          onCancel={() => setCloseModalVisible(false)}
          onOk={() => closeForm.submit()}
          okText="確認結案"
          cancelText="取消"
        >
          <Form form={closeForm} layout="vertical" onFinish={handleCloseSubmit}>
            <Form.Item name="operatorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
            <Form.Item
              name="operatorName"
              label="確認人員姓名"
              rules={[{ required: true, message: "請選擇確認人員姓名" }]}
            >
              <Select
                placeholder="請選擇確認人員姓名"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={users.map((user) => ({
                  value: user.name,
                  label: user.name,
                  userId: user.userId,
                }))}
                onChange={(value, option) => {
                  if (value && option) {
                    const selectedUser = users.find(
                      (user) => user.name === value
                    );
                    if (selectedUser) {
                      closeForm.setFieldValue(
                        "operatorId",
                        selectedUser.userId
                      );
                    } else {
                      console.error("結案確認 - 找不到對應的用戶:", value);
                      closeForm.setFieldValue("operatorId", "");
                    }
                  } else {
                    closeForm.setFieldValue("operatorId", "");
                  }
                }}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 取消修繕 Modal */}
        <Modal
          title="取消修繕"
          open={cancelModalVisible}
          onCancel={() => setCancelModalVisible(false)}
          onOk={() => cancelForm.submit()}
          okText="確認取消"
          cancelText="關閉"
        >
          <Form
            form={cancelForm}
            layout="vertical"
            onFinish={handleCancelSubmit}
          >
            <Form.Item
              name="reason"
              label="取消原因"
              rules={[{ required: true, message: "請輸入取消原因" }]}
            >
              <TextArea rows={4} placeholder="請詳細說明取消原因" />
            </Form.Item>
            <Form.Item name="operatorId" style={{ display: "none" }}>
              <Input />
            </Form.Item>
            <Form.Item
              name="operatorName"
              label="確認人員姓名"
              rules={[{ required: true, message: "請選擇確認人員姓名" }]}
            >
              <Select
                placeholder="請選擇確認人員姓名"
                showSearch
                allowClear
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={users.map((user) => ({
                  value: user.name,
                  label: user.name,
                  userId: user.userId,
                }))}
                onChange={(value, option) => {
                  if (value && option) {
                    const selectedUser = users.find(
                      (user) => user.name === value
                    );
                    if (selectedUser) {
                      cancelForm.setFieldValue(
                        "operatorId",
                        selectedUser.userId
                      );
                    } else {
                      console.error("取消修繕 - 找不到對應的用戶:", value);
                      cancelForm.setFieldValue("operatorId", "");
                    }
                  } else {
                    cancelForm.setFieldValue("operatorId", "");
                  }
                }}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 統計分析 Modal */}
        <Modal
          title="統計分析"
          open={statisticsModalVisible}
          onCancel={() => setStatisticsModalVisible(false)}
          footer={[
            <Button
              key="close"
              onClick={() => setStatisticsModalVisible(false)}
            >
              關閉
            </Button>,
          ]}
          width={800}
        >
          {statistics && (
            <div>
              <Row gutter={16} style={{ marginBottom: "16px" }}>
                <Col span={8}>
                  <Statistic title="總申請數" value={statistics.totalCount} />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="總預估費用"
                    value={statistics.totalEstimatedCost}
                    formatter={(value) => `$${Number(value).toLocaleString()}`}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="總實際費用"
                    value={statistics.totalActualCost}
                    formatter={(value) => `$${Number(value).toLocaleString()}`}
                  />
                </Col>
              </Row>

              <Card title="類型統計" style={{ marginBottom: "16px" }}>
                {(statistics.typeStatistics || []).map((item, index) => (
                  <div key={index} style={{ marginBottom: "8px" }}>
                    <Space>
                      <Tag color="blue">{item.type}</Tag>
                      <span>數量: {item.count}</span>
                      <span>費用: ${item.totalCost.toLocaleString()}</span>
                    </Space>
                  </div>
                ))}
              </Card>

              <Card title="緊急程度統計">
                {(statistics.urgencyStatistics || []).map((item, index) => (
                  <div key={index} style={{ marginBottom: "8px" }}>
                    <Space>
                      <Tag
                        color={
                          URGENCY_LEVELS.find((u) => u.value === item.level)
                            ?.color
                        }
                      >
                        {item.level}
                      </Tag>
                      <span>數量: {item.count}</span>
                      <span>平均完成天數: {item.averageCompletionDays}</span>
                    </Space>
                  </div>
                ))}
              </Card>
            </div>
          )}
        </Modal>
      </Card>
    </div>
  );
};

export default VendorMaintenancePage;
