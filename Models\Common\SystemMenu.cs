﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    public class SystemMenuDTO
    {
        public string SystemMenuId { get; set; } //選單編號
        public string SystemGroupId { get; set; } //系統群組編號
        public string Label { get; set; } //選單標籤
        public string Key { get; set; } //選單鍵
        public string Icon { get; set; } //選單圖示
        public string? ParentId { get; set; } //父層選單編號
        public bool IsMenu { get; set; } //為選單

        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態

        public SystemMenuDTO()
        {
            SystemMenuId = "";
            SystemGroupId = "";
            Label = "";
            Key = "";
            Icon = null;
            ParentId = null;
            IsMenu = false;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class SystemMenu : ModelBaseEntity
    {
        [Key]
        [Comment("選單編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemMenuId { get; set; } //選單編號

        [Comment("系統群組編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemGroupId { get; set; } //系統群組編號

        [Comment("選單標籤")]
        [Column(TypeName = "nvarchar(50)")]
        public string Label { get; set; } //選單標籤

        [Comment("選單鍵")]
        [Column(TypeName = "nvarchar(50)")]
        public string Key { get; set; } //選單鍵

        [Comment("選單圖示")]
        [Column(TypeName = "nvarchar(50)")]
        public string? Icon { get; set; } //選單圖示

        [Comment("父層選單編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? ParentId { get; set; } //父層選單編號

        [Comment("為選單")]
        [Column(TypeName = "bit")]
        public bool IsMenu { get; set; } //為選單

        public ICollection<RolesPermissions> RolesPermissions { get; set; } // 角色權限關聯

        public SystemMenu()
        {
            SystemMenuId = "";
            SystemGroupId = "";
            Label = "";
            Key = "";
            Icon = null;
            ParentId = null;
            IsMenu = false;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;

            // 原有初始化
            RolesPermissions = new List<RolesPermissions>();
        }
    }

    public class SystemMenuNode
    {
        public string SystemMenuId { get; set; } //選單編號
        public string SystemGroupId { get; set; } //系統群組編號
        public string Label { get; set; } //選單標籤
        public string Key { get; set; } //選單鍵
        public string Icon { get; set; } //選單圖示
        public string? ParentId { get; set; } //父層選單編號
        public bool IsMenu { get; set; } //為選單
        public List<SystemMenuNode> Children { get; set; } = new List<SystemMenuNode>();

        public SystemMenuNode()
        {
            SystemMenuId = "";
            SystemGroupId = "";
            Label = "";
            Key = "";
            Icon = null;
            ParentId = null;
            IsMenu = false;
            Children = new List<SystemMenuNode>();
        }
    }
}