"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Space,
  Card,
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  Unit,
  getUnits,
  createUnit,
  updateUnit,
  deleteUnit,
} from "@/services/common/unitService";

const UnitManagement: React.FC = () => {
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUnit, setEditingUnit] = useState<Unit | null>(null);
  const [form] = Form.useForm();

  // 獲取單位列表
  const fetchUnits = async () => {
    setLoading(true);
    try {
      const response = await getUnits();
      if (response.success && response.data) {
        setUnits(response.data);
      } else {
        message.error(response.message || "獲取單位列表失敗");
      }
    } catch (error) {
      message.error("獲取單位列表失敗");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnits();
  }, []);

  // 處理新增/編輯
  const handleSubmit = async (values: any) => {
    try {
      const data = {
        ...values,
        ...(editingUnit && { unitId: editingUnit.unitId }),
      };

      const response = editingUnit
        ? await updateUnit(data)
        : await createUnit(data);

      if (response.success) {
        message.success(editingUnit ? "更新成功" : "新增成功");
        setModalVisible(false);
        setEditingUnit(null);
        form.resetFields();
        fetchUnits();
      } else {
        message.error(
          response.message || (editingUnit ? "更新失敗" : "新增失敗")
        );
      }
    } catch (error) {
      message.error(editingUnit ? "更新失敗" : "新增失敗");
    }
  };

  // 打開新增對話框
  const handleAdd = () => {
    setEditingUnit(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打開編輯對話框
  const handleEdit = (unit: Unit) => {
    setEditingUnit(unit);
    form.setFieldsValue({
      unitNo: unit.unitNo,
      name: unit.name,
      sortCode: unit.sortCode,
    });
    setModalVisible(true);
  };

  // 關閉對話框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingUnit(null);
    form.resetFields();
  };

  // 表格欄位定義
  const columns: ColumnsType<Unit> = [
    {
      title: "單位編號",
      dataIndex: "unitNo",
      key: "unitNo",
      sorter: (a, b) => a.unitNo.localeCompare(b.unitNo),
    },
    {
      title: "單位名稱",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (time: number) => new Date(time).toLocaleString(),
      sorter: (a, b) => a.createTime - b.createTime,
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Card title="單位管理" className="mt-4">
        <div className="mb-4">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增單位
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={units}
          rowKey="unitId"
          loading={loading}
          pagination={{
            total: units.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
          }}
        />
      </Card>

      <Modal
        title={editingUnit ? "編輯單位" : "新增單位"}
        open={modalVisible}
        onOk={() => form.submit()}
        onCancel={handleCancel}
        okText="確定"
        cancelText="取消"
        destroyOnClose
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            label="單位編號"
            name="unitNo"
            rules={[
              { required: true, message: "請輸入單位編號" },
              { max: 50, message: "單位編號不能超過50個字符" },
            ]}
          >
            <Input placeholder="請輸入單位編號" />
          </Form.Item>

          <Form.Item
            label="單位名稱"
            name="name"
            rules={[
              { required: true, message: "請輸入單位名稱" },
              { max: 100, message: "單位名稱不能超過100個字符" },
            ]}
          >
            <Input placeholder="請輸入單位名稱" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UnitManagement;
