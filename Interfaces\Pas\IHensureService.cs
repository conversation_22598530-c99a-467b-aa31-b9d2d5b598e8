using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IHensureService
    {
        /// <summary>
        /// 取得所有眷屬健保依附資料列表
        /// </summary>
        /// <param name="_userId">使用者編號</param>
        /// <returns>眷屬健保依附資料列表</returns>
        Task<List<HensureDTO>> GetHensureListAsync(string _userId);

        /// <summary>
        /// 取得眷屬健保依附資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>眷屬健保依附資料明細</returns>
        Task<HensureDTO> GetHensureDetailAsync(string _uid);

        /// <summary>
        /// 新增眷屬健保依附資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddHensureAsync(HensureDTO _data);

        /// <summary>
        /// 編輯眷屬健保依附資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditHensureAsync(HensureDTO _data);

        /// <summary>
        /// 刪除眷屬健保依附資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteHensureAsync(string _uid);
    }
}
