using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 存放地點
    /// </summary>
    public class StorageLocation : ModelBaseEntity
    {
        [Key]
        [Comment("存放地點編號")]
        public Guid StorageLocationId { get; set; } // 存放地點編號

        [Comment("存放地點名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } // 存放地點名稱

        [Comment("詳細地址")]
        [Column(TypeName = "nvarchar(200)")]
        public string Address { get; set; } // 詳細地址

        [Comment("地點描述")]
        [Column(TypeName = "nvarchar(500)")]
        public string Description { get; set; } // 地點描述

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public StorageLocation()
        {
            StorageLocationId = Guid.NewGuid();
            Name = "";
            Address = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class StorageLocationDTO
    {
        public Guid StorageLocationId { get; set; } // 存放地點編號
        public string Name { get; set; } // 存放地點名稱
        public string Address { get; set; } // 詳細地址
        public string Description { get; set; } // 地點描述
        public int SortCode { get; set; } // 排序號碼
        public long? CreateTime { get; set; } // 新增時間
        public string? CreateUserId { get; set; } // 新增者編號
        public long? UpdateTime { get; set; } // 更新時間
        public string? UpdateUserId { get; set; } // 更新者編號
        public long? DeleteTime { get; set; } // 刪除時間
        public string? DeleteUserId { get; set; } // 刪除者編號

        public StorageLocationDTO()
        {
            StorageLocationId = Guid.Empty;
            Name = "";
            Address = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
        }
    }
}

