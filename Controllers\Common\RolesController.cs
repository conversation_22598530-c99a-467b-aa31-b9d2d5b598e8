using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using System.Threading.Tasks;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("角色管理")]
    public class RolesController : ControllerBase
    {
        private readonly IRolesService _Interface;

        // 在建構子中注入多個依賴
        public RolesController(IRolesService common_Roles_Interface)
        {
            _Interface = common_Roles_Interface;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        [HttpGet]
        [Route("GetRoles")]
        [SwaggerOperation(Summary = "取得角色列表", Description = "取得所有角色列表")]
        public async Task<IActionResult> GetRolesList()
        {
            var roles = await _Interface.GetRolesAsync();
            return Ok(roles);
        }

        [HttpGet]
        [Route("GetRoles/{_rolesId?}")]
        [SwaggerOperation(Summary = "取得角色明細", Description = "依ID取得角色明細")]
        public async Task<IActionResult> GetRolesDetail(string _rolesId)
        {
            var role = await _Interface.GetRolesAsync(_rolesId);
            return Ok(role);
        }

        [HttpPost]
        [Route("AddRoles")]
        [SwaggerOperation(Summary = "新增角色", Description = "新增角色資料")]
        public async Task<IActionResult> AddRoles([FromBody] RolesDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (success, message) = await _Interface.AddRolesAsync(_data,tokenUid);
            return success ? Ok(new { message }):BadRequest(new { message });
        }

        [HttpPost]
        [Route("EditRoles")]
        [SwaggerOperation(Summary = "編輯角色", Description = "修改已存在之角色資料")]
        public async Task<IActionResult> EditRoles([FromBody] RolesDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (success, message) = await _Interface.EditRolesAsync(_data,tokenUid);

            return success ? Ok(new { message }):BadRequest(new { message });
        }

        [HttpPost]
        [Route("DeleteRoles")]
        [SwaggerOperation(Summary = "刪除角色", Description = "刪除已存在之角色資料")]
        public async Task<IActionResult> DeleteRoles([FromBody] RolesDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (success, message) = await _Interface.DeleteRolesAsync(_data,tokenUid);
            return success ? Ok(new { message }):BadRequest(new { message });
        }
    }

}
