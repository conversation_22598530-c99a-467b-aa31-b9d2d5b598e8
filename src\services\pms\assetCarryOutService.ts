import { httpClient } from '../http';
import { ApiResponse, apiEndpoints } from '@/config/api';
import {
    AssetCarryOut,
    AssetCarryOutQuery,
    CreateAssetCarryOutRequest,
    UpdateAssetCarryOutRequest,
    ApprovalRequest,
    CarryOutRequest,
    ReturnRequest,
    BatchProcessRequest,
    AssetCarryOutStatistics
} from '@/app/pms/document_maintenance/asset_carryout_form/interface';

/**
 * 取得攜出申請列表
 * @param query 查詢參數
 * @returns 攜出申請列表
 */
export const getAssetCarryOuts = async (query?: AssetCarryOutQuery): Promise<ApiResponse<AssetCarryOut[]>> => {
    try {
        const params = new URLSearchParams();

        if (query?.status) {
            params.append('status', query.status);
        }
        if (query?.applicantId) {
            params.append('applicantId', query.applicantId);
        }
        if (query?.assetId) {
            params.append('assetId', query.assetId);
        }

        const url = params.toString() ? `${apiEndpoints.getAssetCarryOuts}?${params.toString()}` : apiEndpoints.getAssetCarryOuts;
        const response = await httpClient(url, {
            method: 'GET',
        });

        return response;
    } catch (error: any) {
        console.error('取得攜出申請列表失敗:', error);
        return {
            success: false,
            message: error.message || '取得攜出申請列表失敗',
            data: []
        };
    }
};

/**
 * 根據ID取得攜出申請詳細資料
 * @param carryOutId 攜出單號
 * @returns 攜出申請詳細資料
 */
export const getAssetCarryOutById = async (carryOutId: string): Promise<ApiResponse<AssetCarryOut>> => {
    try {
        const response = await httpClient(`${apiEndpoints.getAssetCarryOutById}/${carryOutId}`, {
            method: 'GET',
        });
        return response;
    } catch (error: any) {
        console.error('取得攜出申請詳細資料失敗:', error);
        return {
            success: false,
            message: error.message || '取得攜出申請詳細資料失敗',
        };
    }
};

/**
 * 新增攜出申請
 * @param request 攜出申請資料
 * @returns 新增結果
 */
export const createAssetCarryOut = async (request: CreateAssetCarryOutRequest): Promise<ApiResponse> => {
    try {
        const response = await httpClient(apiEndpoints.addAssetCarryOut, {
            method: 'POST',
            body: JSON.stringify(request),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error: any) {
        console.error('新增攜出申請失敗:', error);
        return {
            success: false,
            message: error.message || '新增攜出申請失敗',
        };
    }
};

/**
 * 修改攜出申請
 * @param carryOutNo 攜出單號
 * @param request 攜出申請資料
 * @returns 修改結果
 */
export const updateAssetCarryOut = async (carryOutNo: string, request: UpdateAssetCarryOutRequest): Promise<ApiResponse> => {
    try {
        const response = await httpClient(`${apiEndpoints.editAssetCarryOut}/${carryOutNo}`, {
            method: 'POST',
            body: JSON.stringify(request),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error: any) {
        console.error('修改攜出申請失敗:', error);
        return {
            success: false,
            message: error.message || '修改攜出申請失敗',
        };
    }
};

/**
 * 刪除攜出申請
 * @param carryOutNo 攜出單號
 * @param userId 操作人員ID
 * @returns 刪除結果
 */
export const deleteAssetCarryOut = async (carryOutNo: string, userId: string): Promise<ApiResponse> => {
    try {
        const response = await httpClient(`${apiEndpoints.deleteAssetCarryOut}/${carryOutNo}?userId=${userId}`, {
            method: 'POST',
        });
        return response;
    } catch (error: any) {
        console.error('刪除攜出申請失敗:', error);
        return {
            success: false,
            message: error.message || '刪除攜出申請失敗',
        };
    }
};

/**
 * 審核攜出申請
 * @param carryOutNo 攜出單號
 * @param request 審核請求
 * @returns 審核結果
 */
export const approveAssetCarryOut = async (carryOutNo: string, request: ApprovalRequest): Promise<ApiResponse> => {
    try {
        const response = await httpClient(`${apiEndpoints.approveAssetCarryOut}/${carryOutNo}/approve`, {
            method: 'POST',
            body: JSON.stringify(request),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error: any) {
        console.error('審核攜出申請失敗:', error);
        return {
            success: false,
            message: error.message || '審核攜出申請失敗',
        };
    }
};

/**
 * 登記攜出
 * @param carryOutNo 攜出單號
 * @param request 攜出登記請求
 * @returns 登記結果
 */
export const registerCarryOut = async (carryOutNo: string, request: CarryOutRequest): Promise<ApiResponse> => {
    try {
        const response = await httpClient(`${apiEndpoints.registerCarryOut}/${carryOutNo}/carry-out`, {
            method: 'POST',
            body: JSON.stringify(request),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error: any) {
        console.error('登記攜出失敗:', error);
        return {
            success: false,
            message: error.message || '登記攜出失敗',
        };
    }
};

/**
 * 登記歸還
 * @param carryOutNo 攜出單號
 * @param request 歸還登記請求
 * @returns 登記結果
 */
export const registerReturn = async (carryOutNo: string, request: ReturnRequest): Promise<ApiResponse> => {
    try {
        const response = await httpClient(`${apiEndpoints.registerReturn}/${carryOutNo}/return`, {
            method: 'POST',
            body: JSON.stringify(request),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error: any) {
        console.error('登記歸還失敗:', error);
        return {
            success: false,
            message: error.message || '登記歸還失敗',
        };
    }
};

/**
 * 批次處理攜出申請
 * @param request 批次處理資料
 * @returns 處理結果
 */
export const batchProcessCarryOut = async (request: BatchProcessRequest): Promise<ApiResponse> => {
    try {
        const response = await httpClient(apiEndpoints.batchProcessCarryOut, {
            method: 'POST',
            body: JSON.stringify(request),
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return response;
    } catch (error: any) {
        console.error('批次處理失敗:', error);
        return {
            success: false,
            message: error.message || '批次處理失敗',
        };
    }
};

/**
 * 取得攜出統計資料
 * @param userId 使用者ID (可選)
 * @returns 統計資料
 */
export const getCarryOutStatistics = async (userId?: string): Promise<ApiResponse<AssetCarryOutStatistics>> => {
    try {
        const url = userId ? `${apiEndpoints.getCarryOutStatistics}?userId=${userId}` : apiEndpoints.getCarryOutStatistics;
        const response = await httpClient(url, {
            method: 'GET',
        });
        return response;
    } catch (error: any) {
        console.error('取得攜出統計資料失敗:', error);
        return {
            success: false,
            message: error.message || '取得攜出統計資料失敗',
        };
    }
};

/**
 * 檢查逾期未還的資產
 * @returns 逾期列表
 */
export const getOverdueCarryOuts = async (): Promise<ApiResponse<AssetCarryOut[]>> => {
    try {
        const response = await httpClient(apiEndpoints.getOverdueCarryOuts, {
            method: 'GET',
        });
        return response;
    } catch (error: any) {
        console.error('檢查逾期未還資產失敗:', error);
        return {
            success: false,
            message: error.message || '檢查逾期未還資產失敗',
            data: []
        };
    }
};

/**
 * 產生攜出申請單號
 * @returns 新的申請單號
 */
export const generateCarryOutNumber = async (): Promise<ApiResponse<string>> => {
    try {
        const response = await httpClient(apiEndpoints.generateCarryOutNumber, {
            method: 'GET',
        });
        return response;
    } catch (error: any) {
        console.error('產生攜出申請單號失敗:', error);
        return {
            success: false,
            message: error.message || '產生攜出申請單號失敗',
        };
    }
}; 