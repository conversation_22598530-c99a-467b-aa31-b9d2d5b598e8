using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 供應商詳細資訊 </summary>
public class SupplierDetail
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 供應商代碼 </summary>
    [MaxLength(20)]
    [Comment("供應商代碼")]
    [Column(TypeName = "nvarchar(20)")]
    public string? SupplierCode { get; set; }

    /// <summary> 供應商分類編號 </summary>
    [Comment("供應商分類編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid? SupplierCategoryID { get; set; }
    
    /// <summary> 供應商分類導航屬性 </summary>
    public SupplierCategory? SupplierCategory { get; set; }

    /// <summary> 應付結帳日 (每月) </summary>
    [Comment("應付結帳日")]
    [Range(1, 31, ErrorMessage = "結帳日必須在1-31之間")]
    public int? SettlementDay { get; set; }

    /// <summary> 建構式 </summary>
    public SupplierDetail()
    {
        PartnerID = Guid.Empty;
    }
}

/// <summary> 供應商詳細資訊DTO </summary>
public class SupplierDetailDTO
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    public Guid PartnerID { get; set; }

    /// <summary> 供應商代碼 </summary>
    [Column(TypeName = "nvarchar(20)")]
    public string? SupplierCode { get; set; }

    /// <summary> 供應商分類編號 </summary>
    public Guid? SupplierCategoryID { get; set; }
    
    /// <summary> 供應商分類導航屬性 </summary>
    public SupplierCategory? SupplierCategory { get; set; }

    /// <summary> 應付結帳日 (每月) </summary>
    [Comment("應付結帳日")]
    [Range(1, 31, ErrorMessage = "結帳日必須在1-31之間")]
    public int? SettlementDay { get; set; }

    /// <summary> 建構式 </summary>
    public SupplierDetailDTO()
    {
        PartnerID = Guid.Empty;
    }
}