﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 教育訓練資料表
    /// </summary>
    public class Train : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 使用者編號

        [Comment("課程名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string courseName { get; set; } // 課程名稱

        [Comment("費用")]
        [Column(TypeName = "int")]
        public int cost { get; set; } // 費用

        [Comment("名次")]
        [Column(TypeName = "nvarchar(100)")]
        public string ranking { get; set; } // 名次

        [Comment("成績")]
        [Column(TypeName = "nvarchar(100)")]
        public string score { get; set; } // 成績

        [Comment("講師名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string instructor { get; set; } // 講師名稱

        [Comment("時數")]
        [Column(TypeName = "nvarchar(10)")]
        public string durationHours { get; set; } // 時數

        [Comment("訓練機構名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string trainingInstitute { get; set; } // 訓練機構名稱

        [Comment("課程起日")]
        [Column(TypeName = "bigint")]
        public long? courseStartDate { get; set; } // 課程起日

        [Comment("課程迄日")]
        [Column(TypeName = "bigint")]
        public long? courseEndDate { get; set; } // 課程迄日

        [Comment("發證日期")]
        [Column(TypeName = "bigint")]
        public long? certificateDate { get; set; } // 發證日期

        [Comment("證書文號")]
        [Column(TypeName = "nvarchar(100)")]
        public string certificateNumber { get; set; } // 證書文號

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string remark { get; set; } // 備註

        public Train()
        {
            uid = "";
            userId = "";
            courseName = "";
            cost = 0;
            ranking = "";
            score = "";
            instructor = "";
            durationHours = "";
            certificateDate = null;
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class TrainDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string courseName { get; set; } // 課程名稱
        public int cost { get; set; } // 費用
        public string ranking { get; set; } // 名次
        public string score { get; set; } // 成績
        public string instructor { get; set; } // 講師名稱
        public string durationHours { get; set; } // 時數
        public string trainingInstitute { get; set; } // 訓練機構名稱
        public string courseStartDate { get; set; } // 課程起日
        public string courseEndDate { get; set; } // 課程迄日
        public string certificateDate { get; set; } // 發證日期
        public string certificateNumber { get; set; } // 證書文號
        public string remark { get; set; } // 備註
        public TrainDTO()
        {
            uid = "";
            userId = "";
            courseName = "";
            cost = 0;
            ranking = "";
            score = "";
            instructor = "";
            durationHours = "";
            certificateDate = null;
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

