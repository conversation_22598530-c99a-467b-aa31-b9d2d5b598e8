using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("保證書資料管理")]
    public class EnsureController : ControllerBase
    {
        private readonly IEnsureService _interface;

        public EnsureController(IEnsureService ensureService)
        {
            _interface = ensureService;
        }

        [HttpGet]
        [Route("GetAll/{userId}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有保證書資料列表")]
        public async Task<IActionResult> GetEnsureList(string userId)
        {
            var result = await _interface.GetEnsureListAsync(userId);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得保證書明細", Description = "依uid取得保證書資料明細")]
        public async Task<IActionResult> GetEnsureDetail(string uid)
        {
            var result = await _interface.GetEnsureDetailAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增保證書資料", Description = "新增保證書資料")]
        public async Task<IActionResult> AddEnsure([FromBody] EnsureDTO data)
        {
            var (result, msg) = await _interface.AddEnsureAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯保證書資料", Description = "編輯保證書資料")]
        public async Task<IActionResult> EditEnsure([FromBody] EnsureDTO data)
        {
            var (result, msg) = await _interface.EditEnsureAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除保證書資料", Description = "刪除保證書資料")]
        public async Task<IActionResult> DeleteEnsure([FromBody] string uid)
        {
            var (result, msg) = await _interface.DeleteEnsureAsync(uid);
            return Ok(new { result, msg });
        }
    }
}
