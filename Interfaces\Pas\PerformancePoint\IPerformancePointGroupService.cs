using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IPerformancePointGroupService
    {
        /// <summary>
        /// 取得所有點數群組資料
        /// </summary>
        /// <returns>點數群組列表</returns>
        Task<List<PerformancePointGroupDTO>> GetAllAsync();

        /// <summary>
        /// 取得單一點數群組明細
        /// </summary>
        /// <param name="uid">點數群組UID</param>
        /// <returns>點數群組明細</returns>
        Task<PerformancePointGroupDTO?> GetDetailAsync(string uid);

        /// <summary>
        /// 新增點數群組資料
        /// </summary>
        /// <param name="data">資料內容</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> AddAsync(PerformancePointGroupDTO data);

        /// <summary>
        /// 編輯點數群組資料
        /// </summary>
        /// <param name="data">資料內容</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> EditAsync(PerformancePointGroupDTO data);

        /// <summary>
        /// 刪除點數群組資料
        /// </summary>
        /// <param name="uid">資料編號</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> DeleteAsync(string uid);
    }
}
