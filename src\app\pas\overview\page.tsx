'use client';

import { useState } from 'react';
import { Card, Row, Col, Typography, Badge, Button, Flex, Space } from 'antd';
import {
  DollarCircleOutlined,
  SettingOutlined,
  TeamOutlined,
  BarChartOutlined,
  ClockCircleOutlined,
  SafetyCertificateOutlined,
  <PERSON>LeftOutlined,
  SafetyOutlined,
  AccountBookOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import SalaryPointList from '@/app/pas/overview/overview_modules/SalaryPoint/SalaryPointList';
import RegularSalaryMaintainPage from '@/app/pas/overview/overview_modules/regularSalary/RegularSalaryMaintainPage';
import PerformancePointDashboard from '@/app/pas/overview/overview_modules/performancePoint/PerformancePointDashboard';
import InsuranceGradeManagement from '@/app/pas/overview/overview_modules/InsuranceGrade/InsuranceGradeManagement';
import PositionTableManagement from '@/app/pas/overview/overview_modules/positionTable/PositionTableManagement';

const { Title, Text } = Typography;

interface SettingCard {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  status?: 'success' | 'processing' | 'default' | 'error' | 'warning';
}

const OverviewPage = () => {
  const [selectedSetting, setSelectedSetting] = useState<SettingCard | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const settingCards: SettingCard[] = [
    {
      key: 'regularSalary',
      title: '常態薪資項目設定',
      description: '設定基本薪資、獎金等常態性薪資項目',
      icon: <DollarCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      component: <RegularSalaryMaintainPage />,
      status: 'default'
    },
    {
      key: 'salaryPoint',
      title: '薪點管理',
      description: '管理薪點金額調整',
      icon: <BarChartOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      component: <SalaryPointList />,
      status: 'default'
    },
    {
      key: 'performancePoint',
      title: '績效點數設定',
      description: '績效點數項目設定、年度點數總表檢視',
      icon: <SafetyCertificateOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
      component: <PerformancePointDashboard />,
      status: 'processing'
    },
    {
      key: 'laborInsurance',
      title: '勞健保級距費用',
      description: '設定與管理勞保、健保費用級距表',
      icon: <SafetyOutlined style={{ fontSize: '24px', color: '#f5222d' }} />,
      component: <InsuranceGradeManagement />,
      status: 'default'
    },
    {
      key: 'salaryGeneration',
      title: '薪資資料產生作業',
      description: '產生月薪、獎金、補薪等薪資資料，進行薪資計算與核發',
      icon: <AccountBookOutlined style={{ fontSize: '24px', color: '#faad14' }} />,
      component: <div>薪資資料產生作業（開發中）</div>,
      status: 'default'
    },
    {
      key: 'positionTable',
      title: '職務定位表',
      description: '檢視當前職務層級、職責範圍定位',
      icon: <ApartmentOutlined style={{ fontSize: '24px', color: '#08979c' }} />,
      component: <PositionTableManagement />,
      status: 'processing'
    },
    {
      key: 'taxRate',
      title: '薪資稅率設定',
      description: '管理薪資稅率、含累進稅率級距設定',
      icon: <ClockCircleOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />,
      component: <div>薪資稅率設定（開發中）</div>,
      status: 'default'
    },
    {
      key: 'nonEmployeeSalary',
      title: '非員工薪資登錄',
      description: '非員工薪資登錄',
      icon: <TeamOutlined style={{ fontSize: '24px', color: '#eb2f96' }} />,
      component: <div>非員工薪資登錄（開發中）</div>,
      status: 'default'
    },
    {
      key: 'system',
      title: '系統參數設定',
      description: '管理系統基本參數與進階設定',
      icon: <SettingOutlined style={{ fontSize: '24px', color: '#13c2c2' }} />,
      component: <div>系統參數設定（開發中）</div>,
      status: 'default'
    }
  ];

  const handleCardClick = (setting: SettingCard) => {
    setSelectedSetting(setting);
    setIsExpanded(true);
    // 平滑滾動到頁面頂部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBack = () => {
    setIsExpanded(false);
    setTimeout(() => setSelectedSetting(null), 300); // 等待動畫結束後再清除選中狀態
  };

  const renderSettingCards = () => (
    <div style={{ overflow: 'hidden', width: '100%' }}>
      <Row gutter={[24, 24]} style={{
        opacity: isExpanded ? 0 : 1,
        transform: `translateY(${isExpanded ? '20px' : '0'})`,
        transition: 'all 0.3s ease-in-out',
        display: isExpanded ? 'none' : 'flex',
        margin: '0 -8px'  // 抵消 Row 的 gutter
      }}>
        {settingCards.map(setting => (
          <Col xs={24} sm={12} md={8} lg={8} xl={8} key={setting.key} style={{ padding: '0 12px' }}>
            <Badge.Ribbon text={
              setting.status === 'processing' ? '使用中' :
                setting.status === 'success' ? '已設定' :
                  setting.status === 'warning' ? '待設定' :
                    '開發中'
            } color={
              setting.status === 'processing' ? 'blue' :
                setting.status === 'success' ? 'green' :
                  setting.status === 'warning' ? 'orange' :
                    'gray'
            }>
              <Card
                hoverable
                onClick={() => handleCardClick(setting)}
                className="h-full"
                style={{
                  height: '100%',
                  transition: 'all 0.3s',
                  width: '100%'
                }}
              >
                <div className="flex items-center mb-4">
                  {setting.icon}
                  <Title level={4} className="mb-0 ml-3">
                    {setting.title}
                  </Title>
                </div>
                <Text type="secondary">
                  {setting.description}
                </Text>
              </Card>
            </Badge.Ribbon>
          </Col>
        ))}
      </Row>
    </div>
  );

  const renderSettingContent = () => (
    <div style={{
      opacity: isExpanded ? 1 : 0,
      transform: `translateY(${isExpanded ? '0' : '-20px'})`,
      transition: 'all 0.3s ease-in-out',
      display: !isExpanded ? 'none' : 'block',
      width: '100%',
      overflow: 'hidden'
    }}>
      <Flex vertical gap="middle">
        <Space className="py-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            style={{
              display: 'flex',
              alignItems: 'center',
              fontSize: '15px',
              height: '40px',
              borderRadius: '8px',
              transition: 'all 0.3s',
            }}
            className="hover:bg-gray-100 hover:border-gray-300"
          >
            返回設定首頁
          </Button>
        </Space>
        <Card
          title={
            <Flex align="center" gap="middle">
              {selectedSetting?.icon}
              <span>{selectedSetting?.title}</span>
            </Flex>
          }
          style={{ width: '100%' }}
        >
          {selectedSetting?.component}
        </Card>
      </Flex>
    </div>
  );

  return (
    <div className="p-6 overflow-x-hidden" style={{ maxWidth: '100%' }}>
      <div className="mb-6">
        <Title level={2}>功能總覽</Title>
        <Text type="secondary">
          {isExpanded ? selectedSetting?.description : '選擇下方任一設定項目進行管理與設定'}
        </Text>
      </div>

      {renderSettingCards()}
      {renderSettingContent()}
    </div>
  );
};

export default OverviewPage;
