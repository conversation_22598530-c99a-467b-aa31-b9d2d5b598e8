"use client";

import React, { useState, useEffect } from "react";
import { Layout, <PERSON>u, Button, Tooltip } from "antd";
import type { MenuProps } from "antd";
import * as AntdIcons from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { routes, RouteKeys } from "@/config/routes";
import Logo from "@/app/components/common/Logo";
import { useSiderStore } from "@/store/siderStore";
import * as authService from "@/services/authService";

const { Sider: AntSider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

// 動態生成圖標組件
const createIconComponent = (iconName: string) => {
  const Icon = (AntdIcons as any)[iconName];
  return Icon ? React.createElement(Icon) : null;
};

// 動態生成圖標映射表
const generateIconMap = () => {
  const iconMap: { [key: string]: React.ReactNode } = {};

  Object.keys(AntdIcons).forEach((key) => {
    // 只包含 Outlined、Filled 和 TwoTone 類型的圖標
    if (
      key.endsWith("Outlined") ||
      key.endsWith("Filled") ||
      key.endsWith("TwoTone")
    ) {
      iconMap[key] = createIconComponent(key);
    }
  });

  return iconMap;
};

// 圖標映射表
const iconMap = generateIconMap();

// 解析圖標
function parseIcon(iconStr: string | null | undefined): React.ReactNode {
  if (!iconStr) return null;
  return iconMap[iconStr] || null;
}

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

// 轉換函數
function transformMenuData(data: any[]): MenuItem[] {
  // 先過濾出系統設定項目
  const systemSettings = data.find((item) => item.key === "system");
  // 過濾掉系統設定項目
  const otherItems = data.filter((item) => item.key !== "system");

  // 轉換其他項目
  const transformedItems = otherItems.map((menu) => {
    const { label, key, icon, children } = menu;
    return getItem(
      label,
      key,
      parseIcon(icon),
      children && children.length > 0 ? transformMenuData(children) : undefined
    );
  });

  // 如果找到系統設定項目，將其添加到最後
  if (systemSettings) {
    transformedItems.push(
      getItem(
        systemSettings.label,
        systemSettings.key,
        parseIcon(systemSettings.icon),
        systemSettings.children && systemSettings.children.length > 0
          ? transformMenuData(systemSettings.children)
          : undefined
      )
    );
  }

  return transformedItems;
}

interface SiderProps {
  isMobile: boolean;
}

const Sider: React.FC<SiderProps> = ({ isMobile }) => {
  const { collapsed, setCollapsed } = useSiderStore();
  const router = useRouter();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);

  // 獲取選單列表
  const getMenu = async () => {
    try {
      const result = await authService.getAllMeun();
      if (
        result.success &&
        result.data &&
        Array.isArray(result.data) &&
        result.data.length > 0
      ) {
        const menuData = result.data.flatMap((group) => group.menus);
        const items: MenuItem[] = transformMenuData(menuData);
        setMenuItems(items);
      } else {
        setMenuItems([
          getItem("系統設定", "system", parseIcon("SettingOutlined")),
        ]);
      }
    } catch (error) {
      console.error("Error fetching menu:", error);
      setMenuItems([
        getItem("系統設定", "system", parseIcon("SettingOutlined")),
      ]);
    }
  };

  useEffect(() => {
    getMenu();
  }, []);

  const handleMenuClick: MenuProps["onClick"] = ({ key }) => {
    const routeKey = key as RouteKeys;
    if (routes[routeKey]) {
      router.push(routes[routeKey]);
    }
  };

  const handleHomeClick = () => {
    router.push(routes.root);
  };

  return (
    <AntSider
      collapsed={collapsed}
      onCollapse={setCollapsed}
      style={{
        overflow: "auto",
        height: "100vh",
        position: "fixed",
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 100,
      }}
      theme="light"
      width={240}
      collapsible={false}
      trigger={null}
    >
      <div
        style={{
          height: 64,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
        }}
      >
        <Logo showText={!collapsed} />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          height: "calc(100% - 64px)",
        }}
      >
        <Menu
          mode="inline"
          items={menuItems}
          style={{ borderRight: 0, flex: 1 }}
          onClick={handleMenuClick}
        />
        <div
          style={{
            padding: "16px",
            borderTop: "1px solid rgba(0, 0, 0, 0.06)",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 8,
          }}
        >
          <Tooltip title={collapsed ? "回首頁" : ""} placement="right">
            <Button
              type="text"
              icon={parseIcon("HomeOutlined")}
              onClick={handleHomeClick}
              style={{ flex: 1 }}
            >
              {!collapsed && "回首頁"}
            </Button>
          </Tooltip>
          <Button
            type="text"
            icon={parseIcon(
              collapsed ? "MenuUnfoldOutlined" : "MenuFoldOutlined"
            )}
            onClick={() => setCollapsed(!collapsed)}
          />
        </div>
      </div>
    </AntSider>
  );
};

export default Sider;
