using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Pas
{
    public class SalaryPointService : ISalaryPointService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public SalaryPointService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<SalaryPointDTO>> GetListAsync()
        {
            var list = await _context.Pas_SalaryPoint
                .Where(x => !x.IsDeleted)
                .OrderByDescending(x => x.EffectiveDate)
                .ToListAsync();

            return list.Select(x => new SalaryPointDTO
            {
                Uid = x.uid,
                PointLevel = x.PointLevel,
                Amount = x.Amount,
                EffectiveDate = _baseform.TimestampToDateStr(x.EffectiveDate),
                AdjustmentReason = x.AdjustmentReason,
                CreateTime = x.CreateTime,
                CreateUserId = x.CreateUserId,
                UpdateTime = x.UpdateTime,
                UpdateUserId = x.UpdateUserId,
                DeleteTime = x.DeleteTime,
                DeleteUserId = x.DeleteUserId,
                IsDeleted = x.IsDeleted
            }).ToList();
        }

        public async Task<SalaryPointDTO?> GetDetailAsync(string uid)
        {
            var entity = await _context.Pas_SalaryPoint
                .FirstOrDefaultAsync(x => x.uid == uid && !x.IsDeleted);

            if (entity == null) return null;

            return new SalaryPointDTO
            {
                Uid = entity.uid,
                PointLevel = entity.PointLevel,
                Amount = entity.Amount,
                EffectiveDate = _baseform.TimestampToDateStr(entity.EffectiveDate),
                AdjustmentReason = entity.AdjustmentReason,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId,
                IsDeleted = entity.IsDeleted
            };
        }

        public async Task<(bool, string)> AddAsync(SalaryPointDTO dto)
        {
            using var tx = await _context.Database.BeginTransactionAsync();
            try
            {
                var effectiveDate = _baseform.DateStrToTimestamp(dto.EffectiveDate);
                if (!effectiveDate.HasValue)
                    return (false, "生效日期格式不正確");

                var exists = await _context.Pas_SalaryPoint
                    .FirstOrDefaultAsync(x => !x.IsDeleted
                        && x.PointLevel == dto.PointLevel
                        && x.EffectiveDate == effectiveDate.Value);

                if (exists != null)
                    return (false, "已存在相同薪點名稱與生效日期的資料");

                var entity = new SalaryPoint
                {
                    uid = Guid.NewGuid().ToString(),
                    PointLevel = dto.PointLevel,
                    Amount = dto.Amount,
                    EffectiveDate = effectiveDate.Value,
                    AdjustmentReason = dto.AdjustmentReason ?? string.Empty,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId,
                    IsDeleted = false
                };

                await _context.Pas_SalaryPoint.AddAsync(entity);
                await _context.SaveChangesAsync();
                await tx.CommitAsync();

                return (true, "新增薪點金額成功");
            }
            catch (Exception ex)
            {
                await tx.RollbackAsync();
                return (false, $"新增薪點金額失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditAsync(SalaryPointDTO dto)
        {
            using var tx = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_SalaryPoint
                    .FirstOrDefaultAsync(x => x.uid == dto.Uid && !x.IsDeleted);

                if (entity == null)
                    return (false, "資料不存在");

                var effectiveDate = _baseform.DateStrToTimestamp(dto.EffectiveDate);
                if (!effectiveDate.HasValue)
                    return (false, "生效日期格式不正確");

                var exists = await _context.Pas_SalaryPoint
                    .FirstOrDefaultAsync(x => !x.IsDeleted
                        && x.uid != dto.Uid
                        && x.PointLevel == dto.PointLevel
                        && x.EffectiveDate == effectiveDate.Value);

                if (exists != null)
                    return (false, "已存在相同薪點名稱與生效日期的資料");

                entity.PointLevel = dto.PointLevel;
                entity.Amount = dto.Amount;
                entity.EffectiveDate = effectiveDate.Value;
                entity.AdjustmentReason = dto.AdjustmentReason ?? string.Empty;
                entity.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                entity.UpdateUserId = _currentUserService.UserId;

                _context.Pas_SalaryPoint.Update(entity);
                await _context.SaveChangesAsync();
                await tx.CommitAsync();

                return (true, "編輯薪點金額成功");
            }
            catch (Exception ex)
            {
                await tx.RollbackAsync();
                return (false, $"編輯薪點金額失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var tx = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_SalaryPoint
                    .FirstOrDefaultAsync(x => x.uid == uid && !x.IsDeleted);

                if (entity == null)
                    return (false, "資料不存在");

                entity.IsDeleted = true;
                entity.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                entity.DeleteUserId = _currentUserService.UserId;

                _context.Pas_SalaryPoint.Update(entity);
                await _context.SaveChangesAsync();
                await tx.CommitAsync();

                return (true, "刪除薪點金額成功");
            }
            catch (Exception ex)
            {
                await tx.RollbackAsync();
                return (false, $"刪除薪點金額失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<decimal?> GetAmountByDateAsync(string dateStr)
        {
            var timestamp = _baseform.DateStrToTimestamp(dateStr);
            if (!timestamp.HasValue)
                return null;

            var result = await _context.Pas_SalaryPoint
                .Where(x => !x.IsDeleted && x.EffectiveDate <= timestamp)
                .OrderByDescending(x => x.EffectiveDate)
                .FirstOrDefaultAsync();

            return result?.Amount;
        }
    }
}
