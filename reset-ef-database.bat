@echo off
chcp 65001
echo 開始執行 EF 資料庫重置腳本...
echo.

echo 步驟 1: 清除 Migrations 目錄底下的檔案...
if exist "Migrations\*" (
    del /q "Migrations\*.*"
    if %ERRORLEVEL% EQU 0 (
        echo Migrations 目錄檔案已清除 - 成功!
    ) else (
        echo 清除 Migrations 目錄失敗，錯誤代碼: %ERRORLEVEL%
        goto :error
    )
) else (
    echo Migrations 目錄不存在或已為空
)
echo.

echo 步驟 2: 刪除資料庫...
dotnet ef database drop -f
if %ERRORLEVEL% NEQ 0 (
    echo 刪除資料庫失敗，錯誤代碼: %ERRORLEVEL%
    goto :error
)
echo 資料庫刪除成功!
echo.

echo 步驟 3: 創建新的遷移...
dotnet ef migrations add InitialCreate
if %ERRORLEVEL% NEQ 0 (
    echo 創建遷移失敗，錯誤代碼: %ERRORLEVEL%
    goto :error
)
echo 遷移創建成功!
echo.

echo 步驟 4: 更新資料庫...
dotnet ef database update
if %ERRORLEVEL% NEQ 0 (
    echo 更新資料庫失敗，錯誤代碼: %ERRORLEVEL%
    goto :error
)
echo 資料庫更新成功!
echo.

echo 所有操作已完成！
goto :end

:error
echo 執行過程中發生錯誤，腳本停止執行。
pause
exit /b 1

:end
pause
exit /b 0