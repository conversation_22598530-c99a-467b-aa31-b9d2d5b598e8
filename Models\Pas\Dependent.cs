﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Drawing;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 扶養資料表
    /// </summary>
    public class Dependent : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("扶養者員工")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // userid

        [Comment("被扶養者身分證字號")]
        [Column(TypeName = "nvarchar(100)")]
        public string dependentRocId { get; set; } // 被扶養者身分證字號

        [Comment("被扶養者姓名")]
        [Column(TypeName = "nvarchar(100)")]
        public string dependentName { get; set; } // 被扶養者姓名

        [Comment("被扶養者生日")]
        [Column(TypeName = "bigint")]
        public long? dependentBirthday { get; set; } // 被扶養者生日

        [Comment("被扶養者關係類型")]
        [Column(TypeName = "nvarchar(3)")]
        public string dependentRelationType { get; set; } // 被扶養者關係類型

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string remark { get; set; } // 備註

        public Dependent()
        {
            uid = "";
            userId = "";
            dependentRocId = "";
            dependentName = "";
            dependentBirthday = null;
            dependentRelationType = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class DependentDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 依附使用者編號
        public string dependentRocId { get; set; } // 被扶養者身分證字號
        public string dependentName { get; set; } // 被扶養者姓名
        public string dependentBirthday { get; set; } // 被扶養者生日
        public string dependentRelationType { get; set; } // 被扶養者關係類型
        public string dependentRelationTypeName { get; set; } // 被扶養者關係類型名稱
        public string remark { get; set; } // 備註
        public DependentDTO()
        {
            uid = "";
            userId = "";
            dependentRocId = "";
            dependentName = "";
            dependentBirthday = "";
            dependentRelationType = "";
            dependentRelationTypeName = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

