using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IPerformancePointTypeService
    {
        /// <summary>
        /// 根據點數群組UID取得點數類型列表
        /// </summary>
        /// <param name="groupUid">點數群組UID</param>
        /// <returns>點數類型列表</returns>
        Task<List<PerformancePointTypeDTO>> GetByGroupUidAsync(string groupUid);

        /// <summary>
        /// 取得單一點數類型明細
        /// </summary>
        /// <param name="uid">點數類型UID</param>
        /// <returns>點數類型明細</returns>
        Task<PerformancePointTypeDTO?> GetDetailAsync(string uid);

        /// <summary>
        /// 新增點數類型資料
        /// </summary>
        /// <param name="data">點數類型資料內容</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> AddAsync(PerformancePointTypeDTO data);

        /// <summary>
        /// 編輯點數類型資料
        /// </summary>
        /// <param name="data">點數類型資料內容</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> EditAsync(PerformancePointTypeDTO data);

        /// <summary>
        /// 刪除點數類型資料
        /// </summary>
        /// <param name="uid">資料編號</param>
        /// <returns>執行結果與訊息</returns>
        Task<(bool, string)> DeleteAsync(string uid);

        /// <summary>
        /// 獲取點數類型集聯選項
        /// </summary>
        /// <returns>執行結果與訊息</returns>
        Task<List<CascaderOptionDTO>> GetGroupWithTypesAsCascaderAsync();

    }
}
