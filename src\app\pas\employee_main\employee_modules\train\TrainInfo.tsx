import { useEffect, useState } from 'react';
import { Modal, Button, Spin, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Typography, Space, Divider } from 'antd';
import { getTrainDetail, editTrain, addTrain, deleteTrain, Train, getTrainList, createEmptyTrain } from '@/services/pas/TrainService';
import dayjs from 'dayjs';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    BookOutlined,
    TeamOutlined,
    CalendarOutlined,
    FileTextOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    PlusOutlined,
    TrophyOutlined,
    DollarOutlined,
    BankOutlined,
    ClockCircleOutlined,
    FileProtectOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type TrainInfoProps = {
    userId: string;
    active: boolean;
};

const TrainInfo: React.FC<TrainInfoProps> = ({ userId, active }) => {
    const [trainList, setTrainList] = useState<Train[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [trainDetail, setTrainDetail] = useState<Train | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) {
            fetchTrainList();
        }
    }, [active, userId]);

    const fetchTrainList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const { success, data, message: msg } = await getTrainList(userId);
            if (success && data) {
                setTrainList(data);
            } else {
                message.error(msg || '載入資料失敗');
            }
        } catch (error: any) {
            setErrorMsg(error.message || '未知錯誤');
            message.error(error.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getTrainDetail(uid);
            if (success && data) {
                setTrainDetail(data);
                form.resetFields();
                form.setFieldsValue({
                    ...createEmptyTrain(),
                    courseName: data.courseName,
                    cost: data.cost,
                    ranking: data.ranking,
                    score: data.score,
                    instructor: data.instructor,
                    durationHours: data.durationHours,
                    trainingInstitute: data.trainingInstitute,
                    courseStartDate: data.courseStartDate ? dayjs(data.courseStartDate) : null,
                    courseEndDate: data.courseEndDate ? dayjs(data.courseEndDate) : null,
                    certificateDate: data.certificateDate ? dayjs(data.certificateDate) : null,
                    certificateNumber: data.certificateNumber,
                    remark: data.remark,
                });
                setIsModalOpen(true);
            } else {
                message.error(msg || '載入訓練資料失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '載入訓練資料時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };

    const handleAddNew = () => {
        setTrainDetail(null);
        form.resetFields();
        form.setFieldsValue({
            ...createEmptyTrain()
        });
        setIsModalOpen(true);
    };

    const handleFormChange = (field: string, value: any) => {
        form.setFieldsValue({ [field]: value });
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            setModalLoading(true);

            const payload = {
                ...values,
                userId,
                // 額外須處理的欄位.
                courseStartDate: values.courseStartDate ? values.courseStartDate.format('YYYY-MM-DD') : '',
                courseEndDate: values.courseEndDate ? values.courseEndDate.format('YYYY-MM-DD') : '',
                certificateDate: values.certificateDate ? values.certificateDate.format('YYYY-MM-DD') : '',
            };

            if (trainDetail) {
                const res = await editTrain({ ...payload, uid: trainDetail.uid });
                if (res.success && res.data?.result) {
                    message.success('更新成功');
                    setIsModalOpen(false);
                    fetchTrainList();
                } else {
                    message.error(res.data?.msg || res.message || '更新失敗');
                }
            } else {
                const res = await addTrain(payload);
                if (res.success && res.data?.result) {
                    message.success('新增成功');
                    setIsModalOpen(false);
                    fetchTrainList();
                } else {
                    message.error(res.data?.msg || res.message || '新增失敗');
                }
            }
        } catch (error: any) {
            if (error?.errorFields) {
                // 這是 Ant Design 的表單驗證錯誤，不需要顯示額外錯誤訊息
            } else {
                // 其他錯誤才顯示錯誤訊息
                message.error(error.message || '儲存失敗');
            }
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteTrain(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchTrainList();
            } else {
                message.error(res.data?.msg || '刪除失敗');
            }
        } catch (error: any) {
            message.error(error.message || '刪除過程發生錯誤');
        } finally {

        }
    };

    const handleModalCancel = () => {
        setIsModalOpen(false);
    };

    if (!active) return null;

    if (errorMsg) {
        return (
            <div style={{
                color: '#ff4d4f',
                textAlign: 'center',
                padding: '40px 20px',
                background: '#fff1f0',
                borderRadius: '8px',
                border: '1px solid #ffccc7'
            }}>
                <ExclamationCircleOutlined style={{ marginRight: 8 }} />
                錯誤：{errorMsg}
            </div>
        );
    }

    return (
        <>
            <Card
                title={<Title level={4} style={{ margin: 0 }}>教育訓練資料</Title>}
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddNew}
                        style={{ borderRadius: '6px' }}
                    >
                        新增教育訓練資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={trainList}
                    columns={[
                        {
                            title: '課程名稱',
                            dataIndex: 'courseName',
                            render: (text) => (
                                <Space>
                                    <BookOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '費用',
                            dataIndex: 'cost',
                            render: (text) => (
                                <Space>
                                    <DollarOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '名次',
                            dataIndex: 'ranking',
                            render: (text) => (
                                <Space>
                                    <TrophyOutlined style={{ color: '#faad14' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '訓練期間',
                            render: (_, record) => {
                                const start = record.courseStartDate ? dayjs(record.courseStartDate).format('YYYY-MM-DD') : '-';
                                const end = record.courseEndDate ? dayjs(record.courseEndDate).format('YYYY-MM-DD') : '-';
                                return (
                                    <Space>
                                        <CalendarOutlined style={{ color: '#722ed1' }} />
                                        <Text>{`${start} ~ ${end}`}</Text>
                                    </Space>
                                );
                            }
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            )
                        }
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space direction="vertical" size={16}>
                                    <div>
                                        <Space>
                                            <TeamOutlined style={{ color: '#1890ff' }} />
                                            <Text strong>講師：</Text>
                                            <Text>{record.instructor || '-'}</Text>
                                        </Space>
                                    </div>
                                    <div>
                                        <Space>
                                            <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                            <Text strong>備註：</Text>
                                            <Text>{record.remark || '無'}</Text>
                                        </Space>
                                    </div>
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!(record.instructor || record.remark),
                    }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                        className: record.uid === deleteUid ? 'row-deleting-pulse' : '',
                    })}
                />
            </Card>

            <Modal
                title={
                    <Space>
                        <BookOutlined style={{ color: '#1890ff' }} />
                        <Title level={5} style={{ margin: 0 }}>
                            {trainDetail ? '編輯教育訓練資料' : '新增教育訓練資料'}
                        </Title>
                    </Space>
                }
                open={isModalOpen}
                onCancel={handleModalCancel}
                width={800}
                centered
                maskClosable={false}
                destroyOnClose
                footer={[
                    <Button
                        key="cancel"
                        onClick={handleModalCancel}
                        style={{ borderRadius: '6px' }}
                    >
                        取消
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={handleModalOk}
                        loading={modalLoading}
                        style={{
                            borderRadius: '6px',
                            boxShadow: '0 2px 4px rgba(24,144,255,0.2)'
                        }}
                    >
                        確認
                    </Button>
                ]}
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form
                    layout="vertical"
                    form={form}
                    className="mt-4"
                    style={{
                        width: '100%'
                    }}
                >
                    {/* 課程基本資訊 */}
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <BookOutlined style={{ color: '#1890ff' }} />
                                課程基本資訊
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24}>
                                <Form.Item
                                    name="courseName"
                                    label={<Text strong>課程名稱</Text>}
                                    rules={[{ required: true, message: '請輸入課程名稱' }]}
                                >
                                    <Input
                                        placeholder="請輸入課程名稱"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    name="trainingInstitute"
                                    label={<Text strong>訓練機構</Text>}
                                    rules={[{ required: true, message: '請輸入訓練機構' }]}
                                >
                                    <Input
                                        placeholder="請輸入訓練機構名稱"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    name="instructor"
                                    label={<Text strong>講師</Text>}
                                >
                                    <Input
                                        placeholder="請輸入講師姓名"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 課程時間 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <CalendarOutlined style={{ color: '#1890ff' }} />
                                課程時間
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} lg={8}>
                                <Form.Item
                                    name="courseStartDate"
                                    label={<Text strong>開始日期</Text>}
                                    rules={[{ required: true, message: '請選擇開始日期' }]}
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        format="YYYY-MM-DD"
                                        placeholder="請選擇開始日期"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={8}>
                                <Form.Item
                                    name="courseEndDate"
                                    label={<Text strong>結束日期</Text>}
                                    rules={[{ required: true, message: '請選擇結束日期' }]}
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        format="YYYY-MM-DD"
                                        placeholder="請選擇結束日期"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={8}>
                                <Form.Item
                                    name="durationHours"
                                    label={<Text strong>時數</Text>}
                                >
                                    <Input
                                        placeholder="請輸入課程時數"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 成績與費用 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <TrophyOutlined style={{ color: '#1890ff' }} />
                                成績與費用
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} lg={8}>
                                <Form.Item
                                    name="cost"
                                    label={<Text strong>費用</Text>}
                                >
                                    <Input
                                        placeholder="請輸入課程費用"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={8}>
                                <Form.Item
                                    name="ranking"
                                    label={<Text strong>名次</Text>}
                                >
                                    <Input
                                        placeholder="請輸入名次"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={8}>
                                <Form.Item
                                    name="score"
                                    label={<Text strong>成績</Text>}
                                >
                                    <Input
                                        placeholder="請輸入成績"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 證書資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileProtectOutlined style={{ color: '#1890ff' }} />
                                證書資訊
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    name="certificateDate"
                                    label={<Text strong>發證日期</Text>}
                                >
                                    <DatePicker
                                        style={{ width: '100%', borderRadius: '6px' }}
                                        format="YYYY-MM-DD"
                                        placeholder="請選擇發證日期"
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} lg={12}>
                                <Form.Item
                                    name="certificateNumber"
                                    label={<Text strong>證書字號</Text>}
                                >
                                    <Input
                                        placeholder="請輸入證書字號"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 其他資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileTextOutlined style={{ color: '#1890ff' }} />
                                其他資訊
                            </Space>
                        </Title>
                        <Row gutter={[16, 16]}>
                            <Col span={24}>
                                <Form.Item
                                    name="remark"
                                    label={<Text strong>備註</Text>}
                                >
                                    <Input.TextArea
                                        rows={3}
                                        placeholder="請輸入備註內容"
                                        style={{
                                            resize: 'none',
                                            borderRadius: '6px',
                                            backgroundColor: '#fafafa'
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default TrainInfo;
