import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 存放位置
export interface StorageLocation {
    storageLocationId: string;
    name: string;
    address: string;
    description: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    createUserName?: string;
    updateUserName?: string;
}

// 獲取存放位置列表
export async function getStorageLocations(): Promise<ApiResponse<StorageLocation[]>> {
    try {
        const response = await httpClient(apiEndpoints.getStorageLocations, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取存放位置列表失敗",
            data: [],
        };
    }
}

// 獲取存放位置詳情
export async function getStorageLocationDetail(id: string): Promise<ApiResponse<StorageLocation>> {
    try {
        const response = await httpClient(`${apiEndpoints.getStorageLocationDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取存放位置詳情失敗",
            data: undefined,
        };
    }
}

// 新增存放位置
export async function createStorageLocation(data: Partial<StorageLocation>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addStorageLocation, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增存放位置失敗",
        };
    }
}

// 更新存放位置
export async function updateStorageLocation(data: Partial<StorageLocation>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editStorageLocation, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新存放位置失敗",
        };
    }
}

// 刪除存放位置
export async function deleteStorageLocation(data: Partial<StorageLocation>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteStorageLocation, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除存放位置失敗",
        };
    }
}
