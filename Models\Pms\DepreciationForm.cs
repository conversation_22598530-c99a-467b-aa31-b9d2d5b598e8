using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 固定資產折舊單
    /// </summary>
    public class DepreciationForm : ModelBaseEntity
    {
        [Key]
        [Comment("固定資產折舊單編號")]
        [Column(TypeName = "nvarchar(100)")]
        public Guid DepreciationFormId { get; set; } // 固定資產折舊單編號

        [Comment("折舊紀錄編號")]
        [Column(TypeName = "nvarchar(100)")]
        [ForeignKey("DepreciationId")]
        public Guid DepreciationId { get; set; } // 折舊紀錄編號

        [Comment("折舊年度")]
        [Column(TypeName = "int")]
        public int DepreciationYear { get; set; } // 折舊年度

        [Comment("折舊月份")]
        [Column(TypeName = "int")]
        public int DepreciationMonth { get; set; } // 折舊月份

        [Comment("折舊日期")]
        [Column(TypeName = "bigint")]
        public long DepreciationDate { get; set; } // 折舊日期

        [Comment("備註")]
        [Column(TypeName = "nvarchar(500)")]
        public string Notes { get; set; } // 備註

        public DepreciationForm()
        {
            DepreciationFormId = Guid.NewGuid();
            DepreciationId = Guid.Empty;
            DepreciationDate = 0;
            DepreciationYear = 0;
            DepreciationMonth = 0;
            Notes = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class DepreciationFormDTO : ModelBaseEntityDTO
    {
        public string DepreciationFormId { get; set; } // 固定資產折舊單編號
        public string DepreciationId { get; set; } // 折舊紀錄編號
        public long DepreciationDate { get; set; } // 折舊日期
        public int DepreciationYear { get; set; } // 折舊年度
        public int DepreciationMonth { get; set; } // 折舊月份
        public string Notes { get; set; } // 備註
        public ICollection<DepreciationFormDetailDTO> DepreciationFormDetail { get; set; } //折舊紀錄資料

        public DepreciationFormDTO()
        {
            DepreciationFormId = "";
            DepreciationId = "";
            DepreciationDate = 0;
            DepreciationYear = 0;
            DepreciationMonth = 0;
            Notes = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
            DepreciationFormDetail = new List<DepreciationFormDetailDTO>();
        }
    }
}