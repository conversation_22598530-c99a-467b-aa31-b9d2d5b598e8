﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 公司圖片資料表
    /// </summary>
    public class EnterpriseImage : ModelBaseEntity
    {
        [Key]
        [Comment("圖片編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ImageId { get; set; }

        [Comment("圖片名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string ImageName { get; set; }

        [Comment("圖片類型")]
        [Column(TypeName = "nvarchar(50)")]
        public string ImageType { get; set; }

        [Required]
        [Comment("圖片路徑")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string ImagePath { get; set; }

        public EnterpriseImage()
        {
            ImageId = "";
            ImageName = "";
            ImageType = "";
            ImagePath = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class EnterpriseImageUploadDTO : ModelBaseEntityDTO
    {
        [Required]
        public string ImageType { get; set; }

        [Required]
        public string ImageName { get; set; }

        [Required]
        public IFormFile ImageFile { get; set; }

        [Required]
        public new string CreateUserId { get; set; }

        public EnterpriseImageUploadDTO()
        {
            ImageType = "";
            ImageName = "";
            ImageFile = null;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }


    public class EnterpriseImageDTO : ModelBaseEntityDTO
    {
        public string ImageId { get; set; }
        public string ImageType { get; set; }
        public string ImageName { get; set; }
        public string ImagePath { get; set; }

        public EnterpriseImageDTO()
        {
            ImageId = "";
            ImageType = "";
            ImageName = "";
            ImagePath = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteUserId = null;
            DeleteTime = null;
            IsDeleted = false;
        }
    }
}