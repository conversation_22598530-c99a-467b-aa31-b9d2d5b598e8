import { apiEndpoints } from "@/config/api";
import { httpClient } from "./http";
import { UserInfo } from "@/utils/userInfo";

// 登入
export async function login(account: string, password: string) {
    return httpClient(apiEndpoints.login, {
        method: "POST",
        body: JSON.stringify({ account, password }),
    });
}

// 獲取用戶資訊
export async function getMyInfo() {
    return httpClient<UserInfo[]>(apiEndpoints.getMyInfo);
}

// 獲取用戶選單列表
export async function getAllMeun() {
    return await httpClient<any[]>(apiEndpoints.getAllMenu)
} 