using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("資產科目管理")]
    public class AssetAccountController : ControllerBase
    {
        private readonly IAssetAccountService _assetAccountService;

        public AssetAccountController(IAssetAccountService assetAccountService)
        {
            _assetAccountService = assetAccountService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得資產科目列表", Description = "取得所有資產科目列表")]
        public async Task<IActionResult> GetAssetAccountList()
        {
            var accounts = await _assetAccountService.GetAllAsync();
            return Ok(accounts);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得資產科目明細", Description = "依ID取得資產科目明細")]
        public async Task<IActionResult> GetAssetAccountDetail(int id)
        {
            var account = await _assetAccountService.GetByIdAsync(id);
            if (account == null)
                return NotFound($"找不到編號為{id}的資產科目。");

            return Ok(account);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增資產科目", Description = "新增資產科目")]
        public async Task<IActionResult> AddAssetAccount([FromBody] AssetAccountDTO assetAccount)
        {
            var result = await _assetAccountService.AddAsync(assetAccount);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯資產科目", Description = "編輯資產科目")]
        public async Task<IActionResult> EditAssetAccount([FromBody] AssetAccountDTO assetAccount)
        {
            var result = await _assetAccountService.UpdateAsync(assetAccount);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除資產科目", Description = "刪除資產科目")]
        public async Task<IActionResult> DeleteAssetAccount([FromBody] AssetAccountDTO assetAccount)
        {
            var result = await _assetAccountService.DeleteAsync(assetAccount);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }
    }
}
