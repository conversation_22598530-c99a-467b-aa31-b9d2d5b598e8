using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Attributes
{
    /// <summary>
    /// 驗證台灣市內電話格式的屬性
    /// 基本使用（允許分機）
    /// [PhoneNumber]
    /// public string PhoneNumber { get; set; }
    /// 不允許分機
    /// [PhoneNumber(allowExtension: false)]
    /// public string PhoneNumber { get; set; }
    /// 
    /// 有效的電話號碼格式範例：
    /// 02-23456789
    /// (02)2345-6789
    /// 02-2345-6789#123
    /// 02-23456789轉123
    /// 037-123456
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class PhoneNumberAttribute : ValidationAttribute
    {
        private readonly bool _allowExtension;
        private readonly Dictionary<string, string> _areaCodes = new Dictionary<string, string>
        {
            // 北部地區
            {"02", "台北市、新北市、基隆市"},
            {"03", "桃園市、新竹縣市、宜蘭縣、花蓮縣"},
            // 中部地區
            {"037", "苗栗縣"},
            {"04", "台中市、彰化縣"},
            {"049", "南投縣"},
            // 南部地區
            {"05", "雲林縣、嘉義縣市"},
            {"06", "台南市"},
            {"07", "高雄市"},
            {"08", "屏東縣"},
            // 東部地區
            {"089", "台東縣"},
            {"082", "金門縣"},
            {"0836", "馬祖"},
            {"0826", "烏坵"},
            {"0827", "東沙"},
            {"0828", "南沙"}
        };

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="allowExtension">是否允許分機號碼，預設為true</param>
        public PhoneNumberAttribute(bool allowExtension = true)
            : base("市內電話格式不正確")
        {
            _allowExtension = allowExtension;
        }

        /// <summary>
        /// 驗證方法
        /// </summary>
        /// <param name="value">要驗證的值</param>
        /// <param name="validationContext">驗證上下文</param>
        /// <returns>驗證結果</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success; // 若要求必填，請搭配 [Required] 屬性
            }

            string phoneNumber = value.ToString();

            // 移除所有空格和特殊符號
            phoneNumber = Regex.Replace(phoneNumber, @"[\s\-\(\)]", "");

            // 分離分機號碼
            string[] parts = phoneNumber.Split('#', ',', '轉');
            string mainNumber = parts[0];
            string extension = parts.Length > 1 ? parts[1] : null;

            // 檢查是否有分機但不允許分機
            if (!_allowExtension && extension != null)
            {
                return new ValidationResult("此欄位不允許輸入分機號碼");
            }

            // 檢查分機格式
            if (extension != null)
            {
                if (!Regex.IsMatch(extension, @"^\d{1,10}$"))
                {
                    return new ValidationResult("分機號碼格式不正確，應為1-10位數字");
                }
            }

            // 檢查主要電話號碼格式
            if (!IsValidMainNumber(mainNumber))
            {
                return new ValidationResult($"電話號碼格式不正確，應為區碼+電話號碼，例如：02xxxxxxxx。可用的區碼包含：{string.Join("、", _areaCodes.Keys)}");
            }

            return ValidationResult.Success;
        }

        /// <summary>
        /// 驗證主要電話號碼
        /// </summary>
        /// <param name="mainNumber">主要電話號碼</param>
        /// <returns>是否有效</returns>
        private bool IsValidMainNumber(string mainNumber)
        {
            // 檢查是否以區碼開頭
            var areaCode = _areaCodes.Keys.FirstOrDefault(code => mainNumber.StartsWith(code));
            if (areaCode == null)
            {
                return false;
            }

            // 取得區碼後的電話號碼部分
            string number = mainNumber.Substring(areaCode.Length);

            // 檢查電話號碼長度（6-8位）
            if (number.Length < 6 || number.Length > 8)
            {
                return false;
            }

            // 檢查是否全為數字
            return Regex.IsMatch(number, @"^\d+$");
        }

        /// <summary>
        /// 取得指定區碼的地區名稱
        /// </summary>
        /// <param name="areaCode">區碼</param>
        /// <returns>地區名稱</returns>
        public string GetAreaName(string areaCode)
        {
            return _areaCodes.TryGetValue(areaCode, out string areaName) ? areaName : "未知地區";
        }
    }
}