﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 保費級距資料表
    /// </summary>
    public class InsuranceGrade : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("保險類型 (1=勞保, 2=健保, 3=職災)")]
        [Required]
        public int InsuranceType { get; set; }

        [Comment("月投保薪資")]
        [Required]
        public int MonthlySalary { get; set; }

        [Comment("生效日期")]
        public long? StartDate { get; set; }

        [Comment("結束日期")]
        public long? EndDate { get; set; }

        [Comment("備註")]
        [Column(TypeName = "nvarchar(500)")]
        public string? Remark { get; set; }

        public InsuranceGrade()
        {
            uid = "";
            InsuranceType = 1;
            MonthlySalary = 0;
            StartDate = 0;
            EndDate = null;
            Remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 保險級距 DTO
    /// </summary>
    public class InsuranceGradeDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; }

        /// <summary>
        /// 保險類型 (1=勞保, 2=健保, 3=職災)
        /// </summary>
        public int InsuranceType { get; set; }

        /// <summary>
        /// 月投保薪資
        /// </summary>
        public int MonthlySalary { get; set; }

        /// <summary>
        /// 生效日期（timestamp 字串格式）
        /// </summary>
        [Required]
        public string StartDate { get; set; }

        /// <summary>
        /// 結束日期（timestamp 字串格式，可為 null）
        /// </summary>
        public string? EndDate { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 目前生效的員工數量
        /// </summary>
        public int CurrentEmployeeCount { get; set; } = 0;

        /// <summary>
        /// 待生效的員工數量
        /// </summary>
        public int PendingEmployeeCount { get; set; } = 0;

        /// <summary>
        /// 總員工數量（包括已結束的）
        /// </summary>
        public int TotalEmployeeCount { get; set; } = 0;

        public InsuranceGradeDTO()
        {
            uid = "";
            InsuranceType = 0;
            MonthlySalary = 0;
            StartDate = "";
            EndDate = "";
            Remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 保險級距員工詳細資訊 DTO
    /// </summary>
    public class InsuranceGradeEmployeeDetailDTO
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        public string UserId { get; set; } = "";

        /// <summary>
        /// 員工姓名
        /// </summary>
        public string UserName { get; set; } = "";

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DepartmentName { get; set; } = "";

        /// <summary>
        /// 職稱
        /// </summary>
        public string PositionName { get; set; } = "";

        /// <summary>
        /// 級距生效日期
        /// </summary>
        public string StartDate { get; set; } = "";

        /// <summary>
        /// 級距結束日期
        /// </summary>
        public string? EndDate { get; set; } = null;

        /// <summary>
        /// 月投保薪資
        /// </summary>
        public int MonthlySalary { get; set; } = 0;

        /// <summary>
        /// 狀態描述
        /// </summary>
        public string StatusDescription { get; set; } = "";
    }
}

