﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.RegularExpressions;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace FAST_ERP_Backend.Models.Common
{
    public class NotificationDto
    {
        public string Type { get; set; }    // 例如: "msgUI", "friendUI" 等，用於前端不同元件監聽用flag
        public string Action { get; set; }  // 例如: "newmsg", "refresh", "request" 等，處理各元件動作
        public object? Data { get; set; }    // 額外的資料 (例如訊息內容、ID等)

        public NotificationDto()
        {
            Type = "";
            Action = "";
            Data = null;
        }
    }

}
