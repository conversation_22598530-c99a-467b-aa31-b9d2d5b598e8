﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    public class SystemParametersItemDTO
    {
        public string SystemParametersItemId { get; set; } //參數項目編號
        public string SystemParametersId { get; set; } //參數編號
        public string Name { get; set; } //參數項目名稱
        public string Value { get; set; } //參數項目值
        public int? SortOrder { get; set; } //排序編號

        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態

        public SystemParametersItemDTO()
        {
            SystemParametersItemId = "";
            SystemParametersId = "";
            Name = "";
            Value = "";
            SortOrder = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
        }
    }

    public class SystemParametersItem : ModelBaseEntity
    {
        [Key]
        [Comment("參數項目編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemParametersItemId { get; set; } //選單編號

        [Comment("參數編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemParametersId { get; set; } //選單編號

        [Comment("參數項目名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string Name { get; set; } //參數項目名稱

        [Comment("參數項目值")]
        [Column(TypeName = "nvarchar(50)")]
        public string Value { get; set; } //參數項目值

        [Comment("排序編號")]
        [Column(TypeName = "integer")]
        public int? SortOrder { get; set; } //排序編號

        public SystemParametersItem()
        {
            SystemParametersItemId = "";
            SystemParametersId = "";
            Name = "";
            Value = "";
            SortOrder = null;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}