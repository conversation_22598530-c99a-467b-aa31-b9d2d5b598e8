﻿using FAST_ERP_Backend.Models.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

public class TokenHandler
{
    private readonly IConfiguration _configuration;

    private readonly string _tokenKey;
    private readonly string _tokenIssuer;
    private readonly string _tokenAudience;
    private readonly int _tokenExpirationHours;

    public TokenHandler(IConfiguration configuration)
    {
        _tokenKey = configuration["AuthToken:TokenKey"] ?? throw new ArgumentNullException(nameof(_tokenKey), "TokenKey is missing from configuration");
        _tokenIssuer = configuration["AuthToken:TokenIssuer"] ?? throw new ArgumentNullException(nameof(_tokenIssuer), "TokenIssuer is missing from configuration");
        _tokenAudience = configuration["AuthToken:TokenAudience"] ?? throw new ArgumentNullException(nameof(_tokenAudience), "TokenAudience is missing from configuration");
        _tokenExpirationHours = int.Parse(configuration["AuthToken:TokenExpirationHours"] ?? "24");
    }

    // 生成 JWT Token
    public string GenerateJwtToken(UsersDTO user)
    {
        try
        {
            // 加載配置取得tokenKey
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_tokenKey);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                    new Claim(ClaimTypes.Name, user.Account),
                }),
                Expires = DateTime.UtcNow.AddHours(_tokenExpirationHours),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = _tokenIssuer,        // 設定 發行者
                Audience = _tokenAudience     // 設定 受眾
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);

            return tokenHandler.WriteToken(token);
        }
        catch
        {
            return "";
        }
    }

    // 解密並驗證 JWT Token
    public ClaimsPrincipal DecodeJwtToken(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_tokenKey);

            // 驗證參數設置
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true, // 驗證發行者
                ValidIssuer = _tokenIssuer, // 預期的發行者
                ValidateAudience = true, // 驗證受眾
                ValidAudience = _tokenAudience, // 預期的受眾
                ValidateLifetime = true, // 驗證 Token 是否過期
                ClockSkew = TimeSpan.Zero // 不允許時間偏差
            };

            // 驗證並解析 Token
            var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

            // 檢查 Token 類型
            if (!(validatedToken is JwtSecurityToken jwtToken) ||
                !jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                throw new SecurityTokenException("Invalid token");
            }

            return principal;
        }
        catch (Exception ex)
        {
            // 如果 Token 解密或驗證失敗，處理錯誤（如記錄或丟出異常）
            throw new SecurityTokenException("Token validation failed", ex);
        }

        //解密範例
        //var handler = new TokenHandler(configuration);
        //string token = "your_jwt_token_here";
        //var claimsPrincipal = handler.DecodeJwtToken(token);

        //if (claimsPrincipal != null)
        //{
        //var userId = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        //var userAccount = claimsPrincipal.FindFirst(ClaimTypes.Name)?.Value;
        //}
    }
}
