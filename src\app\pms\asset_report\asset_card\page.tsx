"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  Col,
  message,
  DatePicker,
  Typography,
  Badge,
  Tag,
  Empty,
  Checkbox,
  InputNumber,
  Radio,
  Divider,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PrinterOutlined,
  EyeOutlined,
  IdcardOutlined,
  QrcodeOutlined,
} from "@ant-design/icons";
import { Grid } from "antd";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import { QRCodeSVG } from "qrcode.react";
import Barcode from "react-barcode";
import { getAssets, AssetDetail, Asset } from "@/services/pms/assetService";
import { getAssetAccounts } from "@/services/pms/assetAccountService";
import { getAssetCategories } from "@/services/pms/assetCategoryService";
import { getDepartments } from "@/services/common/departmentService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import { getStorageLocations } from "@/services/pms/storageLocationService";
import { getUsers } from "@/services/common/userService";
import { siteConfig } from "@/config/site";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import ReportHeader, {
  getReportPrintStyles,
} from "@/app/components/common/ReportHeader";

// 擴展 dayjs 以支援 isBetween
dayjs.extend(isBetween);

const { Option } = Select;
const { RangePicker } = DatePicker;
const { useBreakpoint } = Grid;
const { Title, Text } = Typography;

// 查詢參數介面
interface AssetCardQuery {
  keyword?: string;
  assetAccountId?: string;
  assetStatusId?: string;
  departmentId?: string;
  assetCategoryId?: string;
  storageLocationId?: string;
  acquisitionDateRange?: [string, string];
}

// 卡片設定介面
interface CardSettings {
  showQRCode: boolean;
  showBarcode: boolean;
  showSpecifications: boolean;
  showFinancialInfo: boolean;
  showMaintenanceInfo: boolean;
  cardsPerRow: number;
  cardOrientation: "portrait" | "landscape";
  cardSize: "small" | "medium" | "large";
  includeHeader: boolean;
  includeFooter: boolean;
}

// 主組件
const AssetCardPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<Asset[]>([]);
  const [filteredData, setFilteredData] = useState<Asset[]>([]);
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);

  // 表單和選項
  const [searchForm] = Form.useForm();
  const [assetAccounts, setAssetAccounts] = useState<any[]>([]);
  const [assetCategories, setAssetCategories] = useState<any[]>([]);
  const [assetStatuses, setAssetStatuses] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [storageLocations, setStorageLocations] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);

  // 列印相關
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrintMode, setIsPrintMode] = useState(false);
  const [cardSettings, setCardSettings] = useState<CardSettings>({
    showQRCode: true,
    showBarcode: false,
    showSpecifications: true,
    showFinancialInfo: true,
    showMaintenanceInfo: false,
    cardsPerRow: 2,
    cardOrientation: "portrait",
    cardSize: "medium",
    includeHeader: true,
    includeFooter: true,
  });

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 載入資產數據
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await getAssets();
      if (result.success && Array.isArray(result.data)) {
        // 從 AssetDetail 中提取 Asset
        const assets = result.data.map((item: AssetDetail) => item.asset);
        setData(assets);
        setFilteredData(assets);
      } else {
        message.error("載入資產數據失敗");
        setData([]);
        setFilteredData([]);
      }
    } catch (error) {
      console.error("載入資產數據錯誤:", error);
      message.error("載入資產數據失敗");
      setData([]);
      setFilteredData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 載入選項數據
  const loadOptions = useCallback(async () => {
    try {
      const [
        accountsResult,
        categoriesResult,
        statusesResult,
        departmentsResult,
        locationsResult,
        usersResult,
      ] = await Promise.all([
        getAssetAccounts(),
        getAssetCategories(),
        getAssetStatuses(),
        getDepartments(),
        getStorageLocations(),
        getUsers(),
      ]);

      if (accountsResult.success) setAssetAccounts(accountsResult.data || []);
      if (categoriesResult.success)
        setAssetCategories(categoriesResult.data || []);
      if (statusesResult.success) setAssetStatuses(statusesResult.data || []);
      if (departmentsResult.success)
        setDepartments(departmentsResult.data || []);
      if (locationsResult.success)
        setStorageLocations(locationsResult.data || []);
      if (usersResult.success) setUsers(usersResult.data || []);
    } catch (error) {
      console.error("載入選項數據錯誤:", error);
      message.error("載入選項數據失敗");
    }
  }, []);

  // =========================== 事件處理 ===========================

  // 搜尋處理 - 使用客戶端過濾
  const handleSearch = () => {
    const values = searchForm.getFieldsValue();
    let filtered = [...data];

    // 關鍵字搜尋
    if (values.keyword?.trim()) {
      const keyword = values.keyword.trim().toLowerCase();
      filtered = filtered.filter(
        (asset) =>
          (asset.assetNo || "").toLowerCase().includes(keyword) ||
          (asset.assetName || "").toLowerCase().includes(keyword) ||
          (asset.specification || "").toLowerCase().includes(keyword)
      );
    }

    // 其他過濾條件
    if (values.assetAccountId) {
      filtered = filtered.filter(
        (asset) => asset.assetAccountId === values.assetAccountId
      );
    }
    if (values.assetStatusId) {
      filtered = filtered.filter(
        (asset) => asset.assetStatusId === values.assetStatusId
      );
    }
    if (values.departmentId) {
      filtered = filtered.filter(
        (asset) => asset.departmentId === values.departmentId
      );
    }
    if (values.assetCategoryId) {
      filtered = filtered.filter(
        (asset) => asset.assetCategoryId === values.assetCategoryId
      );
    }
    if (values.storageLocationId) {
      filtered = filtered.filter(
        (asset) => asset.storageLocationId === values.storageLocationId
      );
    }

    // 取得日期範圍過濾
    if (
      values.acquisitionDateRange &&
      values.acquisitionDateRange.length === 2
    ) {
      const [startDate, endDate] = values.acquisitionDateRange;
      filtered = filtered.filter((asset) => {
        if (!asset.acquisitionDate) return false;
        const assetDate = dayjs(asset.acquisitionDate);
        return assetDate.isBetween(startDate, endDate, "day", "[]");
      });
    }

    setFilteredData(filtered);
    // 清空選擇的資產，因為過濾後可能不存在
    setSelectedAssets([]);
    message.success(`找到 ${filtered.length} 筆符合條件的資料`);
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    setFilteredData(data);
    setSelectedAssets([]);
  };

  // 列印處理
  const handlePrint = () => {
    if (selectedAssets.length === 0) {
      message.warning("請先選擇要列印的財產");
      return;
    }

    // 動態添加列印樣式
    const printStyles = getReportPrintStyles("財產卡片");

    const printWindow = window.open("", "_blank");
    if (printWindow) {
      const printContent = printRef.current?.innerHTML;
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>財產卡片</title>
            <meta charset="utf-8">
            <style>
              ${printStyles}
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .card { 
                border: 2px solid #000; 
                margin: 10px; 
                padding: 15px; 
                display: inline-block;
                box-sizing: border-box;
                page-break-inside: avoid;
                vertical-align: top;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              }
              .card-header { 
                text-align: center; 
                margin-bottom: 15px; 
                padding-bottom: 10px;
                border-bottom: 2px solid #1890ff;
              }
              .card-title { 
                font-size: 18px; 
                font-weight: bold; 
                color: #1890ff;
                margin: 0;
              }
              .card-subtitle { 
                font-size: 12px; 
                color: #666;
                margin: 5px 0 0 0;
              }
              .card-body { 
                display: flex; 
                gap: 15px;
                overflow: hidden;
              }
              .card-left { 
                flex: 1;
                overflow: hidden;
                padding-right: 5px;
              }
              .card-right { 
                width: 100px; 
                text-align: center;
                border-left: 1px solid #eee;
                padding-left: 10px;
                flex-shrink: 0;
              }
              .card-section { 
                margin-bottom: 15px;
                overflow: hidden;
              }
              .section-title { 
                font-weight: bold; 
                font-size: 14px;
                color: #1890ff;
                margin-bottom: 8px;
                padding-bottom: 3px;
                border-bottom: 1px solid #eee;
              }
              .field-row { 
                display: flex; 
                margin: 8px 0;
                align-items: flex-start;
                min-height: 18px;
              }
              .field-label { 
                font-weight: bold; 
                min-width: 80px;
                font-size: 12px;
                color: #333;
              }
              .field-value { 
                flex: 1;
                font-size: 12px;
                word-break: break-word;
                line-height: 1.4;
                overflow-wrap: break-word;
                max-width: 100%;
              }
              .qr-code svg { 
                width: 80px !important; 
                height: 80px !important; 
              }
              .status-tag {
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: bold;
              }
              .card-footer {
                margin-top: 15px;
                padding-top: 10px;
                border-top: 1px solid #eee;
                text-align: center;
                font-size: 10px;
                color: #999;
              }
              @media print {
                body { margin: 0; }
                .card { page-break-inside: avoid; }
              }
            </style>
          </head>
          <body>
            ${printContent}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // 選擇資產
  const handleSelectAsset = (asset: Asset, checked: boolean) => {
    if (checked) {
      setSelectedAssets([...selectedAssets, asset]);
    } else {
      setSelectedAssets(
        selectedAssets.filter((item) => item.assetId !== asset.assetId)
      );
    }
  };

  // 全選/取消全選
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAssets([...filteredData]);
    } else {
      setSelectedAssets([]);
    }
  };

  // =========================== 表格定義 ===========================

  const columns = [
    {
      title: (
        <Checkbox
          checked={
            selectedAssets.length === filteredData.length &&
            filteredData.length > 0
          }
          indeterminate={
            selectedAssets.length > 0 &&
            selectedAssets.length < filteredData.length
          }
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          全選
        </Checkbox>
      ),
      dataIndex: "select",
      key: "select",
      width: 80,
      render: (_: any, record: Asset) => {
        const isSelected = selectedAssets.some(
          (item) => item.assetId === record.assetId
        );
        return (
          <Checkbox
            checked={isSelected}
            onChange={(e) => handleSelectAsset(record, e.target.checked)}
          />
        );
      },
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
      width: 120,
      render: (text: string) => text || "-",
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
      width: 200,
      render: (text: string) => text || "-",
    },
    {
      title: "規格型號",
      dataIndex: "specification",
      key: "specification",
      width: 150,
      render: (text: string) => text || "-",
    },
    {
      title: "所屬部門",
      dataIndex: "departmentId",
      key: "departmentId",
      width: 120,
      render: (departmentId: string) => {
        const department = departments.find(
          (d) => d.departmentId === departmentId
        );
        return department?.name || "-";
      },
    },
    {
      title: "存放地點",
      dataIndex: "storageLocationId",
      key: "storageLocationId",
      width: 120,
      render: (locationId: string) => {
        const location = storageLocations.find(
          (l) => l.storageLocationId === locationId
        );
        return location?.name || "-";
      },
    },
    {
      title: "使用狀態",
      dataIndex: "assetStatusId",
      key: "assetStatusId",
      width: 100,
      render: (statusId: string) => {
        const status = assetStatuses.find((s) => s.assetStatusId === statusId);
        const color =
          STATUS_COLORS[status?.name as keyof typeof STATUS_COLORS] ||
          "default";
        return status ? <Tag color={color}>{status.name}</Tag> : "-";
      },
    },
    {
      title: "取得日期",
      dataIndex: "acquisitionDate",
      key: "acquisitionDate",
      width: 100,
      render: (date: number) => {
        return date ? dayjs(date).format("YYYY/MM/DD") : "-";
      },
    },
  ];

  // =========================== 生命週期 ===========================

  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([loadOptions(), loadData()]);
    };

    initializeData();
  }, [loadData, loadOptions]);

  // =========================== 渲染卡片 ===========================

  const getCardSize = () => {
    switch (cardSettings.cardSize) {
      case "small":
        return { width: "320px", minHeight: "220px" };
      case "medium":
        return { width: "420px", minHeight: "300px" };
      case "large":
        return { width: "520px", minHeight: "380px" };
      default:
        return { width: "420px", minHeight: "300px" };
    }
  };

  const renderCards = () => {
    const cardSize = getCardSize();

    return selectedAssets.map((asset, index) => {
      const department = departments.find(
        (d) => d.departmentId === asset.departmentId
      );
      const location = storageLocations.find(
        (l) => l.storageLocationId === asset.storageLocationId
      );
      const custodian = users.find((u) => u.userId === asset.custodianId);
      const assetAccount = assetAccounts.find(
        (a) => a.assetAccountId === asset.assetAccountId
      );
      const assetCategory = assetCategories.find(
        (c) => c.assetCategoryId === asset.assetCategoryId
      );
      const assetStatus = assetStatuses.find(
        (s) => s.assetStatusId === asset.assetStatusId
      );

      // 生成 QR Code 的內容
      const qrCodeData = JSON.stringify({
        assetNo: asset.assetNo,
        assetName: asset.assetName,
        specification: asset.specification,
        department: department?.name,
        location: location?.name,
        custodian: custodian?.name,
        acquisitionDate: asset.acquisitionDate
          ? dayjs(asset.acquisitionDate).format("YYYY/MM/DD")
          : null,
        purchaseAmount: asset.purchaseAmount,
      });

      return (
        <div
          key={asset.assetId || index}
          className="card"
          style={{
            ...cardSize,
            margin: "10px",
            display: "inline-block",
            verticalAlign: "top",
          }}
        >
          {/* 卡片標題 */}
          {cardSettings.includeHeader && (
            <div className="card-header">
              <h3 className="card-title">
                <IdcardOutlined style={{ marginRight: "8px" }} />
                {siteConfig.copyright} 財產資料卡
              </h3>
            </div>
          )}

          {/* 卡片主體 */}
          <div className="card-body">
            <div className="card-left">
              <div className="card-section">
                <div className="field-row">
                  <span className="field-label">財產編號:</span>
                  <span className="field-value">
                    <strong>{asset.assetNo || "-"}</strong>
                  </span>
                </div>
                <div className="field-row">
                  <span className="field-label">財產名稱:</span>
                  <span className="field-value">
                    <strong>{asset.assetName || "-"}</strong>
                  </span>
                </div>
                <div className="field-row">
                  <span className="field-label">取得日期:</span>
                  <span className="field-value">
                    {asset.acquisitionDate
                      ? dayjs(asset.acquisitionDate).format("YYYY/MM/DD")
                      : "-"}
                  </span>
                </div>
                <div className="field-row">
                  <span className="field-label">財產科目:</span>
                  <span className="field-value">
                    {assetAccount?.assetAccountName || "-"}
                  </span>
                </div>
                <div className="field-row">
                  <span className="field-label">原始價值:</span>
                  <span className="field-value">
                    {asset.purchaseAmount
                      ? `NT$ ${asset.purchaseAmount.toLocaleString()}`
                      : "-"}
                  </span>
                </div>
                {cardSettings.showSpecifications && (
                  <div className="field-row">
                    <span className="field-label">規格型號:</span>
                    <span className="field-value">
                      {asset.specification || "-"}
                    </span>
                  </div>
                )}
                <div className="field-row">
                  <span className="field-label">財產分類:</span>
                  <span className="field-value">
                    {assetCategory?.assetCategoryName || "-"}
                  </span>
                </div>
                <div className="field-row">
                  <span className="field-label">使用狀態:</span>
                  <span className="field-value">
                    {assetStatus ? (
                      <span
                        className="status-tag"
                        style={{
                          backgroundColor:
                            STATUS_COLORS[
                              assetStatus.name as keyof typeof STATUS_COLORS
                            ] || "#f0f0f0",
                          color: "#fff",
                        }}
                      >
                        {assetStatus.name}
                      </span>
                    ) : (
                      "-"
                    )}
                  </span>
                </div>
              </div>

              <div className="card-section">
                <div className="field-row">
                  <span className="field-label">所屬部門:</span>
                  <span className="field-value">{department?.name || "-"}</span>
                </div>
                <div className="field-row">
                  <span className="field-label">存放地點:</span>
                  <span className="field-value">{location?.name || "-"}</span>
                </div>
                <div className="field-row">
                  <span className="field-label">保管人:</span>
                  <span className="field-value">{custodian?.name || "-"}</span>
                </div>
              </div>
            </div>

            {/* 卡片右側 - QR Code / 條碼 */}
            <div className="card-right">
              {cardSettings.showQRCode && (
                <div style={{ marginBottom: "10px" }}>
                  <div style={{ textAlign: "center" }}>
                    <QRCodeSVG value={qrCodeData} size={80} level="M" />
                    <div
                      style={{
                        fontSize: "10px",
                        marginTop: "5px",
                        color: "#666",
                      }}
                    >
                      {asset.assetName || "-"}
                      <br />
                      {asset.assetNo || "-"}
                    </div>
                  </div>
                </div>
              )}

              {cardSettings.showBarcode && (
                <div style={{ marginBottom: "10px" }}>
                  <div style={{ textAlign: "center" }}>
                    <Barcode
                      value={asset.assetNo || ""}
                      width={1}
                      height={30}
                      fontSize={8}
                      margin={0}
                      displayValue={false}
                    />
                    <div
                      style={{
                        fontSize: "10px",
                        marginTop: "5px",
                        color: "#666",
                      }}
                    >
                      {asset.assetName || "-"}
                      <br />
                      {asset.assetNo || "-"}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 卡片頁尾 */}
          {cardSettings.includeFooter && (
            <div className="card-footer">
              列印日期: {dayjs().format("YYYY/MM/DD HH:mm")}
            </div>
          )}
        </div>
      );
    });
  };

  // =========================== 主要渲染 ===========================

  return (
    <div style={{ padding: "20px" }}>
      <Card title="財產卡片">
        {/* 搜尋表單 */}
        <Card title="搜尋條件" style={{ marginBottom: "24px" }}>
          <Form form={searchForm} layout="vertical">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="keyword" label="關鍵字">
                  <Input placeholder="財產編號、財產名稱、規格等" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetAccountId" label="財產科目">
                  <Select placeholder="請選擇" allowClear>
                    {assetAccounts.map((item: any) => (
                      <Option
                        key={item.assetAccountId}
                        value={item.assetAccountId}
                      >
                        {item.assetAccountName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetStatusId" label="使用狀態">
                  <Select placeholder="請選擇" allowClear>
                    {assetStatuses.map((item: any) => (
                      <Option
                        key={item.assetStatusId}
                        value={item.assetStatusId}
                      >
                        <Tag
                          color={STATUS_COLORS[item.name] || "default"}
                          style={{ marginRight: 8 }}
                        >
                          {item.name}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="departmentId" label="所屬部門">
                  <Select placeholder="請選擇" allowClear>
                    {departments.map((item: any) => (
                      <Option key={item.departmentId} value={item.departmentId}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetCategoryId" label="財產分類">
                  <Select placeholder="請選擇" allowClear>
                    {assetCategories.map((item: any) => (
                      <Option
                        key={item.assetCategoryId}
                        value={item.assetCategoryId}
                      >
                        {item.assetCategoryName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="storageLocationId" label="存放地點">
                  <Select placeholder="請選擇" allowClear>
                    {storageLocations.map((item: any) => (
                      <Option
                        key={item.storageLocationId}
                        value={item.storageLocationId}
                      >
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="acquisitionDateRange" label="取得日期範圍">
                  <RangePicker style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                  >
                    搜尋
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
                    重置
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 卡片設定 */}
        <Card title="卡片設定" style={{ marginBottom: "24px" }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Space direction="vertical">
                <Text strong>顯示選項</Text>
                <Checkbox
                  checked={cardSettings.showQRCode}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      showQRCode: e.target.checked,
                    })
                  }
                >
                  顯示 QR Code
                </Checkbox>
                <Checkbox
                  checked={cardSettings.showBarcode}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      showBarcode: e.target.checked,
                    })
                  }
                >
                  顯示條碼
                </Checkbox>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Space direction="vertical">
                <Text strong>資訊選項</Text>
                <Checkbox
                  checked={cardSettings.showSpecifications}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      showSpecifications: e.target.checked,
                    })
                  }
                >
                  顯示規格型號
                </Checkbox>
                <Checkbox
                  checked={cardSettings.showFinancialInfo}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      showFinancialInfo: e.target.checked,
                    })
                  }
                >
                  顯示財務資訊
                </Checkbox>
                <Checkbox
                  checked={cardSettings.showMaintenanceInfo}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      showMaintenanceInfo: e.target.checked,
                    })
                  }
                >
                  顯示維護資訊
                </Checkbox>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <div>
                <Text strong>卡片尺寸:</Text>
                <Radio.Group
                  value={cardSettings.cardSize}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      cardSize: e.target.value,
                    })
                  }
                  style={{ marginTop: "8px" }}
                >
                  <Radio value="small">小</Radio>
                  <Radio value="medium">中</Radio>
                  <Radio value="large">大</Radio>
                </Radio.Group>
              </div>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <div>
                <Text strong>每行卡片數:</Text>
                <InputNumber
                  min={1}
                  max={4}
                  value={cardSettings.cardsPerRow}
                  onChange={(value) =>
                    setCardSettings({
                      ...cardSettings,
                      cardsPerRow: value || 2,
                    })
                  }
                  style={{ width: "100%", marginTop: "8px" }}
                />
              </div>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Space direction="vertical">
                <Text strong>其他選項</Text>
                <Checkbox
                  checked={cardSettings.includeHeader}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      includeHeader: e.target.checked,
                    })
                  }
                >
                  包含標題
                </Checkbox>
                <Checkbox
                  checked={cardSettings.includeFooter}
                  onChange={(e) =>
                    setCardSettings({
                      ...cardSettings,
                      includeFooter: e.target.checked,
                    })
                  }
                >
                  包含頁尾
                </Checkbox>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 操作區域 */}
        <Card style={{ marginBottom: "24px" }}>
          <Row>
            <Col>
              <Space>
                <Button
                  icon={<EyeOutlined />}
                  onClick={() => setIsPrintMode(!isPrintMode)}
                >
                  {isPrintMode ? "返回列表" : "預覽卡片"}
                </Button>
                {isPrintMode && (
                  <Button
                    type="primary"
                    icon={<PrinterOutlined />}
                    onClick={handlePrint}
                    disabled={selectedAssets.length === 0}
                  >
                    列印卡片
                  </Button>
                )}
                <Badge count={selectedAssets.length} showZero>
                  <Button size="small">已選擇</Button>
                </Badge>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 列印/預覽區域 */}
        <div ref={printRef}>
          {isPrintMode ? (
            // 卡片預覽模式
            <div style={{ background: "white", padding: "20px" }}>
              <ReportHeader reportTitle=" 財產資料卡" isPrintMode={true} />

              {selectedAssets.length > 0 ? (
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: `repeat(${cardSettings.cardsPerRow}, 1fr)`,
                    gap: "20px",
                    padding: "20px",
                    background: "#fff",
                  }}
                >
                  {renderCards()}
                </div>
              ) : (
                <Empty
                  description="請先選擇要列印卡片的資產"
                  style={{ margin: "40px 0" }}
                />
              )}
            </div>
          ) : (
            // 列表模式
            <Card title={`財產列表 (${filteredData.length} 筆資料)`}>
              <Table
                columns={columns}
                dataSource={filteredData}
                rowKey={(record) => record.assetId}
                loading={loading}
                scroll={{ x: 800 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span style={{ color: "#999" }}>
                          查無符合條件的資料
                          <br />
                          請調整搜尋條件後重新查詢
                        </span>
                      }
                    />
                  ),
                }}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                  pageSizeOptions: ["10", "20", "50", "100"],
                }}
              />
            </Card>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AssetCardPage;
