﻿using System.Security.Claims;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;

namespace FAST_ERP_Backend.Middlewares
{
    public class MenuPermissions
    {
        private readonly RequestDelegate _next;
        private readonly IServiceScopeFactory _scopeFactory;

        public MenuPermissions(RequestDelegate next, IServiceScopeFactory scopeFactory)
        {
            _next = next;
            _scopeFactory = scopeFactory;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            //不驗證選單權限路徑區塊
            if (ShouldValidatePassword(context))
            {
                await _next(context);
                return;
            }
            ClaimsPrincipal LoginUser = context.User;
            var LoginAccount = LoginUser.FindFirst(ClaimTypes.Name).Value;
            var LoginId = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            //不驗證選單權限帳號區塊
            if (LoginAccount.Equals("FastAdmin11111"))
            {
                await _next(context);
                return;
            }
            try {
                // 建立 Scoped 範圍
                using (var scope = _scopeFactory.CreateScope())
                {
                    var systemMenuService = scope.ServiceProvider.GetRequiredService<ISystemMenuService>();
                    //取得Request路徑
                    var MenuName = context.Request.Path.Value.Split('/').Skip(2).FirstOrDefault();

                    bool result = await systemMenuService.VerifyMenuAsync(MenuName,LoginId);
                    if (result) {
                        await _next(context);
                        return;
                    }
                    else
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("沒有該選單權限");
                        return;
                    }
                }
            }
            catch (Exception)
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("路徑有誤,請聯繫工程人員");
                return;
            }
        }
        //驗證路徑
        private bool ShouldValidatePassword(HttpContext context)
        {
            // 依需求新增其他路徑
            var protectedPaths = new[] {
                "/api/Login"
            };
            return protectedPaths.Any(path =>
                context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase));
        }
    }
}
