// PerformancePointRecord 點數紀錄管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "@/services/http";
import { ApiResponse } from "@/config/api";

// 點數紀錄資料型別
export interface PerformancePointRecord {
    uid: string;
    userId: string;
    pointDate: string; // timestamp 格式字串
    point: string;

    pointUid: string;
    pointName: string;

    groupUid: string;
    groupName: string;

    remark: string;
}

// 建立空的點數紀錄資料
export const createEmptyPerformancePointRecord = (): PerformancePointRecord => ({
    uid: "",
    userId: "",
    pointDate: "",
    point: "0",

    pointUid: "",
    pointName: "",

    groupUid: "",
    groupName: "",
    remark: "",
});

// 取得指定使用者的點數紀錄清單
export async function getPerformancePointRecordList(userId: string): Promise<ApiResponse<PerformancePointRecord[]>> {
    return await httpClient(`${apiEndpoints.getPerformancePointRecordList}/${userId}`, {
        method: "GET",
    });
}

// 取得單筆點數紀錄明細
export async function getPerformancePointRecordDetail(uid: string): Promise<ApiResponse<PerformancePointRecord>> {
    return await httpClient(`${apiEndpoints.getPerformancePointRecordDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增點數紀錄
export async function addPerformancePointRecord(data: Partial<PerformancePointRecord>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addPerformancePointRecord, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增點數紀錄失敗",
        };
    }
}

// 編輯點數紀錄
export async function editPerformancePointRecord(data: Partial<PerformancePointRecord>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editPerformancePointRecord, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯點數紀錄失敗",
        };
    }
}

// 刪除點數紀錄
export async function deletePerformancePointRecord(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deletePerformancePointRecord, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除點數紀錄失敗",
        };
    }
}

// 取得績效點數Summary
export interface PerformancePointSummaryItem {
    userId: string;
    userName: string;
    totalPoint: number;
}

// 根據群組與日期區間，取得點數加總排名
export async function getPerformancePointSummary(
    groupUid: string,
    startDate: string,
    endDate: string
): Promise<ApiResponse<PerformancePointSummaryItem[]>> {
    try {
        const response = await httpClient(apiEndpoints.getPerformancePointSummary, {
            method: "POST",
            body: JSON.stringify({ groupUid, startDate, endDate }),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得點數排名失敗",
            data: [],
        };
    }
}
