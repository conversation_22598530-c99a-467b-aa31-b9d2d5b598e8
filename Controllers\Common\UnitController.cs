using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("單位資料管理")]
    public class UnitController : ControllerBase
    {
        private readonly IUnitService _Interface;

        public UnitController(IUnitService unitService)
        {
            _Interface = unitService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得單位列表", Description = "取得所有單位列表")]
        public async Task<IActionResult> GetUnitList()
        {
            var units = await _Interface.GetUnitAsync();
            return Ok(units);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得單位明細", Description = "依ID取得單位明細")]
        public async Task<IActionResult> GetUnitDetail(string id)
        {
            var unit = await _Interface.GetUnitDetailAsync(id);
            return Ok(unit);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增單位", Description = "新增單位資料")]
        public async Task<IActionResult> AddUnit([FromBody] UnitDTO _data)
        {
            var (result, msg) = await _Interface.AddUnitAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯單位", Description = "修改已存在之單位資料")]
        public async Task<IActionResult> EditUnit([FromBody] UnitDTO _data)
        {
            var (result, msg) = await _Interface.EditUnitAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除單位", Description = "刪除已存在之單位資料")]
        public async Task<IActionResult> DeleteUnit([FromBody] UnitDTO _data)
        {
            var (result, msg) = await _Interface.DeleteUnitAsync(_data);
            return Ok(new { result, msg });
        }
    }
}