"use client";

import React, { useState } from "react";
import { Button, Card, Alert, Progress, Steps, Space } from "antd";
import {
  UploadOutlined,
  FileTextOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { FileUpload } from "./components/FileUpload";
import { DataPreview } from "./components/DataPreview";
import { ImportResults } from "./components/ImportResults";
import {
  validateExcelFile,
  batchImport,
  downloadBatchTemplate,
} from "@/services/pms/assetService";

export interface AssetBatchData {
  財產流水號: string;
  財產編號: string;
  財產名稱: string;
  財產簡稱: string;
  取得日期: string;
  數量: number;
  單位編號: string;
  購入金額: number;
  折舊金額: number;
  累計折舊金額: number;
  輔助金額: number;
  預估殘值: number;
  部門: string;
  股別: string;
  保管人: string;
  使用人: string;
  財產狀態: string;
  狀態異動日期: string;
  使用狀態: string;
  備註: string;
  存放地點: string;
  耐用年限: number;
  保固年限: number;
  廠牌型號: string;
  建物地址: string;
  建物構造: string;
  興建日期: string;
  建物面積: number;
  公設: number;
  使用執照日期: string;
  使用執照號碼: string;
  適用房屋稅目: string;
  公告現值: number;
  地目: string;
  地段: string;
  地號: string;
  土地面積: number;
  財產科目: string;
  財產子目: string;
  財產類別編號: string;
  權狀號碼: string;
  自訂財產編號一: string;
  自訂財產編號二: string;
  報廢原因: string;
  報廢日期: string;
  本年度預備報廢: string;
  報廢後堪用: string;
  設備類型: string;
  新增時間: string;
  新增者編號: string;
  更新時間: string;
  更新者編號: string;
  刪除時間: string;
  刪除者編號: string;
  刪除狀態: string;
}

type ImportStep = "upload" | "preview" | "importing" | "completed";

export default function AssetBatchImportPage() {
  const [currentStep, setCurrentStep] = useState<ImportStep>("upload");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewData, setPreviewData] = useState<AssetBatchData[]>([]);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  // 下載範本檔案
  const handleDownloadTemplate = async () => {
    try {
      setIsLoading(true);

      // 獲取 Blob 數據
      const blob = await downloadBatchTemplate();

      // 創建下載連結
      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "財產批次匯入範本.xlsx";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error("無法獲取範本檔案");
      }
    } catch (error: any) {
      console.error("下載範本錯誤:", error);
      setError(error.message || "下載範本失敗");
    } finally {
      setIsLoading(false);
    }
  };

  // 檔案上傳後驗證
  const handleFileUpload = async (file: File) => {
    try {
      setIsLoading(true);
      setError("");
      setSelectedFile(file);

      // 驗證檔案格式
      const result = await validateExcelFile(file);

      // 檢查API調用是否成功
      if (result.success) {
        // 檢查是否有嵌套的數據結構
        const validationData = result.data.data || result.data;

        // 檢查驗證結果
        if (validationData.isValid) {
          // 設定驗證結果
          setValidationResult({
            isValid: true,
            totalRows: validationData.totalRows || 0,
            validRows: validationData.validRows || 0,
            errorRows: validationData.errorRows || 0,
            errors: [],
            validCount: validationData.validRows || 0,
            invalidCount: validationData.errorRows || 0,
            warningCount: 0,
            details: [],
          });

          // 處理Excel資料，轉換為預覽格式
          let previewDataResult = [];
          if (
            validationData.excelData &&
            Array.isArray(validationData.excelData)
          ) {
            previewDataResult = validationData.excelData.map((item: any) => {
              // 轉換columnValues格式
              const rowData = item.columnValues;
              return {
                財產流水號: rowData["財產流水號"] || "",
                財產編號: rowData["財產編號"] || "",
                財產名稱: rowData["財產名稱"] || "",
                財產簡稱: rowData["財產簡稱"] || "",
                取得日期: rowData["取得日期"] || "",
                數量: parseInt(rowData["數量"]) || 0,
                單位編號: rowData["單位編號"] || "",
                購入金額: parseInt(rowData["購入金額"]) || 0,
                折舊金額: parseInt(rowData["折舊金額"]) || 0,
                累計折舊金額: parseInt(rowData["累計折舊金額"]) || 0,
                輔助金額: parseInt(rowData["輔助金額"]) || 0,
                預估殘值: parseInt(rowData["預估殘值"]) || 0,
                部門: rowData["部門"] || "",
                股別: rowData["股別"] || "",
                保管人: rowData["保管人"] || "",
                使用人: rowData["使用人"] || "",
                財產狀態: rowData["財產狀態"] || "",
                狀態異動日期: rowData["狀態異動日期"] || "",
                使用狀態: rowData["使用狀態"] || "",
                備註: rowData["備註"] || "",
                存放地點: rowData["存放地點"] || "",
                耐用年限: parseInt(rowData["耐用年限"]) || 0,
                保固年限: parseInt(rowData["保固年限"]) || 0,
                廠牌型號: rowData["廠牌型號"] || "",
                建物地址: rowData["建物地址"] || "",
                建物構造: rowData["建物構造"] || "",
                興建日期: rowData["興建日期"] || "",
                建物面積: parseFloat(rowData["面積(m²)"]) || 0,
                公設: parseFloat(rowData["公設(m²)"]) || 0,
                使用執照日期: rowData["使用執照日期"] || "",
                使用執照號碼: rowData["使用執照號碼"] || "",
                適用房屋稅目: rowData["適用房屋稅目"] || "",
                公告現值: parseInt(rowData["公告現值"]) || 0,
                地目: rowData["地目"] || "",
                地段: rowData["地段"] || "",
                地號: rowData["地號"] || "",
                土地面積: parseFloat(rowData["面積(m²)"]) || 0, // 注意：這裡可能需要確認是否有專門的土地面積欄位
                財產科目: rowData["財產科目"] || "",
                財產子目: rowData["財產子目"] || "",
                財產類別編號: rowData["財產類別編號"] || "",
                權狀號碼: rowData["權狀號碼"] || "",
                自訂財產編號一: rowData["自訂財產編號一"] || "",
                自訂財產編號二: rowData["自訂財產編號二"] || "",
                報廢原因: rowData["報廢原因"] || "",
                報廢日期: rowData["報廢日期"] || "",
                本年度預備報廢: rowData["本年度預備報廢"] || "",
                報廢後堪用: rowData["報廢後堪用"] || "",
                設備類型: rowData["設備類型"] || "",
                新增時間: "",
                新增者編號: "",
                更新時間: "",
                更新者編號: "",
                刪除時間: "",
                刪除者編號: "",
                刪除狀態: "",
              };
            });
          }

          setPreviewData(previewDataResult);
          setCurrentStep("preview");
        } else {
          // 設定驗證結果，包含錯誤訊息
          setValidationResult({
            isValid: false,
            totalRows: validationData.totalRows || 0,
            validRows: validationData.validRows || 0,
            errorRows: validationData.errorRows || 0,
            errors: validationData.errors || [],
            validCount: validationData.validRows || 0,
            invalidCount: validationData.errorRows || 0,
            warningCount: 0,
            details:
              validationData.errors?.map((error: any) => ({
                row: error.rowIndex,
                field: error.columnName,
                message: error.errorMessage,
                value: error.cellValue,
                type: "error",
              })) || [],
          });

          // 驗證失敗時不提供預覽資料
          setPreviewData([]);
          setCurrentStep("preview");
        }
      } else {
        // API調用失敗
        setError(result.message || "檔案驗證失敗");
      }
    } catch (error) {
      setError("檔案處理時發生錯誤");
    } finally {
      setIsLoading(false);
    }
  };

  // 確認匯入
  const handleConfirmImport = async () => {
    if (!selectedFile) return;

    try {
      setIsLoading(true);
      setCurrentStep("importing");
      setImportProgress(0);

      // 進度更新
      const progressInterval = setInterval(() => {
        setImportProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 500);

      // 匯入資料
      const result = await batchImport(selectedFile, "system");

      clearInterval(progressInterval);
      setImportProgress(100);

      if (result.success) {
        // 匯入資料
        const importData = result.data;

        // 設置匯入結果狀態
        setImportResult(importData);

        setCurrentStep("completed");
      } else {
        setError(result.message || "匯入失敗");
        setCurrentStep("preview");
      }
    } catch (error) {
      setError("匯入時發生錯誤");
      setCurrentStep("preview");
    } finally {
      setIsLoading(false);
    }
  };

  // 重新開始
  const handleRestart = () => {
    setCurrentStep("upload");
    setSelectedFile(null);
    setPreviewData([]);
    setValidationResult(null);
    setImportProgress(0);
    setImportResult(null);
    setError("");
  };

  return (
    <Card title="財產批次匯入">
      {/* 頂部操作區域 */}
      <Space style={{ marginBottom: 16 }} wrap>
        <Button
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleDownloadTemplate}
          loading={isLoading}
        >
          下載匯入範本
        </Button>
      </Space>

      {/* 進度指示器 */}
      <div style={{ marginBottom: 24 }}>
        <Steps
          current={
            currentStep === "upload"
              ? 0
              : currentStep === "preview"
              ? 1
              : currentStep === "importing"
              ? 2
              : currentStep === "completed"
              ? 3
              : 0
          }
          items={[
            {
              title: "檔案上傳",
              icon: <UploadOutlined />,
              status:
                currentStep === "upload"
                  ? "process"
                  : ["preview", "importing", "completed"].includes(currentStep)
                  ? "finish"
                  : "wait",
            },
            {
              title: "資料預覽",
              icon: <FileTextOutlined />,
              status:
                currentStep === "preview"
                  ? "process"
                  : ["importing", "completed"].includes(currentStep)
                  ? "finish"
                  : "wait",
            },
            {
              title: "資料匯入",
              icon: <UploadOutlined />,
              status:
                currentStep === "importing"
                  ? "process"
                  : currentStep === "completed"
                  ? "finish"
                  : "wait",
            },
            {
              title: "匯入完成",
              icon: <CheckCircleOutlined />,
              status: currentStep === "completed" ? "finish" : "wait",
            },
          ]}
        />
      </div>

      {/* 錯誤訊息 */}
      {error && (
        <Alert
          type="error"
          message={error}
          style={{ marginBottom: 16 }}
          showIcon
        />
      )}

      {/* 主要內容區域 */}
      <Card bordered={false}>
        <div style={{ marginBottom: 16 }}>
          <h2
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              margin: 0,
              fontSize: "18px",
              fontWeight: 500,
            }}
          >
            {currentStep === "upload" && <UploadOutlined />}
            {currentStep === "preview" && <FileTextOutlined />}
            {currentStep === "importing" && <UploadOutlined />}
            {currentStep === "completed" && <CheckCircleOutlined />}

            {currentStep === "upload" && "上傳Excel檔案"}
            {currentStep === "preview" && "資料預覽與確認"}
            {currentStep === "importing" && "正在匯入資料"}
            {currentStep === "completed" && "匯入完成"}
          </h2>
        </div>

        {currentStep === "upload" && (
          <FileUpload onFileUpload={handleFileUpload} isLoading={isLoading} />
        )}

        {currentStep === "preview" && (
          <DataPreview
            data={previewData}
            validationResult={validationResult}
            onConfirm={handleConfirmImport}
            onCancel={handleRestart}
            isLoading={isLoading}
          />
        )}

        {currentStep === "importing" && (
          <div style={{ textAlign: "center", padding: "48px 24px" }}>
            <h3
              style={{
                fontSize: "18px",
                fontWeight: 500,
                marginBottom: "16px",
              }}
            >
              正在匯入資料...
            </h3>
            <p style={{ color: "#8c8c8c", marginBottom: "32px" }}>
              請稍候，正在處理您的檔案
            </p>
            <Progress
              percent={importProgress}
              style={{ marginBottom: "16px" }}
            />
            <p style={{ fontSize: "14px", color: "#8c8c8c" }}>
              進度: {importProgress}%
            </p>
          </div>
        )}

        {currentStep === "completed" && (
          <ImportResults result={importResult} onRestart={handleRestart} />
        )}
      </Card>
    </Card>
  );
}
