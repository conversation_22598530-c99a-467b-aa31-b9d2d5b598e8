import React, { useEffect, useState, useRef } from "react";
import { Input, Button, Avatar, Spin, FloatButton, Badge } from "antd";
import {
  SendOutlined,
  UserOutlined,
  CustomerServiceOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { eventBus } from "@/utils/eventBus";

interface Message {
  id: number;
  userfromname: string;
  content: string;
  type: "user" | "system";
  timestamp: Date;
}

const ChatMessage = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [isnotread, setIsnotread] = useState(false);
  const [isChatVisible, setIsChatVisible] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // 處理來自 eventBus 的訊息
    const handleChatEvent = (
      event: { action: string; data?: any } | undefined
    ) => {
      if (event && event.action === "newmsg") {
        const newMessage = {
          id: Date.now(),
          userfromname: event.data?.user || "未知使用者",
          content: event.data?.message || "未提供訊息內容",
          type: "system" as const,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, newMessage]);

        if (!isChatVisible) {
          setIsnotread(true);
        }
      }
    };

    eventBus.on("msgUI", handleChatEvent);
    return () => {
      eventBus.off("msgUI", handleChatEvent);
    };
  }, [isChatVisible]);

  const handleSend = () => {
    if (!inputValue.trim()) return;

    const newMessage = {
      id: Date.now(),
      userfromname: "客戶名稱",
      content: inputValue.trim(),
      type: "user" as const,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue("");
    setLoading(true);

    // 模擬客服回覆
    setTimeout(() => {
      const response = {
        id: Date.now() + 1,
        userfromname: "客服人員",
        content: "感謝您的訊息，客服人員將盡快回覆。",
        type: "system" as const,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, response]);
      setLoading(false);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <>
      <div style={{ position: "relative" }}>
        <FloatButton
          shape="circle"
          type="primary"
          style={{
            position: "fixed",
            right: "24px",
            bottom: "24px",
          }}
          icon={<CustomerServiceOutlined />}
          tooltip="聯絡客服"
          onClick={() => {
            setIsChatVisible(!isChatVisible);
            setIsnotread(false);
          }}
        />
        {isnotread && (
          <div
            style={{
              position: "fixed",
              bottom: "60px",
              right: "24px",
              width: "10px",
              height: "10px",
              zIndex: 1000,
              backgroundColor: "red",
              borderRadius: "50%",
              border: "1.5px solid white",
            }}
          />
        )}
      </div>
      {isChatVisible && (
        <div
          style={{
            position: "fixed",
            right: "24px",
            bottom: "84px",
            width: "360px",
            height: "500px",
            backgroundColor: "white",
            borderRadius: "8px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            zIndex: 1000,
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
          }}
        >
          <div
            style={{
              padding: "12px 16px",
              borderBottom: "1px solid #f0f0f0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              backgroundColor: "#1890ff",
              color: "white",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <CustomerServiceOutlined />
              <span>客服聊天室</span>
            </div>
            <CloseOutlined
              onClick={() => setIsChatVisible(false)}
              style={{ cursor: "pointer" }}
            />
          </div>
          <div className="flex flex-col h-full">
            <div
              className="flex-1 overflow-y-auto p-4"
              style={{
                height: "calc(500px - 110px)",
                backgroundColor: "#f5f5f5",
              }}
            >
              {messages.length === 0 && (
                <div className="text-center text-gray-400 mt-4">
                  歡迎使用客服聊天室，請問有什麼可以協助您的嗎？
                </div>
              )}
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex items-start mb-4 ${
                    msg.type === "user" ? "flex-row-reverse" : "flex-row"
                  }`}
                >
                  <Avatar
                    size="small"
                    icon={
                      msg.type === "user" ? (
                        <UserOutlined />
                      ) : (
                        <CustomerServiceOutlined />
                      )
                    }
                    style={{
                      backgroundColor:
                        msg.type === "user" ? "#1890ff" : "#52c41a",
                      marginLeft: msg.type === "user" ? "8px" : "0",
                      marginRight: msg.type === "user" ? "0" : "8px",
                    }}
                  />
                  {/* 發送者名稱 */}
                  {msg.userfromname}
                  <div
                    className={`max-w-[70%] px-3 py-2 rounded-lg ${
                      msg.type === "user"
                        ? "bg-blue-500 text-white"
                        : "bg-white text-gray-800"
                    }`}
                    style={{
                      boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <div className="break-words text-sm">{msg.content}</div>
                    <div
                      className={`text-xs mt-1 ${
                        msg.type === "user" ? "text-blue-100" : "text-gray-400"
                      }`}
                    >
                      {msg.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              {loading && (
                <div className="flex justify-center my-2">
                  <Spin size="small" />
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            <div className="p-2 bg-white border-t">
              <div className="flex">
                <Input.TextArea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="請輸入訊息..."
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  className="flex-1 mr-2 text-sm"
                  style={{ resize: "none" }}
                ></Input.TextArea>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleSend}
                  disabled={!inputValue.trim()}
                  size="middle"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatMessage;
