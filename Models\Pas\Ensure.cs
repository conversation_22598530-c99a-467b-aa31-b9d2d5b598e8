﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 保證書資料表
    /// </summary>
    public class Ensure : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 使用者編號

        [Comment("保證書編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string ensureNumber { get; set; } // 保證書編號

        [Comment("保證人姓名")]
        [Column(TypeName = "nvarchar(100)")]
        public string guarantorName { get; set; } // 保證人姓名

        [Comment("保證人身分證")]
        [Column(TypeName = "nvarchar(20)")]
        public string? guarantorPersonalId { get; set; } // 保證人身分證

        [Comment("保證人生日")]
        [Column(TypeName = "bigint")]
        public long? guarantorBirthday { get; set; } // 保證人生日 (timestamp)

        [Comment("保證人地址")]
        [Column(TypeName = "nvarchar(250)")]
        public string? guarantorAddress { get; set; } // 保證人地址

        [Comment("保證人電話")]
        [Column(TypeName = "nvarchar(50)")]
        public string? guarantorPhone { get; set; } // 保證人電話

        [Comment("關係")]
        [Column(TypeName = "nvarchar(50)")]
        public string? relationship { get; set; } // 關係

        [Comment("保證人財產")]
        [Column(TypeName = "nvarchar(250)")]
        public string? guarantorProperty { get; set; } // 保證人財產

        [Comment("財產價值")]
        [Column(TypeName = "nvarchar(100)")]
        public string? propertyValue { get; set; } // 財產價值

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? remark { get; set; } // 備註

        public Ensure()
        {
            uid = "";
            userId = "";
            ensureNumber = "";
            guarantorName = "";
            guarantorPersonalId = "";
            guarantorBirthday = null;
            guarantorAddress = "";
            guarantorPhone = "";
            relationship = "";
            guarantorProperty = "";
            propertyValue = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 保證書資料 DTO
    /// </summary>
    public class EnsureDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string ensureNumber { get; set; } // 保證書編號
        public string guarantorName { get; set; } // 保證人姓名
        public string guarantorPersonalId { get; set; } // 保證人身分證
        public string guarantorBirthday { get; set; } // 保證人生日 (timestamp)
        public string guarantorAddress { get; set; } // 保證人地址
        public string guarantorPhone { get; set; } // 保證人電話
        public string relationship { get; set; } // 關係
        public string guarantorProperty { get; set; } // 保證人財產
        public string propertyValue { get; set; } // 財產價值
        public string remark { get; set; } // 備註

        public EnsureDTO()
        {
            uid = "";
            userId = "";
            ensureNumber = "";
            guarantorName = "";
            guarantorPersonalId = "";
            guarantorBirthday = "";
            guarantorAddress = "";
            guarantorPhone = "";
            relationship = "";
            guarantorProperty = "";
            propertyValue = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }


}

