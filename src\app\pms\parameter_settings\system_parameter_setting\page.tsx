"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  Tabs,
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Spin,
  Switch,
  InputNumber,
  Typography,
  Divider,
  Select,
  Space,
  Popconfirm,
  Radio,
  List,
  Tag,
  Tooltip,
  Alert,
  Descriptions,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  CalculatorOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons";
import {
  SystemParameter,
  getSystemParameters,
  getSystemParametersByType,
  addSystemParameter,
  editSystemParameter,
  deleteSystemParameter,
  getDepreciationMethods,
  setDefaultDepreciationMethod,
  getDecliningBalanceRates,
  setDecliningBalanceRate,
  getInitializationStatus,
  setInitializationStatus,
} from "@/services/pms/systemParameterSettingService";
import { useAuth } from "@/contexts/AuthContext";

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ParameterTypeOption {
  value: string;
  label: string;
}

const SystemParameterSettingPage: React.FC = () => {
  const [parameters, setParameters] = useState<SystemParameter[]>([]);
  const [depreciationMethods, setDepreciationMethods] = useState<
    SystemParameter[]
  >([]);
  const [decliningBalanceRates, setDecliningBalanceRates] = useState<
    SystemParameter[]
  >([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>("");
  const [currentParameter, setCurrentParameter] =
    useState<SystemParameter | null>(null);
  const [activeTab, setActiveTab] = useState<string>("general");
  const [form] = Form.useForm();
  const { user } = useAuth();
  const [rateSetting, setRateSetting] = useState<{
    assetAccountId: string;
    rate: number;
    userId: string;
  }>({
    assetAccountId: "",
    rate: 0,
    userId: "",
  });
  const [isMobile, setIsMobile] = useState(false);
  const [isDecliningBalanceDefault, setIsDecliningBalanceDefault] =
    useState<boolean>(false);
  const [editingRateKey, setEditingRateKey] = useState<string>("");
  const [rateEditForm] = Form.useForm();
  const [depreciationSimulationVisible, setDepreciationSimulationVisible] =
    useState(false);
  const [simulationRate, setSimulationRate] = useState(0);
  const [simulationTitle, setSimulationTitle] = useState("");
  const [simulationOriginalCost, setSimulationOriginalCost] = useState(100000);
  const [simulationYears, setSimulationYears] = useState(5);
  const [isInitialized, setIsInitialized] = useState<boolean>(true);
  const [initializationMessage, setInitializationMessage] =
    useState<string>("");
  const [confirmMethodName, setConfirmMethodName] = useState<string>("");
  const [confirmModalVisible, setConfirmModalVisible] =
    useState<boolean>(false);
  const [selectedMethodId, setSelectedMethodId] = useState<string>("");
  const [selectedMethodName, setSelectedMethodName] = useState<string>("");
  const [jsonKeyValues, setJsonKeyValues] = useState<
    { key: string; value: string }[]
  >([]);
  const [isJsonValue, setIsJsonValue] = useState<boolean>(false);
  const [isConfirmModalVisible, setIsConfirmModalVisible] =
    useState<boolean>(false);
  const [confirmData, setConfirmData] = useState<any>(null);

  // 系統參數類型選項
  const parameterTypeOptions: ParameterTypeOption[] = [
    { value: "general", label: "一般參數" },
  ];

  // 一般參數設定表格列定義
  const columns = [
    {
      title: "參數名稱",
      dataIndex: "parameterName",
      key: "parameterName",
      width: "15%",
    },
    {
      title: "參數值",
      dataIndex: "parameterValue",
      key: "parameterValue",
      width: "30%",
      render: (text: string) => {
        try {
          // 嘗試解析 JSON
          const jsonObj = JSON.parse(text);
          return <pre>{JSON.stringify(jsonObj, null, 2)}</pre>;
        } catch (e) {
          // 如果不是 JSON 就直接顯示文字
          return text;
        }
      },
    },
    {
      title: "參數說明",
      dataIndex: "parameterDescription",
      key: "parameterDescription",
      width: "25%",
    },
    {
      title: "參數類型",
      dataIndex: "parameterType",
      key: "parameterType",
      width: "10%",
      render: (text: string) => {
        const option = parameterTypeOptions.find((opt) => opt.value === text);
        return option ? option.label : text;
      },
    },
    {
      title: "啟用狀態",
      dataIndex: "isEnabled",
      key: "isEnabled",
      width: "10%",
      render: (isEnabled: boolean) => (
        <Tag color={isEnabled ? "success" : "error"}>
          {isEnabled ? "啟用" : "停用"}
        </Tag>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: "10%",
      render: (_: any, record: SystemParameter) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除此參數嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 加載所有系統參數
  const loadSystemParameters = async () => {
    setLoading(true);
    try {
      const response = await getSystemParameters();
      if (response.success && response.data) {
        setParameters(response.data);
      } else {
        message.error(response.message || "獲取系統參數失敗");
      }
    } catch (error) {
      message.error("獲取系統參數時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 加載折舊法設定
  const loadDepreciationMethods = async () => {
    setLoading(true);
    try {
      const response = await getDepreciationMethods();
      if (response.success && response.data) {
        setDepreciationMethods(response.data);
        // 檢查是否有餘額遞減法且為默認選項
        const decliningBalanceMethod = response.data.find((method) => {
          try {
            const jsonObj = JSON.parse(method.parameterValue);
            return jsonObj.code === "declining_balance" && jsonObj.isDefault;
          } catch (e) {
            return false;
          }
        });
        setIsDecliningBalanceDefault(!!decliningBalanceMethod);
      } else {
        message.error(response.message || "獲取折舊法設定失敗");
      }
    } catch (error) {
      message.error("獲取折舊法設定時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 加載餘額遞減法折舊率設定
  const loadDecliningBalanceRates = async () => {
    setLoading(true);
    try {
      const response = await getDecliningBalanceRates();
      if (response.success && response.data) {
        setDecliningBalanceRates(response.data);
      } else {
        message.error(response.message || "獲取餘額遞減法折舊率設定失敗");
      }
    } catch (error) {
      message.error("獲取餘額遞減法折舊率設定時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 依類型加載系統參數
  const loadParametersByType = async (type: string) => {
    setLoading(true);
    try {
      const response = await getSystemParametersByType(type);
      if (response.success && response.data) {
        setParameters(response.data);
      } else {
        message.error(response.message || `獲取${type}類型參數失敗`);
      }
    } catch (error) {
      message.error(`獲取${type}類型參數時發生錯誤`);
    } finally {
      setLoading(false);
    }
  };

  // 檢查系統初始化狀態
  const checkInitializationStatus = async () => {
    try {
      const response = await getInitializationStatus();
      if (response.success) {
        setIsInitialized(response.data.isInitialized);
        setInitializationMessage(response.data.message);
      } else {
        message.error(response.message || "檢查系統初始化狀態失敗");
      }
    } catch (error) {
      message.error("檢查系統初始化狀態時發生錯誤");
    }
  };

  // 設定系統初始化狀態
  const handleSetInitializationStatus = async () => {
    try {
      const response = await setInitializationStatus(true);
      if (response.success) {
        setIsInitialized(true);
        checkInitializationStatus(); // 重新檢查狀態以獲取最新訊息
      } else {
        message.error(response.message || "設定系統初始化狀態失敗");
      }
    } catch (error) {
      message.error("設定系統初始化狀態時發生錯誤");
    }
  };

  // 初始加載
  useEffect(() => {
    const loadInitialData = async () => {
      await checkInitializationStatus();
      if (activeTab === "general") {
        loadSystemParameters();
      } else if (activeTab === "depreciation_method") {
        await loadDepreciationMethods();
      } else if (activeTab === "declining_balance_rate") {
        // 先載入折舊法設定以檢查默認值
        const depMethodResponse = await getDepreciationMethods();
        if (depMethodResponse.success && depMethodResponse.data) {
          const decliningBalanceMethod = depMethodResponse.data.find(
            (method) => {
              try {
                const jsonObj = JSON.parse(method.parameterValue);
                return (
                  jsonObj.code === "declining_balance" && jsonObj.isDefault
                );
              } catch (e) {
                return false;
              }
            }
          );
          setIsDecliningBalanceDefault(!!decliningBalanceMethod);
        }
        loadDecliningBalanceRates();
      }
    };

    loadInitialData();
  }, [activeTab]);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 切換標籤頁
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 新增系統參數
  const handleAdd = () => {
    setModalTitle("新增系統參數");
    setCurrentParameter(null);
    form.resetFields(); // 重置表單
    setJsonKeyValues([{ key: "", value: "" }]);
    setIsJsonValue(false);
    setIsModalVisible(true);
  };

  // 編輯系統參數
  const handleEdit = (record: SystemParameter) => {
    setModalTitle("編輯系統參數");
    setCurrentParameter(record);

    // 嘗試解析參數值是否為JSON格式
    try {
      const jsonObj = JSON.parse(record.parameterValue);
      if (typeof jsonObj === "object" && jsonObj !== null) {
        // 將JSON物件轉換為鍵值對數組
        const keyValues = Object.entries(jsonObj).map(([key, value]) => ({
          key,
          value:
            typeof value === "object" ? JSON.stringify(value) : String(value),
        }));
        setJsonKeyValues(keyValues);
        setIsJsonValue(true);
      } else {
        setIsJsonValue(false);
      }
    } catch (e) {
      // 不是有效的JSON，視為純文字
      setIsJsonValue(false);
    }

    form.setFieldsValue({
      parameterId: record.parameterId,
      parameterName: record.parameterName,
      parameterValue: record.parameterValue,
      parameterDescription: record.parameterDescription,
      parameterType: record.parameterType,
      isEnabled: record.isEnabled,
      sortOrder: record.sortOrder,
    });
    setIsModalVisible(true);
  };

  // 刪除系統參數
  const handleDelete = async (record: SystemParameter) => {
    try {
      setLoading(true);
      const response = await deleteSystemParameter(record);
      if (response.success) {
        message.success("刪除系統參數成功");
        // 重新加載數據
        if (activeTab === "general") {
          loadSystemParameters();
        } else if (activeTab === "depreciation_method") {
          loadDepreciationMethods();
        } else if (activeTab === "declining_balance_rate") {
          loadDecliningBalanceRates();
        }
      } else {
        message.error(response.message || "刪除系統參數失敗");
      }
    } catch (error) {
      message.error("刪除系統參數時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 設定默認折舊法
  const handleSetDefaultDepreciationMethod = async (methodId: string) => {
    if (isInitialized) {
      message.warning("系統已初始化，無法變更折舊法設定");
      return;
    }

    try {
      setLoading(true);
      const response = await setDefaultDepreciationMethod(methodId);
      if (response.success) {
        message.success("設定默認折舊法成功");
        // 檢查是否設定餘額遞減法為默認
        const method = depreciationMethods.find(
          (m) => m.parameterId === methodId
        );
        if (method) {
          try {
            const jsonObj = JSON.parse(method.parameterValue);
            setIsDecliningBalanceDefault(jsonObj.code === "declining_balance");
            // 如果設定了默認折舊法，自動設定系統為已初始化
            await handleSetInitializationStatus();
          } catch (e) {
            setIsDecliningBalanceDefault(false);
          }
        }
        loadDepreciationMethods();
      } else {
        message.error(response.message || "設定默認折舊法失敗");
      }
    } catch (error) {
      message.error("設定默認折舊法時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 判斷折舊率是否正在編輯
  const isEditingRate = (record: SystemParameter) =>
    record.parameterId === editingRateKey;

  // 開始編輯折舊率
  const editRate = (record: SystemParameter) => {
    try {
      const jsonObj = JSON.parse(record.parameterValue);
      rateEditForm.setFieldsValue({ rate: jsonObj.rate });
      setEditingRateKey(record.parameterId);
    } catch (e) {
      console.error("解析折舊率數據錯誤:", e);
    }
  };

  // 保存編輯的折舊率
  const saveRate = async (record: SystemParameter) => {
    try {
      const values = await rateEditForm.validateFields();
      const rate = values.rate;

      await handleSetDecliningBalanceRate(record, rate);
      setEditingRateKey("");
    } catch (errInfo) {
      console.error("驗證失敗:", errInfo);
    }
  };

  // 設定餘額遞減法折舊率
  const handleSetDecliningBalanceRate = async (
    record: SystemParameter,
    rate: number
  ) => {
    try {
      setLoading(true);
      const jsonObj = JSON.parse(record.parameterValue);
      const request = {
        assetAccountId: jsonObj.assetAccountId,
        rate: rate,
        userId: user?.userId || "",
      };

      const response = await setDecliningBalanceRate(request);
      if (response.success) {
        message.success("設定餘額遞減法折舊率成功");
        // 更新本地數據
        const updatedRates = decliningBalanceRates.map((item) => {
          if (item.parameterId === record.parameterId) {
            return {
              ...item,
              parameterValue: JSON.stringify({
                ...jsonObj,
                rate: rate,
              }),
            };
          }
          return item;
        });
        setDecliningBalanceRates(updatedRates);
      } else {
        message.error(response.message || "設定餘額遞減法折舊率失敗");
        // 重新加載數據
        loadDecliningBalanceRates();
      }
    } catch (error) {
      message.error("設定餘額遞減法折舊率時發生錯誤");
      // 重新加載數據
      loadDecliningBalanceRates();
    } finally {
      setLoading(false);
    }
  };

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 處理JSON格式參數
      if (isJsonValue && jsonKeyValues.length > 0) {
        // 過濾掉空鍵值對
        const validKeyValues = jsonKeyValues.filter(
          (item) => item.key.trim() !== ""
        );
        // 創建一個空對象來存儲解析後的鍵值對
        const jsonObj: Record<string, any> = {};
        // 遍歷過濾後的鍵值對
        validKeyValues.forEach((item) => {
          // 嘗試將值轉換為適當的數據類型（數字、布爾值或保持字符串）
          let parsedValue: any = item.value;
          if (parsedValue === "true") {
            parsedValue = true;
          } else if (parsedValue === "false") {
            parsedValue = false;
          } else if (!isNaN(Number(parsedValue)) && parsedValue.trim() !== "") {
            parsedValue = Number(parsedValue);
          }
          jsonObj[item.key] = parsedValue;
        });

        values.parameterValue = JSON.stringify(jsonObj);
      }

      // 顯示確認畫面
      if (currentParameter) {
        // 如果是編輯
        setConfirmData({
          ...currentParameter,
          ...values,
          updateUserId: user?.userId || "",
          isEdit: true,
        });
      } else {
        // 如果是新增
        setConfirmData({
          parameterId: "", // 由後端生成
          ...values,
          createUserId: user?.userId || "",
          isDeleted: false,
          isEdit: false,
        });
      }
      setIsConfirmModalVisible(true);
    } catch (error) {
      message.error("表單驗證失敗，請檢查輸入資料");
    }
  };

  // 確認後提交
  const handleConfirmSubmit = async () => {
    setIsConfirmModalVisible(false);
    setLoading(true);

    try {
      if (confirmData.isEdit) {
        // 編輯模式
        const { isEdit, ...parameterData } = confirmData;
        const response = await editSystemParameter(parameterData);
        if (response.success) {
          message.success("編輯系統參數成功");
          setIsModalVisible(false);
          // 重新加載數據
          if (activeTab === "general") {
            loadSystemParameters();
          } else if (activeTab === "depreciation_method") {
            loadDepreciationMethods();
          } else if (activeTab === "declining_balance_rate") {
            loadDecliningBalanceRates();
          }
        } else {
          message.error(response.message || "編輯系統參數失敗");
        }
      } else {
        // 新增模式
        const { isEdit, ...parameterData } = confirmData;
        const response = await addSystemParameter(parameterData);
        if (response.success) {
          message.success("新增系統參數成功");
          setIsModalVisible(false);
          // 重新加載數據
          if (activeTab === "general") {
            loadSystemParameters();
          } else if (activeTab === "depreciation_method") {
            loadDepreciationMethods();
          } else if (activeTab === "declining_balance_rate") {
            loadDecliningBalanceRates();
          }
        } else {
          message.error(response.message || "新增系統參數失敗");
        }
      }
    } catch (error) {
      message.error("提交資料時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 開啟確認視窗
  const showConfirmModal = (methodId: string, methodName: string) => {
    setSelectedMethodId(methodId);
    setSelectedMethodName(methodName);
    setConfirmMethodName("");
    setConfirmModalVisible(true);
  };

  // 確認設定默認折舊法
  const confirmSetDefaultMethod = async () => {
    if (confirmMethodName !== selectedMethodName) {
      message.error("輸入的折舊法名稱不正確");
      return;
    }
    setConfirmModalVisible(false);
    await handleSetDefaultDepreciationMethod(selectedMethodId);
  };

  // 折舊法表格列定義
  const depreciationMethodColumns = [
    {
      title: "折舊法",
      dataIndex: "parameterName",
      key: "parameterName",
      width: "10%",
    },
    {
      title: "設定值",
      dataIndex: "parameterValue",
      key: "parameterValue",
      width: "40%",
      render: (text: string) => {
        try {
          const jsonObj = JSON.parse(text);
          return (
            <div>
              <p>
                <strong>代碼:</strong> {jsonObj.code}
              </p>
              <p>
                <strong>說明:</strong> {jsonObj.description}
              </p>
              <p>
                <strong>公式:</strong> {jsonObj.formula}
              </p>
              {jsonObj.rate && (
                <p>
                  <strong>折舊率:</strong> {jsonObj.rate}
                </p>
              )}
            </div>
          );
        } catch (e) {
          return text;
        }
      },
    },
    {
      title: "參數說明",
      dataIndex: "parameterDescription",
      key: "parameterDescription",
      width: "25%",
      ellipsis: false,
      wordWrap: true,
    },
    {
      title: "默認選項",
      dataIndex: "parameterValue",
      key: "isDefault",
      width: "10%",
      render: (text: string, record: SystemParameter) => {
        try {
          const jsonObj = JSON.parse(text);
          return (
            <Space direction="vertical" size="small">
              {isInitialized ? (
                <>
                  <Radio checked={jsonObj.isDefault} disabled />
                  {jsonObj.isDefault && <Tag color="green">已設為默認</Tag>}
                </>
              ) : (
                <Radio
                  checked={jsonObj.isDefault}
                  onChange={() =>
                    showConfirmModal(record.parameterId, record.parameterName)
                  }
                />
              )}
            </Space>
          );
        } catch (e) {
          return <Radio disabled />;
        }
      },
    },
  ];

  // 計算餘額遞減法折舊範例數據
  const calculateDecliningBalanceDepreciation = (
    originalCost: number,
    rate: number,
    years: number
  ) => {
    let bookValue = originalCost;
    const result = [];

    for (let year = 1; year <= years; year++) {
      const depreciationAmount = bookValue * (rate / 100);
      const endBookValue = bookValue - depreciationAmount;

      result.push({
        key: year,
        year: `第${year}年`,
        beginBookValue: bookValue.toFixed(0),
        depreciationAmount: depreciationAmount.toFixed(0),
        endBookValue: endBookValue.toFixed(0),
      });

      bookValue = endBookValue;
    }

    return result;
  };

  // 顯示折舊試算 Modal
  const showDepreciationSimulation = (rate: number, assetName: string) => {
    setSimulationRate(rate);
    setSimulationTitle(`${assetName} - 餘額遞減法折舊試算`);
    setDepreciationSimulationVisible(true);
  };

  // 折舊試算表格列定義
  const depreciationSimulationColumns = [
    {
      title: "年度",
      dataIndex: "year",
      key: "year",
      align: "center" as const,
    },
    {
      title: "賬面價值（期初）",
      dataIndex: "beginBookValue",
      key: "beginBookValue",
      align: "right" as const,
      render: (value: string) => `$${value}`,
    },
    {
      title: "折舊額",
      dataIndex: "depreciationAmount",
      key: "depreciationAmount",
      align: "right" as const,
      render: (value: string) => `$${value}`,
    },
    {
      title: "賬面價值（期末）",
      dataIndex: "endBookValue",
      key: "endBookValue",
      align: "right" as const,
      render: (value: string) => `$${value}`,
    },
  ];

  // 餘額遞減法折舊率表格列定義
  const decliningBalanceRateColumns = [
    {
      title: "財產科目",
      dataIndex: "parameterValue",
      key: "assetAccountName",
      width: "10%",
      render: (text: string) => {
        try {
          const jsonObj = JSON.parse(text);
          const assetAccountName = jsonObj.assetAccountName || "";
          return assetAccountName;
        } catch (e) {
          return "";
        }
      },
    },
    {
      title: "折舊率 (%)",
      dataIndex: "parameterValue",
      key: "rate",
      width: "10%",
      render: (text: string, record: SystemParameter) => {
        const editable = isEditingRate(record);
        try {
          const jsonObj = JSON.parse(text);
          return editable ? (
            <Form form={rateEditForm} component={false}>
              <Form.Item
                name="rate"
                style={{ margin: 0 }}
                rules={[
                  { required: true, message: "請輸入折舊率" },
                  {
                    type: "number",
                    min: 0,
                    message: "折舊率為%數",
                  },
                ]}
                initialValue={jsonObj.rate}
              >
                <InputNumber
                  min={0}
                  step={10}
                  precision={2}
                  formatter={(value) => `${value}%`}
                  style={{ width: "100px" }}
                  placeholder="請輸入折舊率"
                />
              </Form.Item>
            </Form>
          ) : (
            <span>{jsonObj.rate}%</span>
          );
        } catch (e) {
          return 0;
        }
      },
    },
    {
      title: "參數說明",
      dataIndex: "parameterDescription",
      key: "parameterDescription",
      width: "60%",
    },
    {
      title: "折舊試算",
      width: "20%",
      render: (_: any, record: SystemParameter) => {
        try {
          const jsonObj = JSON.parse(record.parameterValue);
          const rate = jsonObj.rate;
          const assetName = jsonObj.assetAccountName || "";
          // 土地和未完工程通常不提列折舊
          if (assetName === "土地" || assetName === "未完工程" || rate <= 0) {
            return <Text type="secondary">不適用</Text>;
          }
          return (
            <Button
              type="link"
              icon={<CalculatorOutlined />}
              onClick={() => showDepreciationSimulation(rate, assetName)}
            >
              查看試算
            </Button>
          );
        } catch (e) {
          return null;
        }
      },
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      render: (_: any, record: SystemParameter) => {
        const editable = isEditingRate(record);
        return editable ? (
          <Space>
            <Button
              type="link"
              onClick={() => saveRate(record)}
              style={{ marginRight: 8 }}
            >
              儲存
            </Button>
            <Button type="link" onClick={() => setEditingRateKey("")}>
              取消
            </Button>
          </Space>
        ) : (
          <Button
            type="link"
            disabled={editingRateKey !== "" || !isDecliningBalanceDefault}
            icon={<EditOutlined />}
            onClick={() => editRate(record)}
          >
            編輯
          </Button>
        );
      },
    },
  ];

  // 手機版折舊法列表
  const renderMobileDepreciationList = () => {
    return (
      <List
        loading={loading}
        dataSource={depreciationMethods}
        renderItem={(method) => {
          const jsonObj = JSON.parse(method.parameterValue);
          return (
            <List.Item
              key={method.parameterId}
              style={{
                padding: "12px",
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <div style={{ width: "100%" }}>
                <div style={{ marginBottom: "8px" }}>
                  <Text strong>{method.parameterName}</Text>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">代碼：</Text>
                  <div>{jsonObj.code}</div>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">說明：</Text>
                  <div>{jsonObj.description}</div>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">公式：</Text>
                  <div>{jsonObj.formula}</div>
                </div>
                {jsonObj.rate && (
                  <div style={{ marginBottom: "8px" }}>
                    <Text type="secondary">折舊率：</Text>
                    <div>{jsonObj.rate}</div>
                  </div>
                )}
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">參數說明：</Text>
                  <div>{method.parameterDescription}</div>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">默認選項：</Text>
                  {isInitialized ? (
                    <>
                      <Radio checked={jsonObj.isDefault} disabled />
                      {jsonObj.isDefault && (
                        <Tag color="green" style={{ marginLeft: 8 }}>
                          已設為默認
                        </Tag>
                      )}
                    </>
                  ) : (
                    <Radio
                      checked={jsonObj.isDefault}
                      onChange={() =>
                        showConfirmModal(
                          method.parameterId,
                          method.parameterName
                        )
                      }
                    />
                  )}
                </div>
              </div>
            </List.Item>
          );
        }}
        pagination={{
          pageSize: 10,
          size: "small",
        }}
      />
    );
  };

  // 手機版餘額遞減法折舊率列表
  const renderMobileDecliningBalanceList = () => {
    return (
      <List
        loading={loading}
        dataSource={decliningBalanceRates}
        renderItem={(rate) => {
          const jsonObj = JSON.parse(rate.parameterValue);
          const editable = isEditingRate(rate);
          const assetName = jsonObj.assetAccountName || "";
          const rateValue = jsonObj.rate;
          return (
            <List.Item
              key={rate.parameterId}
              style={{
                padding: "12px",
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <div style={{ width: "100%" }}>
                <div style={{ marginBottom: "8px" }}>
                  <Text strong>{jsonObj.assetAccountName}</Text>
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">折舊率：</Text>
                  {editable ? (
                    <Form form={rateEditForm} component={false}>
                      <Form.Item
                        name="rate"
                        style={{ margin: 0 }}
                        rules={[
                          { required: true, message: "請輸入折舊率" },
                          {
                            type: "number",
                            min: 0,
                            message: "折舊率為%數",
                          },
                        ]}
                        initialValue={jsonObj.rate}
                      >
                        <InputNumber
                          min={0}
                          step={10}
                          precision={2}
                          formatter={(value) => `${value}%`}
                          style={{ width: "100px" }}
                          placeholder="請輸入折舊率"
                        />
                      </Form.Item>
                    </Form>
                  ) : (
                    <span>{jsonObj.rate}%</span>
                  )}
                </div>
                <div style={{ marginBottom: "8px" }}>
                  <Text type="secondary">說明：</Text>
                  <div>{rate.parameterDescription}</div>
                </div>
                {!isDecliningBalanceDefault && (
                  <div style={{ marginBottom: "8px" }}>
                    <Text type="warning">
                      請先將餘額遞減法設為默認折舊法，才能編輯折舊率
                    </Text>
                  </div>
                )}
                <Space style={{ marginTop: "12px" }} wrap>
                  {editable ? (
                    <>
                      <Button type="link" onClick={() => saveRate(rate)}>
                        儲存
                      </Button>
                      <Button type="link" onClick={() => setEditingRateKey("")}>
                        取消
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="link"
                      disabled={
                        editingRateKey !== "" || !isDecliningBalanceDefault
                      }
                      icon={<EditOutlined />}
                      onClick={() => editRate(rate)}
                    >
                      編輯
                    </Button>
                  )}

                  {/* 折舊試算按鈕 */}
                  {assetName !== "土地" &&
                    assetName !== "未完工程" &&
                    rateValue > 0 && (
                      <Button
                        type="link"
                        icon={<CalculatorOutlined />}
                        onClick={() =>
                          showDepreciationSimulation(rateValue, assetName)
                        }
                      >
                        查看試算
                      </Button>
                    )}
                </Space>
              </div>
            </List.Item>
          );
        }}
        pagination={{
          pageSize: 10,
          size: "small",
        }}
      />
    );
  };

  // 切換參數值輸入方式
  const toggleValueInputType = () => {
    if (!isJsonValue) {
      // 從純文字切換到JSON格式
      try {
        // 取得當前文字區域的值並嘗試解析為JSON
        const currentValue = form.getFieldValue("parameterValue");

        if (currentValue && currentValue.trim() !== "") {
          try {
            const parsedJson = JSON.parse(currentValue);
            if (typeof parsedJson === "object" && parsedJson !== null) {
              // 將JSON物件轉換為鍵值對數組
              const keyValues = Object.entries(parsedJson).map(
                ([key, value]) => ({
                  key,
                  value:
                    typeof value === "object"
                      ? JSON.stringify(value)
                      : String(value),
                })
              );

              if (keyValues.length > 0) {
                setJsonKeyValues(keyValues);
                setIsJsonValue(true);
                return;
              }
            }
          } catch (error) {
            console.error("無法解析JSON格式:", error);
            // 如果解析失敗，顯示提示訊息
            message.warning("現有值不是有效的JSON格式，已切換為空白鍵值對");
          }
        }

        // 如果沒有有效的JSON或解析失敗，添加一個空白鍵值對
        setJsonKeyValues([{ key: "", value: "" }]);
      } catch (error) {
        // 出現異常時添加一個空白鍵值對
        setJsonKeyValues([{ key: "", value: "" }]);
        console.error("切換到JSON格式時出錯:", error);
      }
    } else {
      // 從JSON格式切換到純文字
      try {
        if (jsonKeyValues.length > 0) {
          const jsonObj: Record<string, any> = {};

          // 只處理有效的鍵值對（鍵名不為空）
          jsonKeyValues
            .filter((item) => item.key.trim() !== "")
            .forEach((item) => {
              // 嘗試將值轉換為適當的數據類型
              let parsedValue: any = item.value;
              if (parsedValue === "true") {
                parsedValue = true;
              } else if (parsedValue === "false") {
                parsedValue = false;
              } else if (
                !isNaN(Number(parsedValue)) &&
                parsedValue.trim() !== ""
              ) {
                parsedValue = Number(parsedValue);
              }
              jsonObj[item.key] = parsedValue;
            });

          // 美化JSON格式以便於閱讀
          form.setFieldsValue({
            parameterValue: JSON.stringify(jsonObj, null, 2),
          });
        } else {
          form.setFieldsValue({ parameterValue: "{}" });
        }
      } catch (error) {
        console.error("轉換JSON格式時出錯:", error);
        // 發生錯誤時至少保留一個空的JSON對象
        form.setFieldsValue({ parameterValue: "{}" });
      }
    }
    setIsJsonValue(!isJsonValue);
  };

  // 新增鍵值對
  const addKeyValuePair = () => {
    setJsonKeyValues([...jsonKeyValues, { key: "", value: "" }]);
  };

  // 移除鍵值對
  const removeKeyValuePair = (index: number) => {
    const newKeyValues = [...jsonKeyValues];
    newKeyValues.splice(index, 1);
    setJsonKeyValues(newKeyValues);
  };

  // 更新鍵值對
  const updateKeyValue = (
    index: number,
    field: "key" | "value",
    value: string
  ) => {
    const newKeyValues = [...jsonKeyValues];
    newKeyValues[index][field] = value;
    setJsonKeyValues(newKeyValues);
  };

  // 格式化參數值顯示
  const formatParameterValue = (value: string) => {
    try {
      const jsonObj = JSON.parse(value);
      return (
        <pre style={{ maxHeight: "200px", overflow: "auto" }}>
          {JSON.stringify(jsonObj, null, 2)}
        </pre>
      );
    } catch (e) {
      return value;
    }
  };

  // 取得參數類型名稱
  const getParameterTypeName = (type: string) => {
    const option = parameterTypeOptions.find((opt) => opt.value === type);
    return option ? option.label : type;
  };

  return (
    <div className="system-parameter-setting">
      <Card
        title={<Title level={4}>系統參數設定</Title>}
        extra={
          activeTab === "general" && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              style={{ width: isMobile ? "100%" : "auto" }}
            >
              新增參數
            </Button>
          )
        }
        styles={{
          body: { padding: isMobile ? "12px" : "24px" },
        }}
      >
        <Spin spinning={loading}>
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={[
              {
                key: "general",
                label: "一般參數設定",
                children: (
                  <>
                    {isInitialized && initializationMessage && (
                      <Alert
                        message={initializationMessage}
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />
                    )}
                    <Table
                      columns={columns}
                      dataSource={parameters}
                      rowKey="parameterId"
                      pagination={{
                        defaultPageSize: 10,
                        showSizeChanger: true,
                        pageSizeOptions: ["10", "20", "50", "100"],
                        showTotal: (total) => `共 ${total} 筆資料`,
                      }}
                    />
                  </>
                ),
              },
              {
                key: "depreciation_method",
                label: "折舊法設定",
                children: isMobile ? (
                  renderMobileDepreciationList()
                ) : (
                  <Table
                    columns={depreciationMethodColumns}
                    dataSource={depreciationMethods}
                    rowKey="parameterId"
                    pagination={{
                      defaultPageSize: 10,
                      showSizeChanger: true,
                      pageSizeOptions: ["10", "20", "50", "100"],
                      showTotal: (total) => `共 ${total} 筆資料`,
                    }}
                  />
                ),
              },
              {
                key: "declining_balance_rate",
                label: "餘額遞減法折舊率設定",
                children: isMobile ? (
                  renderMobileDecliningBalanceList()
                ) : (
                  <Table
                    columns={decliningBalanceRateColumns}
                    dataSource={decliningBalanceRates}
                    rowKey="parameterId"
                    pagination={{
                      defaultPageSize: 10,
                      showSizeChanger: true,
                      pageSizeOptions: ["10", "20", "50", "100"],
                      showTotal: (total) => `共 ${total} 筆資料`,
                    }}
                    bordered
                    size="middle"
                  />
                ),
              },
            ]}
          />
        </Spin>

        <Modal
          title={modalTitle}
          open={isModalVisible}
          onOk={handleSubmit}
          onCancel={() => setIsModalVisible(false)}
          confirmLoading={loading}
          width={isMobile ? "100%" : 700}
          style={isMobile ? { top: 0 } : {}}
          okText="確認"
          cancelText="取消"
        >
          <Form form={form} layout="vertical">
            <Form.Item name="parameterId" hidden>
              <Input />
            </Form.Item>

            <Form.Item
              name="parameterName"
              label="參數名稱"
              rules={[{ required: true, message: "請輸入參數名稱" }]}
            >
              <Input placeholder="請輸入參數名稱" />
            </Form.Item>

            <Form.Item label="參數值" required>
              <div style={{ marginBottom: 8 }}>
                <Switch
                  checkedChildren="JSON"
                  unCheckedChildren="純文字"
                  checked={isJsonValue}
                  onChange={toggleValueInputType}
                />
              </div>

              {isJsonValue ? (
                <div>
                  {jsonKeyValues.map((item, index) => (
                    <div
                      key={index}
                      style={{ display: "flex", marginBottom: 8 }}
                    >
                      <Input
                        placeholder="參數名稱"
                        value={item.key}
                        onChange={(e) =>
                          updateKeyValue(index, "key", e.target.value)
                        }
                        style={{ width: "30%", marginRight: 8 }}
                      />
                      <Input
                        placeholder="參數值"
                        value={item.value}
                        onChange={(e) =>
                          updateKeyValue(index, "value", e.target.value)
                        }
                        style={{ width: "60%" }}
                      />
                      <Button
                        type="text"
                        icon={<MinusCircleOutlined />}
                        onClick={() => removeKeyValuePair(index)}
                        disabled={jsonKeyValues.length <= 1}
                        style={{ marginLeft: 8 }}
                      />
                    </div>
                  ))}
                  <Button
                    type="dashed"
                    onClick={addKeyValuePair}
                    icon={<PlusCircleOutlined />}
                    style={{ width: "100%" }}
                  >
                    新增系統參數
                  </Button>
                </div>
              ) : (
                <Form.Item
                  name="parameterValue"
                  noStyle
                  rules={[{ required: true, message: "請輸入參數值" }]}
                >
                  <TextArea
                    rows={4}
                    placeholder="請輸入參數值 (JSON 格式或純文字)"
                  />
                </Form.Item>
              )}
            </Form.Item>

            <Form.Item
              name="parameterDescription"
              label="參數說明"
              rules={[{ required: true, message: "請輸入參數說明" }]}
            >
              <TextArea rows={2} placeholder="請輸入參數說明" />
            </Form.Item>

            <Form.Item
              name="parameterType"
              label="參數類型"
              rules={[{ required: true, message: "請選擇參數類型" }]}
            >
              <Select placeholder="請選擇參數類型">
                {parameterTypeOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="isEnabled"
              label="啟用狀態"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Form>
        </Modal>

        {/* 折舊試算 Modal */}
        <Modal
          title={simulationTitle}
          open={depreciationSimulationVisible}
          onCancel={() => setDepreciationSimulationVisible(false)}
          footer={[
            <Button
              key="close"
              onClick={() => setDepreciationSimulationVisible(false)}
            >
              關閉
            </Button>,
          ]}
          width={700}
        >
          <div style={{ marginBottom: 16 }}>
            <div
              style={{
                display: "flex",
                gap: "16px",
                alignItems: "center",
                marginBottom: "16px",
              }}
            >
              <div>
                <Text>原始成本：</Text>
                <InputNumber
                  value={simulationOriginalCost}
                  onChange={(value) =>
                    setSimulationOriginalCost(value || 100000)
                  }
                  min={1}
                  step={10000}
                  style={{ width: "120px", marginLeft: "8px" }}
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ""))}
                />
              </div>
              <div>
                <Text>使用年限：</Text>
                <InputNumber
                  value={simulationYears}
                  onChange={(value) => setSimulationYears(value || 5)}
                  min={1}
                  max={50}
                  style={{ width: "80px", marginLeft: "8px" }}
                  suffix="年"
                />
              </div>
              <div>
                <Text>折舊率：</Text>
                <InputNumber
                  value={simulationRate}
                  onChange={(value) => setSimulationRate(value || 0)}
                  min={0}
                  max={100}
                  step={5}
                  precision={2}
                  style={{ width: "80px", marginLeft: "8px" }}
                  formatter={(value) => `${value}%`}
                />
              </div>
            </div>
            <Text>餘額遞減法折舊計算：</Text>
          </div>
          <Table
            columns={depreciationSimulationColumns}
            dataSource={calculateDecliningBalanceDepreciation(
              simulationOriginalCost,
              simulationRate,
              simulationYears
            )}
            pagination={false}
            bordered
            size="middle"
            summary={(pageData) => {
              const results = calculateDecliningBalanceDepreciation(
                simulationOriginalCost,
                simulationRate,
                simulationYears
              );
              const totalDepreciation = results.reduce(
                (sum, item) => sum + parseFloat(item.depreciationAmount),
                0
              );
              const finalBookValue = results[results.length - 1].endBookValue;

              return (
                <Table.Summary>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} align="center">
                      <Text strong>合計</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1} align="right">
                      <Text strong>
                        ${simulationOriginalCost.toLocaleString()}
                      </Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2} align="right">
                      <Text strong>${totalDepreciation.toLocaleString()}</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      <Text strong>${finalBookValue}</Text>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              * 餘額遞減法是以資產的賬面價值為基礎計算折舊，每年的折舊額 =
              期初賬面價值 × 折舊率
            </Text>
          </div>
        </Modal>

        {/* 設定默認折舊法確認 Modal */}
        <Modal
          title="確認設定折舊法"
          open={confirmModalVisible}
          onCancel={() => setConfirmModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setConfirmModalVisible(false)}>
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              danger
              onClick={confirmSetDefaultMethod}
              disabled={confirmMethodName !== selectedMethodName}
            >
              設定
            </Button>,
          ]}
        >
          <Alert
            message="注意：設定後系統將自動初始化，之後將無法變更折舊法"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <p>
            您即將設定「
            <strong style={{ color: "red" }}>{selectedMethodName}</strong>
            」為折舊法
          </p>
          <Input
            placeholder="確認變更請輸入折舊法名稱"
            value={confirmMethodName}
            onChange={(e) => setConfirmMethodName(e.target.value)}
            style={{ marginTop: 8 }}
          />
          {confirmMethodName && confirmMethodName !== selectedMethodName && (
            <Text type="danger" style={{ display: "block", marginTop: 8 }}>
              輸入的折舊法名稱不正確
            </Text>
          )}
        </Modal>

        {/* 確認提交的系統參數 Modal */}
        <Modal
          title="確認系統參數資料"
          open={isConfirmModalVisible}
          onOk={handleConfirmSubmit}
          onCancel={() => setIsConfirmModalVisible(false)}
          width={isMobile ? "100%" : 700}
          style={isMobile ? { top: 0 } : {}}
          confirmLoading={loading}
          okButtonProps={{
            danger: confirmData?.isEdit,
          }}
          okText={confirmData?.isEdit ? "儲存" : "新增"}
          cancelText="取消"
        >
          {confirmData && (
            <Descriptions bordered column={1} size="small">
              <Descriptions.Item label="參數名稱">
                {confirmData.parameterName}
              </Descriptions.Item>
              <Descriptions.Item label="參數值">
                {formatParameterValue(confirmData.parameterValue)}
              </Descriptions.Item>
              <Descriptions.Item label="參數說明">
                {confirmData.parameterDescription}
              </Descriptions.Item>
              <Descriptions.Item label="參數類型">
                {getParameterTypeName(confirmData.parameterType)}
              </Descriptions.Item>
              <Descriptions.Item label="啟用狀態">
                <Tag color={confirmData.isEnabled ? "success" : "error"}>
                  {confirmData.isEnabled ? "啟用" : "停用"}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          )}
          <div style={{ marginTop: 16 }}>
            <Alert
              message={
                confirmData?.isEdit
                  ? "請確認以上資料是否正確，確認後將更新系統參數"
                  : "請確認以上資料是否正確，確認後將新增系統參數"
              }
              type="error"
              showIcon
            />
          </div>
        </Modal>
      </Card>
    </div>
  );
};

export default SystemParameterSettingPage;
