"use client";

/*全域佈局
/app/components/layout/Layout.tsx
*/

import { Inter } from "next/font/google";
import StyledComponentsRegistry from "@/lib/AntdRegistry";
import MainLayout from "@/app/components/layout/MainLayout";
import { AuthProvider } from "@/contexts/AuthContext";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isLoginPage = pathname === "/login";
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <html lang="zh-TW">
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
      </head>
      <body
        className={inter.className}
        style={{
          margin: 0,
          padding: 0,
          height: "100vh",
          background: "#f5f5f5",
          overflowX: "hidden",
          WebkitOverflowScrolling: "touch",
          fontSize: isMobile ? "14px" : "16px",
        }}
      >
        <StyledComponentsRegistry>
          <AuthProvider>
            {isLoginPage ? children : <MainLayout>{children}</MainLayout>}
          </AuthProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
