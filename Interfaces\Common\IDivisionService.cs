using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IDivisionService
    {
        /// <summary>
        /// 取得組別資料
        /// </summary>
        /// <param name="_divisionId">組別編號</param>
        /// <returns>組別資料列表</returns>
        Task<List<DivisionDTO>> GetDivisionAsync(string _divisionId = "");

        /// <summary>
        /// 新增組別
        /// </summary>
        /// <param name="_data">組別資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddDivisionAsync(DivisionDTO _data);

        /// <summary>
        /// 編輯組別
        /// </summary>
        /// <param name="_data">組別資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditDivisionAsync(DivisionDTO _data);

        /// <summary>
        /// 刪除組別
        /// </summary>
        /// <param name="_data">組別資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteDivisionAsync(DivisionDTO _data);

        /// <summary>
        /// 取得組別詳細資料
        /// </summary>
        /// <param name="_divisionId">組別編號</param>
        /// <returns>組別詳細資料</returns>
        Task<DivisionDTO> GetDivisionDetailAsync(string _divisionId);
    }
}