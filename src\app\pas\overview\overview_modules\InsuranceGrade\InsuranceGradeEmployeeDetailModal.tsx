'use client';

import React, { useState, useEffect } from 'react';
import {
    Modal,
    Table,
    Typography,
    Space,
    Tag,
    message,
    Spin
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
    InsuranceGradeEmployeeDetail,
    getEmployeeInsuranceGradeDetail
} from '@/services/pas/Insurance/InsuranceGradeService';
import {
    DATE_FORMAT,
    getInsuranceTypeName
} from "@/services/pas/Insurance/constants/insuranceConstants";

const { Title } = Typography;

interface InsuranceGradeEmployeeDetailModalProps {
    visible: boolean;
    onClose: () => void;
    gradeUid: string;
    status: 'current' | 'pending';
    insuranceType: number;
    monthlySalary: number;
}

/**
 * 保險級距員工詳細資訊彈窗組件
 */
export default function InsuranceGradeEmployeeDetailModal({
    visible,
    onClose,
    gradeUid,
    status,
    insuranceType,
    monthlySalary
}: InsuranceGradeEmployeeDetailModalProps) {
    const [loading, setLoading] = useState(false);
    const [employeeList, setEmployeeList] = useState<InsuranceGradeEmployeeDetail[]>([]);

    // 載入員工詳細資料
    const loadEmployeeDetail = async () => {
        if (!gradeUid || !status) return;

        setLoading(true);
        try {
            const response = await getEmployeeInsuranceGradeDetail(gradeUid, status);
            if (response.success && response.data) {
                setEmployeeList(response.data);
            } else {
                message.error(response.message || '載入員工資料失敗');
                setEmployeeList([]);
            }
        } catch (error: any) {
            message.error('載入員工資料失敗：' + error.message);
            setEmployeeList([]);
        } finally {
            setLoading(false);
        }
    };

    // 當彈窗顯示時載入資料
    useEffect(() => {
        if (visible) {
            loadEmployeeDetail();
        }
    }, [visible, gradeUid, status]);

    // 表格欄位定義
    const columns: ColumnsType<InsuranceGradeEmployeeDetail> = [
        {
            title: '員工編號',
            dataIndex: 'userId',
            key: 'userId',
            width: 120,
        },
        {
            title: '員工姓名',
            dataIndex: 'userName',
            key: 'userName',
            width: 120,
        },
        {
            title: '部門',
            dataIndex: 'departmentName',
            key: 'departmentName',
            width: 150,
        },
        {
            title: '職稱',
            dataIndex: 'positionName',
            key: 'positionName',
            width: 120,
        },
        {
            title: '生效日期',
            dataIndex: 'startDate',
            key: 'startDate',
            width: 120,
            render: (date: string) => date ? dayjs(date).format(DATE_FORMAT) : '-',
        },
        {
            title: '結束日期',
            dataIndex: 'endDate',
            key: 'endDate',
            width: 120,
            render: (date: string | null) => date ? dayjs(date).format(DATE_FORMAT) : '-',
        },
        {
            title: '狀態',
            dataIndex: 'statusDescription',
            key: 'statusDescription',
            width: 120,
            render: (statusDesc: string) => {
                let color = 'blue';
                if (statusDesc === '長期生效') color = 'green';
                else if (statusDesc === '時間範圍內生效') color = 'cyan';
                else if (statusDesc === '待生效') color = 'orange';

                return <Tag color={color}>{statusDesc}</Tag>;
            },
        },
    ];

    // 取得彈窗標題
    const getModalTitle = () => {
        const statusText = status === 'current' ? '目前生效' : '待生效';
        return `${getInsuranceTypeName(insuranceType)} - ${statusText}員工列表（月投保薪資：${monthlySalary.toLocaleString()}）`;
    };

    return (
        <Modal
            title={getModalTitle()}
            open={visible}
            onCancel={onClose}
            footer={null}
            width={1000}
            destroyOnClose
        >
            <div style={{ marginBottom: '16px' }}>
                <Space>
                    <Typography.Text strong>
                        共 {employeeList.length} 位員工
                    </Typography.Text>
                </Space>
            </div>

            <Spin spinning={loading}>
                <Table
                    columns={columns}
                    dataSource={employeeList}
                    rowKey={(record) => record.userId}
                    scroll={{ x: 800 }}
                    size="small"
                    pagination={{
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total} 筆`,
                        pageSize: 10,
                    }}
                />
            </Spin>
        </Modal>
    );
} 