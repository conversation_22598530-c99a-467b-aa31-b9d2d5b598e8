using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IEquipmentTypeService
    {
        /// <summary>
        /// 取得設備類型資料
        /// </summary>
        /// <param name="_equipmentTypeId">設備類型編號</param>
        /// <returns>設備類型資料列表</returns>
        Task<List<EquipmentTypeDTO>> GetEquipmentTypeAsync(string _equipmentTypeId = "");

        /// <summary>
        /// 新增設備類型
        /// </summary>
        /// <param name="_data">設備類型資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddEquipmentTypeAsync(EquipmentTypeDTO _data);

        /// <summary>
        /// 編輯設備類型
        /// </summary>
        /// <param name="_data">設備類型資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditEquipmentTypeAsync(EquipmentTypeDTO _data);

        /// <summary>
        /// 刪除設備類型
        /// </summary>
        /// <param name="_data">設備類型資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteEquipmentTypeAsync(EquipmentTypeDTO _data);

        /// <summary>
        /// 取得設備類型詳細資料
        /// </summary>
        /// <param name="_equipmentTypeId">設備類型編號</param>
        /// <returns>設備類型詳細資料</returns>
        Task<EquipmentTypeDTO> GetEquipmentTypeDetailAsync(string _equipmentTypeId);
    }
}