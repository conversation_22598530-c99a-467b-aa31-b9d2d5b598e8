using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class DependentService : IDependentService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly EmployeeClass _employeeClass;
        private readonly ICurrentUserService _currentUserService;

        public DependentService(
            ERPDbContext context,
            Baseform baseform,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
        }

        public async Task<List<DependentDTO>> GetDependentListAsync(string userId)
        {
            try
            {
                return await _context.Pas_Dependent
                    .Where(h => h.userId == userId && h.IsDeleted != true)
                    .OrderByDescending(h => h.UpdateTime)
                    .Select(h => new DependentDTO
                    {
                        uid = h.uid,
                        userId = h.userId,
                        dependentRocId = h.dependentRocId,
                        dependentName = h.dependentName,
                        dependentBirthday = _baseform.TimestampToDateStr(h.dependentBirthday),
                        dependentRelationType = h.dependentRelationType,
                        dependentRelationTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_deptypedata, h.dependentRelationType),
                        remark = h.remark,
                        CreateTime = h.CreateTime,
                        CreateUserId = h.CreateUserId,
                        UpdateTime = h.UpdateTime,
                        UpdateUserId = h.UpdateUserId
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得扶養資料錯誤", ex);
            }
        }

        public async Task<DependentDTO> GetDependentDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_Dependent
                    .Where(h => h.uid == uid && h.IsDeleted != true)
                    .Select(h => new DependentDTO
                    {
                        uid = h.uid,
                        userId = h.userId,
                        dependentRocId = h.dependentRocId,
                        dependentName = h.dependentName,
                        dependentRelationType = h.dependentRelationType,
                        dependentBirthday = _baseform.TimestampToDateStr(h.dependentBirthday),
                        dependentRelationTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_deptypedata, h.dependentRelationType),
                        remark = h.remark,
                        CreateTime = h.CreateTime,
                        CreateUserId = h.CreateUserId,
                        UpdateTime = h.UpdateTime,
                        UpdateUserId = h.UpdateUserId
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得扶養資料明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddDependentAsync(DependentDTO data)
        {
            var list_msg_check = CheckDependentInput(data, "add");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newDependent = new Dependent
                {
                    uid = Guid.NewGuid().ToString(),
                    userId = data.userId,
                    dependentRocId = data.dependentRocId,
                    dependentName = data.dependentName,
                    dependentBirthday = _baseform.DateStrToTimestamp(data.dependentBirthday),
                    dependentRelationType = data.dependentRelationType,
                    remark = data.remark,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_Dependent.AddAsync(newDependent);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "扶養資料新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"扶養資料新增失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditDependentAsync(DependentDTO data)
        {
            var list_msg_check = CheckDependentInput(data, "edit");
            if (list_msg_check.Count > 0)
                return (false, list_msg_check[0]);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Dependent.FirstOrDefaultAsync(h => h.uid == data.uid && h.IsDeleted != true);
                if (exist == null)
                    return (false, "找不到對應扶養資料");

                //exist.userId = data.userId; //編輯不用改依附者ID
                exist.dependentRocId = data.dependentRocId;
                exist.dependentName = data.dependentName;
                exist.dependentBirthday = _baseform.DateStrToTimestamp(data.dependentBirthday);
                exist.dependentRelationType = data.dependentRelationType;
                exist.remark = data.remark;
                exist.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                exist.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "扶養資料編輯成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"扶養資料編輯失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteDependentAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var exist = await _context.Pas_Dependent.FirstOrDefaultAsync(h => h.uid == uid && h.IsDeleted != true);
                if (exist == null)
                    return (false, "資料已刪除或不存在");

                exist.IsDeleted = true;
                exist.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                exist.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "扶養資料刪除成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"扶養資料刪除失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public List<string> CheckDependentInput(DependentDTO data, string _mode)
        {
            var list = new List<string>();

            // 檢核item.
            bool idNoExists = false; // 身分證存在.

            if (_mode == "add")
            {
                // 新增需判斷是否已有扶養該身分證資料
                idNoExists = _context.Pas_Dependent
                    .Any(x =>
                    x.dependentRocId == data.dependentRocId &&
                    x.userId == data.userId &&
                    x.IsDeleted != true);
            }

            if (idNoExists)
            {
                list.Add("該眷屬資料已存在，請直接新增投保起迄範圍");
            }

            if (string.IsNullOrWhiteSpace(data.userId))
                list.Add("依附者編號獲取錯誤");

            // 根據實際需求可加上必要欄位檢查
            if (string.IsNullOrWhiteSpace(data.dependentRocId))
                list.Add("請輸入被扶養者身分證字號");
            if (string.IsNullOrWhiteSpace(data.dependentName))
                list.Add("請輸入被扶養者姓名");

            return list;
        }
    }
}
