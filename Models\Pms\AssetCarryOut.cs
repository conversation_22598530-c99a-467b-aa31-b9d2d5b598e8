using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 資產攜出作業
    /// </summary>
    [Table("PmsAssetCarryOut")]
    public class AssetCarryOut : ModelBaseEntity
    {
        /// <summary>
        /// 攜出單號
        /// </summary>
        [Key]
        public Guid CarryOutId { get; set; }

        /// <summary>
        /// 攜出申請單號
        /// </summary>
        [Required]
        [StringLength(50)]
        [Comment("攜出申請單號")]
        public string CarryOutNo { get; set; }

        /// <summary>
        /// 財產編號
        /// </summary>
        [Required]
        public Guid AssetId { get; set; }

        /// <summary>
        /// 攜出申請人
        /// </summary>
        [Required]
        [StringLength(50)]
        [Comment("攜出申請人")]
        [ForeignKey("UserId")]
        public string ApplicantId { get; set; }

        /// <summary>
        /// 攜出申請日期
        /// </summary>
        [Required]
        [Comment("攜出申請日期")]
        public long ApplicationDate { get; set; }

        /// <summary>
        /// 預計攜出日期
        /// </summary>
        [Required]
        [Comment("預計攜出日期")]
        public long PlannedCarryOutDate { get; set; }

        /// <summary>
        /// 預計歸還日期
        /// </summary>
        [Required]
        [Comment("預計歸還日期")]
        public long PlannedReturnDate { get; set; }

        /// <summary>
        /// 實際攜出日期
        /// </summary>
        [Comment("實際攜出日期")]
        public long? ActualCarryOutDate { get; set; }

        /// <summary>
        /// 實際歸還日期
        /// </summary>
        [Comment("實際歸還日期")]
        public long? ActualReturnDate { get; set; }

        /// <summary>
        /// 攜出目的
        /// </summary>
        [Required]
        [StringLength(500)]
        [Comment("攜出目的")]
        public string Purpose { get; set; }

        /// <summary>
        /// 攜出地點
        /// </summary>
        [Required]
        [StringLength(200)]
        [Comment("攜出地點")]
        public string Destination { get; set; }

        /// <summary>
        /// 攜出狀態
        /// </summary>
        [Required]
        [StringLength(20)]
        [Comment("攜出狀態")]
        public string Status { get; set; }

        /// <summary>
        /// 審核人員
        /// </summary>
        [StringLength(50)]
        [Comment("審核人員")]
        [ForeignKey("UserId")]
        public string? ApproverId { get; set; }

        /// <summary>
        /// 審核日期
        /// </summary>
        [Comment("審核日期")]
        public long? ApprovalDate { get; set; }

        /// <summary>
        /// 審核意見
        /// </summary>
        [StringLength(500)]
        [Comment("審核意見")]
        public string? ApprovalComment { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        [StringLength(1000)]
        [Comment("備註")]
        public string? Notes { get; set; }

        /// <summary>
        /// 關聯的財產
        /// </summary>
        [ForeignKey("AssetId")]
        public virtual Asset Asset { get; set; }

        public AssetCarryOut()
        {
            CarryOutId = Guid.NewGuid();
            CarryOutNo = "";
            AssetId = Guid.Empty;
            ApplicantId = "";
            ApplicationDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            PlannedCarryOutDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            PlannedReturnDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            Purpose = "";
            Destination = "";
            Status = "PENDING"; // PENDING, APPROVED, REJECTED, CARRIED_OUT, RETURNED
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 資產攜出作業DTO
    /// </summary>
    public class AssetCarryOutDTO : ModelBaseEntityDTO
    {
        public Guid CarryOutId { get; set; }
        public string CarryOutNo { get; set; }
        public Guid AssetId { get; set; }
        public string AssetNo { get; set; }
        public string AssetName { get; set; }
        public string ApplicantId { get; set; }
        public string ApplicantName { get; set; }
        public long ApplicationDate { get; set; }
        public long PlannedCarryOutDate { get; set; }
        public long PlannedReturnDate { get; set; }
        public long? ActualCarryOutDate { get; set; }
        public long? ActualReturnDate { get; set; }
        public string Purpose { get; set; }
        public string Destination { get; set; }
        public string Status { get; set; }
        public string StatusName { get; set; }
        public string? ApproverId { get; set; }
        public string? ApproverName { get; set; }
        public long? ApprovalDate { get; set; }
        public string? ApprovalComment { get; set; }
        public string? Notes { get; set; }
        public bool IsDeleted { get; set; }

        public AssetCarryOutDTO()
        {
            CarryOutId = Guid.NewGuid();
            CarryOutNo = "";
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            ApplicantId = "";
            ApplicantName = "";
            ApplicationDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            PlannedCarryOutDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            PlannedReturnDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            Purpose = "";
            Destination = "";
            Status = "PENDING";
            StatusName = "待審核";
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 攜出狀態枚舉
    /// </summary>
    public static class AssetCarryOutStatus
    {
        public const string PENDING = "PENDING";        // 待審核
        public const string APPROVED = "APPROVED";      // 已核准
        public const string REJECTED = "REJECTED";      // 已駁回
        public const string CARRIED_OUT = "CARRIED_OUT"; // 已攜出
        public const string RETURNED = "RETURNED";      // 已歸還
        public const string OVERDUE = "OVERDUE";        // 逾期未還

        public static Dictionary<string, string> GetStatusNames()
        {
            return new Dictionary<string, string>
            {
                { PENDING, "待審核" },
                { APPROVED, "已核准" },
                { REJECTED, "已駁回" },
                { CARRIED_OUT, "已攜出" },
                { RETURNED, "已歸還" },
                { OVERDUE, "逾期未還" }
            };
        }
    }

    /// <summary>
    /// 攜出申請批次處理DTO
    /// </summary>
    public class AssetCarryOutBatchDTO
    {
        public List<Guid> CarryOutIds { get; set; }
        public string Action { get; set; } // APPROVE, REJECT, CARRY_OUT, RETURN
        public string? ApprovalComment { get; set; }
        public string OperatorId { get; set; }

        public AssetCarryOutBatchDTO()
        {
            CarryOutIds = new List<Guid>();
            Action = "";
            OperatorId = "";
        }
    }

    /// <summary>
    /// 攜出統計DTO
    /// </summary>
    public class AssetCarryOutStatisticsDTO
    {
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public int RejectedApplications { get; set; }
        public int CarriedOutAssets { get; set; }
        public int ReturnedAssets { get; set; }
        public int OverdueAssets { get; set; }
        public List<AssetCarryOutDTO> RecentApplications { get; set; }
        public List<AssetCarryOutDTO> OverdueList { get; set; }

        public AssetCarryOutStatisticsDTO()
        {
            RecentApplications = new List<AssetCarryOutDTO>();
            OverdueList = new List<AssetCarryOutDTO>();
        }
    }
}