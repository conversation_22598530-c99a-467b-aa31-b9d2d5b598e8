using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("學歷資料管理")]
    public class EducationController : ControllerBase
    {
        private readonly IEducationService _Interface;

        public EducationController(IEducationService educationService)
        {
            _Interface = educationService;
        }

        [HttpGet]
        [Route("GetAll/{_userid}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有學歷資料列表")]
        public async Task<IActionResult> GetEducationList(string _userid)
        {
            var result = await _Interface.GetEducationListAsync(_userid);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得學歷明細", Description = "依uid取得學歷明細")]
        public async Task<IActionResult> GetEducationDetail(string _uid)
        {
            var result = await _Interface.GetEducationDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增學歷", Description = "新增學歷資料")]
        public async Task<IActionResult> AddEducation([FromForm] EducationDTO _data, [FromForm] List<IFormFile> files)
        {
            var (result, msg) = await _Interface.AddEducationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯學歷", Description = "編輯學歷資料")]
        public async Task<IActionResult> EditEducation([FromForm] EducationDTO _data, [FromForm] List<IFormFile> files)
        {
            var (result, msg) = await _Interface.EditEducationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除學歷", Description = "刪除學歷資料")]
        public async Task<IActionResult> DeleteEducation([FromBody] string _uid)
        {
            var (result, msg) = await _Interface.DeleteEducationAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}