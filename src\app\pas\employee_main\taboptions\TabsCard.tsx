import { Collapse, Menu, Badge, Typography, theme } from 'antd';
import {
    mainOptions,
    employeeOptions,
    adjustOptions,
    salaryOptions,
    TabOption
} from '@/app/pas/employee_main/taboptions/constants/tabOptions';
import {
    UserOutlined,
    AuditOutlined,
    DollarOutlined,
    TeamOutlined
} from '@ant-design/icons';

const { Title } = Typography;
const { useToken } = theme;

type TabsCardProps = {
    selectedKey: string;
    onSelect: (key: string) => void;
};

const renderMenuItems = (options: TabOption[]) => {
    return options.map((option) => ({
        key: option.key,
        label: (
            <Badge
                status="default"
                text={option.label}
                style={{ fontSize: '14px' }}
            />
        ),
    }));
};

const TabsCard: React.FC<TabsCardProps> = ({ selectedKey, onSelect }) => {
    const { token } = useToken();

    const menuStyle = {
        background: 'transparent',
        border: 'none',
    };

    const collapseStyle = {
        background: token.colorBgContainer,
        border: `1px solid ${token.colorBorderSecondary}`,
        borderRadius: token.borderRadiusLG,
        marginBottom: 16,
    };

    const items = [
        {
            key: '1',
            label: <Title level={5}><UserOutlined /> 主檔資料</Title>,
            children: (
                <Menu
                    style={menuStyle}
                    selectedKeys={[selectedKey]}
                    onClick={({ key }) => onSelect(key)}
                    items={renderMenuItems(mainOptions)}
                />
            ),
        },
        {
            key: '2',
            label: <Title level={5}><TeamOutlined /> 人事資料維護</Title>,
            children: (
                <Menu
                    style={menuStyle}
                    selectedKeys={[selectedKey]}
                    onClick={({ key }) => onSelect(key)}
                    items={renderMenuItems(employeeOptions)}
                />
            ),
        },
        {
            key: '3',
            label: <Title level={5}><AuditOutlined /> 調整資料維護</Title>,
            children: (
                <Menu
                    style={menuStyle}
                    selectedKeys={[selectedKey]}
                    onClick={({ key }) => onSelect(key)}
                    items={renderMenuItems(adjustOptions)}
                />
            ),
        },
        {
            key: '4',
            label: <Title level={5}><DollarOutlined /> 薪資資料維護</Title>,
            children: (
                <Menu
                    style={menuStyle}
                    selectedKeys={[selectedKey]}
                    onClick={({ key }) => onSelect(key)}
                    items={renderMenuItems(salaryOptions)}
                />
            ),
        },
    ];

    return (
        <div style={{
            padding: 16,
            background: token.colorBgLayout,
            borderRadius: token.borderRadiusLG,
        }}>
            <Collapse
                defaultActiveKey={['1', '2', '3', '4']}
                expandIconPosition="end"
                style={collapseStyle}
                items={items}
            />
        </div>
    );
};

export default TabsCard;
