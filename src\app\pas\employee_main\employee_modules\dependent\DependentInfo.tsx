import React, { useEffect, useState } from "react";
import {
    Table,
    Button,
    Modal,
    Form,
    Input,
    DatePicker,
    Popconfirm,
    Card,
    message,
    Row,
    Col,
    Typography,
    Space,
    Divider
} from "antd";
import dayjs from "dayjs";

import {
    getDependentList,
    addDependent,
    editDependent,
    deleteDependent,
    Dependent,
    createEmptyDependent,
    getDependentDetail,
} from "@/services/pas/DependentService";
import { getDepTypeOptions } from '@/services/pas/OptionParameterService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    IdcardOutlined,
    TeamOutlined,
    FileTextOutlined,
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    PlusOutlined,
    CalendarOutlined,
    UserOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type DependentInfoProps = {
    userId: string;
    active: boolean;
};


const DependentInfo: React.FC<DependentInfoProps> = ({ userId, active }) => {
    // flag.
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);

    // data.
    const [dependentList, setDependentList] = useState<Dependent[]>([]);
    const [dependentDetail, setDependentDetail] = useState<Dependent | null>(null);

    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [form] = Form.useForm();

    useEffect(() => {
        if (active) fetchList();
    }, [active, userId]);

    const fetchList = async () => {
        setLoading(true);
        setErrorMsg('');
        try {
            const res = await getDependentList(userId);
            if (res.success && res.data) {
                setDependentList(res.data);
            } else {
                message.error(res.message || "載入失敗");
                setErrorMsg(res.message || "載入失敗");
            }
        } catch (err) {
            message.error("發生錯誤，無法載入");
            setErrorMsg("發生錯誤，無法載入");
        } finally {
            setLoading(false);
        }
    };

    const handleAddNew = () => {
        setDependentDetail(null);
        form.resetFields();
        form.setFieldsValue({
            ...createEmptyDependent()
        });
        setIsModalOpen(true);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const handleRowClick = async (uid: string) => {
        setModalLoading(true);
        try {
            const { success, data, message: msg } = await getDependentDetail(uid);
            if (success && data) {
                setDependentDetail(data);
                form.resetFields();
                form.setFieldsValue({
                    ...createEmptyDependent(),
                    userId: data.userId,
                    uid: data.uid,
                    dependentRocId: data.dependentRocId,
                    dependentName: data.dependentName,
                    dependentBirthday: data.dependentBirthday ? dayjs(data.dependentBirthday) : null,
                    dependentRelationType: data.dependentRelationType,
                    dependentRelationTypeName: data.dependentRelationTypeName,
                    remark: data.remark,
                });
                setIsModalOpen(true);
            } else {
                message.error(msg || '載入學歷資料失敗');
            }
        } catch (error: any) {
            console.error(error);
            message.error(error.message || '載入學歷資料時發生錯誤');
        } finally {
            setModalLoading(false);
        }
    };


    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const payload: Dependent = {
                ...values,
                userId: userId,
                dependentBirthday: values.dependentBirthday
                    ? values.dependentBirthday.format("YYYY-MM-DD")
                    : "",
            };

            setModalLoading(true);
            let res;
            if (dependentDetail) {
                res = await editDependent({ ...payload, uid: dependentDetail.uid });
            } else {
                res = await addDependent(payload);
            }

            if (res.success && res.data?.result) {
                message.success(dependentDetail ? "更新成功" : "新增成功");
                setIsModalOpen(false);
                fetchList();
            } else {
                message.error(res.data?.msg || "操作失敗");
            }
        } catch (err) {
            if (!(err as any)?.errorFields) {
                message.error("儲存發生錯誤");
            }
        } finally {
            setModalLoading(false);
        }
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deleteDependent(uid);
            if (res.success && res.data?.result) {
                message.success("刪除成功");
                fetchList();
            } else {
                message.error(res.data?.msg || "刪除失敗");
            }
        } catch {
            message.error("刪除發生錯誤");
        }
    };

    if (!active) return null;
    if (errorMsg) return <div style={{ color: 'red', textAlign: 'center', padding: 20 }}>錯誤：{errorMsg}</div>;

    return (
        <>
            <Card
                title={
                    <Space>
                        <TeamOutlined />
                        <Title level={4} style={{ margin: 0 }}>扶養資料</Title>
                    </Space>
                }
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => handleAddNew()}
                        style={{ borderRadius: '6px' }}
                    >
                        新增扶養資料
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Table
                    rowKey="uid"
                    dataSource={dependentList}
                    columns={[
                        {
                            title: '身分證字號',
                            dataIndex: 'dependentRocId',
                            render: (text) => (
                                <Space>
                                    <IdcardOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '姓名',
                            dataIndex: 'dependentName',
                            render: (text) => (
                                <Space>
                                    <UserOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '生日',
                            render: (_, record) => (
                                <Space>
                                    <CalendarOutlined style={{ color: '#722ed1' }} />
                                    <Text>{record.dependentBirthday || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '關係',
                            dataIndex: 'dependentRelationTypeName',
                            render: (text) => (
                                <Space>
                                    <TeamOutlined style={{ color: '#eb2f96' }} />
                                    <Text>{text}</Text>
                                </Space>
                            )
                        },
                        {
                            title: '操作',
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => handleRowClick(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            ),
                        }
                    ]}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space>
                                    <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>備註：</Text>
                                    <Text>{record.remark || '-'}</Text>
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!record.remark,
                    }}
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record.uid),
                    })}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    pagination={{ pageSize: 10, showSizeChanger: false }}
                />
            </Card>

            <Modal
                title={
                    <Space>
                        <TeamOutlined />
                        <Title level={5} style={{ margin: 0 }}>
                            {dependentDetail
                                ? `編輯扶養資料（${form.getFieldValue("dependentName") || ""}）`
                                : "新增扶養資料"}
                        </Title>
                    </Space>
                }
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                onOk={handleSubmit}
                confirmLoading={modalLoading}
                width={800}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <Form layout="vertical" form={form} className="mt-4">
                    {/* 基本資料 */}
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <IdcardOutlined />
                                基本資料
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>身分證字號</Text>}
                                    name="dependentRocId"
                                    rules={[{ required: true, message: "請輸入身分證字號" }]}
                                >
                                    <Input
                                        placeholder="請輸入身分證字號"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>姓名</Text>}
                                    name="dependentName"
                                    rules={[{ required: true, message: "請輸入姓名" }]}
                                >
                                    <Input
                                        placeholder="請輸入姓名"
                                        style={{ borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 關係資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <TeamOutlined />
                                關係資訊
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>關係</Text>}
                                    name="dependentRelationType"
                                    rules={[{ required: true, message: "請選擇依附者關係" }]}
                                >
                                    <ApiSelect fetchOptions={getDepTypeOptions} placeholder="請選擇關係" />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={24} md={12}>
                                <Form.Item
                                    label={<Text strong>生日</Text>}
                                    name="dependentBirthday"
                                >
                                    <DatePicker
                                        style={{ width: "100%", borderRadius: '6px' }}
                                        placeholder="請選擇生日"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>

                    {/* 其他資訊 */}
                    <Divider style={{ margin: '24px 0' }} />
                    <div className="form-section">
                        <Title level={5}>
                            <Space>
                                <FileTextOutlined />
                                其他資訊
                            </Space>
                        </Title>
                        <Row gutter={[24, 16]}>
                            <Col span={24}>
                                <Form.Item
                                    label={<Text strong>備註</Text>}
                                    name="remark"
                                >
                                    <Input.TextArea
                                        rows={3}
                                        placeholder="請輸入備註"
                                        style={{ resize: 'none', borderRadius: '6px' }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}
        </>
    );
};

export default DependentInfo;
