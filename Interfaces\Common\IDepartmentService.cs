﻿using FAST_ERP_Backend.Models.Common;
using System;
using System.Collections.Generic;
using System.Data;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IDepartmentService
    {
        Task<List<DepartmentDTO>> GetDepartmentAsync(string _departmentId = "");
        Task<(bool, string)> AddDepartmentAsync(DepartmentDTO _data);
        Task<(bool, string)> EditDepartmentAsync(DepartmentDTO _data);
        Task<(bool, string)> DeleteDepartmentAsync(DepartmentDTO _data);
    }

}
