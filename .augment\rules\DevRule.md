---
type: "always_apply"
---

🐳 開發環境和工具
Docker 環境配置
後端容器: fast_erp_backend_dev (端口 7136 HTTP, 7137 HTTPS)
前端容器: fast_erp_frontend_dev (端口 3000)
容器操作: 使用 docker exec -it /bin/bash
環境驗證: 開發前確認容器正常運行
端口訪問: 前端通過 http://localhost:3000 外部訪問
🎯 核心開發原則
代碼語言: 所有變數名、方法名、類別名使用英文
註解語言: 所有註解、錯誤訊息、用戶介面文字使用繁體中文
文檔語言: 技術文檔、API 文檔使用繁體中文
命名規範: 遵循 IMS 模組模式 (如 PartnerID 而非 PartnerId)
效能考量: 注意資料庫查詢效能和記憶體使用
共用組件: 優先使用和創建可重用組件
測試代碼設計: 撰寫獨立的測試代碼，提供清楚的移除方法
系統性修復: 一次性修復所有編譯錯誤
違反這些指令，特別是 API 整合驗證步驟，將導致交付品不完整或無法運行。
🏗️ 架構一致性要求
參考模組: 嚴格以 Item 模組為範本 (檔案結構、命名、UI 模式、響應式設計)
基礎類別: 使用 ModelBaseEntity 和 ModelBaseEntityDTO
DTO 策略: 採用單一統一 DTO 架構 (如 Partner、Item 模組)
資料庫管理
EF 重建: 修改 Model 後執行 reset-ef-database.bat
ID 模式: 資料庫使用 nvarchar(100)，模型定義為 Guid
編譯測試: Docker 容器內編譯成功
文檔標準
位置: E:\Project\FastERP\fast_erp_backend\doc
格式: Markdown + Mermaid 圖表
內容: 功能概述、API 說明、測試指南、測試代碼移除指南 (繁體中文)
檔名: 英文檔名加底線 (如 Partner_Enhancement_Documentation.md)
交付檢查清單
代碼在 Docker 容器內編譯成功
功能完整實現並遵循 Item 模組模式
前後端 API 整合測試通過
測試代碼獨立且提供移除指南
響應式設計實現並測試
繁體中文技術文檔完整