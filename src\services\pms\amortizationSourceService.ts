import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 攤提來源
export interface AmortizationSource {
    amortizationSourceId: string;
    assetId: string;
    departmentId: string;
    sourceName: string;
    description: string;
    amount: number;
    createTime: number | null;
    createUserId: string | null;
    createUserName: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    updateUserName: string | null;
}

// 獲取攤提來源列表
export async function getAmortizationSources(): Promise<ApiResponse<AmortizationSource[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAmortizationSources, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取攤提來源列表失敗",
            data: [],
        };
    }
}

// 新增攤提來源
export async function createAmortizationSource(data: Partial<AmortizationSource>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAmortizationSource, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增攤提來源失敗",
        };
    }
}

// 更新攤提來源
export async function updateAmortizationSource(data: Partial<AmortizationSource>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAmortizationSource, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新攤提來源失敗",
        };
    }
}

// 刪除攤提來源
export async function deleteAmortizationSource(data: Partial<AmortizationSource>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAmortizationSource, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除攤提來源失敗",
        };
    }
}

