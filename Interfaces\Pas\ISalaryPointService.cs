using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface ISalaryPointService
    {
        Task<List<SalaryPointDTO>> GetListAsync();
        Task<SalaryPointDTO?> GetDetailAsync(string uid);
        Task<(bool, string)> AddAsync(SalaryPointDTO dto);
        Task<(bool, string)> EditAsync(SalaryPointDTO dto);
        Task<(bool, string)> DeleteAsync(string uid);
        Task<decimal?> GetAmountByDateAsync(string dateStr);


    }
}
