import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 財產狀態
export interface AssetStatus {
    assetStatusId: string;
    assetStatusNo: string;
    name: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 獲取財產狀態列表
export async function getAssetStatuses(): Promise<ApiResponse<AssetStatus[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssetStatuses, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產狀態列表失敗",
            data: [],
        };
    }
}

// 獲取財產狀態詳情
export async function getAssetStatusDetail(id: string): Promise<ApiResponse<AssetStatus>> {
    try {
        const response = await httpClient(`${apiEndpoints.getAssetStatusDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產狀態詳情失敗",
            data: undefined,
        };
    }
}

// 新增財產狀態
export async function createAssetStatus(data: Partial<AssetStatus>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAssetStatus, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增財產狀態失敗",
        };
    }
}

// 更新財產狀態
export async function updateAssetStatus(data: Partial<AssetStatus>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAssetStatus, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新財產狀態失敗",
        };
    }
}

// 刪除財產狀態
export async function deleteAssetStatus(data: Partial<AssetStatus>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAssetStatus, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除財產狀態失敗",
        };
    }
}
