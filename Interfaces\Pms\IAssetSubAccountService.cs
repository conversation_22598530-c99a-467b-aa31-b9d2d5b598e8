using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetSubAccountService
    {
        /// <summary>
        /// 取得所有財產子目
        /// </summary>
        /// <returns>所有財產子目</returns>
        Task<List<AssetSubAccountDTO>> GetAllAsync();

        /// <summary>
        /// 取得財產子目ById
        /// </summary>
        /// <param name="id">財產子目編號</param>
        /// <returns>財產子目</returns>
        Task<string> GetByIdAsync(string id);

        /// <summary>
        /// 取得財產子目ByAssetAccountId
        /// </summary>
        /// <param name="assetAccountId">資產科目編號</param>
        /// <returns>財產子目列表</returns>
        Task<List<AssetSubAccountDTO>> GetByAssetAccountIdAsync(string assetAccountId);

        /// <summary>
        /// 新增財產子目
        /// </summary>
        /// <param name="assetSubAccount">財產子目</param>
        /// <returns>新增的財產子目</returns>
        Task<(bool, string)> AddAsync(AssetSubAccountDTO assetSubAccount);

        /// <summary>
        /// 更新財產子目
        /// </summary>
        /// <param name="assetSubAccount">財產子目</param>
        /// <returns>更新後的財產子目</returns>
        Task<(bool, string)> UpdateAsync(AssetSubAccountDTO assetSubAccount);

        /// <summary>
        /// 刪除財產子目
        /// </summary>
        /// <param name="_data">財產子目</param>
        /// <returns>是否刪除成功</returns> 
        Task<(bool, string)> DeleteAsync(AssetSubAccountDTO _data);
    }
}