using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 附屬設備
    /// </summary>
    public class AccessoryEquipment : ModelBaseEntity
    {
        [Key]
        [Comment("附屬設備編號")]
        public Guid AccessoryEquipmentId { get; set; } // 附屬設備編號

        [Comment("設備編號")]
        public string EquipmentNo { get; set; } // 設備編號

        [Comment("設備名稱")]
        public string EquipmentName { get; set; } // 設備名稱

        [Comment("設備類型")]
        public string EquipmentType { get; set; } // 設備類型

        [Comment("規格/型號")]
        public string Specification { get; set; } // 規格/型號

        [Comment("購入日期")]
        public long? PurchaseDate { get; set; } // 購入日期

        [Comment("購入價格")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; } // 購入價格

        [Comment("財產編號")]
        [ForeignKey("Asset")]
        public Guid AssetId { get; set; } // 財產編號

        public virtual Asset Asset { get; set; }

        [Comment("使用狀態")]
        [Column(TypeName = "nvarchar(100)")]
        public string UsageStatus { get; set; } // 使用狀態

        [Comment("備註")]
        public string Remarks { get; set; } // 備註

        public AccessoryEquipment()
        {
            AccessoryEquipmentId = Guid.NewGuid();
            AssetId = Guid.Empty;
            EquipmentNo = "";
            EquipmentName = "";
            EquipmentType = "";
            Specification = "";
            PurchaseDate = 0;
            PurchasePrice = 0;
            AssetId = Guid.Empty;
            UsageStatus = "";
            Remarks = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class AccessoryEquipmentDTO : ModelBaseEntityDTO
    {
        public Guid AccessoryEquipmentId { get; set; } // 附屬設備ID
        public Guid AssetId { get; set; } // 資產ID
        public string AssetNo { get; set; } // 財產編號
        public string AssetName { get; set; } // 資產名稱
        public string EquipmentNo { get; set; } // 設備編號
        public string EquipmentName { get; set; } // 設備名稱
        public string EquipmentType { get; set; } // 設備類型
        public string Specification { get; set; } // 規格/型號
        public long? PurchaseDate { get; set; } // 購入日期
        public decimal PurchasePrice { get; set; } // 購入價格
        public string UsageStatus { get; set; } // 使用狀態
        public string Remarks { get; set; } // 備註
        public string? CreateUserName { get; set; } // 建立者名稱
        public string? UpdateUserName { get; set; } // 更新者名稱
        public string? DeleteUserName { get; set; } // 刪除者名稱

        public AccessoryEquipmentDTO()
        {
            AccessoryEquipmentId = Guid.NewGuid();
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            EquipmentNo = "";
            EquipmentName = "";
            EquipmentType = "";
            Specification = "";
            PurchaseDate = 0;
            PurchasePrice = 0;
            UsageStatus = "";
            Remarks = "";
            CreateUserId = null;
            CreateUserName = null;
            UpdateUserId = null;
            UpdateUserName = null;
            DeleteUserId = null;
            DeleteUserName = null;
            CreateTime = null;
            UpdateTime = null;
            DeleteTime = null;
            IsDeleted = false;
        }
    }
}