/* 財產位置變動單頁面樣式 */

.container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.pageHeader {
    background: white;
    padding: 16px 24px;
    margin-bottom: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.pageTitle {
    margin: 0;
    color: #262626;
    font-size: 20px;
    font-weight: 600;
}

/* 搜尋區域 */
.searchSection {
    background: white;
    padding: 20px;
    margin-bottom: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.searchForm {
    margin-bottom: 0;
}

.searchActions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
}

/* 統計區域 */
.statisticsSection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.statisticCard {
    background: white;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    text-align: center;
}

.statisticValue {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
}

.statisticLabel {
    color: #8c8c8c;
    font-size: 14px;
}

.statisticPending {
    color: #fa8c16;
}

.statisticApproved {
    color: #52c41a;
}

.statisticRejected {
    color: #ff4d4f;
}

.statisticCompleted {
    color: #1890ff;
}

/* 表格區域 */
.tableSection {
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.tableHeader {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tableTitle {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.tableActions {
    display: flex;
    gap: 8px;
}

.tableContent {
    padding: 0;
}

/* 批次操作 */
.batchActions {
    padding: 16px 20px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.batchInfo {
    color: #1890ff;
    font-weight: 500;
}

.batchButtons {
    display: flex;
    gap: 8px;
}

/* 表單模態框 */
.formModal {
    /* 樣式由 MODAL_CONFIG 控制 */
}

.formContent {
    max-height: 70vh;
    overflow-y: auto;
}

.formSection {
    margin-bottom: 24px;
}

.formSectionTitle {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

/* 明細表格 */
.detailTable {
    margin-top: 16px;
}

.detailTableHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.detailTableTitle {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
}

.addDetailButton {
    /* 按鈕樣式 */
}

/* 財產選擇模態框 */
.assetSelectionModal {
    /* 由 MODAL_CONFIG 控制 */
}

.assetSearchForm {
    padding: 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
}

.assetTable {
    /* 表格樣式 */
}

/* 審核/執行模態框 */
.approvalModal,
.executionModal {
    /* 模態框樣式 */
}

.statusForm {
    padding: 20px 0;
}

/* 狀態標籤 */
.statusTag {
    font-weight: 500;
}

.statusPending {
    background-color: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
}

.statusApproved {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
}

.statusRejected {
    background-color: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
}

.statusCompleted {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

/* 變動項目標籤 */
.changeItemsTag {
    margin: 2px;
    font-size: 12px;
}

/* 工具提示 */
.tooltip {
    max-width: 300px;
}

/* 複製按鈕 */
.copyButton {
    margin-left: 4px;
    font-size: 12px;
}

/* 空狀態 */
.emptyState {
    padding: 40px;
    text-align: center;
}

.emptyIcon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
}

.emptyText {
    color: #8c8c8c;
    font-size: 14px;
}

/* 載入狀態 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

/* 錯誤狀態 */
.error {
    padding: 40px;
    text-align: center;
}

.errorIcon {
    font-size: 48px;
    color: #ff4d4f;
    margin-bottom: 16px;
}

.errorText {
    color: #ff4d4f;
    font-size: 14px;
    margin-bottom: 16px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .pageHeader {
        padding: 12px 16px;
    }

    .searchSection {
        padding: 16px;
    }

    .tableActions {
        flex-direction: column;
        gap: 8px;
    }

    .batchActions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .batchButtons {
        justify-content: center;
    }

    .statisticsSection {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .statisticsSection {
        grid-template-columns: 1fr;
    }

    .tableHeader {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .searchActions {
        justify-content: center;
    }
}

/* 打印樣式 */
@media print {
    .container {
        background: white;
        padding: 0;
    }

    .searchSection,
    .tableActions,
    .batchActions {
        display: none !important;
    }

    .tableSection {
        box-shadow: none;
        border: 1px solid #d9d9d9;
    }
}

/* 深色主題支援 */
@media (prefers-color-scheme: dark) {
    .container {
        background-color: #141414;
    }

    .pageHeader,
    .searchSection,
    .statisticCard,
    .tableSection {
        background: #1f1f1f;
        color: #fff;
    }

    .pageTitle,
    .tableTitle,
    .formSectionTitle,
    .detailTableTitle {
        color: #fff;
    }

    .statisticLabel {
        color: #8c8c8c;
    }

    .batchActions {
        background: #262626;
    }
}