// Cookie 相關的常數
const TOKEN_KEY = 'token';
const COOKIE_MAX_AGE = 86400; // 24小時

// 設置 cookie
export const setCookie = (name: string, value: string, maxAge: number = COOKIE_MAX_AGE) => {
    document.cookie = `${name}=${value}; path=/; max-age=${maxAge}`;
};

// 獲取 cookie
export const getCookie = (name: string): string | null => {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [cookieName, cookieValue] = cookie.split('=').map(c => c.trim());
        if (cookieName === name) {
            return cookieValue;
        }
    }
    return null;
};

// 刪除 cookie
export const removeCookie = (name: string) => {
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT`;
};

// Token 相關操作

// 設置 token
export const setToken = (token: string) => {
    setCookie(TOKEN_KEY, token);
};

// 獲取 token
export const getToken = (): string | null => {
    return getCookie(TOKEN_KEY);
};

// 刪除 token
export const removeToken = () => {
    removeCookie(TOKEN_KEY);
}; 