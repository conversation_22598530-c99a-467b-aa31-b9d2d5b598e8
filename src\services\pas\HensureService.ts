// Hensure 眷屬健保資料管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 眷屬健保資料型別
export interface Hensure {
    uid: string;
    userId: string;
    dependentRocId: string;
    dependentName: string;
    dependentRelationType: string;
    dependentRelationTypeName: string;
    healthInsStartDate: string; // timestamp 格式字串
    healthInsEndDate: string;   // timestamp 格式字串
    remark: string;
    isNewDependent: boolean;
}

// 建立空的眷屬健保資料
export const createEmptyHensure = (): Hensure => ({
    uid: '',
    userId: '',
    dependentRocId: '',
    dependentName: '',
    dependentRelationType: '',
    dependentRelationTypeName: '',
    healthInsStartDate: '',
    healthInsEndDate: '',
    remark: '',
    isNewDependent: true,
});

// 取得眷屬健保資料列表
export async function getHensureList(userId: string): Promise<ApiResponse<Hensure[]>> {
    return await httpClient(`${apiEndpoints.getHensureList}/${userId}`, {
        method: "GET",
    });
}

// 取得眷屬健保資料明細
export async function getHensureDetail(uid: string): Promise<ApiResponse<Hensure>> {
    return await httpClient(`${apiEndpoints.getHensureDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增眷屬健保資料
export async function addHensure(data: Partial<Hensure>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addHensure, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增眷屬健保資料失敗",
        };
    }
}

// 編輯眷屬健保資料
export async function editHensure(data: Partial<Hensure>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editHensure, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯眷屬健保資料失敗",
        };
    }
}

// 刪除眷屬健保資料
export async function deleteHensure(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteHensure, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除眷屬健保資料失敗",
        };
    }
}
