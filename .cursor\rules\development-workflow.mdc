---
description: 
globs: 
alwaysApply: false
---
# 開發流程與環境設置指南

## 開發環境設置
1. Node.js 版本要求：16.x 或更高
2. 使用 yarn 作為包管理器
3. 複製 `.env.example` 到 `.env.local` 並配置環境變量

## 開發命令
- `yarn dev` - 啟動開發服務器
- `yarn dev:https` - 啟動Https開發服務器
- `yarn build` - 構建生產版本
- `yarn start` - 運行生產版本
- `yarn lint` - 運行代碼檢查
- `yarn test` - 運行測試

## Docker 開發環境
1. 使用 [Dockerfile.dev](mdc:Dockerfile.dev) 進行開發環境容器化
2. 使用 Docker Compose 管理開發環境服務

## 分支管理
1. `main` - 主分支，保持穩定可部署狀態
2. `develop` - 開發分支，用於整合功能
3. `feature/*` - 功能分支
4. `hotfix/*` - 緊急修復分支

## 部署流程
1. 代碼提交到 `develop` 分支
2. 通過 CI/CD 流程進行測試和構建
3. 合併到 `main` 分支進行部署
4. 使用 [Dockerfile](mdc:Dockerfile) 構建生產容器

## 開發注意事項
1. 本地開發前先更新依賴
2. 遵循 ESLint 和 Prettier 配置
3. 提交前運行測試和代碼檢查
4. 保持文檔的及時更新

