using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// 財產資料-財產來源 關聯表
/// </summary>
[Table("Pms_AssetAssetSourceMapping", Schema = "dbo")]
public class AssetAssetSourceMapping : ModelBaseEntity
{
    [Key]
    public Guid Id { get; set; }

    [Comment("財產流水號")]
    public Guid AssetId { get; set; }

    [Comment("財產來源編號")]
    public Guid AssetSourceId { get; set; }

    [ForeignKey("AssetId")]
    public virtual Asset Asset { get; set; }

    [ForeignKey("AssetSourceId")]
    public virtual AssetSource AssetSource { get; set; }
}