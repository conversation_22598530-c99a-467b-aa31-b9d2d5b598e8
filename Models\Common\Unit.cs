using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 單位基本資料表
    /// </summary>
    public class Unit : ModelBaseEntity
    {
        [Key]
        [Comment("單位流水號")]
        public Guid UnitId { get; set; } // 單位流水號

        [Comment("單位編號")]
        [RegularExpression(@"^[0-9a-zA-Z]{1,10}$", ErrorMessage = "單位編號必須為1至10位英數字")]
        [StringLength(10, MinimumLength = 1, ErrorMessage = "單位編號必須為1至10位英數字")]
        [Column(TypeName = "nvarchar(10)")]
        [Required(ErrorMessage = "單位編號為必填欄位")]
        public string UnitNo { get; set; } // 單位編號

        [Comment("單位名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } // 單位名稱

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public Unit()
        {
            UnitId = Guid.NewGuid();
            UnitNo = "";
            Name = "";
            SortCode = 0;
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class UnitDTO : ModelBaseEntityDTO
    {
        public Guid UnitId { get; set; } // 單位流水號
        public string UnitNo { get; set; } // 單位編號
        public string Name { get; set; } // 單位名稱
        public int SortCode { get; set; } // 排序號碼

        public UnitDTO()
        {
            UnitId = Guid.Empty;
            UnitNo = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}