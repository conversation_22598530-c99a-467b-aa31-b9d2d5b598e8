﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using System.Threading.Tasks;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;
using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("系統參數管理")]
    public class SystemParametersItemController : ControllerBase
    {
        private readonly ISystemParametersItemService _SystemParametersItemService;
        private readonly IUsersService _usersService;
        public SystemParametersItemController(ISystemParametersItemService SystemParametersItemService, IUsersService usersService)
        {
            _SystemParametersItemService = SystemParametersItemService;
            _usersService = usersService;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        [HttpGet]
        [Route("GetSystemParametersItem")]
        [SwaggerOperation(Summary = "取得系統參數項目列表", Description = "取得系統參數項目列表")]
        public async Task<IActionResult> GetSystemParametersItem(string systemParametersId = "")
        {
            var paramList = await _SystemParametersItemService.GetSystemParametersItemAsync(systemParametersId);
            return Ok(paramList);
        }

        [HttpPost]
        [Route("AddSystemParametersItem")]
        [SwaggerOperation(Summary = "新增系統參數項目", Description = "新增系統參數項目資料")]
        public async Task<IActionResult> AddSystemParametersItem([FromBody] SystemParametersItemDTO paraItem)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersItemService.AddSystemParametersItemAsync(paraItem, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditSystemParametersItem")]
        [SwaggerOperation(Summary = "編輯系統參數項目", Description = "編輯系統參數項目資料")]
        public async Task<IActionResult> EditSystemParametersItem([FromBody] SystemParametersItemDTO paraItem)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersItemService.EditSystemParametersItemAsync(paraItem, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteSystemParametersItem")]
        [SwaggerOperation(Summary = "刪除系統參數項目", Description = "刪除系統參數項目資料")]
        public async Task<IActionResult> DeleteSystemParametersItem([FromBody] SystemParametersItemDTO paraItem)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersItemService.DeleteSystemParametersItemAsync(paraItem, tokenUid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("RestoreSystemParametersItem")]
        [SwaggerOperation(Summary = "還原系統參數項目", Description = "還原系統參數項目資料")]
        public async Task<IActionResult> RestoreSystemParametersItem([FromBody] SystemParametersItemDTO paraItem)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _SystemParametersItemService.RestoreSystemParametersItemAsync(paraItem, tokenUid);
            return Ok(new { result, msg });
        }
    }
}
