# Git 每日工作流程指南（新手工程師版）

這份指南幫助新手工程師快速學會使用 Git 進行一天的開發工作，特別適合 C# 專案（如 ASP.NET Core、資料庫表設計）。我們會以簡單步驟說明如何開始工作、提交代碼、暫存變更（使用 `git stash`）、與團隊協作，並提供進階學習指引。

---

## 使用者情境範例

假設你是新加入的 C# 開發者，今天（2025 年 4 月 17 日）需要：

1. 為一個功能（例如「新增 PartyTag 共享邏輯」）創建分支並開發。
2. 在開發中途暫存變更（使用 `git stash`），以更新主分支或切換任務。
3. 提交代碼給團隊審查，然後清理不再需要的分支。

以下是你的每日 Git 工作流程：

```bash
# 早上：開始工作，更新主分支
git checkout main
git pull origin main

# 創建新分支進行開發
git checkout -b feature/789-party-tags
echo "PartyTag 共享邏輯" >> PartyTag.cs
git add .
# commit message [type] 請置換 feat、fix、refactor等，請參考下一欄
git commit -m "[type]: PartyTag 共享功能"

# 中午：暫存變更以切換任務或更新 main
git stash push -m "[type]: PartyTag 進度"
git checkout main
git pull origin main
git checkout feature/789-party-tags
git stash pop

# 下午：推送代碼，提交審查
git push origin feature/789-party-tags
# 在 GitHub 上創建 Pull Request（PR）

# 晚上：PR 合併後清理
git checkout main
git pull origin main
git branch -d feature/789-party-tags
git push origin --delete feature/789-party-tags
```
## [type]
```
type 代表提交 Commit 的類別，以下為使用慣例：

- feat：新增或修改功能（feature）
- fix：修補 bug（bug fix）
- docs：文件（documentation）
- style：格式
    - 不影響程式碼運行的變動，例如：white-space, formatting, missing semi colons
- refactor：重構
    - 不是新增功能，也非修補 bug 的程式碼變動
- perf：改善效能（improves performance）
- test：增加測試（when adding missing tests）
- chore：maintain
    - 不影響程式碼運行，建構程序或輔助工具的變動，例如修改 config、Grunt Task 任務管理工具
- revert：撤銷回覆先前的 commit
    - 例如：`revert：type(scope):subject`
```
---

## 每日 Git 工作流程（基礎）

以下是新手工程師一天使用 Git 的核心步驟，包含 `git stash` 的使用。每個步驟簡單易懂，適合初學者。

### 1. 早上：開始工作，更新主分支

**為什麼？**\
確保你的本地 `main` 分支與遠端（團隊的代碼庫）同步，避免使用過舊的代碼。

**怎麼做？**

```bash
git checkout main
git pull origin main
```

- `git checkout main`：切換到 `main` 分支（穩定分支）。
- `git pull origin main`：下載遠端最新代碼。
- **注意**：如果有未提交的變更，Git 會提示錯誤，先執行「提交或暫存」。

### 2. 創建新分支進行開發

**為什麼？**\
每個任務（功能或修復）都應該在獨立分支上開發，保持 `main` 分支乾淨。

**怎麼做？**

```bash
git checkout -b feature/789-party-tags
```

- `feature/789-party-tags`：分支名稱，建議格式為 `feature/<issue號>-<描述>`。

- 範例：為「PartyTag 共享」創建分支：

  ```bash
  git checkout -b feature/789-party-tags
  ```

**然後？**\
開始編輯代碼，例如修改 `PartyTag.cs` 或新增 EF Core 遷移：

```bash
echo "public class PartyTag { ... }" >> PartyTag.cs
dotnet ef migrations add AddPartyTags
```

### 3. 提交你的變更

**為什麼？**\
保存你的代碼變更，記錄進度。

**怎麼做？**

```bash
git add .
git commit -m "新增 PartyTag 共享功能"
```

- `git add .`：將所有變更加入暫存區。
- `git commit -m "訊息"`：提交變更，訊息要清楚，例如「修復電話查詢」或「新增 PartyTag 邏輯」。
- **提示**：提交前用 `git status` 檢查哪些檔案被修改。

### 4. 中午：暫存變更（使用 `git stash`）

**為什麼？**\
在開發中途，你可能需要：

- 更新 `main` 分支以獲取團隊的新代碼。
- 切換到另一個分支處理緊急任務（例如修復 bug）。
- 暫時保存未完成的工作，稍後繼續。

**怎麼做？**

```bash
git stash push -m "PartyTag 進度"
```

- `git stash push`：將未提交的變更（工作目錄和暫存區）保存到暫存堆疊。

- `-m "訊息"`：為暫存添加描述，方便辨識。

- 範例：保存 `PartyTag` 功能的進度：

  ```bash
  git stash push -m "PartyTag 共享邏輯進度"
  ```

**然後？**\
執行其他操作，例如更新 `main` 或切換分支：

```bash
git checkout main
git pull origin main
```

**恢復暫存變更**： 回到原分支並恢復工作：

```bash
git checkout feature/789-party-tags
git stash pop
```

- `git stash pop`：恢復最新的暫存並從堆疊移除。

- **注意**：如果有衝突，Git 會提示，手動編輯衝突檔案，然後：

  ```bash
  git add <衝突檔案>
  git commit
  ```

**檢查暫存**： 如果你有多個暫存，查看列表：

```bash
git stash list
```

選擇特定暫存恢復：

```bash
git stash apply stash@{0}
```

- `apply` 保留暫存，`pop` 移除暫存。

### 5. 下午：推送代碼，提交 Pull Request (PR)

**為什麼？**\
將你的分支推送到遠端（如 GitHub），讓團隊審查代碼。**Pull Request (PR)** 是請求將你的代碼合併到 `main` 的方式。

**怎麼做？**

1. 推送分支：

   ```bash
   git push origin feature/789-party-tags
   ```

   - 第一次推送新分支，設置上游：

     ```bash
     git push --set-upstream origin feature/789-party-tags
     ```

2. 創建 PR：

   - 打開 GitHub，找到你的倉庫。
   - 點擊「Compare & pull request」（通常會自動提示）。
   - 設置：
     - **源分支**：`feature/789-party-tags`
     - **目標分支**：`main`
     - **標題**：例如「Add PartyTag shared logic (#789)」
     - **描述**：說明變更，例如「新增 PartyTag 共享功能，更新 EF Core 模型」。
   - 提交 PR。

3. 等待審查：

   - 團隊可能要求修改，提交新變更：

     ```bash
     git add .
     git commit -m "根據審查意見修改"
     git push origin feature/789-party-tags
     ```

### 6. 晚上：清理分支

**為什麼？**\
PR 合併後，刪除分支以保持倉庫整潔。

**怎麼做？**

```bash
git checkout main
git pull origin main
git branch -d feature/789-party-tags
git push origin --delete feature/789-party-tags
```

- `git branch -d`：刪除本地分支。
- `git push origin --delete`：刪除遠端分支。
- **提示**：GitHub 合併 PR 後通常有「Delete branch」按鈕，可直接點擊。

---

## 常見問題（新手必看）

1. **誤在 main 分支改動怎麼辦？**

   - 創建新分支並帶走變更：

     ```bash
     git checkout -b feature/789-party-tags
     ```

2. **推送失敗？**

   - 確保分支正確推送：

     ```bash
     git push --set-upstream origin feature/789-party-tags
     ```

3. **代碼衝突怎麼處理？**

   - 手動編輯衝突檔案（Git 會標記 `<<<<<<<`），然後：

     ```bash
     git add <衝突檔案>
     git commit
     ```

4. **忘記暫存了什麼？**

   - 查看暫存列表：

     ```bash
     git stash list
     ```

   - 檢查特定暫存內容：

     ```bash
     git stash show stash@{0}
     ```

5. **不知道我在哪個分支？**

   - 檢查：

     ```bash
     git branch
     ```

     當前分支會有 `*` 標記。

---

## 進階學習指引

當你熟悉以上流程後，可以探索以下進階主題，提升 Git 技能：

### 1. 管理多個分支

- **情境**：同時處理多個 issue（例如 `PartyTag` 和 `Phone` 查詢）。

- **學習**：

  - 列出所有分支：

    ```bash
    git branch -a
    ```

  - 清理已合併分支：

    ```bash
    git branch --merged | grep -v "\*" | xargs git branch -d
    ```

- **指引**：閱讀 Git Branching Strategies 了解 Gitflow 或簡化模型。

### 2. 進階 Stash 使用

- **情境**：管理多個暫存或包含未追蹤檔案。

- **學習**：

  - 暫存未追蹤檔案（新檔案）：

    ```bash
    git stash --include-untracked
    ```

  - 刪除特定暫存：

    ```bash
    git stash drop stash@{0}
    ```

  - 清除所有暫存：

    ```bash
    git stash clear
    ```

- **指引**：查閱 Git Stash Documentation.

### 3. 處理複雜衝突

- **情境**：EF Core 遷移檔案（`Migrations/`）衝突。

- **學習**：

  - 使用 `git rebase` 同步主分支：

    ```bash
    git rebase main
    ```

  - 解決衝突後：

    ```bash
    git add <衝突檔案>
    git rebase --continue
    ```

- **指引**：查閱 Git Rebase Documentation.

### 4. 自動化與 CI/CD

- **情境**：在 PR 上運行 C# 測試（例如 `dotnet test`）。

- **學習**：

  - 配置 GitHub Actions，範例：

    ```yaml
    name: CI
    on: [pull_request]
    jobs:
      build:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v3
          - name: Setup .NET
            uses: actions/setup-dotnet@v3
            with:
              dotnet-version: '8.0.x'
          - name: Run tests
            run: dotnet test
    ```

- **指引**：參考 GitHub Actions for .NET.

### 5. 資料庫遷移管理

- **情境**：多分支修改 `PartyTag` 或 `Phone` 表，導致遷移衝突。

- **學習**：

  - 檢查遷移：

    ```bash
    dotnet ef migrations script
    ```

  - 合併後重新生成遷移：

    ```bash
    dotnet ef migrations add ConsolidatedMigration
    ```

- **指引**：閱讀 EF Core Migrations.

---

## 針對 C# 專案的提示

- **資料庫變更**：每次分支提交 EF Core 遷移：

  ```bash
  dotnet ef migrations add <MigrationName>
  ```

- **測試**：提交 PR 前運行：

  ```bash
  dotnet test
  ```

- **分支命名**：用 issue 編號，例如 `feature/789-party-tags` 或 `bugfix/456-phone-query`。

- **PR 描述**：記錄資料庫結構變更（例如 `PartyTag` 表新增欄位）。

- **暫存使用**：在修改 `PartyTag.cs` 或遷移檔案時，常用 `git stash` 保存進度，例如：

  ```bash
  git stash push -m "PartyTag 模型修改"
  ```

---

## 總結

作為新手工程師，你的一天 Git 工作流程包括：

1. 更新 `main` 分支。
2. 為任務創建新分支。
3. 提交變更，必要時使用 `git stash` 暫存。
4. 推送分支並提交 PR。
5. PR 合併後清理分支。

`git stash` 是你的好幫手，當需要暫停工作或切換任務時，使用它保存進度。每天重複這個流程，熟悉後可探索進階主題如多分支管理或 CI/CD。遇到問題時，使用 `git status`、`git branch` 或 `git stash list` 檢查狀態，或向團隊求助！