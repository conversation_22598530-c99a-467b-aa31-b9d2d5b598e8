import { useEffect, useState } from 'react';
import { Button, Spin, Cascader, message, Table, Form, Input, DatePicker, Popconfirm, Row, Col, Card, Space, Typography, Select } from 'antd';
import dayjs from 'dayjs';
import {
    getPerformancePointRecordList,
    getPerformancePointRecordDetail,
    addPerformancePointRecord,
    editPerformancePointRecord,
    deletePerformancePointRecord,
    PerformancePointRecord,
    createEmptyPerformancePointRecord
} from '@/services/pas/PerformancePoint/PerformancePointRecordService';
import { getGroupTypeCascaderOptions, CascaderOptionDTO } from '@/services/pas/PerformancePoint/PerformancePointTypeService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import PerformancePointRecordEditor from './PerformancePointRecordEditor';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined, CalendarOutlined, TeamOutlined, IdcardOutlined, FileTextOutlined, MedicineBoxOutlined, InfoCircleOutlined, FilterOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

type Props = {
    userId: string;
    active: boolean;
};

const PerformancePointRecordInfo: React.FC<Props> = ({ userId, active }) => {
    const [list, setList] = useState<PerformancePointRecord[]>([]);
    const [filteredList, setFilteredList] = useState<PerformancePointRecord[]>([]);
    const [loading, setLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    // 篩選狀態
    const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>([
        dayjs().startOf('year'), // 當年1月1日
        dayjs().endOf('year')    // 當年12月31日
    ]);
    const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
    const [groupOptions, setGroupOptions] = useState<string[]>([]);

    //編輯元件管理
    const [editorOpen, setEditorOpen] = useState(false);
    const [editorMode, setEditorMode] = useState<'add' | 'edit'>('add');
    const [editingUid, setEditingUid] = useState<string | undefined>(undefined);

    useEffect(() => {
        if (active) {
            fetchList();
        }
    }, [active, userId]);

    // 當篩選條件改變時，重新篩選資料
    useEffect(() => {
        filterData();
    }, [list, dateRange, selectedGroup]);

    const fetchList = async () => {
        setLoading(true);
        try {
            const res = await getPerformancePointRecordList(userId);
            if (res.success && res.data) {
                setList(res.data);
                // 提取所有群組選項
                const groups = Array.from(new Set(res.data.map(item => item.groupName).filter(Boolean)));
                setGroupOptions(groups);
            } else {
                message.error(res.message || '載入資料失敗');
            }
        } catch (err: any) {
            setErrorMsg(err.message || '載入失敗');
        } finally {
            setLoading(false);
        }
    };

    const filterData = () => {
        let filtered = [...list];

        // 日期篩選
        if (dateRange && dateRange[0] && dateRange[1]) {
            filtered = filtered.filter(item => {
                if (!item.pointDate) return false;
                const itemDate = dayjs(item.pointDate);
                return itemDate.isAfter(dateRange[0]!.subtract(1, 'day')) &&
                    itemDate.isBefore(dateRange[1]!.add(1, 'day'));
            });
        }

        // 群組篩選
        if (selectedGroup) {
            filtered = filtered.filter(item => item.groupName === selectedGroup);
        }

        setFilteredList(filtered);
    };

    const handleDateRangeChange = (dates: any) => {
        setDateRange(dates);
    };

    const handleGroupChange = (value: string) => {
        setSelectedGroup(value === 'all' ? null : value);
    };

    const clearFilters = () => {
        setDateRange([
            dayjs().startOf('year'), // 重置為當年1月1日
            dayjs().endOf('year')    // 重置為當年12月31日
        ]);
        setSelectedGroup(null);
    };

    const handleDelete = async (uid: string) => {
        try {
            const res = await deletePerformancePointRecord(uid);
            if (res.success && res.data?.result) {
                message.success('刪除成功');
                fetchList();
            } else {
                message.error(res.message || '刪除失敗');
            }
        } catch (err: any) {
            message.error(err.message || '刪除失敗');
        }
    };

    //編輯元件控制
    const openAddEditor = () => {
        setEditorMode('add');
        setEditingUid(undefined);
        setEditorOpen(true);
    };

    const openEditEditor = (uid: string) => {
        setEditorMode('edit');
        setEditingUid(uid);
        setEditorOpen(true);
    };

    if (!active) return null;
    if (errorMsg) return <div style={{ color: 'red', textAlign: 'center', padding: 20 }}>錯誤：{errorMsg}</div>;

    return (
        <>
            <Card
                title={
                    <Space>
                        <MedicineBoxOutlined />
                        <Title level={4} style={{ margin: 0 }}>績效點數紀錄</Title>
                    </Space>
                }
                loading={loading}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={openAddEditor}
                        style={{ borderRadius: '6px' }}
                    >
                        新增點數紀錄
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                {/* 篩選區域 */}
                <Card
                    size="small"
                    style={{ marginBottom: 16, background: '#fafafa' }}
                    title={
                        <Space>
                            <FilterOutlined />
                            <Text strong>篩選條件</Text>
                        </Space>
                    }
                >
                    <Row gutter={[16, 16]} align="middle">
                        <Col xs={24} sm={12} md={8}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text>日期範圍：</Text>
                                <RangePicker
                                    value={dateRange}
                                    onChange={handleDateRangeChange}
                                    style={{ width: '100%' }}
                                    placeholder={['開始日期', '結束日期']}
                                />
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={8}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text>群組：</Text>
                                <Select
                                    value={selectedGroup || 'all'}
                                    onChange={handleGroupChange}
                                    style={{ width: '100%' }}
                                    placeholder="選擇群組"
                                >
                                    <Option value="all">全部群組</Option>
                                    {groupOptions.map(group => (
                                        <Option key={group} value={group}>{group}</Option>
                                    ))}
                                </Select>
                            </Space>
                        </Col>
                        <Col xs={24} sm={24} md={8}>
                            <Space>
                                <Button onClick={clearFilters}>
                                    清除篩選
                                </Button>
                                <Text type="secondary">
                                    共 {filteredList.length} 筆資料
                                </Text>
                            </Space>
                        </Col>
                    </Row>
                </Card>

                <Table
                    rowKey="uid"
                    dataSource={filteredList}
                    rowClassName={(record) =>
                        record.uid === deleteUid ? 'row-deleting-pulse' : ''
                    }
                    columns={[
                        {
                            title: "日期",
                            dataIndex: "pointDate",
                            sorter: (a, b) => {
                                if (!a.pointDate && !b.pointDate) return 0;
                                if (!a.pointDate) return -1;
                                if (!b.pointDate) return 1;
                                return dayjs(a.pointDate).valueOf() - dayjs(b.pointDate).valueOf();
                            },
                            sortDirections: ['ascend', 'descend'],
                            render: (text) => (
                                <Space>
                                    <CalendarOutlined style={{ color: '#1890ff' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: "群組",
                            dataIndex: "groupName",
                            sorter: (a, b) => (a.groupName || '').localeCompare(b.groupName || ''),
                            sortDirections: ['ascend', 'descend'],
                            render: (text) => (
                                <Space>
                                    <TeamOutlined style={{ color: '#52c41a' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: "點數類型",
                            dataIndex: "pointName",
                            sorter: (a, b) => (a.pointName || '').localeCompare(b.pointName || ''),
                            sortDirections: ['ascend', 'descend'],
                            render: (text) => (
                                <Space>
                                    <IdcardOutlined style={{ color: '#722ed1' }} />
                                    <Text>{text || '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: "點數",
                            dataIndex: "point",
                            sorter: (a, b) => {
                                const pointA = typeof a.point === 'number' ? a.point : 0;
                                const pointB = typeof b.point === 'number' ? b.point : 0;
                                return pointA - pointB;
                            },
                            sortDirections: ['ascend', 'descend'],
                            render: (text) => (
                                <Space>
                                    <FileTextOutlined style={{ color: '#fa8c16' }} />
                                    <Text>{text ?? '-'}</Text>
                                </Space>
                            )
                        },
                        {
                            title: "操作",
                            render: (_, record) => (
                                <Space onClick={(e) => e.stopPropagation()}>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => openEditEditor(record.uid)}
                                    >
                                        編輯
                                    </Button>
                                    <Popconfirm
                                        title={
                                            <div>
                                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                                <Text>確定要刪除此筆資料嗎？</Text>
                                            </div>
                                        }
                                        onConfirm={() => setDeleteUid(record.uid)}
                                        okText="確認"
                                        cancelText="取消"
                                        okButtonProps={{ danger: true }}
                                    >
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                        >
                                            刪除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            ),
                        }
                    ]}
                    onRow={(record) => ({
                        onClick: () => openEditEditor(record.uid),
                    })}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
                    }}
                    expandable={{
                        expandedRowRender: (record) => (
                            <div style={{
                                padding: '16px 24px',
                                background: 'rgba(0, 0, 0, 0.02)',
                                borderRadius: '8px',
                                margin: '0 24px'
                            }}>
                                <Space>
                                    <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    <Text strong>備註：</Text>
                                    <Text>{record.remark || '-'}</Text>
                                </Space>
                            </div>
                        ),
                        rowExpandable: (record) => !!record.remark,
                    }}
                    style={{ marginTop: '16px' }}
                />
            </Card>


            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        await handleDelete(deleteUid);
                        setDeleteUid(null);
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}

            <PerformancePointRecordEditor
                userId={userId}
                mode={editorMode}
                uid={editingUid}
                open={editorOpen}
                onClose={() => setEditorOpen(false)}
                onSuccess={fetchList}
            />
        </>
    );
};

export default PerformancePointRecordInfo;
