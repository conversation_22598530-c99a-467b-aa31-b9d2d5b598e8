﻿using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models
{
    public static class ModelBuilderExtensions
    {

        public static void ConfigureSystemMenuRelationships(this ModelBuilder modelBuilder)
        {
            // SystemMenu 與 RolesPermissions 的關係
            modelBuilder.Entity<SystemMenu>()
                .HasMany(m => m.RolesPermissions)     // 一個 SystemMenu 可以有多個 RolesPermissions
                .WithOne(r => r.SystemMenu)           // 每個 RolesPermissions 對應一個 SystemMenu
                .HasForeignKey(r => r.SystemMenuId);  // 使用 SystemMenuId 作為外鍵
                
            // Roles 與 RolesPermissions 的關係
            modelBuilder.Entity<Roles>()
                .HasMany(r => r.RolesPermissions)
                .WithOne(rp => rp.Roles)
                .HasForeignKey(rp => rp.RolesId)
                .HasPrincipalKey(r => r.RolesId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
