import React from "react";
import { But<PERSON>, Space, Tag, Tooltip, Descriptions } from "antd";
import { EditOutlined, FileTextOutlined } from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import { AssetDetail } from "@/services/pms/assetService";
import { AssetStatus } from "@/services/pms/assetStatusService";
import { AssetAccount } from "@/services/pms/assetAccountService";
import { AssetSubAccount } from "@/services/pms/assetSubAccountService";
import { Department } from "@/services/common/departmentService";
import { Unit } from "@/services/common/unitService";
import { User } from "@/services/common/userService";
import { StorageLocation } from "@/services/pms/storageLocationService";
import { STATUS_COLORS } from "./interface";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { formatTWCurrency } from "@/utils/formatUtils";

// 獲取表格列定義
export const getColumns = (
  handleView: (record: AssetDetail) => void,
  handleEdit: (record: AssetDetail) => void,
  assetStatusOptions: AssetStatus[],
  assetAccounts: AssetAccount[],
  assetSubAccounts: AssetSubAccount[],
  departments: Department[],
  units: Unit[],
  custodians: User[],
  storageLocations: StorageLocation[]
): ColumnsType<AssetDetail> => {
  return [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "財產編號",
      dataIndex: ["asset", "assetNo"],
      key: "assetNo",
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="財產編號">
                  {record.asset.assetNo}
                </Descriptions.Item>
                <Descriptions.Item label="財產名稱">
                  {record.asset.assetName}
                </Descriptions.Item>
                <Descriptions.Item label="財產科目">
                  {
                    assetAccounts.find(
                      (account) =>
                        account.assetAccountId === record.asset.assetAccountId
                    )?.assetAccountName
                  }
                </Descriptions.Item>
                <Descriptions.Item label="財產子目">
                  {
                    assetSubAccounts.find(
                      (subAccount) =>
                        subAccount.assetSubAccountId ===
                        record.asset.assetSubAccountId
                    )?.assetSubAccountName
                  }
                </Descriptions.Item>
                <Descriptions.Item label="取得日期">
                  {DateTimeExtensions.formatDateFromTimestamp(
                    record.asset.acquisitionDate
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="數量">
                  {record.asset.quantity +
                    "/" +
                    (units.find((u) => u.unitId === record.asset.unitId)
                      ?.name ||
                      record.asset.unitId ||
                      "-")}
                </Descriptions.Item>
                <Descriptions.Item label="購入金額">
                  {formatTWCurrency(record.asset.purchaseAmount || 0)}
                </Descriptions.Item>
                <Descriptions.Item label="補助金額">
                  {formatTWCurrency(record.asset.subsidyAmount || 0)}
                </Descriptions.Item>
                <Descriptions.Item label="所屬部門">
                  {departments.find(
                    (dep) => dep.departmentId === record.asset.departmentId
                  )?.name || "-"}
                </Descriptions.Item>
                {/* 當科目為土地時顯示權狀號碼 */}
                {assetAccounts
                  .find(
                    (account) =>
                      account.assetAccountId === record.asset.assetAccountId
                  )
                  ?.assetAccountName.includes("土地") && (
                  <Descriptions.Item label="權狀號碼">
                    {record.asset.certificateNo || "-"}
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="保管人">
                  {(() => {
                    const custodian = custodians.find(
                      (user) => user.userId === record.asset.custodianId
                    );
                    return custodian
                      ? `${custodian.name} (${custodian.account})`
                      : record.asset.custodianId || "-";
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label="存放地點">
                  {(() => {
                    const location = storageLocations.find(
                      (loc) =>
                        loc.storageLocationId === record.asset.storageLocationId
                    );
                    return location ? location.name : "-";
                  })()}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
          styles={{
            root: { maxWidth: "400px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "財產名稱",
      dataIndex: ["asset", "assetName"],
      key: "assetName",
    },
    {
      title: "取得日期",
      dataIndex: ["asset", "acquisitionDate"],
      key: "acquisitionDate",
      render: (text) => (
        <span>{DateTimeExtensions.formatDateFromTimestamp(text)}</span>
      ),
    },
    {
      title: "購入金額",
      dataIndex: ["asset", "purchaseAmount"],
      key: "purchaseAmount",
      render: (text) => <Tag color="red">{formatTWCurrency(text)}</Tag>,
    },
    {
      title: "部門",
      dataIndex: ["asset", "departmentName"],
      key: "departmentName",
    },
    {
      title: "保管人",
      dataIndex: ["asset", "custodianId"],
      key: "custodianId",
      render: (custodianId: string) => {
        const custodian = custodians.find(
          (user) => user.userId === custodianId
        );
        return custodian
          ? `${custodian.name} (${custodian.account})`
          : custodianId || "-";
      },
    },
    {
      title: "使用狀態",
      dataIndex: ["asset", "assetStatusId"],
      key: "assetStatusId",
      render: (statusId: string) => {
        const status = assetStatusOptions.find(
          (s) => s.assetStatusId === statusId
        );
        if (!status) return <Tag>未知狀態</Tag>;

        const statusName = status.name;
        const color = STATUS_COLORS[statusName] || "default";
        return <Tag color={color}>{statusName}</Tag>;
      },
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<FileTextOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
        </Space>
      ),
    },
  ];
};

// 獲取行動裝置顯示的欄位定義
export const getMobileColumns = (
  assetStatusOptions: AssetStatus[]
): ColumnsType<AssetDetail> => {
  return [
    {
      title: "財產編號",
      dataIndex: ["asset", "assetNo"],
      key: "assetNo",
    },
    {
      title: "財產名稱",
      dataIndex: ["asset", "assetName"],
      key: "assetName",
      ellipsis: true,
    },
    {
      title: "使用狀態",
      dataIndex: ["asset", "assetStatus"],
      key: "assetStatus",
      render: (statusId: string) => {
        const status = assetStatusOptions.find(
          (s) => s.assetStatusId === statusId
        );
        if (!status) return <Tag>未知狀態</Tag>;

        const statusName = status.name;
        const color = STATUS_COLORS[statusName] || "default";
        return <Tag color={color}>{statusName}</Tag>;
      },
    },
  ];
};
