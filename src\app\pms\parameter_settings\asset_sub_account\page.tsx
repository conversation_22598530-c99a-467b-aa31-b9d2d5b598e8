"use client";

/* 財產子目管理
  /app/pms/parameter_settings/asset_sub_account/page.tsx
*/

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Tag,
  Tooltip,
  Select,
  Typography,
  List,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  TagOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  AssetSubAccount,
  getAssetSubAccounts,
  createAssetSubAccount,
  updateAssetSubAccount,
  deleteAssetSubAccount,
} from "@/services/pms/assetSubAccountService";
import {
  AssetAccount,
  getAssetAccounts,
} from "@/services/pms/assetAccountService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { useAuth } from "@/contexts/AuthContext";

const { Text } = Typography;

// 定義階層數據結構
interface AssetSubAccountGroup {
  assetSubAccountId: string;
  assetSubAccountNo: string;
  assetAccountId: string;
  assetAccountNo: string;
  assetSubAccountName: string;
  isGroup: boolean;
  accountName: string;
  children: AssetSubAccount[];
  sortCode: number;
  createTime: number;
  createUserId: string;
  updateTime: number | null;
  updateUserId: string | null;
  deleteTime: number | null;
  deleteUserId: string | null;
  createUserName: string;
  updateUserName: string;
  deleteUserName: string;
  assetAccountName: string;
}

const AssetSubAccountPage: React.FC = () => {
  const [assetSubAccounts, setAssetSubAccounts] = useState<AssetSubAccount[]>(
    []
  );
  const [assetAccounts, setAssetAccounts] = useState<AssetAccount[]>([]);
  const [filteredAssetSubAccounts, setFilteredAssetSubAccounts] = useState<
    AssetSubAccountGroup[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAssetSubAccount, setEditingAssetSubAccount] =
    useState<AssetSubAccount | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState("");
  const [selectedAssetAccountId, setSelectedAssetAccountId] =
    useState<string>("");
  const { user } = useAuth();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [assetSubAccountToDelete, setAssetSubAccountToDelete] =
    useState<AssetSubAccount | null>(null);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [editForm] = Form.useForm();
  const [editingKey, setEditingKey] = useState<string>("");
  const [isMobile, setIsMobile] = useState(false);

  // 加載財產子目列表
  const loadAssetSubAccounts = async () => {
    setLoading(true);
    try {
      const response = await getAssetSubAccounts();
      if (response.success && response.data) {
        // 依照子目編號排序
        const sortedData = response.data.sort((a, b) =>
          a.assetSubAccountNo.localeCompare(b.assetSubAccountNo)
        );
        setAssetSubAccounts(sortedData);
        setFilteredAssetSubAccounts(getGroupedData());
      } else {
        notifyError("獲取財產子目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產子目列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 加載財產科目列表
  const loadAssetAccounts = async () => {
    try {
      const response = await getAssetAccounts();
      if (response.success && response.data) {
        const sortedData = response.data.sort((a, b) =>
          b.assetAccountNo.localeCompare(b.assetAccountNo)
        );
        setAssetAccounts(sortedData);
      } else {
        notifyError("獲取財產科目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產科目列表失敗", "請稍後再試");
    }
  };

  // 加載初始資料
  useEffect(() => {
    loadAssetAccounts();
    loadAssetSubAccounts();
  }, []);

  // 處理表格數據，按照資產科目進行分組
  const getGroupedData = () => {
    // 建立所有科目的分組
    const groupedMap = assetAccounts.reduce((acc, assetAccount) => {
      acc[assetAccount.assetAccountId] = {
        accountId: assetAccount.assetAccountId,
        accountName: assetAccount.assetAccountName,
        assetAccountNo: assetAccount.assetAccountNo,
        children: [],
      };
      return acc;
    }, {} as Record<string, { accountId: string; accountName: string; assetAccountNo: string; children: AssetSubAccount[] }>);

    // 將子目加入對應的科目中
    assetSubAccounts.forEach((item) => {
      if (groupedMap[item.assetAccountId]) {
        groupedMap[item.assetAccountId].children.push(item);
      }
    });

    // 轉換為array
    return Object.values(groupedMap)
      .map((group) => ({
        assetSubAccountId: group.accountId,
        assetSubAccountNo: "",
        assetAccountId: group.accountId,
        assetAccountNo: group.assetAccountNo,
        assetSubAccountName: "",
        isGroup: true,
        accountName: group.accountName,
        children: group.children.sort((a, b) => a.sortCode - b.sortCode),
        sortCode: 0,
        createTime: 0,
        createUserId: "",
        updateTime: null,
        updateUserId: null,
        deleteTime: null,
        deleteUserId: null,
        createUserName: "",
        updateUserName: "",
        deleteUserName: "",
        assetAccountName: group.accountName,
      }))
      .sort((a, b) => a.assetAccountNo.localeCompare(b.assetAccountNo));
  };

  // 更新展開的行
  const updateExpandedRows = (accounts: AssetSubAccountGroup[]) => {
    const keys = accounts.map((account) => account.assetSubAccountId);
    setExpandedRowKeys(keys);
  };

  // 處理搜尋和篩選
  useEffect(() => {
    let filteredAccounts = [...assetAccounts];
    let filteredSubAccounts = [...assetSubAccounts];

    // 按科目篩選
    if (selectedAssetAccountId) {
      filteredAccounts = filteredAccounts.filter(
        (item) => item.assetAccountId === selectedAssetAccountId
      );
      filteredSubAccounts = filteredSubAccounts.filter(
        (item) => item.assetAccountId === selectedAssetAccountId
      );
    }

    // 按搜尋文字篩選
    if (searchText) {
      const searchLower = searchText.toLowerCase();

      // 篩選科目
      filteredAccounts = filteredAccounts.filter(
        (account) =>
          account.assetAccountName.toLowerCase().includes(searchLower) ||
          account.assetAccountNo.toLowerCase().includes(searchLower)
      );

      // 篩選子目
      filteredSubAccounts = filteredSubAccounts.filter((item) =>
        item.assetSubAccountName.toLowerCase().includes(searchLower)
      );

      // 如果子目符合搜尋條件，也要包含其對應的科目
      const matchedAccountIds = new Set(
        filteredSubAccounts.map((item) => item.assetAccountId)
      );
      filteredAccounts = assetAccounts.filter(
        (account) =>
          matchedAccountIds.has(account.assetAccountId) ||
          filteredAccounts.some(
            (a) => a.assetAccountId === account.assetAccountId
          )
      );
    }

    // 建立分組資料
    const groupedMap: Record<
      string,
      {
        accountId: string;
        accountName: string;
        assetAccountNo: string;
        children: AssetSubAccount[];
      }
    > = {};

    // 先加入所有篩選後的科目
    filteredAccounts.forEach((account) => {
      groupedMap[account.assetAccountId] = {
        accountId: account.assetAccountId,
        accountName: account.assetAccountName,
        assetAccountNo: account.assetAccountNo,
        children: [],
      };
    });

    // 再加入對應的子目
    filteredSubAccounts.forEach((item) => {
      if (groupedMap[item.assetAccountId]) {
        groupedMap[item.assetAccountId].children.push(item);
      }
    });

    const groupedFiltered = Object.values(groupedMap)
      .map((group) => ({
        assetSubAccountId: group.accountId,
        assetSubAccountNo: "",
        assetAccountId: group.accountId,
        assetAccountNo: group.assetAccountNo,
        assetSubAccountName: "",
        isGroup: true,
        accountName: group.accountName,
        children: group.children.sort((a, b) => a.sortCode - b.sortCode),
        sortCode: 0,
        createTime: 0,
        createUserId: "",
        updateTime: null,
        updateUserId: null,
        deleteTime: null,
        deleteUserId: null,
        createUserName: "",
        updateUserName: "",
        deleteUserName: "",
        assetAccountName: group.accountName,
      }))
      .sort((a, b) => a.assetAccountNo.localeCompare(b.assetAccountNo));

    setFilteredAssetSubAccounts(groupedFiltered);

    // 如果有篩選條件，自動展開所有結果
    if (selectedAssetAccountId || searchText) {
      updateExpandedRows(groupedFiltered);
    } else {
      setExpandedRowKeys([]);
    }
  }, [searchText, selectedAssetAccountId, assetSubAccounts, assetAccounts]);

  // 檢查是否正在編輯
  const isEditing = (record: AssetSubAccount) =>
    record.assetSubAccountId === editingKey;

  // 開始編輯
  const edit = (record: AssetSubAccount) => {
    editForm.setFieldsValue({
      assetSubAccountName: record.assetSubAccountName,
    });
    setEditingKey(record.assetSubAccountId);
  };

  // 取消編輯
  const cancelEdit = () => {
    setEditingKey("");
  };

  // 儲存編輯
  const save = async (assetSubAccountId: string) => {
    try {
      const values = await editForm.validateFields();
      const currentRecord = assetSubAccounts.find(
        (item) => item.assetSubAccountId === assetSubAccountId
      );

      if (!currentRecord || !user?.userId) {
        notifyError("更新失敗", "無法取得完整資料");
        return;
      }

      const updateData = {
        ...currentRecord,
        assetSubAccountId,
        assetSubAccountName: values.assetSubAccountName,
        updateUserId: user.userId,
        updateTime: DateTimeExtensions.toTimestamp(new Date()),
      };

      const result = await updateAssetSubAccount(updateData);
      if (result.success) {
        notifySuccess("更新成功", "財產子目已更新");
        loadAssetSubAccounts();
        setEditingKey("");
      } else {
        notifyError("更新失敗", result.message || "請稍後再試");
      }
    } catch (errInfo) {
      console.error("儲存失敗:", errInfo);
    }
  };

  // 表格列定義
  const columns: ColumnsType<any> = [
    {
      key: "index",
      width: 80,
      render: (_, record, index) => {
        if (record.isGroup) return null;
        return <span>{index + 1}</span>;
      },
    },
    {
      title: "科目/子目",
      dataIndex: "assetSubAccountName",
      key: "assetSubAccountName",
      render: (text, record) => {
        if (record.isGroup) {
          return (
            <Typography.Title level={5} style={{ margin: 0 }}>
              <TagOutlined style={{ marginRight: 8, color: "#1890ff" }} />[
              {record.assetAccountNo}] {record.accountName}
              <Tag
                color={record.children.length > 0 ? "blue" : "default"}
                style={{ marginLeft: 8 }}
              >
                {record.children.length} 個子目
              </Tag>
            </Typography.Title>
          );
        }

        const editable = isEditing(record);
        return editable ? (
          <Form form={editForm} component={false}>
            <Space>
              <span style={{ width: 100 }}>[{record.assetSubAccountNo}]</span>
              <Form.Item
                name="assetSubAccountName"
                style={{ margin: 0, width: 200 }}
                rules={[{ required: true, message: "請輸入子目名稱" }]}
              >
                <Input placeholder="子目名稱" />
              </Form.Item>
            </Space>
          </Form>
        ) : (
          <span style={{ paddingLeft: 24 }}>
            [{record.assetSubAccountNo}] {text}
          </span>
        );
      },
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (text, record) => {
        if (record.isGroup) return null;
        return <span>{DateTimeExtensions.formatFromTimestamp(text)}</span>;
      },
    },
    {
      title: "建立者",
      dataIndex: "createUserName",
      key: "createUserName",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => {
        if (record.isGroup) return null;
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              type="link"
              onClick={() => save(record.assetSubAccountId)}
              style={{ marginRight: 8 }}
            >
              儲存
            </Button>
            <Button type="link" onClick={cancelEdit}>
              取消
            </Button>
          </Space>
        ) : (
          <Space size="middle">
            <Button
              type="link"
              disabled={editingKey !== ""}
              icon={<EditOutlined />}
              onClick={() => edit(record)}
            >
              編輯
            </Button>
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            >
              刪除
            </Button>
          </Space>
        );
      },
    },
  ];

  // 檢查子目編號是否已存在
  const isSubAccountIdExists = (id: string, excludeId?: string) => {
    return assetSubAccounts.some(
      (item) =>
        item.assetSubAccountId === id &&
        (!excludeId || item.assetSubAccountId !== excludeId)
    );
  };

  // 檢查"科目+子目"組合是否已存在
  const isAssetSubAccountCombinationExists = (
    assetAccountNo: string,
    subAccountNo: string,
    excludeNo?: string
  ) => {
    return assetSubAccounts.some(
      (item) =>
        item.assetAccountNo === assetAccountNo &&
        item.assetSubAccountNo === subAccountNo &&
        (!excludeNo || item.assetSubAccountNo !== excludeNo)
    );
  };

  // 檢查子目編號是否符合1-10位數格式
  const isTwoDigitNumber = (id: string) => {
    return /^[a-zA-Z0-9]{1,10}$/.test(id);
  };

  // 驗證子目編號
  const validateSubAccountNo = async (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error("請輸入子目編號"));
    }

    if (!isTwoDigitNumber(value)) {
      return Promise.reject(new Error("子目編號必須最少一位數"));
    }

    // 取得當前選擇的資產科目
    const assetAccountId = form.getFieldValue("assetAccountId");
    if (assetAccountId) {
      // 找到對應的科目資料
      const assetAccount = assetAccounts.find(
        (item) => item.assetAccountId === assetAccountId
      );
      if (assetAccount) {
        const exists = isAssetSubAccountCombinationExists(
          assetAccount.assetAccountNo,
          value,
          editingAssetSubAccount?.assetSubAccountNo
        );

        if (exists) {
          return Promise.reject(new Error("同一科目下的子目編號不能重複"));
        }
      }
    }

    return Promise.resolve();
  };

  // 監聽科目變更，重新驗證子目編號
  const handleAssetAccountChange = () => {
    // 如果子目編號已填寫，則重新驗證
    const subAccountId = form.getFieldValue("subAccountId");
    if (subAccountId) {
      form.validateFields(["subAccountId"]);
    }
  };

  // 處理編輯
  const handleEdit = (assetSubAccount: AssetSubAccount) => {
    setEditingAssetSubAccount(assetSubAccount);
    form.setFieldsValue({
      ...assetSubAccount,
      subAccountId: assetSubAccount.assetSubAccountId,
    });
    setIsModalVisible(true);
  };

  // 處理刪除
  const handleDelete = (assetSubAccount: AssetSubAccount) => {
    setAssetSubAccountToDelete(assetSubAccount);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!assetSubAccountToDelete || !user?.userId) return;

    try {
      const response = await deleteAssetSubAccount({
        assetSubAccountId: assetSubAccountToDelete.assetSubAccountId,
        deleteUserId: user.userId,
      });
      if (response.success) {
        notifySuccess("刪除成功", "財產子目已刪除");
        loadAssetSubAccounts();
        setDeleteModalVisible(false);
        setAssetSubAccountToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message || "請稍後再試");
      }
    } catch (error) {
      notifyError("刪除失敗", "請稍後再試");
    }
  };

  // 打開新增財產子目表單
  const showAddModal = () => {
    setEditingAssetSubAccount(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 取得財產科目列表，用於下拉選擇
  const getAssetAccountOptions = () => {
    return assetAccounts.map((account) => ({
      value: account.assetAccountId,
      label: `[${account.assetAccountNo}] ${account.assetAccountName}`,
      assetAccountNo: account.assetAccountNo,
      assetAccountName: account.assetAccountName,
    }));
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 確保科目資料存在
      const assetAccount = assetAccounts.find(
        (item) => item.assetAccountId === values.assetAccountId
      );
      // 如果找不到科目資料，且不是編輯模式，則提示錯誤
      if (!assetAccount && !editingAssetSubAccount) {
        notifyError("新增失敗", "找不到對應的科目資料");
        return;
      }
      // 提交資料
      const submitData = {
        ...values,
        assetSubAccountId: editingAssetSubAccount?.assetSubAccountId,
        assetSubAccountNo: editingAssetSubAccount
          ? editingAssetSubAccount.assetSubAccountNo
          : values.assetSubAccountNo,
        assetAccountNo:
          assetAccount?.assetAccountNo ||
          editingAssetSubAccount?.assetAccountNo,
        assetAccountName:
          assetAccount?.assetAccountName ||
          editingAssetSubAccount?.assetAccountName,
        createUserId: editingAssetSubAccount?.createUserId || user?.userId,
        updateUserId: editingAssetSubAccount ? user?.userId : null,
        deleteUserId: null,
      };

      if (editingAssetSubAccount) {
        // 更新財產子目
        const response = await updateAssetSubAccount(submitData);
        if (response.success) {
          notifySuccess("更新成功", "財產子目已更新");
          loadAssetSubAccounts();
        } else {
          notifyError("更新失敗", response.message || "請稍後再試");
        }
      } else {
        // 新增財產子目
        const response = await createAssetSubAccount(submitData);
        if (response.success) {
          notifySuccess("新增成功", "財產子目已新增");
          loadAssetSubAccounts();
        } else {
          notifyError("新增失敗", response.message || "請稍後再試");
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error("操作失敗:", error);
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 手機版列表
  const renderMobileList = () => {
    return (
      <>
        {filteredAssetSubAccounts.map((group) => (
          <div key={group.assetAccountId} style={{ marginBottom: "24px" }}>
            {/* 科目標題 */}
            <Typography.Title level={5} style={{ margin: "0 0 12px 0" }}>
              <TagOutlined style={{ marginRight: 8, color: "#1890ff" }} />[
              {group.assetAccountNo}] {group.accountName}
              <Tag
                color={group.children.length > 0 ? "blue" : "default"}
                style={{ marginLeft: 8 }}
              >
                {group.children.length} 個子目
              </Tag>
            </Typography.Title>

            {/* 子目列表 */}
            <List
              dataSource={group.children}
              renderItem={(item, index) => (
                <List.Item
                  style={{
                    padding: "12px",
                    borderBottom: "1px solid #f0f0f0",
                  }}
                >
                  <div style={{ width: "100%" }}>
                    {/* 標題列 */}
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginBottom: "8px",
                        gap: "8px",
                      }}
                    >
                      <div
                        style={{
                          width: 24,
                          height: 24,
                          background: "#f0f0f0",
                          borderRadius: "50%",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "14px",
                          flexShrink: 0,
                        }}
                      >
                        {index + 1}
                      </div>
                      <Text strong style={{ fontSize: "16px" }}>
                        {item.assetSubAccountName}
                      </Text>
                      <Text type="secondary" style={{ fontSize: "14px" }}>
                        ({item.assetSubAccountNo})
                      </Text>
                    </div>

                    {/* 時間資訊 */}
                    <div style={{ marginBottom: "8px" }}>
                      <Text type="secondary" style={{ fontSize: "13px" }}>
                        建立時間：
                        {DateTimeExtensions.formatFromTimestamp(
                          item.createTime
                        )}
                      </Text>
                    </div>

                    {/* 建立者 */}
                    <div style={{ marginBottom: "8px" }}>
                      <Text style={{ fontSize: "14px", color: "#666" }}>
                        建立者：{item.createUserId}
                      </Text>
                    </div>

                    {/* 操作按鈕列 */}
                    <div
                      style={{
                        display: "flex",
                        gap: "16px",
                        marginTop: "12px",
                      }}
                    >
                      <Button
                        type="link"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(item)}
                        style={{ padding: 0 }}
                      >
                        編輯
                      </Button>
                      <Button
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(item)}
                        style={{ padding: 0 }}
                      >
                        刪除
                      </Button>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </div>
        ))}
      </>
    );
  };

  return (
    <Card
      title="財產子目管理"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
        wrap
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增財產子目
        </Button>
        <Select
          placeholder="選擇財產科目"
          allowClear
          style={{ width: isMobile ? "100%" : "auto" }}
          onChange={(value) => setSelectedAssetAccountId(value || "")}
          value={selectedAssetAccountId || undefined}
          options={[{ value: "", label: "全部" }, ...getAssetAccountOptions()]}
        />
        <Input
          placeholder="搜尋子目名稱或所屬科目"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: "100%" }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredAssetSubAccounts}
          rowKey="assetSubAccountId"
          loading={loading}
          pagination={{
            defaultPageSize: 20,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
          expandable={{
            defaultExpandAllRows: false,
            expandedRowKeys: expandedRowKeys,
            onExpandedRowsChange: (keys: readonly React.Key[]) =>
              setExpandedRowKeys([...keys]),
          }}
        />
      )}

      <Modal
        title={editingAssetSubAccount ? "編輯財產子目" : "新增財產子目"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        maskClosable={false}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setIsModalVisible(false);
              form.resetFields();
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={form.submit}
          >
            {editingAssetSubAccount ? "儲存" : "新增"}
          </Button>,
        ]}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="assetAccountId"
            label="所屬科目："
            rules={[{ required: true, message: "請選擇所屬科目" }]}
          >
            <Select
              placeholder="請選擇所屬科目"
              onChange={handleAssetAccountChange}
              options={getAssetAccountOptions()}
            />
          </Form.Item>

          {!editingAssetSubAccount && (
            <Form.Item
              name="assetSubAccountNo"
              label="子目編號："
              rules={[
                { required: true, message: "請輸入子目編號" },
                { validator: validateSubAccountNo },
              ]}
              tooltip="同一科目下的子目編號不能重複"
            >
              <Input
                placeholder="請輸入子目編號"
                minLength={2}
                maxLength={10}
                style={{ width: "100%" }}
              />
            </Form.Item>
          )}

          <Form.Item
            name="assetSubAccountName"
            label="子目名稱："
            rules={[{ required: true, message: "請輸入子目名稱" }]}
          >
            <Input placeholder="請輸入子目名稱" maxLength={50} />
          </Form.Item>

          {editingAssetSubAccount && (
            <>
              <Form.Item label="建立時間：">
                <span>
                  {DateTimeExtensions.formatFromTimestamp(
                    editingAssetSubAccount.createTime
                  )}
                </span>
              </Form.Item>

              {editingAssetSubAccount.updateTime && (
                <Form.Item label="更新時間：">
                  <span>
                    {DateTimeExtensions.formatFromTimestamp(
                      editingAssetSubAccount.updateTime
                    )}
                  </span>
                </Form.Item>
              )}
            </>
          )}
        </Form>
      </Modal>

      {/* 刪除確認 Modal */}
      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setAssetSubAccountToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled:
            deleteConfirmText !==
            (assetSubAccountToDelete?.assetSubAccountName || ""),
        }}
      >
        <div>
          <p>
            請輸入
            <strong>「{assetSubAccountToDelete?.assetSubAccountName}」</strong>
            以確認刪除：
          </p>
          <Input
            placeholder="請輸入子目名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default AssetSubAccountPage;
