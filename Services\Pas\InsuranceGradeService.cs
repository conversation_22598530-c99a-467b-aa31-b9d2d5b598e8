using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Pas;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pas
{
    public class InsuranceGradeService : IInsuranceGradeService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public InsuranceGradeService(
            ERPDbContext context,
            Baseform baseform,
            ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<InsuranceGradeDTO>> GetInsuranceGradeListAsync(int _typeid)
        {
            try
            {
                var query = _context.Pas_InsuranceGrade
                    .Where(ig => ig.IsDeleted != true);

                query = query.Where(ig => ig.InsuranceType == _typeid);

                var gradeList = await query
                    .OrderBy(ig => ig.InsuranceType)
                    .ThenBy(ig => ig.MonthlySalary)
                    .Select(ig => new InsuranceGradeDTO
                    {
                        uid = ig.uid,
                        InsuranceType = ig.InsuranceType,
                        MonthlySalary = ig.MonthlySalary,
                        StartDate = _baseform.TimestampToDateStr(ig.StartDate),
                        EndDate = ig.EndDate.HasValue ? _baseform.TimestampToDateStr(ig.EndDate.Value) : null,
                        Remark = ig.Remark,
                        UpdateTime = ig.UpdateTime
                    }).ToListAsync();

                // 獲取員工統計資料並合併到級距列表中
                var employeeStats = await GetInsuranceGradeEmployeeStatsAsync(_typeid);

                foreach (var grade in gradeList)
                {
                    var stats = employeeStats.FirstOrDefault(s => s.GradeUid == grade.uid);
                    if (stats != default)
                    {
                        grade.CurrentEmployeeCount = stats.CurrentEmployeeCount;
                        grade.PendingEmployeeCount = stats.PendingEmployeeCount;
                        grade.TotalEmployeeCount = stats.TotalEmployeeCount;
                    }
                }

                return gradeList;
            }
            catch (Exception ex)
            {
                throw new Exception("取得保險級距資料錯誤", ex);
            }
        }

        public async Task<InsuranceGradeDTO> GetInsuranceGradeDetailAsync(string uid)
        {
            try
            {
                return await _context.Pas_InsuranceGrade
                    .Where(ig => ig.uid == uid && ig.IsDeleted != true)
                    .Select(ig => new InsuranceGradeDTO
                    {
                        uid = ig.uid,
                        InsuranceType = ig.InsuranceType,
                        MonthlySalary = ig.MonthlySalary,
                        StartDate = _baseform.TimestampToDateStr(ig.StartDate),
                        EndDate = ig.EndDate.HasValue ? _baseform.TimestampToDateStr(ig.EndDate.Value) : null,
                        Remark = ig.Remark,
                        UpdateTime = ig.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得保險級距明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddInsuranceGradeAsync(InsuranceGradeDTO data)
        {
            var list_msg_check = CheckInsuranceGradeInput(data, "add");
            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 檢查是否有重複的保險類型和月投保薪資組合
                var existingGrade = await _context.Pas_InsuranceGrade
                    .FirstOrDefaultAsync(ig => ig.InsuranceType == data.InsuranceType
                                            && ig.MonthlySalary == data.MonthlySalary
                                            && ig.IsDeleted != true);

                if (existingGrade != null)
                {
                    return (false, "此保險類型和月投保薪資組合已存在");
                }

                var newInsuranceGrade = new InsuranceGrade
                {
                    uid = Guid.NewGuid().ToString(),
                    InsuranceType = data.InsuranceType,
                    MonthlySalary = data.MonthlySalary,
                    StartDate = _baseform.DateStrToTimestamp(data.StartDate),
                    EndDate = !string.IsNullOrEmpty(data.EndDate) ? _baseform.DateStrToTimestamp(data.EndDate) : null,
                    Remark = data.Remark,
                    CreateTime = _baseform.DateStrToTimestamp(DateTime.Now.ToString("yyyy-MM-dd")),
                    CreateUserId = _currentUserService.UserId
                };

                await _context.Pas_InsuranceGrade.AddAsync(newInsuranceGrade);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "保險級距資料登錄成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"保險級距資料登錄失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditInsuranceGradeAsync(InsuranceGradeDTO data)
        {
            var list_msg_check = CheckInsuranceGradeInput(data, "edit");
            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingGrade = await _context.Pas_InsuranceGrade
                    .FirstOrDefaultAsync(ig => ig.uid == data.uid && ig.IsDeleted != true);

                if (existingGrade == null)
                {
                    return (false, "找不到對應的保險級距資料");
                }

                // 檢查是否有重複的保險類型和月投保薪資組合（排除自己）
                var duplicateGrade = await _context.Pas_InsuranceGrade
                    .FirstOrDefaultAsync(ig => ig.InsuranceType == data.InsuranceType
                                            && ig.MonthlySalary == data.MonthlySalary
                                            && ig.uid != data.uid
                                            && ig.IsDeleted != true);

                if (duplicateGrade != null)
                {
                    return (false, "此保險類型和月投保薪資組合已存在");
                }

                existingGrade.InsuranceType = data.InsuranceType;
                existingGrade.MonthlySalary = data.MonthlySalary;
                existingGrade.StartDate = _baseform.DateStrToTimestamp(data.StartDate);
                existingGrade.EndDate = !string.IsNullOrEmpty(data.EndDate) ? _baseform.DateStrToTimestamp(data.EndDate) : null;
                existingGrade.Remark = data.Remark;

                existingGrade.UpdateTime = _baseform.DateStrToTimestamp(DateTime.Now.ToString("yyyy-MM-dd"));
                existingGrade.UpdateUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯保險級距資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯保險級距資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteInsuranceGradeAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingGrade = await _context.Pas_InsuranceGrade
                    .FirstOrDefaultAsync(ig => ig.uid == uid && ig.IsDeleted != true);

                if (existingGrade == null)
                {
                    return (false, "資料已刪除或不存在");
                }

                existingGrade.IsDeleted = true;
                existingGrade.DeleteTime = _baseform.DateStrToTimestamp(DateTime.Now.ToString("yyyy-MM-dd"));
                existingGrade.DeleteUserId = _currentUserService.UserId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除保險級距資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除保險級距資料失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<InsuranceGradeDTO> GetInsuranceGradeBySalaryAsync(int monthlySalary, int insuranceType)
        {
            try
            {
                return await _context.Pas_InsuranceGrade
                    .Where(ig => ig.InsuranceType == insuranceType
                              && ig.MonthlySalary <= monthlySalary
                              && ig.IsDeleted != true)
                    .OrderByDescending(ig => ig.MonthlySalary)
                    .Select(ig => new InsuranceGradeDTO
                    {
                        uid = ig.uid,
                        InsuranceType = ig.InsuranceType,
                        MonthlySalary = ig.MonthlySalary,
                        StartDate = _baseform.TimestampToDateStr(ig.StartDate),
                        EndDate = ig.EndDate.HasValue ? _baseform.TimestampToDateStr(ig.EndDate.Value) : null,
                        Remark = ig.Remark,
                        UpdateTime = ig.UpdateTime
                    }).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("取得保險級距資料錯誤", ex);
            }
        }

        private async Task<List<(string GradeUid, int CurrentEmployeeCount, int PendingEmployeeCount, int TotalEmployeeCount)>> GetInsuranceGradeEmployeeStatsAsync(int insuranceType)
        {
            try
            {
                var currentTimestamp = _baseform.DateStrToTimestamp(DateTime.Now.ToString("yyyy-MM-dd"));

                // 取得指定保險類型的所有級距
                var grades = await _context.Pas_InsuranceGrade
                    .Where(ig => ig.InsuranceType == insuranceType && ig.IsDeleted != true)
                    .Select(ig => ig.uid)
                    .ToListAsync();

                var stats = new List<(string GradeUid, int CurrentEmployeeCount, int PendingEmployeeCount, int TotalEmployeeCount)>();

                foreach (var gradeUid in grades)
                {
                    // 查詢該級距的所有員工記錄（未刪除的）
                    var allRecords = await _context.Pas_InsuranceHistory
                        .Where(ih => ih.InsuranceGradeUid == gradeUid
                                  && ih.InsuranceType == insuranceType
                                  && ih.IsDeleted != true)
                        .ToListAsync();

                    // 計算目前生效的員工數量（按UserId去重）
                    // 1. EndDate為null且StartDate <= 當前時間（長期生效）
                    // 2. StartDate <= 當前時間且EndDate >= 當前時間（時間範圍內生效）
                    var currentEmployeeCount = allRecords
                        .Where(ih => (ih.EndDate == null && ih.StartDate <= currentTimestamp) ||
                                    (ih.StartDate <= currentTimestamp && ih.EndDate >= currentTimestamp))
                        .Select(ih => ih.UserId)
                        .Distinct()
                        .Count();

                    // 計算待生效的員工數量（StartDate > 當前時間，按UserId去重）
                    var pendingEmployeeCount = allRecords
                        .Where(ih => ih.StartDate > currentTimestamp)
                        .Select(ih => ih.UserId)
                        .Distinct()
                        .Count();

                    // 計算總員工數量（包括已結束的記錄）
                    var totalEmployeeCount = allRecords
                        .Select(ih => ih.UserId)
                        .Distinct()
                        .Count();

                    stats.Add((gradeUid, currentEmployeeCount, pendingEmployeeCount, totalEmployeeCount));
                }

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception("取得保險級距員工統計資料錯誤", ex);
            }
        }

        public async Task<List<InsuranceGradeEmployeeDetailDTO>> GetEmployeeInsuranceGradeDetailAsync(string gradeUid, string status)
        {
            try
            {
                var currentTimestamp = _baseform.DateStrToTimestamp(DateTime.Now.ToString("yyyy-MM-dd"));

                // 取得級距資訊
                var grade = await _context.Pas_InsuranceGrade
                    .FirstOrDefaultAsync(ig => ig.uid == gradeUid && ig.IsDeleted != true);

                if (grade == null)
                {
                    return new List<InsuranceGradeEmployeeDetailDTO>();
                }

                // 根據狀態過濾保險歷史記錄
                var query = _context.Pas_InsuranceHistory
                    .Where(ih => ih.InsuranceGradeUid == gradeUid && ih.IsDeleted != true);

                if (status.ToLower() == "current")
                {
                    // 目前生效的員工
                    query = query.Where(ih =>
                        (ih.EndDate == null && ih.StartDate <= currentTimestamp) ||
                        (ih.StartDate <= currentTimestamp && ih.EndDate >= currentTimestamp));
                }
                else if (status.ToLower() == "pending")
                {
                    // 待生效的員工
                    query = query.Where(ih => ih.StartDate > currentTimestamp);
                }

                var insuranceHistories = await query.ToListAsync();

                // 取得不重複的員工ID
                var userIds = insuranceHistories.Select(ih => ih.UserId).Distinct().ToList();

                // 查詢員工基本資料
                var users = await _context.Common_Users
                    .Where(e => userIds.Contains(e.UserId) && e.IsDeleted != true)
                    .ToListAsync();

                var result = new List<InsuranceGradeEmployeeDetailDTO>();

                foreach (var userId in userIds)
                {
                    var user = users.FirstOrDefault(e => e.UserId == userId);
                    var userHistories = insuranceHistories.Where(ih => ih.UserId == userId).ToList();

                    // 取得該員工最新的一筆記錄（按生效日期排序）
                    var latestHistory = userHistories.OrderByDescending(ih => ih.StartDate).FirstOrDefault();

                    if (latestHistory != null)
                    {
                        string statusDescription = "";
                        if (status.ToLower() == "current")
                        {
                            statusDescription = latestHistory.EndDate == null ? "長期生效" : "時間範圍內生效";
                        }
                        else if (status.ToLower() == "pending")
                        {
                            statusDescription = "待生效";
                        }

                        result.Add(new InsuranceGradeEmployeeDetailDTO
                        {
                            UserId = userId,
                            UserName = user?.Name ?? "未知",
                            DepartmentName = "未設定",
                            PositionName = "未設定",
                            StartDate = _baseform.TimestampToDateStr(latestHistory.StartDate),
                            EndDate = latestHistory.EndDate.HasValue ? _baseform.TimestampToDateStr(latestHistory.EndDate.Value) : null,
                            MonthlySalary = grade.MonthlySalary,
                            StatusDescription = statusDescription
                        });
                    }
                }

                return result.OrderBy(r => r.UserName).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception("取得保險級距員工詳細資料錯誤", ex);
            }
        }

        public List<string> CheckInsuranceGradeInput(InsuranceGradeDTO data, string mode)
        {
            List<string> list_errorMsg = new List<string>();

            if (data.InsuranceType < 1 || data.InsuranceType > 3)
                list_errorMsg.Add("保險類型必須為 1=勞保, 2=健保, 3=職災");

            if (data.MonthlySalary <= 0)
                list_errorMsg.Add("月投保薪資必須大於0");

            if (string.IsNullOrEmpty(data.StartDate))
                list_errorMsg.Add("請輸入生效日期");
            else if (!_baseform.IsValidDateOrEmpty(data.StartDate))
                list_errorMsg.Add("生效日期格式輸入錯誤");

            if (!string.IsNullOrEmpty(data.EndDate) && !_baseform.IsValidDateOrEmpty(data.EndDate))
                list_errorMsg.Add("結束日期格式輸入錯誤");

            // 檢查結束日期是否大於開始日期
            if (!string.IsNullOrEmpty(data.StartDate) && !string.IsNullOrEmpty(data.EndDate))
            {
                var startTimestamp = _baseform.DateStrToTimestamp(data.StartDate);
                var endTimestamp = _baseform.DateStrToTimestamp(data.EndDate);
                if (endTimestamp <= startTimestamp)
                {
                    list_errorMsg.Add("結束日期必須大於生效日期");
                }
            }

            return list_errorMsg;
        }
    }
}