﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class SystemParametersService : ISystemParametersService
    {
        private readonly ERPDbContext _context;
        private readonly ISystemParametersItemService _systemParametersItemService;

        public SystemParametersService(ERPDbContext context, ISystemParametersItemService systemParametersItemService)
        {
            _context = context;
            _systemParametersItemService = systemParametersItemService;
        }


        /// <summary>
        /// 新增系統參數
        /// </summary>
        /// <param name="SystemParameters"></param>
        /// <param name="tokenUid"></param>
        public async Task<(bool, string)> AddSystemParametersAsync(SystemParametersDTO SystemParameters, string tokenUid = "")
        {
            try
            {
                var newSystemParameters = new SystemParameters
                {
                    SystemParametersId = Guid.NewGuid().ToString(),
                    SystemGroupId = SystemParameters.SystemGroupId,
                    ParameterCode = SystemParameters.ParameterCode,
                    Description = SystemParameters.Description,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid,
                };

                await _context.Common_SystemParameters.AddAsync(newSystemParameters);
                await _context.SaveChangesAsync();

                return (true, "新增系統選單成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增系統選單失敗: {ex.Message}");
            }
        }
        /// <summary>
        /// 刪除系統參數
        /// </summary>
        /// <param name="SystemParameters"></param>
        /// <param name="tokenUid"></param>
        public async Task<(bool, string)> DeleteSystemParametersAsync(SystemParametersDTO SystemParameters, string tokenUid = "")
        {
            /// 開始交易
            /// 這裡使用了 BeginTransactionAsync() 方法來開始一個新的交易，這樣可以確保在操作過程中出現任何異常時都能夠回滾交易。
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingSystemParameters = await _context.Common_SystemParameters
                    .FirstOrDefaultAsync(e => e.SystemParametersId == SystemParameters.SystemParametersId);

                if (existingSystemParameters == null)
                {
                    throw new Exception($"系統選單不存在或已被刪除");
                }

                long deleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();

                // 刪除項目資料
                (bool itemSuccess, string itemMessage) = await _systemParametersItemService.DeleteBySystemParameterIdAsync(existingSystemParameters.SystemParametersId, tokenUid, deleteTime);
                if (!itemSuccess)
                {
                    throw new Exception($"{itemMessage}");
                }

                existingSystemParameters.DeleteTime = deleteTime;
                existingSystemParameters.DeleteUserId = SystemParameters.DeleteUserId;
                existingSystemParameters.IsDeleted = true;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync(); // 提交交易
                return (true, "刪除系統選單成功");

            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除系統選單失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯系統參數
        /// </summary>
        /// <param name="SystemParameters"></param>
        /// <param name="tokenUid"></param>
        public async Task<(bool, string)> EditSystemParametersAsync(SystemParametersDTO SystemParameters, string tokenUid = "")
        {
            try
            {
                var existingSystemParameters = await _context.Common_SystemParameters
                    .FirstOrDefaultAsync(e => e.SystemParametersId == SystemParameters.SystemParametersId);

                if (existingSystemParameters != null)
                {
                    existingSystemParameters.SystemGroupId = SystemParameters.SystemGroupId;
                    existingSystemParameters.ParameterCode = SystemParameters.ParameterCode;
                    existingSystemParameters.Description = SystemParameters.Description;
                    existingSystemParameters.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingSystemParameters.UpdateUserId = tokenUid;

                    await _context.SaveChangesAsync();
                    return (true, "編輯系統選單成功");
                }
                else
                {
                    return (false, "系統選單不存在或已被刪除");
                }
            }
            catch (Exception ex)
            {
                return (false, $"編輯系統選單失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得系統參數列表
        /// </summary>
        /// <param name="SystemParametersId"></param>
        public async Task<List<SystemParametersDTO>> GetSystemParametersAsync(string SystemParametersId = "")
        {
            var query = _context.Common_SystemParameters.AsQueryable();
            if (!string.IsNullOrEmpty(SystemParametersId))
            {
                query = query.Where(e => e.SystemParametersId == SystemParametersId);
            }

            return await query.Select(e => new SystemParametersDTO
            {
                SystemParametersId = e.SystemParametersId,
                SystemGroupId = e.SystemGroupId,
                ParameterCode = e.ParameterCode,
                Description = e.Description
            }).ToListAsync();
        }

        public async Task<(bool, string)> RestoreSystemParametersAsync(SystemParametersDTO SystemParameters, string tokenUid = "")
        {
            /// 開始交易
            /// 這裡使用了 BeginTransactionAsync() 方法來開始一個新的交易，這樣可以確保在操作過程中出現任何異常時都能夠回滾交易。
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingSystemParameters = await _context.Common_SystemParameters
                    .IgnoreQueryFilters() // 忽略全域篩選條件
                    .FirstOrDefaultAsync(
                        e => e.SystemParametersId == SystemParameters.SystemParametersId
                        && e.DeleteTime != null
                    );

                if (existingSystemParameters != null)
                {
                    long deleteTime = existingSystemParameters.DeleteTime.Value;
                    // 還原項目資料
                    (bool itemSuccess, string itemMessage) = await _systemParametersItemService.RestoreBySystemParameterIdAsync(existingSystemParameters.SystemParametersId, tokenUid, deleteTime);
                    if (!itemSuccess)
                    {
                        throw new Exception($"{itemMessage}");
                    }

                    existingSystemParameters.DeleteTime = null;
                    existingSystemParameters.DeleteUserId = null;

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync(); // 提交交易
                    return (true, "還原系統選單成功");
                }
                else
                {
                    return (false, "系統選單不存在或已被還原");
                }
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"還原系統選單失敗: {ex.Message}");
            }
        }
    }
}
