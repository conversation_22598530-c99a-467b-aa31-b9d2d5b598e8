using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("點數紀錄管理")]
    public class PerformancePointRecordController : ControllerBase
    {
        private readonly IPerformancePointRecordService _service;

        public PerformancePointRecordController(IPerformancePointRecordService service)
        {
            _service = service;
        }

        [HttpGet]
        [Route("GetByUser/{userId}")]
        [SwaggerOperation(Summary = "根據使用者ID取得點數紀錄列表", Description = "依使用者ID查詢所有點數紀錄")]
        public async Task<IActionResult> GetByUserId(string userId)
        {
            var result = await _service.GetByUserIdAsync(userId);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得點數紀錄明細", Description = "依UID取得點數紀錄資料")]
        public async Task<IActionResult> GetDetail(string uid)
        {
            var result = await _service.GetDetailAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增點數紀錄", Description = "新增一筆點數紀錄資料")]
        public async Task<IActionResult> Add([FromBody] PerformancePointRecordDTO dto)
        {
            var (result, msg) = await _service.AddAsync(dto);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯點數紀錄", Description = "編輯點數紀錄資料")]
        public async Task<IActionResult> Edit([FromBody] PerformancePointRecordDTO dto)
        {
            var (result, msg) = await _service.EditAsync(dto);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除點數紀錄", Description = "依UID刪除點數紀錄（軟刪除）")]
        public async Task<IActionResult> Delete([FromBody] string uid)
        {
            var (result, msg) = await _service.DeleteAsync(uid);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("GetPerformancePointSummary")]
        [SwaggerOperation(Summary = "取得點數彙總資料", Description = "依群組與日期範圍彙總各使用者點數")]
        public async Task<IActionResult> GetPerformancePointSummary([FromBody] PerformancePointSummaryQuery dto)
        {
            var result = await _service.GetPerformancePointSummaryAsync(dto);
            return Ok(result);
        }
    }
}
