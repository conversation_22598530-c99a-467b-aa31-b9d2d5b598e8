"use client";

/* 財產管理
  /app/pms/document_maintenance/fixed_asset_maintenance_form/page.tsx
*/

import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Input,
  Select,
  DatePicker,
  Divider,
  Typography,
  Dropdown,
  MenuProps,
  Pagination,
  Form,
  Tag,
  InputNumber,
  Tabs,
  Descriptions,
  Tooltip,
  Menu,
  Row,
  Col,
  Statistic,
} from "antd";
import Icon, {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  CheckOutlined,
  FileTextOutlined,
  EditOutlined,
  DatabaseOutlined,
  DollarOutlined,
  CalendarOutlined,
  ToolOutlined,
  SafetyCertificateOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import dayjs from "dayjs";
import {
  Asset,
  AssetDetail,
  getAssets,
  addAsset,
  editAsset,
  deleteAsset,
  getAssetById,
  AssetSource as ServiceAssetSource,
  getNewAssetNo,
} from "@/services/pms/assetService";
import {
  AssetCategory,
  getAssetCategories,
} from "@/services/pms/assetCategoryService";
import {
  Department,
  getDepartments,
} from "@/services/common/departmentService";
import { Division, getDivisions } from "@/services/common/divisionService";
import {
  AssetAccount,
  getAssetAccounts,
} from "@/services/pms/assetAccountService";
import {
  getAssetSources,
  createAssetSource,
} from "@/services/pms/assetSourceService";
import {
  AmortizationSource,
  getAmortizationSources,
  createAmortizationSource,
} from "@/services/pms/amortizationSourceService";
import {
  AssetSubAccount,
  getAssetSubAccounts,
} from "@/services/pms/assetSubAccountService";
import { Unit, getUnits } from "@/services/common/unitService";
import {
  AssetStatus,
  getAssetStatuses,
} from "@/services/pms/assetStatusService";
import {
  getEquipmentTypes,
  EquipmentType,
} from "@/services/pms/equipmentTypeService";
import {
  Manufacturer,
  getManufacturers,
} from "@/services/pms/manufacturerService";
import { User, getUsers } from "@/services/common/userService";
import {
  StorageLocation,
  getStorageLocations,
} from "@/services/pms/storageLocationService";

import { getColumns, getMobileColumns } from "./columns";
import AssetMaintenanceForm from "./Form";
import { AssetMaintenanceFormQuery } from "./interface";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { formatTWCurrency } from "@/utils/formatUtils";

const { Title } = Typography;

// 固定資產維護單頁面
const FixedAssetMaintenancePage: React.FC = () => {
  // 主要狀態
  const [assetDetails, setAssetDetails] = useState<AssetDetail[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<AssetDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [editingAsset, setEditingAsset] = useState<AssetDetail | null>(null);
  const [searchText, setSearchText] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const { user } = useAuth();

  // 各種資料項目
  const [assetStatusOptions, setAssetStatusOptions] = useState<AssetStatus[]>(
    []
  );
  const [assetCategories, setAssetCategories] = useState<AssetCategory[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [assetAccounts, setAssetAccounts] = useState<AssetAccount[]>([]);
  const [assetSources, setAssetSources] = useState<ServiceAssetSource[]>([]);
  const [amortizationSources, setAmortizationSources] = useState<
    AmortizationSource[]
  >([]);
  const [assetSubAccounts, setAssetSubAccounts] = useState<AssetSubAccount[]>(
    []
  );
  const [units, setUnits] = useState<Unit[]>([]);
  const [equipmentTypes, setEquipmentTypes] = useState<EquipmentType[]>([]);
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [custodians, setCustodians] = useState<User[]>([]);
  const [assetUsers, setAssetUsers] = useState<User[]>([]);
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>(
    []
  );

  // 篩選相關狀態
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // 行動裝置
  const [isMobile, setIsMobile] = useState(false);

  // 分頁相關狀態
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 表單相關數據
  const formDataRef = useRef<{
    assetAccountId?: string;
    assetSubAccountId?: string;
    assetCategoryId?: string;
    assetNo?: string;
  }>({});
  const [selectedAssetAccountId, setSelectedAssetAccountId] =
    useState<string>("");
  const [filteredAssetSubAccounts, setFilteredAssetSubAccounts] = useState<
    AssetSubAccount[]
  >([]);

  // 統計數據
  const [statistics, setStatistics] = useState({
    totalCount: 0,
    totalValue: 0,
    totalDepreciation: 0,
    averageAge: 0,
    normalCount: 0,
    idleCount: 0,
    maintenanceCount: 0,
    scrapCount: 0,
    loanedCount: 0,
    lostCount: 0,
    transferredCount: 0,
    soldCount: 0,
  });

  // 定義篩選選項
  const filterOptions = [
    {
      label: "財產類別",
      value: "assetCategory",
      children: assetCategories.map((category) => ({
        label: category.assetCategoryName,
        value: category.assetCategoryId,
      })),
    },
    {
      label: "使用狀態",
      value: "assetStatusId",
      children: assetStatusOptions.map((status) => ({
        label: status.name,
        value: status.assetStatusId,
      })),
    },
    {
      label: "所屬部門",
      value: "department",
      children: departments.map((dept) => ({
        label: dept.name,
        value: dept.departmentId,
      })),
    },
    {
      label: "財產科目",
      value: "assetAccount",
      children: assetAccounts.map((account) => ({
        label: account.assetAccountName,
        value: account.assetAccountId,
      })),
    },
    {
      label: "設備類型",
      value: "equipmentType",
      children: equipmentTypes.map((type) => ({
        label: type.name,
        value: type.equipmentTypeId,
      })),
    },
    {
      label: "取得日期",
      value: "acquisitionDate",
      type: "date",
    },
    {
      label: "財產編號",
      value: "assetNo",
      type: "input",
    },
    {
      label: "保管人",
      value: "custodian",
      type: "input",
    },
    {
      label: "使用人",
      value: "user",
      type: "input",
    },
  ];

  // 加載所有數據
  useEffect(() => {
    loadAssets();
    loadAssetCategories();
    loadDepartments();
    loadDivisions();
    loadAssetAccounts();
    loadAssetSources();
    loadAmortizationSources();
    loadAssetSubAccounts();
    loadUnits();
    loadAssetStatuses();
    loadEquipmentTypes();
    loadManufacturers();
    loadUsers();
    loadStorageLocations();
  }, []);

  // 檢測行動裝置
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // 計算統計數據
  const calculateStatistics = (assetData: AssetDetail[]) => {
    const currentYear = dayjs().year();
    let totalValue = 0;
    let totalDepreciation = 0;
    let totalAge = 0;
    let validAgeCount = 0;

    // 計算各狀態的數量
    const statusCounts = {
      normalCount: 0,
      idleCount: 0,
      maintenanceCount: 0,
      scrapCount: 0,
      loanedCount: 0,
      lostCount: 0,
      transferredCount: 0,
      soldCount: 0,
    };

    assetData.forEach((assetDetail) => {
      const asset = assetDetail.asset;

      // 累計總值
      if (asset.unitPrice && asset.quantity) {
        totalValue += asset.unitPrice * asset.quantity;
      }

      // 累計折舊
      if (asset.accumulatedDepreciationAmount) {
        totalDepreciation += asset.accumulatedDepreciationAmount;
      }

      // 計算資產年齡
      if (asset.acquisitionDate) {
        const acquisitionYear = dayjs(asset.acquisitionDate).year();
        const age = currentYear - acquisitionYear;
        if (age >= 0) {
          totalAge += age;
          validAgeCount++;
        }
      }

      // 計算各狀態數量 - 從資產狀態選項中找到對應的狀態名稱
      const assetStatus = assetStatusOptions.find(
        (status) => status.assetStatusId === asset.assetStatusId
      );
      const statusName = assetStatus?.name || "";
      switch (statusName) {
        case "正常使用":
          statusCounts.normalCount++;
          break;
        case "閒置":
          statusCounts.idleCount++;
          break;
        case "維修中":
          statusCounts.maintenanceCount++;
          break;
        case "已報廢":
          statusCounts.scrapCount++;
          break;
        case "借出":
          statusCounts.loanedCount++;
          break;
        case "已丟失":
          statusCounts.lostCount++;
          break;
        case "已轉移":
          statusCounts.transferredCount++;
          break;
        case "已出售":
          statusCounts.soldCount++;
          break;
      }
    });

    const newStatistics = {
      totalCount: assetData.length,
      totalValue: Math.round(totalValue),
      totalDepreciation: Math.round(totalDepreciation),
      averageAge:
        validAgeCount > 0
          ? Math.round((totalAge / validAgeCount) * 10) / 10
          : 0,
      ...statusCounts,
    };

    setStatistics(newStatistics);
  };

  // 當資產狀態選項載入後重新計算統計
  useEffect(() => {
    if (assetStatusOptions.length > 0 && assetDetails.length > 0) {
      calculateStatistics(assetDetails);
    }
  }, [assetStatusOptions, assetDetails]);

  // 當過濾條件改變時重新計算統計
  useEffect(() => {
    if (assetStatusOptions.length > 0 && filteredAssets.length >= 0) {
      calculateStatistics(filteredAssets);
    }
  }, [filteredAssets, assetStatusOptions]);

  // 加載財產列表
  const loadAssets = async () => {
    setLoading(true);
    try {
      const response = await getAssets();
      if (response.success && response.data) {
        setAssetDetails(response.data);
        setFilteredAssets(response.data);
        // 計算統計數據
        calculateStatistics(response.data);
      } else {
        notifyError("獲取財產列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 加載財產類別
  const loadAssetCategories = async () => {
    try {
      const response = await getAssetCategories();
      if (response.success && response.data) {
        setAssetCategories(response.data);
      } else {
        notifyError("獲取財產類別列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產類別列表失敗", "請稍後再試");
    }
  };

  // 加載部門資料
  const loadDepartments = async () => {
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        setDepartments(response.data);
      } else {
        notifyError("獲取部門列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取部門列表失敗", "請稍後再試");
    }
  };

  // 加載組別資料
  const loadDivisions = async () => {
    try {
      const response = await getDivisions();
      if (response.success && response.data) {
        setDivisions(response.data);
      } else {
        notifyError("獲取組別列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取組別列表失敗", "請稍後再試");
    }
  };

  // 加載財產科目資料
  const loadAssetAccounts = async () => {
    try {
      const response = await getAssetAccounts();
      if (response.success && response.data) {
        setAssetAccounts(response.data);
      } else {
        notifyError("獲取財產科目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產科目列表失敗", "請稍後再試");
    }
  };

  // 加載財產來源資料
  const loadAssetSources = async () => {
    try {
      const response = await getAssetSources();
      if (response.success && response.data) {
        // 將回應資料轉換為正確的類型
        const formattedSources: ServiceAssetSource[] = response.data.map(
          (source, index) => ({
            assetSourceId: source.assetSourceId || "",
            assetSourceName: source.assetSourceName || "",
            sortCode: index + 1, // 使用索引作為排序碼
            createTime: source.createTime || 0,
            createUserId: source.createUserId || "",
            createUserName: source.createUserName || undefined,
            updateTime: source.updateTime || 0,
            updateUserId: source.updateUserId || "",
            updateUserName: source.updateUserName || undefined,
            deleteTime: source.deleteTime || 0,
            deleteUserId: source.deleteUserId || "",
            deleteUserName: source.deleteUserName || undefined,
            assetAssetSources: [],
          })
        );
        setAssetSources(formattedSources);
      } else {
        notifyError("獲取財產來源列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產來源列表失敗", "請稍後再試");
    }
  };

  // 加載攤提來源資料
  const loadAmortizationSources = async () => {
    try {
      const response = await getAmortizationSources();
      if (response.success && response.data) {
        setAmortizationSources(response.data);
      } else {
        notifyError("獲取攤提來源列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取攤提來源列表失敗", "請稍後再試");
    }
  };

  // 加載財產子目資料
  const loadAssetSubAccounts = async () => {
    try {
      const response = await getAssetSubAccounts();
      if (response.success && response.data) {
        setAssetSubAccounts(response.data);
      } else {
        notifyError("獲取財產子目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產子目列表失敗", "請稍後再試");
    }
  };

  // 加載單位資料
  const loadUnits = async () => {
    try {
      const response = await getUnits();
      if (response.success && response.data) {
        setUnits(response.data);
      } else {
        notifyError("獲取單位列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取單位列表失敗", "請稍後再試");
    }
  };

  // 加載財產狀態
  const loadAssetStatuses = async () => {
    try {
      const response = await getAssetStatuses();
      if (response.success && response.data) {
        setAssetStatusOptions(response.data);
      } else {
        notifyError("獲取財產狀態列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產狀態列表失敗", "請稍後再試");
    }
  };

  // 加載設備類型
  const loadEquipmentTypes = async () => {
    try {
      const response = await getEquipmentTypes();
      if (response.success && response.data) {
        setEquipmentTypes(response.data);
      } else {
        notifyError("獲取設備類型列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取設備類型列表失敗", "請稍後再試");
    }
  };

  // 加載廠牌型號
  const loadManufacturers = async () => {
    try {
      const response = await getManufacturers();
      if (response.success && response.data) {
        setManufacturers(response.data);
      } else {
        notifyError("獲取廠牌型號列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取廠牌型號列表失敗", "請稍後再試");
    }
  };

  // 加載使用者
  const loadUsers = async () => {
    try {
      const response = await getUsers();
      if (response.success && response.data) {
        setUsers(response.data);
        // 簡化處理：實際應用中應根據角色過濾
        setCustodians(response.data);
        setAssetUsers(response.data);
      } else {
        notifyError("獲取使用者列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取使用者列表失敗", "請稍後再試");
    }
  };

  // 加載存放地點資料
  const loadStorageLocations = async () => {
    try {
      const response = await getStorageLocations();
      if (response.success && response.data) {
        setStorageLocations(response.data);
      } else {
        notifyError("獲取存放地點列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取存放地點列表失敗", "請稍後再試");
    }
  };

  // 應用篩選
  useEffect(() => {
    let filtered: AssetDetail[] = assetDetails.filter(
      (detail): detail is AssetDetail =>
        detail && detail.asset !== null && detail.asset !== undefined
    );

    // 搜尋文字篩選
    if (searchText) {
      filtered = filtered.filter((detail: AssetDetail) =>
        detail.asset.assetName?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 應用所有活動的篩選條件
    filtered = filtered.filter((detail: AssetDetail) => {
      return activeFilters.every((filterKey) => {
        const value = filterValues[filterKey];
        if (!value) return true;

        const option = filterOptions.find((opt) => opt.value === filterKey);
        if (!option) return true;

        // 根據選擇的篩選條件進行篩選
        switch (filterKey) {
          case "assetCategory":
            return detail.asset.assetCategoryId === value;
          case "assetStatusId":
            return detail.asset.assetStatusId === value;
          case "department":
            return detail.asset.departmentId === value;
          case "assetAccount":
            return detail.asset.assetAccountId === value;
          case "equipmentType":
            return detail.asset.equipmentTypeId === value;
          case "acquisitionDate":
            const acquisitionDate = dayjs.unix(
              detail.asset.acquisitionDate || 0
            );
            return acquisitionDate.isSame(value, "day");
          case "assetNo":
            return detail.asset.assetNo?.includes(value);
          case "custodian":
            return detail.asset.custodianId?.includes(value);
          case "user":
            return detail.asset.userId?.includes(value);
          default:
            return true;
        }
      });
    });

    setFilteredAssets(filtered);
    setTotalCount(filtered.length);
  }, [searchText, activeFilters, filterValues, assetDetails]);

  // 處理查看財產詳情
  const handleView = (assetDetail: AssetDetail) => {
    setEditingAsset(assetDetail);
    setIsViewMode(true);
    setIsModalVisible(true);
  };

  // 處理編輯
  const handleEdit = (assetDetail: AssetDetail) => {
    setEditingAsset(assetDetail);
    setIsViewMode(false);
    setIsModalVisible(true);
  };

  // 顯示新增財產表單
  const showAddModal = () => {
    setEditingAsset(null);
    setIsViewMode(false);
    setIsModalVisible(true);
  };

  // 處理表單成功提交
  const handleFormSuccess = async (assetDetailData: AssetDetail) => {
    try {
      if (editingAsset) {
        // 更新財產
        const response = await editAsset(assetDetailData);
        if (response.success) {
          notifySuccess("更新成功", response.message || "財產資料已更新");
          // 重新載入完整財產列表
          await loadAssets();
          // 重新載入來源列表確保最新
          await loadAssetSources();
          await loadAmortizationSources();

          // 關閉對話框並重置狀態
          setIsModalVisible(false);
          setEditingAsset(null);
        } else {
          console.error("更新失敗:", response);
          notifyError(
            "更新失敗",
            response.message || "更新財產時發生錯誤，請稍後再試"
          );
        }
      } else {
        // 新增財產
        const response = await addAsset(assetDetailData);
        if (response.success) {
          notifySuccess("新增成功", response.message || "財產資料已新增");
          // 重新載入完整財產列表
          await loadAssets();
          // 重新載入來源列表確保最新
          await loadAssetSources();
          await loadAmortizationSources();

          // 關閉對話框並重置狀態
          setIsModalVisible(false);
          setEditingAsset(null);
        } else {
          console.error("新增失敗:", response);
          notifyError(
            "新增失敗",
            response.message || "新增財產時發生錯誤，請稍後再試"
          );
        }
      }
    } catch (error) {
      console.error("操作失敗:", error);
      notifyError("操作失敗", "處理財產資料時發生錯誤，請稍後再試");
    }
  };

  // 處理表單取消
  const handleFormCancel = () => {
    setIsModalVisible(false);
    setEditingAsset(null);
    setIsViewMode(false);
  };

  // 處理頁碼變更
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  // 處理每頁顯示數量變更
  const handlePageSizeChange = (current: number, size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 獲取當前頁數據
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAssets.slice(startIndex, endIndex);
  };

  // 手機版列表渲染
  const renderMobileList = () => {
    // 取得行動裝置欄位定義
    const mobileColumns = getMobileColumns(assetStatusOptions);
    return (
      <div className="mobile-list">
        {getCurrentPageData().map((record) => (
          <Card
            key={record.asset.assetId}
            style={{ marginBottom: 16 }}
            actions={[
              <Button
                key="view"
                type="link"
                icon={<FileTextOutlined />}
                onClick={() => handleView(record)}
              >
                查看
              </Button>,
              <Button
                key="edit"
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                編輯
              </Button>,
            ]}
          >
            <div style={{ marginBottom: 8 }}>
              <Tag color="blue" style={{ marginRight: 8 }}>
                {record.asset.assetNo}
              </Tag>
              {/* 使用 getMobileColumns 的欄位定義渲染狀態 */}
              {mobileColumns.find((col) => col.key === "assetStatus")?.render
                ? (
                    mobileColumns.find((col) => col.key === "assetStatus")!
                      .render as (
                      text: any,
                      record: any,
                      index: number
                    ) => React.ReactNode
                  )(record.asset.assetStatusId, record, 0)
                : null}
            </div>

            <div style={{ marginBottom: 12 }}>
              <Typography.Title level={5} style={{ margin: 0 }}>
                {record.asset.assetName}
              </Typography.Title>
              {record.asset.assetShortName && (
                <Typography.Text type="secondary">
                  ({record.asset.assetShortName})
                </Typography.Text>
              )}
            </div>

            <Descriptions size="small" column={1}>
              <Descriptions.Item label="取得日期">
                {DateTimeExtensions.formatDateFromTimestamp(
                  record.asset.acquisitionDate
                )}
              </Descriptions.Item>
              <Descriptions.Item label="所屬部門">
                {departments.find(
                  (dep) => dep.departmentId === record.asset.departmentId
                )?.name || "-"}
              </Descriptions.Item>
              <Descriptions.Item label="保管人">
                {(() => {
                  const custodian = custodians.find(
                    (user) => user.userId === record.asset.custodianId
                  );
                  return custodian
                    ? `${custodian.name} (${custodian.account})`
                    : record.asset.custodianId || "-";
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="使用人">
                {(() => {
                  const assetUser = assetUsers.find(
                    (user) => user.userId === record.asset.userId
                  );
                  return assetUser
                    ? `${assetUser.name} (${assetUser.account})`
                    : record.asset.userId || "-";
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="購入金額">
                <Tag color="red">
                  {formatTWCurrency(record.asset.purchaseAmount || 0)}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        ))}

        <Pagination
          total={totalCount}
          pageSize={pageSize}
          current={currentPage}
          onChange={handlePageChange}
          onShowSizeChange={handlePageSizeChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 筆`}
          style={{ margin: "16px 0", textAlign: "center" }}
        />
      </div>
    );
  };

  // 清除篩選條件的處理函數
  const handleClearFilters = () => {
    setActiveFilters([]);
    setFilterValues({});
    setSearchText("");
  };

  // 處理新增篩選條件
  const handleAddFilter = (filterKey: string) => {
    if (!activeFilters.includes(filterKey)) {
      setActiveFilters([...activeFilters, filterKey]);
    }
  };

  // 處理移除篩選條件
  const handleRemoveFilter = (filterKey: string) => {
    setActiveFilters(activeFilters.filter((key) => key !== filterKey));
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];
    setFilterValues(newFilterValues);
  };

  // 處理篩選值變更
  const handleFilterValueChange = (filterKey: string, value: any) => {
    setFilterValues({
      ...filterValues,
      [filterKey]: value,
    });
  };

  // 渲染篩選控制元素
  const renderFilterControl = (filterKey: string) => {
    const filterOption = filterOptions.find((opt) => opt.value === filterKey);
    if (!filterOption) return null;

    return (
      <div className="filter-control">
        {filterOption.type === "date" ? (
          <DatePicker
            style={{ width: 200 }}
            onChange={(value) =>
              handleFilterValueChange(filterKey, value ? value.valueOf() : null)
            }
            placeholder={`選擇${filterOption.label}`}
            allowClear
          />
        ) : filterOption.type === "input" ? (
          <Input
            style={{ width: 200 }}
            onChange={(e) => handleFilterValueChange(filterKey, e.target.value)}
            placeholder={`輸入${filterOption.label}`}
            allowClear
          />
        ) : (
          <Select
            style={{ width: 200 }}
            onChange={(value) => handleFilterValueChange(filterKey, value)}
            options={filterOption.children}
            placeholder={`選擇${filterOption.label}`}
            allowClear
          />
        )}
      </div>
    );
  };

  // 更新篩選選項
  const filterMenu: MenuProps = {
    items: [
      ...filterOptions.map((option) => ({
        key: option.value,
        label: (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: "120px",
            }}
          >
            <span>{option.label}</span>
            {activeFilters.includes(option.value) && (
              <CheckOutlined style={{ color: "#1890ff" }} />
            )}
          </div>
        ),
        onClick: () => {
          if (activeFilters.includes(option.value)) {
            handleRemoveFilter(option.value);
          } else {
            handleAddFilter(option.value);
          }
        },
      })),
      ...(activeFilters.length > 0
        ? [
            { type: "divider" as const },
            {
              key: "clear",
              label: "清除所有篩選",
              onClick: handleClearFilters,
              danger: true,
            },
          ]
        : []),
    ],
  };

  // 處理財產科目選擇變更
  const handleAssetAccountChange = useCallback(
    (value: string) => {
      setSelectedAssetAccountId(value);
      // 清空財產子目的選擇
      formDataRef.current.assetSubAccountId = undefined;

      // 根據選擇的財產科目過濾財產子目
      if (value) {
        const filtered = assetSubAccounts.filter(
          (subAccount) => subAccount.assetAccountId === value
        );
        setFilteredAssetSubAccounts(filtered);
      } else {
        setFilteredAssetSubAccounts([]);
      }

      // 設置資產科目ID
      formDataRef.current.assetAccountId = value;

      // 檢查是否可以取得新財產編號
      checkAndGetNewAssetNo();
    },
    [assetSubAccounts]
  );

  // 處理財產子目選擇變更
  const handleAssetSubAccountChange = useCallback((value: string) => {
    // 設置資產子目ID
    formDataRef.current.assetSubAccountId = value;
    // 檢查是否可以取得新財產編號
    checkAndGetNewAssetNo();
  }, []);

  // 處理財產類別選擇變更
  const handleAssetCategoryChange = useCallback((value: string) => {
    // 設置資產類別ID
    formDataRef.current.assetCategoryId = value;
    // 檢查是否可以取得新財產編號
    checkAndGetNewAssetNo();
  }, []);

  // 檢查並取得新財產編號
  const checkAndGetNewAssetNo = async () => {
    // 如果是編輯模式，不需要取得新財產編號
    if (editingAsset) return;

    const accountId = formDataRef.current.assetAccountId;
    const subAccountId = formDataRef.current.assetSubAccountId;
    const categoryId = formDataRef.current.assetCategoryId;

    // 只有當三個欄位都有值時才取得新財產編號
    if (accountId && subAccountId && categoryId) {
      try {
        // 找到對應的科目資料
        const assetAccount = assetAccounts.find(
          (account) => account.assetAccountId === accountId
        );
        // 找到對應的子目資料
        const assetSubAccount = assetSubAccounts.find(
          (subAccount) => subAccount.assetSubAccountId === subAccountId
        );
        // 找到對應的類別資料
        const assetCategory = assetCategories.find(
          (category) => category.assetCategoryId === categoryId
        );

        if (assetAccount && assetSubAccount && assetCategory) {
          const response = await getNewAssetNo(
            assetAccount.assetAccountNo,
            assetSubAccount.assetSubAccountNo,
            assetCategory.assetCategoryId
          );

          if (response.success && response.data && response.data.assetNo) {
            formDataRef.current.assetNo = response.data.assetNo;
          }
        }
      } catch (error) {
        console.error("取得新財產編號失敗:", error);
      }
    }
  };

  // 渲染頁面
  return (
    <Card title="固定資產維護單">
      {/* 統計卡片區域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="資產總數"
              value={statistics.totalCount}
              valueStyle={{ color: "#1890ff" }}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="資產總值"
              value={statistics.totalValue}
              valueStyle={{ color: "#1890ff" }}
              prefix={<DollarOutlined />}
              formatter={(value) => `${formatTWCurrency(Number(value))}`}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="累計折舊"
              value={statistics.totalDepreciation}
              valueStyle={{ color: "#fa8c16" }}
              prefix={<CalendarOutlined />}
              formatter={(value) => `${formatTWCurrency(Number(value))}`}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="平均年齡"
              value={statistics.averageAge}
              valueStyle={{ color: "#722ed1" }}
              prefix={<ClockCircleOutlined />}
              suffix="年"
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="正常使用"
              value={statistics.normalCount}
              valueStyle={{ color: "#52c41a" }}
              prefix={<CheckOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="閒置"
              value={statistics.idleCount}
              valueStyle={{ color: "#13c2c2" }}
              prefix={<HomeOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="維修中"
              value={statistics.maintenanceCount}
              valueStyle={{ color: "#faad14" }}
              prefix={<ToolOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="已報廢"
              value={statistics.scrapCount}
              valueStyle={{ color: "#ff4d4f" }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="借出"
              value={statistics.loanedCount}
              valueStyle={{ color: "#eb2f96" }}
              prefix={<SafetyCertificateOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="已丟失"
              value={statistics.lostCount}
              valueStyle={{ color: "#f5222d" }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="已轉移"
              value={statistics.transferredCount}
              valueStyle={{ color: "#08979c" }}
              prefix={<SafetyCertificateOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4}>
          <Card>
            <Statistic
              title="已出售"
              value={statistics.soldCount}
              valueStyle={{ color: "#722ed1" }}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Space style={{ marginBottom: 16 }} wrap>
        <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
          新增固定資產維護單
        </Button>
        <Dropdown menu={filterMenu} trigger={["click"]}>
          <Button icon={<FilterOutlined />}>新增篩選條件</Button>
        </Dropdown>
        {activeFilters.map((filterKey) => {
          const filterOption = filterOptions.find(
            (opt) => opt.value === filterKey
          );
          if (!filterOption) return null;
          return (
            <Space key={filterKey} style={{ marginRight: 8 }}>
              {renderFilterControl(filterKey)}
            </Space>
          );
        })}
        <Input
          placeholder="搜尋財產名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 200 }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={getColumns(
            handleView,
            handleEdit,
            assetStatusOptions,
            assetAccounts,
            assetSubAccounts,
            departments,
            units,
            custodians,
            storageLocations
          )}
          dataSource={filteredAssets}
          rowKey={(record) => record.asset.assetId}
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      {/* 財產表單對話框 */}
      <Modal
        title={
          isViewMode
            ? "財產詳細資訊"
            : editingAsset
            ? "編輯財產資訊"
            : "新增財產"
        }
        open={isModalVisible}
        onCancel={handleFormCancel}
        width={isMobile ? "100%" : 900}
        style={isMobile ? { top: 0, margin: 0, maxWidth: "100vw" } : {}}
        styles={{
          body: isMobile
            ? { padding: "12px", maxHeight: "80vh", overflowY: "auto" }
            : {},
        }}
        footer={null}
        maskClosable={false}
      >
        <AssetMaintenanceForm
          editingAsset={editingAsset}
          isViewMode={isViewMode}
          onCancel={handleFormCancel}
          onSuccess={handleFormSuccess}
          assetStatusOptions={assetStatusOptions}
          assetCategories={assetCategories}
          departments={departments}
          divisions={divisions}
          assetAccounts={assetAccounts}
          assetSubAccounts={assetSubAccounts}
          units={units}
          equipmentTypes={equipmentTypes}
          manufacturers={manufacturers}
          custodians={custodians}
          assetUsers={assetUsers}
          assetSources={assetSources}
          amortizationSources={amortizationSources}
          storageLocations={storageLocations}
          isMobile={isMobile}
        />
      </Modal>
    </Card>
  );
};

export default FixedAssetMaintenancePage;
