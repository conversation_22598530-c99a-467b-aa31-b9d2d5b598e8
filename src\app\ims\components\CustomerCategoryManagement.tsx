"use client";

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Modal, Form, Input, Button, Space, Tag, Row, Col, TreeSelect, Popconfirm, message, Card, Badge, Alert } from 'antd';
import type { InputRef } from 'antd';
import { PlusOutlined, EditOutlined, SaveOutlined, UndoOutlined, ApartmentOutlined, NodeIndexOutlined, ExpandOutlined, CompressOutlined, CaretRightOutlined, CaretDownOutlined, DeleteOutlined, ContactsOutlined } from '@ant-design/icons';
import { CustomerCategory } from '@/services/ims/partner';
import { addCustomerCategory, editCustomerCategory, deleteCustomerCategory, buildCustomerCategoryTree } from '@/services/ims/CustomerCategoryService';

interface CustomerCategoryManagementProps {
  visible: boolean;
  onClose: () => void;
  categories: CustomerCategory[];
  onDataChange: () => void;
}

const CustomerCategoryManagement: React.FC<CustomerCategoryManagementProps> = ({
  visible,
  onClose,
  categories,
  onDataChange
}) => {
  // 狀態管理
  const [selectedCategory, setSelectedCategory] = useState<CustomerCategory | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [operationMode, setOperationMode] = useState<'add' | 'edit' | 'addChild'>('add');
  const [parentCategoryForChild, setParentCategoryForChild] = useState<CustomerCategory | null>(null);

  // 折疊功能狀態管理
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isAllExpanded, setIsAllExpanded] = useState(false);

  // Focus management refs
  const nameInputRef = useRef<InputRef>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);

  // 移動端檢測狀態
  const [isMobile, setIsMobile] = useState(false);

  // 驗證分類資料的輔助函數
  const validateCategoryData = (category: any): boolean => {
    return category &&
           typeof category === 'object' &&
           category.customerCategoryID &&
           typeof category.customerCategoryID === 'string' &&
           category.name &&
           typeof category.name === 'string';
  };

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 建立分類樹狀資料
  const categoryTreeData = useMemo(() => {
    if (!Array.isArray(categories)) {
      console.warn('⚠️ CustomerCategoryManagement: categories 不是陣列');
      return [];
    }

    const tree = buildCustomerCategoryTree(categories);
    
    const convertToTreeData = (cats: CustomerCategory[]): any[] => {
      return cats.map(cat => ({
        title: cat.name,
        value: cat.customerCategoryID,
        key: cat.customerCategoryID,
        children: cat.children && cat.children.length > 0 ? convertToTreeData(cat.children) : undefined
      }));
    };

    return convertToTreeData(tree);
  }, [categories]);

  // 排序後的分類顯示資料
  const sortedCategoriesForDisplay = useMemo(() => {
    if (!Array.isArray(categories)) {
      return [];
    }

    const sortedCategories = [...categories].sort((a, b) => {
      // 先按 sortCode 排序，再按名稱排序
      if (a.sortCode !== b.sortCode) {
        return a.sortCode - b.sortCode;
      }
      return a.name.localeCompare(b.name);
    });

    return sortedCategories;
  }, [categories]);

  // 重置表單
  const resetForm = () => {
    form.resetFields();
    setSelectedCategory(null);
    setOperationMode('add');
    setParentCategoryForChild(null);
  };

  // 處理新增分類
  const handleAdd = () => {
    resetForm();
    setOperationMode('add');
    
    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理編輯分類
  const handleEdit = (category: CustomerCategory) => {
    if (!validateCategoryData(category)) {
      message.error('無效的分類資料');
      return;
    }

    setSelectedCategory(category);
    setOperationMode('edit');
    form.setFieldsValue({
      name: category.name,
      description: category.description || '',
      parentID: category.parentID || undefined,
      sortCode: category.sortCode || 0,
    });

    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理新增子分類
  const handleAddChild = (parentCategory: CustomerCategory) => {
    if (!validateCategoryData(parentCategory)) {
      message.error('無效的父分類資料');
      return;
    }

    resetForm();
    setOperationMode('addChild');
    setParentCategoryForChild(parentCategory);
    form.setFieldsValue({
      parentID: parentCategory.customerCategoryID,
    });

    setTimeout(() => {
      nameInputRef.current?.focus();
    }, 100);
  };

  // 處理刪除分類
  const handleDelete = async (category: CustomerCategory) => {
    if (!validateCategoryData(category)) {
      message.error('無效的分類資料');
      return;
    }

    try {
      setLoading(true);
      const response = await deleteCustomerCategory(category.customerCategoryID);
      
      if (response.success) {
        message.success('客戶分類刪除成功');
        onDataChange();
        resetForm();
      } else {
        message.error(response.message || '刪除客戶分類失敗');
      }
    } catch (error) {
      console.error('❌ 刪除客戶分類時發生錯誤:', error);
      message.error('刪除客戶分類失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      let response;

      if (operationMode === 'edit' && selectedCategory) {
        // 編輯模式
        response = await editCustomerCategory({
          customerCategoryID: selectedCategory.customerCategoryID,
          name: values.name,
          description: values.description || '',
          parentID: values.parentID || null,
          sortCode: values.sortCode || 0,
        });
      } else {
        // 新增模式 (包括新增子分類)
        response = await addCustomerCategory({
          name: values.name,
          description: values.description || '',
          parentID: values.parentID || null,
          sortCode: values.sortCode || 0,
        });
      }

      if (response.success) {
        const action = operationMode === 'edit' ? '更新' : '新增';
        message.success(`客戶分類${action}成功`);
        onDataChange();
        resetForm();
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('❌ 提交客戶分類時發生錯誤:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 切換分類展開/折疊
  const toggleCategoryExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // 全部展開/折疊
  const toggleAllExpansion = () => {
    if (isAllExpanded) {
      setExpandedCategories(new Set());
    } else {
      const allIds = new Set(categories.map(cat => cat.customerCategoryID));
      setExpandedCategories(allIds);
    }
    setIsAllExpanded(!isAllExpanded);
  };

  // 渲染分類項目
  const renderCategoryItem = (category: CustomerCategory, level: number = 0) => {
    if (!validateCategoryData(category)) {
      return null;
    }

    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories.has(category.customerCategoryID);
    const indent = level * 24;

    return (
      <div key={category.customerCategoryID}>
        <div
          style={{
            padding: '8px 12px',
            marginLeft: indent,
            borderLeft: level > 0 ? '2px solid #f0f0f0' : 'none',
            backgroundColor: selectedCategory?.customerCategoryID === category.customerCategoryID ? '#e6f7ff' : 'transparent',
            borderRadius: '4px',
            margin: '2px 0',
          }}
        >
          <Row align="middle" justify="space-between">
            <Col flex="auto">
              <Space>
                {hasChildren && (
                  <Button
                    type="text"
                    size="small"
                    icon={isExpanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
                    onClick={() => toggleCategoryExpansion(category.customerCategoryID)}
                  />
                )}
                {!hasChildren && <span style={{ width: 22, display: 'inline-block' }} />}
                
                <Tag color="blue" style={{ margin: 0 }}>
                  {category.name}
                </Tag>
                
                {category.description && (
                  <span style={{ color: '#666', fontSize: '12px' }}>
                    {category.description}
                  </span>
                )}
                
                <Badge count={category.sortCode} style={{ backgroundColor: '#52c41a' }} />
              </Space>
            </Col>
            
            <Col>
              <Space size="small">
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddChild(category)}
                  title="新增子分類"
                />
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(category)}
                  title="編輯分類"
                />
                <Popconfirm
                  title="確定要刪除此客戶分類嗎？"
                  description="刪除後無法復原，請確認。"
                  onConfirm={() => handleDelete(category)}
                  okText="確定"
                  cancelText="取消"
                >
                  <Button
                    type="link"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                    title="刪除分類"
                  />
                </Popconfirm>
              </Space>
            </Col>
          </Row>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {category.children!.map(child => renderCategoryItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <ContactsOutlined style={{ color: '#1890ff' }} />
          <span>客戶分類管理</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={isMobile ? '95%' : 1000}
      style={{ top: 20 }}
    >
      <Row gutter={16} style={{ height: '70vh' }}>
        {/* 左側：分類列表 */}
        <Col span={isMobile ? 24 : 14}>
          <Card
            title={
              <Space>
                <ApartmentOutlined />
                <span>分類結構</span>
                <Badge count={categories.length} style={{ backgroundColor: '#52c41a' }} />
              </Space>
            }
            extra={
              <Button
                type="text"
                size="small"
                icon={isAllExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                onClick={toggleAllExpansion}
              >
                {isAllExpanded ? '全部折疊' : '全部展開'}
              </Button>
            }
            bodyStyle={{ padding: '12px', maxHeight: '60vh', overflowY: 'auto' }}
          >
            {categories.length === 0 ? (
              <Alert
                message="尚無客戶分類"
                description="點擊右側「新增分類」按鈕開始建立客戶分類結構。"
                type="info"
                showIcon
              />
            ) : (
              <div>
                {buildCustomerCategoryTree(categories).map(category => renderCategoryItem(category))}
              </div>
            )}
          </Card>
        </Col>

        {/* 右側：操作表單 */}
        <Col span={isMobile ? 24 : 10}>
          <Card
            title={
              <Space>
                <NodeIndexOutlined />
                <span>
                  {operationMode === 'edit' ? '編輯分類' : 
                   operationMode === 'addChild' ? '新增子分類' : '新增分類'}
                </span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                size="small"
              >
                新增分類
              </Button>
            }
            bodyStyle={{ padding: '16px' }}
          >
            <div ref={formContainerRef}>
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{ sortCode: 0 }}
              >
                <Form.Item
                  label="分類名稱"
                  name="name"
                  rules={[
                    { required: true, message: '請輸入分類名稱' },
                    { max: 50, message: '分類名稱不能超過50個字符' }
                  ]}
                >
                  <Input
                    ref={nameInputRef}
                    placeholder="請輸入分類名稱"
                    maxLength={50}
                  />
                </Form.Item>

                <Form.Item
                  label="分類描述"
                  name="description"
                  rules={[
                    { max: 200, message: '分類描述不能超過200個字符' }
                  ]}
                >
                  <Input.TextArea
                    placeholder="請輸入分類描述（選填）"
                    rows={3}
                    maxLength={200}
                  />
                </Form.Item>

                <Form.Item
                  label="上級分類"
                  name="parentID"
                >
                  <TreeSelect
                    placeholder="請選擇上級分類（選填）"
                    allowClear
                    treeData={categoryTreeData}
                    disabled={operationMode === 'addChild'}
                  />
                </Form.Item>

                <Form.Item
                  label="排序代碼"
                  name="sortCode"
                  rules={[
                    { type: 'number', min: 0, max: 9999, message: '排序代碼必須在0-9999之間' }
                  ]}
                >
                  <Input
                    type="number"
                    placeholder="請輸入排序代碼"
                    min={0}
                    max={9999}
                  />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      icon={<SaveOutlined />}
                    >
                      {operationMode === 'edit' ? '更新' : '新增'}
                    </Button>
                    <Button
                      onClick={resetForm}
                      icon={<UndoOutlined />}
                    >
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </div>
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default CustomerCategoryManagement;
