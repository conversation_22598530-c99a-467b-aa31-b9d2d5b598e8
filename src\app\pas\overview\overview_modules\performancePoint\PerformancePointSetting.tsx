import React, { useEffect, useState } from "react";
import {
    Button,
    Table,
    Modal,
    Form,
    Input,
    message,
    Row,
    Col,
    Card,
    Typography,
    Space,
    Tag,
    Tooltip,
    Empty,
    Popconfirm
} from "antd";
import {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    SearchOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from "antd/es/table";
import {
    PerformancePointGroup,
    getPerformancePointGroupList,
    addPerformancePointGroup,
    editPerformancePointGroup,
    deletePerformancePointGroup,
    createEmptyPerformancePointGroup,
} from "@/services/pas/PerformancePoint/PerformancePointGroupService";

import {
    PerformancePointType,
    getPerformancePointTypeList,
    addPerformancePointType,
    editPerformancePointType,
    deletePerformancePointType,
    createEmptyPerformancePointType,
} from "@/services/pas/PerformancePoint/PerformancePointTypeService";
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';

const { Title, Text } = Typography;

const PerformancePointSetting: React.FC = () => {
    // --- 狀態管理 ---
    const [groupList, setGroupList] = useState<PerformancePointGroup[]>([]);
    const [selectedGroup, setSelectedGroup] = useState<PerformancePointGroup | null>(null);
    const [searchText, setSearchText] = useState('');
    const [pointTypeSearchText, setPointTypeSearchText] = useState('');

    const [pointTypeList, setPointTypeList] = useState<PerformancePointType[]>([]);

    const [loadingGroup, setLoadingGroup] = useState(false);
    const [loadingType, setLoadingType] = useState(false);

    // Modal 顯示控制 & 編輯資料
    const [groupModalVisible, setGroupModalVisible] = useState(false);
    const [groupModalLoading, setGroupModalLoading] = useState(false);
    const [groupEditingData, setGroupEditingData] = useState<PerformancePointGroup | null>(null);

    const [typeModalVisible, setTypeModalVisible] = useState(false);
    const [typeModalLoading, setTypeModalLoading] = useState(false);
    const [typeEditingData, setTypeEditingData] = useState<PerformancePointType | null>(null);

    // 刪除參數
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [deleteMode, setDeleteMode] = useState<'Group' | 'Type' | null>(null);

    // Form Instance
    const [groupForm] = Form.useForm();
    const [typeForm] = Form.useForm();

    // --- 載入點數群組列表 ---
    const loadGroupList = async () => {
        setLoadingGroup(true);

        const previousGroupUid = selectedGroup?.uid; // 儲存目前選中的 groupUid
        const res = await getPerformancePointGroupList();
        setLoadingGroup(false);

        if (res.success) {
            const groups = res.data || [];
            setGroupList(groups);

            if (groups.length > 0) {
                // 嘗試找到之前選中的群組
                const matchedGroup = groups.find((g) => g.uid === previousGroupUid);
                if (matchedGroup) {
                    setSelectedGroup(matchedGroup);
                } else {
                    setSelectedGroup(groups[0]); // 若找不到，就選第一個
                }
            } else {
                setSelectedGroup(null);
                setPointTypeList([]);
            }
        } else {
            message.error("載入點數群組失敗：" + res.message);
        }
    };


    // --- 載入選中群組的點數類型 ---
    const loadPointTypeList = async (groupUid: string) => {
        setLoadingType(true);
        const res = await getPerformancePointTypeList(groupUid);
        setLoadingType(false);
        if (res.success) {
            setPointTypeList(res.data || []);
        } else {
            message.error("載入點數類型失敗：" + res.message);
        }
    };

    useEffect(() => {
        loadGroupList();
    }, []);

    // 當 selectedGroup 變動時，載入其點數類型
    useEffect(() => {
        if (selectedGroup) {
            loadPointTypeList(selectedGroup.uid);
            setTypeModalVisible(false); // 切換群組時自動關閉類型編輯
        } else {
            setPointTypeList([]);
        }
    }, [selectedGroup]);

    // --- Table欄位 ---
    const groupColumns: ColumnsType<PerformancePointGroup> = [
        {
            title: "群組名稱",
            dataIndex: "groupName",
            key: "groupName",
            sorter: (a, b) => a.groupName.localeCompare(b.groupName),
            render: (text, record) => (
                <Space>
                    <Text strong>{text}</Text>
                    {record.pointTypeCount > 0 && (
                        <Tag color="green">{record.pointTypeCount} 個類型</Tag>
                    )}
                </Space>
            )
        },
        {
            title: "權重比例",
            dataIndex: "weightRatio",
            key: "weightRatio",
            width: 120,
            sorter: (a, b) => Number(a.weightRatio) - Number(b.weightRatio),
            render: (text) => <Tag color="blue">{text}%</Tag>
        },
        {
            title: "操作",
            key: "action",
            width: 150,
            render: (_, record) => (
                <div onClick={(e) => e.stopPropagation()}>
                    <Space size="small">
                        <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => {
                                setGroupEditingData(record);
                                groupForm.setFieldsValue(record);
                                setGroupModalVisible(true);
                            }}
                        >
                            編輯
                        </Button>

                        <Popconfirm
                            title="確定要刪除此筆資料嗎？"
                            onConfirm={() => {
                                setDeleteUid(record.uid);
                                setDeleteMode('Group');
                            }} // 不直接刪除
                            okText="確認"
                            cancelText="取消"
                        >
                            <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                            >
                                刪除
                            </Button>
                        </Popconfirm>

                    </Space>
                </div>
            ),
        },
    ];

    const pointTypeColumns: ColumnsType<PerformancePointType> = [
        {
            title: "類型名稱",
            dataIndex: "pointName",
            key: "pointName",
            sorter: (a, b) => a.pointName.localeCompare(b.pointName),
            render: (text) => <Text strong>{text}</Text>
        },
        {
            title: "權重比例",
            dataIndex: "weightRatio",
            key: "weightRatio",
            width: 120,
            sorter: (a, b) => Number(a.weightRatio) - Number(b.weightRatio),
            render: (text) => <Tag color="blue">{text}%</Tag>
        },
        {
            title: "操作",
            key: "action",
            width: 150,
            render: (_, record) => (
                <div onClick={(e) => e.stopPropagation()}>
                    <Space size="small">
                        <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => {
                                setTypeEditingData(record);
                                typeForm.setFieldsValue({
                                    ...record,
                                    groupUid: [record.groupUid],
                                });
                                setTypeModalVisible(true);
                            }}
                        >
                            編輯
                        </Button>
                        <Popconfirm
                            title="確定要刪除此筆資料嗎？"
                            onConfirm={() => {
                                setDeleteUid(record.uid);
                                setDeleteMode('Type');
                            }} // 不直接刪除
                            okText="確認"
                            cancelText="取消"
                        >
                            <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                            >
                                刪除
                            </Button>
                        </Popconfirm>
                    </Space>
                </div>
            ),
        },
    ];

    // 篩選群組列表
    const filteredGroupList = groupList.filter(group =>
        group.groupName.toLowerCase().includes(searchText.toLowerCase()) ||
        group.weightRatio.toString().includes(searchText)
    );

    // 篩選點數類型列表
    const filteredPointTypeList = pointTypeList.filter(type =>
        type.pointName.toLowerCase().includes(pointTypeSearchText.toLowerCase()) ||
        type.weightRatio.toString().includes(pointTypeSearchText) ||
        type.groupName.toLowerCase().includes(pointTypeSearchText.toLowerCase())
    );

    const handledataDelete = async (uid: string) => {
        try {
            if (deleteMode === 'Type') {
                const res = await deletePerformancePointType(uid);
                if (res.success && res.data?.result) {
                    message.success('刪除成功');
                    if (selectedGroup) {
                        loadPointTypeList(selectedGroup.uid);
                    }
                } else {
                    message.error(res.data?.msg || '刪除失敗');
                }

            }
            else if (deleteMode === 'Group') {
                const res = await deletePerformancePointGroup(uid);
                if (res.success) {
                    message.success("刪除成功");
                    if (selectedGroup?.uid === uid) {
                        setSelectedGroup(null);
                        setPointTypeList([]);
                    }
                    loadGroupList();
                } else {
                    message.error("刪除失敗：" + res.message);
                }

            }
            else {
                message.error('刪除模式不正確');
            }

        } catch (err: any) {
            message.error(err.message || '刪除錯誤');
        }
    };

    // 檢查群組名稱是否重複
    const checkGroupNameDuplicate = async (groupName: string, excludeUid?: string) => {
        const duplicate = groupList.find(
            group => group.groupName === groupName && group.uid !== excludeUid
        );
        if (duplicate) {
            throw new Error('群組名稱已存在');
        }
    };

    // 檢查點數類型名稱是否重複
    const checkPointNameDuplicate = async (pointName: string, excludeUid?: string) => {
        const duplicate = pointTypeList.find(
            type => type.pointName === pointName && type.uid !== excludeUid
        );
        if (duplicate) {
            throw new Error('點數類型名稱已存在');
        }
    };

    // 驗證權重比例
    const validateWeightRatio = (value: string) => {


        const num = parseFloat(value);

        if (isNaN(num)) {
            return Promise.reject('請輸入有效的數字');
        }

        if (num < 0) {
            return Promise.reject('權重比例不能小於0');
        }

        if (num > 100) {
            return Promise.reject('權重比例不能大於100');
        }

        if (value.includes('.') && value.split('.')[1].length > 2) {
            return Promise.reject('權重比例最多只能有兩位小數');
        }

        return Promise.resolve();
    };

    // --- Modal 提交處理 ---
    const handleGroupModalOk = () => {
        groupForm.validateFields()
            .then(async (values) => {
                try {
                    // 檢查名稱重複
                    await checkGroupNameDuplicate(values.groupName, groupEditingData?.uid);

                    setGroupModalLoading(true);
                    let res;
                    if (groupEditingData?.uid) {
                        res = await editPerformancePointGroup({ ...groupEditingData, ...values });
                    } else {
                        const payload = {
                            ...createEmptyPerformancePointGroup(),
                            ...values,
                            weightRatio: values.weightRatio != null ? String(values.weightRatio) : "0",
                        };
                        res = await addPerformancePointGroup(payload);
                    }
                    setGroupModalLoading(false);
                    if (res.success) {
                        message.success(groupEditingData?.uid ? "編輯成功" : "新增成功");
                        setGroupModalVisible(false);
                        loadGroupList();
                    } else {
                        message.error("操作失敗：" + res.message);
                    }
                } catch (error: any) {
                    setGroupModalLoading(false);
                    message.error(error.message || "操作失敗");
                }
            })
            .catch((error) => {
                message.error('表單驗證失敗：' + error.message);
            });
    };

    const handleTypeModalOk = () => {
        typeForm
            .validateFields()
            .then(async (values) => {
                try {
                    // 檢查名稱重複
                    await checkPointNameDuplicate(values.pointName, typeEditingData?.uid);

                    setTypeModalLoading(true);
                    const payload = {
                        ...createEmptyPerformancePointType(),
                        ...typeEditingData,
                        ...values,
                        weightRatio: values.weightRatio != null ? String(values.weightRatio) : "0",
                        groupUid: selectedGroup?.uid,
                    };

                    let res;
                    if (typeEditingData?.uid) {
                        res = await editPerformancePointType(payload);
                    } else {
                        res = await addPerformancePointType(payload);
                    }
                    setTypeModalLoading(false);
                    if (res.success) {
                        message.success(typeEditingData?.uid ? "編輯成功" : "新增成功");
                        setTypeModalVisible(false);
                        if (selectedGroup) {
                            loadPointTypeList(selectedGroup.uid);
                        }
                        loadGroupList();
                    } else {
                        message.error("操作失敗：" + res.message);
                    }
                } catch (error: any) {
                    setTypeModalLoading(false);
                    message.error(error.message || "操作失敗");
                }
            })
            .catch((error) => {
                message.error('表單驗證失敗：' + error.message);
            });
    };

    return (
        <div className="performance-point-setting" style={{ padding: '24px' }}>
            <Row gutter={[24, 24]}>
                <Col span={24}>
                    <Card>
                        <Space direction="vertical" style={{ width: '100%' }} size="large">
                            <Row justify="space-between" align="middle">
                                <Col>
                                    <Title level={2} style={{ margin: 0 }}>績效點數設定</Title>
                                </Col>
                                <Col>
                                    <Space>
                                        <Tooltip title="點數群組用於分類不同類型的績效指標">
                                            <InfoCircleOutlined style={{ fontSize: '16px', color: '#1890ff' }} />
                                        </Tooltip>
                                    </Space>
                                </Col>
                            </Row>

                            <Row gutter={[24, 24]}>
                                {/* 點數群組管理區塊 */}
                                <Col span={24} lg={12}>
                                    <Card
                                        title={
                                            <Space>
                                                <Text strong>點數群組管理</Text>
                                                <Tag color="blue">{groupList.length} 個群組</Tag>
                                            </Space>
                                        }
                                        extra={
                                            <Space>
                                                <Input.Search
                                                    placeholder="搜尋群組..."
                                                    allowClear
                                                    style={{ width: 200 }}
                                                    onChange={e => setSearchText(e.target.value)}
                                                    prefix={<SearchOutlined />}
                                                />
                                                <Button
                                                    type="primary"
                                                    icon={<PlusOutlined />}
                                                    onClick={() => {
                                                        setGroupEditingData(null);
                                                        groupForm.resetFields();
                                                        setGroupModalVisible(true);
                                                    }}
                                                >
                                                    新增群組
                                                </Button>
                                            </Space>
                                        }
                                    >
                                        <Table
                                            rowKey="uid"
                                            columns={groupColumns}
                                            dataSource={filteredGroupList}
                                            loading={loadingGroup}
                                            pagination={{
                                                pageSize: 10,
                                                showSizeChanger: true,
                                                showTotal: (total) => `共 ${total} 筆資料`,
                                            }}
                                            rowClassName={(record) =>
                                                `${record.uid === deleteUid ? 'row-deleting-pulse' : ''} ${selectedGroup?.uid === record.uid ? 'row-selected' : ''
                                                }`
                                            }
                                            onRow={(record) => ({
                                                onClick: () => setSelectedGroup(record),
                                                style: {
                                                    cursor: 'pointer',
                                                    transition: 'all 0.3s',
                                                },
                                            })}
                                            locale={{
                                                emptyText: <Empty description="尚無點數群組" />
                                            }}
                                        />
                                    </Card>
                                </Col>

                                {/* 點數類型管理區塊 */}
                                <Col span={24} lg={12}>
                                    <Card
                                        title={
                                            <Space>
                                                <Text strong>點數類型管理</Text>
                                                {selectedGroup && (
                                                    <Tag color="purple">{selectedGroup.groupName}</Tag>
                                                )}
                                            </Space>
                                        }
                                        extra={
                                            <Space>
                                                <Input.Search
                                                    placeholder="搜尋類型..."
                                                    allowClear
                                                    style={{ width: 200 }}
                                                    onChange={e => setPointTypeSearchText(e.target.value)}
                                                    prefix={<SearchOutlined />}
                                                />
                                                <Button
                                                    type="primary"
                                                    icon={<PlusOutlined />}
                                                    disabled={!selectedGroup}
                                                    onClick={() => {
                                                        setTypeEditingData(null);
                                                        typeForm.resetFields();
                                                        if (selectedGroup) {
                                                            typeForm.setFieldsValue({ groupUid: [selectedGroup.uid] });
                                                        }
                                                        setTypeModalVisible(true);
                                                    }}
                                                >
                                                    新增類型
                                                </Button>
                                            </Space>
                                        }
                                    >
                                        {selectedGroup ? (
                                            <Table
                                                rowKey="uid"
                                                columns={pointTypeColumns}
                                                dataSource={filteredPointTypeList}
                                                loading={loadingType}
                                                pagination={{
                                                    pageSize: 10,
                                                    showSizeChanger: true,
                                                    showTotal: (total) => `共 ${total} 筆資料`,
                                                }}
                                                rowClassName={(record) =>
                                                    record.uid === deleteUid ? 'row-deleting-pulse' : ''
                                                }
                                                locale={{
                                                    emptyText: <Empty description="尚無點數類型" />
                                                }}
                                            />
                                        ) : (
                                            <Empty description="請先選擇點數群組" />
                                        )}
                                    </Card>
                                </Col>
                            </Row>
                        </Space>
                    </Card>
                </Col>
            </Row>

            {/* Modal 部分保持不變 */}
            <Modal
                title={groupEditingData?.uid ? "編輯點數群組" : "新增點數群組"}
                open={groupModalVisible}
                confirmLoading={groupModalLoading}
                onCancel={() => setGroupModalVisible(false)}
                onOk={handleGroupModalOk}
                destroyOnClose
            >
                <Form form={groupForm} layout="vertical" initialValues={{ weightRatio: 0 }}>
                    <Form.Item
                        label="群組名稱"
                        name="groupName"
                        rules={[
                            { required: true, message: "請輸入群組名稱" },
                            { max: 20, message: "群組名稱最多20個字" },
                            { min: 2, message: "群組名稱最少2個字" },
                        ]}
                    >
                        <Input maxLength={20} />
                    </Form.Item>
                    <Form.Item
                        label="權重比例"
                        name="weightRatio"
                        rules={[
                            { required: true, message: "請輸入權重比例" },
                            { validator: (_, value) => validateWeightRatio(value) }
                        ]}
                        tooltip="請輸入0-100之間的數字，最多保留兩位小數"
                    >
                        <Input type="number" step="0.01" suffix="%" />
                    </Form.Item>
                </Form>
            </Modal>

            <Modal
                title={typeEditingData?.uid ? "編輯點數類型" : "新增點數類型"}
                open={typeModalVisible}
                confirmLoading={typeModalLoading}
                onCancel={() => setTypeModalVisible(false)}
                onOk={handleTypeModalOk}
                destroyOnClose
            >
                <Form form={typeForm} layout="vertical" initialValues={{ weightRatio: 0 }}>
                    <Form.Item
                        label="類型名稱"
                        name="pointName"
                        rules={[
                            { required: true, message: "請輸入類型名稱" },
                            { max: 20, message: "類型名稱最多20個字" },
                            { min: 2, message: "類型名稱最少2個字" },
                        ]}
                    >
                        <Input maxLength={20} />
                    </Form.Item>
                    <Form.Item
                        label="權重比例"
                        name="weightRatio"
                        rules={[
                            { required: true, message: "請輸入權重比例" },
                            { validator: (_, value) => validateWeightRatio(value) }
                        ]}
                        tooltip="請輸入0-100之間的數字，最多保留兩位小數"
                    >
                        <Input type="number" step="0.01" suffix="%" />
                    </Form.Item>
                </Form>
            </Modal>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handledataDelete(deleteUid);
                            setDeleteUid(null);
                            setDeleteMode(null);
                        } catch {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}

            <style jsx global>{`
                .performance-point-setting .ant-card {
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
                }
                
                .performance-point-setting .row-selected {
                    background-color: #e6f7ff;
                }
                

                .performance-point-setting .ant-table-row {
                    cursor: pointer;
                    transition: all 0.3s;
                }
                
                .performance-point-setting .ant-table-row:hover {
                    background-color: #f5f5f5;
                }
            `}</style>
        </div>
    );
};

export default PerformancePointSetting;
