import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 系統選單介面
export interface SystemMenu {
    systemMenuId: string;
    systemGroupId: string;
    label: string;
    key: string;
    icon: string | null;
    parentId: string | null;
    children?: SystemMenu[];
}

// 系統選單樹狀結構
export interface MenuTreeResponse {
    systemGroupId: string;
    systemCode: string;
    name: string;
    menus: SystemMenu[];
}
  
// 獲取系統選單列表
export async function getSystemMenus(): Promise<ApiResponse<SystemMenu[]>> {
    try {
        const response = await httpClient(apiEndpoints.getSystemMenus, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取系統選單列表失敗",
            data: []
        };
    }
}

// 新增系統選單
export async function createSystemMenu(data: Partial<SystemMenu>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addSystemMenu, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增系統選單失敗",
        };
    }
}

// 更新系統選單
export async function updateSystemMenu(data: Partial<SystemMenu>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editSystemMenu, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新系統選單失敗",
        };
    }
}

// 刪除系統選單
export async function deleteSystemMenu(systemMenuId: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteSystemMenu, {
            method: "POST",
            body: JSON.stringify({ systemMenuId, icon: "" }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除系統選單失敗",
        };
    }
}

// 獲取選單樹狀結構
export async function getAllMenuTree(): Promise<ApiResponse<MenuTreeResponse[]>> {
    try {
      const response = await httpClient(apiEndpoints.getAllMenu, {
        method: "GET",
      });
      return response;
    } catch (error: any) {
      return {
        success: false,
        message: error.message || "獲取選單樹狀結構失敗",
        data: []
      };
    }
}