﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;

namespace FAST_ERP_Backend.Services.Common
{
    public class RolesPermissionsService : IRolesPermissionsService
    {
        private readonly ERPDbContext _context;

        public RolesPermissionsService(ERPDbContext context)
        {
            _context = context;
        }

        public async Task<List<RolesPermissionsDTO>> GetRolesPermissionsAsync(string _rolesId)
        {
            IQueryable<RolesPermissions> query = _context.Common_RolesPermissions;

            if (!string.IsNullOrEmpty(_rolesId))
            {
                query = query.Where(e => e.RolesId == _rolesId);
            }

            var result = await query
                .OrderBy(e => e.CreateTime)
                .Select(t => new RolesPermissionsDTO
                {
                    RolesPermissionsId = t.RolesPermissionsId,
                    RolesId = t.RolesId,
                    SystemMenuId = t.SystemMenuId
                })
                .ToListAsync();

            return result;
        }

        public async Task<(bool, string)> AddRolesPermissionsAsync(RolesPermissionsDTO dto, string tokenUid, string rolesId, long CreateTime)
        {
            try
            {
                var newRolesPermission = new RolesPermissions
                {
                    RolesPermissionsId = Guid.NewGuid().ToString().Trim(),
                    RolesId = rolesId,
                    SystemMenuId = dto.SystemMenuId,
                    CreateTime = CreateTime,
                    CreateUserId = tokenUid,
                };

                await _context.Common_RolesPermissions.AddAsync(newRolesPermission);
                return (true, "新增角色權限成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增角色權限失敗: {ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteRolesPermissionsAsync(RolesPermissions rolesPermissions, string tokenUid, string rolesId, long DeleteTime)
        {

            try
            {
                rolesPermissions.DeleteUserId = tokenUid;
                rolesPermissions.DeleteTime = DeleteTime;
                rolesPermissions.IsDeleted = true;

                return (true, "刪除角色權限成功");
            }
            catch (Exception ex)
            {
                await _context.Database.RollbackTransactionAsync();
                return (false, $"刪除角色權限失敗: {ex.Message}");
            }
        }
    }
}
