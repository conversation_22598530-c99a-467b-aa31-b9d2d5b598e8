.customTable {
    --ant-primary-color: #3b82f6;
}

.customTable .ant-table-thead>tr>th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e2e8f0;
}

.customTable .ant-table-tbody>tr:hover>td {
    background-color: #f8fafc !important;
}

.customTable .ant-table-tbody>tr>td {
    border-bottom: 1px solid #f1f5f9;
    padding: 16px 12px;
}

.customTable .ant-pagination {
    margin: 24px 0;
    text-align: center;
}

.customTable .ant-pagination-item {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.customTable .ant-pagination-item-active {
    background: #3b82f6;
    border-color: #3b82f6;
}

.customTable .ant-pagination-item-active a {
    color: white;
}

.statsCard {
    transition: all 0.3s ease;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.statsCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.searchCard {
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.actionCard {
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.tableCard {
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.pageTitle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.selectedInfo {
    background: linear-gradient(135deg, #ebf4ff 0%, #dbeafe 100%);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    color: #1e40af;
}

.actionButton {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.actionButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.primaryButton {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    color: white;
}

.primaryButton:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
    color: white;
}

/* 流程步驟區域 */
.processSection {
    margin-bottom: 24px;
    padding: 24px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.processHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.processTitle {
    font-size: 18px;
    font-weight: 600;
    color: #262626;
    margin: 0;
}