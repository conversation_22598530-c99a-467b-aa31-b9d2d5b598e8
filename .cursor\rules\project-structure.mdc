---
description: 
globs: 
alwaysApply: false
---
# Fast ERP Frontend 專案結構指南

這個專案是使用 Next.js 框架開發的前端應用程式。以下是主要的目錄結構和重要文件說明：

## 核心配置文件
- [next.config.js](mdc:next.config.js) - Next.js 的主要配置文件
- [tsconfig.json](mdc:tsconfig.json) - TypeScript 配置
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS 配置

## 主要源碼結構 (/src)
- `/app` - Next.js 13+ 的應用程式路由和頁面組件
- `/components` - 可重用的 React 組件
- `/services` - API 服務和後端整合
- `/store` - 狀態管理相關文件
- `/utils` - 通用工具函數
- `/types` - TypeScript 類型定義
- `/constants` - 常量定義
- `/contexts` - React Context 相關文件
- `/styles` - 全局樣式文件

## 重要入口文件
- [src/middleware.ts](mdc:src/middleware.ts) - Next.js 中間件配置

## 開發和部署
- [Dockerfile](mdc:Dockerfile) - 生產環境容器配置
- [Dockerfile.dev](mdc:Dockerfile.dev) - 開發環境容器配置

請注意：
1. 所有新的頁面組件應放在 `/src/app` 目錄下
2. 共用組件應放在 `/src/components` 目錄下
3. API 整合相關代碼應放在 `/src/services` 目錄下

