# Backend JWT Middleware Configuration 文檔

## 概述

Backend JWT Middleware Configuration 為 FastERP 後端的 JWT 驗證中介軟體添加了環境基礎控制功能，允許在開發環境中禁用 JWT 驗證，同時確保生產環境的安全性。

## 功能特點

### 🔒 安全性保證
- **生產環境保護**: 禁止在生產環境中禁用 JWT 驗證
- **開發環境靈活性**: 允許在開發環境中禁用 JWT 驗證以便測試
- **配置驗證**: 啟動時驗證配置的合法性
- **詳細日誌**: 提供清楚的操作日誌和警告訊息

### ⚙️ 配置選項
- **環境檢測**: 自動檢測當前運行環境
- **配置驅動**: 通過 appsettings.json 控制 JWT 驗證
- **預設安全**: 預設啟用 JWT 驗證，確保安全性
- **錯誤處理**: 完善的錯誤處理和異常訊息

## 技術實現

### 配置結構

#### appsettings.json 配置
```json
{
  "AuthToken": {
    "TokenKey": "12345678901234567890123456789012",
    "TokenIssuer": "sfaic",
    "TokenAudience": "fasterp",
    "TokenExpirationHours": "24",
    "JwtValidationEnabled": true
  }
}
```

#### appsettings.Development.json 配置（開發環境專用）
```json
{
  "AuthToken": {
    "JwtValidationEnabled": false
  }
}
```

### 中介軟體更新

#### PasswordValidationMiddleware.cs 更新
```csharp
public class PasswordValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IConfiguration _configuration;
    private readonly TokenHandler _tokenHandler;
    private readonly IWebHostEnvironment _environment;
    private readonly bool _jwtValidationEnabled;

    public PasswordValidationMiddleware(RequestDelegate next, IConfiguration configuration, IWebHostEnvironment environment)
    {
        _next = next;
        _configuration = configuration;
        _environment = environment;
        _tokenHandler = new TokenHandler(configuration);
        
        // 讀取 JWT 驗證啟用設定，預設為 true
        _jwtValidationEnabled = _configuration.GetValue<bool>("AuthToken:JwtValidationEnabled", true);
        
        // 在開發環境中，如果設定為禁用 JWT 驗證，則記錄警告
        if (!_jwtValidationEnabled && _environment.IsDevelopment())
        {
            Console.WriteLine("⚠️ 警告: JWT 驗證已在開發環境中禁用。這僅應用於開發和測試目的。");
        }
        else if (!_jwtValidationEnabled && !_environment.IsDevelopment())
        {
            throw new InvalidOperationException("❌ 錯誤: 不允許在生產環境中禁用 JWT 驗證。請檢查 AuthToken:JwtValidationEnabled 設定。");
        }
    }

    public async Task InvokeAsync(HttpContext context)
    {
        //驗證路徑
        if (!ShouldValidatePassword(context))
        {
            // 如果 JWT 驗證被禁用（僅限開發環境），則跳過驗證
            if (!_jwtValidationEnabled)
            {
                Console.WriteLine($"🔓 開發模式: 跳過 JWT 驗證 - 路徑: {context.Request.Path}");
                await _next(context);
                return;
            }

            // 先檢查是否帶有 Token,沒有則設成空字串
            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last() ?? string.Empty;
            try
            {
                // 驗證 Token
                var claimsPrincipal = _tokenHandler.DecodeJwtToken(token);
                context.User = claimsPrincipal;
                await _next(context);
                return;
            }
            catch (SecurityTokenException ex)
            {
                Console.WriteLine($"❌ JWT 驗證失敗: {ex.Message} - 路徑: {context.Request.Path}");
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("請重新登入");
                return;
            }
        }
        await _next(context);
    }
}
```

## 使用方法

### 開發環境配置

#### 1. 創建 appsettings.Development.json
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AuthToken": {
    "JwtValidationEnabled": false
  }
}
```

#### 2. 設定環境變數
```bash
# Windows
set ASPNETCORE_ENVIRONMENT=Development

# Linux/macOS
export ASPNETCORE_ENVIRONMENT=Development
```

#### 3. 啟動應用程式
```bash
dotnet run
```

### 生產環境配置

#### 1. 確保 appsettings.json 中的設定
```json
{
  "AuthToken": {
    "JwtValidationEnabled": true
  }
}
```

#### 2. 設定環境變數
```bash
# Windows
set ASPNETCORE_ENVIRONMENT=Production

# Linux/macOS
export ASPNETCORE_ENVIRONMENT=Production
```

## 安全考量

### 環境檢測機制
- **自動檢測**: 使用 `IWebHostEnvironment.IsDevelopment()` 檢測環境
- **強制驗證**: 在非開發環境中強制啟用 JWT 驗證
- **啟動檢查**: 應用程式啟動時驗證配置合法性

### 錯誤處理
```csharp
// 生產環境中嘗試禁用 JWT 驗證會拋出異常
if (!_jwtValidationEnabled && !_environment.IsDevelopment())
{
    throw new InvalidOperationException("❌ 錯誤: 不允許在生產環境中禁用 JWT 驗證。請檢查 AuthToken:JwtValidationEnabled 設定。");
}
```

### 日誌記錄
- **開發模式警告**: 清楚標示 JWT 驗證已禁用
- **驗證失敗日誌**: 記錄 JWT 驗證失敗的詳細信息
- **路徑追蹤**: 記錄請求路徑以便調試

## 測試指南

### 開發環境測試

#### 1. 禁用 JWT 驗證測試
```bash
# 設定開發環境
export ASPNETCORE_ENVIRONMENT=Development

# 在 appsettings.Development.json 中設定
{
  "AuthToken": {
    "JwtValidationEnabled": false
  }
}

# 啟動應用程式並測試 API 端點
curl -X GET http://localhost:7136/api/test
```

#### 2. 啟用 JWT 驗證測試
```bash
# 在 appsettings.Development.json 中設定
{
  "AuthToken": {
    "JwtValidationEnabled": true
  }
}

# 測試需要 JWT Token 的 API
curl -X GET http://localhost:7136/api/test \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 生產環境測試

#### 1. 配置驗證測試
```bash
# 設定生產環境
export ASPNETCORE_ENVIRONMENT=Production

# 嘗試禁用 JWT 驗證（應該失敗）
{
  "AuthToken": {
    "JwtValidationEnabled": false
  }
}

# 啟動應用程式（應該拋出異常）
dotnet run
```

## 故障排除

### 常見問題

#### 1. 應用程式啟動失敗
**問題**: 在生產環境中禁用 JWT 驗證導致啟動失敗
**解決方案**: 
```json
{
  "AuthToken": {
    "JwtValidationEnabled": true
  }
}
```

#### 2. 開發環境中 JWT 驗證仍然啟用
**問題**: appsettings.Development.json 配置未生效
**解決方案**: 
- 確認 `ASPNETCORE_ENVIRONMENT=Development`
- 檢查 appsettings.Development.json 檔案位置
- 驗證 JSON 格式正確性

#### 3. 日誌訊息未顯示
**問題**: 看不到 JWT 驗證相關的日誌
**解決方案**: 
- 檢查日誌等級設定
- 確認 Console 日誌提供者已啟用
- 驗證中介軟體註冊順序

### 調試技巧

#### 1. 檢查配置值
```csharp
// 在 Startup 或 Program.cs 中添加調試代碼
var jwtEnabled = builder.Configuration.GetValue<bool>("AuthToken:JwtValidationEnabled", true);
Console.WriteLine($"JWT Validation Enabled: {jwtEnabled}");
```

#### 2. 驗證環境設定
```csharp
// 檢查當前環境
var environment = builder.Environment.EnvironmentName;
Console.WriteLine($"Current Environment: {environment}");
Console.WriteLine($"Is Development: {builder.Environment.IsDevelopment()}");
```

## 最佳實踐

### 配置管理
1. **分離配置**: 使用不同的 appsettings 檔案管理不同環境
2. **預設安全**: 預設啟用 JWT 驗證，確保安全性
3. **明確配置**: 在開發環境中明確設定 JWT 驗證狀態

### 安全性
1. **環境隔離**: 確保開發和生產環境的配置隔離
2. **定期檢查**: 定期檢查生產環境的 JWT 驗證狀態
3. **監控日誌**: 監控 JWT 驗證相關的日誌訊息

### 開發流程
1. **本地開發**: 在本地開發時可以禁用 JWT 驗證
2. **測試環境**: 在測試環境中啟用 JWT 驗證
3. **生產部署**: 確保生產環境中 JWT 驗證始終啟用

## 實施步驟

### 後端實施步驟

#### 1. 更新 PasswordValidationMiddleware.cs
將提供的代碼更新應用到 `Middlewares/PasswordValidationMiddleware.cs` 檔案中。

#### 2. 創建開發環境配置檔案
在後端專案根目錄創建 `appsettings.Development.json`：
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AuthToken": {
    "JwtValidationEnabled": false
  }
}
```

#### 3. 更新 appsettings.json
確保生產環境配置包含 JWT 驗證設定：
```json
{
  "AuthToken": {
    "TokenKey": "your-secret-key-here",
    "TokenIssuer": "sfaic",
    "TokenAudience": "fasterp",
    "TokenExpirationHours": "24",
    "JwtValidationEnabled": true
  }
}
```

#### 4. 測試配置
- 在開發環境中測試 JWT 驗證禁用功能
- 在生產環境中驗證 JWT 驗證強制啟用
- 檢查日誌輸出是否正確

## 版本歷史

### v1.0.0 (2024-01-07)
- 初始版本發布
- 支援環境基礎的 JWT 驗證控制
- 完整的安全性檢查和錯誤處理
- 詳細的日誌記錄和調試支援
