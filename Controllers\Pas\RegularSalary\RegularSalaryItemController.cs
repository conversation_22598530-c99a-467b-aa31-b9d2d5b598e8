using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("常態薪資項目管理")]
    public class RegularSalaryItemController : ControllerBase
    {
        private readonly IRegularSalaryItemService _service;

        public RegularSalaryItemController(IRegularSalaryItemService service)
        {
            _service = service;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得所有常態薪資項目", Description = "取得所有未刪除的常態薪資項目")]
        public async Task<IActionResult> GetAll()
        {
            var result = await _service.GetAllAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得常態薪資項目明細", Description = "依UID取得常態薪資項目明細")]
        public async Task<IActionResult> GetDetail(string uid)
        {
            var result = await _service.GetByIdAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增常態薪資項目", Description = "新增常態薪資項目資料")]
        public async Task<IActionResult> Add([FromBody] RegularSalaryItemDTO data)
        {
            var (result, msg) = await _service.AddAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯常態薪資項目", Description = "編輯常態薪資項目資料")]
        public async Task<IActionResult> Edit([FromBody] RegularSalaryItemDTO data)
        {
            var (result, msg) = await _service.EditAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除常態薪資項目", Description = "依UID軟刪除常態薪資項目")]
        public async Task<IActionResult> Delete([FromBody] string uid)
        {
            var (result, msg) = await _service.DeleteAsync(uid);
            return Ok(new { result, msg });
        }

        [HttpGet]
        [Route("GetSalaryItemTypeOptions")]
        [SwaggerOperation(Summary = "取得加項/扣項級聯選單", Description = "依照加項或扣項類型取得底下的薪資項目選項")]
        public async Task<IActionResult> GetSalaryItemTypeOptions()
        {
            var result = await _service.GetSalaryItemTypeOptionsAsync();
            return Ok(result);
        }
    }
}
