﻿using System.Security.Claims;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("系統群組管理")]
    public class SystemGroupsController : ControllerBase
    {
        private readonly ISystemGroupsService _Interface;

        public SystemGroupsController(ISystemGroupsService systemGroupsService)
        {
            _Interface = systemGroupsService;
        }

        //取得登入者token資訊,在middleware時就會將資訊存入
        private ClaimsPrincipal LoginUser => HttpContext.User;

        /// <summary>
        /// 取得所有系統群組
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSystemGroups")]
        [SwaggerOperation(Summary = "取得系統群組列表", Description = "取得所有系統群組資料")]
        public async Task<IActionResult> GetSystemGroupsAsync()
        {
            var systemGroup = await _Interface.GetSystemGroupsAsync();
            return Ok(systemGroup);
        }

        /// <summary>
        /// 取得單一系統群組
        /// </summary>
        /// <param name="systemGroupId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSystemGroups/{systemGroupId}")]
        [SwaggerOperation(Summary = "取得單一系統群組", Description = "依ID取得系統群組")]
        public async Task<IActionResult> GetSystemGroupsAsync(string systemGroupId)
        {
            var systemGroup = await _Interface.GetSystemGroupsAsync(systemGroupId);
            return Ok(systemGroup);
        }

        /// <summary>
        /// 新增系統群組
        /// </summary>
        [HttpPost]
        [Route("AddSystemGroups")]
        [SwaggerOperation(Summary = "新增系統群組", Description = "新增系統群組資料")]
        public async Task<IActionResult> AddSystemGroupsAsync([FromBody] SystemGroupsDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.AddSystemGroupsAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }

        /// <summary>
        /// 修改系統群組
        /// </summary>
        [HttpPost]
        [Route("EditSystemGroups")]
        [SwaggerOperation(Summary = "修改系統群組", Description = "修改系統群組資料")]
        public async Task<IActionResult> EditSystemGroupsAsync([FromBody] SystemGroupsDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.EditSystemGroupsAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }

        /// <summary>
        /// 刪除系統群組
        /// </summary>
        [HttpPost]
        [Route("DeleteSystemmGroups")]
        [SwaggerOperation(Summary = "刪除系統群組", Description = "刪除系統群組資料")]
        public async Task<IActionResult> DeleteSystemmGroupsAsync([FromBody] SystemGroupsDTO _data)
        {
            var tokenUid = LoginUser.FindFirst(ClaimTypes.NameIdentifier).Value;
            var (result, msg) = await _Interface.DeleteSystemmGroupsAsync(_data, tokenUid);
            return Ok(new { result, msg });
        }
    }
}
