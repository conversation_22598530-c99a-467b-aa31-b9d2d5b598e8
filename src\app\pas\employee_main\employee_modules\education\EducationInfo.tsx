import { useEffect, useState } from 'react';
import { Button, Upload, message, Table, Form, Input, DatePicker, Popconfirm, Modal, Row, Col, Card, Typography, Space, Divider } from 'antd';
import { getEducationDetail, editEducation, addEducation, deleteEducation, Education, getEducationList, createEmptyEducation } from '@/services/pas/EducationService';
import type { UploadFile } from 'antd/es/upload/interface';
import {
  PlusOutlined,
  BookOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UploadOutlined,
  ReadOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  BankOutlined,
  AuditOutlined,
  FileProtectOutlined,
  TeamOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { getDegreeTypeOptions, getGraduateOptions } from '@/services/pas/OptionParameterService';
import ApiSelect from '@/app/pas/components/ApiSelect';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

type EducationInfoProps = {
  userId: string;
  active: boolean;
};

const EducationInfo: React.FC<EducationInfoProps> = ({ userId, active }) => {
  const [educationList, setEducationList] = useState<Education[]>([]);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [educationDetail, setEducationDetail] = useState<Education | null>(null);
  const [deleteUid, setDeleteUid] = useState<string | null>(null);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);


  useEffect(() => {
    if (active) {
      fetchEducationList();
    }
  }, [active, userId]);

  const fetchEducationList = async () => {
    setLoading(true);
    setErrorMsg('');
    try {
      const { success, data, message: msg } = await getEducationList(userId);
      if (success && data) {
        setEducationList(data);
      } else {
        message.error(msg || '載入資料失敗');
      }
    } catch (error: any) {
      setErrorMsg(error.message || '未知錯誤');
      message.error(error.message || '載入失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleRowClick = async (uid: string) => {
    setModalLoading(true);
    try {
      const { success, data, message: msg } = await getEducationDetail(uid);
      if (success && data) {
        setEducationDetail(data);
        form.resetFields();
        form.setFieldsValue({
          ...createEmptyEducation(),
          schoolName: data.schoolName,
          degreeType: data.degreeType,
          graduateType: data.graduateType,
          departmentName: data.departmentName,
          periodDateStart: data.periodDateStart ? dayjs(data.periodDateStart) : null,
          periodDateEnd: data.periodDateEnd ? dayjs(data.periodDateEnd) : null,
          certificateDate: data.certificateDate ? dayjs(data.certificateDate) : null,
          certificateNumber: data.certificateNumber,
          remark: data.remark,
        });
        setIsModalOpen(true);
      } else {
        message.error(msg || '載入學歷資料失敗');
      }
    } catch (error: any) {
      console.error(error);
      message.error(error.message || '載入學歷資料時發生錯誤');
    } finally {
      setModalLoading(false);
    }
  };

  const handleFormChange = (field: string, value: any) => {
    form.setFieldsValue({ [field]: value });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      setModalLoading(true);

      const formData = new FormData();
      formData.append('userId', userId ?? '');
      formData.append('schoolName', values.schoolName ?? '');
      formData.append('degreeType', values.degreeType ?? '');
      formData.append('graduateType', values.graduateType ?? '');
      formData.append('departmentName', values.departmentName ?? '');
      formData.append('certificateDate', values.certificateDate ? values.certificateDate.format('YYYY-MM-DD') : '');
      formData.append('periodDateStart', values.periodDateStart ? values.periodDateStart.format('YYYY-MM-DD') : '');
      formData.append('periodDateEnd', values.periodDateEnd ? values.periodDateEnd.format('YYYY-MM-DD') : '');
      formData.append('certificateNumber', values.certificateNumber ?? '');
      formData.append('remark', values.remark ?? '');

      // 附加檔案
      fileList.forEach((file) => {
        if (file.originFileObj) {
          formData.append('files', file.originFileObj); // 正確取得原始 File
        }
      });


      let res;
      if (educationDetail) {
        formData.append('uid', educationDetail.uid);
        res = await editEducation(formData); // 修改為接收 FormData 的版本
      } else {
        res = await addEducation(formData);  // 修改為接收 FormData 的版本
      }

      if (res.success && res.data?.result) {
        message.success(educationDetail ? '更新成功' : '新增成功');
        setIsModalOpen(false);
        setFileList([]);
        fetchEducationList();
      } else {
        throw new Error(res.data?.msg || res.message || '儲存失敗');
      }
    } catch (error: any) {
      if (!error?.errorFields) {
        message.error(error.message || '儲存失敗');
      }
    } finally {
      setModalLoading(false);
    }
  };



  const handleDelete = async (uid: string) => {
    try {
      const res = await deleteEducation(uid);
      if (res.success && res.data?.result) {
        message.success('刪除成功');
        fetchEducationList();
      } else {
        message.error(res.data?.msg || '刪除失敗');
      }
    } catch (error: any) {
      message.error(error.message || '刪除過程發生錯誤');
    } finally {

    }
  };

  const handleAddNew = () => {
    setEducationDetail(null);
    setFileList([]); // <-- 清空檔案
    form.resetFields();
    form.setFieldsValue({
      ...createEmptyEducation()
    });
    setIsModalOpen(true);
  };

  const handleModalCancel = () => {
    setIsModalOpen(false);
    setFileList([]); // <-- 清空檔案
  };


  if (!active) return null;

  if (errorMsg) {
    return (
      <div style={{ color: 'red', textAlign: 'center', padding: 20 }}>
        錯誤：{errorMsg}
      </div>
    );
  }

  return (
    <>
      <Card
        title={
          <Space>
            <ReadOutlined />
            <Title level={4} style={{ margin: 0 }}>學歷資料</Title>
          </Space>
        }
        loading={loading}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddNew}
            style={{ borderRadius: '6px' }}
          >
            新增學歷資料
          </Button>
        }
        className="shadow-sm"
        style={{ borderRadius: '8px' }}
      >
        <Table
          rowKey="uid"
          dataSource={educationList}
          columns={[
            {
              title: '學位',
              dataIndex: 'degreeTypeName',
              render: (text) => (
                <Space>
                  <BookOutlined style={{ color: '#1890ff' }} />
                  <Text strong>{text}</Text>
                </Space>
              )
            },
            {
              title: '結業別',
              dataIndex: 'graduateTypeName',
              render: (text) => (
                <Space>
                  <AuditOutlined style={{ color: '#52c41a' }} />
                  <Text>{text}</Text>
                </Space>
              )
            },
            {
              title: '學校名稱',
              dataIndex: 'schoolName',
              render: (text) => (
                <Space>
                  <BankOutlined style={{ color: '#722ed1' }} />
                  <Text>{text}</Text>
                </Space>
              )
            },
            {
              title: '院系科別',
              dataIndex: 'departmentName',
              render: (text) => (
                <Space>
                  <TeamOutlined style={{ color: '#eb2f96' }} />
                  <Text>{text}</Text>
                </Space>
              )
            },
            {
              title: '就讀期間',
              render: (_, record) => {
                const start = record.periodDateStart || '-';
                const end = record.periodDateEnd || '-';
                return (
                  <Space>
                    <CalendarOutlined style={{ color: '#fa8c16' }} />
                    <Text>{start} ~ {end}</Text>
                  </Space>
                );
              },
            },
            {
              title: '發證日期',
              dataIndex: 'certificateDate',
              render: (text) => (
                <Space>
                  <FileProtectOutlined style={{ color: '#13c2c2' }} />
                  <Text>{text || '-'}</Text>
                </Space>
              )
            },
            {
              title: '證件字號',
              dataIndex: 'certificateNumber',
              render: (text) => (
                <Space>
                  <FileTextOutlined style={{ color: '#2f54eb' }} />
                  <Text>{text || '-'}</Text>
                </Space>
              )
            },
            {
              title: '操作',
              render: (_, record) => (
                <Space onClick={(e) => e.stopPropagation()}>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleRowClick(record.uid)}
                  >
                    編輯
                  </Button>
                  <Popconfirm
                    title={
                      <div>
                        <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                        <Text>確定要刪除此筆資料嗎？</Text>
                      </div>
                    }
                    onConfirm={() => setDeleteUid(record.uid)}
                    okText="確認"
                    cancelText="取消"
                    okButtonProps={{ danger: true }}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                    >
                      刪除
                    </Button>
                  </Popconfirm>
                </Space>
              ),
            }
          ]}
          expandable={{
            expandedRowRender: (record) => (
              <div style={{
                padding: '16px 24px',
                background: 'rgba(0, 0, 0, 0.02)',
                borderRadius: '8px',
                margin: '0 24px'
              }}>
                <Space>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                  <Text strong>備註：</Text>
                  <Text>{record.remark || '-'}</Text>
                </Space>
              </div>
            ),
            rowExpandable: (record) => !!record.remark,
          }}
          onRow={(record) => ({
            onClick: () => handleRowClick(record.uid),
          })}
          rowClassName={(record) =>
            record.uid === deleteUid ? 'row-deleting-pulse' : ''
          }
          pagination={{ pageSize: 10, showSizeChanger: false }}
        />
      </Card>

      <Modal
        title={
          <Space>
            <ReadOutlined />
            <Title level={5} style={{ margin: 0 }}>
              {educationDetail ? '編輯學歷資料' : '新增學歷資料'}
            </Title>
          </Space>
        }
        open={isModalOpen}
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        confirmLoading={modalLoading}
        width={800}
        centered
        maskClosable={false}
        destroyOnClose
        styles={{
          header: {
            marginBottom: 0,
            padding: '16px 24px',
            borderBottom: '1px solid #f0f0f0'
          },
          body: {
            padding: '24px'
          }
        }}
      >
        <Form form={form} layout="vertical" className="mt-4">
          {/* 基本資料 */}
          <div className="form-section">
            <Title level={5}>
              <Space>
                <BookOutlined />
                學校資訊
              </Space>
            </Title>
            <Row gutter={[24, 16]}>
              <Col xs={24}>
                <Form.Item
                  label={<Text strong>學校名稱</Text>}
                  name="schoolName"
                  rules={[{ required: true, message: '請輸入學校名稱' }]}
                >
                  <Input
                    placeholder="請輸入學校名稱"
                    style={{ borderRadius: '6px' }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  label={<Text strong>學位代號</Text>}
                  name="degreeType"
                  rules={[{ required: true, message: '請選擇學位代號' }]}
                >
                  <ApiSelect
                    fetchOptions={getDegreeTypeOptions}
                    placeholder="請選擇學位代號"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  label={<Text strong>結業代號</Text>}
                  name="graduateType"
                  rules={[{ required: true, message: '請選擇結業代號' }]}
                >
                  <ApiSelect
                    fetchOptions={getGraduateOptions}
                    placeholder="請選擇結業代號"
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  label={<Text strong>院系科別</Text>}
                  name="departmentName"
                >
                  <Input
                    placeholder="請輸入院系科別"
                    style={{ borderRadius: '6px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* 修業期間 */}
          <Divider style={{ margin: '24px 0' }} />
          <div className="form-section">
            <Title level={5}>
              <Space>
                <CalendarOutlined />
                修業期間
              </Space>
            </Title>
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  label={<Text strong>修業起日</Text>}
                  name="periodDateStart"
                >
                  <DatePicker
                    style={{ width: '100%', borderRadius: '6px' }}
                    placeholder="請選擇起日"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  label={<Text strong>修業迄日</Text>}
                  name="periodDateEnd"
                >
                  <DatePicker
                    style={{ width: '100%', borderRadius: '6px' }}
                    placeholder="請選擇迄日"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  label={<Text strong>發證日期</Text>}
                  name="certificateDate"
                >
                  <DatePicker
                    style={{ width: '100%', borderRadius: '6px' }}
                    placeholder="請選擇發證日期"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12}>
                <Form.Item
                  label={<Text strong>證件字號</Text>}
                  name="certificateNumber"
                >
                  <Input
                    placeholder="請輸入證件字號"
                    style={{ borderRadius: '6px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* 其他資訊 */}
          <Divider style={{ margin: '24px 0' }} />
          <div className="form-section">
            <Title level={5}>
              <Space>
                <FileTextOutlined />
                其他資訊
              </Space>
            </Title>
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Form.Item
                  label={<Text strong>備註</Text>}
                  name="remark"
                >
                  <Input.TextArea
                    rows={3}
                    placeholder="請輸入備註"
                    style={{ resize: 'none', borderRadius: '6px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* 附件上傳 */}
          <Divider style={{ margin: '24px 0' }} />
          <div className="form-section">
            <Title level={5}>
              <Space>
                <UploadOutlined />
                證書附件
              </Space>
            </Title>
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Form.Item label={<Text strong>證書附件上傳</Text>}>
                  <Upload
                    multiple
                    beforeUpload={(file) => {
                      const isLt10M = file.size / 1024 / 1024 < 10;
                      if (!isLt10M) {
                        message.error('檔案大小不可超過 10MB');
                      }
                      return isLt10M || Upload.LIST_IGNORE;
                    }}
                    fileList={fileList}
                    onChange={({ fileList: newFileList }) => setFileList(newFileList)}
                    onRemove={(file) => {
                      setFileList((prev) => prev.filter((f) => f.uid !== file.uid));
                    }}
                    listType="picture"
                  >
                    <Button
                      icon={<UploadOutlined />}
                      style={{ borderRadius: '6px' }}
                    >
                      選擇檔案
                    </Button>
                  </Upload>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </Modal>

      {deleteUid && (
        <DeleteWithCountdown
          onDelete={async () => {
            try {
              await handleDelete(deleteUid);
              setDeleteUid(null);
            } catch (error) {
              message.error('刪除失敗，請稍後再試');
            }
          }}
          onCancel={() => setDeleteUid(null)}
        />
      )}
    </>
  );
};

export default EducationInfo;
