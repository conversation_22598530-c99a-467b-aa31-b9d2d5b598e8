﻿using System.Text.Json.Nodes;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FAST_ERP_Backend.Services.Common
{
    public class SystemGroupsService : ISystemGroupsService
    {
        private readonly ERPDbContext _context;
        private readonly EncryptionHelper _encryptionHelper;
        private readonly TokenHandler _tokenHandler;

        public SystemGroupsService(ERPDbContext context, EncryptionHelper encryptionHelper, TokenHandler tokenHandler)
        {
            _context = context;
            _encryptionHelper = encryptionHelper;
            _tokenHandler = tokenHandler;
        }
        public async Task<(bool, string)> AddSystemGroupsAsync(SystemGroupsDTO systemGroup, String tokenUid = "")
        {
            try
            {
                var newGuid = Guid.NewGuid().ToString();
                var OptionTime = systemGroup.OptionTime;

                var newGroup = new SystemGroups
                {
                    SystemGroupId = newGuid,
                    SystemCode = systemGroup.SystemCode,
                    Name = systemGroup.Name,
                    Option = getEncryptOption(newGuid, OptionTime),     //將設定時間與Guid轉成JSON後加密
                    Remark = systemGroup.Remark,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid,
                };
                await _context.Common_SystemGroups.AddAsync(newGroup);
                await _context.SaveChangesAsync();

                return (true, "新增系統群組成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增系統群組失敗: {ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteSystemmGroupsAsync(SystemGroupsDTO systemGroup, String tokenUid = "")
        {
            var existingGroup = await _context.Common_SystemGroups
                .FirstOrDefaultAsync(e => e.SystemGroupId == systemGroup.SystemGroupId);

            if (existingGroup != null)
            {
                try
                {
                    existingGroup.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingGroup.DeleteUserId = systemGroup.DeleteUserId;
                    existingGroup.IsDeleted = true;

                    await _context.SaveChangesAsync();
                    return (true, "刪除系統群組成功");
                }
                catch (Exception ex)
                {
                    return (false, $"刪除系統群組失敗: {ex.Message}");
                }
            }
            return (false, "系統群組不存在");
        }

        public async Task<(bool, string)> EditSystemGroupsAsync(SystemGroupsDTO systemGroup, String tokenUid = "")
        {
            var existingGroup = await _context.Common_SystemGroups
                .FirstOrDefaultAsync(e => e.SystemGroupId == systemGroup.SystemGroupId);

            if (existingGroup != null)
            {
                try
                {
                    existingGroup.SystemCode = systemGroup.SystemCode;
                    existingGroup.Name = systemGroup.Name;
                    existingGroup.Option = getEncryptOption(systemGroup.SystemGroupId, systemGroup.OptionTime);
                    existingGroup.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingGroup.UpdateUserId = tokenUid;

                    await _context.SaveChangesAsync();
                    return (true, "更新系統群組成功");
                }
                catch (Exception ex)
                {
                    return (false, $"更新系統群組失敗: {ex.Message}");
                }
            }
            return (false, "系統群組不存在");
        }

        public async Task<List<SystemGroupsDTO>> GetSystemGroupsAsync(string systemGroupId = "")
        {

            var query = _context.Common_SystemGroups.AsQueryable();

            if (!string.IsNullOrEmpty(systemGroupId))
            {
                query = query.Where(e => e.SystemGroupId == systemGroupId);
            }

            var systemGroups = await query.ToListAsync();

            return systemGroups.Select(systemGroup => new SystemGroupsDTO
            {
                SystemGroupId = systemGroup.SystemGroupId,
                SystemCode = systemGroup.SystemCode,
                Name = systemGroup.Name,
                OptionTime = getOptionTime(systemGroup.Option),
                Remark = systemGroup.Remark
            }).ToList();

        }

        public async Task<bool> VerifyGroupAsync(string verifyGroupName)
        {
            bool result = false;
            var nowTime = DateTimeOffset.Now.ToUnixTimeSeconds();

            var group = await _context.Common_SystemGroups
                    .FirstOrDefaultAsync(e => e.SystemCode == verifyGroupName);
            if (group != null)
            {
                string decryptedOption = _encryptionHelper.DecryptString(group.Option);
                var jsonObject = JsonConvert.DeserializeObject<OptionJson>(decryptedOption);
                //判斷是否有該群組權限且時間未過期*
                result = jsonObject != null &&
                         jsonObject.Guid == group.SystemGroupId &&
                         jsonObject.OptionTime > nowTime;

            }
            return result;
        }

        //加密設定,ID用以對照用,時間為可用時間
        private String getEncryptOption(String getGuid, long getTime)
        {
            var jsonObject = new OptionJson
            {
                Guid = getGuid,
                OptionTime = getTime
            };
            string jsonString = JsonConvert.SerializeObject(jsonObject, Formatting.Indented);

            return _encryptionHelper.EncryptString(jsonString);
        }

        public long getOptionTime(String getOption)
        {
            string jsonString = _encryptionHelper.DecryptString(getOption);
            long optionTime = 0;
            try
            {
                var jsonObj = JsonConvert.DeserializeObject<OptionJson>(jsonString);

                optionTime = jsonObj.OptionTime;
            }
            catch (JsonReaderException ex)
            {
                Console.WriteLine($"JSON 解析錯誤: {ex.Message}");
            }
            return optionTime;
        }
    }
}
