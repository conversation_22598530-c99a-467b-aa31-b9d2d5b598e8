using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;
using FAST_ERP_Backend.Interfaces.Pms;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("財產系統使用者身分管理")]
    public class PmsUserRoleController : ControllerBase
    {
        private readonly IPmsUserRoleService _pmsUserRoleService;

        public PmsUserRoleController(IPmsUserRoleService pmsUserRoleService)
        {
            _pmsUserRoleService = pmsUserRoleService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得所有財產系統使用者身分", Description = "取得所有財產系統使用者身分資料")]
        public async Task<ActionResult<List<PmsUserRoleDTO>>> GetAllPmsUserRoles()
        {
            var result = await _pmsUserRoleService.GetAllPmsUserRolesAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得指定財產系統使用者身分", Description = "取得指定財產系統使用者身分資料")]
        public async Task<ActionResult<PmsUserRoleDTO>> GetPmsUserRoleById(Guid id)
        {
            var result = await _pmsUserRoleService.GetPmsUserRoleByIdAsync(id);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增財產系統使用者身分", Description = "新增財產系統使用者身分資料")]
        public async Task<ActionResult<PmsUserRoleDTO>> CreatePmsUserRole(PmsUserRoleDTO pmsUserRoleDTO)
        {
            var result = await _pmsUserRoleService.CreatePmsUserRoleAsync(pmsUserRoleDTO);
            return CreatedAtAction(nameof(GetPmsUserRoleById), new { id = result.PmsUserRoleId }, result);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯財產系統使用者身分", Description = "編輯財產系統使用者身分資料")]
        public async Task<ActionResult<PmsUserRoleDTO>> UpdatePmsUserRole(Guid id, PmsUserRoleDTO pmsUserRoleDTO)
        {
            if (id != pmsUserRoleDTO.PmsUserRoleId)
                return BadRequest();

            var result = await _pmsUserRoleService.UpdatePmsUserRoleAsync(pmsUserRoleDTO);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除財產系統使用者身分", Description = "刪除財產系統使用者身分資料")]
        public async Task<ActionResult> DeletePmsUserRole(Guid id)
        {
            var success = await _pmsUserRoleService.DeletePmsUserRoleAsync(id);
            if (!success)
                return NotFound();
            return NoContent();
        }

        [HttpGet]
        [Route("GetUserRoles")]
        [SwaggerOperation(Summary = "取得使用者身分", Description = "取得使用者身分資料")]
        public async Task<ActionResult<List<PmsUserRoleDTO>>> GetUserRoles(string userId)
        {
            var result = await _pmsUserRoleService.GetUserRolesAsync(userId);
            return Ok(result);
        }

        [HttpPost]
        [Route("AssignRoleToUser")]
        [SwaggerOperation(Summary = "指派使用者身分", Description = "指派使用者身分資料")]
        public async Task<ActionResult<PmsUserRoleMappingDTO>> AssignRoleToUser(PmsUserRoleMappingDTO mappingDTO)
        {
            var result = await _pmsUserRoleService.AssignRoleToUserAsync(mappingDTO);
            return Ok(result);
        }

        [HttpPost]
        [Route("RemoveRoleFromUser")]
        [SwaggerOperation(Summary = "移除使用者身分", Description = "移除使用者身分資料")]
        public async Task<ActionResult<bool>> RemoveRoleFromUser([FromBody] PmsUserRoleMappingDTO mappingDTO)
        {
            var success = await _pmsUserRoleService.RemoveRoleFromUserAsync(mappingDTO.UserId, mappingDTO.PmsUserRoleId);
            return Ok(success);
        }
    }
}