.position-table-container {
    padding   : 24px;
    background: #f5f5f5;
    min-height: 100vh;
}

.header-section {
    margin-bottom: 40px;
}

.header-content {
    display        : flex;
    justify-content: space-between;
    align-items    : center;
    flex-wrap      : wrap;
    gap            : 16px;
}

.header-text {
    text-align: left;
}

.header-text h2 {
    color        : #1890ff;
    margin-bottom: 8px;
    margin       : 0 0 8px 0;
}

.header-text p {
    color    : #666;
    font-size: 14px;
    margin   : 0;
}

.header-actions {
    flex-shrink   : 0;
    display       : flex;
    flex-direction: column;
    align-items   : flex-end;
    gap           : 8px;
}

.zoom-controls {
    display       : flex;
    flex-direction: column;
    align-items   : flex-end;
    gap           : 4px;
}

.zoom-buttons {
    display      : flex;
    align-items  : center;
    gap          : 4px;
    background   : #f5f5f5;
    padding      : 4px;
    border-radius: 6px;
    border       : 1px solid #d9d9d9;
}

.zoom-btn {
    width          : 24px;
    height         : 24px;
    border         : none;
    background     : #fff;
    border-radius  : 4px;
    cursor         : pointer;
    font-size      : 14px;
    font-weight    : bold;
    display        : flex;
    align-items    : center;
    justify-content: center;
    transition     : all 0.2s ease;
    box-shadow     : 0 1px 2px rgba(0, 0, 0, 0.1);
}

.zoom-btn:hover:not(:disabled) {
    background: #1890ff;
    color     : white;
    transform : translateY(-1px);
}

.zoom-btn:disabled {
    background: #f5f5f5;
    color     : #ccc;
    cursor    : not-allowed;
}

.zoom-info {
    font-size  : 12px;
    color      : #333;
    font-weight: 500;
    min-width  : 36px;
    text-align : center;
}

.zoom-hint {
    font-size : 10px;
    color     : #999;
    text-align: center;
}

.zoom-reset-hint {
    position  : absolute;
    top       : 10px;
    right     : 10px;
    z-index   : 1000;
    opacity   : 0.8;
    transition: opacity 0.3s ease;
}

.zoom-reset-hint:hover {
    opacity: 1;
}

.zoom-reset-btn {
    background   : #1890ff;
    color        : white;
    border       : none;
    padding      : 6px 12px;
    border-radius: 4px;
    font-size    : 12px;
    cursor       : pointer;
    box-shadow   : 0 2px 4px rgba(0, 0, 0, 0.1);
    transition   : all 0.3s ease;
}

.zoom-reset-btn:hover {
    background: #40a9ff;
    transform : translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items   : flex-start;
    }

    .header-text {
        text-align: center;
        width     : 100%;
    }

    .header-actions {
        width          : 100%;
        flex-direction : row;
        justify-content: space-between;
        align-items    : center;
    }

    .header-actions .ant-space {
        flex: 1;
    }

    .zoom-controls {
        margin-left: 10px;
    }

    .zoom-hint {
        font-size: 9px;
    }
}

.org-chart {
    display        : flex;
    justify-content: center;
    width          : 100%;
    overflow       : hidden;
}

.chart-zoom-area {
    width   : 100%;
    height  : 100%;
    overflow: visible;
    position: relative;
}

.chart-container {
    position : relative;
    padding  : 30px;
    max-width: 1200px;
    width    : 100%;
    min-width: 900px;
    height   : 600px;
}

.org-chart-flow {
    position: relative;
    width   : 100%;
}

.org-level {
    display        : flex;
    justify-content: space-around;
    align-items    : center;
    margin         : 40px 0;
    position       : relative;
    z-index        : 10;
    padding        : 0 10%;
    flex-wrap      : wrap;
}

.org-level.level-1 {
    margin: 20px 0;
}

.org-level.level-2 {
    gap: 120px;
}

.org-level.level-3 {
    gap: 150px;
}

.org-level.level-4 {
    gap      : 50px;
    flex-wrap: wrap;
}

.position-table-employee-card {
    position: relative;
    z-index : 15;
}

.position-card {
    width        : 140px;
    min-height   : 100px;
    border-radius: 8px;
    box-shadow   : 0 2px 8px rgba(0, 0, 0, 0.1);
    border       : 2px solid #e6f7ff;
    background   : #fff;
    transition   : all 0.3s ease;
}

.position-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform : translateY(-2px);
}

.position-card.level-1 {
    border-color: #ff4d4f;
    background  : linear-gradient(135deg, #fff2f0 0%, #fff 100%);
}

.position-card.level-2 {
    border-color: #faad14;
    background  : linear-gradient(135deg, #fffbe6 0%, #fff 100%);
}

.position-card.level-3 {
    border-color: #52c41a;
    background  : linear-gradient(135deg, #f6ffed 0%, #fff 100%);
}

.position-card.level-4 {
    border-color: #1890ff;
    background  : linear-gradient(135deg, #e6f7ff 0%, #fff 100%);
}

.card-content {
    text-align: center;
    padding   : 8px 4px;
}

.department-tag {
    background   : #1890ff;
    color        : white;
    font-size    : 10px;
    padding      : 2px 6px;
    border-radius: 10px;
    margin-bottom: 6px;
    display      : inline-block;
}

.position-title {
    font-weight  : bold;
    font-size    : 12px;
    color        : #333;
    margin-bottom: 4px;
}

.position-table-employee-name {
    font-size      : 11px;
    color          : #666;
    display        : flex;
    align-items    : center;
    justify-content: center;
}

/* 新的組織架構圖樣式 - 優化滾動 */
.org-chart-container {
    width              : 100%;
    height             : 700px;
    overflow           : auto;
    border             : 1px solid #e8e8e8;
    border-radius      : 8px;
    /* 將背景設定到容器上，確保始終鋪滿整個區域 */
    background         : linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position           : relative;
    scrollbar-width    : thin;
    scrollbar-color    : #bfbfbf #f0f0f0;
    user-select        : none;
    /* 確保滾動功能正常 */
    overflow-x         : auto;
    overflow-y         : auto;
    /* 在拖曳時禁用smooth滾動以提高響應速度 */
    scroll-behavior    : auto;
    /* 重要：在拖曳時禁用瀏覽器原生滾動 */
    overscroll-behavior: none;
}

.org-chart-container.dragging {
    cursor         : grabbing !important;
    overflow       : auto;
    overflow-x     : auto !important;
    overflow-y     : auto !important;
    user-select    : none !important;
    pointer-events : auto;
    /* 在拖曳時強制關閉所有動畫和過渡效果 */
    scroll-behavior: auto !important;
    transition     : none !important;
}

/* 拖曳時禁用所有子元素的指針事件，除了背景 */
.org-chart-container.dragging .position-table-employee-node {
    pointer-events: none !important;
}

.org-chart-container.dragging .position-table-employee-node rect,
.org-chart-container.dragging .position-table-employee-node circle,
.org-chart-container.dragging .position-table-employee-node text {
    pointer-events: none !important;
}

/* 自定義滾動條樣式 */
.org-chart-container::-webkit-scrollbar {
    width : 8px;
    height: 8px;
}

.org-chart-container::-webkit-scrollbar-track {
    background   : #f0f0f0;
    border-radius: 4px;
}

.org-chart-container::-webkit-scrollbar-thumb {
    background   : #bfbfbf;
    border-radius: 4px;
    transition   : background 0.3s ease;
}

.org-chart-container::-webkit-scrollbar-thumb:hover {
    background: #8c8c8c;
}

.org-chart-svg {
    display         : block;
    /* 移除背景，讓容器背景透過來，這樣背景就不會受縮放影響 */
    background      : transparent;
    transition      : transform 0.1s ease-out;
    will-change     : transform;
    transform-origin: top left;
    /* 移除min-width和min-height限制，允許SVG超出容器產生滾動 */
    /* 確保 SVG 支援拖曳 */
    pointer-events  : all;
    cursor          : inherit;
    user-select     : none;
}

.org-chart-container.dragging .org-chart-svg {
    cursor: grabbing !important;
}

.loading-container {
    display        : flex;
    justify-content: center;
    align-items    : center;
    height         : 400px;
    font-size      : 16px;
    color          : #666;
}

/* 員工節點基礎樣式 - 移除跳動效果 */
.position-table-employee-node {
    transition: opacity 0.3s ease;
    cursor    : pointer;
}

.position-table-employee-node:hover {
    opacity: 1;
}

/* 員工卡片背景懸停效果 */
.position-table-employee-card-background {
    transition: all 0.3s ease;
}

.position-table-employee-node:hover .position-table-employee-card-background {
    filter      : brightness(1.05);
    stroke-width: 3;
    stroke      : #1890ff;
}

.position-table-employee-node rect {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.position-table-employee-node text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    user-select: none;
}

/* 連接線樣式優化 */
.links-layer path {
    transition: all 0.3s ease;
}

.links-layer path:hover {
    stroke-width: 2.5 !important;
    stroke      : #1890ff !important;
    filter      : drop-shadow(0 1px 3px rgba(24, 144, 255, 0.3));
}

/* 響應式設計 */
@media (max-width: 1200px) {
    .chart-container {
        min-width: 800px;
    }

    .org-level.level-2 {
        gap: 80px;
    }

    .org-level.level-3 {
        gap: 100px;
    }

    .horizontal-branch {
        width      : 400px;
        margin-left: -200px;
    }
}

@media (max-width: 1000px) {
    .chart-container {
        min-width: 700px;
    }

    .position-card {
        width     : 100px;
        min-height: 75px;
    }

    .org-level.level-2 {
        gap: 60px;
    }

    .org-level.level-3 {
        gap: 80px;
    }

    .org-level.level-4 {
        gap: 40px;
    }

    .horizontal-branch {
        width      : 300px;
        margin-left: -150px;
    }
}

@media (max-width: 768px) {
    .position-card {
        width     : 90px;
        min-height: 70px;
    }

    .department-tag {
        font-size: 8px;
        padding  : 1px 4px;
    }

    .position-title {
        font-size: 10px;
    }

    .position-table-employee-name {
        font-size: 9px;
    }

    .org-level.level-2 {
        gap: 40px;
    }

    .org-level.level-3 {
        gap: 60px;
    }

    .org-level.level-4 {
        gap: 30px;
    }

    .horizontal-branch {
        width      : 250px;
        margin-left: -125px;
    }

    .org-chart-container {
        height: 500px;
    }
}

/* 額外的視覺優化 */
/* 改善照片區域樣式 */
.position-table-employee-node image {
    transition: all 0.3s ease;
}

.position-table-employee-node:hover image {
    filter: brightness(1.1) contrast(1.05);
}

/* Avatar 專門樣式 */
.position-table-employee-avatar {
    border-radius: 4px;
    transition   : all 0.3s ease;
}

.position-table-employee-node:hover .position-table-employee-avatar {
    filter         : brightness(1.1) contrast(1.1) saturate(1.1);
    /* 移除 transform: scale 避免位置偏移 */
}

/* Avatar 背景樣式 */
.position-table-avatar-background {
    transition: all 0.3s ease;
}

.position-table-employee-node:hover .position-table-avatar-background {
    filter      : brightness(1.1);
    stroke-width: 2;
    /* 移除填色變化，保持原始顏色 */
}

/* 文字頭像樣式 */
.position-table-avatar-text {
    transition    : all 0.3s ease;
    text-shadow   : 0 1px 2px rgba(0, 0, 0, 0.1);
    pointer-events: none;
}

.position-table-employee-node:hover .position-table-avatar-text {
    filter         : brightness(1.2);
    /* 移除 transform: scale 避免位置偏移 */
}

/* 文字懸停效果 */
.position-table-employee-node text {
    transition: fill 0.3s ease;
}

.position-table-employee-node:hover text {
    fill: #1890ff;
}

/* 展開/收合按鈕增強效果 */
.position-table-employee-node circle {
    transition: all 0.3s ease;
}

.position-table-employee-node:hover circle {
    fill  : #1890ff;
    filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.3));
}

/* 平滑滾動 */
.org-chart-container {
    scroll-behavior: smooth;
}

/* 載入狀態優化 */
.loading-container {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* 員工詳細資料樣式 */
.employee-details {
    padding: 0;
}

.detail-items {
    display       : flex;
    flex-direction: column;
    gap           : 16px;
}

.detail-item {
    display      : flex;
    align-items  : center;
    padding      : 12px 16px;
    background   : #fafafa;
    border-radius: 6px;
    border-left  : 4px solid #1890ff;
    transition   : all 0.3s ease;
}

.detail-item:hover {
    background       : #f0f7ff;
    border-left-color: #40a9ff;
}

.detail-label {
    font-weight : 600;
    color       : #333;
    min-width   : 100px;
    margin-right: 12px;
}

.detail-value {
    color      : #666;
    flex       : 1;
    font-weight: 500;
}