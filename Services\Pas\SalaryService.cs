﻿using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Extensions.Pas;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class SalaryService : ISalaryService
    {
        private readonly EncryptionHelper _encryptionHelper;
        private readonly ERPDbContext _context;

        private readonly EmployeeClass _employeeClass;

        private readonly Baseform _baseform;

        private readonly ICurrentUserService _currentUserService;

        public SalaryService(
            EncryptionHelper encryptionHelper,
            Baseform baseform,
            ERPDbContext context,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService)
        {
            _encryptionHelper = encryptionHelper;
            _baseform = baseform;
            _context = context;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
        }

        // 取得員工薪資明細
        public async Task<SalaryDTO> GetSalaryDetailAsync(string _userid)
        {
            try
            {
                var entity = await _context.Pas_Salary
                .Where(x => x.UserId.ToString() == _userid && x.IsDeleted != true)
                .FirstOrDefaultAsync();

                var salaryDto = new SalaryDTO
                {
                    UserId = entity?.UserId ?? "",
                    SalaryStatus = entity?.SalaryStatus ?? "",
                    SalaryStatusName = _employeeClass.GetlistCompareName(_employeeClass.list_payoffdata, entity?.SalaryStatus ?? ""),
                    EmployerContributionRate = entity?.EmployerContributionRate,
                    EmployeeContributionType = entity?.EmployeeContributionType ?? "",
                    EmployeeContributionTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_EmployeeContributionTypedata, entity?.EmployeeContributionType ?? ""),
                    EmployeeContributionRate = entity?.EmployeeContributionRate,
                    EmployeeContributionAmount = entity?.EmployeeContributionAmount,
                    TaxType = entity?.TaxType ?? "",
                    TaxTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_IncomeTaxdata, entity?.TaxType ?? ""),
                    FixedTaxRate = entity?.FixedTaxRate,
                    FixedTaxAmount = entity?.FixedTaxAmount,
                    TransferAccount = entity?.TransferAccount ?? "",
                    Remark = entity?.Remark ?? "",
                    CreateUserId = entity?.CreateUserId ?? "",
                    UpdateTime = entity?.UpdateTime
                };

                return salaryDto;
            }
            catch (Exception ex)
            {
                throw new Exception("取得員工薪資資料錯誤", ex);
            }
        }

        // 編輯薪資資料
        public async Task<(bool, string)> EditSalaryAsync(SalaryDTO _data)
        {
            List<string> list_msg_check = CheckSalaryInput(_data);

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            // 資料處理.
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                var existsalary = await _context.Pas_Salary
            .FirstOrDefaultAsync(x => x.UserId == _data.UserId);

                if (existsalary != null)
                {
                    existsalary.SalaryStatus = _data.SalaryStatus;
                    existsalary.EmployerContributionRate = _data.EmployerContributionRate;
                    existsalary.EmployeeContributionType = _data.EmployeeContributionType;
                    existsalary.EmployeeContributionRate = _data.EmployeeContributionRate;
                    existsalary.EmployeeContributionAmount = _data.EmployeeContributionAmount;
                    existsalary.TaxType = _data.TaxType;
                    existsalary.FixedTaxRate = _data.FixedTaxRate;
                    existsalary.FixedTaxAmount = _data.FixedTaxAmount;
                    existsalary.TransferAccount = _data.TransferAccount;
                    existsalary.Remark = _data.Remark;
                    existsalary.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                    existsalary.UpdateUserId = _currentUserService.UserId;
                }
                else
                {
                    return (false, "無對應salary資料");
                }

                // 兩張表一起存.
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "編輯員工薪資資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯員工薪資資料失敗: {ex.InnerException.Message}");
            }

        }


        public List<string> CheckSalaryInput(SalaryDTO _data)
        {
            List<string> list_errorMsg = new List<string>();

            // 共同輸入檢核.
            if (_data.SalaryStatus.Trim() == "")
            {
                list_errorMsg.Add("請選擇發薪狀態");
            }

            if (_data.EmployeeContributionType.Trim() == "")
            {
                list_errorMsg.Add("請選擇員工自提類型");
            }

            if (_data.TaxType.Trim() == "")
            {
                list_errorMsg.Add("請選擇計稅型式");
            }


            return list_errorMsg;
        }

    }
}
