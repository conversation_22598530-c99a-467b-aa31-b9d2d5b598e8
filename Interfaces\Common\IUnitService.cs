using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Interfaces.Common
{
    public interface IUnitService
    {
        /// <summary>
        /// 取得單位資料
        /// </summary>
        /// <param name="_unitId">單位編號</param>
        /// <returns>單位資料列表</returns>
        Task<List<UnitDTO>> GetUnitAsync(string _unitId = "");

        /// <summary>
        /// 新增單位
        /// </summary>
        /// <param name="_data">單位資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddUnitAsync(UnitDTO _data);

        /// <summary>
        /// 編輯單位
        /// </summary>
        /// <param name="_data">單位資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditUnitAsync(UnitDTO _data);

        /// <summary>
        /// 刪除單位
        /// </summary>
        /// <param name="_data">單位資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteUnitAsync(UnitDTO _data);

        /// <summary>
        /// 取得單位詳細資料
        /// </summary>
        /// <param name="_unitId">單位編號</param>
        /// <returns>單位詳細資料</returns>
        Task<UnitDTO> GetUnitDetailAsync(string _unitId);
    }
}