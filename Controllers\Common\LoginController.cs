using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("登入管理")]
    public class LoginController : ControllerBase
    {
        private readonly IUsersService _Interface;

        // 在建構子中注入多個依賴
        public LoginController(IUsersService common_Users_Interface)
        {
            _Interface = common_Users_Interface;
        }
        
        [HttpPost]
        [Route("VerifyLogin")]
        [SwaggerOperation(Summary = "登入檢核", Description = "驗證登入資訊")]
        public async Task<IActionResult> VerifyLogin([FromBody] UsersDTO _data)
        {
            var (result, msg,token) = await _Interface.VerifyLoginAsync(_data);
            return Ok(new { result, msg, token });
        }

    }

}
