import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 組別資訊
export interface Division {
    divisionId: string;      // 組別編號
    name: string;            // 組別名稱
    departmentId: string;    // 部門編號
    sortCode: number;        // 排序號碼
    createTime: number | null;    // 新增時間
    createUserId: string | null;  // 新增者編號
    updateTime: number | null;    // 更新時間
    updateUserId: string | null;  // 更新者編號
    deleteTime: number | null;    // 刪除時間
    deleteUserId: string | null;  // 刪除者編號
    enterpriseGroupId: string;    // 公司群組編號
}

// 獲取組別列表
export async function getDivisions(): Promise<ApiResponse<Division[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDivisions, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取組別列表失敗",
            data: []
        };
    }
}

// 獲取單一組別資訊
export async function getDivisionDetail(id: string): Promise<ApiResponse<Division>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDivisionDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        console.log(response);
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取組別資訊失敗",
            data: undefined
        };
    }
}

// 新增組別資訊
export async function createDivision(data: Partial<Division>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addDivision, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增組別資訊失敗",
        };
    }
}

// 更新組別資訊
export async function updateDivision(data: Partial<Division>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editDivision, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新組別資訊失敗",
        };
    }
}

// 刪除組別資訊
export async function deleteDivision(data: Partial<Division>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteDivision, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除組別資訊失敗",
        };
    }
} 