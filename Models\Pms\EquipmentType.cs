using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 設備類型
    /// </summary>
    public class EquipmentType : ModelBaseEntity
    {
        [Key]
        [Comment("設備類型流水號")]
        public Guid EquipmentTypeId { get; set; }

        [Comment("設備類型編號")]
        [RegularExpression(@"^[0-9a-zA-Z]{1,10}$", ErrorMessage = "設備類型編號必須為1至10位英數字")]
        [StringLength(10, MinimumLength = 1, ErrorMessage = "設備類型編號必須為1至10位英數字")]
        [Column(TypeName = "nvarchar(10)")]
        [Required(ErrorMessage = "設備類型編號為必填欄位")]
        public string EquipmentTypeNo { get; set; }

        [Comment("設備類型名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; }

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; }

        public EquipmentType()
        {
            EquipmentTypeId = Guid.NewGuid();
            EquipmentTypeNo = "";
            Name = "";
            SortCode = 0;
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class EquipmentTypeDTO : ModelBaseEntityDTO
    {
        public Guid EquipmentTypeId { get; set; }
        public string EquipmentTypeNo { get; set; }
        public string Name { get; set; }
        public int SortCode { get; set; }

        public EquipmentTypeDTO()
        {
            EquipmentTypeId = Guid.Empty;
            EquipmentTypeNo = "";
            Name = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}