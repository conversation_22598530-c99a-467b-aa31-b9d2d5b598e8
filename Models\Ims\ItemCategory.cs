using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Attributes;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 庫存品分類 </summary>
public class ItemCategory : ModelBaseEntity
{
    /// <summary> 庫存品分類編號 </summary>
    [Key]
    [Comment("庫存品分類編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ItemCategoryID { get; set; }

    /// <summary> 名稱 </summary>
    [Comment("名稱")]
    [Column(TypeName = "nvarchar(100)")]
    public string Name { get; set; }

    /// <summary> 描述 </summary>
    [Comment("描述")]
    [Column(TypeName = "nvarchar(500)")]
    public string Description { get; set; }

    /// <summary> 父分類ID </summary>
    [Comment("父分類ID")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid? ParentID { get; set; }

    /// <summary> 排序 </summary>
    [Comment("排序")]
    [Column(TypeName = "int")]
    public int SortCode { get; set; }

    /// <summary> 父分類 </summary>
    public ItemCategory? Parent { get; set; }
    
    /// <summary> 子分類 </summary>
    public ICollection<ItemCategory> Children { get; set; }

    /// <summary> 庫存品 </summary>
    public ICollection<Item> Items { get; set; }

    /// <summary> 建構式 </summary>
    public ItemCategory()
    {
        ItemCategoryID = Guid.NewGuid();
        Name = string.Empty;
        Description = string.Empty;
        ParentID = null;
        SortCode = 0;
        Children = new List<ItemCategory>();
        Items = new List<Item>();
    }
}

/// <summary> 庫存品分類DTO </summary>
public class ItemCategoryDTO : ModelBaseEntityDTO
{
    /// <summary> 庫存品分類編號 </summary>
    public Guid ItemCategoryID { get; set; }

    /// <summary> 名稱 </summary>
    public string Name { get; set; }

    /// <summary> 描述 </summary>
    public string Description { get; set; }

    /// <summary> 父分類ID </summary>
    public Guid? ParentID { get; set; }

    /// <summary> 排序 </summary>
    public int SortCode { get; set; }

    /// <summary> 父分類 </summary>
    public ItemCategoryDTO? Parent { get; set; }

    /// <summary> 子分類 </summary>
    public ICollection<ItemCategoryDTO> Children { get; set; }

    /// <summary> 庫存品 </summary>
    public ICollection<ItemDTO> Items { get; set; }

    /// <summary> 建構式 </summary>
    public ItemCategoryDTO()
    {
        ItemCategoryID = Guid.NewGuid();
        Name = string.Empty;
        Description = string.Empty;
        ParentID = null;
        SortCode = 0;
        Children = new List<ItemCategoryDTO>();
        Items = new List<ItemDTO>();
    }
}
