﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// Select選項
    /// </summary>
    public class SelectOption
    {
        public string OptionValue { get; set; } // 選項uid
        public string OptionText { get; set; } // 選項文字

        public SelectOption()
        {
            OptionValue = "";
            OptionText = "";
        }
    }

    /// <summary>
    /// 集聯選項
    /// </summary>
    public class CascaderOptionDTO
    {
        public string value { get; set; } // 對應 uid
        public string label { get; set; } // 顯示名稱
        public List<CascaderOptionDTO> children { get; set; } // 子節點

        public CascaderOptionDTO()
        {
            value = "";
            label = "";
            children = new List<CascaderOptionDTO>();
        }
    }

}

