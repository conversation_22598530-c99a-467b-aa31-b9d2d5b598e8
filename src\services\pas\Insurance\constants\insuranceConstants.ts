// 保險類型常數
export const INSURANCE_TYPES = {
    LABOR: 1,        // 勞保
    HEALTH: 2,       // 健保
    ACCIDENT: 3      // 職災
} as const;

export type InsuranceType = typeof INSURANCE_TYPES[keyof typeof INSURANCE_TYPES];

// 保險類型選項
export const INSURANCE_TYPE_OPTIONS = [
    { value: INSURANCE_TYPES.LABOR, label: '勞保' },
    { value: INSURANCE_TYPES.HEALTH, label: '健保' },
    { value: INSURANCE_TYPES.ACCIDENT, label: '職災' },
];

// 取得保險類型名稱
export function getInsuranceTypeName(insuranceType: number): string {
    const option = INSURANCE_TYPE_OPTIONS.find(opt => opt.value === insuranceType);
    return option?.label || '未知';
}

// 日期格式
export const DATE_FORMAT = 'YYYY-MM-DD'; 