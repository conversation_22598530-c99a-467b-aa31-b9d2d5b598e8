using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Attributes;
using Microsoft.EntityFrameworkCore;


namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 庫存品售價 </summary>
public class ItemPrice : ModelBaseEntity
{
    /// <summary> 售價編號 </summary>
    [Key]
    [Comment("售價編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ItemPriceID { get; set; }

    /// <summary> 庫存品編號 </summary>
    [Comment("庫存品編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid ItemID { get; set; }

    /// <summary> 庫存品 </summary>
    public Item? Item { get; set; }

    /// <summary> 價格類別 </summary>
    [Comment("價格類別")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PriceTypeID { get; set; }

    /// <summary> 價格類別 </summary>
    public PriceType? PriceType { get; set; }

    /// <summary> 售價 </summary>
    [Comment("售價")]
    [Column(TypeName = "decimal(38, 8)")]
    public decimal Price { get; set; }

    /// <summary> 建構式 </summary>
    public ItemPrice()
    {
        ItemPriceID = Guid.NewGuid();
        ItemID = Guid.NewGuid();
        PriceTypeID = Guid.NewGuid();
        Price = 0;
    }
}

/// <summary> 庫存品售價 DTO </summary>
public class ItemPriceDTO : ModelBaseEntityDTO
{
    /// <summary> 售價編號 </summary>
    public Guid ItemPriceID { get; set; }

    /// <summary> 庫存品編號 </summary>
    public Guid ItemID { get; set; }

    /// <summary> 價格類別 </summary>
    public Guid PriceTypeID { get; set; }

    /// <summary> 售價 </summary>
    public decimal Price { get; set; }

    /// <summary> 建構式 </summary>
    public ItemPriceDTO()
    {
        ItemPriceID = Guid.NewGuid();
        ItemID = Guid.NewGuid();
        PriceTypeID = Guid.NewGuid();
        Price = 0;
    }
}