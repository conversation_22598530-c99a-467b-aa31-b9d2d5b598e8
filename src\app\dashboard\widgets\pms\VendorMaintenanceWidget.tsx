import React, { useState, useEffect } from "react";
import {
  Card,
  Tag,
  Button,
  Statistic,
  Row,
  Col,
  Spin,
  Tooltip,
  Descriptions,
} from "antd";
import {
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { getVendorMaintenances } from "@/services/pms/vendorMaintenanceService";
import { getAssets } from "@/services/pms/assetService";
import { getUsers } from "@/services/common/userService";
import { getDepartments } from "@/services/common/departmentService";
import {
  VendorMaintenance,
  MaintenanceStatistics,
} from "@/app/pms/document_maintenance/vendor_maintenance_form/interface";
import {
  STATUS_COLORS,
  STATUS_MAPPING,
  URGENCY_COLORS,
  URGENCY_MAPPING,
  MAINTENANCE_TYPE_MAPPING,
} from "@/app/pms/document_maintenance/vendor_maintenance_form/interface";
import { sortMaintenanceList } from "@/app/pms/document_maintenance/vendor_maintenance_form/config";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { formatTWCurrency } from "@/utils/formatUtils";

export interface VendorMaintenanceWidgetProps {
  showViewAllButton?: boolean;
  maxItems?: number;
}

const VendorMaintenanceWidget: React.FC<VendorMaintenanceWidgetProps> = ({
  showViewAllButton = true,
  maxItems = 5,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [maintenances, setMaintenances] = useState<VendorMaintenance[]>([]);
  const [statistics, setStatistics] = useState<MaintenanceStatistics | null>(
    null
  );

  // 資料的顯示名稱
  const enrichMaintenanceData = async (dataArray: VendorMaintenance[]) => {
    try {
      // 並行獲取所有需要的資料
      const [assetsResponse, usersResponse, departmentsResponse] =
        await Promise.all([getAssets(), getUsers(), getDepartments()]);

      const assets = assetsResponse.success ? assetsResponse.data || [] : [];
      const users = usersResponse.success ? usersResponse.data || [] : [];
      const departments = departmentsResponse.success
        ? departmentsResponse.data || []
        : [];

      // 為每筆維修資料顯示名稱
      dataArray.forEach((maintenance) => {
        // 財產名稱
        if (maintenance.assetId && !maintenance.assetName) {
          const assetDetail = assets.find(
            (a) => a.asset.assetId === maintenance.assetId
          );
          if (assetDetail) {
            maintenance.assetName =
              assetDetail.asset.assetName || assetDetail.asset.assetNo || "";
          }
        }

        // 申請人姓名
        if (maintenance.applicantId && !maintenance.applicantName) {
          const applicant = users.find(
            (u) => u.userId === maintenance.applicantId
          );
          if (applicant) {
            maintenance.applicantName =
              applicant.name || applicant.account || "";
          }
        }

        // 申請部門名稱
        if (maintenance.departmentId && !maintenance.applicantDepartment) {
          const department = departments.find(
            (d) => d.departmentId === maintenance.departmentId
          );
          if (department) {
            maintenance.applicantDepartment = department.name || "";
          }
        }

        // 檢驗人員姓名
        if (maintenance.inspectorId && !maintenance.inspectorName) {
          const inspector = users.find(
            (u) => u.userId === maintenance.inspectorId
          );
          if (inspector) {
            maintenance.inspectorName =
              inspector.name || inspector.account || "";
          }
        }
      });
    } catch (error) {
      console.error("資料顯示名稱失敗:", error);
    }
  };

  // 從實際資料計算統計
  const calculateStatisticsFromData = (dataArray: VendorMaintenance[]) => {
    // 過濾掉已取消的案件
    const filteredData = dataArray.filter(
      (item) => item.status !== "已取消" && item.status !== "CANCELLED"
    );

    // 計算統計數據
    const totalCount = filteredData.length;
    const pendingCount = filteredData.filter(
      (item) => item.status === "待審核" || item.status === "PENDING"
    ).length;
    const inProgressCount = filteredData.filter(
      (item) =>
        item.status === "已審核" ||
        item.status === "APPROVED" ||
        item.status === "已指派" ||
        item.status === "ASSIGNED" ||
        item.status === "施工中" ||
        item.status === "IN_PROGRESS" ||
        item.status === "已完成" ||
        item.status === "COMPLETED" ||
        item.status === "已驗收" ||
        item.status === "INSPECTED"
    ).length;
    const closedCount = filteredData.filter(
      (item) => item.status === "已結案" || item.status === "CLOSED"
    ).length;

    const calculatedStatistics: MaintenanceStatistics = {
      totalCount,
      pendingCount,
      approvedCount: 0, // Widget 不需要細分這些狀態
      assignedCount: 0,
      inProgressCount,
      completedCount: 0,
      inspectedCount: 0,
      closedCount,
      totalEstimatedCost: 0,
      totalActualCost: 0,
      averageCompletionDays: 0,
      typeStatistics: [],
      urgencyStatistics: [],
    };

    setStatistics(calculatedStatistics);
  };

  // 載入資料
  const loadData = async () => {
    try {
      setLoading(true);

      // 載入修繕申請資料
      const maintenanceResponse = await getVendorMaintenances({});

      if (maintenanceResponse.success && maintenanceResponse.data) {
        let dataArray: VendorMaintenance[] = [];
        if (
          (maintenanceResponse.data as any).data &&
          Array.isArray((maintenanceResponse.data as any).data)
        ) {
          dataArray = (maintenanceResponse.data as any).data;
        } else if (Array.isArray(maintenanceResponse.data)) {
          dataArray = maintenanceResponse.data;
        }

        // 從實際資料計算統計
        calculateStatisticsFromData(dataArray);

        // 顯示名稱
        await enrichMaintenanceData(dataArray);

        // 按緊急程度(高->低)、申請日期排序
        dataArray.sort(sortMaintenanceList);

        // 只取前 maxItems 項
        const limitedData = dataArray.slice(0, maxItems);
        setMaintenances(limitedData);
      } else {
        // 如果載入失敗，設置默認統計
        setStatistics({
          totalCount: 0,
          pendingCount: 0,
          approvedCount: 0,
          assignedCount: 0,
          inProgressCount: 0,
          completedCount: 0,
          inspectedCount: 0,
          closedCount: 0,
          totalEstimatedCost: 0,
          totalActualCost: 0,
          averageCompletionDays: 0,
          typeStatistics: [],
          urgencyStatistics: [],
        });
      }
    } catch (error) {
      console.error("載入廠商修繕資料失敗:", error);
      // 設置默認統計
      setStatistics({
        totalCount: 0,
        pendingCount: 0,
        approvedCount: 0,
        assignedCount: 0,
        inProgressCount: 0,
        completedCount: 0,
        inspectedCount: 0,
        closedCount: 0,
        totalEstimatedCost: 0,
        totalActualCost: 0,
        averageCompletionDays: 0,
        typeStatistics: [],
        urgencyStatistics: [],
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [maxItems]);

  // 跳轉到完整頁面
  const handleViewAll = () => {
    router.push("/pms/document_maintenance/vendor_maintenance_form");
  };

  return (
    <Card
      extra={
        showViewAllButton && (
          <Button
            type="link"
            size="small"
            onClick={handleViewAll}
            icon={<FileTextOutlined />}
          >
            查看全部
          </Button>
        )
      }
      size="small"
      style={{ height: "100%" }}
      styles={{
        body: {
          padding: "8px",
          height: "100%",
          overflow: "hidden",
        },
      }}
    >
      <Spin spinning={loading}>
        <div
          style={{ height: "100%", display: "flex", flexDirection: "column" }}
        >
          {/* 統計資訊區域 */}
          {statistics && (
            <Row gutter={[4, 4]} style={{ marginBottom: "8px" }}>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#f0f9ff" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.totalCount || 0}
                    title="總計"
                    valueStyle={{ fontSize: "14px", color: "#1890ff" }}
                    prefix={<FileTextOutlined style={{ fontSize: "12px" }} />}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#fff7e6" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.pendingCount || 0}
                    title="待審核"
                    valueStyle={{ fontSize: "14px", color: "#fa8c16" }}
                    prefix={
                      <ClockCircleOutlined style={{ fontSize: "12px" }} />
                    }
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#f6ffed" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.inProgressCount || 0}
                    title="進行中"
                    valueStyle={{ fontSize: "14px", color: "#52c41a" }}
                    prefix={
                      <CheckCircleOutlined style={{ fontSize: "12px" }} />
                    }
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  size="small"
                  style={{ textAlign: "center", background: "#fff1f0" }}
                  styles={{ body: { padding: "8px" } }}
                >
                  <Statistic
                    value={statistics.closedCount || 0}
                    title="已結案"
                    valueStyle={{ fontSize: "14px", color: "#8c8c8c" }}
                    prefix={
                      <ExclamationCircleOutlined style={{ fontSize: "12px" }} />
                    }
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* 最新申請列表 */}
          <div
            style={{
              flex: 1,
              minHeight: 0,
              border: "1px solid #f0f0f0",
              borderRadius: "6px",
            }}
          >
            <div
              style={{
                padding: "6px 8px",
                background: "#fafafa",
                borderBottom: "1px solid #f0f0f0",
                fontSize: "12px",
                fontWeight: "bold",
              }}
            >
              最新申請
            </div>
            <div
              style={{
                height: "calc(100% - 33px)",
                overflow: "hidden",
                position: "relative",
              }}
            >
              {maintenances.length === 0 && !loading ? (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    color: "#8c8c8c",
                    fontSize: "12px",
                  }}
                >
                  暫無修繕申請資料
                </div>
              ) : (
                <div
                  style={{
                    height: "100%",
                    overflowY: maintenances.length > 3 ? "auto" : "hidden",
                    overflowX: "hidden",
                  }}
                >
                  {maintenances.map((item, index) => (
                    <div
                      key={item.maintenanceNumber}
                      style={{
                        padding: "6px 8px",
                        borderBottom:
                          index < maintenances.length - 1
                            ? "1px solid #f0f0f0"
                            : "none",
                        fontSize: "11px",
                        minHeight: "50px",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          marginBottom: "4px",
                        }}
                      >
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div
                            style={{
                              fontWeight: "bold",
                              color: "#1890ff",
                              fontSize: "12px",
                              marginBottom: "2px",
                            }}
                          >
                            <Tooltip
                              title={
                                <div style={{ padding: "8px" }}>
                                  <Descriptions
                                    size="small"
                                    column={1}
                                    bordered
                                  >
                                    <Descriptions.Item label="修繕單號">
                                      {item.maintenanceNumber}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="財產名稱">
                                      {item.assetName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申請人">
                                      {item.applicantName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申請部門">
                                      {item.applicantDepartment}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="故障描述">
                                      {item.faultDescription}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="修繕類型">
                                      {MAINTENANCE_TYPE_MAPPING[
                                        item.maintenanceType
                                      ] || item.maintenanceType}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="緊急程度">
                                      <Tag
                                        color={
                                          URGENCY_COLORS[item.urgencyLevel] ||
                                          "default"
                                        }
                                      >
                                        {URGENCY_MAPPING[item.urgencyLevel] ||
                                          item.urgencyLevel}
                                      </Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="狀態">
                                      <Tag
                                        color={
                                          STATUS_COLORS[item.status] ||
                                          "default"
                                        }
                                      >
                                        {STATUS_MAPPING[item.status] ||
                                          item.status}
                                      </Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="申請日期">
                                      {item.applicationDate
                                        ? DateTimeExtensions.formatDateFromTimestamp(
                                            item.applicationDate
                                          )
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="預估費用">
                                      {formatTWCurrency(item.estimatedCost)}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="實際費用">
                                      {item.actualCost
                                        ? formatTWCurrency(item.actualCost)
                                        : "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="廠商">
                                      {item.vendorName || "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="廠商聯絡人">
                                      {item.vendorContact || "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="廠商電話">
                                      {item.vendorPhone || "-"}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="備註">
                                      {item.notes || "-"}
                                    </Descriptions.Item>
                                  </Descriptions>
                                </div>
                              }
                              color="#fff"
                              placement="right"
                              styles={{
                                root: { maxWidth: "500px" },
                                body: { padding: "0" },
                              }}
                            >
                              {item.maintenanceNumber}
                            </Tooltip>
                          </div>
                          <div
                            style={{
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              color: item.assetName ? "#333" : "#999",
                              fontSize: "11px",
                              fontWeight: "500",
                            }}
                            title={
                              item.assetName ||
                              `資產ID: ${item.assetId || "未指定"}`
                            }
                          >
                            {item.assetName ||
                              `資產ID: ${item.assetId || "未指定"}`}
                          </div>
                        </div>
                        <div
                          style={{
                            display: "flex",
                            gap: "4px",
                            flexShrink: 0,
                            marginLeft: "8px",
                          }}
                        >
                          <Tag
                            color={
                              URGENCY_COLORS[item.urgencyLevel] || "default"
                            }
                            style={{
                              fontSize: "10px",
                              margin: 0,
                              padding: "0 4px",
                            }}
                          >
                            {URGENCY_MAPPING[item.urgencyLevel] ||
                              item.urgencyLevel}
                          </Tag>
                          <Tag
                            color={STATUS_COLORS[item.status] || "default"}
                            style={{
                              fontSize: "10px",
                              margin: 0,
                              padding: "0 4px",
                            }}
                          >
                            {STATUS_MAPPING[item.status] || item.status}
                          </Tag>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </Spin>
    </Card>
  );
};

export { VendorMaintenanceWidget };
export default VendorMaintenanceWidget;
