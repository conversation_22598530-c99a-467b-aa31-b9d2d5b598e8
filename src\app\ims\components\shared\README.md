# IMS 共享組件使用指南

## 概述

本目錄包含 IMS (庫存管理系統) 模組的共享組件，旨在提供統一、可重用的 UI 組件，提高開發效率和維護性。

## 🎯 推薦使用方式

### 新頁面開發 - 使用 FilterSearchContainer (推薦)

對於新的頁面開發，強烈推薦使用 `FilterSearchContainer` 組件，它提供了最簡化的使用方式：

```tsx
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';

// 只需要這一個組件就能實現完整的篩選功能！
<FilterSearchContainer
  filterOptions={yourFilterOptions}
  onFilterResult={(state) => {
    // 處理篩選結果
    const filtered = applyFilters(data, state);
    setFilteredData(filtered);
  }}
/>
```

### 現有頁面遷移 - 使用 useFilterSearch Hook

對於現有頁面的遷移，可以使用 `useFilterSearch` Hook 來簡化狀態管理：

```tsx
import { useFilterSearch } from '@/app/ims/hooks/useFilterSearch';

const filterSearch = useFilterSearch({
  onClear: () => {
    // 額外的清除邏輯
  }
});

// 使用 Hook 提供的狀態和方法
```

## 組件列表

### 1. AdvancedFilterComponent

進階篩選組件，支援多種篩選類型和動態配置。

#### 特性
- 支援 input、select、treeSelect 三種篩選類型
- 動態新增/移除篩選條件
- 響應式設計，支援移動端
- TypeScript 類型安全
- 統一的清除功能

#### 使用範例

```tsx
import AdvancedFilterComponent, { type FilterOption } from '@/app/ims/components/shared/AdvancedFilterComponent';

const filterOptions: FilterOption[] = [
  {
    label: "商品名稱",
    value: "name",
    type: "input",
    placeholder: "輸入商品名稱"
  },
  {
    label: "商品狀態",
    value: "status",
    children: [
      { label: "啟用", value: "active" },
      { label: "停用", value: "inactive" }
    ]
  },
  {
    label: "商品分類",
    value: "category",
    type: "treeSelect",
    treeData: categoryTreeData,
    width: 250
  }
];

<AdvancedFilterComponent
  filterOptions={filterOptions}
  onFilterChange={(event) => {
    console.log('篩選變更:', event);
    // 處理篩選邏輯
  }}
  onClearFilters={() => {
    console.log('清除所有篩選');
    // 處理清除邏輯
  }}
  compact={isMobile}
  layout={isMobile ? 'vertical' : 'horizontal'}
/>
```

### 2. FilterSearchPanel

整合篩選和搜尋功能的高層次組件，提供完整的篩選搜尋解決方案。

#### 特性
- 整合 AdvancedFilterComponent 和搜尋框
- 統一的清除功能
- 篩選結果統計顯示
- 響應式設計
- 簡化的配置介面

#### 使用範例

```tsx
import FilterSearchPanel from '@/app/ims/components/shared/FilterSearchPanel';

<FilterSearchPanel
  title="篩選與搜尋"
  filterOptions={filterOptions}
  searchPlaceholder="搜尋商品名稱、編號或條碼"
  onSearchChange={(text) => {
    setSearchText(text);
    // 處理搜尋邏輯
  }}
  onFilterChange={(event) => {
    setFilterData(event);
    // 處理篩選邏輯
  }}
  onClearAll={() => {
    // 清除所有狀態
    setSearchText('');
    setFilterData({ activeFilters: [], filterValues: {} });
  }}
  showStats={true}
  stats={{
    total: totalItems,
    filtered: filteredItems.length
  }}
  compact={isMobile}
/>
```

## 最佳實踐

### 1. 狀態管理

建議使用統一的狀態管理模式：

```tsx
// 篩選狀態
const [searchText, setSearchText] = useState('');
const [filterData, setFilterData] = useState({
  activeFilters: [],
  filterValues: {}
});

// 統一的清除函數
const handleClearAll = useCallback(() => {
  setSearchText('');
  setFilterData({ activeFilters: [], filterValues: {} });
  // 其他相關狀態清除
}, []);
```

### 2. 響應式設計

組件已內建響應式支援，但建議在使用時考慮移動端體驗：

```tsx
const [isMobile, setIsMobile] = useState(false);

useEffect(() => {
  const checkMobile = () => setIsMobile(window.innerWidth <= 768);
  checkMobile();
  window.addEventListener('resize', checkMobile);
  return () => window.removeEventListener('resize', checkMobile);
}, []);

// 在組件中使用
<FilterSearchPanel
  compact={isMobile}
  searchPlaceholder={isMobile ? "搜尋" : "搜尋商品名稱、編號或條碼"}
/>
```

### 3. 類型安全

充分利用 TypeScript 類型定義：

```tsx
import { type FilterOption, type FilterChangeEvent } from '@/app/ims/components/shared/AdvancedFilterComponent';

const filterOptions: FilterOption[] = [
  // 配置篩選選項
];

const handleFilterChange = (event: FilterChangeEvent) => {
  // 類型安全的事件處理
};
```

## 遷移指南

### 從舊版篩選組件遷移

1. **替換 import**：
   ```tsx
   // 舊版
   import AdvancedFilterComponent from './OldFilterComponent';
   
   // 新版
   import FilterSearchPanel from '@/app/ims/components/shared/FilterSearchPanel';
   ```

2. **簡化配置**：
   ```tsx
   // 舊版 - 需要分別配置篩選和搜尋
   <div>
     <AdvancedFilterComponent {...filterProps} />
     <Input {...searchProps} />
     <Button onClick={clearAll}>清除</Button>
   </div>
   
   // 新版 - 統一配置
   <FilterSearchPanel
     filterOptions={filterOptions}
     onSearchChange={handleSearch}
     onFilterChange={handleFilter}
     onClearAll={handleClearAll}
   />
   ```

3. **統一清除邏輯**：
   ```tsx
   // 確保所有相關狀態都在 onClearAll 中處理
   const handleClearAll = () => {
     setSearchText('');
     setFilterData({ activeFilters: [], filterValues: {} });
     setCategoryFilter('');
     setStatusFilter('');
     // 其他相關狀態
   };
   ```

## 故障排除

### 常見問題

1. **篩選不生效**：檢查 `onFilterChange` 回調是否正確更新狀態
2. **清除功能不完整**：確保 `onClearAll` 包含所有相關狀態的清除
3. **移動端顯示問題**：檢查 `compact` 和 `layout` 屬性設定
4. **類型錯誤**：確保正確 import 類型定義

### 調試技巧

啟用開發模式日誌：

```tsx
const handleFilterChange = (event: FilterChangeEvent) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('篩選變更:', event);
  }
  // 處理邏輯
};
```

## 貢獻指南

1. 遵循現有的代碼風格和命名慣例
2. 添加適當的 TypeScript 類型定義
3. 包含響應式設計考慮
4. 更新相關文檔和使用範例
5. 確保向後兼容性

## 版本歷史

- v1.0.0: 初始版本，包含基本篩選功能
- v1.1.0: 新增 FilterSearchPanel 整合組件
- v1.2.0: 改善響應式設計和清除功能一致性
