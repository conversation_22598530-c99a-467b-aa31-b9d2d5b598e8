﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 縣市基本資料表
    /// </summary>
    public class City : ModelBaseEntity
    {
        [Key]
        [Comment("縣市編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string CityId { get; set; } // 縣市編號

        [Comment("縣市名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } // 縣市名稱

        [Comment("英文名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string EnglishName { get; set; } // 英文名稱

        [Comment("說明描述")]
        [Column(TypeName = "nvarchar(500)")]
        public string Description { get; set; } // 說明描述

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序號碼

        public City()
        {
            CityId = "";
            Name = "";
            EnglishName = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class CityDTO : ModelBaseEntityDTO
    {
        public string CityId { get; set; } // 縣市編號
        public string Name { get; set; } // 縣市名稱
        public string EnglishName { get; set; } // 英文名稱
        public string Description { get; set; } // 說明描述
        public int SortCode { get; set; } // 排序號碼

        public CityDTO()
        {
            CityId = "";
            Name = "";
            EnglishName = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}