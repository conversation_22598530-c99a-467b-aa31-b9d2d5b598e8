// PerformancePointGroup 點數群組管理
import { apiEndpoints } from "@/config/api";
import { httpClient } from "@/services/http";
import { ApiResponse } from "@/config/api";

// 點數群組資料型別
export interface PerformancePointGroup {
    uid: string;
    groupName: string;
    weightRatio: string;
    pointTypeCount: number;
}

// 建立空的點數群組資料
export const createEmptyPerformancePointGroup = (): PerformancePointGroup => ({
    uid: '',
    groupName: '',
    weightRatio: '1.00',
    pointTypeCount: 0,
});

// 取得點數群組列表
export async function getPerformancePointGroupList(): Promise<ApiResponse<PerformancePointGroup[]>> {
    return await httpClient(apiEndpoints.getPerformancePointGroupList, {
        method: "GET",
    });
}

// 取得單一點數群組明細
export async function getPerformancePointGroupDetail(uid: string): Promise<ApiResponse<PerformancePointGroup>> {
    return await httpClient(`${apiEndpoints.getPerformancePointGroupDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增點數群組
export async function addPerformancePointGroup(data: Partial<PerformancePointGroup>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addPerformancePointGroup, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增點數群組失敗",
        };
    }
}

// 編輯點數群組
export async function editPerformancePointGroup(data: Partial<PerformancePointGroup>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editPerformancePointGroup, {
            method: "POST",
            body: JSON.stringify(data),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯點數群組失敗",
        };
    }
}

// 刪除點數群組
export async function deletePerformancePointGroup(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deletePerformancePointGroup, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: { "Content-Type": "application/json" },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除點數群組失敗",
        };
    }
}
