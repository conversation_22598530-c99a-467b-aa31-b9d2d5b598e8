using AutoMapper;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Pms
{
    /// <summary>
    /// 廠商修繕作業服務實作
    /// </summary>
    public class VendorMaintenanceService : IVendorMaintenanceService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public VendorMaintenanceService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得修繕申請列表
        /// </summary>
        public async Task<List<VendorMaintenanceDTO>> GetVendorMaintenanceListAsync(
            string? status = null,
            string? maintenanceType = null,
            string? urgencyLevel = null,
            long? startDate = null,
            long? endDate = null)
        {
            try
            {
                var query = _context.Pms_VendorMaintenance.Include(vm => vm.Asset).AsQueryable();

                // 狀態篩選
                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(vm => vm.Status == status);
                }

                // 修繕類型篩選
                if (!string.IsNullOrEmpty(maintenanceType))
                {
                    query = query.Where(vm => vm.MaintenanceType == maintenanceType);
                }

                // 緊急程度篩選
                if (!string.IsNullOrEmpty(urgencyLevel))
                {
                    query = query.Where(vm => vm.UrgencyLevel == urgencyLevel);
                }

                // 日期範圍篩選
                if (startDate.HasValue)
                {
                    query = query.Where(vm => vm.ApplicationDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(vm => vm.ApplicationDate <= endDate.Value);
                }

                var vendorMaintenances = await query
                    .OrderByDescending(vm => vm.ApplicationDate)
                    .ToListAsync();

                var result = vendorMaintenances.Select(vm => new VendorMaintenanceDTO
                {
                    MaintenanceNumber = vm.MaintenanceNumber,
                    AssetId = vm.AssetId,
                    ApplicantId = vm.ApplicantId,
                    DepartmentId = vm.DepartmentId,
                    ApplicationDate = vm.ApplicationDate,
                    FaultDescription = vm.FaultDescription,
                    MaintenanceType = vm.MaintenanceType,
                    UrgencyLevel = vm.UrgencyLevel,
                    EstimatedCost = vm.EstimatedCost,
                    VendorName = vm.VendorName,
                    VendorContact = vm.VendorContact,
                    VendorPhone = vm.VendorPhone,
                    ScheduledStartDate = vm.ScheduledStartDate,
                    ScheduledEndDate = vm.ScheduledEndDate,
                    ActualStartDate = vm.ActualStartDate,
                    ActualEndDate = vm.ActualEndDate,
                    ActualCost = vm.ActualCost,
                    MaintenanceResult = vm.MaintenanceResult,
                    InspectorId = vm.InspectorId,
                    InspectionDate = vm.InspectionDate,
                    InspectionResult = vm.InspectionResult,
                    InspectionNotes = vm.InspectionNotes,
                    Status = vm.Status,
                    Notes = vm.Notes,
                    CreateTime = vm.CreateTime,
                    CreateUserId = vm.CreateUserId,
                    UpdateTime = vm.UpdateTime,
                    UpdateUserId = vm.UpdateUserId
                }).ToList();

                return result;
            }
            catch (Exception ex)
            {
                return new List<VendorMaintenanceDTO>();
            }
        }

        /// <summary>
        /// 根據修繕單號取得詳細資料
        /// </summary>
        public async Task<VendorMaintenanceDTO?> GetVendorMaintenanceByNumberAsync(string maintenanceNumber)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .Include(vm => vm.Asset)
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                    return null;

                return new VendorMaintenanceDTO
                {
                    MaintenanceNumber = vendorMaintenance.MaintenanceNumber,
                    AssetId = vendorMaintenance.AssetId,
                    ApplicantId = vendorMaintenance.ApplicantId,
                    DepartmentId = vendorMaintenance.DepartmentId,
                    ApplicationDate = vendorMaintenance.ApplicationDate,
                    FaultDescription = vendorMaintenance.FaultDescription,
                    MaintenanceType = vendorMaintenance.MaintenanceType,
                    UrgencyLevel = vendorMaintenance.UrgencyLevel,
                    EstimatedCost = vendorMaintenance.EstimatedCost,
                    VendorName = vendorMaintenance.VendorName,
                    VendorContact = vendorMaintenance.VendorContact,
                    VendorPhone = vendorMaintenance.VendorPhone,
                    ScheduledStartDate = vendorMaintenance.ScheduledStartDate,
                    ScheduledEndDate = vendorMaintenance.ScheduledEndDate,
                    ActualStartDate = vendorMaintenance.ActualStartDate,
                    ActualEndDate = vendorMaintenance.ActualEndDate,
                    ActualCost = vendorMaintenance.ActualCost,
                    MaintenanceResult = vendorMaintenance.MaintenanceResult,
                    InspectorId = vendorMaintenance.InspectorId,
                    InspectionDate = vendorMaintenance.InspectionDate,
                    InspectionResult = vendorMaintenance.InspectionResult,
                    InspectionNotes = vendorMaintenance.InspectionNotes,
                    Status = vendorMaintenance.Status,
                    Notes = vendorMaintenance.Notes,
                    CreateTime = vendorMaintenance.CreateTime,
                    CreateUserId = vendorMaintenance.CreateUserId,
                    UpdateTime = vendorMaintenance.UpdateTime,
                    UpdateUserId = vendorMaintenance.UpdateUserId
                };
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 新增修繕申請
        /// </summary>
        public async Task<(bool success, string message)> CreateVendorMaintenanceAsync(VendorMaintenanceDTO vendorMaintenanceDto)
        {
            try
            {
                // 檢查財產是否存在
                var asset = await _context.Pms_Assets.FirstOrDefaultAsync(a => a.AssetId == vendorMaintenanceDto.AssetId);
                if (asset == null)
                {
                    return (false, "指定的財產不存在");
                }

                // 產生修繕單號
                var maintenanceNumber = await GenerateMaintenanceNumberAsync();

                var vendorMaintenance = new VendorMaintenance
                {
                    MaintenanceNumber = maintenanceNumber,
                    AssetId = vendorMaintenanceDto.AssetId,
                    ApplicantId = vendorMaintenanceDto.ApplicantId,
                    DepartmentId = vendorMaintenanceDto.DepartmentId,
                    ApplicationDate = vendorMaintenanceDto.ApplicationDate,
                    FaultDescription = vendorMaintenanceDto.FaultDescription,
                    MaintenanceType = vendorMaintenanceDto.MaintenanceType,
                    UrgencyLevel = vendorMaintenanceDto.UrgencyLevel,
                    EstimatedCost = vendorMaintenanceDto.EstimatedCost,
                    Status = VendorMaintenanceStatus.PENDING,
                    Notes = vendorMaintenanceDto.Notes,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds(),
                    CreateUserId = vendorMaintenanceDto.CreateUserId
                };

                _context.Pms_VendorMaintenance.Add(vendorMaintenance);
                await _context.SaveChangesAsync();

                return (true, "修繕申請新增成功");
            }
            catch (Exception ex)
            {
                return (false, "新增修繕申請失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 修改修繕申請
        /// </summary>
        public async Task<(bool success, string message)> UpdateVendorMaintenanceAsync(string maintenanceNumber, VendorMaintenanceDTO vendorMaintenanceDto)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                // 只有待審核狀態才能修改
                if (vendorMaintenance.Status != VendorMaintenanceStatus.PENDING)
                {
                    return (false, "只有待審核狀態的修繕申請才能修改");
                }

                // 更新資料
                vendorMaintenance.DepartmentId = vendorMaintenanceDto.DepartmentId;
                vendorMaintenance.ApplicationDate = vendorMaintenanceDto.ApplicationDate;
                vendorMaintenance.ApplicantId = vendorMaintenanceDto.ApplicantId;
                vendorMaintenance.FaultDescription = vendorMaintenanceDto.FaultDescription;
                vendorMaintenance.FaultDescription = vendorMaintenanceDto.FaultDescription;
                vendorMaintenance.MaintenanceType = vendorMaintenanceDto.MaintenanceType;
                vendorMaintenance.UrgencyLevel = vendorMaintenanceDto.UrgencyLevel;
                vendorMaintenance.EstimatedCost = vendorMaintenanceDto.EstimatedCost;
                vendorMaintenance.Notes = vendorMaintenanceDto.Notes;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = vendorMaintenanceDto.UpdateUserId;

                await _context.SaveChangesAsync();

                return (true, "修繕申請修改成功");
            }
            catch (Exception ex)
            {
                return (false, "修改修繕申請失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 刪除修繕申請
        /// </summary>
        public async Task<(bool success, string message)> DeleteVendorMaintenanceAsync(string maintenanceNumber)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                // 只有待審核狀態才能刪除
                if (vendorMaintenance.Status != VendorMaintenanceStatus.PENDING)
                {
                    return (false, "只有待審核狀態的修繕申請才能刪除");
                }

                vendorMaintenance.DeleteTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.DeleteUserId = vendorMaintenance.UpdateUserId;
                vendorMaintenance.IsDeleted = true;

                _context.Update(vendorMaintenance);
                await _context.SaveChangesAsync();

                return (true, "修繕申請刪除成功");
            }
            catch (Exception ex)
            {
                return (false, "刪除修繕申請失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 審核修繕申請
        /// </summary>
        public async Task<(bool success, string message)> ApproveVendorMaintenanceAsync(string maintenanceNumber, bool isApproved, string? reason, string approverId, string approverName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                if (vendorMaintenance.Status != VendorMaintenanceStatus.PENDING)
                {
                    return (false, "只有待審核狀態的修繕申請才能進行審核");
                }

                vendorMaintenance.Status = isApproved ? VendorMaintenanceStatus.APPROVED : VendorMaintenanceStatus.REJECTED;
                vendorMaintenance.ApproverId = approverId;
                vendorMaintenance.ApprovalDate = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.ApprovalComment = reason;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = approverId;

                await _context.SaveChangesAsync();

                var statusText = isApproved ? "核准" : "拒絕";
                return (true, $"修繕申請{statusText}成功");
            }
            catch (Exception ex)
            {
                return (false, "審核修繕申請失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 指派廠商
        /// </summary>
        public async Task<(bool success, string message)> AssignVendorAsync(string maintenanceNumber, string vendorId, string vendorName,
            string? vendorContact, string? vendorPhone, long? scheduledStartDate, long? scheduledEndDate,
            string operatorId, string operatorName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                if (vendorMaintenance.Status != VendorMaintenanceStatus.APPROVED)
                {
                    return (false, "只有已核准狀態的修繕申請才能指派廠商");
                }

                vendorMaintenance.VendorName = vendorName;
                vendorMaintenance.VendorContact = vendorContact;
                vendorMaintenance.VendorPhone = vendorPhone;
                vendorMaintenance.ScheduledStartDate = scheduledStartDate ?? 0;
                vendorMaintenance.ScheduledEndDate = scheduledEndDate ?? 0;
                vendorMaintenance.Status = VendorMaintenanceStatus.ASSIGNED;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "廠商指派成功");
            }
            catch (Exception ex)
            {
                return (false, "指派廠商失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 開始施工
        /// </summary>
        public async Task<(bool success, string message)> StartMaintenanceAsync(string maintenanceNumber, long actualStartDate, string operatorId, string operatorName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                if (vendorMaintenance.Status != VendorMaintenanceStatus.ASSIGNED)
                {
                    return (false, "只有已指派廠商狀態的修繕申請才能開始施工");
                }

                vendorMaintenance.ActualStartDate = actualStartDate;
                vendorMaintenance.Status = VendorMaintenanceStatus.IN_PROGRESS;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "修繕施工已開始");
            }
            catch (Exception ex)
            {
                return (false, "開始施工失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 完成修繕
        /// </summary>
        public async Task<(bool success, string message)> CompleteMaintenanceAsync(string maintenanceNumber, long actualEndDate,
            decimal? actualCost, string? maintenanceResult, string operatorId, string operatorName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                if (vendorMaintenance.Status != VendorMaintenanceStatus.IN_PROGRESS)
                {
                    return (false, "只有施工中狀態的修繕申請才能完成修繕");
                }

                vendorMaintenance.ActualEndDate = actualEndDate;
                vendorMaintenance.ActualCost = actualCost;
                vendorMaintenance.MaintenanceResult = maintenanceResult;
                vendorMaintenance.Status = VendorMaintenanceStatus.COMPLETED;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "修繕已完成");
            }
            catch (Exception ex)
            {
                return (false, "完成修繕失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 驗收修繕
        /// </summary>
        public async Task<(bool success, string message)> InspectMaintenanceAsync(string maintenanceNumber, string inspectionResult,
            string? inspectionNotes, string inspectorId, string inspectorName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                if (vendorMaintenance.Status != VendorMaintenanceStatus.COMPLETED)
                {
                    return (false, "只有已完成狀態的修繕申請才能進行驗收");
                }

                vendorMaintenance.InspectorId = inspectorId;
                vendorMaintenance.InspectionDate = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.InspectionResult = inspectionResult;
                vendorMaintenance.InspectionNotes = inspectionNotes;
                vendorMaintenance.Status = VendorMaintenanceStatus.INSPECTED;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = inspectorId;

                await _context.SaveChangesAsync();

                return (true, "修繕驗收完成");
            }
            catch (Exception ex)
            {
                return (false, "驗收修繕失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 結案
        /// </summary>
        public async Task<(bool success, string message)> CloseMaintenanceAsync(string maintenanceNumber, string operatorId, string operatorName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                if (vendorMaintenance.Status != VendorMaintenanceStatus.INSPECTED)
                {
                    return (false, "只有已驗收狀態的修繕申請才能結案");
                }

                vendorMaintenance.Status = VendorMaintenanceStatus.CLOSED;
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "修繕申請已結案");
            }
            catch (Exception ex)
            {
                return (false, "結案失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 取消修繕
        /// </summary>
        public async Task<(bool success, string message)> CancelMaintenanceAsync(string maintenanceNumber, string reason, string operatorId, string operatorName)
        {
            try
            {
                var vendorMaintenance = await _context.Pms_VendorMaintenance
                    .FirstOrDefaultAsync(vm => vm.MaintenanceNumber == maintenanceNumber);

                if (vendorMaintenance == null)
                {
                    return (false, "修繕申請不存在");
                }

                // 已結案的申請不能取消
                if (vendorMaintenance.Status == VendorMaintenanceStatus.CLOSED)
                {
                    return (false, "已結案的修繕申請不能取消");
                }

                vendorMaintenance.Status = VendorMaintenanceStatus.CANCELLED;
                vendorMaintenance.Notes = (vendorMaintenance.Notes ?? "") + $"\n取消原因：{reason}";
                vendorMaintenance.UpdateTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
                vendorMaintenance.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "修繕申請已取消");
            }
            catch (Exception ex)
            {
                return (false, "取消修繕申請失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 批次處理修繕申請
        /// </summary>
        public async Task<(bool success, string message)> BatchProcessAsync(VendorMaintenanceBatchDTO batchDto)
        {
            try
            {
                var processedCount = 0;
                var errors = new List<string>();

                foreach (var maintenanceNumber in batchDto.MaintenanceNumbers)
                {
                    var result = batchDto.Action.ToUpper() switch
                    {
                        "APPROVE" => await ApproveVendorMaintenanceAsync(maintenanceNumber, true, batchDto.Reason, batchDto.OperatorId, batchDto.OperatorName),
                        "REJECT" => await ApproveVendorMaintenanceAsync(maintenanceNumber, false, batchDto.Reason, batchDto.OperatorId, batchDto.OperatorName),
                        "CANCEL" => await CancelMaintenanceAsync(maintenanceNumber, batchDto.Reason ?? "批次取消", batchDto.OperatorId, batchDto.OperatorName),
                        _ => (false, "不支援的批次操作")
                    };

                    var (success, errorMessage) = result;
                    if (success)
                    {
                        processedCount++;
                    }
                    else
                    {
                        errors.Add($"{maintenanceNumber}: {errorMessage}");
                    }
                }

                var message = $"批次處理完成，成功處理 {processedCount} 筆";
                if (errors.Any())
                {
                    message += $"，失敗 {errors.Count} 筆：{string.Join("; ", errors)}";
                }

                return (true, message);
            }
            catch (Exception ex)
            {
                return (false, "批次處理失敗：" + ex.Message);
            }
        }

        /// <summary>
        /// 取得修繕統計資料
        /// </summary>
        public async Task<VendorMaintenanceStatisticsDTO> GetStatisticsAsync(long? startDate = null, long? endDate = null)
        {
            try
            {
                var query = _context.Pms_VendorMaintenance.AsQueryable();

                if (startDate.HasValue)
                {
                    query = query.Where(vm => vm.ApplicationDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(vm => vm.ApplicationDate <= endDate.Value);
                }

                var vendorMaintenances = await query.ToListAsync();

                var statistics = new VendorMaintenanceStatisticsDTO
                {
                    TotalCount = vendorMaintenances.Count,
                    PendingCount = vendorMaintenances.Count(vm => vm.Status == VendorMaintenanceStatus.PENDING),
                    ApprovedCount = vendorMaintenances.Count(vm => vm.Status == VendorMaintenanceStatus.APPROVED),
                    InProgressCount = vendorMaintenances.Count(vm => vm.Status == VendorMaintenanceStatus.IN_PROGRESS),
                    CompletedCount = vendorMaintenances.Count(vm => vm.Status == VendorMaintenanceStatus.COMPLETED),
                    InspectedCount = vendorMaintenances.Count(vm => vm.Status == VendorMaintenanceStatus.INSPECTED),
                    ClosedCount = vendorMaintenances.Count(vm => vm.Status == VendorMaintenanceStatus.CLOSED),
                    TotalEstimatedCost = vendorMaintenances.Sum(vm => vm.EstimatedCost),
                    TotalActualCost = vendorMaintenances.Where(vm => vm.ActualCost.HasValue).Sum(vm => vm.ActualCost.Value)
                };

                // 計算平均完成天數
                var completedMaintenances = vendorMaintenances
                    .Where(vm => vm.ActualStartDate != 0 && vm.ActualEndDate != 0)
                    .ToList();

                if (completedMaintenances.Any())
                {
                    statistics.AverageCompletionDays = completedMaintenances
                        .Average(vm =>
                        {
                            var startDate = DateTimeOffset.FromUnixTimeMilliseconds(vm.ActualStartDate).DateTime;
                            var endDate = DateTimeOffset.FromUnixTimeMilliseconds(vm.ActualEndDate).DateTime;
                            return (endDate - startDate).TotalDays;
                        });
                }

                // 修繕類型統計
                statistics.TypeStatistics = vendorMaintenances
                    .GroupBy(vm => vm.MaintenanceType)
                    .Select(g => new MaintenanceTypeStatistic
                    {
                        Type = g.Key,
                        Count = g.Count(),
                        TotalCost = g.Where(vm => vm.ActualCost.HasValue).Sum(vm => vm.ActualCost.Value)
                    })
                    .ToList();

                // 緊急程度統計
                statistics.UrgencyStatistics = vendorMaintenances
                    .GroupBy(vm => vm.UrgencyLevel)
                    .Select(g => new UrgencyLevelStatistic
                    {
                        Level = g.Key,
                        Count = g.Count(),
                        AverageCompletionDays = g.Where(vm => vm.ActualStartDate != 0 && vm.ActualEndDate != 0)
                            .DefaultIfEmpty()
                            .Average(vm => vm != null && vm.ActualStartDate != 0 && vm.ActualEndDate != 0
                                ? (DateTimeOffset.FromUnixTimeMilliseconds(vm.ActualEndDate).DateTime -
                                   DateTimeOffset.FromUnixTimeMilliseconds(vm.ActualStartDate).DateTime).TotalDays
                                : 0)
                    })
                    .ToList();

                return statistics;
            }
            catch (Exception ex)
            {
                return new VendorMaintenanceStatisticsDTO();
            }
        }

        /// <summary>
        /// 產生修繕單號
        /// </summary>
        public async Task<string> GenerateMaintenanceNumberAsync()
        {
            try
            {
                var today = DateTime.Now.ToString("yyyyMMdd");
                var prefix = $"VM{today}";

                var lastNumber = await _context.Pms_VendorMaintenance
                    .Where(vm => vm.MaintenanceNumber.StartsWith(prefix))
                    .OrderByDescending(vm => vm.MaintenanceNumber)
                    .Select(vm => vm.MaintenanceNumber)
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(lastNumber))
                {
                    return $"{prefix}001";
                }

                var lastSequence = lastNumber.Substring(prefix.Length);
                if (int.TryParse(lastSequence, out var sequence))
                {
                    return $"{prefix}{(sequence + 1):D3}";
                }

                return $"{prefix}001";
            }
            catch (Exception ex)
            {
                return $"VM{DateTime.Now:yyyyMMdd}001";
            }
        }

        /// <summary>
        /// 檢查逾期修繕案件
        /// </summary>
        public async Task<List<VendorMaintenanceDTO>> GetOverdueMaintenanceAsync()
        {
            try
            {
                var todayTimestamp = new DateTimeOffset(DateTime.Today).ToUnixTimeMilliseconds();

                var overdueMaintenances = await _context.Pms_VendorMaintenance
                    .Include(vm => vm.Asset)
                    .Where(vm => vm.ScheduledEndDate != 0 &&
                                vm.ScheduledEndDate < todayTimestamp &&
                                vm.Status != VendorMaintenanceStatus.COMPLETED &&
                                vm.Status != VendorMaintenanceStatus.INSPECTED &&
                                vm.Status != VendorMaintenanceStatus.CLOSED &&
                                vm.Status != VendorMaintenanceStatus.CANCELLED)
                    .ToListAsync();

                return overdueMaintenances.Select(vm => new VendorMaintenanceDTO
                {
                    MaintenanceNumber = vm.MaintenanceNumber,
                    AssetId = vm.AssetId,
                    ApplicantId = vm.ApplicantId,
                    DepartmentId = vm.DepartmentId,
                    ApplicationDate = vm.ApplicationDate,
                    FaultDescription = vm.FaultDescription,
                    MaintenanceType = vm.MaintenanceType,
                    UrgencyLevel = vm.UrgencyLevel,
                    EstimatedCost = vm.EstimatedCost,
                    VendorName = vm.VendorName,
                    VendorContact = vm.VendorContact,
                    VendorPhone = vm.VendorPhone,
                    ScheduledStartDate = vm.ScheduledStartDate,
                    ScheduledEndDate = vm.ScheduledEndDate,
                    ActualStartDate = vm.ActualStartDate,
                    ActualEndDate = vm.ActualEndDate,
                    ActualCost = vm.ActualCost,
                    MaintenanceResult = vm.MaintenanceResult,
                    InspectorId = vm.InspectorId,
                    InspectionDate = vm.InspectionDate,
                    InspectionResult = vm.InspectionResult,
                    InspectionNotes = vm.InspectionNotes,
                    Status = vm.Status,
                    Notes = vm.Notes,
                    CreateTime = vm.CreateTime,
                    CreateUserId = vm.CreateUserId,
                    UpdateTime = vm.UpdateTime,
                    UpdateUserId = vm.UpdateUserId
                }).ToList();
            }
            catch (Exception ex)
            {
                return new List<VendorMaintenanceDTO>();
            }
        }
    }
}