
# **IMS 夥伴模組 - 開發規劃書**

- **版本**: 6.0
- **日期**: 2025-07-02
- **作者**: Gemini (Revised)
- **狀態**: 最終定稿 (遵循 Item 慣例)

---

## 1. 核心開發原則 (The "Item" Convention)

本模組的開發將**嚴格遵循**現有 `Item` 模組所揭示的設計慣例，以確保新舊程式碼風格的絕對一致。

- **單一 DTO 原則**: 
    - 對於 `Partner` 實體，只使用唯一的 `PartnerDto` 進行所有 `Create`, `Read`, `Update`, `Delete` 操作。
    - **不建立**任何新的 DTO 類別 (如 `PartnerCreateDto` 或 `PartnerUpdateDto`)。

- **Service 層處理 ID 與資料轉換**:
    - **ID 生成**: 在 `AddAsync` 方法中，將忽略傳入的 `PartnerDto.PartnerID`，並由 Service 層使用 `Guid.NewGuid()` 強制生成新 ID。
    - **手動映射**: **針對本模組 (IMS 夥伴模組)，嚴格不使用 AutoMapper**。所有 Entity 與 DTO 之間的資料轉換，都將在 Service 層透過手動欄位對應完成。專案其他模組可能使用 AutoMapper，但本模組必須手動處理。

- **關聯資料獨立管理**:
    - `PartnerController` 的職責僅限於 `Partner` 核心實體及其一對一的明細表 (`IndividualDetail`, `CustomerDetail` 等)。
    - `Partner` 的關聯集合（如 `Addresses`, `Contacts`）的 CRUD 操作，應由其各自獨立的 Controller (如 `PartnerAddressController`, `PartnerContactController`) 進行管理。`PartnerController` 的 `Create/Update` 方法**不處理**這些集合的寫入。

---

## 2. 資料模型與 DTO

### 2.1. 實體關聯圖 (ERD)

```mermaid
erDiagram
    Partner {
        Guid PartnerID PK
    }
    IndividualDetail {
        Guid PartnerID PK,FK
    }
    OrganizationDetail {
        Guid PartnerID PK,FK
    }
    CustomerDetail {
        Guid PartnerID PK,FK
        Guid CustomerCategoryID FK
    }
    SupplierDetail {
        Guid PartnerID PK,FK
        Guid SupplierCategoryID FK
    }
    CustomerCategory {
        Guid CustomerCategoryID PK
    }
    SupplierCategory {
        Guid SupplierCategoryID PK
    }
    PartnerAddress {
        Guid PartnerAddressID PK
        Guid PartnerID FK
    }
    Contact {
        Guid ContactID PK
    }
    ContactRole {
        Guid ContactRoleID PK
    }
    PartnerContact {
        Guid PartnerID PK,FK
        Guid ContactID PK,FK
        Guid ContactRoleID PK,FK
    }

    Partner ||--o| IndividualDetail : "has"
    Partner ||--o| OrganizationDetail : "has"
    Partner ||--o| CustomerDetail : "has"
    Partner ||--o| SupplierDetail : "has"
    CustomerDetail }|--|| CustomerCategory : "belongs to"
    SupplierDetail }|--|| SupplierCategory : "belongs to"
    Partner ||--o{ PartnerAddress : "has many"
    Partner }o--o{ PartnerContact : "links to"
    Contact }o--o{ PartnerContact : "links from"
    ContactRole }o--o{ PartnerContact : "has role"
```

### 2.2. DTO 使用說明

- 所有 Controller 端點將統一接收 `[FromBody] PartnerDto`。
- `PartnerDto` 中的 `Addresses` 和 `Contacts` 集合屬性，僅在 `Get` 相關方法中被填充並回傳，在 `Create/Update` 時將被 Service 層忽略。

---

## 3. 技術實作規劃

### 3.1. Controller API 規格

**`Controllers/Ims/PartnerController.cs`**

| 方法 | HTTP Verb | Request Body | 成功回應 | 關鍵職責 |
| :--- | :--- | :--- | :--- | :--- |
| `GetAll` | `GET` | (None) | `200 OK` (List of `PartnerDto`) | 查詢並回傳 `Partner` 列表，包含關聯資料。 |
| `Get`| `GET` | (None) | `200 OK` (`PartnerDto`) | 查詢並回傳單一 `Partner`，包含關聯資料。 |
| `Add` | `POST` | `PartnerDto` | `201 Created` | 僅處理 `Partner` 核心資料的新增。 |
| `Update` | `PATCH` | `PartnerDto` | `200 OK` | 僅處理 `Partner` 核心資料的更新。 |
| `Delete` | `DELETE`| `PartnerDto` | `204 No Content` | 僅處理 `Partner` 核心資料的軟刪除。 |

### 3.2. Service 層實作要點

**`Services/Ims/PartnerService.cs`**

- **`AddAsync(PartnerDto dto)`**: 
    1. 忽略 `dto.PartnerID`。
    2. 建立新的 `Partner` Entity，`PartnerID = Guid.NewGuid()`。
    3. 手動將 `dto` 中的 `IndividualDetail`, `OrganizationDetail` 等對應欄位映射到新 Entity。
    4. 將新 Entity 新增至 `_context` 並儲存。

- **`UpdateAsync(PartnerDto dto)`**: 
    1. 使用 `dto.PartnerID` 從 `_context` 查詢出 Entity。
    2. 若找不到，回傳錯誤。
    3. 手動將 `dto` 中的欄位更新至查找到的 Entity。
    4. 更新 `_context` 並儲存。

- **`GetAsync(Guid partnerId)`**: 
    1. 使用 `partnerId` 查詢 `Partner`。
    2. **必須使用** `Include()` 和 `ThenInclude()` 來載入 `IndividualDetail`, `OrganizationDetail`, `CustomerDetail`, `SupplierDetail`, `Addresses`, `PartnerContacts.Contact` 等所有關聯資料。
    3. 手動將查詢到的完整 Entity 樹，映射到 `PartnerDto` 並回傳。

- **手動映射範例 (Entity to DTO)**:
    ```csharp
    // 範例：將 Partner Entity 映射到 PartnerDto
    private PartnerDto MapPartnerToDto(Partner entity)
    {
        if (entity == null) return null;

        var dto = new PartnerDto
        {
            PartnerID = entity.PartnerID,
            // 複製 ModelBaseEntity 的屬性
            CreateTime = entity.CreateTime,
            CreateUserId = entity.CreateUserId,
            UpdateTime = entity.UpdateTime,
            UpdateUserId = entity.UpdateUserId,
            DeleteTime = entity.DeleteTime,
            DeleteUserId = entity.DeleteUserId,
            IsDeleted = entity.IsDeleted,

            // 映射一對一關聯的詳細資料
            IndividualDetail = entity.IndividualDetail != null ? new IndividualDetailDTO
            {
                PartnerID = entity.IndividualDetail.PartnerID,
                LastName = entity.IndividualDetail.LastName,
                FirstName = entity.IndividualDetail.FirstName,
                IdentificationNumber = entity.IndividualDetail.IdentificationNumber,
                BirthDate = entity.IndividualDetail.BirthDate
            } : null,
            OrganizationDetail = entity.OrganizationDetail != null ? new OrganizationDetailDTO
            {
                PartnerID = entity.OrganizationDetail.PartnerID,
                CompanyName = entity.OrganizationDetail.CompanyName,
                BussinessID = entity.OrganizationDetail.BussinessID,
                TaxID = entity.OrganizationDetail.TaxID,
                ResponsiblePerson = entity.OrganizationDetail.ResponsiblePerson
            } : null,
            CustomerDetail = entity.CustomerDetail != null ? new CustomerDetailDTO
            {
                PartnerID = entity.CustomerDetail.PartnerID,
                CustomerCode = entity.CustomerDetail.CustomerCode,
                CustomerCategoryID = entity.CustomerDetail.CustomerCategoryID,
                SettlementDay = entity.CustomerDetail.SettlementDay,
                // 注意：CustomerCategory 導航屬性可能需要額外映射，取決於需求
            } : null,
            SupplierDetail = entity.SupplierDetail != null ? new SupplierDetailDTO
            {
                PartnerID = entity.SupplierDetail.PartnerID,
                SupplierCode = entity.SupplierDetail.SupplierCode,
                SupplierCategoryID = entity.SupplierDetail.SupplierCategoryID,
                SettlementDay = entity.SupplierDetail.SettlementDay,
                // 注意：SupplierCategory 導航屬性可能需要額外映射，取決於需求
            } : null,

            // 集合屬性在 Get 相關方法中填充，Create/Update 時忽略
            // Addresses = entity.Addresses?.Select(a => new PartnerAddressDTO { /* 映射地址屬性 */ }).ToList(),
            // PartnerContacts = entity.PartnerContacts?.Select(pc => new PartnerContactDTO { /* 映射聯絡人關聯屬性 */ }).ToList()
        };
        return dto;
    }

    // 範例：將 PartnerDto 映射到現有的 Partner Entity (用於 Update)
    private void MapDtoToEntity(PartnerDto dto, Partner entity)
    {
        if (dto == null || entity == null) return;

        // 核心屬性映射
        // entity.PartnerID = dto.PartnerID; // ID 通常不更新

        // 更新 ModelBaseEntity 的屬性 (通常由 SaveChangesAsync 自動處理 UpdateTime/UserId)
        // entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        // entity.UpdateUserId = _currentUserService.GetUserId(); // 假設有 CurrentUserService

        // 映射一對一關聯的詳細資料 (如果存在且需要更新)
        if (dto.IndividualDetail != null)
        {
            if (entity.IndividualDetail == null)
            {
                entity.IndividualDetail = new IndividualDetail { PartnerID = entity.PartnerID };
            }
            entity.IndividualDetail.LastName = dto.IndividualDetail.LastName;
            entity.IndividualDetail.FirstName = dto.IndividualDetail.FirstName;
            entity.IndividualDetail.IdentificationNumber = dto.IndividualDetail.IdentificationNumber;
            entity.IndividualDetail.BirthDate = dto.IndividualDetail.BirthDate;
        }
        // 針對 OrganizationDetail, CustomerDetail, SupplierDetail 進行類似處理
        // ...

        // 集合屬性 (Addresses, PartnerContacts) 在 Create/Update 時應被忽略，由獨立 Controller 管理
    }
    ```

### 3.3. 前置建議：獨立的關聯資料管理

為了實現對 `Partner` 關聯資料的管理，強烈建議後續**另行開發**以下 Controller：
- `PartnerAddressController`
- `PartnerContactController`

---

## 4. 錯誤處理與回應標準化

本專案透過 `Middlewares/GlobalExceptionHandlerMiddleware.cs` 實現全域錯誤處理。

- **錯誤捕獲**: 所有未處理的例外都會被此中介軟體捕獲。
- **回應格式**: 錯誤回應統一為 JSON 格式，`Content-Type` 為 `application/json`。
- **HTTP 狀態碼**: 預設為 `500 Internal Server Error`。
- **錯誤訊息**:
    - **開發環境 (Development)**: 回應包含詳細的錯誤訊息 (`ex.Message`) 和堆疊追蹤 (`ex.ToString()`)，以利開發者除錯。
    - **生產環境 (Production)**: 回應為通用訊息 "伺服器發生未預期的錯誤，請聯繫系統管理員。"，以避免敏感資訊洩露。

**開發注意事項**:
- 對於特定的業務邏輯錯誤（例如：資料驗證失敗、找不到資源），建議在 Service 層拋出自訂的例外（例如 `BusinessLogicException`），並在 `GlobalExceptionHandlerMiddleware` 中擴展處理，將其轉換為更具體的 HTTP 狀態碼（如 `400 Bad Request`, `404 Not Found`）和對應的錯誤訊息，而非統一回傳 `500`。

---

## 5. 驗證策略

本專案利用 C# Model 中的 `System.ComponentModel.DataAnnotations` 屬性（如 `[Required]`, `[MaxLength]`, `[Range]`）進行資料驗證。ASP.NET Core 的 Model Binding 和 Model Validation 機制會自動觸發這些驗證。

**開發注意事項**:
- 在 Controller Action 中，務必檢查 `ModelState.IsValid` 以判斷資料是否通過驗證。例如：
    ```csharp
    [HttpPost]
    public async Task<IActionResult> AddPartner([FromBody] PartnerDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState); // 回傳驗證錯誤訊息
        }
        // ... 繼續處理業務邏輯
    }
    ```
- 建議實作一個全域的 Action Filter 或在基底 Controller 中統一處理 `ModelState` 錯誤，將驗證失敗的訊息格式化為 JSON 回應，並回傳 `400 Bad Request`，以提供一致的錯誤處理體驗。
- 對於超出 Data Annotations 範圍的複雜業務邏輯驗證，應在 Service 層實作，並在驗證失敗時拋出自訂的例外（例如 `BusinessLogicException`），以便 `GlobalExceptionHandlerMiddleware` 進行統一處理。

---

## 6. 日誌記錄策略

本專案的日誌記錄主要透過 `Microsoft.Extensions.Logging` 框架，並整合了自訂的 `MongoDBLoggerService` 進行集中化日誌儲存。

- **日誌服務**: `MongoDBLoggerService` 實現了 `ILoggerService` 介面，負責將日誌寫入 MongoDB 資料庫。
- **實體變更日誌**: `ERPDbContext.cs` 中的 `SaveChangesAsync` 方法會自動捕獲實體變更，並透過 `ILoggerService` 記錄詳細的變更日誌，這對於審計追蹤非常有用。
- **日誌級別**: 在 `Program.cs` 中配置了日誌級別：
    - **開發環境**: `LogLevel.Debug` (最低級別)
    - **生產環境**: `LogLevel.Information`

**開發注意事項**:
- 在 Service 或 Controller 中，可以透過依賴注入 `ILoggerService` 來記錄日誌。例如：
    ```csharp
    private readonly ILoggerService _logger;

    public PartnerService(ILoggerService logger, ERPDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task AddAsync(PartnerDto dto)
    {
        _logger.LogInformation("Adding new partner: {PartnerName}", dto.Name); // 範例日誌記錄
        // ... 業務邏輯
    }
    ```
- 選擇適當的日誌級別：
    - `Debug`: 詳細的內部執行資訊，僅在開發時使用。
    - `Information`: 應用程式的正常流程事件，例如請求開始、重要操作完成。
    - `Warning`: 可能導致問題的情況，但應用程式仍能繼續執行。
    - `Error`: 應用程式崩潰或無法恢復的嚴重錯誤。
    - `Critical`: 應用程式崩潰或無法恢復的嚴重錯誤。
- 雖然專案中存在 `log4net.config` 檔案，但目前 `log4net` 並未在 `Program.cs` 中啟用，請勿依賴其進行日誌記錄。

---

## 7. 身份驗證與授權

本專案使用 JWT (JSON Web Token) 進行身份驗證，並透過自訂中介軟體實現授權。

- **身份驗證**: `Middlewares/PasswordValidationMiddleware.cs` 負責驗證傳入請求的 JWT Token。如果 Token 無效或缺失，請求將被拒絕並回傳 `401 Unauthorized`。驗證成功後，解析出的用戶身份資訊 (ClaimsPrincipal) 會被設置到 `HttpContext.User`。
- **授權**: 專案中包含 `Middlewares/GroupPermissions.cs` 和 `Middlewares/MenuPermissions.cs` 兩個自訂授權中介軟體，用於基於用戶的群組和選單權限進行細粒度控制。

**開發注意事項**:
- **啟用授權**: 目前 `Program.cs` 中 `app.UseGroupPermissions()` 和 `app.UseMenuPermissions()` 被註解掉。如果需要啟用這些細粒度授權，請在 `Program.cs` 中取消註解。
- **使用 `[Authorize]` 屬性**: 在 Controller 或 Action 上使用 `[Authorize]` 屬性來保護 API 端點。例如：
    ```csharp
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class PartnerController : ControllerBase
    {
        // ...
    }

    [Authorize(Roles = "Admin,Manager")] // 僅允許 Admin 和 Manager 角色存取
    [HttpGet("GetAll")]
    public async Task<IActionResult> GetAll()
    {
        // ...
    }
    ```
- **獲取當前用戶資訊**: 可以透過注入 `ICurrentUserService` 來獲取當前登入用戶的 ID 或其他 Claims 資訊。

---

## 8. `GetAll` 方法的擴展性 (分頁/過濾/排序)

對於 `GetAll` 類型的方法，特別是當資料量可能較大時，應考慮實作分頁 (Pagination)、過濾 (Filtering) 和排序 (Sorting) 功能，以提升 API 的效率和可用性。

**開發注意事項**:
- **定義查詢參數 DTO**: 建議定義一個通用的查詢參數 DTO，例如 `QueryParameters` 或 `PaginationParameters`，包含以下屬性：
    - `int PageNumber`: 當前頁碼 (預設 1)
    - `int PageSize`: 每頁記錄數 (預設 10-20)
    - `string SortBy`: 排序欄位名稱 (例如 "Name", "CreateTime")
    - `string SortOrder`: 排序順序 ("asc" 或 "desc")
    - `string Filter`: 過濾條件 (例如 JSON 字串或多個單獨的過濾欄位)
- **Service 層實作**: 在 Service 層接收這些查詢參數，並使用 `IQueryable` 的擴展方法來構建動態查詢。例如：
    ```csharp
    public async Task<List<PartnerDto>> GetAllAsync(QueryParameters queryParams)
    {
        var query = _context.Ims_Partner.AsQueryable();

        // 範例：過濾
        if (!string.IsNullOrEmpty(queryParams.Filter))
        {
            query = query.Where(p => p.Name.Contains(queryParams.Filter)); // 假設 Partner 有 Name 屬性
        }

        // 範例：排序
        if (!string.IsNullOrEmpty(queryParams.SortBy))
        {
            query = query.OrderBy(queryParams.SortBy, queryParams.SortOrder); // 需要自訂擴展方法
        }

        // 範例：分頁
        query = query.Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                     .Take(queryParams.PageSize);

        var entities = await query.ToListAsync();
        // 手動映射到 DTO
        return entities.Select(e => MapPartnerToDto(e)).ToList();
    }
    ```
- **擴展方法**: 可以參考 `Extensions/EmployeeQueryExtensions.cs` 的模式，為 `IQueryable` 建立通用的分頁、過濾和排序擴展方法。

---

## 9. 併發控制

目前專案中未明確實作樂觀併發控制 (Optimistic Concurrency Control)。這表示在多個用戶同時嘗試修改同一筆資料時，可能會發生「最後寫入者獲勝」的情況，導致資料遺失。

**開發注意事項**:
- 如果應用程式需要處理高併發更新的場景，建議考慮實作樂觀併發控制。
- 常見的實作方式是在資料庫實體中添加一個 `RowVersion` (或 `Timestamp`) 屬性。Entity Framework Core 可以配置此屬性，以便在更新時檢查資料版本，如果版本不匹配則拋出 `DbUpdateConcurrencyException`。
- 範例 (`ModelBaseEntity.cs` 中添加):
    ```csharp
    [Timestamp]
    public byte[] RowVersion { get; set; }
    ```
- 在 Service 層處理 `DbUpdateConcurrencyException`，通知用戶資料已被其他用戶修改，並提供重新載入或合併變更的選項。

---

## 10. 軟刪除實作細節

本專案採用軟刪除 (Soft Delete) 機制來處理資料的邏輯刪除，而非物理刪除。這有助於保留歷史資料和方便資料恢復。

- **實作方式**: 所有繼承自 `ModelBaseEntity` 的資料庫實體都包含以下屬性：
    - `bool IsDeleted`: 標示資料是否已被軟刪除 (預設為 `false`)。
    - `long? DeleteTime`: 記錄資料被軟刪除的時間戳。
    - `string? DeleteUserId`: 記錄執行軟刪除操作的用戶 ID。
- **全域查詢過濾器**: 在 `ERPDbContext.cs` 的 `OnModelCreating` 方法中，為所有繼承 `ModelBaseEntity` 的實體配置了全域查詢過濾器 `e => !e.IsDeleted`。這意味著：
    - 在執行任何查詢時（例如 `_context.Ims_Partner.ToList()`），預設只會回傳 `IsDeleted` 為 `false` 的記錄。
    - 開發者無需手動在每個查詢中添加 `Where(e => !e.IsDeleted)` 條件。
- **軟刪除操作**: 當需要「刪除」一筆資料時，應更新其 `IsDeleted` 屬性為 `true`，並設置 `DeleteTime` 和 `DeleteUserId`，然後呼叫 `_context.SaveChangesAsync()`。

**開發注意事項**:
- 如果需要查詢包含已軟刪除的資料（例如：管理員查看所有歷史記錄），可以使用 Entity Framework Core 的 `IgnoreQueryFilters()` 方法。例如：
    ```csharp
    var allPartners = await _context.Ims_Partner.IgnoreQueryFilters().ToListAsync();
    ```
- 確保在執行軟刪除操作時，正確設置 `DeleteTime` 和 `DeleteUserId`，這對於審計和追蹤非常重要。

---
