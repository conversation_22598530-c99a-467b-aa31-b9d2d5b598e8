using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Services.Pms
{
    /// <summary>
    /// 資產攜出作業服務
    /// </summary>
    public class AssetCarryOutService : IAssetCarryOutService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public AssetCarryOutService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得攜出申請列表
        /// </summary>
        /// <param name="status">狀態篩選</param>
        /// <param name="applicantId">申請人篩選</param>
        /// <param name="assetId">財產篩選</param>
        /// <returns>攜出申請列表</returns>
        public async Task<List<AssetCarryOutDTO>> GetCarryOutListAsync(string? status = null, string? applicantId = null, Guid? assetId = null)
        {
            try
            {
                var query = _context.Set<AssetCarryOut>()
                    .Where(co => co.DeleteTime == null || co.DeleteTime == 0)
                    .Include(co => co.Asset)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(co => co.Status == status);
                }

                if (!string.IsNullOrEmpty(applicantId))
                {
                    query = query.Where(co => co.ApplicantId == applicantId);
                }

                if (assetId.HasValue)
                {
                    query = query.Where(co => co.AssetId == assetId.Value);
                }

                var carryOuts = await query
                    .OrderByDescending(co => co.ApplicationDate)
                    .ToListAsync();

                var result = new List<AssetCarryOutDTO>();
                var statusNames = AssetCarryOutStatus.GetStatusNames();

                foreach (var carryOut in carryOuts)
                {
                    var dto = _mapper.Map<AssetCarryOutDTO>(carryOut);
                    dto.AssetNo = carryOut.Asset?.AssetNo ?? "";
                    dto.AssetName = carryOut.Asset?.AssetName ?? "";
                    dto.StatusName = statusNames.GetValueOrDefault(carryOut.Status, carryOut.Status);

                    // 設定申請人和審核人名稱
                    dto.ApplicantName = await GetUserNameAsync(carryOut.ApplicantId);
                    if (!string.IsNullOrEmpty(carryOut.ApproverId))
                    {
                        dto.ApproverName = await GetUserNameAsync(carryOut.ApproverId);
                    }

                    result.Add(dto);
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得攜出申請列表時發生錯誤: {ex.Message}");
                return new List<AssetCarryOutDTO>();
            }
        }

        /// <summary>
        /// 取得攜出申請詳細資料
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <returns>攜出申請詳細資料</returns>
        public async Task<AssetCarryOutDTO?> GetCarryOutDetailAsync(string carryOutNo)
        {
            try
            {
                var carryOut = await _context.Set<AssetCarryOut>()
                    .Include(co => co.Asset)
                    .FirstOrDefaultAsync(co => co.CarryOutNo == carryOutNo && (co.DeleteTime == null || co.DeleteTime == 0));

                if (carryOut == null)
                {
                    return null;
                }

                var dto = _mapper.Map<AssetCarryOutDTO>(carryOut);
                dto.AssetNo = carryOut.Asset?.AssetNo ?? "";
                dto.AssetName = carryOut.Asset?.AssetName ?? "";

                var statusNames = AssetCarryOutStatus.GetStatusNames();
                dto.StatusName = statusNames.GetValueOrDefault(carryOut.Status, carryOut.Status);

                dto.ApplicantName = await GetUserNameAsync(carryOut.ApplicantId);
                if (!string.IsNullOrEmpty(carryOut.ApproverId))
                {
                    dto.ApproverName = await GetUserNameAsync(carryOut.ApproverId);
                }

                return dto;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得攜出申請詳細資料時發生錯誤: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 新增攜出申請
        /// </summary>
        /// <param name="carryOutDto">攜出申請資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> CreateCarryOutApplicationAsync(AssetCarryOutDTO carryOutDto)
        {
            try
            {
                // 驗證財產是否存在
                var asset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetId == carryOutDto.AssetId && (a.DeleteTime == null || a.DeleteTime == 0));

                if (asset == null)
                {
                    return (false, "指定的財產不存在");
                }

                // 檢查該財產是否已有未歸還的攜出記錄
                var existingCarryOut = await _context.Set<AssetCarryOut>()
                    .Where(co => co.AssetId == carryOutDto.AssetId
                              && (co.Status == AssetCarryOutStatus.APPROVED || co.Status == AssetCarryOutStatus.CARRIED_OUT)
                              && (co.DeleteTime == null || co.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingCarryOut != null)
                {
                    return (false, "該財產目前有未歸還的攜出記錄，無法再次申請攜出");
                }

                // 產生攜出申請單號
                var carryOutNo = await GenerateCarryOutNoAsync();

                var carryOut = _mapper.Map<AssetCarryOut>(carryOutDto);
                carryOut.CarryOutNo = carryOutNo;
                carryOut.Status = AssetCarryOutStatus.PENDING;
                carryOut.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.CreateUserId = carryOutDto.CreateUserId;

                await _context.Set<AssetCarryOut>().AddAsync(carryOut);
                await _context.SaveChangesAsync();

                return (true, $"攜出申請建立成功，申請單號：{carryOutNo}");
            }
            catch (Exception ex)
            {
                return (false, $"建立攜出申請失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 修改攜出申請
        /// </summary>
        /// <param name="carryOutDto">攜出申請資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> UpdateCarryOutApplicationAsync(AssetCarryOutDTO carryOutDto)
        {
            try
            {
                var carryOut = await _context.Set<AssetCarryOut>()
                    .FirstOrDefaultAsync(co => co.CarryOutId == carryOutDto.CarryOutId && (co.DeleteTime == null || co.DeleteTime == 0));

                if (carryOut == null)
                {
                    return (false, "找不到指定的攜出申請");
                }

                // 只有待審核狀態才能修改
                if (carryOut.Status != AssetCarryOutStatus.PENDING)
                {
                    return (false, "只有待審核狀態的申請才能修改");
                }

                // 更新資料
                carryOut.PlannedCarryOutDate = carryOutDto.PlannedCarryOutDate;
                carryOut.PlannedReturnDate = carryOutDto.PlannedReturnDate;
                carryOut.Purpose = carryOutDto.Purpose;
                carryOut.Destination = carryOutDto.Destination;
                carryOut.Notes = carryOutDto.Notes;
                carryOut.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.UpdateUserId = carryOutDto.UpdateUserId;

                await _context.SaveChangesAsync();

                return (true, "攜出申請修改成功");
            }
            catch (Exception ex)
            {
                return (false, $"修改攜出申請失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 刪除攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="userId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> DeleteCarryOutApplicationAsync(string carryOutNo, string userId)
        {
            try
            {
                var carryOut = await _context.Set<AssetCarryOut>()
                    .FirstOrDefaultAsync(co => co.CarryOutNo == carryOutNo && (co.DeleteTime == null || co.DeleteTime == 0));

                if (carryOut == null)
                {
                    return (false, "找不到指定的攜出申請");
                }

                // 只有待審核或已駁回狀態才能刪除
                if (carryOut.Status != AssetCarryOutStatus.PENDING && carryOut.Status != AssetCarryOutStatus.REJECTED)
                {
                    return (false, "只有待審核或已駁回狀態的申請才能刪除");
                }

                carryOut.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.DeleteUserId = userId;
                carryOut.IsDeleted = true;

                await _context.SaveChangesAsync();

                return (true, "攜出申請刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除攜出申請失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 審核攜出申請
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="isApproved">是否核准</param>
        /// <param name="approverId">審核人員</param>
        /// <param name="comment">審核意見</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> ApproveCarryOutApplicationAsync(string carryOutNo, bool isApproved, string approverId, string? comment = null)
        {
            try
            {
                var carryOut = await _context.Set<AssetCarryOut>()
                    .FirstOrDefaultAsync(co => co.CarryOutNo == carryOutNo && (co.DeleteTime == null || co.DeleteTime == 0));

                if (carryOut == null)
                {
                    return (false, "找不到指定的攜出申請");
                }

                if (carryOut.Status != AssetCarryOutStatus.PENDING)
                {
                    return (false, "只有待審核狀態的申請才能進行審核");
                }

                carryOut.Status = isApproved ? AssetCarryOutStatus.APPROVED : AssetCarryOutStatus.REJECTED;
                carryOut.ApproverId = approverId;
                carryOut.ApprovalDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.ApprovalComment = comment;
                carryOut.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.UpdateUserId = approverId;

                await _context.SaveChangesAsync();

                string statusText = isApproved ? "核准" : "駁回";
                return (true, $"攜出申請{statusText}成功");
            }
            catch (Exception ex)
            {
                return (false, $"審核攜出申請失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 登記攜出
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="actualCarryOutDate">實際攜出日期</param>
        /// <param name="operatorId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> RegisterCarryOutAsync(string carryOutNo, long actualCarryOutDate, string operatorId)
        {
            try
            {
                var carryOut = await _context.Set<AssetCarryOut>()
                    .FirstOrDefaultAsync(co => co.CarryOutNo == carryOutNo && (co.DeleteTime == null || co.DeleteTime == 0));

                if (carryOut == null)
                {
                    return (false, "找不到指定的攜出申請");
                }

                if (carryOut.Status != AssetCarryOutStatus.APPROVED)
                {
                    return (false, "只有已核准的申請才能登記攜出");
                }

                carryOut.Status = AssetCarryOutStatus.CARRIED_OUT;
                carryOut.ActualCarryOutDate = actualCarryOutDate;
                carryOut.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "攜出登記成功");
            }
            catch (Exception ex)
            {
                return (false, $"登記攜出失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 登記歸還
        /// </summary>
        /// <param name="carryOutNo">攜出單號</param>
        /// <param name="actualReturnDate">實際歸還日期</param>
        /// <param name="operatorId">操作人員</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> RegisterReturnAsync(string carryOutNo, long actualReturnDate, string operatorId)
        {
            try
            {
                var carryOut = await _context.Set<AssetCarryOut>()
                    .FirstOrDefaultAsync(co => co.CarryOutNo == carryOutNo && (co.DeleteTime == null || co.DeleteTime == 0));

                if (carryOut == null)
                {
                    return (false, "找不到指定的攜出申請");
                }

                if (carryOut.Status != AssetCarryOutStatus.CARRIED_OUT)
                {
                    return (false, "只有已攜出的資產才能登記歸還");
                }

                carryOut.Status = AssetCarryOutStatus.RETURNED;
                carryOut.ActualReturnDate = actualReturnDate;
                carryOut.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                carryOut.UpdateUserId = operatorId;

                await _context.SaveChangesAsync();

                return (true, "歸還登記成功");
            }
            catch (Exception ex)
            {
                return (false, $"登記歸還失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 批次處理攜出申請
        /// </summary>
        /// <param name="batchDto">批次處理資料</param>
        /// <returns>結果(成功/失敗, 訊息)</returns>
        public async Task<(bool success, string message)> BatchProcessAsync(AssetCarryOutBatchDTO batchDto)
        {
            try
            {
                var carryOuts = await _context.Set<AssetCarryOut>()
                    .Where(co => batchDto.CarryOutIds.Contains(co.CarryOutId) && (co.DeleteTime == null || co.DeleteTime == 0))
                    .ToListAsync();

                if (carryOuts.Count != batchDto.CarryOutIds.Count)
                {
                    return (false, "部分攜出申請不存在");
                }

                int successCount = 0;
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                foreach (var carryOut in carryOuts)
                {
                    switch (batchDto.Action.ToUpper())
                    {
                        case "APPROVE":
                            if (carryOut.Status == AssetCarryOutStatus.PENDING)
                            {
                                carryOut.Status = AssetCarryOutStatus.APPROVED;
                                carryOut.ApproverId = batchDto.OperatorId;
                                carryOut.ApprovalDate = currentTime;
                                carryOut.ApprovalComment = batchDto.ApprovalComment;
                                successCount++;
                            }
                            break;

                        case "REJECT":
                            if (carryOut.Status == AssetCarryOutStatus.PENDING)
                            {
                                carryOut.Status = AssetCarryOutStatus.REJECTED;
                                carryOut.ApproverId = batchDto.OperatorId;
                                carryOut.ApprovalDate = currentTime;
                                carryOut.ApprovalComment = batchDto.ApprovalComment;
                                successCount++;
                            }
                            break;

                        case "CARRY_OUT":
                            if (carryOut.Status == AssetCarryOutStatus.APPROVED)
                            {
                                carryOut.Status = AssetCarryOutStatus.CARRIED_OUT;
                                carryOut.ActualCarryOutDate = currentTime;
                                successCount++;
                            }
                            break;

                        case "RETURN":
                            if (carryOut.Status == AssetCarryOutStatus.CARRIED_OUT)
                            {
                                carryOut.Status = AssetCarryOutStatus.RETURNED;
                                carryOut.ActualReturnDate = currentTime;
                                successCount++;
                            }
                            break;
                    }

                    carryOut.UpdateTime = currentTime;
                    carryOut.UpdateUserId = batchDto.OperatorId;
                }

                await _context.SaveChangesAsync();

                return (true, $"批次處理完成，成功處理 {successCount} 筆記錄");
            }
            catch (Exception ex)
            {
                return (false, $"批次處理失敗：{ex.Message}");
            }
        }

        /// <summary>
        /// 取得攜出統計資料
        /// </summary>
        /// <param name="userId">使用者ID (可選，用於個人統計)</param>
        /// <returns>統計資料</returns>
        public async Task<AssetCarryOutStatisticsDTO> GetStatisticsAsync(string? userId = null)
        {
            try
            {
                var query = _context.Set<AssetCarryOut>()
                    .Where(co => co.DeleteTime == null || co.DeleteTime == 0);

                if (!string.IsNullOrEmpty(userId))
                {
                    query = query.Where(co => co.ApplicantId == userId);
                }

                var allCarryOuts = await query.ToListAsync();

                var statistics = new AssetCarryOutStatisticsDTO
                {
                    TotalApplications = allCarryOuts.Count,
                    PendingApplications = allCarryOuts.Count(co => co.Status == AssetCarryOutStatus.PENDING),
                    ApprovedApplications = allCarryOuts.Count(co => co.Status == AssetCarryOutStatus.APPROVED),
                    RejectedApplications = allCarryOuts.Count(co => co.Status == AssetCarryOutStatus.REJECTED),
                    CarriedOutAssets = allCarryOuts.Count(co => co.Status == AssetCarryOutStatus.CARRIED_OUT),
                    ReturnedAssets = allCarryOuts.Count(co => co.Status == AssetCarryOutStatus.RETURNED)
                };

                // 檢查逾期未還
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var overdueCarryOuts = allCarryOuts
                    .Where(co => co.Status == AssetCarryOutStatus.CARRIED_OUT && co.PlannedReturnDate < currentTime)
                    .ToList();

                statistics.OverdueAssets = overdueCarryOuts.Count;

                // 最近申請
                var recentCarryOuts = await query
                    .Include(co => co.Asset)
                    .OrderByDescending(co => co.ApplicationDate)
                    .Take(10)
                    .ToListAsync();

                statistics.RecentApplications = await ConvertToDTO(recentCarryOuts);

                // 逾期清單
                var overdueCarryOutsWithAsset = await _context.Set<AssetCarryOut>()
                    .Include(co => co.Asset)
                    .Where(co => overdueCarryOuts.Select(o => o.CarryOutId).Contains(co.CarryOutId))
                    .ToListAsync();

                statistics.OverdueList = await ConvertToDTO(overdueCarryOutsWithAsset);

                return statistics;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得攜出統計資料時發生錯誤: {ex.Message}");
                return new AssetCarryOutStatisticsDTO();
            }
        }

        /// <summary>
        /// 產生攜出申請單號
        /// </summary>
        /// <returns>攜出申請單號</returns>
        public async Task<string> GenerateCarryOutNoAsync()
        {
            try
            {
                var today = DateTime.Now;
                var prefix = $"CO{today:yyyyMMdd}";

                var lastCarryOut = await _context.Set<AssetCarryOut>()
                    .Where(co => co.CarryOutNo.StartsWith(prefix))
                    .OrderByDescending(co => co.CarryOutNo)
                    .FirstOrDefaultAsync();

                int sequence = 1;
                if (lastCarryOut != null && lastCarryOut.CarryOutNo.Length > prefix.Length)
                {
                    var lastSequence = lastCarryOut.CarryOutNo.Substring(prefix.Length);
                    if (int.TryParse(lastSequence, out int lastSeq))
                    {
                        sequence = lastSeq + 1;
                    }
                }

                return $"{prefix}{sequence:D4}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"產生攜出申請單號時發生錯誤: {ex.Message}");
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                return $"CO{DateTime.Now:yyyyMMdd}{timestamp % 10000:D4}";
            }
        }

        /// <summary>
        /// 檢查逾期未還的資產
        /// </summary>
        /// <returns>逾期未還的攜出申請列表</returns>
        public async Task<List<AssetCarryOutDTO>> CheckOverdueAssetsAsync()
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                var overdueCarryOuts = await _context.Set<AssetCarryOut>()
                    .Include(co => co.Asset)
                    .Where(co => co.Status == AssetCarryOutStatus.CARRIED_OUT
                              && co.PlannedReturnDate < currentTime
                              && (co.DeleteTime == null || co.DeleteTime == 0))
                    .ToListAsync();

                return await ConvertToDTO(overdueCarryOuts);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"檢查逾期未還資產時發生錯誤: {ex.Message}");
                return new List<AssetCarryOutDTO>();
            }
        }

        /// <summary>
        /// 轉換為DTO
        /// </summary>
        /// <param name="carryOuts">攜出實體列表</param>
        /// <returns>DTO列表</returns>
        private async Task<List<AssetCarryOutDTO>> ConvertToDTO(List<AssetCarryOut> carryOuts)
        {
            var result = new List<AssetCarryOutDTO>();
            var statusNames = AssetCarryOutStatus.GetStatusNames();

            foreach (var carryOut in carryOuts)
            {
                var dto = _mapper.Map<AssetCarryOutDTO>(carryOut);
                dto.AssetNo = carryOut.Asset?.AssetNo ?? "";
                dto.AssetName = carryOut.Asset?.AssetName ?? "";
                dto.StatusName = statusNames.GetValueOrDefault(carryOut.Status, carryOut.Status);
                dto.ApplicantName = await GetUserNameAsync(carryOut.ApplicantId);

                if (!string.IsNullOrEmpty(carryOut.ApproverId))
                {
                    dto.ApproverName = await GetUserNameAsync(carryOut.ApproverId);
                }

                result.Add(dto);
            }

            return result;
        }

        /// <summary>
        /// 取得使用者名稱
        /// </summary>
        /// <param name="userId">使用者ID</param>
        /// <returns>使用者名稱</returns>
        private async Task<string> GetUserNameAsync(string userId)
        {
            try
            {
                // 這裡需要根據實際的使用者系統來實現
                var user = await _context.Set<Users>()
                    .FirstOrDefaultAsync(u => u.UserId == userId);

                return user?.Name ?? userId;
            }
            catch
            {
                return userId;
            }
        }
    }
}