using AutoMapper;
using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Mappings
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // 財產主檔(Asset) 映射
            CreateMap<Asset, AssetDTO>()
                .ForMember(dest => dest.AssetId, opt => opt.MapFrom(src => src.AssetId))
                .ReverseMap();

            // 折舊紀錄(DepreciationForm) 映射
            CreateMap<DepreciationForm, DepreciationFormDTO>()
                .ForMember(dest => dest.DepreciationFormId, opt => opt.MapFrom(src => src.DepreciationFormId.ToString()))
                .ForMember(dest => dest.DepreciationId, opt => opt.MapFrom(src => src.DepreciationId.ToString()))
                .ReverseMap()
                .ForMember(dest => dest.DepreciationFormId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.DepreciationFormId) ? Guid.NewGuid() : Guid.Parse(src.DepreciationFormId)))
                .ForMember(dest => dest.DepreciationId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.DepreciationId) ? Guid.Empty : Guid.Parse(src.DepreciationId)));

            // 折舊紀錄明細(Depreciation) 映射
            CreateMap<DepreciationFormDetail, DepreciationFormDetailDTO>()
                .ForMember(dest => dest.DepreciationId, opt => opt.MapFrom(src => src.DepreciationId.ToString()))
                .ReverseMap()
                .ForMember(dest => dest.DepreciationId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.DepreciationId) ? Guid.NewGuid() : Guid.Parse(src.DepreciationId)));

            // 配件設備(AccessoryEquipment) 映射
            CreateMap<AccessoryEquipment, AccessoryEquipmentDTO>()
                .ForMember(dest => dest.AccessoryEquipmentId, opt => opt.MapFrom(src => src.AccessoryEquipmentId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 保險單位(InsuranceUnit) 映射
            CreateMap<InsuranceUnit, InsuranceUnitDTO>()
                .ForMember(dest => dest.InsuranceUnitId, opt => opt.MapFrom(src => src.InsuranceUnitId))
                .ReverseMap();

            // 攤銷來源(AmortizationSource) 映射
            CreateMap<AmortizationSource, AmortizationSourceDTO>()
                .ForMember(dest => dest.AmortizationSourceId, opt => opt.MapFrom(src => src.AmortizationSourceId))
                .ReverseMap();

            // 財產來源(AssetSource) 映射
            CreateMap<AssetSource, AssetSourceDTO>()
                .ForMember(dest => dest.AssetSourceId, opt => opt.MapFrom(src => src.AssetSourceId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產科目(AssetAccount) 映射
            CreateMap<AssetAccount, AssetAccountDTO>()
                .ForMember(dest => dest.AssetAccountId, opt => opt.MapFrom(src => src.AssetAccountId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產子目(AssetSubAccount) 映射
            CreateMap<AssetSubAccount, AssetSubAccountDTO>()
                .ForMember(dest => dest.AssetSubAccountId, opt => opt.MapFrom(src => src.AssetSubAccountId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產類別(AssetCategory) 映射
            CreateMap<AssetCategory, AssetCategoryDTO>()
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 系統參數(PmsSystemParameter) 映射
            CreateMap<PmsSystemParameter, PmsSystemParameterDTO>()
                .ReverseMap();

            // 存放地點(StorageLocation) 映射
            CreateMap<StorageLocation, StorageLocationDTO>()
                .ForMember(dest => dest.StorageLocationId, opt => opt.MapFrom(src => src.StorageLocationId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產系統使用者身分(PmsUserRole) 映射
            CreateMap<PmsUserRole, PmsUserRoleDTO>()
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產系統使用者身分映射(PmsUserRoleMapping) 映射
            CreateMap<PmsUserRoleMapping, PmsUserRoleMappingDTO>()
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 廠牌型號(Manufacturer) 映射
            CreateMap<Manufacturer, ManufacturerDTO>()
                .ForMember(dest => dest.ManufacturerId, opt => opt.MapFrom(src => src.ManufacturerId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 資產攜出作業(AssetCarryOut) 映射
            CreateMap<AssetCarryOut, AssetCarryOutDTO>()
                .ForMember(dest => dest.CarryOutId, opt => opt.MapFrom(src => src.CarryOutId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產位置變動單(AssetLocationTransfer) 映射
            CreateMap<AssetLocationTransfer, AssetLocationTransferDTO>()
                .ForMember(dest => dest.TransferId, opt => opt.MapFrom(src => src.TransferId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());

            // 財產位置變動單明細(AssetLocationTransferDetail) 映射
            CreateMap<AssetLocationTransferDetail, AssetLocationTransferDetailDTO>()
                .ForMember(dest => dest.DetailId, opt => opt.MapFrom(src => src.DetailId))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
                .ForMember(dest => dest.CreateUserId, opt => opt.Ignore());
        }
    }
}