"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  Col,
  message,
  DatePicker,
  Typography,
  Badge,
  Tag,
  Empty,
  Checkbox,
  Radio,
  Divider,
  Statistic,
  Modal,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PrinterOutlined,
  FileExcelOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined,
  EnvironmentOutlined,
  UserOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import { Grid } from "antd";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import { getAssets, AssetDetail, Asset } from "@/services/pms/assetService";
import { getAssetAccounts } from "@/services/pms/assetAccountService";
import { getAssetCategories } from "@/services/pms/assetCategoryService";
import { getDepartments } from "@/services/common/departmentService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import { getStorageLocations } from "@/services/pms/storageLocationService";
import { getUsers } from "@/services/common/userService";
import { formatTWCurrency } from "@/utils/formatUtils";
import { siteConfig } from "@/config/site";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import ReportHeader, {
  getReportPrintStyles,
} from "@/app/components/common/ReportHeader";

// 擴展 dayjs 以支援 isBetween
dayjs.extend(isBetween);

const { Option } = Select;
const { RangePicker } = DatePicker;
const { useBreakpoint } = Grid;
const { Title, Text } = Typography;
const { TextArea } = Input;

// 盤點狀態列舉
enum InventoryStatus {
  PENDING = "pending", // 待盤點
  FOUND = "found", // 已盤點
  MISSING = "missing", // 遺失
  DAMAGED = "damaged", // 損壞
  RELOCATED = "relocated", // 位置異動
}

// 盤點狀態顯示配置
const INVENTORY_STATUS_CONFIG = {
  [InventoryStatus.PENDING]: {
    label: "待盤點",
    color: "#d9d9d9",
    icon: ExclamationCircleOutlined,
  },
  [InventoryStatus.FOUND]: {
    label: "已盤點",
    color: "#52c41a",
    icon: CheckCircleOutlined,
  },
  [InventoryStatus.MISSING]: {
    label: "遺失",
    color: "#f5222d",
    icon: CloseCircleOutlined,
  },
  [InventoryStatus.DAMAGED]: {
    label: "損壞",
    color: "#fa8c16",
    icon: ExclamationCircleOutlined,
  },
  [InventoryStatus.RELOCATED]: {
    label: "位置異動",
    color: "#1890ff",
    icon: EnvironmentOutlined,
  },
};

// 查詢參數介面
interface InventoryQuery {
  keyword?: string;
  assetAccountId?: string;
  assetStatusId?: string;
  departmentId?: string;
  assetCategoryId?: string;
  storageLocationId?: string;
  acquisitionDateRange?: [string, string];
  inventoryStatus?: InventoryStatus;
  inventoryDateRange?: [string, string];
  inventoryPersonId?: string;
}

// 盤點記錄介面
interface InventoryRecord {
  assetId: string;
  inventoryStatus: InventoryStatus;
  inventoryDate?: string;
  inventoryPersonId?: string;
  inventoryPersonName?: string;
  remarks?: string;
  newLocationId?: string;
  newLocationName?: string;
}

// 盤點統計介面
interface InventoryStatistics {
  totalCount: number;
  pendingCount: number;
  foundCount: number;
  missingCount: number;
  damagedCount: number;
  relocatedCount: number;
  completionRate: number;
}

// 主組件
const AssetInventoryListPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<Asset[]>([]);
  const [filteredData, setFilteredData] = useState<Asset[]>([]);
  const [inventoryRecords, setInventoryRecords] = useState<
    Map<string, InventoryRecord>
  >(new Map());
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<InventoryStatistics>({
    totalCount: 0,
    pendingCount: 0,
    foundCount: 0,
    missingCount: 0,
    damagedCount: 0,
    relocatedCount: 0,
    completionRate: 0,
  });

  // 表單和選項
  const [searchForm] = Form.useForm();
  const [assetAccounts, setAssetAccounts] = useState<any[]>([]);
  const [assetCategories, setAssetCategories] = useState<any[]>([]);
  const [assetStatuses, setAssetStatuses] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [storageLocations, setStorageLocations] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);

  // 列印相關
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrintMode, setIsPrintMode] = useState(false);

  // 盤點操作相關
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);
  const [batchInventoryVisible, setBatchInventoryVisible] = useState(false);
  const [batchForm] = Form.useForm();

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 載入資產數據
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await getAssets();
      if (result.success && Array.isArray(result.data)) {
        // 從 AssetDetail 中提取 Asset
        const assets = result.data.map((item: AssetDetail) => item.asset);
        setData(assets);
        setFilteredData(assets);
        // 初始化盤點記錄
        initializeInventoryRecords(assets);
      } else {
        message.error("載入資產數據失敗");
        setData([]);
        setFilteredData([]);
      }
    } catch (error) {
      console.error("載入資產數據錯誤:", error);
      message.error("載入資產數據失敗");
      setData([]);
      setFilteredData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化盤點記錄
  const initializeInventoryRecords = (assets: Asset[]) => {
    const records = new Map<string, InventoryRecord>();
    assets.forEach((asset) => {
      records.set(asset.assetId, {
        assetId: asset.assetId,
        inventoryStatus: InventoryStatus.PENDING,
      });
    });
    setInventoryRecords(records);
  };

  // 載入選項數據
  const loadOptions = useCallback(async () => {
    try {
      const [
        accountsResult,
        categoriesResult,
        statusesResult,
        departmentsResult,
        locationsResult,
        usersResult,
      ] = await Promise.all([
        getAssetAccounts(),
        getAssetCategories(),
        getAssetStatuses(),
        getDepartments(),
        getStorageLocations(),
        getUsers(),
      ]);

      if (accountsResult.success) setAssetAccounts(accountsResult.data || []);
      if (categoriesResult.success)
        setAssetCategories(categoriesResult.data || []);
      if (statusesResult.success) setAssetStatuses(statusesResult.data || []);
      if (departmentsResult.success)
        setDepartments(departmentsResult.data || []);
      if (locationsResult.success)
        setStorageLocations(locationsResult.data || []);
      if (usersResult.success) setUsers(usersResult.data || []);
    } catch (error) {
      console.error("載入選項數據錯誤:", error);
      message.error("載入選項數據失敗");
    }
  }, []);

  // 計算盤點統計
  const calculateStatistics = useCallback(() => {
    const stats = {
      totalCount: filteredData.length,
      pendingCount: 0,
      foundCount: 0,
      missingCount: 0,
      damagedCount: 0,
      relocatedCount: 0,
      completionRate: 0,
    };

    filteredData.forEach((asset) => {
      const record = inventoryRecords.get(asset.assetId);
      if (record) {
        switch (record.inventoryStatus) {
          case InventoryStatus.PENDING:
            stats.pendingCount++;
            break;
          case InventoryStatus.FOUND:
            stats.foundCount++;
            break;
          case InventoryStatus.MISSING:
            stats.missingCount++;
            break;
          case InventoryStatus.DAMAGED:
            stats.damagedCount++;
            break;
          case InventoryStatus.RELOCATED:
            stats.relocatedCount++;
            break;
        }
      }
    });

    stats.completionRate =
      stats.totalCount > 0
        ? Math.round(
            ((stats.totalCount - stats.pendingCount) / stats.totalCount) * 100
          )
        : 0;

    setStatistics(stats);
  }, [filteredData, inventoryRecords]);

  // =========================== 事件處理 ===========================

  // 搜尋處理
  const handleSearch = () => {
    const values = searchForm.getFieldsValue();
    let filtered = [...data];

    // 關鍵字搜尋
    if (values.keyword?.trim()) {
      const keyword = values.keyword.trim().toLowerCase();
      filtered = filtered.filter(
        (asset) =>
          (asset.assetNo || "").toLowerCase().includes(keyword) ||
          (asset.assetName || "").toLowerCase().includes(keyword) ||
          (asset.specification || "").toLowerCase().includes(keyword)
      );
    }

    // 其他過濾條件
    if (values.assetAccountId) {
      filtered = filtered.filter(
        (asset) => asset.assetAccountId === values.assetAccountId
      );
    }
    if (values.assetStatusId) {
      filtered = filtered.filter(
        (asset) => asset.assetStatusId === values.assetStatusId
      );
    }
    if (values.departmentId) {
      filtered = filtered.filter(
        (asset) => asset.departmentId === values.departmentId
      );
    }
    if (values.assetCategoryId) {
      filtered = filtered.filter(
        (asset) => asset.assetCategoryId === values.assetCategoryId
      );
    }
    if (values.storageLocationId) {
      filtered = filtered.filter(
        (asset) => asset.storageLocationId === values.storageLocationId
      );
    }

    // 盤點狀態過濾
    if (values.inventoryStatus) {
      filtered = filtered.filter((asset) => {
        const record = inventoryRecords.get(asset.assetId);
        return record?.inventoryStatus === values.inventoryStatus;
      });
    }

    // 取得日期範圍過濾
    if (
      values.acquisitionDateRange &&
      values.acquisitionDateRange.length === 2
    ) {
      const [startDate, endDate] = values.acquisitionDateRange;
      filtered = filtered.filter((asset) => {
        if (!asset.acquisitionDate) return false;
        const assetDate = dayjs(asset.acquisitionDate);
        return assetDate.isBetween(startDate, endDate, "day", "[]");
      });
    }

    // 盤點日期範圍過濾
    if (values.inventoryDateRange && values.inventoryDateRange.length === 2) {
      const [startDate, endDate] = values.inventoryDateRange;
      filtered = filtered.filter((asset) => {
        const record = inventoryRecords.get(asset.assetId);
        if (!record?.inventoryDate) return false;
        const inventoryDate = dayjs(record.inventoryDate);
        return inventoryDate.isBetween(startDate, endDate, "day", "[]");
      });
    }

    setFilteredData(filtered);
    setSelectedAssets([]);
    message.success(`找到 ${filtered.length} 筆符合條件的資料`);
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    setFilteredData(data);
    setSelectedAssets([]);
    message.success("搜尋條件已重置");
  };

  // 列印處理
  const handlePrint = () => {
    if (isPrintMode && filteredData.length > 0) {
      const printStyles = getReportPrintStyles("財產盤點清冊");

      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        message.error("無法開啟列印視窗，請檢查瀏覽器設定");
        return;
      }

      // 生成報表內容
      const reportHeader = `
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="margin: 0; font-size: 24px; font-weight: bold;">${
            siteConfig.copyright
          }</h1>
          <h2 style="margin: 10px 0 0 0; font-size: 20px;">財產盤點清冊</h2>
          <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
            列印日期：${dayjs().format("YYYY年MM月DD日 HH:mm:ss")}
          </p>
        </div>
      `;

      // 生成表格內容
      const tableContent =
        filteredData.length > 0
          ? `
        <table class="inventory-table">
          <thead>
            <tr>
              <th>序號</th>
              <th>財產編號</th>
              <th>財產名稱</th>
              <th>規格型號</th>
              <th>所屬部門</th>
              <th>存放地點</th>
              <th>原始價值</th>
              <th>盤點狀態</th>
              <th>盤點日期</th>
              <th>盤點人員</th>
              <th>備註</th>
            </tr>
          </thead>
          <tbody>
            ${filteredData
              .map((asset, index) => {
                const department = departments.find(
                  (d) => d.departmentId === asset.departmentId
                );
                const location = storageLocations.find(
                  (l) => l.storageLocationId === asset.storageLocationId
                );
                const inventoryRecord = inventoryRecords.get(asset.assetId);
                const status =
                  inventoryRecord?.inventoryStatus || InventoryStatus.PENDING;
                const config = INVENTORY_STATUS_CONFIG[status];

                return `
                <tr>
                  <td>${index + 1}</td>
                  <td>${asset.assetNo || "-"}</td>
                  <td>${asset.assetName || "-"}</td>
                  <td>${asset.specification || "-"}</td>
                  <td>${department?.name || "-"}</td>
                  <td>${location?.name || "-"}</td>
                  <td style="text-align: right;">
                    ${
                      asset.purchaseAmount
                        ? formatTWCurrency(asset.purchaseAmount)
                        : "-"
                    }
                  </td>
                  <td class="status-${status}">${config.label}</td>
                  <td>
                    ${
                      inventoryRecord?.inventoryDate
                        ? dayjs(inventoryRecord.inventoryDate).format(
                            "YYYY/MM/DD"
                          )
                        : "-"
                    }
                  </td>
                  <td>${inventoryRecord?.inventoryPersonName || "-"}</td>
                  <td>${inventoryRecord?.remarks || "-"}</td>
                </tr>
              `;
              })
              .join("")}
          </tbody>
        </table>
      `
          : `
        <div style="text-align: center; padding: 40px; color: #999;">
          查無符合條件的資料
        </div>
      `;

      // 生成統計摘要（放在最後一頁）
      const statisticsSummary = `
        <div style="page-break-before: always; padding-top: 50px;">
          <h3 style="text-align: center; font-size: 18px; margin-bottom: 30px; color: #1890ff;">
            盤點統計摘要
          </h3>
          <div style="display: flex; justify-content: space-around; align-items: center; padding: 20px; border: 2px solid #1890ff; border-radius: 8px; background-color: #f0f8ff;">
            <div style="text-align: center; padding: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #1890ff;">${
                statistics.totalCount
              }</div>
              <div style="font-size: 14px; color: #666; margin-top: 5px;">總數量</div>
            </div>
            <div style="text-align: center; padding: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #d9d9d9;">${
                statistics.pendingCount
              }</div>
              <div style="font-size: 14px; color: #666; margin-top: 5px;">待盤點</div>
            </div>
            <div style="text-align: center; padding: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #52c41a;">${
                statistics.foundCount
              }</div>
              <div style="font-size: 14px; color: #666; margin-top: 5px;">已找到</div>
            </div>
            <div style="text-align: center; padding: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #f5222d;">${
                statistics.missingCount
              }</div>
              <div style="font-size: 14px; color: #666; margin-top: 5px;">遺失</div>
            </div>
            <div style="text-align: center; padding: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: #fa8c16;">${
                statistics.damagedCount
              }</div>
              <div style="font-size: 14px; color: #666; margin-top: 5px;">損壞</div>
            </div>
            <div style="text-align: center; padding: 15px;">
              <div style="font-size: 24px; font-weight: bold; color: ${
                statistics.completionRate >= 80 ? "#52c41a" : "#fa8c16"
              };">${statistics.completionRate}%</div>
              <div style="font-size: 14px; color: #666; margin-top: 5px;">完成率</div>
            </div>
          </div>
        </div>
      `;

      printWindow.document.write(`
        <html>
          <head>
            <title>財產盤點清冊</title>
            <meta charset="utf-8">
            <style>
              ${printStyles}
              body { 
                margin: 0; 
                padding: 20px; 
                font-family: "Microsoft JhengHei", Arial, sans-serif; 
                line-height: 1.4;
              }
              .inventory-table { 
                width: 100%; 
                border-collapse: collapse; 
                margin-top: 20px; 
                font-size: 12px;
              }
              .inventory-table th, .inventory-table td { 
                border: 1px solid #000; 
                padding: 8px; 
                text-align: left; 
                vertical-align: top;
              }
              .inventory-table th { 
                background-color: #f0f8ff; 
                font-weight: bold; 
                text-align: center; 
                font-size: 13px;
              }
              .inventory-table tbody tr:nth-child(even) {
                background-color: #f9f9f9;
              }
              .status-pending { color: #666; font-weight: bold; }
              .status-found { color: #52c41a; font-weight: bold; }
              .status-missing { color: #f5222d; font-weight: bold; }
              .status-damaged { color: #fa8c16; font-weight: bold; }
              .status-relocated { color: #1890ff; font-weight: bold; }
              @media print {
                body { margin: 0; padding: 15px; }
                .inventory-table { 
                  page-break-inside: auto; 
                }
                .inventory-table tr {
                  page-break-inside: avoid;
                }
                .inventory-table thead {
                  display: table-header-group;
                }
                h1, h2, h3 { 
                  page-break-after: avoid; 
                }
              }
            </style>
          </head>
          <body>
            ${tableContent}
            ${statisticsSummary}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // 匯出Excel
  const handleExportExcel = () => {
    const csvData = filteredData.map((asset) => {
      const record = inventoryRecords.get(asset.assetId);
      const department = departments.find(
        (d) => d.departmentId === asset.departmentId
      );
      const location = storageLocations.find(
        (l) => l.storageLocationId === asset.storageLocationId
      );
      const assetAccount = assetAccounts.find(
        (a) => a.assetAccountId === asset.assetAccountId
      );
      const assetCategory = assetCategories.find(
        (c) => c.assetCategoryId === asset.assetCategoryId
      );
      const inventoryPerson = users.find(
        (u) => u.userId === record?.inventoryPersonId
      );

      return {
        財產編號: asset.assetNo || "-",
        財產名稱: asset.assetName || "-",
        規格型號: asset.specification || "-",
        財產分類: assetCategory?.assetCategoryName || "-",
        財產科目: assetAccount?.assetAccountName || "-",
        所屬部門: department?.name || "-",
        存放地點: location?.name || "-",
        取得日期: asset.acquisitionDate
          ? dayjs(asset.acquisitionDate).format("YYYY/MM/DD")
          : "-",
        原始價值: asset.purchaseAmount
          ? formatTWCurrency(asset.purchaseAmount)
          : "-",
        盤點狀態: record
          ? INVENTORY_STATUS_CONFIG[record.inventoryStatus].label
          : "待盤點",
        盤點日期: record?.inventoryDate
          ? dayjs(record.inventoryDate).format("YYYY/MM/DD")
          : "-",
        盤點人員: inventoryPerson?.name || "-",
        備註: record?.remarks || "-",
      };
    });

    const csvContent = convertToCSV(csvData, true);
    const filename = `財產盤點清冊_${dayjs().format("YYYYMMDD_HHmmss")}.csv`;
    downloadCSV(csvContent, filename);
  };

  // CSV轉換函數
  const convertToCSV = (data: any[], includeCompanyHeader: boolean = false) => {
    if (data.length === 0) return "";

    const headers = Object.keys(data[0]);
    let csvContent = "";

    // 添加公司標題
    if (includeCompanyHeader) {
      csvContent += `${siteConfig.copyright}\n`;
      csvContent += `財產盤點清冊\n`;
      csvContent += `列印日期：${dayjs().format("YYYY/MM/DD HH:mm:ss")}\n`;
      csvContent += `\n`;
    }

    // 添加表頭
    csvContent += headers.map((header) => `"${header}"`).join(",") + "\n";

    // 添加數據
    data.forEach((row) => {
      const values = headers.map((header) => {
        const value = row[header];
        return `"${
          value !== null && value !== undefined
            ? String(value).replace(/"/g, '""')
            : ""
        }"`;
      });
      csvContent += values.join(",") + "\n";
    });

    return csvContent;
  };

  // 下載CSV
  const downloadCSV = (csvContent: string, filename: string) => {
    const BOM = "\uFEFF";
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success("匯出成功");
  };

  // 更新盤點狀態
  const updateInventoryStatus = (
    assetId: string,
    status: InventoryStatus,
    data?: Partial<InventoryRecord>
  ) => {
    setInventoryRecords((prev) => {
      const newRecords = new Map(prev);
      const existing = newRecords.get(assetId) || {
        assetId,
        inventoryStatus: InventoryStatus.PENDING,
      };

      newRecords.set(assetId, {
        ...existing,
        inventoryStatus: status,
        inventoryDate: dayjs().format("YYYY-MM-DD"),
        ...data,
      });

      return newRecords;
    });

    // 根據狀態顯示不同的成功訊息
    const statusLabel = INVENTORY_STATUS_CONFIG[status].label;
    message.success(`已標記為「${statusLabel}」`);
  };

  // 批量盤點
  const handleBatchInventory = () => {
    if (selectedAssets.length === 0) {
      message.warning("請先選擇要盤點的資產");
      return;
    }
    setBatchInventoryVisible(true);
  };

  // 執行批量盤點
  const executeBatchInventory = async () => {
    try {
      const values = await batchForm.validateFields();

      selectedAssets.forEach((asset) => {
        updateInventoryStatus(asset.assetId, values.inventoryStatus, {
          inventoryPersonId: values.inventoryPersonId,
          inventoryPersonName: users.find(
            (u) => u.userId === values.inventoryPersonId
          )?.name,
          remarks: values.remarks,
          newLocationId: values.newLocationId,
          newLocationName: storageLocations.find(
            (l) => l.storageLocationId === values.newLocationId
          )?.name,
        });
      });

      setBatchInventoryVisible(false);
      batchForm.resetFields();
      setSelectedAssets([]);
      message.success(`已完成 ${selectedAssets.length} 筆資產的批量盤點`);
    } catch (error) {
      console.error("批量盤點錯誤:", error);
    }
  };

  // 選擇資產
  const handleSelectAsset = (asset: Asset, checked: boolean) => {
    if (checked) {
      setSelectedAssets([...selectedAssets, asset]);
    } else {
      setSelectedAssets(
        selectedAssets.filter((item) => item.assetId !== asset.assetId)
      );
    }
  };

  // 全選/取消全選
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAssets([...filteredData]);
    } else {
      setSelectedAssets([]);
    }
  };

  // =========================== 表格定義 ===========================

  const columns = [
    {
      title: (
        <Checkbox
          checked={
            selectedAssets.length === filteredData.length &&
            filteredData.length > 0
          }
          indeterminate={
            selectedAssets.length > 0 &&
            selectedAssets.length < filteredData.length
          }
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          全選
        </Checkbox>
      ),
      dataIndex: "select",
      key: "select",
      width: 80,
      fixed: "left" as const,
      render: (_: any, record: Asset) => {
        const isSelected = selectedAssets.some(
          (item) => item.assetId === record.assetId
        );
        return (
          <Checkbox
            checked={isSelected}
            onChange={(e) => handleSelectAsset(record, e.target.checked)}
          />
        );
      },
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
      width: 120,
      fixed: "left" as const,
      render: (text: string) => text || "-",
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
      width: 200,
      render: (text: string) => text || "-",
    },
    {
      title: "規格型號",
      dataIndex: "specification",
      key: "specification",
      width: 150,
      render: (text: string) => text || "-",
    },
    {
      title: "所屬部門",
      dataIndex: "departmentId",
      key: "departmentId",
      width: 120,
      render: (departmentId: string) => {
        const department = departments.find(
          (d) => d.departmentId === departmentId
        );
        return department?.name || "-";
      },
    },
    {
      title: "存放地點",
      dataIndex: "storageLocationId",
      key: "storageLocationId",
      width: 120,
      render: (locationId: string) => {
        const location = storageLocations.find(
          (l) => l.storageLocationId === locationId
        );
        return location?.name || "-";
      },
    },
    {
      title: "原始價值",
      dataIndex: "purchaseAmount",
      key: "purchaseAmount",
      width: 120,
      align: "right" as const,
      render: (amount: number) => (amount ? formatTWCurrency(amount) : "-"),
    },
    {
      title: "盤點狀態",
      dataIndex: "inventoryStatus",
      key: "inventoryStatus",
      width: 120,
      render: (_: any, record: Asset) => {
        const inventoryRecord = inventoryRecords.get(record.assetId);
        const status =
          inventoryRecord?.inventoryStatus || InventoryStatus.PENDING;
        const config = INVENTORY_STATUS_CONFIG[status];
        const IconComponent = config.icon;

        return (
          <Tag color={config.color} icon={<IconComponent />}>
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: "盤點日期",
      dataIndex: "inventoryDate",
      key: "inventoryDate",
      width: 120,
      render: (_: any, record: Asset) => {
        const inventoryRecord = inventoryRecords.get(record.assetId);
        return inventoryRecord?.inventoryDate
          ? dayjs(inventoryRecord.inventoryDate).format("YYYY/MM/DD")
          : "-";
      },
    },
    {
      title: "盤點人員",
      dataIndex: "inventoryPerson",
      key: "inventoryPerson",
      width: 100,
      render: (_: any, record: Asset) => {
        const inventoryRecord = inventoryRecords.get(record.assetId);
        return inventoryRecord?.inventoryPersonName || "-";
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 200,
      fixed: "right" as const,
      render: (_: any, record: Asset) => (
        <Space size="small">
          <Button
            size="small"
            type="primary"
            ghost
            icon={<CheckCircleOutlined />}
            onClick={() =>
              updateInventoryStatus(record.assetId, InventoryStatus.FOUND)
            }
          >
            已盤點
          </Button>
          <Button
            size="small"
            danger
            ghost
            icon={<CloseCircleOutlined />}
            onClick={() =>
              updateInventoryStatus(record.assetId, InventoryStatus.MISSING)
            }
          >
            遺失
          </Button>
        </Space>
      ),
    },
  ];

  // 移動端列定義
  const mobileColumns = [
    {
      title: "資產資訊",
      dataIndex: "assetInfo",
      key: "assetInfo",
      render: (_: any, record: Asset) => {
        const department = departments.find(
          (d) => d.departmentId === record.departmentId
        );
        const location = storageLocations.find(
          (l) => l.storageLocationId === record.storageLocationId
        );
        const inventoryRecord = inventoryRecords.get(record.assetId);
        const status =
          inventoryRecord?.inventoryStatus || InventoryStatus.PENDING;
        const config = INVENTORY_STATUS_CONFIG[status];
        const IconComponent = config.icon;

        return (
          <div>
            <div style={{ fontWeight: "bold", marginBottom: "4px" }}>
              {record.assetNo} - {record.assetName}
            </div>
            <div
              style={{ fontSize: "12px", color: "#666", marginBottom: "4px" }}
            >
              {record.specification || "無規格"}
            </div>
            <div style={{ fontSize: "12px", marginBottom: "4px" }}>
              <EnvironmentOutlined /> {department?.name} - {location?.name}
            </div>
            <div style={{ fontSize: "12px", marginBottom: "8px" }}>
              價值:{" "}
              {record.purchaseAmount
                ? formatTWCurrency(record.purchaseAmount)
                : "-"}
            </div>
            <Tag color={config.color} icon={<IconComponent />}>
              {config.label}
            </Tag>
            {inventoryRecord?.inventoryDate && (
              <Tag color="blue" style={{ marginLeft: "4px" }}>
                <CalendarOutlined />{" "}
                {dayjs(inventoryRecord.inventoryDate).format("MM/DD")}
              </Tag>
            )}
            {inventoryRecord?.inventoryPersonName && (
              <Tag color="green" style={{ marginLeft: "4px" }}>
                <UserOutlined /> {inventoryRecord.inventoryPersonName}
              </Tag>
            )}
            <div style={{ marginTop: "8px" }}>
              <Space size="small">
                <Button
                  size="small"
                  type="primary"
                  ghost
                  icon={<CheckCircleOutlined />}
                  onClick={() =>
                    updateInventoryStatus(record.assetId, InventoryStatus.FOUND)
                  }
                >
                  已盤點
                </Button>
                <Button
                  size="small"
                  danger
                  ghost
                  icon={<CloseCircleOutlined />}
                  onClick={() =>
                    updateInventoryStatus(
                      record.assetId,
                      InventoryStatus.MISSING
                    )
                  }
                >
                  遺失
                </Button>
              </Space>
            </div>
          </div>
        );
      },
    },
  ];

  // =========================== 生命週期 ===========================

  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([loadOptions(), loadData()]);
    };

    initializeData();
  }, [loadData, loadOptions]);

  // 當數據或盤點記錄變化時重新計算統計
  useEffect(() => {
    calculateStatistics();
  }, [calculateStatistics]);

  // =========================== 渲染 ===========================

  return (
    <>
      <style jsx global>{`
        .inventory-table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 20px;
          font-size: 12px;
        }
        .inventory-table th,
        .inventory-table td {
          border: 1px solid #d9d9d9;
          padding: 8px;
          text-align: left;
          vertical-align: top;
        }
        .inventory-table th {
          background-color: #f0f8ff;
          font-weight: bold;
          text-align: center;
          font-size: 13px;
        }
        .inventory-table tbody tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        .status-pending {
          color: #666;
          font-weight: bold;
        }
        .status-found {
          color: #52c41a;
          font-weight: bold;
        }
        .status-missing {
          color: #f5222d;
          font-weight: bold;
        }
        .status-damaged {
          color: #fa8c16;
          font-weight: bold;
        }
        .status-relocated {
          color: #1890ff;
          font-weight: bold;
        }
      `}</style>
      <div style={{ padding: "24px" }}>
        <Card title="財產盤點清冊">
          {/* 盤點統計 */}
          <Card title="盤點統計" style={{ marginBottom: "24px" }}>
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={8} md={4}>
                <Statistic
                  title="總數量"
                  value={statistics.totalCount}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col xs={12} sm={8} md={4}>
                <Statistic
                  title="待盤點"
                  value={statistics.pendingCount}
                  valueStyle={{ color: "#d9d9d9" }}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={8} md={4}>
                <Statistic
                  title="已盤點"
                  value={statistics.foundCount}
                  valueStyle={{ color: "#52c41a" }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={8} md={4}>
                <Statistic
                  title="遺失"
                  value={statistics.missingCount}
                  valueStyle={{ color: "#f5222d" }}
                  prefix={<CloseCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={8} md={4}>
                <Statistic
                  title="損壞"
                  value={statistics.damagedCount}
                  valueStyle={{ color: "#fa8c16" }}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={8} md={4}>
                <Statistic
                  title="完成率"
                  value={statistics.completionRate}
                  suffix="%"
                  valueStyle={{
                    color:
                      statistics.completionRate >= 80 ? "#52c41a" : "#fa8c16",
                  }}
                />
              </Col>
            </Row>
          </Card>

          {/* 搜尋條件 */}
          <Card title="搜尋條件" style={{ marginBottom: "24px" }}>
            <Form form={searchForm} layout="vertical">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="keyword" label="關鍵字搜尋">
                    <Input placeholder="財產編號/名稱/規格" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="inventoryStatus" label="盤點狀態">
                    <Select placeholder="請選擇" allowClear>
                      {Object.entries(INVENTORY_STATUS_CONFIG).map(
                        ([status, config]) => (
                          <Option key={status} value={status}>
                            <Tag color={config.color}>{config.label}</Tag>
                          </Option>
                        )
                      )}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="assetAccountId" label="財產科目">
                    <Select placeholder="請選擇" allowClear>
                      {assetAccounts.map((item: any) => (
                        <Option
                          key={item.assetAccountId}
                          value={item.assetAccountId}
                        >
                          {item.assetAccountName}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="departmentId" label="所屬部門">
                    <Select placeholder="請選擇" allowClear>
                      {departments.map((item: any) => (
                        <Option
                          key={item.departmentId}
                          value={item.departmentId}
                        >
                          {item.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="storageLocationId" label="存放地點">
                    <Select placeholder="請選擇" allowClear>
                      {storageLocations.map((item: any) => (
                        <Option
                          key={item.storageLocationId}
                          value={item.storageLocationId}
                        >
                          {item.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Form.Item name="inventoryDateRange" label="盤點日期範圍">
                    <RangePicker style={{ width: "100%" }} />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col>
                  <Space>
                    <Button
                      type="primary"
                      icon={<SearchOutlined />}
                      onClick={handleSearch}
                    >
                      搜尋
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={handleResetSearch}
                    >
                      重置
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </Card>

          {/* 操作區域 */}
          <Card style={{ marginBottom: "24px" }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => setIsPrintMode(!isPrintMode)}
                  >
                    {isPrintMode ? "返回列表" : "預覽報表"}
                  </Button>
                  {isPrintMode && (
                    <Button
                      type="primary"
                      icon={<PrinterOutlined />}
                      onClick={handlePrint}
                    >
                      列印報表
                    </Button>
                  )}
                  <Button
                    icon={<FileExcelOutlined />}
                    onClick={handleExportExcel}
                  >
                    匯出Excel
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleBatchInventory}
                    disabled={selectedAssets.length === 0}
                  >
                    批量盤點 ({selectedAssets.length})
                  </Button>
                </Space>
              </Col>
              <Col>
                <Badge count={selectedAssets.length} showZero>
                  <Button size="small">已選擇</Button>
                </Badge>
              </Col>
            </Row>
          </Card>

          {/* 列表/報表區域 */}
          <div ref={printRef}>
            {isPrintMode ? (
              // 報表模式
              <div style={{ background: "white", padding: "20px" }}>
                <ReportHeader reportTitle="財產盤點清冊" isPrintMode={true} />

                {filteredData.length > 0 ? (
                  <table className="inventory-table">
                    <thead>
                      <tr>
                        <th>序號</th>
                        <th>財產編號</th>
                        <th>財產名稱</th>
                        <th>規格型號</th>
                        <th>所屬部門</th>
                        <th>存放地點</th>
                        <th>原始價值</th>
                        <th>盤點狀態</th>
                        <th>盤點日期</th>
                        <th>盤點人員</th>
                        <th>備註</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredData.map((asset, index) => {
                        const department = departments.find(
                          (d) => d.departmentId === asset.departmentId
                        );
                        const location = storageLocations.find(
                          (l) => l.storageLocationId === asset.storageLocationId
                        );
                        const inventoryRecord = inventoryRecords.get(
                          asset.assetId
                        );
                        const status =
                          inventoryRecord?.inventoryStatus ||
                          InventoryStatus.PENDING;
                        const config = INVENTORY_STATUS_CONFIG[status];

                        return (
                          <tr key={asset.assetId}>
                            <td>{index + 1}</td>
                            <td>{asset.assetNo || "-"}</td>
                            <td>{asset.assetName || "-"}</td>
                            <td>{asset.specification || "-"}</td>
                            <td>{department?.name || "-"}</td>
                            <td>{location?.name || "-"}</td>
                            <td style={{ textAlign: "right" }}>
                              {asset.purchaseAmount
                                ? formatTWCurrency(asset.purchaseAmount)
                                : "-"}
                            </td>
                            <td className={`status-${status}`}>
                              {config.label}
                            </td>
                            <td>
                              {inventoryRecord?.inventoryDate
                                ? dayjs(inventoryRecord.inventoryDate).format(
                                    "YYYY/MM/DD"
                                  )
                                : "-"}
                            </td>
                            <td>
                              {inventoryRecord?.inventoryPersonName || "-"}
                            </td>
                            <td>{inventoryRecord?.remarks || "-"}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                ) : (
                  <Empty
                    description="查無符合條件的資料"
                    style={{ margin: "40px 0" }}
                  />
                )}

                {/* 統計摘要 - 移到報表最後 */}
                <div
                  style={{
                    marginTop: "40px",
                    padding: "20px",
                    border: "2px solid #1890ff",
                    borderRadius: "8px",
                    backgroundColor: "#f0f8ff",
                  }}
                >
                  <h3
                    style={{
                      textAlign: "center",
                      fontSize: "18px",
                      marginBottom: "20px",
                      color: "#1890ff",
                    }}
                  >
                    盤點統計摘要
                  </h3>
                  <Row gutter={[16, 16]} justify="space-around">
                    <Col span={4} style={{ textAlign: "center" }}>
                      <div
                        style={{
                          fontSize: "24px",
                          fontWeight: "bold",
                          color: "#1890ff",
                        }}
                      >
                        {statistics.totalCount}
                      </div>
                      <div style={{ fontSize: "14px", color: "#666" }}>
                        總數量
                      </div>
                    </Col>
                    <Col span={4} style={{ textAlign: "center" }}>
                      <div
                        style={{
                          fontSize: "24px",
                          fontWeight: "bold",
                          color: "#d9d9d9",
                        }}
                      >
                        {statistics.pendingCount}
                      </div>
                      <div style={{ fontSize: "14px", color: "#666" }}>
                        待盤點
                      </div>
                    </Col>
                    <Col span={4} style={{ textAlign: "center" }}>
                      <div
                        style={{
                          fontSize: "24px",
                          fontWeight: "bold",
                          color: "#52c41a",
                        }}
                      >
                        {statistics.foundCount}
                      </div>
                      <div style={{ fontSize: "14px", color: "#666" }}>
                        已找到
                      </div>
                    </Col>
                    <Col span={4} style={{ textAlign: "center" }}>
                      <div
                        style={{
                          fontSize: "24px",
                          fontWeight: "bold",
                          color: "#f5222d",
                        }}
                      >
                        {statistics.missingCount}
                      </div>
                      <div style={{ fontSize: "14px", color: "#666" }}>
                        遺失
                      </div>
                    </Col>
                    <Col span={4} style={{ textAlign: "center" }}>
                      <div
                        style={{
                          fontSize: "24px",
                          fontWeight: "bold",
                          color: "#fa8c16",
                        }}
                      >
                        {statistics.damagedCount}
                      </div>
                      <div style={{ fontSize: "14px", color: "#666" }}>
                        損壞
                      </div>
                    </Col>
                    <Col span={4} style={{ textAlign: "center" }}>
                      <div
                        style={{
                          fontSize: "24px",
                          fontWeight: "bold",
                          color:
                            statistics.completionRate >= 80
                              ? "#52c41a"
                              : "#fa8c16",
                        }}
                      >
                        {statistics.completionRate}%
                      </div>
                      <div style={{ fontSize: "14px", color: "#666" }}>
                        完成率
                      </div>
                    </Col>
                  </Row>
                </div>
              </div>
            ) : (
              // 列表模式
              <Card title={`財產盤點清冊 (${filteredData.length} 筆資料)`}>
                <Table
                  columns={isMobile ? mobileColumns : columns}
                  dataSource={filteredData}
                  rowKey={(record) => record.assetId}
                  loading={loading}
                  scroll={{ x: isMobile ? undefined : 1400 }}
                  locale={{
                    emptyText: (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                          <span style={{ color: "#999" }}>
                            查無符合條件的資料
                            <br />
                            請調整搜尋條件後重新查詢
                          </span>
                        }
                      />
                    ),
                  }}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                    pageSizeOptions: ["10", "20", "50", "100"],
                  }}
                />
              </Card>
            )}
          </div>
        </Card>

        {/* 批量盤點模態框 */}
        <Modal
          title="批量盤點"
          open={batchInventoryVisible}
          onCancel={() => setBatchInventoryVisible(false)}
          onOk={executeBatchInventory}
          okText="確認盤點"
          cancelText="取消"
          width={600}
        >
          <Form form={batchForm} layout="vertical">
            <Form.Item
              name="inventoryStatus"
              label="盤點狀態"
              rules={[{ required: true, message: "請選擇盤點狀態" }]}
            >
              <Radio.Group>
                {Object.entries(INVENTORY_STATUS_CONFIG).map(
                  ([status, config]) => {
                    if (status === InventoryStatus.PENDING) return null;
                    const IconComponent = config.icon;
                    return (
                      <Radio key={status} value={status}>
                        <Tag color={config.color} icon={<IconComponent />}>
                          {config.label}
                        </Tag>
                      </Radio>
                    );
                  }
                )}
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="inventoryPersonId"
              label="盤點人員"
              rules={[{ required: true, message: "請選擇盤點人員" }]}
            >
              <Select placeholder="請選擇盤點人員">
                {users.map((user: any) => (
                  <Option key={user.userId} value={user.userId}>
                    {user.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="newLocationId" label="新存放地點">
              <Select placeholder="如有位置異動請選擇新地點" allowClear>
                {storageLocations.map((location: any) => (
                  <Option
                    key={location.storageLocationId}
                    value={location.storageLocationId}
                  >
                    {location.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="remarks" label="備註">
              <TextArea rows={3} placeholder="請輸入盤點備註" />
            </Form.Item>
          </Form>

          <Divider />
          <div>
            <Text strong>將要盤點的資產 ({selectedAssets.length} 筆)：</Text>
            <div
              style={{ maxHeight: "200px", overflow: "auto", marginTop: "8px" }}
            >
              {selectedAssets.map((asset) => (
                <Tag key={asset.assetId} style={{ margin: "2px" }}>
                  {asset.assetNo} - {asset.assetName}
                </Tag>
              ))}
            </div>
          </div>
        </Modal>
      </div>
    </>
  );
};

export default AssetInventoryListPage;
