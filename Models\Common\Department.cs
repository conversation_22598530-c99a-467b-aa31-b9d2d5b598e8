﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace FAST_ERP_Backend.Models.Common
{
    public class DepartmentDTO : ModelBaseEntityDTO
    {
        public string DepartmentId { get; set; } //部門編號
        public string Name { get; set; } //部門名稱
        public int SortCode { get; set; } //排序號碼
        public string EnterpriseGroupId { get; set; } //部門編號

        public DepartmentDTO()
        {
            DepartmentId = "";
            SortCode = 0;
            Name = "";
            EnterpriseGroupId = "";
            CreateTime = null;
            CreateUserId = "";
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
        }
    }

    public class Department : ModelBaseEntity
    {
        [Key]
        [Comment("部門編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string DepartmentId { get; set; } //部門編號

        [Comment("部門名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } //部門名稱

        [Comment("排序號碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } //排序號碼

        [Comment("公司編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string EnterpriseGroupId { get; set; } // 公司編號

        public Department()
        {
            DepartmentId = "";
            SortCode = 0;
            Name = "";
            EnterpriseGroupId = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}