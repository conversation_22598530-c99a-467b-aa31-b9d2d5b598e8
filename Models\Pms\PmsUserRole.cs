using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Models.Pms
{
    public class PmsUserRole : ModelBaseEntity
    {
        [Key]
        [Comment("財產系統使用者身分編號")]
        public Guid PmsUserRoleId { get; set; }

        [Comment("身分名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string RoleName { get; set; }

        [Comment("說明")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Description { get; set; }

        [Comment("排序編碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; }

        public PmsUserRole()
        {
            PmsUserRoleId = Guid.Empty;
            RoleName = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class PmsUserRoleDTO
    {
        public Guid PmsUserRoleId { get; set; }    // 財產系統使用者身分編號
        public string RoleName { get; set; }         // 身分名稱
        public string Description { get; set; }      // 說明
        public int SortCode { get; set; }           // 排序編碼

        public long? CreateTime { get; set; }       // 新增時間
        public string? CreateUserId { get; set; }   // 新增者編號
        public long? UpdateTime { get; set; }       // 更新時間
        public string? UpdateUserId { get; set; }   // 更新者編號
        public long? DeleteTime { get; set; }       // 刪除時間
        public string? DeleteUserId { get; set; }   // 刪除者編號
        public bool IsDeleted { get; set; }         // 刪除狀態

        public PmsUserRoleDTO()
        {
            PmsUserRoleId = Guid.Empty;
            RoleName = "";
            Description = "";
            SortCode = 0;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}