import React from "react";
import {
  But<PERSON>,
  Space,
  Tag,
  Tooltip,
  Descriptions,
  Card,
  Typography,
} from "antd";
import {
  EditOutlined,
  FileTextOutlined,
  CalculatorOutlined,
} from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import { STATUS_COLORS } from "./interface";
import { Depreciation } from "@/services/pms/depreciationFormDetailService";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { DepreciationSimulationResult } from "./interface";
import { Asset } from "@/services/pms/assetService";
import { Department } from "@/services/common/departmentService";
import { AssetAccount } from "@/services/pms/assetAccountService";
import { AssetSubAccount } from "@/services/pms/assetSubAccountService";
import { formatTWCurrency } from "@/utils/formatUtils";

// 獲取折舊紀錄表格列定義
export const getColumns = (
  handleView: (record: Depreciation) => void,
  handleEdit: (record: Depreciation) => void,
  handleCalculate: (record: Depreciation) => void,
  departments: Department[],
  assetAccounts: AssetAccount[],
  assetSubAccounts: AssetSubAccount[]
): ColumnsType<Depreciation> => {
  return [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "財產名稱",
      dataIndex: "assetNo",
      key: "assetNo",
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="財產名稱">
                  {`${record.assetNo}-${record.assetName}`}
                </Descriptions.Item>
                <Descriptions.Item label="折舊年月">
                  {`${record.depreciationYear}年${record.depreciationMonth}月`}
                </Descriptions.Item>
                <Descriptions.Item label="折舊方法">
                  {record.depreciationMethod}
                </Descriptions.Item>
                <Descriptions.Item label="折舊日期">
                  {DateTimeExtensions.formatDateFromTimestamp(
                    record.depreciationDate
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="耐用年限">
                  {record.serviceLifeRemaining}年
                </Descriptions.Item>
                <Descriptions.Item label="調整">
                  {record.isAdjustment ? "是" : "否"}
                </Descriptions.Item>
                <Descriptions.Item label="折舊率">
                  {(record.depreciationRate * 100).toFixed(2)}%
                </Descriptions.Item>
                <Descriptions.Item label="備註">
                  {record.notes || "-"}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
          styles={{
            root: { maxWidth: "400px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>
            {`${text}-${record.assetName}`}
          </span>
        </Tooltip>
      ),
    },
    {
      title: "折舊年月",
      key: "depreciationYearMonth",
      render: (_, record) => (
        <span>{`${record.depreciationYear}年${record.depreciationMonth}月`}</span>
      ),
    },
    {
      title: "原始金額",
      dataIndex: "originalAmount",
      key: "originalAmount",
      render: (text) => <p>{formatTWCurrency(text)}</p>,
    },
    {
      title: "本次折舊",
      dataIndex: "currentDepreciation",
      key: "currentDepreciation",
      render: (text) => <p>{formatTWCurrency(text)}</p>,
    },
    {
      title: "累計折舊",
      dataIndex: "accumulatedDepreciation",
      key: "accumulatedDepreciation",
      render: (text) => <p>{formatTWCurrency(text)}</p>,
    },
    {
      title: "剩餘價值",
      dataIndex: "endingBookValue",
      key: "endingBookValue",
      render: (text) => <p>{formatTWCurrency(text)}</p>,
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<FileTextOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            icon={<CalculatorOutlined />}
            onClick={() => handleCalculate(record)}
          >
            重算
          </Button>
        </Space>
      ),
    },
  ];
};

// 獲取資產選擇表格列定義
export const getAssetSelectionColumns = (
  handleSelect: (record: Asset) => void
): ColumnsType<Asset> => {
  return [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
    },
    {
      title: "購入金額",
      dataIndex: "purchaseAmount",
      key: "purchaseAmount",
      render: (text) => <Tag color="red">{formatTWCurrency(text)}</Tag>,
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          onClick={() => handleSelect(record)}
        >
          選擇
        </Button>
      ),
    },
  ];
};

// 獲取折舊試算結果表格列定義
export const getSimulationResultColumns = (
  departments: Department[],
  assetAccounts: AssetAccount[],
  assetSubAccounts: AssetSubAccount[]
): ColumnsType<DepreciationSimulationResult> => {
  return [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "財產名稱",
      dataIndex: "assetNo",
      key: "assetNo",
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="財產名稱">
                  {`${record.assetNo}-${record.assetName}`}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
          styles={{
            root: { maxWidth: "400px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>
            {`${text}-${record.assetName}`}
          </span>
        </Tooltip>
      ),
    },
    {
      title: "原始金額",
      dataIndex: "originalAmount",
      key: "originalAmount",
      align: "right",
      render: (text) => <strong>{formatTWCurrency(text)}</strong>,
    },
    {
      title: "本次折舊",
      dataIndex: "currentDepreciation",
      key: "currentDepreciation",
      align: "right",
      render: (text) => <strong>{formatTWCurrency(text)}</strong>,
    },
    {
      title: "累計折舊",
      dataIndex: "accumulatedDepreciation",
      key: "accumulatedDepreciation",
      align: "right",
      render: (text) => <strong>{formatTWCurrency(text)}</strong>,
    },
    {
      title: "剩餘價值",
      dataIndex: "endingBookValue",
      key: "endingBookValue",
      align: "right",
      render: (text) => <strong>{formatTWCurrency(text)}</strong>,
    },
    {
      title: "折舊率",
      dataIndex: "depreciationRate",
      key: "depreciationRate",
      align: "right",
      render: (text) => <span>{(text * 100).toFixed(2)}%</span>,
    },
  ];
};

// 渲染行動裝置列表
export const getMobileColumns = (
  handleView: (record: Depreciation) => void,
  handleEdit: (record: Depreciation) => void,
  handleCalculate: (record: Depreciation) => void
): ColumnsType<Depreciation> => [
  {
    title: "財產資訊",
    dataIndex: "assetNo",
    key: "assetInfo",
    render: (_, record) => (
      <Card size="small" bordered={false} style={{ padding: 0 }}>
        <Typography.Title level={5} style={{ margin: "0 0 8px 0" }}>
          {`${record.assetNo}-${record.assetName}`}
        </Typography.Title>
        <Descriptions size="small" column={1} colon={false} bordered>
          <Descriptions.Item label="折舊年月">
            {`${record.depreciationYear}年${record.depreciationMonth}月`}
          </Descriptions.Item>
          <Descriptions.Item label="折舊方法">
            {record.depreciationMethod || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="原始金額">
            <Typography.Text type="danger">
              {formatTWCurrency(record.originalAmount)}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="本次折舊">
            {formatTWCurrency(record.currentDepreciation)}
          </Descriptions.Item>
          <Descriptions.Item label="累計折舊">
            {formatTWCurrency(record.accumulatedDepreciation)}
          </Descriptions.Item>
          <Descriptions.Item label="剩餘價值">
            <Typography.Text strong>
              {formatTWCurrency(record.endingBookValue)}
            </Typography.Text>
          </Descriptions.Item>
        </Descriptions>
        <Space
          style={{ marginTop: 12, display: "flex", justifyContent: "flex-end" }}
        >
          <Button
            type="link"
            size="small"
            icon={<FileTextOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            size="small"
            icon={<CalculatorOutlined />}
            onClick={() => handleCalculate(record)}
          >
            重算
          </Button>
        </Space>
      </Card>
    ),
  },
];
