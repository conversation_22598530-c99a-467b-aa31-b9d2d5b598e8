using FAST_ERP_Backend.Models.Pas;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface IPromotionService
    {
        /// <summary>
        /// 取得使用者的升遷異動列表
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <returns>升遷異動列表</returns>
        Task<List<PromotionDTO>> GetPromotionListAsync(string userId);

        /// <summary>
        /// 取得升遷異動明細
        /// </summary>
        /// <param name="uid">升遷異動編號</param>
        /// <returns>升遷異動明細</returns>
        Task<PromotionDTO> GetPromotionDetailAsync(string uid);

        /// <summary>
        /// 取得最新升遷資料作為預設值
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <returns>最新升遷資料</returns>
        Task<PromotionDTO> GetLatestPromotionAsync(string userId);

        /// <summary>
        /// 新增升遷異動資料
        /// </summary>
        /// <param name="data">升遷異動資料</param>
        /// <returns>成功狀態和訊息</returns>
        Task<(bool, string)> AddPromotionAsync(PromotionDTO data);

        /// <summary>
        /// 編輯升遷異動資料
        /// </summary>
        /// <param name="data">升遷異動資料</param>
        /// <returns>成功狀態和訊息</returns>
        Task<(bool, string)> EditPromotionAsync(PromotionDTO data);

        /// <summary>
        /// 刪除升遷異動資料
        /// </summary>
        /// <param name="uid">升遷異動編號</param>
        /// <returns>成功狀態和訊息</returns>
        Task<(bool, string)> DeletePromotionAsync(string uid);

        /// <summary>
        /// 根據指定日期取得該時間點生效的升遷資料及部門資訊
        /// </summary>
        /// <param name="userId">使用者編號</param>
        /// <param name="effectiveDate">查詢日期 (格式: yyyy-MM-dd)，如果為空則使用當天日期</param>
        /// <returns>該時間點生效的升遷資料</returns>
        Task<PromotionDTO?> GetPromotionByEffectiveDateAsync(string userId, string effectiveDate = null);
    }
}