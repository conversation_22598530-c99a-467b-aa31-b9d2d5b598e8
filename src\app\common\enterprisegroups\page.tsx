"use client";

/* 
  公司基本資訊
  /app/common/enterprisegroups/page.tsx
*/

import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  DatePicker,
  Card,
  Row,
  Col,
  Button,
  Divider,
  message,
  Modal,
  Space,
  Table,
  AutoComplete,
  Tooltip,
  Tag,
  Popover,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  UserOutlined,
  IdcardOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  EnterpriseGroup,
  createEnterpriseGroup,
  getEnterpriseGroups,
  getEnterpriseGroupDetail,
  updateEnterpriseGroup,
  deleteEnterpriseGroup,
} from "@/services/common/enterpriseGroupService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { useAuth } from "@/contexts/AuthContext";
import dayjs from "dayjs";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { siteConfig } from "@/config/site";
import {
  Department,
  getDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentDetail,
} from "@/services/common/departmentService";
import {
  Division,
  getDivisions,
  createDivision,
  updateDivision,
  deleteDivision,
} from "@/services/common/divisionService";
import GoogleMapsPreview from "@/app/components/common/GoogleMapsPreview";

const EnterpriseGroups: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingId, setEditingId] = useState<string>("");
  const [dataSource, setDataSource] = useState<EnterpriseGroup[]>([]);
  const [filteredData, setFilteredData] = useState<EnterpriseGroup[]>([]);
  const [searchText, setSearchText] = useState("");
  const [initialFormValues, setInitialFormValues] = useState<any>(null);
  const { user } = useAuth();
  const [modal, contextHolder] = Modal.useModal();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [groupToDelete, setGroupToDelete] = useState<EnterpriseGroup | null>(
    null
  );
  const [emailOptions, setEmailOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [departmentModalVisible, setDepartmentModalVisible] = useState(false);
  const [selectedCompany, setSelectedCompany] =
    useState<EnterpriseGroup | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [editingKey, setEditingKey] = useState<string>("");
  const [departmentLoading, setDepartmentLoading] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] =
    useState<Department | null>(null);
  const [deleteDepartmentModalVisible, setDeleteDepartmentModalVisible] =
    useState(false);
  const [deleteDepartmentConfirmText, setDeleteDepartmentConfirmText] =
    useState("");
  const [departmentFormValid, setDepartmentFormValid] = useState(false);
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [divisionModalVisible, setDivisionModalVisible] = useState(false);
  const [selectedDepartment, setSelectedDepartment] =
    useState<Department | null>(null);
  const [divisionForm] = Form.useForm();
  const [divisionEditForm] = Form.useForm();
  const [editingDivisionKey, setEditingDivisionKey] = useState<string>("");
  const [divisionLoading, setDivisionLoading] = useState(false);
  const [divisionToDelete, setDivisionToDelete] = useState<Division | null>(
    null
  );
  const [deleteDivisionModalVisible, setDeleteDivisionModalVisible] =
    useState(false);
  const [deleteDivisionConfirmText, setDeleteDivisionConfirmText] =
    useState("");
  const [divisionFormValid, setDivisionFormValid] = useState(false);

  // 載入公司列表
  const loadData = async () => {
    setTableLoading(true);
    try {
      const response = await getEnterpriseGroups();
      if (response.success && response.data) {
        setDataSource(response.data);
      } else {
        notifyError("載入失敗", response.message || "無法獲取公司列表");
      }
    } catch (error: any) {
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setTableLoading(false);
    }
  };

  // 載入公司列表
  useEffect(() => {
    loadData();
  }, []);

  // 載入所有部門資料
  const loadAllDepartments = async () => {
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        setDepartments(response.data);
      } else {
        notifyError("載入失敗", response.message || "無法獲取部門列表");
      }
    } catch (error) {
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 在組件載入時獲取所有部門資料
  useEffect(() => {
    loadAllDepartments();
  }, []);

  // 載入組別列表
  const loadDivisions = async () => {
    try {
      const response = await getDivisions();
      if (response.success && response.data) {
        setDivisions(response.data);
      } else {
        notifyError("載入失敗", response.message || "無法獲取組別列表");
      }
    } catch (error) {
      console.error("載入組別錯誤:", error);
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 在組件載入時獲取所有組別資料
  useEffect(() => {
    loadDivisions();
  }, []);

  // 監聽 Modal 開啟狀態，設置表單值
  useEffect(() => {
    if (isModalOpen && initialFormValues) {
      form.setFieldsValue(initialFormValues);
    }
  }, [isModalOpen, initialFormValues, form]);

  // 處理搜尋和篩選
  useEffect(() => {
    let filtered = dataSource;

    // 按搜尋文字篩選
    if (searchText) {
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(searchText.toLowerCase()) ||
          item.unifiedNumber.toLowerCase().includes(searchText.toLowerCase()) ||
          item.representative
            ?.toLowerCase()
            .includes(searchText.toLowerCase()) ||
          item.companyPhone?.toLowerCase().includes(searchText.toLowerCase()) ||
          item.address1?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredData(filtered);
  }, [searchText, dataSource]);

  // 顯示新增公司資訊表單
  const showModal = () => {
    setEditingId("");
    setInitialFormValues({
      establishDate: dayjs(),
    });
    setIsModalOpen(true);
  };

  // 顯示編輯公司資訊表單
  const showEditModal = async (items: EnterpriseGroup) => {
    try {
      setEditingId(items.enterpriseGroupsId);

      // 準備初始表單值
      const formValues = {
        name: items.name,
        unifiedNumber: items.unifiedNumber,
        representative: items.representative,
        establishDate: items.establishDate
          ? dayjs(
              DateTimeExtensions.formatDateFromTimestamp(items.establishDate)
            )
          : null,
        accountingPeriod: items.accountingPeriod,
        companyPhone: items.companyPhone,
        phone: items.phone,
        email: items.email,
        mobilePhone: items.mobilePhone,
        fax: items.fax,
        website: items.website,
        address1: items.address1,
        address2: items.address2,
        englishName: items.englishName,
        englishAddress: items.englishAddress,
        sortCode: items.sortCode?.toString(),
      };

      setInitialFormValues(formValues);
      setIsModalOpen(true);

      // 獲取詳細資訊
      const response = await getEnterpriseGroupDetail(items.enterpriseGroupsId);

      if (response.success && response.data) {
        const data = response.data;
        // 更新表單值
        const updatedValues = {
          name: data.name || items.name,
          unifiedNumber: data.unifiedNumber || items.unifiedNumber,
          representative: data.representative || items.representative,
          establishDate: data.establishDate
            ? dayjs(
                DateTimeExtensions.formatDateFromTimestamp(data.establishDate)
              )
            : items.establishDate
            ? dayjs(
                DateTimeExtensions.formatDateFromTimestamp(items.establishDate)
              )
            : null,
          accountingPeriod: data.accountingPeriod || items.accountingPeriod,
          companyPhone: data.companyPhone || items.companyPhone,
          phone: data.phone || items.phone,
          email: data.email || items.email,
          mobilePhone: data.mobilePhone || items.mobilePhone,
          fax: data.fax || items.fax,
          website: data.website || items.website,
          address1: data.address1 || items.address1,
          address2: data.address2 || items.address2,
          englishName: data.englishName || items.englishName,
          englishAddress: data.englishAddress || items.englishAddress,
          sortCode: (data.sortCode || items.sortCode)?.toString(),
        };

        setInitialFormValues(updatedValues);
      }
    } catch (error: any) {
      console.error("載入錯誤:", error);
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 取消表單
  const handleCancel = () => {
    form.resetFields();
    setEditingId("");
    setInitialFormValues(null);
    setIsModalOpen(false);
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    if (!user?.userId) {
      notifyError("操作失敗", "無法取得使用者資訊");
      return;
    }

    setLoading(true);
    try {
      const data: Partial<EnterpriseGroup> = {
        name: values.name,
        unifiedNumber: values.unifiedNumber,
        representative: values.representative,
        establishDate: DateTimeExtensions.toTimestamp(values.establishDate),
        accountingPeriod: values.accountingPeriod || "",
        companyPhone: values.companyPhone || "",
        phone: values.phone || "",
        email: values.email || "",
        mobilePhone: values.mobilePhone || "",
        fax: values.fax || "",
        website: values.website || "",
        address1: values.address1 || "",
        address2: values.address2 || "",
        englishName: values.englishName || "",
        englishAddress: values.englishAddress || "",
        sortCode: parseInt(values.sortCode || "0"),
        updateUserId: user.userId,
      };

      let result;
      if (editingId) {
        // 編輯模式
        result = await updateEnterpriseGroup({
          ...data,
          enterpriseGroupsId: editingId,
          updateUserId: user.userId,
          createUserId: "",
          deleteUserId: "",
        });
      } else {
        // 新增模式
        result = await createEnterpriseGroup({
          ...data,
          enterpriseGroupsId: "",
          createUserId: user.userId,
          deleteUserId: "",
        });
      }

      console.log(result);

      if (result.success) {
        notifySuccess(
          "更新成功",
          editingId ? "公司資訊已更新" : "公司基本資訊已建立"
        );
        handleCancel();
        loadData(); // 重新載入列表
      } else {
        notifyError("更新失敗", result.message || "請稍後再試");
      }
    } catch (error: any) {
      notifyError("更新錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除公司資訊
  const handleDelete = (record: EnterpriseGroup) => {
    setGroupToDelete(record);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!groupToDelete || !user?.userId) return;

    try {
      const result = await deleteEnterpriseGroup({
        enterpriseGroupsId: groupToDelete.enterpriseGroupsId,
        deleteUserId: user.userId,
        createUserId: "",
        updateUserId: "",
        deleteTime: DateTimeExtensions.toTimestamp(new Date()),
      });

      if (result.success) {
        notifySuccess("刪除成功", "公司資訊已刪除");
        loadData();
        setDeleteModalVisible(false);
        setGroupToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", result.message || "請稍後再試");
      }
    } catch (error) {
      console.error("刪除錯誤:", error);
      notifyError("刪除錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 載入部門列表
  const loadDepartments = async () => {
    if (!selectedCompany) return;
    setDepartmentLoading(true);
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        const filteredDepartments = response.data.filter(
          (dept) =>
            dept.enterpriseGroupId === selectedCompany.enterpriseGroupsId
        );
        setDepartments(filteredDepartments);
      } else {
        notifyError("載入失敗", response.message || "無法獲取部門列表");
      }
    } catch (error) {
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setDepartmentLoading(false);
    }
  };

  // 顯示部門管理 Modal
  const showDepartmentModal = async (company: EnterpriseGroup) => {
    setSelectedCompany(company);
    setDepartmentLoading(true);
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        const filteredDepartments = response.data.filter(
          (dept) => dept.enterpriseGroupId === company.enterpriseGroupsId
        );
        setDepartments(filteredDepartments);
      } else {
        notifyError("載入失敗", response.message || "無法獲取部門列表");
      }
    } catch (error) {
      notifyError("載入錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setDepartmentLoading(false);
      setDepartmentModalVisible(true);
    }
  };

  // 部門列表欄位定義
  const isEditing = (record: Department) => record.departmentId === editingKey;

  const edit = (record: Department) => {
    editForm.setFieldsValue({ name: record.name });
    setEditingKey(record.departmentId);
  };

  const save = async (departmentId: string) => {
    try {
      const values = await editForm.validateFields();
      const updatedDepartment = {
        departmentId,
        name: values.name,
        updateUserId: user?.userId || "",
        enterpriseGroupId: selectedCompany?.enterpriseGroupsId || "",
      };

      const result = await updateDepartment(updatedDepartment);
      if (result.success) {
        notifySuccess("更新成功", "部門資訊已更新");
        loadDepartments();
        setEditingKey("");
      } else {
        notifyError("更新失敗", result.message || "請稍後再試");
      }
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo);
    }
  };

  // 檢查部門名稱是否重複
  const checkDepartmentNameExists = (name: string) => {
    return departments.some((dept) => dept.name === name);
  };

  // 監聽部門表單變化
  const handleDepartmentFormChange = async () => {
    try {
      const values = await departmentForm.validateFields(["departmentName"]);
      const nameExists = values.departmentName
        ? checkDepartmentNameExists(values.departmentName)
        : true;
      setDepartmentFormValid(values.departmentName && !nameExists);
    } catch (error) {
      setDepartmentFormValid(false);
    }
  };

  // 修改部門表單提交
  const handleDepartmentSubmit = async (values: any) => {
    if (!selectedCompany || !user?.userId) return;

    // 再次檢查名稱是否重複
    if (checkDepartmentNameExists(values.departmentName)) {
      notifyError("新增失敗", "部門名稱已存在");
      return;
    }

    setDepartmentLoading(true);
    try {
      const departmentData: Partial<Department> = {
        enterpriseGroupId: selectedCompany.enterpriseGroupsId,
        name: values.departmentName,
        sortCode: parseInt(values.sortCode || "0"),
        createUserId: user.userId,
        updateUserId: user.userId,
      };

      const result = await createDepartment(departmentData);

      if (result.success) {
        notifySuccess("新增成功", "部門已建立");
        departmentForm.resetFields();
        setDepartmentFormValid(false);
        loadDepartments();
        // 關閉 Modal
        setDepartmentModalVisible(false);
        setSelectedCompany(null);
      } else {
        notifyError("新增失敗", result.message || "請稍後再試");
      }
    } catch (error) {
      notifyError("新增錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setDepartmentLoading(false);
    }
  };

  // 處理刪除部門
  const handleDeleteDepartment = (record: Department) => {
    setDepartmentToDelete(record);
    setDeleteDepartmentConfirmText("");
    setDeleteDepartmentModalVisible(true);
  };

  // 執行刪除部門
  const executeDeleteDepartment = async () => {
    if (!departmentToDelete || !user?.userId) return;

    try {
      const result = await deleteDepartment({
        departmentId: departmentToDelete.departmentId,
        deleteUserId: user.userId,
        createUserId: "",
        updateUserId: "",
        deleteTime: DateTimeExtensions.toTimestamp(new Date()),
      });

      if (result.success) {
        notifySuccess("刪除成功", "部門已刪除");
        await loadAllDepartments(); // 使用 await 確保數據完全加載
        loadDepartments();
        setDeleteDepartmentModalVisible(false);
        setDepartmentToDelete(null);
        setDeleteDepartmentConfirmText("");
      } else {
        notifyError("刪除失敗", result.message || "請稍後再試");
      }
    } catch (error) {
      console.error("刪除錯誤:", error);
      notifyError("刪除錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 公司列表欄位
  const columns: ColumnsType<EnterpriseGroup> = [
    {
      title: "公司名稱",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "統一編號",
      dataIndex: "unifiedNumber",
      key: "unifiedNumber",
    },
    {
      title: "公司負責人",
      dataIndex: "representative",
      key: "representative",
    },
    {
      title: "公司電話",
      dataIndex: "companyPhone",
      key: "companyPhone",
      render: (text) => (
        <span>
          <PhoneOutlined style={{ marginRight: 5, color: "#1890ff" }} />
          {text}
        </span>
      ),
    },
    {
      title: "公司地址",
      dataIndex: "address1",
      key: "address1",
      render: (text) => (
        <span>
          <Popover
            content={<GoogleMapsPreview address={text} />}
            title="位置預覽"
            trigger="hover"
          >
            <EnvironmentOutlined
              style={{
                color: "#1890ff",
                fontSize: "16px",
                marginTop: "4px",
                flexShrink: 0,
              }}
            />
            {text}
          </Popover>
        </span>
      ),
    },
    {
      title: "成立日期",
      dataIndex: "establishDate",
      key: "establishDate",
      render: (establishDate: number) =>
        DateTimeExtensions.formatDateFromTimestamp(establishDate),
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          >
            編輯
          </Button>
          {/* <Button
            type="link"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button> */}
        </Space>
      ),
    },
  ];

  // 處理電子郵件自動完成
  const handleEmailSearch = (value: string) => {
    if (!value || value.includes("@")) {
      setEmailOptions([]);
      return;
    }
    const options = siteConfig.email.domains.map((domain) => ({
      label: `${value}@${domain}`,
      value: `${value}@${domain}`,
    }));
    setEmailOptions(options);
  };

  // 修改部門表格定義，添加組別展開功能
  const departmentColumns = [
    {
      title: "部門名稱",
      dataIndex: "name",
      key: "name",
      editable: true,
      render: (_: any, record: Department) => {
        const editable = isEditing(record);
        const divisionCount = divisions.filter(
          (div) => div.departmentId === record.departmentId
        ).length;

        return editable ? (
          <Form form={editForm} component={false}>
            <Form.Item
              name="name"
              style={{ margin: 0 }}
              rules={[{ required: true, message: "請輸入部門名稱" }]}
            >
              <Input />
            </Form.Item>
          </Form>
        ) : (
          <Space>
            {record.name}
            <Tag color="blue" style={{ marginLeft: 8 }}>
              {divisionCount} 個組別
            </Tag>
          </Space>
        );
      },
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: Department) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              type="link"
              onClick={() => save(record.departmentId)}
              style={{ marginRight: 8 }}
            >
              儲存
            </Button>
            <Button type="link" onClick={() => setEditingKey("")}>
              取消
            </Button>
          </Space>
        ) : (
          <Space size="middle">
            <Button
              type="link"
              disabled={editingKey !== ""}
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedCompany(
                  record.enterpriseGroupId
                    ? dataSource.find(
                        (company) =>
                          company.enterpriseGroupsId ===
                          record.enterpriseGroupId
                      ) || null
                    : null
                );
                edit(record);
              }}
            >
              編輯
            </Button>
            <Button
              type="link"
              icon={<DeleteOutlined />}
              danger
              onClick={() => handleDeleteDepartment(record)}
            >
              刪除
            </Button>
          </Space>
        );
      },
    },
  ];

  // 修改部門子表格列定義，添加組別展開功能
  const expandedDivisionRender = (record: Department) => {
    const divisionData = divisions.filter((div) => {
      return div.departmentId === record.departmentId;
    });

    const divisionColumns = [
      {
        title: "組別名稱",
        dataIndex: "name",
        key: "name",
        width: "60%",
        render: (_: any, record: Division) => {
          const editable = isEditingDivision(record);
          return editable ? (
            <Form form={divisionEditForm} component={false}>
              <Form.Item
                name="name"
                style={{ margin: 0 }}
                rules={[
                  { required: true, message: "請輸入組別名稱" },
                  {
                    validator: async (_, value) => {
                      if (!value)
                        return Promise.reject(new Error("請輸入組別名稱"));
                      if (value.trim() === "") {
                        return Promise.reject(new Error("組別名稱不能為空白"));
                      }
                      const exists = divisions
                        .filter((div) => div.divisionId !== record.divisionId)
                        .some(
                          (div) =>
                            div.name === value &&
                            div.departmentId === record.departmentId
                        );
                      if (exists) {
                        return Promise.reject(
                          new Error("組別名稱已存在於該部門")
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input placeholder="請輸入組別名稱" />
              </Form.Item>
            </Form>
          ) : (
            <span>{record.name}</span>
          );
        },
      },
      {
        title: "操作",
        key: "action",
        width: "40%",
        render: (_: any, record: Division) => {
          const editable = isEditingDivision(record);
          return editable ? (
            <Space>
              <Button
                type="link"
                onClick={() => saveDivision(record.divisionId)}
                style={{ marginRight: 8 }}
              >
                儲存
              </Button>
              <Button type="link" onClick={() => setEditingDivisionKey("")}>
                取消
              </Button>
            </Space>
          ) : (
            <Space size="middle">
              <Button
                type="link"
                disabled={editingDivisionKey !== ""}
                icon={<EditOutlined />}
                onClick={() => editDivision(record)}
              >
                編輯
              </Button>
              <Button
                type="link"
                icon={<DeleteOutlined />}
                danger
                onClick={() => handleDeleteDivision(record)}
              >
                刪除
              </Button>
            </Space>
          );
        },
      },
    ];

    return (
      <div style={{ margin: "0 48px" }}>
        <div
          style={{
            marginBottom: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedDepartment(record);
              setDivisionModalVisible(true);
            }}
          >
            新增「{record.name}」-組別
          </Button>
        </div>
        <Table
          columns={divisionColumns}
          dataSource={divisionData}
          pagination={false}
          rowKey="divisionId"
          locale={{ emptyText: "尚無組別資料" }}
          size="small"
        />
      </div>
    );
  };

  // 修改部門表格定義，添加組別展開功能
  const expandedRowRender = (record: EnterpriseGroup) => {
    const departmentData = departments.filter(
      (dept) => dept.enterpriseGroupId === record.enterpriseGroupsId
    );

    return (
      <div style={{ padding: "0 48px" }}>
        <div
          style={{
            marginBottom: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedCompany(record);
              setDepartmentModalVisible(true);
            }}
          >
            新增「{record.name}」-部門
          </Button>
        </div>
        <Table
          columns={departmentColumns}
          dataSource={departmentData}
          pagination={false}
          rowKey="departmentId"
          expandable={{
            expandedRowRender: expandedDivisionRender,
            expandRowByClick: true,
          }}
        />
      </div>
    );
  };

  // 檢查組別名稱是否重複
  const checkDivisionNameExists = (name: string, departmentId: string) => {
    return divisions.some(
      (div) => div.name === name && div.departmentId === departmentId
    );
  };

  // 監聽組別表單變化
  const handleDivisionFormChange = async () => {
    try {
      const values = await divisionForm.validateFields(["name"]);
      const nameExists =
        values.name && selectedDepartment
          ? checkDivisionNameExists(
              values.name,
              selectedDepartment.departmentId
            )
          : true;
      setDivisionFormValid(values.name && !nameExists);
    } catch (error) {
      setDivisionFormValid(false);
    }
  };

  // 處理組別編輯
  const isEditingDivision = (record: Division) =>
    record.divisionId === editingDivisionKey;

  const editDivision = (record: Division) => {
    divisionEditForm.setFieldsValue({
      name: record.name,
    });
    setEditingDivisionKey(record.divisionId);
  };

  // 儲存組別編輯
  const saveDivision = async (divisionId: string) => {
    try {
      const values = await divisionEditForm.validateFields();
      const updatedDivision = {
        divisionId,
        name: values.name,
        description: values.description,
        updateUserId: user?.userId || "",
        departmentId: selectedDepartment?.departmentId || "",
        enterpriseGroupId: selectedDepartment?.enterpriseGroupId || "",
      };
      const result = await updateDivision(updatedDivision);
      if (result.success) {
        notifySuccess("更新成功", "組別資訊已更新");
        await loadDivisions(); // 使用 await 確保數據加載完成
        setEditingDivisionKey("");
      } else {
        notifyError("更新失敗", result.message || "請稍後再試");
      }
    } catch (errInfo) {
      console.error("驗證失敗:", errInfo);
    }
  };

  // 處理刪除組別
  const handleDeleteDivision = (record: Division) => {
    setDivisionToDelete(record);
    setDeleteDivisionConfirmText("");
    setDeleteDivisionModalVisible(true);
  };

  // 執行刪除組別
  const executeDeleteDivision = async () => {
    if (!divisionToDelete || !user?.userId) return;

    try {
      const result = await deleteDivision({
        divisionId: divisionToDelete.divisionId,
        deleteUserId: user.userId,
        deleteTime: DateTimeExtensions.toTimestamp(new Date()),
      });

      if (result.success) {
        notifySuccess("刪除成功", "組別已刪除");
        loadDivisions();
        setDeleteDivisionModalVisible(false);
        setDivisionToDelete(null);
        setDeleteDivisionConfirmText("");
      } else {
        notifyError("刪除失敗", result.message || "請稍後再試");
      }
    } catch (error) {
      console.error("刪除錯誤:", error);
      notifyError("刪除錯誤", "發生未預期的錯誤，請稍後再試");
    }
  };

  // 新增組別
  const handleDivisionSubmit = async (values: any) => {
    if (!selectedDepartment || !user?.userId) return;

    if (checkDivisionNameExists(values.name, selectedDepartment.departmentId)) {
      notifyError("新增失敗", "組別名稱已存在於該部門");
      return;
    }

    setDivisionLoading(true);
    try {
      const divisionData: Partial<Division> = {
        departmentId: selectedDepartment.departmentId,
        name: values.name,
        sortCode: parseInt(values.sortCode || "0"),
        createUserId: user.userId,
        updateUserId: user.userId,
        enterpriseGroupId: selectedDepartment.enterpriseGroupId || "",
      };
      const result = await createDivision(divisionData);

      if (result.success) {
        notifySuccess("新增成功", "組別已建立");
        divisionForm.resetFields();
        setDivisionFormValid(false);
        await loadDivisions(); // 使用 await 確保數據加載完成
        setDivisionModalVisible(false);
        setSelectedDepartment(null);
      } else {
        notifyError("新增失敗", result.message || "請稍後再試");
      }
    } catch (error) {
      console.error("新增組別錯誤:", error);
      notifyError("新增錯誤", "發生未預期的錯誤，請稍後再試");
    } finally {
      setDivisionLoading(false);
    }
  };

  return (
    <Card title="公司基本資訊" bordered={false}>
      {contextHolder}
      <Space style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
          新增公司
        </Button>
        <Input
          placeholder="搜尋公司名稱、統編、負責人"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </Space>

      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey="enterpriseGroupsId"
        loading={tableLoading}
        expandable={{
          expandedRowRender,
          expandRowByClick: true,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
        }}
      />

      {/* 添加公司新增/編輯 Modal */}
      <Modal
        title={editingId ? "編輯公司" : "新增公司"}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="公司名稱"
                name="name"
                rules={[{ required: true, message: "請輸入公司名稱" }]}
              >
                <Input placeholder="請輸入公司名稱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="統一編號"
                name="unifiedNumber"
                rules={[{ required: true, message: "請輸入統一編號" }]}
              >
                <Input placeholder="請輸入統一編號" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="公司負責人"
                name="representative"
                rules={[{ required: true, message: "請輸入公司負責人" }]}
              >
                <Input placeholder="請輸入公司負責人" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="成立日期"
                name="establishDate"
                rules={[{ required: true, message: "請選擇成立日期" }]}
              >
                <DatePicker style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="會計年度" name="accountingPeriod">
                <Input placeholder="請輸入會計年度" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="公司電話" name="companyPhone">
                <Input placeholder="請輸入公司電話" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="聯絡電話" name="phone">
                <Input placeholder="請輸入聯絡電話" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="電子郵件" name="email">
                <AutoComplete
                  options={emailOptions}
                  onSearch={handleEmailSearch}
                  placeholder="請輸入電子郵件"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="行動電話" name="mobilePhone">
                <Input placeholder="請輸入行動電話" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="傳真" name="fax">
                <Input placeholder="請輸入傳真" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="公司網站" name="website">
                <Input placeholder="請輸入公司網站" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="排序" name="sortCode">
                <Input type="number" placeholder="請輸入排序" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="公司地址" name="address1">
                <Input placeholder="請輸入公司地址" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="公司地址(其他)" name="address2">
                <Input placeholder="請輸入其他地址" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="英文名稱" name="englishName">
                <Input placeholder="請輸入英文名稱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="英文地址" name="englishAddress">
                <Input placeholder="請輸入英文地址" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space style={{ width: "100%", justifyContent: "end" }}>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingId ? "儲存" : "新增"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setGroupToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled: deleteConfirmText !== (groupToDelete?.name || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{groupToDelete?.name}」</strong>以確認刪除：
          </p>
          <Input
            placeholder="請輸入公司名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>

      {/* 添加部門管理 Modal */}
      <Modal
        title={`${selectedCompany?.name || ""} - 部門管理`}
        open={departmentModalVisible}
        onCancel={() => {
          setDepartmentModalVisible(false);
          setSelectedCompany(null);
          setEditingKey("");
          setDepartmentFormValid(false);
          departmentForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={departmentForm}
          layout="vertical"
          onFinish={handleDepartmentSubmit}
          onValuesChange={handleDepartmentFormChange}
        >
          <Row>
            <Col span={24}>
              <Form.Item
                label="部門名稱"
                name="departmentName"
                validateTrigger={["onChange", "onBlur"]}
                rules={[
                  {
                    validator: async (_, value) => {
                      if (!value) {
                        return Promise.reject(new Error("請輸入部門名稱"));
                      }
                      if (value.trim() === "") {
                        return Promise.reject(new Error("部門名稱不能為空白"));
                      }
                      if (checkDepartmentNameExists(value)) {
                        return Promise.reject(new Error("部門名稱已存在"));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  placeholder="請輸入部門名稱"
                  onChange={() => {
                    setTimeout(handleDepartmentFormChange, 0);
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: "100%", justifyContent: "end" }}>
              <Button onClick={() => setDepartmentModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={departmentLoading}
                disabled={!departmentFormValid}
              >
                新增部門
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加刪除部門確認 Modal */}
      <Modal
        title="確認刪除"
        open={deleteDepartmentModalVisible}
        onCancel={() => {
          setDeleteDepartmentModalVisible(false);
          setDepartmentToDelete(null);
          setDeleteDepartmentConfirmText("");
        }}
        onOk={executeDeleteDepartment}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled:
            deleteDepartmentConfirmText !== (departmentToDelete?.name || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{departmentToDelete?.name}」</strong>以確認刪除：
          </p>
          <Input
            placeholder="請輸入部門名稱"
            value={deleteDepartmentConfirmText}
            onChange={(e) => setDeleteDepartmentConfirmText(e.target.value)}
          />
        </div>
      </Modal>

      {/* 添加組別管理 Modal */}
      <Modal
        title={`${selectedDepartment?.name || ""} - 組別管理`}
        open={divisionModalVisible}
        onCancel={() => {
          setDivisionModalVisible(false);
          setSelectedDepartment(null);
          setEditingDivisionKey("");
          setDivisionFormValid(false);
          divisionForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={divisionForm}
          layout="vertical"
          onFinish={handleDivisionSubmit}
          onValuesChange={handleDivisionFormChange}
        >
          <Row>
            <Col span={24}>
              <Form.Item
                label="組別名稱"
                name="name"
                validateTrigger={["onChange", "onBlur"]}
                rules={[
                  {
                    validator: async (_, value) => {
                      if (!value) {
                        return Promise.reject(new Error("請輸入組別名稱"));
                      }
                      if (value.trim() === "") {
                        return Promise.reject(new Error("組別名稱不能為空白"));
                      }
                      if (
                        selectedDepartment &&
                        checkDivisionNameExists(
                          value,
                          selectedDepartment.departmentId
                        )
                      ) {
                        return Promise.reject(
                          new Error("組別名稱已存在於該部門")
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  placeholder="請輸入組別名稱"
                  onChange={() => {
                    setTimeout(handleDivisionFormChange, 0);
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: "100%", justifyContent: "end" }}>
              <Button onClick={() => setDivisionModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={divisionLoading}
                disabled={!divisionFormValid}
              >
                新增組別
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加刪除組別確認 Modal */}
      <Modal
        title="確認刪除"
        open={deleteDivisionModalVisible}
        onCancel={() => {
          setDeleteDivisionModalVisible(false);
          setDivisionToDelete(null);
          setDeleteDivisionConfirmText("");
        }}
        onOk={executeDeleteDivision}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled:
            deleteDivisionConfirmText !== (divisionToDelete?.name || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{divisionToDelete?.name}」</strong>
            以確認刪除：
          </p>
          <Input
            placeholder="請輸入組別名稱"
            value={deleteDivisionConfirmText}
            onChange={(e) => setDeleteDivisionConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default EnterpriseGroups;
