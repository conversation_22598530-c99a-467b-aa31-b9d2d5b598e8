using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("扶養資料管理")]
    public class DependentController : ControllerBase
    {
        private readonly IDependentService _interface;

        public DependentController(IDependentService DependentService)
        {
            _interface = DependentService;
        }

        [HttpGet]
        [Route("GetAll/{_userId}")]
        [SwaggerOperation(Summary = "取得扶養列表", Description = "取得所有扶養列表")]
        public async Task<IActionResult> GetDependentList(string _userId)
        {
            var result = await _interface.GetDependentListAsync(_userId);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得扶養明細", Description = "依uid取得扶養明細")]
        public async Task<IActionResult> GetDependentDetail(string _uid)
        {
            var result = await _interface.GetDependentDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增扶養資料", Description = "新增扶養資料")]
        public async Task<IActionResult> AddDependent([FromBody] DependentDTO _data)
        {
            var (result, msg) = await _interface.AddDependentAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯扶養資料", Description = "編輯扶養資料")]
        public async Task<IActionResult> EditDependent([FromBody] DependentDTO _data)
        {
            var (result, msg) = await _interface.EditDependentAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除扶養資料", Description = "刪除扶養資料")]
        public async Task<IActionResult> DeleteDependent([FromBody] string _uid)
        {
            var (result, msg) = await _interface.DeleteDependentAsync(_uid);
            return Ok(new { result, msg });
        }

    }
}
