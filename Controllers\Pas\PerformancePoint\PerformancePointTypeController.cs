using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("點數類型管理")]
    public class PerformancePointTypeController : ControllerBase
    {
        private readonly IPerformancePointTypeService _service;

        public PerformancePointTypeController(IPerformancePointTypeService service)
        {
            _service = service;
        }

        [HttpGet]
        [Route("GetByGroup/{groupUid}")]
        [SwaggerOperation(Summary = "根據群組UID取得點數類型列表", Description = "根據點數群組UID取得點數類型資料列表")]
        public async Task<IActionResult> GetByGroup(string groupUid)
        {
            var result = await _service.GetByGroupUidAsync(groupUid);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{uid}")]
        [SwaggerOperation(Summary = "取得點數類型明細", Description = "依UID取得點數類型資料明細")]
        public async Task<IActionResult> GetDetail(string uid)
        {
            var result = await _service.GetDetailAsync(uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增點數類型資料", Description = "新增點數類型資料")]
        public async Task<IActionResult> Add([FromBody] PerformancePointTypeDTO data)
        {
            var (result, msg) = await _service.AddAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯點數類型資料", Description = "編輯點數類型資料")]
        public async Task<IActionResult> Edit([FromBody] PerformancePointTypeDTO data)
        {
            var (result, msg) = await _service.EditAsync(data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除點數類型資料", Description = "刪除點數類型資料")]
        public async Task<IActionResult> Delete([FromBody] string uid)
        {
            var (result, msg) = await _service.DeleteAsync(uid);
            return Ok(new { result, msg });
        }

        [HttpGet]
        [Route("GetGroupTypeCascaderOptions")]
        [SwaggerOperation(Summary = "取得點數群組類型級聯選單", Description = "取得點數群組類型級聯選單")]
        public async Task<IActionResult> GetGroupTypeCascaderOptions()
        {
            var result = await _service.GetGroupWithTypesAsCascaderAsync();
            return Ok(result);
        }
    }
}
