﻿using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace FAST_ERP_Backend.Services.Pms
{
    public class ManufacturerService : IManufacturerService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public ManufacturerService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /// <summary>
        /// 取得製造商資料
        /// </summary>
        /// <param name="_manufacturerId"></param>
        /// <returns></returns>
        public async Task<List<ManufacturerDTO>> GetManufacturerAsync(string _manufacturerId = "")
        {
            try
            {
                var query = _context.Set<Manufacturer>()
                    .Where(m => !m.IsDeleted)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(_manufacturerId))
                {
                    query = query.Where(m => m.ManufacturerId == Guid.Parse(_manufacturerId));
                }

                var manufacturers = await query
                    .OrderBy(m => m.SortCode)
                    .ToListAsync();

                return _mapper.Map<List<ManufacturerDTO>>(manufacturers);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得製造商資料時發生錯誤: {ex.Message}");
                return new List<ManufacturerDTO>();
            }
        }

        /// <summary>
        /// 新增製造商
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddManufacturerAsync(ManufacturerDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = _mapper.Map<Manufacturer>(_data);
                entity.ManufacturerId = _data.ManufacturerId;
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = _data.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯製造商
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditManufacturerAsync(ManufacturerDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Set<Manufacturer>()
                                    .FirstOrDefaultAsync(m => m.ManufacturerId == _data.ManufacturerId && !m.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(_data, entity);
                entity.ManufacturerId = _data.ManufacturerId;
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除製造商
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteManufacturerAsync(ManufacturerDTO _data)
        {
            try
            {
                var entity = await _context.Set<Manufacturer>()
                    .FirstOrDefaultAsync(m => m.ManufacturerId == _data.ManufacturerId && !m.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得製造商詳細資料   
        /// </summary>
        /// <param name="_manufacturerId"></param>
        /// <returns></returns>
        public async Task<ManufacturerDTO> GetManufacturerDetailAsync(string _manufacturerId)
        {
            try
            {
                var entity = await _context.Set<Manufacturer>()
                    .FirstOrDefaultAsync(m => m.ManufacturerId == Guid.Parse(_manufacturerId) && !m.IsDeleted);

                if (entity == null)
                {
                    return null;
                }

                return _mapper.Map<ManufacturerDTO>(entity);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得製造商詳細資料時發生錯誤: {ex.Message}");
                return null;
            }
        }
    }
}

