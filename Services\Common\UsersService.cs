﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.AccessControl;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Server.Tools;
using Microsoft.EntityFrameworkCore;
using log4net;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace FAST_ERP_Backend.Services.Common
{
    public class UsersService : IUsersService
    {
        private readonly EncryptionHelper _encryptionHelper;
        private readonly TokenHandler _tokenHandler;
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ILoggerService _logger;

        public UsersService(EncryptionHelper encryptionHelper, TokenHandler tokenHandler, Baseform baseform, ERPDbContext context, ILoggerService logger)
        {
            _encryptionHelper = encryptionHelper;
            _tokenHandler = tokenHandler;
            _baseform = baseform;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 取得使用者列表
        /// </summary>
        /// <param name="_userId"></param>
        /// <returns></returns>
        public async Task<List<UsersDTO>> GetUsersAsync(string _userId)
        {
            IQueryable<Users> query = _context.Common_Users;

            // id條件.
            if (!string.IsNullOrEmpty(_userId))
            {
                query = query.Where(e => e.UserId == _userId);
            }

            var result = await query
                .OrderBy(e => e.SortCode)
                .Select(t => new UsersDTO
                {
                    UserId = t.UserId,
                    Account = t.Account,
                    Name = t.Name,
                    EnterpriseGroupId = t.EnterpriseGroupId,
                    RolesId = t.RolesId,
                    PositionId = t.PositionId,
                    EMail = t.EMail,
                    PermanentAddress = t.PermanentAddress,
                    MailingAddress = t.MailingAddress,
                    TelNo = t.TelNo,
                    Phone = t.Phone,
                    AltPhone = t.AltPhone,
                    SortCode = t.SortCode,
                })
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// 新增使用者資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddUsersAsync(UsersDTO _data, String tokenUid = "")
        {
            string uid = Guid.NewGuid().ToString().Trim();
            try
            {
                var newUser = new Users
                {
                    UserId = uid,
                    Account = _data.Account,
                    Password = _encryptionHelper.EncryptString(_data.Password), //加密.
                    Name = _data.Name,
                    EnterpriseGroupId = _data.EnterpriseGroupId,
                    RolesId = _data.RolesId,
                    PositionId = _data.PositionId,
                    EMail = _data.EMail,
                    PermanentAddress = _data.PermanentAddress,
                    MailingAddress = _data.MailingAddress,
                    TelNo = _data.TelNo,
                    Phone = _data.Phone,
                    AltPhone = _data.AltPhone,
                    SortCode = _data.SortCode,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = tokenUid,
                };

                await _context.Database.BeginTransactionAsync();

                await _context.Common_Users.AddAsync(newUser);
                await _context.SaveChangesAsync();

                await _context.Database.CommitTransactionAsync();

                return (true, "新增使用者資料成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增使用者資料失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯使用者資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditUsersAsync(UsersDTO _data, String tokenUid = "")
        {
            var existingUser = await _context.Common_Users
                .FirstOrDefaultAsync(e => e.UserId == _data.UserId);

            if (existingUser != null)
            {
                try
                {
                    await _context.Database.BeginTransactionAsync();

                    // 更新資料
                    UsersHelper.UpdateUserFields(existingUser, _data);

                    existingUser.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingUser.UpdateUserId = tokenUid;

                    await _context.SaveChangesAsync();
                    await _context.Database.CommitTransactionAsync();

                    return (true, "編輯使用者資料成功");
                }
                catch (Exception ex)
                {
                    await _context.Database.RollbackTransactionAsync();
                    return (false, $"編輯使用者資料失敗: {ex.Message}");
                }
            }
            else
            {
                return (false, "使用者不存在");
            }
        }

        /// <summary>
        /// 刪除使用者資料
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteUsersAsync(UsersDTO _data, String tokenUid = "")
        {
            var existingUser = await _context.Common_Users
                .FirstOrDefaultAsync(e => e.UserId == _data.UserId);

            if (existingUser != null)
            {
                try
                {
                    await _context.Database.BeginTransactionAsync();

                    existingUser.DeleteUserId = _data.DeleteUserId;
                    existingUser.DeleteTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                    existingUser.IsDeleted = true;

                    await _context.SaveChangesAsync();
                    await _context.Database.CommitTransactionAsync();

                    return (true, "刪除使用者成功");
                }
                catch (Exception ex)
                {
                    await _context.Database.RollbackTransactionAsync();
                    return (false, $"刪除使用者失敗: {ex.Message}");
                }
            }
            else
            {
                return (false, "使用者不存在");
            }
        }

        /// <summary>
        /// 變更使用者密碼
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> ChangeUsersPasswordAsync(ChangeUsersPasswordDTO _data, String tokenUid = "")
        {
            var existingUser = await _context.Common_Users
                .FirstOrDefaultAsync(e => e.UserId == _data.UserId);

            if (existingUser == null)
            {
                return (false, "使用者不存在");
            }

            // 檢查舊密碼是否正確
            if (existingUser.Password != _encryptionHelper.EncryptString(_data.oldPassword))
            {
                return (false, "舊密碼不正確");
            }

            // 密碼格式檢核
            var result = await CheckPassword(_data.newPassword);
            if (!result.IsValid)
            {
                return (false, result.Message);
            }

            try
            {
                await _context.Database.BeginTransactionAsync();

                existingUser.Password = _encryptionHelper.EncryptString(_data.newPassword); // 加密新密碼
                existingUser.UpdateTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                existingUser.UpdateUserId = tokenUid;

                await _context.SaveChangesAsync();
                await _context.Database.CommitTransactionAsync();

                return (true, "變更密碼成功");
            }
            catch (Exception ex)
            {
                await _context.Database.RollbackTransactionAsync();
                return (false, $"變更密碼失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 登入資訊驗證
        /// </summary>
        /// <param name="_logindata"></param>
        /// <returns></returns>
        public async Task<(bool, string, string)> VerifyLoginAsync(UsersDTO _logindata)
        {
            bool is_result = false;
            string str_result = "密碼錯誤";
            string str_token = string.Empty;

            string str_loginAcc = _logindata.Account.Trim();
            string str_loginPw = _logindata.Password.Trim();

            //找到該登入帳號之user資料.
            var existingUser = await _context.Common_Users
                .FirstOrDefaultAsync(e => e.Account == str_loginAcc);

            if (existingUser != null)
            {
                if (existingUser.Password == _encryptionHelper.EncryptString(str_loginPw))
                {
                    var existingUserDto = new UsersDTO
                    {
                        UserId = existingUser.UserId,
                        Account = existingUser.Account,
                    };
                    //登入成功取得Token回傳.
                    str_token = _tokenHandler.GenerateJwtToken(existingUserDto);

                    //Token有值則取得成功.
                    if (str_token != "")
                    {
                        is_result = true;
                        str_result = "登入成功";
                    }
                }
            }

            return (is_result, str_result, str_token);
        }

        /// <summary>
        /// 使用者密碼格式檢核
        /// </summary>
        /// <param name="_password"></param>
        /// <returns></returns>
        public Task<(bool IsValid, string Message)> CheckPassword(string _password)
        {
            if (string.IsNullOrEmpty(_password))
            {
                return Task.FromResult((false, "密碼不能為空"));
            }

            int int_pwminlength = 8;
            int int_pwmaxlength = 20;

            if (_password.Length < int_pwminlength || _password.Length > int_pwmaxlength)
            {
                return Task.FromResult((false, $"密碼長度需介於{int_pwminlength}至{int_pwmaxlength}位數"));
            }

            // 檢查是否全部為半形字元（ASCII 字元）
            if (_password.Any(c => c > 127)) // 半形字元的 ASCII 編碼範圍為 0-127.
            {
                return Task.FromResult((false, "密碼必須為半形字符"));
            }

            // 使用正則表達式檢查是否包含英文字母、數字和符號.
            string pattern = @"^[a-zA-Z0-9!@#$%^&*(),.?""':{}|<>_\-+=;\\/\[\]`~]+$";
            if (!Regex.IsMatch(_password, pattern))
            {
                return Task.FromResult((false, "密碼只能包含英文字母、數字和常見符號"));
            }

            return Task.FromResult((true, "密碼格式正確"));
        }
    }
}