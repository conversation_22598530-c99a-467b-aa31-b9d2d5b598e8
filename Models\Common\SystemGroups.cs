﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace FAST_ERP_Backend.Models.Common
{
    public class SystemGroupsDTO
    {
        public string SystemGroupId { get; set; } //群組編號
        public string SystemCode { get; set; } //群組代號
        public string Name { get; set; } //群組名稱
        public long OptionTime { get; set; } //群組設定
        public string Remark { get; set; } //備註
        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態
        public SystemGroupsDTO()
        {
            SystemGroupId = "";
            SystemCode = "";
            Name = "";
            OptionTime = 0;
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
    public class SystemGroups : ModelBaseEntity
    {
        [Key]
        [Comment("系統群組編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemGroupId { get; set; }

        [Comment("系統代碼(如Common、Pms等)")]
        [Column(TypeName = "nvarchar(50)")]
        public string SystemCode { get; set; }

        [Comment("系統名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; }

        [Comment("系統設定")]
        [Column(TypeName = "nvarchar(200)")]
        public string Option { get; set; }

        [Comment("系統備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Remark { get; set; }

        public SystemGroups()
        {
            SystemGroupId = "";
            SystemCode = "";
            Name = "";
            Option = "";
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class OptionJson
    {
        public string Guid { get; set; }
        public long OptionTime { get; set; }
        public OptionJson()
        {
            Guid = "";
            OptionTime = 0;
        }
    }

    /// <summary>
    /// 系統群組節點
    /// </summary>
    public class SystemGroupsNode
    {
        public string SystemGroupId { get; set; } //群組編號
        public string SystemCode { get; set; } //群組代號
        public string Name { get; set; } //群組名稱
        public List<SystemMenuNode> Menus { get; set; }// 群組內的選單

        public SystemGroupsNode()
        {
            SystemGroupId = "";
            SystemCode = "";
            Name = "";
            Menus = new List<SystemMenuNode>();
        }
    }
}
