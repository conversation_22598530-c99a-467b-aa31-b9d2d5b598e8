# 合約管理系統 - 完整分析文件
*從產品經理、系統架構師、開發人員、操作者四重視角分析*

---

## 📋 產品經理視角 - 系統商業價值分析

### 產品定位與市場需求
本系統是專為台灣 B2B 企業設計的合約生命週期管理解決方案，解決企業在廠商管理、合約追蹤、帳務處理等關鍵業務痛點。

### 核心商業價值
- **效率提升**：自動化合約續約和帳單產生，減少 70% 人工作業時間
- **風險控制**：智能提醒系統確保合約到期前及時處理，避免業務中斷
- **成本管控**：清楚的成本利潤分析，幫助企業優化供應商選擇
- **合規管理**：完整的合約和帳務記錄，滿足稽核和法規要求

### 目標用戶群體
- **主要用戶**：中小型企業採購部門、財務部門
- **次要用戶**：大型企業分支機構、初創公司業務部門
- **用戶畫像**：需要管理 10-500 個廠商合約的企業管理人員

### 產品功能價值排序
1. **高價值功能**：智能續約提醒、自動帳單產生、合約狀態追蹤
2. **中價值功能**：廠商資料管理、帳務記錄查詢、統計報表
3. **基礎功能**：資料新增編輯、搜尋篩選、權限管理

### 市場競爭優勢
- **本地化優勢**：完全支援台灣商業習慣和格式要求
- **易用性**：直觀的中文介面，降低學習成本
- **自動化程度**：智能業務邏輯減少人工干預
- **成本效益**：相比大型 ERP 系統更具價格競爭力

---

## 🏗️ 系統架構師視角 - 技術架構設計

### 整體架構設計原則
- **前後端分離**：React + Express 架構，支援獨立開發和部署
- **微服務導向**：模組化設計，便於功能擴展和維護
- **資料驅動**：以 PostgreSQL 為核心的關聯式資料設計
- **API 優先**：RESTful API 設計，支援多端存取

### 技術棧選擇理由
```
前端技術棧：
├── React 18 + TypeScript     # 現代化前端框架，類型安全
├── TanStack Query            # 伺服器狀態管理，自動快取
├── Radix UI + Tailwind CSS   # 無障礙組件庫 + 實用CSS框架
└── Wouter                    # 輕量級路由解決方案

後端技術棧：
├── Express.js + TypeScript   # 成熟的 Node.js 框架
├── Drizzle ORM              # 類型安全的資料庫操作
├── Zod                      # 資料驗證和類型推斷
└── PostgreSQL               # 可靠的關聯式資料庫

部署架構：
├── Neon Serverless          # 無伺服器資料庫託管
├── Vite                     # 高效能建置工具
└── Replit Deployments       # 一鍵部署平台
```

### 資料庫架構設計
```sql
-- 核心實體關係
vendors (1) ←→ (N) contracts (1) ←→ (N) billing_records
                   ↓
            contracts (1) ←→ (N) reminders

-- 關鍵設計決策
1. 外鍵約束確保資料完整性
2. 時間戳記追蹤資料變更
3. 軟刪除支援資料恢復
4. 索引優化查詢效能
```

### 安全性架構
- **資料驗證**：前後端雙重驗證，Zod schema 統一定義
- **API 安全**：輸入清理、參數驗證、錯誤處理
- **資料庫安全**：ORM 防注入、連接池管理
- **未來擴展**：預留認證授權接口

### 效能架構考量
- **前端優化**：組件懶加載、虛擬滾動、智能快取
- **後端優化**：資料庫查詢優化、API 響應壓縮
- **網路優化**：HTTP/2、資源壓縮、CDN 就緒
- **擴展性**：水平擴展設計、狀態無關服務

---

## 💻 開發人員視角 - 實作細節分析

### 開發環境與工具鏈
```bash
# 開發環境設定
Node.js 18+                    # 執行環境
TypeScript 5+                  # 類型系統
ESLint + Prettier             # 程式碼品質
Vite                          # 開發伺服器
Drizzle Kit                   # 資料庫遷移工具
```

### 程式碼組織結構
```
專案根目錄/
├── client/src/               # 前端程式碼
│   ├── components/           # 可重用組件
│   │   ├── modals/          # 對話框組件
│   │   ├── tables/          # 表格組件
│   │   └── ui/              # 基礎UI組件
│   ├── pages/               # 頁面組件
│   ├── hooks/               # 自訂 React Hooks
│   └── lib/                 # 工具函式
├── server/                  # 後端程式碼
│   ├── routes.ts            # API 路由定義
│   ├── storage.ts           # 資料存取層
│   └── db.ts                # 資料庫連接
├── shared/                  # 共享程式碼
│   └── schema.ts            # 資料模型定義
└── 設定檔案群組
```

### 關鍵技術實作

#### 1. 智能合約續約系統
```typescript
// 核心邏輯：基於原合約結束日期計算新期間
const renewContract = async (originalContract) => {
  // 計算開始日期（原合約結束日次日）
  const startDate = new Date(originalContract.endDate);
  startDate.setDate(startDate.getDate() + 1);
  
  // 根據計費周期計算結束日期
  const endDate = calculateEndDate(startDate, originalContract.billingCycle);
  
  // 智能命名（第2期、第3期...）
  const newName = generateSmartContractName(originalContract);
  
  return createContract({ ...originalContract, startDate, endDate, contractName: newName });
};
```

#### 2. 計費周期自動計算
```typescript
// 根據合約計費周期自動計算帳單金額
const generateBilling = (contract) => {
  const contractValue = parseFloat(contract.contractValue);
  
  const billingAmount = {
    monthly: contractValue / 12,    // 月繳
    quarterly: contractValue / 4,   // 季繳  
    annual: contractValue          // 年繳
  }[contract.billingCycle];
  
  const dueDate = calculateDueDate(contract.billingCycle);
  
  return { amount: billingAmount, dueDate };
};
```

#### 3. 提醒系統架構
```typescript
// 自動建立續約提醒
const createAutoReminder = async (contract) => {
  const reminderDate = new Date(contract.endDate);
  reminderDate.setDate(reminderDate.getDate() - 30); // 提前30天
  
  return createReminder({
    contractId: contract.id,
    title: `合約續約提醒：${contract.contractName}`,
    reminderDate: reminderDate.toISOString().split('T')[0],
    reminderType: "renewal",
    priority: "high"
  });
};
```

### 資料流程設計
```
使用者操作 → React組件 → TanStack Query → API請求
                ↓
Express路由 → Zod驗證 → Storage層 → Drizzle ORM → PostgreSQL
                ↓
資料回傳 → JSON響應 → Query快取 → UI更新
```

### 錯誤處理機制
- **前端錯誤**：Toast 通知、表單驗證、網路錯誤重試
- **後端錯誤**：統一錯誤格式、日誌記錄、優雅降級
- **資料庫錯誤**：事務回滾、約束檢查、連接管理

### 測試策略
- **單元測試**：核心業務邏輯、工具函式
- **整合測試**：API 端點、資料庫操作
- **端到端測試**：關鍵使用者流程
- **效能測試**：API 響應時間、資料庫查詢效率

---

## 👤 操作者視角 - 實際使用指南

### 系統存取與登入
**目前狀態**：系統採用簡化模式，無需登入即可使用
**未來規劃**：將整合企業認證系統，支援角色權限管理

### 核心業務操作流程

#### 📊 儀表板 - 系統總覽
**功能**：展示系統關鍵指標和即時狀態
- **廠商總數**：目前管理的廠商數量
- **活躍合約**：正在執行的合約統計
- **逾期提醒**：需要立即處理的事項
- **月營收**：當月財務概況

**操作技巧**：
- 每日開始工作時先查看儀表板
- 關注紅色數字提醒，優先處理逾期項目
- 定期檢查統計趨勢，了解業務狀況

#### 🏢 廠商管理 - 供應商關係維護
**主要功能**：建立和維護供應商基本資料

**新增廠商**：
1. 點擊「新增廠商」按鈕
2. **必填資訊**：公司名稱
3. **建議填寫**：統一編號（報稅用）、聯絡人、電話、email、地址
4. 儲存後可立即建立合約

**編輯廠商**：
- 點擊列表中的編輯按鈕
- 可修改所有資訊，系統自動記錄變更時間
- 修改後會影響該廠商所有相關合約的顯示

**刪除廠商**：
- 點擊刪除按鈕會出現確認對話框
- ⚠️ **注意**：刪除廠商會同時刪除所有相關合約和帳務記錄
- 建議先確認該廠商沒有進行中的重要合約

**搜尋功能**：
- 支援公司名稱模糊搜尋
- 可根據統一編號精確查找
- 搜尋結果即時顯示

#### 📋 合約管理 - 核心業務功能
**主要功能**：管理合約全生命週期

**建立新合約**：
1. 點擊「新增合約」
2. **基本資訊**：
   - 選擇廠商（下拉選單）
   - 合約名稱（建議包含服務內容和年份）
   - 合約描述（詳細說明服務內容）
3. **財務資訊**：
   - 合約總金額（年度總值）
   - 計費周期（月繳/季繳/年繳）
4. **時間設定**：
   - 開始日期
   - 結束日期
5. **公司資訊**：
   - 公司抬頭（發票用）
   - 統一編號
6. **合約條款**：詳細條款內容

**智能續約功能**：
- 點擊合約列表中的續約按鈕
- 系統自動：
  - 計算新合約期間（從原合約結束日次日開始）
  - 生成智能合約名稱（第2期、第3期...）
  - 保留原合約所有條件
  - 建立續約提醒（到期前30天）

**產生帳單功能**：
- 點擊「產生帳單」按鈕
- 系統根據計費周期自動計算：
  - **月繳**：年約金額 ÷ 12
  - **季繳**：年約金額 ÷ 4  
  - **年繳**：完整年約金額
- 自動設定到期日和建立帳務記錄

**合約刪除**：
- 點擊刪除按鈕出現確認對話框
- ⚠️ **重要**：會同時刪除相關帳務記錄和提醒
- 建議在刪除前先確認財務結算完成

#### 💰 帳務管理 - 財務追蹤
**主要功能**：管理所有財務記錄和付款狀態

**檢視帳務記錄**：
- 列表顯示所有帳單，包含：
  - 發票號碼（系統自動生成）
  - 關聯合約和廠商
  - 帳單金額和成本
  - 到期日和付款狀態
  - 聯絡資訊和地址

**新增帳務記錄**：
1. 點擊「新增帳務記錄」
2. 選擇關聯合約
3. 填寫帳單詳細資訊
4. 系統自動產生發票號碼

**編輯帳務記錄**：
- 可修改金額、成本、到期日等資訊
- 更新聯絡資訊和地址
- 新增備註說明

**付款確認**：
- 點擊綠色確認按鈕標記為「已付款」
- 已付款的帳單會顯示綠色狀態徽章
- 系統自動記錄付款確認時間

**帳務記錄刪除**：
- 點擊紅色刪除按鈕
- 出現確認對話框，確認後永久刪除
- ⚠️ **注意**：刪除後無法恢復，建議謹慎操作

#### ⏰ 提醒管理 - 智能通知系統
**主要功能**：管理所有提醒事項和重要通知

**自動提醒機制**：
- 建立合約時自動產生續約提醒（到期前30天）
- 系統智能分析提醒狀態：
  - **綠色「正常」**：距離到期超過3天
  - **黃色「即將到期」**：3天內到期
  - **紅色「已逾期」**：已超過提醒日期
  - **藍色「已完成」**：已標記完成的提醒

**篩選功能**（新功能）：
- **狀態篩選**：全部/啟用中/已完成/已暫停
- **優先級篩選**：全部/高優先級/中優先級/低優先級
- **日期範圍**：設定開始和結束日期查詢
- **預設顯示**：近1個月的提醒（最實用的時間範圍）
- **清除篩選**：一鍵重設所有篩選條件

**手動提醒管理**：
1. **新增提醒**：
   - 選擇關聯合約
   - 設定提醒標題和描述
   - 選擇提醒日期
   - 設定優先級（高/中/低）
   - 選擇提醒類型

2. **編輯提醒**：
   - 修改提醒內容和時間
   - 調整優先級
   - 更新描述資訊

3. **完成提醒**：
   - 點擊綠色勾選按鈕標記完成
   - 完成的提醒顯示藍色徽章
   - 系統記錄完成時間

4. **暫停提醒**：
   - 點擊時鐘按鈕暫時停用
   - 適用於需要延後處理的事項

5. **刪除提醒**（新功能）：
   - 點擊紅色刪除按鈕
   - 確認對話框防止誤刪
   - 永久刪除，無法恢復

### 日常操作建議

#### 每日工作流程
1. **上午檢查**：
   - 開啟儀表板查看整體狀況
   - 檢查即將到期的提醒
   - 處理逾期的帳務記錄

2. **例行維護**：
   - 確認新收到的發票並更新付款狀態
   - 檢查即將到期的合約準備續約
   - 更新廠商聯絡資訊

3. **週期性任務**：
   - 每週檢查合約狀態
   - 每月檢視帳務統計
   - 季度檢討廠商表現

#### 高效操作技巧
1. **善用搜尋功能**：
   - 廠商名稱、合約名稱都支援模糊搜尋
   - 統一編號可進行精確查找
   - 發票號碼快速定位帳務記錄

2. **活用篩選功能**：
   - 提醒管理的日期範圍篩選
   - 帳務管理的付款狀態篩選
   - 合約管理的狀態篩選

3. **批量處理**：
   - 一次性確認多筆付款
   - 批量設定提醒完成狀態
   - 統一更新合約狀態

#### 常見問題處理

**Q: 如何處理合約到期？**
A: 
1. 收到續約提醒後，先確認是否需要續約
2. 如需續約，使用智能續約功能
3. 如不續約，將合約狀態改為「已過期」
4. 標記續約提醒為完成

**Q: 帳單金額計算錯誤怎麼辦？**
A:
1. 檢查合約的計費周期設定是否正確
2. 使用編輯功能手動調整帳單金額
3. 如需要，可新增備註說明調整原因

**Q: 廠商資訊變更如何處理？**
A:
1. 直接編輯廠商資料
2. 系統會自動更新所有相關合約的顯示
3. 新的帳單會使用更新後的聯絡資訊

**Q: 如何備份重要資料？**
A:
1. 目前系統資料自動備份至雲端資料庫
2. 建議定期匯出重要合約和廠商清單
3. 重要合約建議額外保存紙本或PDF備份

### 系統維護與優化

#### 效能優化建議
- 定期清理已完成的提醒
- 歸檔過期的合約和帳務記錄
- 保持廠商資料的即時更新

#### 資料品質管理
- 確保廠商統一編號的正確性
- 維護完整的聯絡資訊
- 定期檢查合約金額的合理性

#### 使用者培訓要點
1. **基礎操作**：新增、編輯、刪除的標準流程
2. **智能功能**：續約、帳單產生的自動化操作
3. **篩選搜尋**：快速定位所需資訊的技巧
4. **錯誤處理**：常見問題的解決方案

---

## 🔧 系統技術規格

### 效能指標
- **頁面載入時間**：< 2 秒
- **API 響應時間**：< 500ms
- **資料庫查詢**：< 100ms
- **並發使用者**：支援 100+ 同時在線

### 瀏覽器支援
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 資料容量規劃
- **廠商數量**：無限制
- **合約記錄**：支援萬筆級別
- **帳務記錄**：支援十萬筆級別
- **提醒記錄**：自動清理機制

### 安全性保障
- **資料加密**：傳輸和儲存全程加密
- **備份機制**：每日自動備份
- **存取控制**：未來支援角色權限
- **審計日誌**：完整操作記錄

---

## 📈 未來發展規劃

### 短期優化（1-3個月）
- 新增批量操作功能
- 完善報表統計功能
- 整合 Email 通知系統
- 新增資料匯出功能

### 中期擴展（3-6個月）
- 實作使用者權限管理
- 新增行動端應用
- 整合電子簽核流程
- 開發 API 對外接口

### 長期願景（6-12個月）
- 人工智慧合約分析
- 自動風險評估系統
- 供應商評級機制
- 區塊鏈合約驗證

---

*本文件定期更新，確保與系統實際狀況同步*

## 系統架構

### 整體架構圖
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │────│   後端 (Express) │────│ 資料庫 (PostgreSQL) │
│                 │    │                 │    │                 │
│ - 使用者介面     │    │ - API 路由      │    │ - 資料持久化     │
│ - 狀態管理       │    │ - 業務邏輯      │    │ - 關聯查詢       │
│ - 路由控制       │    │ - 資料驗證      │    │ - 事務處理       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 前端架構 (Client)

#### 技術棧
- **框架**: React 18 + TypeScript
- **狀態管理**: TanStack Query (React Query) v5
- **路由**: Wouter
- **UI 組件**: Radix UI + shadcn/ui
- **樣式**: Tailwind CSS
- **表單處理**: React Hook Form + Zod 驗證
- **建置工具**: Vite

#### 目錄結構
```
client/src/
├── components/
│   ├── layout/          # 版面配置組件
│   │   ├── header.tsx   # 頂部導航欄（含通知功能）
│   │   └── navigation.tsx # 側邊導航選單
│   ├── modals/          # 模態對話框
│   │   ├── add-vendor-modal.tsx
│   │   ├── edit-vendor-modal.tsx
│   │   ├── add-contract-modal.tsx
│   │   ├── edit-contract-modal.tsx
│   │   ├── add-billing-modal.tsx
│   │   ├── edit-billing-modal.tsx
│   │   ├── view-billing-modal.tsx
│   │   ├── add-reminder-modal.tsx
│   │   └── edit-reminder-modal.tsx
│   ├── tables/          # 資料表格組件
│   │   └── data-table.tsx # 通用資料表格
│   └── ui/              # 基礎 UI 組件 (shadcn/ui)
├── pages/               # 頁面組件
│   ├── dashboard.tsx    # 儀表板
│   ├── vendors.tsx      # 廠商管理
│   ├── contracts.tsx    # 合約管理
│   ├── billing.tsx      # 帳務管理
│   ├── reminders.tsx    # 提醒管理
│   └── not-found.tsx    # 404 頁面
├── hooks/               # 自訂 Hooks
│   ├── use-mobile.tsx   # 行動裝置檢測
│   └── use-toast.ts     # 通知提示
├── lib/                 # 工具函式
│   ├── queryClient.ts   # API 請求配置
│   ├── types.ts         # TypeScript 類型定義
│   └── utils.ts         # 通用工具函式
├── App.tsx              # 應用程式主組件
├── main.tsx             # 應用程式入口點
└── index.css            # 全域樣式
```

#### 關鍵組件分析

**1. Header 組件 (header.tsx)**
- 功能：顯示系統標題、通知按鈕、使用者資訊
- 特色：即時通知徽章、彈出式通知面板
- 通知內容：即將到期提醒、逾期付款警告
- 中文化：完整支援中文介面

**2. Navigation 組件 (navigation.tsx)**
- 功能：側邊導航選單，支援多層級結構
- 路由：使用 Wouter 進行客戶端路由
- 響應式：自動適應不同螢幕尺寸
- 圖示：使用 Lucide React 圖示庫

**3. DataTable 組件 (data-table.tsx)**
- 功能：通用資料表格，支援排序、篩選、搜尋
- 特色：可配置欄位、自訂渲染函式
- 效能：虛擬滾動，處理大量資料
- 操作：內建編輯、刪除、查看等操作按鈕

**4. Modal 組件系列**
- 標準化：統一的對話框介面設計
- 表單驗證：使用 React Hook Form + Zod
- 錯誤處理：友善的錯誤訊息顯示
- 響應式：自動適應螢幕尺寸

### 後端架構 (Server)

#### 技術棧
- **框架**: Express.js + TypeScript
- **資料庫**: PostgreSQL with Neon serverless
- **ORM**: Drizzle ORM
- **驗證**: Zod schemas
- **WebSocket**: ws (用於資料庫連接)

#### 目錄結構
```
server/
├── db.ts              # 資料庫連接配置
├── index.ts           # 應用程式入口點
├── routes.ts          # API 路由定義
├── storage.ts         # 資料存取層
└── vite.ts            # Vite 整合配置
```

#### API 路由設計

**廠商管理路由**
- `GET /api/vendors` - 獲取廠商列表
- `GET /api/vendors/:id` - 獲取單一廠商詳情
- `POST /api/vendors` - 建立新廠商
- `PUT /api/vendors/:id` - 更新廠商資訊
- `DELETE /api/vendors/:id` - 刪除廠商
- `GET /api/vendors/search?q=keyword` - 搜尋廠商

**合約管理路由**
- `GET /api/contracts` - 獲取合約列表
- `GET /api/contracts/:id` - 獲取單一合約詳情
- `POST /api/contracts` - 建立新合約
- `PUT /api/contracts/:id` - 更新合約資訊
- `DELETE /api/contracts/:id` - 刪除合約
- `POST /api/contracts/:id/renew` - 合約續約
- `POST /api/contracts/:id/generate-billing` - 產生帳單

**帳務管理路由**
- `GET /api/billing-records` - 獲取帳務記錄
- `GET /api/billing-records/:id` - 獲取單一帳務記錄
- `POST /api/billing-records` - 建立新帳務記錄
- `PUT /api/billing-records/:id` - 更新帳務記錄
- `DELETE /api/billing-records/:id` - 刪除帳務記錄
- `GET /api/billing-records/overdue` - 獲取逾期帳單

**提醒管理路由**
- `GET /api/reminders` - 獲取提醒列表（支援篩選）
- `GET /api/reminders/:id` - 獲取單一提醒詳情
- `POST /api/reminders` - 建立新提醒
- `PUT /api/reminders/:id` - 更新提醒
- `DELETE /api/reminders/:id` - 刪除提醒
- `GET /api/reminders/upcoming` - 獲取即將到期提醒

**儀表板路由**
- `GET /api/dashboard/stats` - 獲取統計資料

### 資料庫設計 (Database)

#### 技術規格
- **資料庫**: PostgreSQL 15+
- **託管**: Neon Serverless
- **ORM**: Drizzle ORM
- **遷移**: Drizzle Kit

#### 資料表結構

**1. vendors (廠商表)**
```sql
CREATE TABLE vendors (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    tax_id VARCHAR(20),
    contact_person VARCHAR(100),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

**2. contracts (合約表)**
```sql
CREATE TABLE contracts (
    id SERIAL PRIMARY KEY,
    vendor_id INTEGER REFERENCES vendors(id) ON DELETE CASCADE,
    contract_name VARCHAR(255) NOT NULL,
    description TEXT,
    contract_value DECIMAL(15,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    company_header VARCHAR(255),
    company_tax_id VARCHAR(20),
    terms TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

**3. billing_records (帳務記錄表)**
```sql
CREATE TABLE billing_records (
    id SERIAL PRIMARY KEY,
    contract_id INTEGER REFERENCES contracts(id) ON DELETE CASCADE,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    cost DECIMAL(15,2),
    billing_date DATE NOT NULL,
    due_date DATE NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'pending',
    billing_company_header VARCHAR(255),
    billing_company_tax_id VARCHAR(20),
    billing_address TEXT,
    billing_contact_person VARCHAR(100),
    billing_contact_phone VARCHAR(50),
    billing_contact_email VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

**4. reminders (提醒表)**
```sql
CREATE TABLE reminders (
    id SERIAL PRIMARY KEY,
    contract_id INTEGER REFERENCES contracts(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    reminder_date DATE NOT NULL,
    reminder_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    status VARCHAR(20) DEFAULT 'active',
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 資料關聯設計
```
vendors (1) ←→ (N) contracts (1) ←→ (N) billing_records
                   ↓
                contracts (1) ←→ (N) reminders
```

#### 索引策略
- vendors: `company_name`, `tax_id`, `status`
- contracts: `vendor_id`, `start_date`, `end_date`, `status`
- billing_records: `contract_id`, `due_date`, `payment_status`
- reminders: `contract_id`, `reminder_date`, `status`

## 業務流程分析

### 1. 廠商管理流程
```
新增廠商 → 填寫基本資訊 → 驗證資料 → 儲存至資料庫 → 返回廠商列表
編輯廠商 → 載入現有資料 → 修改資訊 → 驗證資料 → 更新資料庫
刪除廠商 → 檢查關聯合約 → 確認刪除 → 級聯刪除相關資料
```

### 2. 合約管理流程
```
建立合約 → 選擇廠商 → 填寫合約詳情 → 設定計費周期 → 儲存合約 → 自動建立續約提醒
合約續約 → 載入原合約 → 計算新期間 → 智能命名 → 建立新合約 → 更新舊合約狀態
產生帳單 → 根據計費周期 → 計算帳單金額 → 設定到期日 → 建立帳務記錄
```

### 3. 帳務管理流程
```
檢視帳單 → 載入帳務列表 → 篩選排序 → 檢視詳情
編輯帳單 → 載入帳單資料 → 修改資訊 → 驗證資料 → 更新記錄
付款確認 → 標記為已付款 → 更新付款狀態 → 記錄付款時間
```

### 4. 提醒管理流程
```
自動提醒 → 合約建立時 → 計算到期前30天 → 自動建立續約提醒
手動提醒 → 填寫提醒資訊 → 設定日期和優先級 → 儲存提醒
完成提醒 → 標記完成 → 記錄完成時間 → 更新狀態
```

## 核心功能實現

### 1. 智能合約續約系統

**邏輯實現**
```typescript
// 計算續約期間
const originalEndDate = new Date(originalContract.endDate);
const startDate = new Date(originalEndDate);
startDate.setDate(startDate.getDate() + 1); // 次日開始

// 根據計費周期計算結束日期
switch (originalContract.billingCycle) {
    case "monthly":
        endDate.setMonth(endDate.getMonth() + 1);
        endDate.setDate(endDate.getDate() - 1);
        break;
    case "quarterly":
        endDate.setMonth(endDate.getMonth() + 3);
        endDate.setDate(endDate.getDate() - 1);
        break;
    case "annual":
        endDate.setFullYear(endDate.getFullYear() + 1);
        endDate.setDate(endDate.getDate() - 1);
        break;
}
```

**智能命名系統**
```typescript
// 分析現有合約，產生智能命名
const baseContractName = originalContract.contractName
    .replace(/ \(第\d+期\)$/, '')
    .replace(/ \(續約\)$/, '');

const renewalCount = existingContracts.filter(c => 
    c.contractName.startsWith(baseContractName) && 
    c.vendorId === originalContract.vendorId
).length;

const newContractName = renewalCount > 1 ? 
    `${baseContractName} (第${renewalCount + 1}期)` : 
    `${baseContractName} (第2期)`;
```

### 2. 計費周期實質功能

**自動計算帳單金額**
```typescript
const contractValue = parseFloat(contract.contractValue);
let billingAmount: number;

switch (contract.billingCycle) {
    case "monthly":
        billingAmount = contractValue / 12; // 年約除以12個月
        break;
    case "quarterly":
        billingAmount = contractValue / 4;  // 年約除以4季
        break;
    case "annual":
        billingAmount = contractValue;      // 完整年約金額
        break;
}
```

**動態到期日計算**
```typescript
let dueDate = new Date(today);

switch (contract.billingCycle) {
    case "monthly":
        dueDate.setMonth(dueDate.getMonth() + 1);
        break;
    case "quarterly":
        dueDate.setMonth(dueDate.getMonth() + 3);
        break;
    case "annual":
        dueDate.setFullYear(dueDate.getFullYear() + 1);
        break;
}
```

### 3. 自動提醒系統

**續約提醒自動建立**
```typescript
// 在建立合約時自動建立續約提醒
const reminderDate = new Date(contract.endDate);
reminderDate.setDate(reminderDate.getDate() - 30); // 提前30天提醒

const renewalReminder = {
    contractId: newContract.id,
    title: `合約續約提醒：${contract.contractName}`,
    description: `合約將於 ${contract.endDate} 到期，請準備續約相關事宜`,
    reminderDate: reminderDate.toISOString().split('T')[0],
    reminderType: "renewal",
    priority: "high",
    status: "active"
};
```

**提醒篩選功能**
```typescript
// 預設顯示近1個月的提醒
if (!startDate && !endDate) {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    reminders = reminders.filter(reminder => 
        new Date(reminder.reminderDate) >= oneMonthAgo
    );
}
```

### 4. 即時通知系統

**通知徽章計算**
```typescript
const { data: upcomingReminders = [] } = useQuery<ReminderWithContract[]>({
    queryKey: ["/api/reminders/upcoming"],
});

const { data: overduePayments = [] } = useQuery<BillingRecordWithContract[]>({
    queryKey: ["/api/billing-records/overdue"],
});

const totalNotifications = upcomingReminders.length + overduePayments.length;
```

## 使用者操作指南

### 系統登入與首頁
1. 開啟瀏覽器，進入系統首頁
2. 系統無需登入，直接顯示儀表板
3. 儀表板顯示：
   - 廠商總數
   - 活躍合約數量
   - 逾期付款提醒
   - 月營收統計

### 廠商管理操作
1. **新增廠商**
   - 點擊「新增廠商」按鈕
   - 填寫公司名稱（必填）
   - 輸入統一編號
   - 填寫聯絡人資訊
   - 輸入地址資訊
   - 點擊「新增廠商」確認

2. **編輯廠商**
   - 在廠商列表點擊編輯按鈕
   - 修改所需資訊
   - 點擊「更新廠商」儲存

3. **刪除廠商**
   - 點擊刪除按鈕
   - 系統會檢查是否有關聯合約
   - 確認後執行刪除

### 合約管理操作
1. **建立合約**
   - 點擊「新增合約」
   - 選擇關聯廠商
   - 填寫合約名稱和描述
   - 設定合約金額
   - 選擇開始和結束日期
   - 設定計費周期（月繳/季繳/年繳）
   - 輸入公司抬頭和統編
   - 填寫合約條款
   - 系統自動建立續約提醒

2. **合約續約**
   - 在合約列表點擊續約按鈕
   - 系統自動計算新的合約期間
   - 智能產生新的合約名稱
   - 建立新合約並更新舊合約狀態

3. **產生帳單**
   - 點擊「產生帳單」按鈕
   - 系統根據計費周期自動計算金額
   - 月繳：年約金額÷12
   - 季繳：年約金額÷4
   - 年繳：完整年約金額

### 帳務管理操作
1. **檢視帳單**
   - 瀏覽帳務記錄列表
   - 可依發票號碼、合約、廠商搜尋
   - 點擊查看按鈕檢視詳細資訊

2. **編輯帳單**
   - 點擊編輯按鈕
   - 修改帳單資訊
   - 更新付款狀態
   - 儲存變更

3. **標記付款**
   - 點擊付款確認按鈕
   - 帳單狀態自動更新為「已付款」

### 提醒管理操作
1. **檢視提醒**
   - 預設顯示近1個月的提醒
   - 可依狀態、優先級篩選
   - 支援日期範圍查詢

2. **新增提醒**
   - 點擊「新增提醒」
   - 選擇關聯合約
   - 填寫提醒標題和描述
   - 設定提醒日期
   - 選擇提醒類型和優先級

3. **編輯提醒**
   - 點擊編輯按鈕
   - 修改提醒資訊
   - 更新狀態或優先級

4. **完成提醒**
   - 點擊完成按鈕
   - 提醒狀態更新為「已完成」

## 系統特色功能

### 1. 完整中文化支援
- 所有介面文字完全中文化
- 錯誤訊息和通知訊息中文化
- 日期格式採用台灣標準（年/月/日）
- 貨幣顯示為新台幣（TWD）

### 2. 響應式設計
- 支援桌面、平板、手機等多種裝置
- 自動適應不同螢幕尺寸
- 觸控操作友善

### 3. 即時資料更新
- 使用 React Query 管理伺服器狀態
- 資料變更即時反映到介面
- 智能快取機制，提升效能

### 4. 資料驗證機制
- 前端使用 Zod 進行表單驗證
- 後端 API 層級資料驗證
- 友善的錯誤訊息提示

### 5. 自動化業務邏輯
- 合約建立自動產生續約提醒
- 智能計算合約續約期間
- 計費周期自動計算帳單金額

## 技術實現細節

### 前端狀態管理
使用 TanStack Query 進行伺服器狀態管理：
```typescript
const { data: contracts, isLoading } = useQuery({
    queryKey: ["/api/contracts"],
});

const createMutation = useMutation({
    mutationFn: async (data) => {
        return await apiRequest("POST", "/api/contracts", data);
    },
    onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["/api/contracts"] });
    },
});
```

### 後端資料存取
使用 Drizzle ORM 進行類型安全的資料庫操作：
```typescript
async getContracts(): Promise<ContractWithVendor[]> {
    return await this.db.select().from(contracts).leftJoin(
        vendors,
        eq(contracts.vendorId, vendors.id)
    );
}
```

### 表單處理
結合 React Hook Form 和 Zod 進行表單管理：
```typescript
const form = useForm({
    resolver: zodResolver(insertContractSchema),
    defaultValues: {
        contractName: "",
        contractValue: "",
        startDate: "",
        endDate: "",
    },
});
```

## 部署與維護

### 開發環境設定
1. 安裝 Node.js 18+ 和 npm
2. 安裝專案依賴：`npm install`
3. 設定環境變數：`DATABASE_URL`
4. 執行資料庫遷移：`npm run db:push`
5. 啟動開發伺服器：`npm run dev`

### 生產環境部署
1. 建置前端：`npm run build`
2. 建置後端：使用 ESBuild 打包
3. 設定生產環境變數
4. 部署到 Replit 或其他雲端平台

### 維護建議
1. 定期備份資料庫
2. 監控系統效能和錯誤日誌
3. 定期更新依賴套件
4. 實施自動化測試

## 效能優化

### 前端優化
- 使用 React.memo 避免不必要的重渲染
- 實施虛擬滾動處理大量資料
- 圖片和資源懶加載
- 程式碼分割和動態匯入

### 後端優化
- 資料庫查詢優化和索引設計
- API 回應快取
- 資料庫連接池管理
- 壓縮和最小化回應內容

### 資料庫優化
- 合理的索引策略
- 查詢效能監控
- 定期資料庫維護
- 讀寫分離（未來擴展）

## 安全性考量

### 資料安全
- 輸入資料驗證和清理
- SQL 注入防護（ORM 提供）
- XSS 攻擊防護
- HTTPS 加密傳輸

### 存取控制
- API 路由驗證
- 資料存取權限控制
- 敏感資料加密儲存
- 定期安全審核

## 未來擴展規劃

### 功能擴展
1. **使用者權限管理**
   - 多使用者支援
   - 角色權限設定
   - 操作日誌記錄

2. **報表系統**
   - 財務報表產生
   - 合約統計分析
   - 資料匯出功能

3. **通知系統增強**
   - Email 通知
   - SMS 簡訊提醒
   - 推播通知

4. **整合功能**
   - 會計系統整合
   - 電子發票系統
   - 第三方支付整合

### 技術改進
1. **效能提升**
   - Redis 快取層
   - CDN 內容分發
   - 資料庫讀寫分離

2. **可靠性增強**
   - 自動備份機制
   - 災難復原計畫
   - 監控和告警系統

3. **開發體驗**
   - 自動化測試套件
   - CI/CD 流程
   - 程式碼品質檢查

## 總結

本合約管理系統成功實現了台灣企業所需的完整合約管理功能，從廠商管理、合約追蹤、帳務處理到自動化提醒，提供了一站式的解決方案。系統採用現代化技術架構，確保了良好的擴展性和維護性，完整的中文化支援使其完全符合台灣企業的使用需求。

通過智能化的業務邏輯實現，如自動續約計算、計費周期功能、智能提醒系統等，大幅提升了企業的工作效率。響應式設計和友善的使用者介面，讓使用者能夠輕鬆上手並高效使用系統。

系統的模組化設計和完善的技術文檔，為未來的功能擴展和系統維護奠定了良好的基礎。