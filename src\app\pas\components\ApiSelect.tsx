import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import { ApiResponse } from "@/config/api";
import { SelectOption } from "@/services/pas/OptionParameterService";

interface ApiSelectProps {
    fetchOptions: () => Promise<ApiResponse<SelectOption[]>>; // API 載入選項的方法
    value?: string | string[];            // 綁定的值（由 Form 傳入）
    placeholder?: string;                 // 提示文字
    labelKey?: keyof SelectOption;        // 選項顯示文字的 key（預設為 optionText）
    valueKey?: keyof SelectOption;        // 選項值的 key（預設為 optionValue）
    mode?: 'multiple' | 'tags';           // 多選或標籤模式
    allowClear?: boolean;                 // 是否可清除
    showSearch?: boolean;                 // 是否顯示搜尋
    disabled?: boolean;                   // 是否禁用
    onChange?: (value: any) => void;      // 選項變更時觸發（由 Form 傳入）
}

const ApiSelect: React.FC<ApiSelectProps> = ({
    fetchOptions,
    value,
    placeholder = '請選擇',
    labelKey = 'optionText',
    valueKey = 'optionValue',
    mode,
    allowClear = true,
    showSearch = true,
    disabled = false,
    onChange,
}) => {
    const [options, setOptions] = useState<{ value: string; label: string }[]>([]);
    const [loading, setLoading] = useState(false);

    // 載入選項資料
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                const response = await fetchOptions();
                if (response.success && Array.isArray(response.data)) {
                    const list = response.data.map(item => ({
                        value: item[valueKey] ?? item.optionValue, // 轉成標準 value
                        label: item[labelKey] ?? item.optionText,  // 轉成標準 label
                    }));
                    setOptions(list);
                } else {
                    setOptions([]);
                }
            } catch (error) {
                console.error('載入選項失敗', error);
                setOptions([]);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [fetchOptions, labelKey, valueKey]);
    // 注意：如果 labelKey 或 valueKey 有改，也應重新載入

    return (
        <Select
            placeholder={placeholder}        // 尚未選取時的提示文字
            loading={loading}               // 顯示載入圈圈
            value={value}                   // 綁定目前選中的值
            onChange={onChange}             // 選擇變動時觸發（由 Form 傳入）
            style={{ width: '100%' }}       // 撐滿寬度
            showSearch={showSearch}         // 啟用搜尋
            allowClear={allowClear}         // 允許清除
            disabled={disabled}             // 是否禁用
            mode={mode}                     // 選取模式：單選、多選、標籤
            optionFilterProp="label"        // 搜尋條件為 label
            options={options}               // 下拉選單資料
            notFoundContent={loading ? <Spin size="small" /> : '無資料'} // 沒資料時顯示
        />
    );
};

export default ApiSelect;
