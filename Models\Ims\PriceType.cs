using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Attributes;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 價格類別 </summary>
public class PriceType : ModelBaseEntity
{
    /// <summary> 價格類別編號 </summary>
    [Key]
    [Comment("價格類別編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PriceTypeID { get; set; }

    /// <summary> 價格類別名稱 </summary>
    [Comment("價格類別名稱")]
    [Column(TypeName = "nvarchar(100)")]
    public string Name { get; set; }

    /// <summary> 價格類別描述 </summary>
    [Comment("價格類別描述")]
    [Column(TypeName = "nvarchar(500)")]
    public string Description { get; set; }

    /// <summary> 排序 </summary>
    [Comment("排序")]
    [Column(TypeName = "int")]
    public int SortCode { get; set; }

    /// <summary> 允許停用 </summary>
    /// <remarks> 預設資料(一般、會員、大批)不允許停用 </remarks>
    [Comment("允許停用")]
    [Column(TypeName = "bit")]
    [DefaultValue(true)]
    public bool AllowStop { get; set; }

    /// <summary> 停用 </summary>
    [Comment("停用")]
    [Column(TypeName = "bit")]
    [DefaultValue(false)]
    public bool IsStop { get; set; }

    /// <summary> 建構式 </summary>
    public PriceType()
    {
        PriceTypeID = Guid.NewGuid();
        Name = string.Empty;
        Description = string.Empty;
        SortCode = 0;
        AllowStop = true;
        IsStop = false;
    }
}

/// <summary> 價格類別 DTO </summary>
public class PriceTypeDTO : ModelBaseEntityDTO
{
    /// <summary> 價格類別編號 </summary>
    public Guid PriceTypeID { get; set; }

    /// <summary> 價格類別名稱 </summary>
    public string Name { get; set; }

    /// <summary> 價格類別描述 </summary>
    public string Description { get; set; }

    /// <summary> 排序 </summary>
    public int SortCode { get; set; }

    /// <summary> 允許停用 </summary>
    public bool AllowStop { get; set; }

    /// <summary> 停用 </summary>
    public bool IsStop { get; set; }

    /// <summary> 建構式 </summary>
    public PriceTypeDTO()
    {
        PriceTypeID = Guid.NewGuid();
        Name = string.Empty;
        Description = string.Empty;
        SortCode = 0;
        AllowStop = true;
        IsStop = false;
    }
}