/* 儀表板自定義樣式 */

/* 網格佈局樣式 */
.react-grid-layout {
    position: relative;
    min-height: calc(100vh - 250px);
    width: 100% !important; /* 確保容器寬度正確 */
    max-width: 100% !important; /* 防止超出容器 */
    overflow: hidden; /* 防止內容溢出 */
    box-sizing: border-box;
}

.react-grid-item {
    transition: all 200ms ease;
    transition-property: left, top;
    border-radius: 8px;
    box-sizing: border-box;
    /* 確保組件不會超出容器邊界 */
    max-width: 100%;
    overflow: hidden;
}

.react-grid-item.cssTransforms {
    transition-property: transform;
}

/* 響應式容器樣式 */
.dashboard-grid-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden; /* 防止水平滾動 */
    box-sizing: border-box;
}

.dashboard-grid-container.edit-mode {
    overflow-x: visible; /* 編輯模式允許拖拽超出邊界 */
}

/* 確保在小螢幕上正確顯示 */
@media (max-width: 768px) {
    .react-grid-layout {
        min-height: auto;
        padding: 0 !important;
    }

    .react-grid-item {
        min-width: 0 !important;
        max-width: 100% !important;
    }

    .dashboard-grid-container {
        padding: 10px !important;
        overflow-x: hidden !important;
    }
}

@media (max-width: 480px) {
    .react-grid-layout {
        margin: 0 !important;
    }

    .react-grid-item {
        margin: 4px 0 !important;
    }

    .dashboard-grid-container {
        padding: 8px !important;
    }
}

/* Widget removal button styles */
.widget-remove-button {
    transition: all 0.2s ease;
}

.widget-remove-button:hover {
    background-color: #ff4d4f !important;
    color: white !important;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

.widget-remove-button:active {
    transform: scale(0.95);
}

/* Drag mode toggle button styles */
.drag-mode-toggle {
    transition: all 0.3s ease;
}

.drag-mode-toggle.smart-mode {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    border-color: #52c41a;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.drag-mode-toggle.smart-mode:hover {
    background: linear-gradient(135deg, #73d13d, #95de64);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
}

.drag-mode-toggle.free-mode {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    border-color: #1890ff;
}

.drag-mode-toggle.free-mode:hover {
    background: linear-gradient(135deg, #40a9ff, #69c0ff);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.react-grid-item>.react-resizable-handle {
    position: absolute;
    width: 20px;
    height: 20px;
    bottom: 0;
    right: 0;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0NDQiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PHBhdGggZD0ibTUgNWgtNHYtNGg0eiIvPjwvZz48L3N2Zz4=');
    background-position: bottom right;
    padding: 0 3px 3px 0;
    background-repeat: no-repeat;
    background-origin: content-box;
    box-sizing: border-box;
    cursor: se-resize;
}

.react-grid-item.react-grid-placeholder {
    background: #1890ff;
    opacity: 0.2;
    transition-duration: 100ms;
    z-index: 2;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
    border-radius: 8px;
}

/* 編輯模式樣式 */
.react-grid-item.edit-mode {
    border: 2px dashed #1890ff !important;
    background: rgba(24, 144, 255, 0.05);
}

.react-grid-item.edit-mode:hover {
    border-color: #40a9ff !important;
    background: rgba(24, 144, 255, 0.1);
}

/* 拖拽時的樣式 */
.react-grid-item.react-draggable-dragging {
    transition: none;
    z-index: 3;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
    transform: rotate(2deg);
}

/* Widget卡片樣式 */
.dashboard-widget-card {
    height: 100%;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    overflow: hidden;
}

.dashboard-widget-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.dashboard-widget-card .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    padding: 0 24px;
    min-height: 64px;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

.dashboard-widget-card .ant-card-head-title {
    padding: 20px 0;
    font-weight: 600;
    font-size: 16px;
    color: #262626;
}

.dashboard-widget-card .ant-card-body {
    padding: 24px;
    height: calc(100% - 64px);
    overflow: auto;
    background: white;
}

/* 工具列樣式 */
.dashboard-toolbar {
    background: white;
    padding: 32px 40px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 40px;
    border: 1px solid #f0f0f0;
}

.dashboard-toolbar h1 {
    margin: 0;
    font-size: 28px;
    color: #1890ff;
    font-weight: 700;
}

.dashboard-toolbar p {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 15px;
    line-height: 1.5;
}

/* 編輯模式提示 */
.edit-mode-banner {
    background: linear-gradient(90deg, #e6f7ff 0%, #bae7ff 100%);
    border: 1px solid #91d5ff;
    border-radius: 8px;
    padding: 20px 24px;
    margin-bottom: 32px;
    color: #0050b3;
    font-size: 14px;
    line-height: 1.6;
}

/* 新增組件對話框樣式 */
.add-widget-modal .ant-modal-body {
    padding: 32px;
}

.widget-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    height: 100%;
}

.widget-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.widget-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.widget-card.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 主容器樣式 */
.dashboard-container {
    padding: 40px;
    background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
    min-height: 100vh;
}

/* 響應式設計 */
@media (max-width: 1200px) {
    .dashboard-container {
        padding: 32px;
    }

    .dashboard-toolbar {
        padding: 24px 32px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 20px;
    }

    .dashboard-toolbar {
        flex-direction: column;
        gap: 16px;
        padding: 20px 24px;
    }

    .dashboard-toolbar .ant-space {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .react-grid-item>.react-resizable-handle {
        width: 30px;
        height: 30px;
    }

    .dashboard-widget-card .ant-card-body {
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .dashboard-toolbar h1 {
        font-size: 24px;
    }

    .dashboard-toolbar p {
        font-size: 14px;
    }
}

/* 滾動條樣式 */
.dashboard-widget-card .ant-card-body::-webkit-scrollbar {
    width: 8px;
}

.dashboard-widget-card .ant-card-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dashboard-widget-card .ant-card-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.dashboard-widget-card .ant-card-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 載入狀態樣式 */
.dashboard-loading {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-widget-card {
    animation: fadeInUp 0.6s ease-out;
}

.dashboard-toolbar {
    animation: fadeInUp 0.4s ease-out;
}