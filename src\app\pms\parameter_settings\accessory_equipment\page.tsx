"use client";

/* 附屬設備
  /app/pms/parameter_settings/accessory_equipment/page.tsx
*/

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  DatePicker,
  Divider,
  Tooltip,
  Descriptions,
  List,
  Typography,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  AccessoryEquipment,
  getAccessoryEquipments,
  createAccessoryEquipment,
  updateAccessoryEquipment,
  deleteAccessoryEquipment,
} from "@/services/pms/accessoryEquipmentService";
import { notifySuccess, notifyError } from "@/utils/notification";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { useAuth } from "@/contexts/AuthContext";
import dayjs from "dayjs";
import { getAssets } from "@/services/pms/assetService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import {
  getEquipmentTypes,
  EquipmentType,
} from "@/services/pms/equipmentTypeService";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import {
  getAssetAccounts,
  AssetAccount,
} from "@/services/pms/assetAccountService";

const { Text } = Typography;

const AccessoryEquipmentPage: React.FC = () => {
  const [accessoryEquipments, setAccessoryEquipments] = useState<
    AccessoryEquipment[]
  >([]);
  const [filteredEquipments, setFilteredEquipments] = useState<
    AccessoryEquipment[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingEquipment, setEditingEquipment] =
    useState<AccessoryEquipment | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState("");
  const [assetFilter, setAssetFilter] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const { user } = useAuth();
  const [equipmentTypes, setEquipmentTypes] = useState<EquipmentType[]>([]);
  const [customType, setCustomType] = useState<string>("");
  const [assetStatuses, setAssetStatuses] = useState<
    { assetStatusId: string; assetStatusNo: string; name: string }[]
  >([]);
  const [customStatus, setCustomStatus] = useState<string>("");
  const [isMobile, setIsMobile] = useState(false);

  // 財產類型
  interface SimpleAsset {
    assetId: string;
    assetNo: string;
    assetName: string;
  }

  // 財產科目類型
  interface AssetAccountGroup {
    accountNo: string;
    accountName: string;
    assets: SimpleAsset[];
  }
  // 財產列表
  const [assets, setAssets] = useState<SimpleAsset[]>([]);
  // 財產科目列表
  const [assetAccounts, setAssetAccounts] = useState<AssetAccount[]>([]);
  // 財產科目分組列表
  const [groupedAssets, setGroupedAssets] = useState<AssetAccountGroup[]>([]);

  // 加載附屬設備列表
  const loadAccessoryEquipments = async () => {
    setLoading(true);
    try {
      const response = await getAccessoryEquipments();
      if (response.success && response.data) {
        setAccessoryEquipments(response.data);
        setFilteredEquipments(response.data);
      } else {
        notifyError("獲取附屬設備列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取附屬設備列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 加載財產科目列表
  const loadAssetAccounts = async () => {
    try {
      const response = await getAssetAccounts();
      if (response.success && response.data) {
        setAssetAccounts(response.data);
      } else {
        notifyError("獲取財產科目列表失敗", response.message);
      }
    } catch (error) {
      console.error("獲取財產科目列表錯誤:", error);
      notifyError("獲取財產科目列表失敗", "請稍後再試");
    }
  };

  // 將財產按科目分組
  const groupAssetsByAccount = (
    assets: SimpleAsset[],
    accounts: AssetAccount[]
  ) => {
    const groups: AssetAccountGroup[] = [];

    accounts.forEach((account) => {
      const accountAssets = assets.filter((asset) =>
        asset.assetNo.startsWith(account.assetAccountNo)
      );

      if (accountAssets.length > 0) {
        groups.push({
          accountNo: account.assetAccountNo,
          accountName: account.assetAccountName,
          assets: accountAssets,
        });
      }
    });

    return groups;
  };

  // 加載財產列表
  const loadAssets = async () => {
    try {
      const response = await getAssets();
      if (response.success && response.data) {
        const assetsData = response.data.map((item: any) => ({
          assetId: item.asset.assetId,
          assetNo: item.asset.assetNo,
          assetName: item.asset.assetName,
        }));
        setAssets(assetsData);

        // 如果已有財產科目資料，進行分組
        if (assetAccounts.length > 0) {
          const grouped = groupAssetsByAccount(assetsData, assetAccounts);
          setGroupedAssets(grouped);
        }
      } else {
        notifyError("獲取財產列表失敗", response.message);
      }
    } catch (error) {
      console.error("獲取財產列表錯誤:", error);
      notifyError("獲取財產列表失敗", "請稍後再試");
    }
  };

  // 加載財產狀態列表
  const loadAssetStatuses = async () => {
    try {
      const response = await getAssetStatuses();
      if (response.success && response.data) {
        setAssetStatuses(response.data);
      } else {
        notifyError("獲取財產狀態列表失敗", response.message);
      }
    } catch (error) {
      console.error("獲取財產狀態列表錯誤:", error);
      notifyError("獲取財產狀態列表失敗", "請稍後再試");
    }
  };

  // 監聽資產狀態列表變化，設定預設值
  useEffect(() => {
    if (assetStatuses.length > 0 && !editingEquipment && isModalVisible) {
      form.setFieldsValue({
        usageStatus: assetStatuses[0].assetStatusId,
      });
    }
  }, [assetStatuses, editingEquipment, isModalVisible]);

  // 加載設備類型列表
  const loadEquipmentTypes = async () => {
    try {
      const response = await getEquipmentTypes();
      if (response.success && response.data) {
        setEquipmentTypes(response.data);
      } else {
        notifyError("獲取設備類型列表失敗", response.message);
      }
    } catch (error) {
      console.error("獲取設備類型列表錯誤:", error);
      notifyError("獲取設備類型列表失敗", "請稍後再試");
    }
  };

  // 加載資料
  useEffect(() => {
    loadAccessoryEquipments(); // 加載附屬設備列表
    loadAssetAccounts(); // 加載財產科目列表
    loadAssets(); // 加載財產列表
    loadAssetStatuses(); // 加載財產狀態列表
    loadEquipmentTypes(); // 加載設備類型列表
  }, []);

  // 當財產科目或財產資料更新時，重新分組
  useEffect(() => {
    if (assets.length > 0 && assetAccounts.length > 0) {
      const grouped = groupAssetsByAccount(assets, assetAccounts);
      setGroupedAssets(grouped);
    }
  }, [assets, assetAccounts]);

  // 處理搜尋及篩選
  useEffect(() => {
    let filtered = accessoryEquipments;

    // 設備名稱搜尋篩選
    if (searchText) {
      filtered = filtered.filter((equipment) =>
        equipment.equipmentName
          ? equipment.equipmentName
              .toLowerCase()
              .includes(searchText.toLowerCase())
          : false
      );
    }

    // 所屬財產篩選
    if (assetFilter) {
      filtered = filtered.filter(
        (equipment) => equipment.assetId === assetFilter
      );
    }

    // 使用狀態篩選
    if (statusFilter) {
      filtered = filtered.filter(
        (equipment) => equipment.usageStatus === statusFilter
      );
    }

    setFilteredEquipments(filtered);
  }, [searchText, assetFilter, statusFilter, accessoryEquipments]);

  // 處理所屬財產篩選變更
  const handleAssetFilterChange = (value: string) => {
    setAssetFilter(value);
  };

  // 處理使用狀態篩選變更
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // 處理選單自訂輸入
  const handleCustomTypeChange = (value: string) => {
    setCustomType(value);
  };

  // 處理選單新增選項
  const handleAddCustomType = () => {
    notifyError("無法新增類型", "設備類型由系統管理，不可自行新增");
    setCustomType("");
  };

  // 處理使用狀態自訂輸入
  const handleCustomStatusChange = (value: string) => {
    setCustomStatus(value);
  };

  // 處理使用狀態新增選項
  const handleAddCustomStatus = () => {
    if (customStatus) {
      notifyError("無法新增狀態", "財產狀態由系統管理，不可自行新增");
      setCustomStatus("");
    }
  };

  // 處理財產下拉選單
  const handleAssetSelect = (assetId: string) => {
    const selectedAsset = assets.find((asset) => asset.assetId === assetId);
    if (selectedAsset) {
      form.setFieldsValue({
        assetId: selectedAsset.assetId,
        assetName: selectedAsset.assetName,
      });
      console.log("選擇的財產ID:", selectedAsset.assetId);
    }
  };

  // 獲取狀態名稱
  const getStatusName = (statusId: string) => {
    const status = assetStatuses.find((s) => s.assetStatusId === statusId);
    return status ? status.name : statusId;
  };

  // 獲取狀態顏色
  const getStatusColor = (statusId: string) => {
    const status = assetStatuses.find((s) => s.assetStatusId === statusId);
    return status ? STATUS_COLORS[status.name] || "default" : "default";
  };

  // 表格列定義
  const columns: ColumnsType<AccessoryEquipment> = [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "設備編號",
      dataIndex: "equipmentNo",
      key: "equipmentNo",

      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="設備類型">
                  {record.equipmentType}
                </Descriptions.Item>
                <Descriptions.Item label="規格/型號">
                  {record.specification}
                </Descriptions.Item>
                <Descriptions.Item label="購入日期">
                  {DateTimeExtensions.formatDateFromTimestamp(
                    record.purchaseDate
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="備註">
                  {record.remarks}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          styles={{
            root: { maxWidth: "400px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "設備名稱",
      dataIndex: "equipmentName",
      key: "equipmentName",
    },
    {
      title: "所屬財產",
      dataIndex: "assetName",
      key: "assetName",
      render: (text, record) => {
        const asset = assets.find((a) => a.assetId === record.assetId);
        return asset
          ? `${asset.assetNo}-${asset.assetName}`
          : record.assetName || "-";
      },
    },
    {
      title: "購入價格",
      dataIndex: "purchasePrice",
      key: "purchasePrice",
      render: (text) => (
        <Tag color="red">
          {new Intl.NumberFormat("zh-TW", {
            style: "currency",
            currency: "TWD",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(text)}
        </Tag>
      ),
    },
    {
      title: "使用狀態",
      dataIndex: "usageStatus",
      key: "usageStatus",
      render: (statusId: string) => {
        const statusName = getStatusName(statusId);
        const color = getStatusColor(statusId);
        return <Tag color={color}>{statusName}</Tag>;
      },
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (text) => (
        <span>{DateTimeExtensions.formatFromTimestamp(text)}</span>
      ),
    },
    {
      title: "建立者",
      dataIndex: "createUserName",
      key: "createUserName",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
        </Space>
      ),
    },
  ];

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      // 處理日期
      if (
        values.purchaseDate &&
        typeof values.purchaseDate === "object" &&
        values.purchaseDate.valueOf
      ) {
        values.purchaseDate = values.purchaseDate.valueOf();
      }

      // 處理購買價格
      if (typeof values.purchasePrice === "string") {
        values.purchasePrice = parseFloat(values.purchasePrice);
        if (isNaN(values.purchasePrice)) {
          values.purchasePrice = 0;
        }
      }

      // 提交到後端的數據
      const submitData = {
        equipmentNo: values.equipmentNo,
        equipmentName: values.equipmentName,
        equipmentType: values.equipmentType,
        specification: values.specification,
        purchaseDate: values.purchaseDate,
        purchasePrice: values.purchasePrice,
        usageStatus: values.usageStatus,
        remarks: values.remarks || "",
        assetId: values.assetId || "",
        assetName: values.assetName || "",
      };

      if (editingEquipment) {
        // 更新附屬設備
        const response = await updateAccessoryEquipment({
          ...submitData,
          accessoryEquipmentId: editingEquipment.accessoryEquipmentId,
          updateUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("更新成功", "附屬設備已更新");
          loadAccessoryEquipments();
        } else {
          notifyError("更新失敗", response.message || "請稍後再試");
        }
      } else {
        // 新增附屬設備
        const response = await createAccessoryEquipment({
          ...submitData,
          createUserId: user?.userId,
        });
        if (response.success) {
          notifySuccess("新增成功", "附屬設備已新增");
          loadAccessoryEquipments();
        } else {
          notifyError("新增失敗", response.message || "請稍後再試");
        }
      }
      setIsModalVisible(false);
      form.resetFields();
      setEditingEquipment(null);
    } catch (error) {
      console.error("操作失敗:", error);
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (equipment: AccessoryEquipment) => {
    const formData = { ...equipment };

    // 處理購入日期
    if (formData.purchaseDate) {
      formData.purchaseDate = dayjs(formData.purchaseDate) as any;
    }

    setEditingEquipment(equipment);
    form.setFieldsValue(formData);
    setIsModalVisible(true);
  };

  // 打開新增附屬設備的表單
  const showAddModal = () => {
    setIsModalVisible(true);
    setEditingEquipment(null);
    form.resetFields();

    // 如果已有狀態數據，設定預設值
    if (assetStatuses.length > 0) {
      form.setFieldsValue({
        usageStatus: assetStatuses[0].assetStatusId,
      });
    }
  };

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredEquipments}
        renderItem={(equipment, index) => (
          <List.Item
            key={equipment.accessoryEquipmentId}
            style={{
              padding: "12px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <div style={{ width: "100%" }}>
              {/* 標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <div
                  style={{
                    width: 24,
                    height: 24,
                    background: "#f0f0f0",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "14px",
                    flexShrink: 0,
                  }}
                >
                  {index + 1}
                </div>
                <Text strong style={{ fontSize: "16px" }}>
                  {equipment.equipmentName}
                </Text>
              </div>

              {/* 設備編號列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "14px" }}>
                  設備編號：{equipment.equipmentNo}
                </Text>
              </div>

              {/* 規格型號列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px" }}>
                  規格/型號：{equipment.specification}
                </Text>
              </div>

              {/* 設備類型列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px" }}>
                  設備類型：{equipment.equipmentType}
                </Text>
              </div>

              {/* 所屬財產列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px" }}>
                  所屬財產：
                  {equipment.assetNo
                    ? `${equipment.assetNo}-${equipment.assetName}`
                    : equipment.assetName}
                </Text>
              </div>

              {/* 購入價格列 */}
              <div
                style={{
                  marginBottom: "8px",
                }}
              >
                <Text style={{ fontSize: "14px" }}>購入價格：</Text>
                <Tag color="red" style={{ margin: 0 }}>
                  {new Intl.NumberFormat("zh-TW", {
                    style: "currency",
                    currency: "TWD",
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(equipment.purchasePrice)}
                </Tag>
              </div>

              {/* 使用狀態列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text style={{ fontSize: "14px" }}>
                  使用狀態：
                  <Tag color={getStatusColor(equipment.usageStatus)}>
                    {getStatusName(equipment.usageStatus)}
                  </Tag>
                </Text>
              </div>

              {/* 時間和建立者列 */}
              <div style={{ marginBottom: "8px" }}>
                <Text type="secondary" style={{ fontSize: "13px" }}>
                  建立時間：
                  {DateTimeExtensions.formatFromTimestamp(equipment.createTime)}
                </Text>
              </div>

              {/* 操作按鈕列 */}
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginTop: "12px",
                }}
              >
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(equipment)}
                  style={{ padding: 0 }}
                >
                  編輯
                </Button>
              </div>
            </div>
          </List.Item>
        )}
        pagination={{
          onChange: (page) => {
            console.log(page);
          },
          pageSize: 10,
          size: "small",
          style: { marginTop: "16px" },
        }}
      />
    );
  };

  return (
    <Card
      title="附屬設備管理"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
        wrap
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增附屬設備
        </Button>

        {/* 所屬財產篩選 */}
        <Select
          placeholder="所屬財產"
          onChange={handleAssetFilterChange}
          allowClear
          style={{ width: isMobile ? "100%" : 300 }}
        >
          {groupedAssets.map((group) => (
            <Select.OptGroup
              key={group.accountNo}
              label={`${group.accountNo}-${group.accountName}`}
            >
              {group.assets.map((asset) => (
                <Select.Option
                  key={`filter-asset-${asset.assetId}`}
                  value={asset.assetId}
                >
                  {`${asset.assetNo}-${asset.assetName}`}
                </Select.Option>
              ))}
            </Select.OptGroup>
          ))}
        </Select>

        {/* 使用狀態篩選 */}
        <Select
          placeholder="使用狀態"
          onChange={handleStatusFilterChange}
          allowClear
          style={{ width: isMobile ? "100%" : 250 }}
          defaultValue=""
        >
          <Select.Option key="filter-status-all" value="">
            全部
          </Select.Option>
          {assetStatuses.map((status) => (
            <Select.Option
              key={`filter-status-${status.assetStatusId}`}
              value={status.assetStatusId}
            >
              {status.name}
            </Select.Option>
          ))}
        </Select>

        {/* 搜尋設備名稱 */}
        <Input
          placeholder="搜尋設備名稱"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: isMobile ? "100%" : 200 }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredEquipments}
          rowKey="accessoryEquipmentId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingEquipment ? "編輯附屬設備" : "新增附屬設備"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingEquipment(null);
        }}
        maskClosable={false}
        okText="確認"
        cancelText="取消"
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="equipmentNo"
            label="設備編號："
            rules={[{ required: true, message: "請輸入設備編號" }]}
          >
            <Input placeholder="請輸入設備編號" />
          </Form.Item>

          <Form.Item
            name="equipmentName"
            label="設備名稱："
            rules={[{ required: true, message: "請輸入設備名稱" }]}
          >
            <Input placeholder="請輸入設備名稱" />
          </Form.Item>

          <Form.Item
            name="assetId"
            label="所屬財產："
            rules={[{ required: false, message: "請選擇所屬財產" }]}
          >
            <Select
              placeholder="請選擇所屬財產"
              onChange={handleAssetSelect}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                ((option?.children || "") + "")
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
              style={{ width: "100%" }}
            >
              {groupedAssets.map((group) => (
                <Select.OptGroup
                  key={group.accountNo}
                  label={`${group.accountNo}-${group.accountName}`}
                >
                  {group.assets.map((asset) => (
                    <Select.Option
                      key={`asset-${asset.assetId}`}
                      value={asset.assetId}
                    >
                      {`${asset.assetNo}-${asset.assetName}`}
                    </Select.Option>
                  ))}
                </Select.OptGroup>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="assetName"
            label="所屬財產名稱："
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>

          <Form.Item
            name="equipmentType"
            label="設備類型："
            rules={[{ required: true, message: "請輸入設備類型" }]}
          >
            <Select placeholder="請選擇設備類型" showSearch allowClear>
              {equipmentTypes.map((type) => (
                <Select.Option key={type.equipmentTypeId} value={type.name}>
                  {type.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="specification"
            label="規格/型號："
            rules={[{ required: true, message: "請輸入規格/型號" }]}
          >
            <Input placeholder="請輸入規格/型號" />
          </Form.Item>

          <Form.Item
            name="purchaseDate"
            label="購入日期："
            rules={[{ required: true, message: "請選擇購入日期" }]}
          >
            <DatePicker
              placeholder="請選擇購入日期"
              style={{ width: "100%" }}
            />
          </Form.Item>

          <Form.Item
            name="purchasePrice"
            label="購入價格："
            rules={[{ required: true, message: "請輸入購入價格" }]}
          >
            <Input
              placeholder="請輸入購入價格"
              type="number"
              min={0}
              step={0.01}
            />
          </Form.Item>

          <Form.Item
            name="usageStatus"
            label="使用狀態："
            rules={[{ required: true, message: "請選擇使用狀態" }]}
          >
            <Select placeholder="請選擇使用狀態">
              {assetStatuses.map((status) => (
                <Select.Option
                  key={status.assetStatusId}
                  value={status.assetStatusId}
                >
                  {status.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="remarks" label="備註：">
            <Input.TextArea
              placeholder="請輸入備註"
              maxLength={100}
              autoSize={{ minRows: 3, maxRows: 6 }}
              showCount
            />
          </Form.Item>

          <Form.Item name="createTime" label="建立時間：">
            <span>
              {DateTimeExtensions.formatFromTimestamp(
                editingEquipment?.createTime
              )}
            </span>
          </Form.Item>

          <Form.Item name="createUserName" label="建立者：">
            <span>{editingEquipment?.createUserName}</span>
          </Form.Item>

          <Form.Item
            name="createUserId"
            label="建立者編號："
            style={{ display: "none" }}
          >
            <span>{editingEquipment?.createUserId}</span>
          </Form.Item>

          <Form.Item name="updateTime" label="更新時間：">
            <span>
              {DateTimeExtensions.formatFromTimestamp(
                editingEquipment?.updateTime
              )}
            </span>
          </Form.Item>

          {editingEquipment?.updateUserName && (
            <Form.Item name="updateUserName" label="更新者：">
              <span>{editingEquipment?.updateUserName}</span>
            </Form.Item>
          )}

          {editingEquipment?.updateUserId && (
            <Form.Item
              name="updateUserId"
              label="更新者編號："
              style={{ display: "none" }}
            >
              <span>{editingEquipment?.updateUserId}</span>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </Card>
  );
};

export default AccessoryEquipmentPage;
