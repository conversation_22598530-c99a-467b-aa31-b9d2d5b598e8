"use client";

/* 固定資產折舊單
  /app/pms/document_maintenance/fixed_asset_depreciation_form/page.tsx
*/

import React, { useEffect, useState, useCallback } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Input,
  Select,
  DatePicker,
  Divider,
  Typography,
  Dropdown,
  MenuProps,
  Pagination,
  Form,
  Tag,
  InputNumber,
  Tabs,
  Descriptions,
  Tooltip,
  Menu,
  Alert,
  message,
  Popconfirm,
  Radio,
  Checkbox,
  Statistic,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  CheckOutlined,
  FileTextOutlined,
  EditOutlined,
  CalculatorOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import dayjs from "dayjs";
import {
  Depreciation,
  getDepreciations,
  getDepreciationById,
  createDepreciation,
  updateDepreciation,
  deleteDepreciation,
  calculateDepreciation,
  DepreciationCalculateRequest,
} from "@/services/pms/depreciationFormDetailService";
import { Asset, getAssets, getAssetById } from "@/services/pms/assetService";
import {
  Department,
  getDepartments,
} from "@/services/common/departmentService";
import {
  AssetAccount,
  getAssetAccounts,
} from "@/services/pms/assetAccountService";
import {
  AssetSubAccount,
  getAssetSubAccounts,
} from "@/services/pms/assetSubAccountService";
import {
  SystemParameter,
  getDepreciationMethods as getDepreciationMethodsFromSystem,
} from "@/services/pms/systemParameterSettingService";
import {
  DepreciationForm,
  getDepreciationForms,
  createDepreciationForm,
  updateDepreciationForm,
  deleteDepreciationForm,
  getDepreciationFormDetail,
  checkDepreciationUsage,
  getAllDepreciationForms,
} from "@/services/pms/depreciationFormService";

import {
  getColumns,
  getAssetSelectionColumns,
  getSimulationResultColumns,
  getMobileColumns,
} from "./columns";
import AssetDepreciationForm from "./Form";
import {
  AssetDepreciationFormQuery,
  DepreciationSimulationResult,
  formInitialValues,
} from "./interface";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { formatTWCurrency } from "@/utils/formatUtils";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

// 固定資產折舊單頁面
const AssetDepreciationFormPage: React.FC = () => {
  // 主要狀態
  const [depreciations, setDepreciations] = useState<Depreciation[]>([]);
  const [depreciationForms, setDepreciationForms] = useState<
    DepreciationForm[]
  >([]);
  const [historicalDepreciations, setHistoricalDepreciations] = useState<
    Depreciation[]
  >([]);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);
  // 分頁-固定資產折舊單、財產清單
  const [activeTab, setActiveTab] = useState("depreciationForm");
  const [isFormModalVisible, setIsFormModalVisible] = useState(false);
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false);
  const [isSimulationModalVisible, setIsSimulationModalVisible] =
    useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [editingDepreciation, setEditingDepreciation] =
    useState<Depreciation | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [assetDepreciations, setAssetDepreciations] = useState<Depreciation[]>(
    []
  );
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();

  // 各種資料項目
  const [departments, setDepartments] = useState<Department[]>([]);
  const [assetAccounts, setAssetAccounts] = useState<AssetAccount[]>([]);
  const [assetSubAccounts, setAssetSubAccounts] = useState<AssetSubAccount[]>(
    []
  );
  const [depreciationMethods, setDepreciationMethods] = useState<string[]>([]);
  const [systemDepreciationMethods, setSystemDepreciationMethods] = useState<
    SystemParameter[]
  >([]);
  const [defaultDepreciationMethod, setDefaultDepreciationMethod] =
    useState<string>("");
  const [simulationResults, setSimulationResults] = useState<
    DepreciationSimulationResult[]
  >([]);

  // 篩選相關狀態
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // 行動裝置
  const [isMobile, setIsMobile] = useState(false);

  // 分頁相關狀態
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 表單相關
  const [mainForm] = Form.useForm();
  const [simulationForm] = Form.useForm();

  // 新增狀態
  const [simulationTitle, setSimulationTitle] = useState("");
  const [simulationOriginalCost, setSimulationOriginalCost] = useState(100000);
  const [simulationYears, setSimulationYears] = useState(5);

  // 折舊單相關狀態
  const [isFormDetailModalVisible, setIsFormDetailModalVisible] =
    useState(false);
  const [selectedDepreciationForm, setSelectedDepreciationForm] =
    useState<DepreciationForm | null>(null);
  const [isDepreciationFormModalVisible, setIsDepreciationFormModalVisible] =
    useState(false);
  const [isEditingDepreciationForm, setIsEditingDepreciationForm] =
    useState(false);
  const [depreciationFormDetail, setDepreciationFormDetail] =
    useState<Depreciation | null>(null);
  const [depreciationFormYear, setDepreciationFormYear] = useState<number>(
    new Date().getFullYear()
  );
  const [depreciationFormMonth, setDepreciationFormMonth] = useState<number>(
    new Date().getMonth() + 1
  );
  const [depreciationFormDate, setDepreciationFormDate] = useState<number>();
  const [filteredDepreciationRecords, setFilteredDepreciationRecords] =
    useState<Depreciation[]>([]);
  const [selectedDepreciationIds, setSelectedDepreciationIds] = useState<
    string[]
  >([]);
  const [summaryInfo, setSummaryInfo] = useState<{
    count: number;
    totalOriginalAmount: number;
    totalCurrentDepreciation: number;
    totalAccumulatedDepreciation: number;
    totalBeginningBookValue: number;
    totalEndingBookValue: number;
  }>({
    count: 0,
    totalOriginalAmount: 0,
    totalCurrentDepreciation: 0,
    totalAccumulatedDepreciation: 0,
    totalBeginningBookValue: 0,
    totalEndingBookValue: 0,
  });

  // 新增：折舊單年份篩選
  const [yearFilter, setYearFilter] = useState<number | null>(null);
  // 新增：折舊單月份篩選
  const [monthFilter, setMonthFilter] = useState<number | null>(null);

  // 新增：處理折舊單年份篩選變更
  const handleYearFilterChange = (year: number | null) => {
    setYearFilter(year);
    filterDepreciationForms(year, monthFilter);
  };

  // 新增：處理折舊單月份篩選變更
  const handleMonthFilterChange = (month: number | null) => {
    setMonthFilter(month);
    filterDepreciationForms(yearFilter, month);
  };

  // 新增：篩選折舊單
  const filterDepreciationForms = (
    year: number | null,
    month: number | null
  ) => {
    let filtered = [...depreciationForms];

    // 搜尋文字篩選
    if (searchText) {
      filtered = filtered.filter(
        (form) =>
          form.depreciationId
            ?.toLowerCase()
            .includes(searchText.toLowerCase()) ||
          form.notes?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 年份篩選
    if (year) {
      filtered = filtered.filter((form) => form.depreciationYear === year);
    }

    // 月份篩選
    if (month) {
      filtered = filtered.filter((form) => form.depreciationMonth === month);
    }

    setFilteredDepreciationForms(filtered);
  };

  // 新增：清除折舊單篩選
  const clearDepreciationFilters = () => {
    setYearFilter(null);
    setMonthFilter(null);
    setSearchText("");
    setFilteredDepreciationForms(depreciationForms);
  };

  // 更新：當depreciationForms變更時，重設篩選後的折舊單數據
  useEffect(() => {
    setFilteredDepreciationForms(depreciationForms);
  }, [depreciationForms]);

  // 更新：當搜尋文字、年份或月份篩選變更時，篩選折舊單
  useEffect(() => {
    filterDepreciationForms(yearFilter, monthFilter);
  }, [searchText, yearFilter, monthFilter, depreciationForms]);

  // 初始化
  useEffect(() => {
    loadAssets();
    loadDepreciations();
    loadDepartments();
    loadAssetAccounts();
    loadAssetSubAccounts();
    loadDepreciationMethods();
    loadDepreciationForms();
  }, []);

  // 檢測行動裝置
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // 定義篩選選項 - 針對資產列表調整
  const filterOptions = [
    {
      label: "財產編號",
      value: "assetNo",
      type: "input",
    },
    {
      label: "財產名稱",
      value: "assetName",
      type: "input",
    },
    {
      label: "財產科目",
      value: "assetAccountId",
      children: assetAccounts.map((account) => ({
        label: account.assetAccountName,
        value: account.assetAccountId,
      })),
    },
    {
      label: "財產子目",
      value: "assetSubAccountId",
      children: assetSubAccounts.map((subAccount) => ({
        label: subAccount.assetSubAccountName,
        value: subAccount.assetSubAccountId,
      })),
    },
    {
      label: "部門",
      value: "departmentId",
      children: departments.map((dept) => ({
        label: dept.name,
        value: dept.departmentId,
      })),
    },
  ];

  // 處理篩選器
  const handleClearFilters = () => {
    setActiveFilters([]);
    setFilterValues({});
    setSearchText("");
  };

  // 處理新增篩選條件
  const handleAddFilter = (filterKey: string) => {
    if (!activeFilters.includes(filterKey)) {
      setActiveFilters([...activeFilters, filterKey]);
    }
  };

  // 處理移除篩選條件
  const handleRemoveFilter = (filterKey: string) => {
    setActiveFilters(activeFilters.filter((key) => key !== filterKey));
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];
    setFilterValues(newFilterValues);
  };

  // 更新篩選選單
  const filterMenu: MenuProps = {
    items: [
      ...filterOptions.map((option) => ({
        key: option.value,
        label: (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: "120px",
            }}
          >
            <span>{option.label}</span>
            {activeFilters.includes(option.value) && (
              <CheckOutlined style={{ color: "#1890ff" }} />
            )}
          </div>
        ),
        onClick: () => {
          if (activeFilters.includes(option.value)) {
            handleRemoveFilter(option.value);
          } else {
            handleAddFilter(option.value);
          }
        },
      })),
      ...(activeFilters.length > 0
        ? [
            { type: "divider" as const },
            {
              key: "clear",
              label: "清除所有篩選",
              onClick: handleClearFilters,
              danger: true,
            },
          ]
        : []),
    ],
  };

  // 渲染篩選控制元素
  const renderFilterControl = (filterKey: string) => {
    const filterOption = filterOptions.find((opt) => opt.value === filterKey);
    if (!filterOption) return null;

    return (
      <div className="filter-control">
        {filterOption.type === "date" ? (
          <DatePicker
            style={{ width: 200 }}
            onChange={(value) =>
              handleFilterValueChange(filterKey, value ? value.valueOf() : null)
            }
            placeholder={`選擇${filterOption.label}`}
            allowClear
          />
        ) : filterOption.type === "input" ? (
          <Input
            style={{ width: 200 }}
            onChange={(e) => handleFilterValueChange(filterKey, e.target.value)}
            placeholder={`輸入${filterOption.label}`}
            allowClear
          />
        ) : (
          <Select
            style={{ width: 200 }}
            onChange={(value) => handleFilterValueChange(filterKey, value)}
            options={filterOption.children}
            placeholder={`選擇${filterOption.label}`}
            allowClear
          />
        )}
      </div>
    );
  };

  // 加載折舊記錄
  const loadDepreciations = async () => {
    try {
      setLoading(true);
      const response = await getDepreciations();
      if (response.success && response.data) {
        const activeDepreciations = response.data.filter(
          (item) => !item.deleteTime
        );

        // 按期別組織折舊數據
        const groupedByPeriod = activeDepreciations.reduce((result, item) => {
          // 對於歷史折舊，按期別分組
          if (item.depreciationDate) {
            result.push(item);
          }
          return result;
        }, [] as Depreciation[]);

        // 排序 - 先按年份降序，同年份按月份降序
        const sortedDepreciations = groupedByPeriod.sort((a, b) => {
          if (a.depreciationYear !== b.depreciationYear) {
            return b.depreciationYear - a.depreciationYear;
          }
          return b.depreciationMonth - a.depreciationMonth;
        });

        setDepreciations(activeDepreciations);
        setHistoricalDepreciations(sortedDepreciations);
        setTotalCount(activeDepreciations.length);
      } else {
        message.error(response.message || "獲取折舊列表失敗");
      }
    } catch (error) {
      console.error("獲取折舊列表失敗:", error);
      message.error("獲取折舊列表失敗");
    } finally {
      setLoading(false);
    }
  };

  // 加載部門資料
  const loadDepartments = async () => {
    try {
      const response = await getDepartments();
      if (response.success && response.data) {
        setDepartments(response.data);
      } else {
        notifyError("獲取部門列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取部門列表失敗", "請稍後再試");
    }
  };

  // 加載財產科目資料
  const loadAssetAccounts = async () => {
    try {
      const response = await getAssetAccounts();
      if (response.success && response.data) {
        setAssetAccounts(response.data);
      } else {
        notifyError("獲取財產科目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產科目列表失敗", "請稍後再試");
    }
  };

  // 加載財產子目資料
  const loadAssetSubAccounts = async () => {
    try {
      const response = await getAssetSubAccounts();
      if (response.success && response.data) {
        setAssetSubAccounts(response.data);
      } else {
        notifyError("獲取財產子目列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取財產子目列表失敗", "請稍後再試");
    }
  };

  // 加載資產資料
  const loadAssets = async () => {
    try {
      setLoading(true);
      const response = await getAssets();
      if (response.success && response.data) {
        // 過濾出可用資產（未刪除的）
        const activeAssets = response.data
          .filter((item) => !item.asset.deleteTime)
          .map((item) => item.asset);

        setAssets(activeAssets);
        setFilteredAssets(activeAssets);
      } else {
        message.error(response.message || "獲取財產列表失敗");
      }
    } catch (error) {
      console.error("獲取財產列表失敗:", error);
      message.error("獲取財產列表失敗");
    } finally {
      setLoading(false);
    }
  };

  // 加載折舊方法 - 使用系統參數服務
  const loadDepreciationMethods = async () => {
    try {
      // 從系統參數獲取完整的折舊法資訊
      const systemResponse = await getDepreciationMethodsFromSystem();
      if (systemResponse.success && systemResponse.data) {
        setSystemDepreciationMethods(systemResponse.data);

        // 取得預設的折舊法
        const defaultMethod = systemResponse.data.find(
          (method: SystemParameter) => {
            try {
              const methodData = JSON.parse(method.parameterValue);
              return methodData.isDefault === true;
            } catch (error) {
              return false;
            }
          }
        );

        if (defaultMethod) {
          setDefaultDepreciationMethod(defaultMethod.parameterName);
        }

        // 折舊法名稱
        const methodNames = systemResponse.data.map(
          (method: SystemParameter) => method.parameterName
        );
        setDepreciationMethods(methodNames);
      } else {
        notifyError("獲取折舊方法列表失敗", systemResponse.message);
      }
    } catch (error) {
      notifyError("獲取折舊方法列表失敗", "請稍後再試");
    }
  };

  // 加載折舊單數據
  const loadDepreciationForms = async () => {
    try {
      setLoading(true);
      const response = await getDepreciationForms();
      if (response.success && response.data) {
        const activeDepreciationForms = response.data.filter(
          (item) => !item.deleteTime
        );

        // 按年月排序 - 先按年份降序，同年份按月份降序
        const sortedDepreciationForms = activeDepreciationForms.sort((a, b) => {
          if (a.depreciationYear !== b.depreciationYear) {
            return b.depreciationYear - a.depreciationYear;
          }
          return b.depreciationMonth - a.depreciationMonth;
        });

        setDepreciationForms(sortedDepreciationForms);
      } else {
        message.error(response.message || "獲取折舊單列表失敗");
      }
    } catch (error) {
      console.error("獲取折舊單列表失敗:", error);
      message.error("獲取折舊單列表失敗");
    } finally {
      setLoading(false);
    }
  };

  // 處理資產點選，顯示該資產的折舊記錄
  const handleAssetSelect = async (asset: Asset) => {
    try {
      setLoading(true);
      setSelectedAsset(asset);

      // 獲取所有折舊記錄
      const depreciationResponse = await getDepreciations();

      if (depreciationResponse.success && depreciationResponse.data) {
        // 過濾該資產的折舊記錄
        const relevantDepreciations = depreciationResponse.data.filter(
          (item) => !item.deleteTime && item.assetId === asset.assetId
        );

        setAssetDepreciations(relevantDepreciations);
        // 顯示詳情Modal
        setIsDetailsModalVisible(true);
      } else {
        setAssetDepreciations([]);
        // 顯示詳情Modal
        setIsDetailsModalVisible(true);
      }
    } catch (error) {
      console.error("載入折舊記錄失敗:", error);
      message.error("載入折舊記錄失敗");
    } finally {
      setLoading(false);
    }
  };

  // 資產搜尋
  const handleAssetSearch = (value: string) => {
    // 設置搜尋文字
    setSearchText(value);
    if (activeTab === "asset") {
      // 財產清單搜尋
      const filtered = assets.filter(
        (asset) =>
          asset.assetNo?.toLowerCase().includes(value.toLowerCase()) ||
          asset.assetName?.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredAssets(filtered);
    } else if (activeTab === "depreciationForm") {
      // 折舊單搜尋
      const filtered = depreciationForms.filter(
        (form) =>
          form.depreciationId?.toLowerCase().includes(value.toLowerCase()) ||
          form.notes?.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredDepreciationForms(filtered);
    }
  };

  // 處理查看折舊詳情
  const handleView = async (record: Depreciation) => {
    try {
      // 載入資產資訊
      if (record.assetId) {
        const assetResponse = await getAssetById(record.assetId);
        if (assetResponse.success && assetResponse.data?.asset) {
          setSelectedAsset(assetResponse.data.asset);
        }
      }

      // 設置狀態
      setIsFormModalVisible(true);
      setEditingDepreciation(record);
      setIsViewMode(true);
    } catch (error) {
      notifyError("載入資產詳情失敗", "請稍後再試");
    }
  };

  // 編輯折舊紀錄
  const handleEdit = async (record: Depreciation) => {
    try {
      // 載入資產資訊
      if (record.assetId) {
        const assetResponse = await getAssetById(record.assetId);
        if (assetResponse.success && assetResponse.data?.asset) {
          setSelectedAsset(assetResponse.data.asset);
        }
      }

      // 顯示表單
      setIsFormModalVisible(true);
      setEditingDepreciation(record);
      setIsViewMode(false);
    } catch (error) {
      notifyError("載入資產詳情失敗", "請稍後再試");
    }
  };

  // 處理重新計算折舊
  const handleCalculate = async (record: Depreciation) => {
    try {
      setLoading(true);

      // 計算折舊請求
      const request: DepreciationCalculateRequest = {
        assetId: record.assetId,
        year: record.depreciationYear,
        month: record.depreciationMonth,
        userId: user?.userId || "",
      };

      // 計算折舊
      const response = await calculateDepreciation(request);
      // 判斷是否成功
      if (response.success) {
        notifySuccess("折舊重算成功", response.message || "資產折舊已重新計算");
        // 重新載入折舊記錄
        await loadDepreciations();
      } else {
        notifyError("折舊重算失敗", response.message || "計算折舊時發生錯誤");
      }
    } catch (error) {
      notifyError("折舊重算失敗", "處理折舊計算時發生錯誤，請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 處理表單取消
  const handleFormCancel = () => {
    setIsFormModalVisible(false);
    setEditingDepreciation(null);
    setSelectedAsset(null);
    setIsViewMode(false);
  };

  // 表單成功提交
  const handleFormSuccess = async (depreciationData: Depreciation) => {
    try {
      setLoading(true);
      // 判斷是否為編輯模式
      if (editingDepreciation) {
        // 更新折舊記錄
        const response = await updateDepreciation(depreciationData);
        if (response.success) {
          notifySuccess("更新成功", response.message || "折舊記錄已更新");
          // 重新載入折舊記錄
          await loadDepreciations();

          // 關閉對話框並重置狀態
          setIsFormModalVisible(false);
          setEditingDepreciation(null);
          setSelectedAsset(null);
          mainForm.resetFields();
        } else {
          notifyError(
            "更新失敗",
            response.message || "更新折舊記錄時發生錯誤，請稍後再試"
          );
        }
      } else {
        // 新增折舊記錄
        const response = await createDepreciation(depreciationData);
        if (response.success) {
          notifySuccess("新增成功", response.message || "折舊記錄已新增");
          // 重新載入折舊記錄
          await loadDepreciations();

          // 關閉對話框並重置狀態
          setIsFormModalVisible(false);
          setEditingDepreciation(null);
          setSelectedAsset(null);
          mainForm.resetFields();
        } else {
          notifyError(
            "新增失敗",
            response.message || "新增折舊記錄時發生錯誤，請稍後再試"
          );
        }
      }
    } catch (error) {
      notifyError("操作失敗", "處理折舊記錄時發生錯誤，請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 根據年月篩選折舊記錄
  const filterDepreciationRecords = (year: number, month: number) => {
    const filtered = depreciations.filter(
      (item) =>
        item.depreciationYear === year && item.depreciationMonth === month
    );
    setFilteredDepreciationRecords(filtered);

    // 預設勾選本期的所有折舊記錄
    if (filtered.length > 0) {
      const currentPeriodIds = filtered.map((record) => record.depreciationId);
      setSelectedDepreciationIds(currentPeriodIds);
      updateSummaryInfo(currentPeriodIds);
    } else {
      // 如果沒有記錄，清空選擇
      setSelectedDepreciationIds([]);
      updateSummaryInfo([]);
    }
  };

  // 顯示新增折舊單表單
  const showAddDepreciationFormModal = () => {
    try {
      // 設置當前年月
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;

      // 檢查是否已存在當前年月的折舊單
      /* const existingForm = depreciationForms.find(
        (form) =>
          form.depreciationYear === currentYear &&
          form.depreciationMonth === currentMonth
      );

      if (existingForm) {
        // 如果已存在相同年月的折舊單，顯示提示訊息
        Modal.warning({
          title: "本期已存在折舊單",
          content: `${currentYear}年${currentMonth}月的折舊單已經存在，編號為 ${existingForm.depreciationFormId}。`,
          okText: "知道了",
        });
        return;
      } */

      // 重置表單狀態
      setIsEditingDepreciationForm(false);
      setSelectedDepreciationForm(null);
      setDepreciationFormYear(currentYear);
      setDepreciationFormMonth(currentMonth);

      // 篩選當前年月的折舊記錄
      const currentPeriodRecords = depreciations.filter(
        (item) =>
          item.depreciationYear === currentYear &&
          item.depreciationMonth === currentMonth
      );

      // 設置篩選後的記錄
      setFilteredDepreciationRecords(currentPeriodRecords);

      // 如果有記錄，預設全選
      if (currentPeriodRecords && currentPeriodRecords.length > 0) {
        // 獲取所有折舊ID
        const allIds = currentPeriodRecords.map(
          (record) => record.depreciationId
        );

        // 設置選中的ID列表
        setSelectedDepreciationIds(allIds);

        // 直接計算總計
        const summary = {
          count: currentPeriodRecords.length,
          totalOriginalAmount: currentPeriodRecords.reduce(
            (sum, item) => sum + (Number(item.originalAmount) || 0),
            0
          ),
          totalCurrentDepreciation: currentPeriodRecords.reduce(
            (sum, item) => sum + (Number(item.currentDepreciation) || 0),
            0
          ),
          totalAccumulatedDepreciation: currentPeriodRecords.reduce(
            (sum, item) => sum + (Number(item.accumulatedDepreciation) || 0),
            0
          ),
          totalBeginningBookValue: currentPeriodRecords.reduce(
            (sum, item) => sum + (Number(item.beginningBookValue) || 0),
            0
          ),
          totalEndingBookValue: currentPeriodRecords.reduce(
            (sum, item) => sum + (Number(item.endingBookValue) || 0),
            0
          ),
        };

        // 設置總計資訊
        setSummaryInfo(summary);
      } else {
        // 如果沒有找到記錄，清空選擇和總計
        setSelectedDepreciationIds([]);
        setSummaryInfo({
          count: 0,
          totalOriginalAmount: 0,
          totalCurrentDepreciation: 0,
          totalAccumulatedDepreciation: 0,
          totalBeginningBookValue: 0,
          totalEndingBookValue: 0,
        });
      }

      // 顯示折舊單表單
      setTimeout(() => {
        setIsDepreciationFormModalVisible(true);
      }, 100);
    } catch (error) {
      console.error("開啟折舊單表單時發生錯誤:", error);
    }
  };

  // 處理折舊單編輯
  const handleEditDepreciationForm = (record: DepreciationForm) => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    setSelectedDepreciationForm(record);
    setIsEditingDepreciationForm(true);

    // 設置折舊年月
    setDepreciationFormYear(currentYear);
    setDepreciationFormMonth(currentMonth);

    // 預設篩選系統當前年月的折舊記錄，並自動勾選
    filterDepreciationRecords(currentYear, currentMonth);

    setIsDepreciationFormModalVisible(true);
  };

  // 處理折舊單查看明細
  const handleViewDetailDepreciationForm = (record: DepreciationForm) => {
    try {
      setLoading(true);
      setSelectedDepreciationForm(record);

      // 獲取所有折舊單資料
      getAllDepreciationForms()
        .then((response) => {
          if (response.success && response.data) {
            // 從所有折舊單中找到當前選中的折舊單
            const currentForm = response.data.find(
              (form) => form.depreciationFormId === record.depreciationFormId
            );

            if (currentForm && currentForm.depreciationFormDetail) {
              // 設置折舊單資料
              setSelectedDepreciationForm(currentForm);

              // 設置折舊單明細資料到表格中
              setFilteredDepreciationRecords(
                currentForm.depreciationFormDetail
              );

              // 選中所有明細記錄
              const detailIds = currentForm.depreciationFormDetail.map(
                (detail) => detail.depreciationId
              );
              setSelectedDepreciationIds(detailIds);

              // 計算並更新統計資訊
              const summary = {
                count: currentForm.depreciationFormDetail.length,
                totalOriginalAmount: currentForm.depreciationFormDetail.reduce(
                  (sum, item) => sum + (Number(item.originalAmount) || 0),
                  0
                ),
                totalCurrentDepreciation:
                  currentForm.depreciationFormDetail.reduce(
                    (sum, item) =>
                      sum + (Number(item.currentDepreciation) || 0),
                    0
                  ),
                totalAccumulatedDepreciation:
                  currentForm.depreciationFormDetail.reduce(
                    (sum, item) =>
                      sum + (Number(item.accumulatedDepreciation) || 0),
                    0
                  ),
                totalBeginningBookValue:
                  currentForm.depreciationFormDetail.reduce(
                    (sum, item) => sum + (Number(item.beginningBookValue) || 0),
                    0
                  ),
                totalEndingBookValue: currentForm.depreciationFormDetail.reduce(
                  (sum, item) => sum + (Number(item.endingBookValue) || 0),
                  0
                ),
              };
              setSummaryInfo(summary);

              // 設置年月
              setDepreciationFormYear(currentForm.depreciationYear);
              setDepreciationFormMonth(currentForm.depreciationMonth);
              //加入設置折舊日期
              setDepreciationFormDate(currentForm.depreciationDate);

              // 設置為查看模式
              setIsEditingDepreciationForm(false);

              // 顯示表單
              setIsDepreciationFormModalVisible(true);
            } else {
              message.error("未找到折舊單明細資料");
              setLoading(false);
            }
          } else {
            message.error(response.message || "獲取折舊單資料失敗");
            setLoading(false);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      console.error("查看折舊單明細失敗:", error);
      message.error("查看折舊單明細失敗");
      setLoading(false);
    }
  };

  // 計算並更新選中記錄的統計資訊
  const updateSummaryInfo = (selectedIds: string[]) => {
    if (!selectedIds.length) {
      setSummaryInfo({
        count: 0,
        totalOriginalAmount: 0,
        totalCurrentDepreciation: 0,
        totalAccumulatedDepreciation: 0,
        totalBeginningBookValue: 0,
        totalEndingBookValue: 0,
      });
      return;
    }

    const selectedRecords = filteredDepreciationRecords.filter((record) =>
      selectedIds.includes(record.depreciationId)
    );

    const summary = {
      count: selectedRecords.length,
      totalOriginalAmount: selectedRecords.reduce(
        (sum, item) => sum + (item.originalAmount || 0),
        0
      ),
      totalCurrentDepreciation: selectedRecords.reduce(
        (sum, item) => sum + (item.currentDepreciation || 0),
        0
      ),
      totalAccumulatedDepreciation: selectedRecords.reduce(
        (sum, item) => sum + (item.accumulatedDepreciation || 0),
        0
      ),
      totalBeginningBookValue: selectedRecords.reduce(
        (sum, item) => sum + (item.beginningBookValue || 0),
        0
      ),
      totalEndingBookValue: selectedRecords.reduce(
        (sum, item) => sum + (item.endingBookValue || 0),
        0
      ),
    };

    setSummaryInfo(summary);
  };

  // 處理折舊記錄選擇
  const handleDepreciationRecordSelect = (
    depreciationId: string,
    checked: boolean
  ) => {
    let newSelectedIds: string[];

    if (checked) {
      newSelectedIds = [...selectedDepreciationIds, depreciationId];
    } else {
      newSelectedIds = selectedDepreciationIds.filter(
        (id) => id !== depreciationId
      );
    }

    setSelectedDepreciationIds(newSelectedIds);
    updateSummaryInfo(newSelectedIds);
  };

  // 處理全選/取消全選
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = filteredDepreciationRecords.map(
        (record) => record.depreciationId
      );
      setSelectedDepreciationIds(allIds);
      updateSummaryInfo(allIds);
    } else {
      setSelectedDepreciationIds([]);
      updateSummaryInfo([]);
    }
  };

  // 處理折舊年變更
  const handleDepreciationYearChange = (value: number | null) => {
    if (value) {
      setDepreciationFormYear(value);
      filterDepreciationRecords(value, depreciationFormMonth);
    }
  };

  // 處理折舊月變更
  const handleDepreciationMonthChange = (value: number | null) => {
    if (value) {
      setDepreciationFormMonth(value);
      filterDepreciationRecords(depreciationFormYear, value);
    }
  };

  // 限制日期選擇在當前選擇的年月
  const disabledDate = (current: dayjs.Dayjs) => {
    if (!current) return false;
    // 獲取選擇的年月的起始和結束時間
    const startOfMonth = dayjs()
      .year(depreciationFormYear)
      .month(depreciationFormMonth - 1)
      .startOf("month");
    const endOfMonth = dayjs()
      .year(depreciationFormYear)
      .month(depreciationFormMonth - 1)
      .endOf("month");

    // 只允許選擇當前選擇年月內的日期
    return (
      current.isBefore(startOfMonth, "day") ||
      current.isAfter(endOfMonth, "day")
    );
  };

  // 格式化折舊金額
  const formatDepreciationInfo = (record: Depreciation) => {
    return `${record.assetName} (${record.assetNo}) - 原值:${formatTWCurrency(
      record.originalAmount
    )} 本次折舊:${formatTWCurrency(
      record.currentDepreciation
    )} 累計折舊:${formatTWCurrency(record.accumulatedDepreciation)}`;
  };

  // 處理頁碼變更
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  // 處理每頁顯示數量變更
  const handlePageSizeChange = (current: number, size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 獲取當前頁數據
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAssets.slice(startIndex, endIndex);
  };

  // 處理篩選值變更
  const handleFilterValueChange = (filterKey: string, value: any) => {
    setFilterValues({
      ...filterValues,
      [filterKey]: value,
    });
  };

  // 篩選器處理 - 針對資產做調整
  const applyFilters = useCallback(() => {
    let filtered = [...assets];

    // 篩選器
    activeFilters.forEach((filterKey) => {
      const value = filterValues[filterKey];
      if (value !== undefined && value !== null && value !== "") {
        switch (filterKey) {
          case "assetNo":
            filtered = filtered.filter((asset) =>
              asset.assetNo.toLowerCase().includes(value.toLowerCase())
            );
            break;
          case "assetName":
            filtered = filtered.filter((asset) =>
              asset.assetName.toLowerCase().includes(value.toLowerCase())
            );
            break;
          case "assetAccountId":
            filtered = filtered.filter(
              (asset) => asset.assetAccountId === value
            );
            break;
          case "assetSubAccountId":
            filtered = filtered.filter(
              (asset) => asset.assetSubAccountId === value
            );
            break;
          case "departmentId":
            filtered = filtered.filter((asset) => asset.departmentId === value);
            break;
          default:
            break;
        }
      }
    });

    setFilteredAssets(filtered);
    setTotalCount(filtered.length);
    setCurrentPage(1);
  }, [activeFilters, assets, filterValues]);

  // 更新篩選器
  useEffect(() => {
    applyFilters();
  }, [activeFilters, filterValues, applyFilters]);

  // 處理關閉資產折舊詳情模態框
  const handleDetailsModalClose = () => {
    setIsDetailsModalVisible(false);
    setAssetDepreciations([]);
  };

  // 歷史折舊清單的列定義
  const historicalColumns = [
    {
      key: "index",
      width: 80,
      render: (_: any, __: any, index: number) => (
        <span>{(currentPage - 1) * pageSize + index + 1}</span>
      ),
    },
    {
      title: "折舊年月",
      key: "depreciationYearMonth",
      render: (_: any, record: Depreciation) => (
        <span>{`${record.depreciationYear}年${record.depreciationMonth}月`}</span>
      ),
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
    },
    {
      title: "折舊方法",
      dataIndex: "depreciationMethod",
      key: "depreciationMethod",
    },
    {
      title: "本次折舊",
      dataIndex: "currentDepreciation",
      key: "currentDepreciation",
      render: (text: number) => (
        <Tag color="blue">{formatTWCurrency(text)}</Tag>
      ),
    },
    {
      title: "累計折舊",
      dataIndex: "accumulatedDepreciation",
      key: "accumulatedDepreciation",
      render: (text: number) => (
        <Tag color="purple">{formatTWCurrency(text)}</Tag>
      ),
    },
    {
      title: "執行日期",
      dataIndex: "executionDepreciationDate",
      key: "executionDepreciationDate",
      render: (text: number) => (
        <span>{DateTimeExtensions.formatDateFromTimestamp(text)}</span>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: Depreciation) => (
        <Button
          type="link"
          icon={<FileTextOutlined />}
          onClick={() => handleView(record)}
        >
          查看
        </Button>
      ),
    },
  ];

  // 當前折舊清單的列定義
  const currentColumns = [
    {
      key: "index",
      width: 80,
      render: (_: any, __: any, index: number) => (
        <span>{(currentPage - 1) * pageSize + index + 1}</span>
      ),
    },
    {
      title: "財產編號",
      dataIndex: "assetNo",
      key: "assetNo",
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
    },
    {
      title: "部門",
      key: "department",
      render: (_: any, record: Asset) => {
        const dept = departments.find(
          (d) => d.departmentId === record.departmentId
        );
        return dept ? dept.name : "-";
      },
    },
    {
      title: "財產科目",
      key: "assetAccount",
      render: (_: any, record: Asset) => {
        const account = assetAccounts.find(
          (a) => a.assetAccountId === record.assetAccountId
        );
        return account ? account.assetAccountName : "-";
      },
    },
    {
      title: "購入金額",
      dataIndex: "purchaseAmount",
      key: "purchaseAmount",
      render: (text: number) => <Tag color="red">{formatTWCurrency(text)}</Tag>,
    },
    {
      title: "累計折舊",
      dataIndex: "accumulatedDepreciationAmount",
      key: "accumulatedDepreciationAmount",
      render: (text: number) => (
        <Tag color="blue">{formatTWCurrency(text)}</Tag>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: Asset) => (
        <Button
          type="link"
          icon={<FileTextOutlined />}
          onClick={() => handleAssetSelect(record)}
        >
          查看折舊紀錄
        </Button>
      ),
    },
  ];

  // 折舊單表格列定義
  const depreciationFormColumns = [
    {
      key: "index",
      width: 60,
      render: (_: any, __: any, index: number) => <span>{index + 1}</span>,
    },
    {
      title: "折舊單編號",
      dataIndex: "depreciationFormId",
      key: "depreciationFormId",
    },
    {
      title: "折舊年月",
      key: "depreciationYearMonth",
      render: (_: any, record: DepreciationForm) => (
        <span>{`${record.depreciationYear}年${record.depreciationMonth}月`}</span>
      ),
    },
    {
      title: "折舊日期",
      dataIndex: "depreciationDate",
      key: "depreciationDate",
      render: (date: number) => (
        <span>
          {date ? DateTimeExtensions.formatDateFromTimestamp(date) : "-"}
        </span>
      ),
    },
    {
      title: "備註",
      dataIndex: "notes",
      key: "notes",
      render: (text: string) => <span>{text || "-"}</span>,
    },
    {
      title: "建立者",
      dataIndex: "createUserName",
      key: "createUserName",
    },
    {
      title: "建立時間",
      dataIndex: "createTime",
      key: "createTime",
      render: (time: number) => (
        <span>{DateTimeExtensions.formatDateFromTimestamp(time)}</span>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: DepreciationForm) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<FileTextOutlined />}
            onClick={() => handleViewDetailDepreciationForm(record)}
          >
            查看明細
          </Button>
          <Popconfirm
            title="確定要刪除此折舊單?"
            icon={<ExclamationCircleOutlined style={{ color: "red" }} />}
            onConfirm={() => handleDeleteDepreciationForm(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 篩選後的折舊單數據
  const [filteredDepreciationForms, setFilteredDepreciationForms] = useState<
    DepreciationForm[]
  >([]);

  // 初始設置篩選後的折舊單數據
  useEffect(() => {
    setFilteredDepreciationForms(depreciationForms);
  }, [depreciationForms]);

  // 處理折舊單查看
  const handleViewDepreciationForm = async (record: DepreciationForm) => {
    try {
      setLoading(true);
      setSelectedDepreciationForm(record);

      // 獲取折舊單明細
      const response = await getDepreciationFormDetail(record.depreciationId);
      if (response.success && response.data) {
        setDepreciationFormDetail(response.data);
        setIsFormDetailModalVisible(true);
      } else {
        message.error(response.message || "獲取折舊單明細失敗");
      }
    } catch (error) {
      console.error("獲取折舊單明細失敗:", error);
      message.error("獲取折舊單明細失敗");
    } finally {
      setLoading(false);
    }
  };

  // 處理折舊單刪除
  const handleDeleteDepreciationForm = async (record: DepreciationForm) => {
    try {
      setLoading(true);

      // 檢查折舊單是否已被使用
      const checkResponse = await checkDepreciationUsage(record.depreciationId);
      if (checkResponse.success && checkResponse.data === true) {
        message.error("此折舊單已被使用，無法刪除");
        return;
      }

      // 執行刪除
      const response = await deleteDepreciationForm(record);
      if (response.success) {
        message.success("折舊單刪除成功");
        loadDepreciationForms(); // 重新加載數據
      } else {
        message.error(response.message || "折舊單刪除失敗");
      }
    } catch (error) {
      console.error("折舊單刪除失敗:", error);
      message.error("折舊單刪除失敗");
    } finally {
      setLoading(false);
    }
  };

  // 處理關閉折舊單詳情
  const handleCloseDepreciationFormDetail = () => {
    setIsFormDetailModalVisible(false);
    setSelectedDepreciationForm(null);
    setDepreciationFormDetail(null);
  };

  // 處理關閉折舊單表單
  const handleCloseDepreciationFormModal = () => {
    setIsDepreciationFormModalVisible(false);
    setSelectedDepreciationForm(null);
    setIsEditingDepreciationForm(false);
  };

  // 處理折舊單表單提交
  const handleDepreciationFormSubmit = async (values: any) => {
    try {
      // 確認是否選擇了折舊清單
      if (selectedDepreciationIds.length === 0) {
        message.error("請至少選擸一筆折舊清單");
        return;
      }

      // 獲取選中的折舊清單
      const selectedRecords = filteredDepreciationRecords.filter((record) =>
        selectedDepreciationIds.includes(record.depreciationId)
      );

      // 使用 state 中的值，而不是表單值
      const formYear = depreciationFormYear;
      const formMonth = depreciationFormMonth;

      // 顯示確認視窗
      Modal.confirm({
        title: `${isEditingDepreciationForm ? "更新" : "新增"}折舊單確認`,
        width: 1200,
        icon: <ExclamationCircleOutlined />,
        content: (
          <div style={{ maxHeight: "600px", overflow: "auto" }}>
            <Descriptions title="折舊單資訊" bordered column={2} size="small">
              <Descriptions.Item label="折舊年月" span={2}>
                {`${formYear}年${formMonth}月`}
              </Descriptions.Item>
              <Descriptions.Item label="折舊日期" span={2}>
                {values.depreciationDate
                  ? values.depreciationDate.format("YYYY-MM-DD")
                  : ""}
              </Descriptions.Item>
              <Descriptions.Item label="備註" span={2}>
                {values.notes || "無"}
              </Descriptions.Item>
            </Descriptions>

            <Divider orientation="left">
              折舊清單 ({selectedRecords.length}筆)
            </Divider>

            <Table
              dataSource={selectedRecords}
              columns={[
                {
                  key: "index",
                  width: 60,
                  render: (_, __, index) => index + 1,
                },
                {
                  title: "資產編號",
                  dataIndex: "assetNo",
                  key: "assetNo",
                  width: 120,
                },
                {
                  title: "資產名稱",
                  dataIndex: "assetName",
                  key: "assetName",
                  width: 180,
                },
                {
                  title: "原始金額",
                  dataIndex: "originalAmount",
                  key: "originalAmount",
                  width: 120,
                  render: (text) => formatTWCurrency(text),
                },
                {
                  title: "期初帳面價值",
                  dataIndex: "beginningBookValue",
                  key: "beginningBookValue",
                  width: 140,
                  render: (text) => formatTWCurrency(text),
                },
                {
                  title: "本次折舊",
                  dataIndex: "currentDepreciation",
                  key: "currentDepreciation",
                  width: 120,
                  render: (text) => formatTWCurrency(text),
                },
                {
                  title: "累計折舊",
                  dataIndex: "accumulatedDepreciation",
                  key: "accumulatedDepreciation",
                  width: 120,
                  render: (text) => formatTWCurrency(text),
                },
                {
                  title: "期末帳面價值",
                  dataIndex: "endingBookValue",
                  key: "endingBookValue",
                  width: 140,
                  render: (text) => formatTWCurrency(text),
                },
              ]}
              rowKey="depreciationId"
              pagination={false}
              size="small"
              scroll={{ y: 400 }}
            />

            <Divider orientation="left">總計資訊</Divider>
            <div
              style={{
                backgroundColor: "#f5f5f5",
                padding: "16px",
                borderRadius: "6px",
                marginBottom: "16px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "24px",
                  marginBottom: "16px",
                }}
              >
                <Statistic
                  title="總資產數量"
                  value={summaryInfo.count}
                  precision={0}
                  formatter={(value) => `${value} 筆`}
                />
                <Statistic
                  title="總原始金額"
                  value={summaryInfo.totalOriginalAmount}
                  precision={0}
                  formatter={(value) => formatTWCurrency(value as number)}
                />
                <Statistic
                  title="總期初帳面價值"
                  value={summaryInfo.totalBeginningBookValue}
                  precision={0}
                  formatter={(value) => formatTWCurrency(value as number)}
                />
              </div>
              <div style={{ display: "flex", flexWrap: "wrap", gap: "24px" }}>
                <Statistic
                  title="總本次折舊"
                  value={summaryInfo.totalCurrentDepreciation}
                  precision={0}
                  formatter={(value) => formatTWCurrency(value as number)}
                />
                <Statistic
                  title="總累計折舊"
                  value={summaryInfo.totalAccumulatedDepreciation}
                  precision={0}
                  formatter={(value) => formatTWCurrency(value as number)}
                />
                <Statistic
                  title="總期末帳面價值"
                  value={summaryInfo.totalEndingBookValue}
                  precision={0}
                  formatter={(value) => formatTWCurrency(value as number)}
                />
              </div>
            </div>
          </div>
        ),
        okText: "確認送出",
        cancelText: "取消",
        onOk: async () => {
          setLoading(true);
          try {
            // 為選中的折舊記錄創建折舊單
            const formData = {
              createTime: Date.now(),
              createUserId: user?.userId || "",
              updateTime: 0,
              updateUserId: "",
              deleteTime: 0,
              deleteUserId: "",
              isDeleted: false,
              depreciationFormId: "", // 由後端生成
              depreciationId: selectedDepreciationIds[0], // 使用第一筆記錄的ID
              depreciationDate: values.depreciationDate
                ? Math.floor(values.depreciationDate.valueOf() / 1000)
                : Math.floor(Date.now() / 1000),
              depreciationYear: formYear,
              depreciationMonth: formMonth,
              notes: values.notes || "",
              depreciationFormDetail: selectedDepreciationIds.map(
                (depreciationId) => {
                  const selectedRecord = filteredDepreciationRecords.find(
                    (record) => record.depreciationId === depreciationId
                  );

                  if (!selectedRecord) {
                    throw new Error(`找不到折舊記錄: ${depreciationId}`);
                  }

                  return {
                    ...selectedRecord,
                    updateUserId: user?.userId || "",
                    depreciationDate: values.depreciationDate
                      ? Math.floor(values.depreciationDate.valueOf() / 1000)
                      : Math.floor(Date.now() / 1000),
                  };
                }
              ),
            };

            let response;
            if (isEditingDepreciationForm && selectedDepreciationForm) {
              // 更新
              response = await updateDepreciationForm({
                ...formData,
                depreciationFormId: selectedDepreciationForm.depreciationFormId,
                updateTime: Date.now(),
                updateUserId: user?.userId || "",
              });
            } else {
              // 新增
              response = await createDepreciationForm(formData);
            }

            if (response.success) {
              message.success(
                isEditingDepreciationForm ? "折舊單更新成功" : `成功新增折舊單`
              );
              setIsDepreciationFormModalVisible(false);
              loadDepreciationForms(); // 重新加載數據
            } else {
              message.error(
                `${
                  isEditingDepreciationForm ? "更新" : "新增"
                }折舊單時發生錯誤: ${response.message || "未知錯誤"}`
              );
            }
          } catch (error) {
            console.error(
              isEditingDepreciationForm ? "折舊單更新失敗:" : "折舊單新增失敗:",
              error
            );
            message.error(
              isEditingDepreciationForm ? "折舊單更新失敗" : "折舊單新增失敗"
            );
          } finally {
            setLoading(false);
          }
        },
      });
      return;
    } catch (error) {
      console.error(
        isEditingDepreciationForm ? "折舊單更新失敗:" : "折舊單新增失敗:",
        error
      );
      message.error(
        isEditingDepreciationForm ? "折舊單更新失敗" : "折舊單新增失敗"
      );
    }
  };

  // 折舊記錄表格列定義
  const depreciationRecordColumns = [
    {
      title: (
        <Checkbox
          indeterminate={
            selectedDepreciationIds.length > 0 &&
            selectedDepreciationIds.length < filteredDepreciationRecords.length
          }
          checked={
            filteredDepreciationRecords.length > 0 &&
            selectedDepreciationIds.length ===
              filteredDepreciationRecords.length
          }
          onChange={(e) => handleSelectAll(e.target.checked)}
          disabled={
            !isEditingDepreciationForm && selectedDepreciationForm !== null
          }
        />
      ),
      key: "select",
      width: 50,
      render: (_: any, record: Depreciation) =>
        !isEditingDepreciationForm &&
        selectedDepreciationForm !== null ? null : (
          <Checkbox
            checked={selectedDepreciationIds.includes(record.depreciationId)}
            onChange={(e) =>
              handleDepreciationRecordSelect(
                record.depreciationId,
                e.target.checked
              )
            }
          />
        ),
    },
    {
      dataIndex: "index",
      key: "index",
      width: 50,
      render: (_: any, __: any, index: number) => <span>{index + 1}</span>,
    },
    {
      title: "資產編號",
      dataIndex: "assetNo",
      key: "assetNo",
      width: 120,
    },
    {
      title: "資產名稱",
      dataIndex: "assetName",
      key: "assetName",
      width: 180,
    },
    {
      title: "原始金額",
      dataIndex: "originalAmount",
      key: "originalAmount",
      width: 120,
      render: (text: number) => formatTWCurrency(text),
    },
    {
      title: "本次折舊",
      dataIndex: "currentDepreciation",
      key: "currentDepreciation",
      width: 120,
      render: (text: number) => formatTWCurrency(text),
    },
    {
      title: "累計折舊",
      dataIndex: "accumulatedDepreciation",
      key: "accumulatedDepreciation",
      width: 120,
      render: (text: number) => formatTWCurrency(text),
    },
    {
      title: "期初帳面價值",
      dataIndex: "beginningBookValue",
      key: "beginningBookValue",
      width: 140,
      render: (text: number) => formatTWCurrency(text),
    },
    {
      title: "期末帳面價值",
      dataIndex: "endingBookValue",
      key: "endingBookValue",
      width: 140,
      render: (text: number) => formatTWCurrency(text),
    },
  ];

  return (
    <div style={{ padding: "20px" }}>
      <Card title="固定資產折舊作業">
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key)}
          items={[
            {
              key: "depreciationForm",
              label: "固定資產折舊單",
              children: (
                <>
                  {/* 固定資產折舊單內容 */}
                  <div
                    style={{
                      marginBottom: 16,
                      display: "flex",
                      flexWrap: "wrap",
                      alignItems: "center",
                      gap: 8,
                    }}
                  >
                    <Select
                      placeholder="選擇年份"
                      style={{ width: 120 }}
                      allowClear
                      value={yearFilter}
                      onChange={handleYearFilterChange}
                      options={Array.from(
                        new Set(
                          depreciationForms.map((form) => form.depreciationYear)
                        )
                      )
                        .sort((a, b) => b - a) // 降序排列，最新年份優先
                        .map((year) => ({ value: year, label: `${year}年` }))}
                    />
                    <Select
                      placeholder="選擇月份"
                      style={{ width: 120 }}
                      allowClear
                      value={monthFilter}
                      onChange={handleMonthFilterChange}
                      options={Array.from({ length: 12 }, (_, i) => ({
                        value: i + 1,
                        label: `${i + 1}月`,
                      }))}
                    />
                    {(yearFilter || monthFilter || searchText) && (
                      <Button
                        icon={<ClearOutlined />}
                        onClick={clearDepreciationFilters}
                      >
                        清除篩選
                      </Button>
                    )}
                    <Input
                      placeholder="搜尋折舊單編號或備註"
                      value={searchText}
                      onChange={(e) => handleAssetSearch(e.target.value)}
                      style={{ width: 200 }}
                      prefix={<SearchOutlined />}
                    />
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={showAddDepreciationFormModal}
                      style={{ marginLeft: "auto" }}
                    >
                      新增折舊單
                    </Button>
                  </div>
                  <Table
                    dataSource={filteredDepreciationForms}
                    columns={depreciationFormColumns}
                    rowKey="depreciationFormId"
                    loading={loading}
                    pagination={{
                      current: currentPage,
                      pageSize: pageSize,
                      total: filteredDepreciationForms.length,
                      onChange: handlePageChange,
                      onShowSizeChange: handlePageSizeChange,
                    }}
                  />
                </>
              ),
            },
            {
              key: "asset",
              label: "財產清單",
              children: (
                <>
                  {/* 財產清單內容 */}
                  <div style={{ marginBottom: 16 }}>
                    <Space>
                      <Input
                        placeholder="搜尋財產編號或名稱"
                        value={searchText}
                        onChange={(e) => handleAssetSearch(e.target.value)}
                        style={{ width: 200 }}
                        prefix={<SearchOutlined />}
                      />
                    </Space>
                  </div>
                  <Table
                    dataSource={filteredAssets}
                    columns={currentColumns}
                    rowKey="assetId"
                    loading={loading}
                    pagination={{
                      current: currentPage,
                      pageSize: pageSize,
                      total: totalCount,
                      onChange: handlePageChange,
                      onShowSizeChange: handlePageSizeChange,
                    }}
                  />
                </>
              ),
            },
          ]}
        />
      </Card>

      {/* 折舊單詳情Modal */}
      <Modal
        title={`折舊單詳情 - ${selectedDepreciationForm?.depreciationYear}年${selectedDepreciationForm?.depreciationMonth}月`}
        open={isFormDetailModalVisible}
        onCancel={handleCloseDepreciationFormDetail}
        width={800}
        footer={[
          <Button key="back" onClick={handleCloseDepreciationFormDetail}>
            關閉
          </Button>,
        ]}
      >
        {depreciationFormDetail ? (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="折舊單編號" span={2}>
              {selectedDepreciationForm?.depreciationFormId}
            </Descriptions.Item>
            <Descriptions.Item label="折舊年月">
              {`${selectedDepreciationForm?.depreciationYear}年${selectedDepreciationForm?.depreciationMonth}月`}
            </Descriptions.Item>
            <Descriptions.Item label="折舊日期">
              {selectedDepreciationForm?.depreciationDate
                ? DateTimeExtensions.formatDateFromTimestamp(
                    selectedDepreciationForm.depreciationDate
                  )
                : "-"}
            </Descriptions.Item>
            <Descriptions.Item label="資產編號">
              {depreciationFormDetail.assetNo}
            </Descriptions.Item>
            <Descriptions.Item label="資產名稱">
              {depreciationFormDetail.assetName}
            </Descriptions.Item>
            <Descriptions.Item label="折舊方法">
              {depreciationFormDetail.depreciationMethod}
            </Descriptions.Item>
            <Descriptions.Item label="折舊率">
              {`${(depreciationFormDetail.depreciationRate * 100).toFixed(2)}%`}
            </Descriptions.Item>
            <Descriptions.Item label="原始金額">
              {formatTWCurrency(depreciationFormDetail.originalAmount)}
            </Descriptions.Item>
            <Descriptions.Item label="累計折舊">
              {formatTWCurrency(depreciationFormDetail.accumulatedDepreciation)}
            </Descriptions.Item>
            <Descriptions.Item label="本次折舊">
              {formatTWCurrency(depreciationFormDetail.currentDepreciation)}
            </Descriptions.Item>
            <Descriptions.Item label="剩餘年限">
              {depreciationFormDetail.serviceLifeRemaining}
            </Descriptions.Item>
            <Descriptions.Item label="期初帳面價值">
              {formatTWCurrency(depreciationFormDetail.beginningBookValue)}
            </Descriptions.Item>
            <Descriptions.Item label="期末帳面價值">
              {formatTWCurrency(depreciationFormDetail.endingBookValue)}
            </Descriptions.Item>
            <Descriptions.Item label="是否調整" span={2}>
              {depreciationFormDetail.isAdjustment ? "是" : "否"}
            </Descriptions.Item>
            {depreciationFormDetail.isAdjustment && (
              <Descriptions.Item label="調整原因" span={2}>
                {depreciationFormDetail.adjustmentReason || "-"}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="備註" span={2}>
              {depreciationFormDetail.notes || "-"}
            </Descriptions.Item>
          </Descriptions>
        ) : (
          <div>無法取得折舊單明細資料</div>
        )}
      </Modal>

      {/* 折舊單表單Modal */}
      <Modal
        title={
          isEditingDepreciationForm
            ? "編輯折舊單"
            : isDepreciationFormModalVisible && selectedDepreciationForm
            ? "查看折舊單明細"
            : "新增折舊單"
        }
        open={isDepreciationFormModalVisible}
        onCancel={handleCloseDepreciationFormModal}
        footer={null}
        width={1200}
      >
        <Form
          layout="vertical"
          onFinish={handleDepreciationFormSubmit}
          initialValues={
            isEditingDepreciationForm && selectedDepreciationForm
              ? {
                  ...selectedDepreciationForm,
                  depreciationDate: selectedDepreciationForm.depreciationDate
                    ? dayjs(selectedDepreciationForm.depreciationDate)
                    : undefined,
                }
              : {
                  depreciationYear: depreciationFormYear,
                  depreciationMonth: depreciationFormMonth,
                  depreciationDate: dayjs(),
                }
          }
        >
          <Divider orientation="left">折舊單資訊</Divider>
          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item label="折舊年" style={{ flex: 1 }}>
              <Text style={{ fontSize: "26px" }} strong>
                {depreciationFormYear} 年
              </Text>
              <Form.Item name="depreciationYear" hidden={true}>
                <InputNumber value={depreciationFormYear} />
              </Form.Item>
            </Form.Item>
            <Form.Item label="折舊月" style={{ flex: 1 }}>
              {!isEditingDepreciationForm &&
              selectedDepreciationForm !== null ? (
                <Text style={{ fontSize: "26px" }} strong>
                  {selectedDepreciationForm.depreciationMonth} 月
                </Text>
              ) : (
                <Select
                  value={depreciationFormMonth}
                  onChange={(value) => {
                    // 設置折舊月份
                    setDepreciationFormMonth(value);

                    // 篩選該月的折舊記錄
                    const filtered = depreciations.filter(
                      (item) =>
                        item.depreciationYear === depreciationFormYear &&
                        item.depreciationMonth === value
                    );
                    setFilteredDepreciationRecords(filtered);

                    // 設置選中的折舊記錄
                    const allIds = filtered.map(
                      (record) => record.depreciationId
                    );
                    setSelectedDepreciationIds(allIds);

                    // 設置合計資訊
                    const summary = {
                      count: filtered.length,
                      totalOriginalAmount: filtered.reduce(
                        (sum, item) => sum + (Number(item.originalAmount) || 0),
                        0
                      ),
                      totalCurrentDepreciation: filtered.reduce(
                        (sum, item) =>
                          sum + (Number(item.currentDepreciation) || 0),
                        0
                      ),
                      totalAccumulatedDepreciation: filtered.reduce(
                        (sum, item) =>
                          sum + (Number(item.accumulatedDepreciation) || 0),
                        0
                      ),
                      totalBeginningBookValue: filtered.reduce(
                        (sum, item) =>
                          sum + (Number(item.beginningBookValue) || 0),
                        0
                      ),
                      totalEndingBookValue: filtered.reduce(
                        (sum, item) =>
                          sum + (Number(item.endingBookValue) || 0),
                        0
                      ),
                    };
                    setSummaryInfo(summary);
                  }}
                  style={{ width: "100%" }}
                  options={Array.from({ length: 12 }, (_, i) => {
                    const now = new Date();
                    const thisYear = now.getFullYear();
                    const thisMonth = now.getMonth() + 1;
                    // 只允許今年（含）之後的月份
                    const allow =
                      depreciationFormYear > thisYear ||
                      (depreciationFormYear === thisYear && i + 1 >= thisMonth);
                    return {
                      value: i + 1,
                      label: `${i + 1} 月`,
                      disabled: !allow,
                    };
                  })}
                  disabled={
                    isEditingDepreciationForm ||
                    selectedDepreciationForm !== null
                  }
                />
              )}
              <Form.Item name="depreciationMonth" hidden={true}>
                <InputNumber
                  value={depreciationFormMonth}
                  onChange={(value) => {
                    if (value) {
                      setDepreciationFormMonth(value);
                    }
                  }}
                />
              </Form.Item>
            </Form.Item>
            <Form.Item
              name="depreciationDate"
              label="折舊日期"
              rules={[{ required: true, message: "請選擇折舊日期" }]}
              style={{ flex: 1 }}
            >
              {!isEditingDepreciationForm &&
              selectedDepreciationForm !== null ? (
                <Text style={{ fontSize: "26px" }} strong>
                  {DateTimeExtensions.formatDateFromTimestamp(
                    selectedDepreciationForm.depreciationDate
                  )}
                </Text>
              ) : (
                <DatePicker
                  style={{ width: "100%" }}
                  disabledDate={disabledDate}
                  placeholder={`${depreciationFormYear}年${depreciationFormMonth}月`}
                />
              )}
            </Form.Item>
          </div>

          <Divider orientation="left">折舊清單</Divider>

          {filteredDepreciationRecords.length > 0 ? (
            <>
              <Table
                dataSource={filteredDepreciationRecords}
                columns={depreciationRecordColumns}
                rowKey="depreciationId"
                pagination={false}
                size="small"
                scroll={{ y: 600 }}
                rowClassName={(record) =>
                  selectedDepreciationIds.includes(record.depreciationId)
                    ? "ant-table-row-selected"
                    : ""
                }
                onRow={(record) => ({
                  onClick: () => {
                    if (!selectedDepreciationForm) {
                      // 只有在新增模式下才允許選擇
                      const isSelected = selectedDepreciationIds.includes(
                        record.depreciationId
                      );
                      handleDepreciationRecordSelect(
                        record.depreciationId,
                        !isSelected
                      );
                    }
                  },
                  style: {
                    cursor: selectedDepreciationForm ? "default" : "pointer",
                  },
                })}
              />

              {/* 選中記錄的統計資訊 */}
              {selectedDepreciationIds.length > 0 && (
                <div
                  style={{
                    marginTop: "16px",
                    backgroundColor: "#f5f5f5",
                    padding: "16px",
                    borderRadius: "6px",
                    marginBottom: "16px",
                  }}
                >
                  <div style={{ fontWeight: "bold", marginBottom: "16px" }}>
                    {selectedDepreciationForm ? "折舊單明細" : "已選擇"}{" "}
                    {summaryInfo.count} 筆記錄
                  </div>
                  <div
                    style={{
                      display: "flex",
                      flexWrap: "wrap",
                      gap: "24px",
                      marginBottom: "16px",
                    }}
                  >
                    <Statistic
                      title="總原始金額"
                      value={summaryInfo.totalOriginalAmount}
                      precision={0}
                      formatter={(value) => formatTWCurrency(value as number)}
                    />
                    <Statistic
                      title="總期初帳面價值"
                      value={summaryInfo.totalBeginningBookValue}
                      precision={0}
                      formatter={(value) => formatTWCurrency(value as number)}
                    />
                    <Statistic
                      title="總本次折舊"
                      value={summaryInfo.totalCurrentDepreciation}
                      precision={0}
                      formatter={(value) => formatTWCurrency(value as number)}
                    />
                  </div>
                  <div
                    style={{ display: "flex", flexWrap: "wrap", gap: "24px" }}
                  >
                    <Statistic
                      title="總累計折舊"
                      value={summaryInfo.totalAccumulatedDepreciation}
                      precision={0}
                      formatter={(value) => formatTWCurrency(value as number)}
                    />
                    <Statistic
                      title="總期末帳面價值"
                      value={summaryInfo.totalEndingBookValue}
                      precision={0}
                      formatter={(value) => formatTWCurrency(value as number)}
                    />
                    <Statistic
                      title="總資產數量"
                      value={summaryInfo.count}
                      precision={0}
                      formatter={(value) => `${value} 筆`}
                    />
                  </div>
                </div>
              )}
            </>
          ) : (
            <Alert
              message="沒有找到符合條件的折舊記錄"
              description="所選年月沒有可用的折舊記錄，請嘗試選擇其他年月。"
              type="info"
              showIcon
              style={{ marginBottom: "16px" }}
            />
          )}

          <Form.Item name="notes" label="備註" style={{ marginTop: "16px" }}>
            <Input.TextArea
              rows={3}
              placeholder="請輸入備註"
              disabled={
                !isEditingDepreciationForm && selectedDepreciationForm !== null
              }
            />
          </Form.Item>

          <Form.Item>
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <Space>
                <Button onClick={handleCloseDepreciationFormModal}>
                  {!isEditingDepreciationForm && selectedDepreciationForm
                    ? "關閉"
                    : "取消"}
                </Button>
                {(!selectedDepreciationForm || isEditingDepreciationForm) && (
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    disabled={
                      filteredDepreciationRecords.length === 0 ||
                      selectedDepreciationIds.length === 0
                    }
                  >
                    {isEditingDepreciationForm
                      ? "更新"
                      : `新增${
                          selectedDepreciationIds.length > 0
                            ? ` (${selectedDepreciationIds.length}筆)`
                            : ""
                        }`}
                  </Button>
                )}
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 折舊記錄詳情Modal */}
      <Modal
        title={`${selectedAsset?.assetName || ""} - 折舊記錄`}
        open={isDetailsModalVisible}
        onCancel={handleDetailsModalClose}
        width={1000}
        footer={[
          <Button key="back" onClick={handleDetailsModalClose}>
            關閉
          </Button>,
        ]}
      >
        {assetDepreciations.length > 0 ? (
          <Table
            dataSource={assetDepreciations}
            columns={[
              {
                key: "index",
                width: 60,
                render: (_, __, index) => <span>{index + 1}</span>,
              },
              {
                title: "折舊年月",
                key: "depreciationYearMonth",
                render: (_, record) => (
                  <span>{`${record.depreciationYear}年${record.depreciationMonth}月`}</span>
                ),
              },
              {
                title: "折舊方法",
                dataIndex: "depreciationMethod",
                key: "depreciationMethod",
              },
              {
                title: "原始金額",
                dataIndex: "originalAmount",
                key: "originalAmount",
                render: (text) => <span>{formatTWCurrency(text)}</span>,
              },
              {
                title: "期初帳面價值",
                dataIndex: "beginningBookValue",
                key: "beginningBookValue",
                render: (text) => <span>{formatTWCurrency(text)}</span>,
              },
              {
                title: "本次折舊",
                dataIndex: "currentDepreciation",
                key: "currentDepreciation",
                render: (text) => <span>{formatTWCurrency(text)}</span>,
              },
              {
                title: "累計折舊",
                dataIndex: "accumulatedDepreciation",
                key: "accumulatedDepreciation",
                render: (text) => <span>{formatTWCurrency(text)}</span>,
              },
              {
                title: "期末帳面價值",
                dataIndex: "endingBookValue",
                key: "endingBookValue",
                render: (text) => <span>{formatTWCurrency(text)}</span>,
              },
              {
                title: "折舊日期",
                dataIndex: "depreciationDate",
                key: "depreciationDate",
                render: (text) => (
                  <span>
                    {DateTimeExtensions.formatDateFromTimestamp(text)}
                  </span>
                ),
              },
            ]}
            rowKey="depreciationId"
            pagination={{ pageSize: 10 }}
          />
        ) : (
          <Alert
            message="無折舊記錄"
            description="此財產目前沒有折舊記錄。"
            type="info"
            showIcon
          />
        )}
      </Modal>

      {/* 折舊表單對話框 */}
      {isFormModalVisible && (
        <Modal
          title={
            isViewMode
              ? "檢視折舊"
              : editingDepreciation
              ? "編輯折舊"
              : "新增折舊"
          }
          open={isFormModalVisible}
          onCancel={handleFormCancel}
          width={isMobile ? "100%" : 900}
          style={isMobile ? { top: 0, margin: 0, maxWidth: "100vw" } : {}}
          styles={{
            body: isMobile
              ? { padding: "12px", maxHeight: "80vh", overflowY: "auto" }
              : {},
          }}
          footer={null}
          maskClosable={false}
          destroyOnClose
        >
          <AssetDepreciationForm
            editingDepreciation={editingDepreciation}
            isViewMode={isViewMode}
            selectedAsset={selectedAsset}
            onCancel={handleFormCancel}
            onSuccess={handleFormSuccess}
            departments={departments}
            assetAccounts={assetAccounts}
            assetSubAccounts={assetSubAccounts}
            depreciationMethods={depreciationMethods}
            isMobile={isMobile}
            form={mainForm}
          />
        </Modal>
      )}
    </div>
  );
};

export default AssetDepreciationFormPage;
