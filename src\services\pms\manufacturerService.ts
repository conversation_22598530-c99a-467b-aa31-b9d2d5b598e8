import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 製造商介面定義
export interface Manufacturer {
    manufacturerId: string;
    name: string;
    model: string;
    manufacturerName: string;
    supplier: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    description: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
    createUserName?: string;
    updateUserName?: string;
    deleteUserName?: string;
}

// 取得製造商列表
export async function getManufacturers(): Promise<ApiResponse<Manufacturer[]>> {
    try {
        const response = await httpClient(apiEndpoints.getManufacturers, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取製造商列表失敗",
            data: [],
        };
    }
}

// 取得製造商詳情
export async function getManufacturerById(id: string): Promise<ApiResponse<Manufacturer>> {
    try {
        const response = await httpClient(`${apiEndpoints.getManufacturerDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取製造商詳情失敗",
            data: undefined,
        };
    }
}

// 新增製造商
export async function addManufacturer(data: Partial<Manufacturer>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addManufacturer, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增製造商失敗",
        };
    }
}

// 編輯製造商
export async function editManufacturer(data: Partial<Manufacturer>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editManufacturer, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯製造商失敗",
        };
    }
}

// 刪除製造商
export async function deleteManufacturer(data: Partial<Manufacturer>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteManufacturer, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });

        console.log(JSON.stringify(data));
        console.log(response);

        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除製造商失敗",
        };
    }
}
