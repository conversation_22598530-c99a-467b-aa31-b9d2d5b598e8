import { AssetSubAccount } from './assetSubAccountService';
import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";
import { getCookie } from '@/utils/cookies';

// 附屬設備
export interface AccessoryEquipment {
    accessoryEquipmentId: string;
    equipmentNo: string;
    equipmentName: string;
    equipmentType: string;
    specification: string;
    purchaseDate: number;
    purchasePrice: number;
    assetId: string;
    asset?: any;
    usageStatus: string;
    remarks: string;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
}

// 保險單位
export interface InsuranceUnit {
    insuranceUnitId: string;
    name: string;
    companyNo: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    address: string;
    website: string;
    description: string;
    sortCode: number;
    insuranceAmount: number;
    insuranceStartDate: number;
    insuranceExpiryDate: number;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
    assetInsuranceUnits: any[];
}

// 攤提來源
export interface AmortizationSource {
    amortizationSourceId: string;
    departmentId: string;
    departmentName?: string;
    sourceName: string;
    description: string;
    amount: number;
    createTime: number;
    createUserId: string;
    createUserName?: string;
    updateTime?: number;
    updateUserId?: string;
    updateUserName?: string;
    deleteTime?: number;
    deleteUserId?: string;
    deleteUserName?: string;
    assetAmortizationSources: any[];
}

// 財產來源
export interface AssetSource {
    assetSourceId: string;
    assetSourceName: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    createUserName?: string;
    updateTime?: number;
    updateUserId?: string;
    updateUserName?: string;
    deleteTime?: number;
    deleteUserId?: string;
    deleteUserName?: string;
    assetAssetSources: any[];
}

// 財產
export interface Asset {
    assetId: string;
    assetNo: string;
    assetName: string;
    assetShortName?: string;
    acquisitionDate: number;
    quantity: number;
    unitId: string;
    purchaseAmount: number;
    subsidyAmount: number;
    estimatedResidualValue: number;
    departmentId: string;
    divisionId: string;
    custodianId: string;
    userId: string;
    username?: string;
    assetStatusId: string;
    statusChangeDate: number;
    usage: string;
    notes?: string;
    storageLocationId: string;
    serviceLife: number;
    depreciationAmount: number;
    accumulatedDepreciationAmount: number;
    insurancePeriod: number;
    manufacturerId: string;
    specification?: string;
    buildingAddress?: string;
    buildingStructure?: string;
    constructionDate?: number;
    floorArea?: number;
    publicArea?: number;
    usageExpiryDate?: number;
    usageLicenseNo?: string;
    buildingTaxItem?: string;
    publicValue?: number;
    landSection?: string;
    landLocation?: string;
    landNumber?: string;
    landArea?: number;
    certificateNo?: string;
    assetAccountId: string;
    assetSubAccountId: string;
    assetCategoryId: string;
    equipmentTypeId: string;
    unitPrice: number;
    customAssetNo1?: string;
    customAssetNo2?: string;
    scrapReason?: string;
    scrapDate: number;
    estimatedScrapYear?: number;
    usableAfterScrap?: string;
    createTime: number;
    createUserId: string;
    updateTime?: number;
    updateUserId?: string;
    deleteTime?: number;
    deleteUserId?: string;
    accessoryEquipments: AccessoryEquipment[];
    assetInsuranceUnits: any[];
    assetAmortizationSources: any[];
    assetAssetSources: any[];
}

// 財產詳細資料 
export interface AssetDetail {
    asset: Asset;
    accessoryEquipments: AccessoryEquipment[];
    insuranceUnits: InsuranceUnit[];
    amortizationSources: AmortizationSource[];
    assetSources: AssetSource[];
}

// 獲取所有財產
export async function getAssets(): Promise<ApiResponse<AssetDetail[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssets, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產列表失敗",
            data: [],
        };
    }
}

// 獲取單一財產詳細資料
export async function getAssetById(id: string): Promise<ApiResponse<AssetDetail>> {
    try {
        const response = await httpClient(`${apiEndpoints.getAssetDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取財產詳細資料失敗",
            data: {} as AssetDetail,
        };
    }
}

// 新增財產
export async function addAsset(data: AssetDetail): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.addAsset, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增財產失敗",
            data: null,
        };
    }
}

// 編輯財產
export async function editAsset(data: AssetDetail): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.editAsset, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        console.log(JSON.stringify(data));
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯財產失敗",
            data: null,
        };
    }
}

// 刪除財產
export async function deleteAsset(data: AssetDetail): Promise<ApiResponse<any>> {
    try {
        const response = await httpClient(apiEndpoints.deleteAsset, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除財產失敗",
            data: null,
        };
    }
}

interface NewAssetNoResponse {
    assetNo: string;
    message: string;
    success: boolean;
}

// 獲取新財產編號
export async function getNewAssetNo(subject?: string, subSubject?: string, category?: string): Promise<ApiResponse<NewAssetNoResponse>> {
    try {
        const queryParams = new URLSearchParams();
        if (subject) queryParams.append('subject', subject);
        if (subSubject) queryParams.append('subSubject', subSubject);
        if (category) queryParams.append('category', category);

        const url = `${apiEndpoints.getNewAssetNo}?${queryParams.toString()}`;

        const response = await httpClient(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取新財產編號失敗",
            data: { assetNo: "", message: "", success: false },
        };
    }
}

// 驗證批次轉檔Excel檔案
export async function validateExcelFile(file: File): Promise<ApiResponse<any>> {
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await httpClient(apiEndpoints.validateExcelFile, {
            method: "POST",
            body: formData,
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "驗證Excel檔案失敗",
            data: null,
        };
    }
}

// 批次匯入資產資料
export const batchImport = async (file: File, userId: string = "system") => {
    try {
        const formData = new FormData();
        formData.append("file", file);

        const response = await httpClient(`${apiEndpoints.batchImport}?userId=${userId}`, {
            method: "POST",
            body: formData,
        });

        return response;
    } catch (error) {
        console.error("批次匯入資產資料失敗:", error);
        return {
            success: false,
            message: error instanceof Error ? error.message : "批次匯入時發生未知錯誤",
        };
    }
};

// 下載批次轉檔範本
export async function downloadBatchTemplate(): Promise<Blob | null> {
    try {
        // 從cookies獲取token
        const token = getCookie('token') || '';

        // 使用原生fetch但確保有正確的認證頭
        const response = await fetch(apiEndpoints.downloadBatchTemplate, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${token}`
            },
            credentials: 'include'  // 包含cookies
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => "未知錯誤");
            throw new Error(`下載失敗 (${response.status}): ${errorText}`);
        }

        // 檢查Content-Type是否為excel
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("spreadsheet")) {
            throw new Error(`非預期的檔案類型: ${contentType}`);
        }

        return await response.blob();
    } catch (error: any) {
        console.error("下載範本失敗:", error);
        throw error;
    }
}


