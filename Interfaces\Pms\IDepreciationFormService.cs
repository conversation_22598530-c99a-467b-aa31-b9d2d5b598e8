using FAST_ERP_Backend.Models.Pms;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IDepreciationFormService
    {
        /// <summary>
        /// 取得固定資產折舊單資料
        /// </summary>
        /// <returns>固定資產折舊單資料列表</returns>
        Task<List<DepreciationFormDTO>> GetDepreciationFormsAsync();

        /// <summary>
        /// 新增固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddDepreciationFormAsync(DepreciationFormDTO _data);

        /// <summary>
        /// 編輯固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditDepreciationFormAsync(DepreciationFormDTO _data);

        /// <summary>
        /// 刪除固定資產折舊單
        /// </summary>
        /// <param name="_data">固定資產折舊單資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteDepreciationFormAsync(DepreciationFormDTO _data);

        /// <summary>
        /// 取得固定資產折舊單明細
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>固定資產折舊單明細</returns>
        Task<DepreciationFormDetailDTO> GetDepreciationFormDetailAsync(string _depreciationId);

        /// <summary>
        /// 檢查折舊紀錄是否已被使用
        /// </summary>
        /// <param name="_depreciationId">折舊紀錄編號</param>
        /// <returns>是否已被使用</returns>
        Task<bool> IsDepreciationUsedAsync(string _depreciationId);
    }
}