﻿using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;

public class SignalRHub : Hub
{
    // 用來儲存連線ID對應的 userId (群組名稱)
    private static readonly ConcurrentDictionary<string, string> ConnectionUserMap = new ConcurrentDictionary<string, string>();
    private readonly ILogger<SignalRHub> _logger;

    public SignalRHub(ILogger<SignalRHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var httpContext = Context.GetHttpContext();
        var userId = httpContext.Request.Query["userId"].ToString();
        //var account = httpContext.Request.Query["account"].ToString();
        var connectionId = Context.ConnectionId;

        // 確保 userId 合法，防止無效請求
        if (string.IsNullOrEmpty(userId))
        {
            _logger.LogWarning($"Connection {connectionId} has invalid userId");
            throw new HubException("Invalid userId.");
        }

        try
        {
            // 將該連線加入到以 userId 為名稱的群組中
            await Groups.AddToGroupAsync(connectionId, userId);
            ConnectionUserMap.TryAdd(connectionId, userId);

            _logger.LogInformation($"Connection {connectionId} added to group {userId}");
        }
        catch (Exception ex)
        {
            // 記錄錯誤日誌
            _logger.LogError(ex, $"Error adding connection {connectionId} to group {userId}");
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var connectionId = Context.ConnectionId;

        // 嘗試從映射中移除此連線，取得對應的 userId
        if (ConnectionUserMap.TryRemove(connectionId, out var userId))
        {
            // 從 userId 群組中移除此連線
            try
            {
                await Groups.RemoveFromGroupAsync(connectionId, userId);
                _logger.LogInformation($"Connection {connectionId} removed from group {userId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error removing connection {connectionId} from group {userId}");
            }
        }

        await base.OnDisconnectedAsync(exception);
    }

    public string GetConnectionId()
    {
        // 供前端呼叫獲取當前連結ID之方法(前端呼叫)
        return Context.ConnectionId;
    }

    public int GetCurrentConnectionCount()
    {
        // 回傳當前的連線數(前端呼叫)
        return ConnectionUserMap.Count;
    }

    public string? GetUserIdByConnectionId()
    {
        // 根據現在連線ID獲取userId
        if (ConnectionUserMap.TryGetValue(Context.ConnectionId, out var userId))
        {
            return userId;
        }
        else
        {
            // 如果該 connectionId 不存在於字典中，回傳 null 或適當的訊息
            return null;
        }
    }

}
