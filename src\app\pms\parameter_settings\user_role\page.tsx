"use client";

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  message,
  Tag,
  Tooltip,
  Descriptions,
  List,
  Typography,
  Select,
} from "antd";
import {
  EditOutlined,
  SearchOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  PmsUserRole,
  UserRoleWithMapping,
  getUserRoles,
  assignRoleToUser,
  removeRoleFromUser,
  getPmsUserRoles,
} from "@/services/pms/userRoleService";
import { User, getUsers } from "@/services/common/userService";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import guidUtils from "@/utils/guidUtils";

const { Text } = Typography;
const { Option } = Select;

const UserRolePage = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [userRoles, setUserRoles] = useState<PmsUserRole[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();
  const { user } = useAuth();
  const [searchText, setSearchText] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [userRoleMap, setUserRoleMap] = useState<Record<string, PmsUserRole[]>>(
    {}
  );
  const [filterRole, setFilterRole] = useState<string>("");
  const [visibleAccounts, setVisibleAccounts] = useState<Set<string>>(
    new Set()
  );
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [pendingChanges, setPendingChanges] = useState<{
    rolesToAdd: string[];
    rolesToRemove: string[];
    formValues: { roles: string[] };
  } | null>(null);

  // 載入使用者列表和身分資料
  const loadUsers = async () => {
    setLoading(true);
    try {
      const [usersResponse, rolesResponse] = await Promise.all([
        getUsers(),
        getPmsUserRoles(),
      ]);

      if (usersResponse.success && usersResponse.data) {
        const sortedUsers = usersResponse.data.sort(
          (a, b) => a.sortCode - b.sortCode
        );
        setUsers(sortedUsers);
        setFilteredUsers(sortedUsers);

        // 載入每個使用者的身分
        const roleMap: Record<string, PmsUserRole[]> = {};
        await Promise.all(
          sortedUsers.map(async (user) => {
            const userRoles = await getUserRoles(user.userId);
            if (userRoles.success && userRoles.data) {
              roleMap[user.userId] = userRoles.data;
            }
          })
        );
        setUserRoleMap(roleMap);
      } else {
        notifyError(
          "獲取使用者列表失敗",
          usersResponse.message || "請稍後再試"
        );
      }

      if (rolesResponse.success && rolesResponse.data) {
        setUserRoles(rolesResponse.data);
      } else {
        notifyError("獲取身分列表失敗", rolesResponse.message || "請稍後再試");
      }
    } catch (error: any) {
      console.error("Load Users Error:", error);
      notifyError("獲取資料失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 更新搜尋和篩選邏輯
  useEffect(() => {
    let filtered = [...users];

    // 文字搜尋
    if (searchText) {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchText.toLowerCase()) ||
          user.account.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 身分篩選
    if (filterRole) {
      filtered = filtered.filter((user) =>
        userRoleMap[user.userId]?.some(
          (role) => role.pmsUserRoleId === filterRole
        )
      );
    }

    setFilteredUsers(filtered);
  }, [searchText, users, filterRole, userRoleMap]);

  // 處理編輯
  const handleEdit = async (record: User) => {
    setEditingUser(record);
    setLoading(true);
    try {
      const response = await getUserRoles(record.userId);
      if (response.success && response.data) {
        const currentRoles = response.data.map((role) => role.pmsUserRoleId);
        setSelectedRoles(currentRoles);
        form.setFieldsValue({ roles: currentRoles });
      }
    } catch (error) {
      console.error("Get User Roles Error:", error);
      notifyError("獲取使用者身分失敗", "請稍後再試");
    } finally {
      setLoading(false);
      setIsModalVisible(true);
    }
  };

  // 處理表單提交前的確認
  const handleFormSubmit = async (values: { roles: string[] }) => {
    if (!editingUser) return;

    // 獲取當前使用者的身分
    const currentRoles = await getUserRoles(editingUser.userId);
    const currentRoleIds = currentRoles.success
      ? currentRoles.data?.map((r) => r.pmsUserRoleId) || []
      : [];

    // 計算要新增和移除的身分
    const rolesToAdd = values.roles.filter(
      (roleId) => !currentRoleIds.includes(roleId)
    );
    const rolesToRemove = currentRoleIds.filter(
      (roleId) => !values.roles.includes(roleId)
    );

    // 如果沒有變更，直接關閉視窗
    if (rolesToAdd.length === 0 && rolesToRemove.length === 0) {
      setIsModalVisible(false);
      return;
    }

    // 設定待確認的變更
    setPendingChanges({
      rolesToAdd,
      rolesToRemove,
      formValues: values,
    });
    setIsConfirmModalVisible(true);
  };

  // 執行實際的身分變更
  const executeRoleChanges = async () => {
    if (!editingUser || !pendingChanges) return;

    try {
      setLoading(true);

      // 先執行移除
      for (const roleId of pendingChanges.rolesToRemove) {
        const removeData = {
          userId: editingUser.userId,
          pmsUserRoleId: roleId,
        };

        const response = await removeRoleFromUser(removeData);
        if (!response) {
          notifyError("移除身分失敗", "移除身分失敗");
          setLoading(false);
          return;
        }
      }

      // 再執行新增
      for (const roleId of pendingChanges.rolesToAdd) {
        const assignData = {
          pmsUserRoleMappingId: guidUtils(),
          userId: editingUser.userId,
          pmsUserRoleId: roleId,
          createUserId: user?.userId || "",
        };

        const response = await assignRoleToUser(assignData);
        if (!response.success) {
          notifyError("指派身分失敗", response.message || "請稍後再試");
          setLoading(false);
          return;
        }
      }

      notifySuccess("更新成功", `「${editingUser?.name}」身分已成功更新`);
      setIsConfirmModalVisible(false);
      setIsModalVisible(false);
      setPendingChanges(null);
      loadUsers();
    } catch (error: any) {
      console.error("更新使用者身分失敗", error);
      notifyError("更新失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  // 取消變更
  const handleCancel = () => {
    setIsConfirmModalVisible(false);
    setPendingChanges(null);
  };

  // 獲取角色名稱
  const getRoleNames = (roleIds: string[]) => {
    return roleIds
      .map(
        (id) => userRoles.find((role) => role.pmsUserRoleId === id)?.roleName
      )
      .filter((name) => name)
      .join("、");
  };

  // 處理帳號顯示/隱藏
  const toggleAccountVisibility = (userId: string) => {
    setVisibleAccounts((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  // 隱碼處理函數
  const maskAccount = (account: string) => {
    if (account.length <= 2) {
      return account;
    }
    return (
      account.charAt(0) +
      "*".repeat(account.length - 2) +
      account.charAt(account.length - 1)
    );
  };

  // 表格欄位定義
  const columns: ColumnsType<User> = [
    {
      title: "序號",
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "帳號",
      dataIndex: "account",
      key: "account",
      render: (text, record) => (
        <Space>
          <span>
            {visibleAccounts.has(record.userId) ? text : maskAccount(text)}
          </span>
          <Button
            type="text"
            icon={
              visibleAccounts.has(record.userId) ? (
                <EyeOutlined />
              ) : (
                <EyeInvisibleOutlined />
              )
            }
            onClick={() => toggleAccountVisibility(record.userId)}
            size="small"
          />
        </Space>
      ),
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="姓名">
                  {record.name}
                </Descriptions.Item>
                <Descriptions.Item label="電話">
                  {record.telNo}
                </Descriptions.Item>
                <Descriptions.Item label="手機">
                  {record.phone}
                </Descriptions.Item>
                <Descriptions.Item label="Email">
                  {record.eMail}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "身分",
      key: "currentRoles",
      render: (_, record) => (
        <Space wrap>
          {userRoleMap[record.userId]?.map((role) => (
            <Tag key={role.pmsUserRoleId} color="red">
              {role.roleName}
            </Tag>
          )) || "無"}
        </Space>
      ),
    },
    {
      title: "操作",
      key: "roles",
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            管理身分
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card title="使用者身分管理">
      <Space style={{ marginBottom: 16 }} wrap>
        <Input
          placeholder="搜尋姓名、帳號"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: isMobile ? "100%" : 260 }}
        />
        <Select
          placeholder="篩選身分"
          allowClear
          style={{ width: isMobile ? "100%" : 200 }}
          onChange={(value) => setFilterRole(value)}
        >
          {userRoles.map((role) => (
            <Option key={role.pmsUserRoleId} value={role.pmsUserRoleId}>
              {role.roleName}
            </Option>
          ))}
        </Select>
      </Space>

      {isMobile ? (
        <List
          loading={loading}
          dataSource={filteredUsers}
          renderItem={(user) => (
            <List.Item
              key={user.userId}
              actions={[
                <Button
                  type="link"
                  key="edit"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(user)}
                >
                  管理身分
                </Button>,
              ]}
            >
              <List.Item.Meta
                title={<Text strong>{user.name}</Text>}
                description={
                  <Space
                    direction="vertical"
                    size={2}
                    style={{ width: "100%" }}
                  >
                    <Space>
                      <Text type="secondary">帳號：</Text>
                      <Text type="secondary">
                        {visibleAccounts.has(user.userId)
                          ? user.account
                          : maskAccount(user.account)}
                      </Text>
                      <Button
                        type="text"
                        icon={
                          visibleAccounts.has(user.userId) ? (
                            <EyeOutlined />
                          ) : (
                            <EyeInvisibleOutlined />
                          )
                        }
                        onClick={() => toggleAccountVisibility(user.userId)}
                        size="small"
                      />
                    </Space>
                    <Text type="secondary">電話：{user.phone}</Text>
                    <Text type="secondary">Email：{user.eMail}</Text>
                    <Space wrap>
                      <Text type="secondary">目前身分：</Text>
                      {userRoleMap[user.userId]?.map((role) => (
                        <Tag key={role.pmsUserRoleId} color="blue">
                          {role.roleName}
                        </Tag>
                      )) || "無"}
                    </Space>
                  </Space>
                }
              />
            </List.Item>
          )}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      ) : (
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="userId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title="管理使用者身分"
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingUser(null);
          setSelectedRoles([]);
          form.resetFields();
        }}
        width={600}
        okText="確認"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          initialValues={{ roles: selectedRoles }}
        >
          <Form.Item name="roles">
            <Select
              mode="multiple"
              placeholder="請選擇身分"
              style={{ width: "100%" }}
              optionFilterProp="children"
              allowClear
            >
              {userRoles.map((role) => (
                <Option key={role.pmsUserRoleId} value={role.pmsUserRoleId}>
                  {role.roleName}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="確認身分變更"
        open={isConfirmModalVisible}
        onOk={executeRoleChanges}
        onCancel={handleCancel}
        width={500}
        okText="確認變更"
        cancelText="取消"
      >
        <div style={{ marginBottom: "16px" }}>
          <Text strong>使用者：{editingUser?.name}</Text>
        </div>
        {pendingChanges?.rolesToAdd.length ? (
          <div style={{ marginBottom: "8px" }}>
            <Text type="danger">
              新增身分：{getRoleNames(pendingChanges.rolesToAdd)}
            </Text>
          </div>
        ) : null}
        {pendingChanges?.rolesToRemove.length ? (
          <div style={{ marginBottom: "8px" }}>
            <Text type="danger">
              移除身分：{getRoleNames(pendingChanges.rolesToRemove)}
            </Text>
          </div>
        ) : null}
      </Modal>
    </Card>
  );
};

export default UserRolePage;
