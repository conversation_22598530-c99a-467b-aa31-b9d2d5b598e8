using OfficeOpenXml;
using FAST_ERP_Backend.Models.Pms;
using System.Globalization;
using System.Text.RegularExpressions;

namespace FAST_ERP_Backend.Helper
{
    /// <summary>
    /// Excel處理助手類別
    /// </summary>
    public static class ExcelHelper
    {
        /// <summary>
        /// 財產基本資料Excel欄位對應設定
        /// </summary>
        public static List<ExcelColumnMappingDTO> GetAssetColumnMappings()
        {
            return new List<ExcelColumnMappingDTO>
            {
                new ExcelColumnMappingDTO { ColumnIndex = 1, ColumnName = "財產流水號", PropertyName = "AssetId", IsRequired = false, DataType = "guid", ValidationPattern = "", ErrorMessage = "財產流水號格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 2, ColumnName = "財產編號", PropertyName = "AssetNo", IsRequired = false, DataType = "string", ValidationPattern = @"^[A-Za-z0-9]{0,100}$", ErrorMessage = "財產編號格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 3, ColumnName = "財產名稱", PropertyName = "AssetName", IsRequired = true, DataType = "string", ValidationPattern = @"^.{1,100}$", ErrorMessage = "財產名稱不能為空且長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 4, ColumnName = "財產簡稱", PropertyName = "AssetShortName", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "財產簡稱長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 5, ColumnName = "取得日期", PropertyName = "AcquisitionDate", IsRequired = true, DataType = "timestamp", ValidationPattern = "", ErrorMessage = "取得日期格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 6, ColumnName = "數量", PropertyName = "Quantity", IsRequired = true, DataType = "decimal", ValidationPattern = @"^\d+(\.\d{1,2})?$", ErrorMessage = "數量必須為正數且最多兩位小數" },
                new ExcelColumnMappingDTO { ColumnIndex = 7, ColumnName = "單位編號", PropertyName = "UnitId", IsRequired = true, DataType = "guid", ValidationPattern = "", ErrorMessage = "單位編號格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 8, ColumnName = "購入金額", PropertyName = "PurchaseAmount", IsRequired = true, DataType = "decimal", ValidationPattern = @"^\d+(\.\d{1,2})?$", ErrorMessage = "購入金額必須為正數且最多兩位小數" },
                new ExcelColumnMappingDTO { ColumnIndex = 9, ColumnName = "折舊金額", PropertyName = "DepreciationAmount", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "折舊金額格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 10, ColumnName = "累計折舊金額", PropertyName = "AccumulatedDepreciationAmount", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "累計折舊金額格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 11, ColumnName = "輔助金額", PropertyName = "SubsidyAmount", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "輔助金額格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 12, ColumnName = "預估殘值", PropertyName = "EstimatedResidualValue", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "預估殘值格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 13, ColumnName = "部門", PropertyName = "DepartmentId", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "部門ID長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 14, ColumnName = "股別", PropertyName = "DivisionId", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "股別長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 15, ColumnName = "保管人", PropertyName = "CustodianId", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "保管人ID長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 16, ColumnName = "使用人", PropertyName = "UserId", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "使用人ID長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 17, ColumnName = "財產狀態", PropertyName = "AssetStatus", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "財產狀態ID長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 18, ColumnName = "狀態異動日期", PropertyName = "StatusChangeDate", IsRequired = false, DataType = "timestamp", ValidationPattern = "", ErrorMessage = "狀態異動日期格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 19, ColumnName = "使用狀態", PropertyName = "Usage", IsRequired = false, DataType = "string", ValidationPattern = "", ErrorMessage = "使用狀態格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 20, ColumnName = "備註", PropertyName = "Notes", IsRequired = false, DataType = "string", ValidationPattern = "", ErrorMessage = "備註格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 21, ColumnName = "存放地點", PropertyName = "StorageLocationId", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "存放地點長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 22, ColumnName = "耐用年限", PropertyName = "ServiceLife", IsRequired = false, DataType = "int", ValidationPattern = @"^\d*$", ErrorMessage = "耐用年限必須為整數" },
                new ExcelColumnMappingDTO { ColumnIndex = 23, ColumnName = "保固年限", PropertyName = "InsurancePeriod", IsRequired = false, DataType = "int", ValidationPattern = @"^\d*$", ErrorMessage = "保固年限必須為整數" },
                new ExcelColumnMappingDTO { ColumnIndex = 24, ColumnName = "廠牌型號", PropertyName = "ManufacturerId", IsRequired = false, DataType = "guid", ValidationPattern = "", ErrorMessage = "廠牌型號ID格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 25, ColumnName = "建物地址", PropertyName = "BuildingAddress", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,200}$", ErrorMessage = "建物地址長度不能超過200字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 26, ColumnName = "建物構造", PropertyName = "BuildingStructure", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "建物構造長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 27, ColumnName = "興建日期", PropertyName = "ConstructionDate", IsRequired = false, DataType = "timestamp", ValidationPattern = "", ErrorMessage = "興建日期格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 28, ColumnName = "面積(m²)", PropertyName = "FloorArea", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "面積格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 29, ColumnName = "公設(m²)", PropertyName = "PublicArea", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "公設面積格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 30, ColumnName = "使用執照日期", PropertyName = "UsageExpiryDate", IsRequired = false, DataType = "timestamp", ValidationPattern = "", ErrorMessage = "使用執照日期格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 31, ColumnName = "使用執照號碼", PropertyName = "UsageLicenseNo", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "使用執照號碼長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 32, ColumnName = "適用房屋稅目", PropertyName = "BuildingTaxItem", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,50}$", ErrorMessage = "適用房屋稅目長度不能超過50字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 33, ColumnName = "公告現值", PropertyName = "PublicValue", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "公告現值格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 34, ColumnName = "地目", PropertyName = "LandSection", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,50}$", ErrorMessage = "地目長度不能超過50字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 35, ColumnName = "地段", PropertyName = "LandLocation", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,200}$", ErrorMessage = "地段長度不能超過200字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 36, ColumnName = "地號", PropertyName = "LandNumber", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,50}$", ErrorMessage = "地號長度不能超過50字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 37, ColumnName = "面積(m²)", PropertyName = "LandArea", IsRequired = false, DataType = "decimal", ValidationPattern = @"^\d*(\.\d{1,2})?$", ErrorMessage = "土地面積格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 38, ColumnName = "財產科目", PropertyName = "AssetAccountId", IsRequired = false, DataType = "guid", ValidationPattern = "", ErrorMessage = "財產科目ID格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 39, ColumnName = "財產子目", PropertyName = "AssetSubAccountId", IsRequired = false, DataType = "guid", ValidationPattern = "", ErrorMessage = "財產子目ID格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 40, ColumnName = "資產類別編號", PropertyName = "AssetCategoryId", IsRequired = true, DataType = "string", ValidationPattern = @"^[A-Za-z]{1}$", ErrorMessage = "資產類別編號必須為1位英文字母" },
                new ExcelColumnMappingDTO { ColumnIndex = 41, ColumnName = "權狀號碼", PropertyName = "CertificateNo", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "權狀號碼長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 42, ColumnName = "自訂財產編號一", PropertyName = "CustomAssetNo1", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "自訂財產編號一長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 43, ColumnName = "自訂財產編號二", PropertyName = "CustomAssetNo2", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,100}$", ErrorMessage = "自訂財產編號二長度不能超過100字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 44, ColumnName = "報廢原因", PropertyName = "ScrapReason", IsRequired = false, DataType = "string", ValidationPattern = @"^.{0,500}$", ErrorMessage = "報廢原因長度不能超過500字元" },
                new ExcelColumnMappingDTO { ColumnIndex = 45, ColumnName = "報廢日期", PropertyName = "ScrapDate", IsRequired = false, DataType = "timestamp", ValidationPattern = "", ErrorMessage = "報廢日期格式不正確" },
                new ExcelColumnMappingDTO { ColumnIndex = 46, ColumnName = "本年度預備報廢", PropertyName = "PrepareToScrapThisYear", IsRequired = false, DataType = "string", ValidationPattern = @"^[YN]?$", ErrorMessage = "本年度預備報廢必須為Y或N" },
                new ExcelColumnMappingDTO { ColumnIndex = 47, ColumnName = "報廢後堪用", PropertyName = "UsableAfterScrap", IsRequired = false, DataType = "string", ValidationPattern = @"^[YN]?$", ErrorMessage = "報廢後堪用必須為Y或N" },
                new ExcelColumnMappingDTO { ColumnIndex = 48, ColumnName = "設備類型", PropertyName = "EquipmentTypeId", IsRequired = false, DataType = "guid", ValidationPattern = "", ErrorMessage = "設備類型ID格式不正確" }
            };
        }

        /// <summary>
        /// 驗證Excel檔案格式
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="columnMappings">欄位對應設定</param>
        /// <returns>驗證結果</returns>
        public static BatchValidationResultDTO ValidateExcelFormat(ExcelWorksheet worksheet, List<ExcelColumnMappingDTO> columnMappings)
        {
            var result = new BatchValidationResultDTO();
            var errors = new List<BatchValidationErrorDTO>();

            try
            {
                // 檢查標題列
                for (int col = 1; col <= columnMappings.Count; col++)
                {
                    var expectedColumnName = columnMappings[col - 1].ColumnName;
                    var actualColumnName = worksheet.Cells[1, col].Value?.ToString()?.Trim();

                    if (string.IsNullOrEmpty(actualColumnName) || actualColumnName != expectedColumnName)
                    {
                        errors.Add(new BatchValidationErrorDTO
                        {
                            RowIndex = 1,
                            ColumnName = $"第{col}欄",
                            ErrorMessage = $"標題列格式錯誤，預期為「{expectedColumnName}」，實際為「{actualColumnName}」",
                            CellValue = actualColumnName
                        });
                    }
                }

                // 計算總行數（排除標題列）
                var totalRows = worksheet.Dimension?.End.Row - 1 ?? 0;
                result.TotalRows = totalRows;

                // 如果標題列有錯誤，直接返回
                if (errors.Count > 0)
                {
                    result.IsValid = false;
                    result.Errors = errors;
                    result.ErrorRows = 1;
                    return result;
                }

                // 驗證資料列
                for (int row = 2; row <= worksheet.Dimension?.End.Row; row++)
                {
                    var rowErrors = ValidateExcelRow(worksheet, row, columnMappings);
                    errors.AddRange(rowErrors);
                }

                result.Errors = errors;
                result.ErrorRows = errors.Select(e => e.RowIndex).Distinct().Count();
                result.ValidRows = totalRows - result.ErrorRows;
                result.IsValid = errors.Count == 0;

                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add(new BatchValidationErrorDTO
                {
                    RowIndex = 0,
                    ColumnName = "檔案",
                    ErrorMessage = $"檔案驗證發生錯誤：{ex.Message}",
                    CellValue = ""
                });
                return result;
            }
        }

        /// <summary>
        /// 驗證Excel單一資料列
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="rowIndex">列索引</param>
        /// <param name="columnMappings">欄位對應設定</param>
        /// <returns>錯誤列表</returns>
        public static List<BatchValidationErrorDTO> ValidateExcelRow(ExcelWorksheet worksheet, int rowIndex, List<ExcelColumnMappingDTO> columnMappings)
        {
            var errors = new List<BatchValidationErrorDTO>();

            foreach (var mapping in columnMappings)
            {
                var cellValue = worksheet.Cells[rowIndex, mapping.ColumnIndex].Value?.ToString()?.Trim();

                // 必填欄位檢查
                if (mapping.IsRequired && string.IsNullOrEmpty(cellValue))
                {
                    errors.Add(new BatchValidationErrorDTO
                    {
                        RowIndex = rowIndex,
                        ColumnName = mapping.ColumnName,
                        ErrorMessage = $"{mapping.ColumnName}為必填欄位",
                        CellValue = cellValue
                    });
                    continue;
                }

                // 非必填但為空的欄位跳過驗證
                if (!mapping.IsRequired && string.IsNullOrEmpty(cellValue))
                {
                    continue;
                }

                // 資料格式驗證
                var validationError = ValidateCellValue(cellValue, mapping);
                if (validationError != null)
                {
                    validationError.RowIndex = rowIndex;
                    validationError.ColumnName = mapping.ColumnName;
                    validationError.CellValue = cellValue;
                    errors.Add(validationError);
                }
            }

            return errors;
        }

        /// <summary>
        /// 驗證儲存格值
        /// </summary>
        /// <param name="cellValue">儲存格值</param>
        /// <param name="mapping">欄位對應設定</param>
        /// <returns>驗證錯誤</returns>
        public static BatchValidationErrorDTO ValidateCellValue(string cellValue, ExcelColumnMappingDTO mapping)
        {
            if (string.IsNullOrEmpty(cellValue))
                return null;

            switch (mapping.DataType.ToLower())
            {
                case "string":
                    if (!string.IsNullOrEmpty(mapping.ValidationPattern) && !Regex.IsMatch(cellValue, mapping.ValidationPattern))
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    break;

                case "decimal":
                    if (!decimal.TryParse(cellValue, out var decimalValue) || decimalValue < 0)
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    if (!string.IsNullOrEmpty(mapping.ValidationPattern) && !Regex.IsMatch(cellValue, mapping.ValidationPattern))
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    break;

                case "int":
                    if (!int.TryParse(cellValue, out var intValue) || intValue < 0)
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    break;

                case "date":
                    if (!DateTime.TryParse(cellValue, out var dateValue))
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    break;

                case "timestamp":
                    if (!long.TryParse(cellValue, out var timestampValue))
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    // 驗證時間戳是否在合理範圍內 (1970-2100年)
                    if (timestampValue < -2147483648 || timestampValue > 4102444800)
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = "時間戳超出有效範圍" };
                    }
                    break;

                case "guid":
                    if (!Guid.TryParse(cellValue, out var guidValue))
                    {
                        return new BatchValidationErrorDTO { ErrorMessage = mapping.ErrorMessage };
                    }
                    break;
            }

            return null;
        }

        /// <summary>
        /// 將Excel資料轉換為AssetDTO
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="rowIndex">列索引</param>
        /// <param name="columnMappings">欄位對應設定</param>
        /// <returns>AssetDTO</returns>
        public static AssetDTO ConvertExcelRowToAssetDTO(ExcelWorksheet worksheet, int rowIndex, List<ExcelColumnMappingDTO> columnMappings)
        {
            var asset = new AssetDTO();

            foreach (var mapping in columnMappings)
            {
                var cellValue = worksheet.Cells[rowIndex, mapping.ColumnIndex].Value?.ToString()?.Trim();

                if (string.IsNullOrEmpty(cellValue))
                    continue;

                switch (mapping.PropertyName)
                {
                    case "AssetId":
                        if (Guid.TryParse(cellValue, out var assetId))
                        {
                            asset.AssetId = assetId;
                        }
                        break;
                    case "AssetNo":
                        asset.AssetNo = cellValue;
                        break;
                    case "AssetName":
                        asset.AssetName = cellValue;
                        break;
                    case "AssetShortName":
                        asset.AssetShortName = cellValue;
                        break;
                    case "AcquisitionDate":
                        if (long.TryParse(cellValue, out var acquisitionDate))
                        {
                            asset.AcquisitionDate = acquisitionDate;
                        }
                        break;
                    case "Quantity":
                        if (decimal.TryParse(cellValue, out var quantity))
                        {
                            asset.Quantity = quantity;
                        }
                        break;
                    case "UnitId":
                        if (Guid.TryParse(cellValue, out var unitId))
                        {
                            asset.UnitId = unitId;
                        }
                        break;
                    case "PurchaseAmount":
                        if (decimal.TryParse(cellValue, out var purchaseAmount))
                        {
                            asset.PurchaseAmount = purchaseAmount;
                        }
                        break;
                    case "DepreciationAmount":
                        if (decimal.TryParse(cellValue, out var depreciationAmount))
                        {
                            asset.DepreciationAmount = depreciationAmount;
                        }
                        break;
                    case "AccumulatedDepreciationAmount":
                        if (decimal.TryParse(cellValue, out var accumulatedAmount))
                        {
                            asset.AccumulatedDepreciationAmount = accumulatedAmount;
                        }
                        break;
                    case "SubsidyAmount":
                        if (decimal.TryParse(cellValue, out var subsidyAmount))
                        {
                            asset.SubsidyAmount = subsidyAmount;
                        }
                        break;
                    case "EstimatedResidualValue":
                        if (decimal.TryParse(cellValue, out var residualValue))
                        {
                            asset.EstimatedResidualValue = residualValue;
                        }
                        break;
                    case "DepartmentId":
                        asset.DepartmentId = cellValue;
                        break;
                    case "DivisionId":
                        asset.DivisionId = cellValue;
                        break;
                    case "CustodianId":
                        asset.CustodianId = cellValue;
                        break;
                    case "UserId":
                        asset.UserId = cellValue;
                        break;
                    case "AssetStatus":
                        asset.AssetStatusId = cellValue;
                        break;
                    case "StatusChangeDate":
                        if (long.TryParse(cellValue, out var statusChangeDate))
                        {
                            asset.StatusChangeDate = statusChangeDate;
                        }
                        break;
                    case "Usage":
                        asset.Usage = cellValue;
                        break;
                    case "Notes":
                        asset.Notes = cellValue;
                        break;
                    case "StorageLocationId":
                        asset.StorageLocationId = cellValue;
                        break;
                    case "ServiceLife":
                        if (int.TryParse(cellValue, out var serviceLife))
                        {
                            asset.ServiceLife = serviceLife;
                        }
                        break;
                    case "InsurancePeriod":
                        if (int.TryParse(cellValue, out var insurancePeriod))
                        {
                            asset.InsurancePeriod = insurancePeriod;
                        }
                        break;
                    case "ManufacturerId":
                        if (Guid.TryParse(cellValue, out var manufacturerId))
                        {
                            asset.ManufacturerId = manufacturerId;
                        }
                        break;
                    case "BuildingAddress":
                        asset.BuildingAddress = cellValue;
                        break;
                    case "BuildingStructure":
                        asset.BuildingStructure = cellValue;
                        break;
                    case "ConstructionDate":
                        if (long.TryParse(cellValue, out var constructionDate))
                        {
                            asset.ConstructionDate = constructionDate;
                        }
                        break;
                    case "FloorArea":
                        if (decimal.TryParse(cellValue, out var floorArea))
                        {
                            asset.FloorArea = floorArea;
                        }
                        break;
                    case "PublicArea":
                        if (decimal.TryParse(cellValue, out var publicArea))
                        {
                            asset.PublicArea = publicArea;
                        }
                        break;
                    case "UsageExpiryDate":
                        if (long.TryParse(cellValue, out var usageExpiryDate))
                        {
                            asset.UsageExpiryDate = usageExpiryDate;
                        }
                        break;
                    case "UsageLicenseNo":
                        asset.UsageLicenseNo = cellValue;
                        break;
                    case "BuildingTaxItem":
                        asset.BuildingTaxItem = cellValue;
                        break;
                    case "PublicValue":
                        if (decimal.TryParse(cellValue, out var publicValue))
                        {
                            asset.PublicValue = publicValue;
                        }
                        break;
                    case "LandSection":
                        asset.LandSection = cellValue;
                        break;
                    case "LandLocation":
                        asset.LandLocation = cellValue;
                        break;
                    case "LandNumber":
                        asset.LandNumber = cellValue;
                        break;
                    case "LandArea":
                        if (decimal.TryParse(cellValue, out var landArea))
                        {
                            asset.LandArea = landArea;
                        }
                        break;
                    case "AssetAccountId":
                        if (Guid.TryParse(cellValue, out var assetAccountId))
                        {
                            asset.AssetAccountId = assetAccountId;
                        }
                        break;
                    case "AssetSubAccountId":
                        if (Guid.TryParse(cellValue, out var assetSubAccountId))
                        {
                            asset.AssetSubAccountId = assetSubAccountId;
                        }
                        break;
                    case "AssetCategoryId":
                        asset.AssetCategoryId = cellValue;
                        break;
                    case "CertificateNo":
                        asset.CertificateNo = cellValue;
                        break;
                    case "CustomAssetNo1":
                        asset.CustomAssetNo1 = cellValue;
                        break;
                    case "CustomAssetNo2":
                        asset.CustomAssetNo2 = cellValue;
                        break;
                    case "ScrapReason":
                        asset.ScrapReason = cellValue;
                        break;
                    case "ScrapDate":
                        if (long.TryParse(cellValue, out var scrapDate))
                        {
                            asset.ScrapDate = scrapDate;
                        }
                        break;
                    case "EstimatedScrapYear":
                        if (long.TryParse(cellValue, out var estimatedScrapYear))
                        {
                            asset.EstimatedScrapYear = estimatedScrapYear;
                        }
                        break;
                    case "UsableAfterScrap":
                        asset.UsableAfterScrap = cellValue;
                        break;
                    case "EquipmentTypeId":
                        if (Guid.TryParse(cellValue, out var equipmentTypeId))
                        {
                            asset.EquipmentTypeId = equipmentTypeId;
                        }
                        break;
                }
            }

            return asset;
        }

        /// <summary>
        /// 產生批次轉檔範本
        /// </summary>
        /// <returns>Excel檔案位元組陣列</returns>
        public static byte[] GenerateBatchTemplate()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("財產基本資料");
                var columnMappings = GetAssetColumnMappings();

                // 設定標題列
                for (int col = 1; col <= columnMappings.Count; col++)
                {
                    worksheet.Cells[1, col].Value = columnMappings[col - 1].ColumnName;
                    worksheet.Cells[1, col].Style.Font.Bold = true;
                    worksheet.Cells[1, col].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, col].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    worksheet.Cells[1, col].AutoFitColumns();
                }

                // 新增範例資料列，參考 PmsBatch.csv 格式
                var currentTimestamp = ((DateTimeOffset)DateTime.Now).ToUnixTimeSeconds();
                var sampleGuid = Guid.NewGuid();

                // 第1列範例資料
                worksheet.Cells[2, 1].Value = "";  // 財產流水號 (可為空)
                worksheet.Cells[2, 2].Value = "";  // 財產編號 (可為空，系統自動產生)
                worksheet.Cells[2, 3].Value = "北門段532號";  // 財產名稱
                worksheet.Cells[2, 4].Value = "土地";  // 財產簡稱
                worksheet.Cells[2, 5].Value = currentTimestamp;  // 取得日期 (Unix timestamp)
                worksheet.Cells[2, 6].Value = "1";  // 數量
                worksheet.Cells[2, 7].Value = sampleGuid.ToString();  // 單位編號 (GUID)
                worksheet.Cells[2, 8].Value = "64149245";  // 購入金額
                worksheet.Cells[2, 9].Value = "0";  // 折舊金額
                worksheet.Cells[2, 10].Value = "0";  // 累計折舊金額
                worksheet.Cells[2, 11].Value = "0";  // 輔助金額
                worksheet.Cells[2, 12].Value = "0";  // 預估殘值
                worksheet.Cells[2, 13].Value = "";  // 部門
                worksheet.Cells[2, 14].Value = "";  // 股別
                worksheet.Cells[2, 15].Value = "";  // 保管人
                worksheet.Cells[2, 16].Value = "";  // 使用人
                worksheet.Cells[2, 17].Value = "";  // 財產狀態
                worksheet.Cells[2, 18].Value = "0";  // 狀態異動日期
                worksheet.Cells[2, 19].Value = "";  // 使用狀態
                worksheet.Cells[2, 20].Value = "原牛潮埔段489-2等8筆重測重編，肉品市場基地";  // 備註
                worksheet.Cells[2, 21].Value = "";  // 存放地點
                worksheet.Cells[2, 22].Value = "0";  // 耐用年限
                worksheet.Cells[2, 23].Value = "0";  // 保固年限
                worksheet.Cells[2, 24].Value = "";  // 廠牌型號
                worksheet.Cells[2, 25].Value = "";  // 建物地址
                worksheet.Cells[2, 26].Value = "";  // 建物構造
                worksheet.Cells[2, 27].Value = "";  // 興建日期
                worksheet.Cells[2, 28].Value = "";  // 面積(m²)
                worksheet.Cells[2, 29].Value = "";  // 公設(m²)
                worksheet.Cells[2, 30].Value = "";  // 使用執照日期
                worksheet.Cells[2, 31].Value = "";  // 使用執照號碼
                worksheet.Cells[2, 32].Value = "";  // 適用房屋稅目
                worksheet.Cells[2, 33].Value = "";  // 公告現值
                worksheet.Cells[2, 34].Value = "";  // 地目
                worksheet.Cells[2, 35].Value = "";  // 地段
                worksheet.Cells[2, 36].Value = "";  // 地號
                worksheet.Cells[2, 37].Value = "";  // 面積(m²)
                worksheet.Cells[2, 38].Value = "";  // 財產科目
                worksheet.Cells[2, 39].Value = "";  // 財產子目
                worksheet.Cells[2, 40].Value = "A";  // 資產類別編號
                worksheet.Cells[2, 41].Value = "";  // 權狀號碼
                worksheet.Cells[2, 42].Value = "";  // 自訂財產編號一
                worksheet.Cells[2, 43].Value = "";  // 自訂財產編號二
                worksheet.Cells[2, 44].Value = "";  // 報廢原因
                worksheet.Cells[2, 45].Value = "";  // 報廢日期
                worksheet.Cells[2, 46].Value = "";  // 本年度預備報廢
                worksheet.Cells[2, 47].Value = "";  // 報廢後堪用
                worksheet.Cells[2, 48].Value = "";  // 設備類型

                // 設定資料驗證和註解
                // 為必填欄位添加背景色提示
                for (int col = 1; col <= columnMappings.Count; col++)
                {
                    var mapping = columnMappings[col - 1];
                    if (mapping.IsRequired)
                    {
                        worksheet.Cells[2, col].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[2, col].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightYellow);

                        // 添加註解說明必填欄位
                        worksheet.Cells[1, col].AddComment($"【必填欄位】\n{mapping.ErrorMessage}", "System");
                    }
                    else
                    {
                        // 添加註解說明欄位格式
                        if (!string.IsNullOrEmpty(mapping.ErrorMessage))
                        {
                            worksheet.Cells[1, col].AddComment($"【選填欄位】\n{mapping.ErrorMessage}", "System");
                        }
                    }
                }

                // 自動調整欄寬
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
        }
    }
}