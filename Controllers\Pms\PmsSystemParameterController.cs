using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;
using FAST_ERP_Backend.Models.Pms.Enums;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("PMS系統參數設定")]
    public class PmsSystemParameterController : ControllerBase
    {
        private readonly IPmsSystemParameterService _pmsSystemParameterService;

        public PmsSystemParameterController(IPmsSystemParameterService pmsSystemParameterService)
        {
            _pmsSystemParameterService = pmsSystemParameterService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得所有系統參數", Description = "取得財產管理系統所有參數設定")]
        public async Task<IActionResult> GetAllParameters()
        {
            var result = await _pmsSystemParameterService.GetAllParametersAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetByType/{parameterType}")]
        [SwaggerOperation(Summary = "依類型取得系統參數", Description = "依類型取得財產管理系統參數設定")]
        public async Task<IActionResult> GetParametersByType(PmsParameterType parameterType)
        {
            var result = await _pmsSystemParameterService.GetParametersByTypeAsync(parameterType);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "依ID取得系統參數", Description = "依ID取得財產管理系統參數設定")]
        public async Task<IActionResult> GetParameterById(string id)
        {
            var result = await _pmsSystemParameterService.GetParameterByIdAsync(id);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增系統參數", Description = "新增財產管理系統參數設定")]
        public async Task<IActionResult> AddParameter([FromBody] PmsSystemParameterDTO parameter)
        {
            var (success, message) = await _pmsSystemParameterService.AddParameterAsync(parameter);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯系統參數", Description = "編輯財產管理系統參數設定")]
        public async Task<IActionResult> EditParameter([FromBody] PmsSystemParameterDTO parameter)
        {
            var (success, message) = await _pmsSystemParameterService.EditParameterAsync(parameter);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除系統參數", Description = "刪除財產管理系統參數設定")]
        public async Task<IActionResult> DeleteParameter([FromBody] PmsSystemParameterDTO parameter)
        {
            var (success, message) = await _pmsSystemParameterService.DeleteParameterAsync(parameter);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("GetDepreciationMethods")]
        [SwaggerOperation(Summary = "取得折舊法設定", Description = "取得折舊法相關設定")]
        public async Task<IActionResult> GetDepreciationMethods()
        {
            var result = await _pmsSystemParameterService.GetDepreciationMethodsAsync();
            return Ok(result);
        }

        [HttpPost]
        [Route("SetDefaultDepreciationMethod/{methodId}")]
        [SwaggerOperation(Summary = "設定默認折舊法", Description = "設定默認折舊法方式")]
        public async Task<IActionResult> SetDefaultDepreciationMethod(string methodId)
        {
            var (success, message) = await _pmsSystemParameterService.SetDefaultDepreciationMethodAsync(methodId);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("GetDecliningBalanceRates")]
        [SwaggerOperation(Summary = "取得所有餘額遞減法折舊率設定", Description = "取得所有財產科目的餘額遞減法折舊率設定")]
        public async Task<IActionResult> GetAllDecliningBalanceRates()
        {
            var result = await _pmsSystemParameterService.GetAllDecliningBalanceRatesAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("GetDecliningBalanceRate/{assetAccountId}")]
        [SwaggerOperation(Summary = "取得特定財產科目的餘額遞減法折舊率", Description = "取得特定財產科目的餘額遞減法折舊率設定")]
        public async Task<IActionResult> GetDecliningBalanceRate(string assetAccountId)
        {
            var (success, rate, message) = await _pmsSystemParameterService.GetDecliningBalanceRateForAssetAccountAsync(assetAccountId);
            if (success)
            {
                return Ok(new { rate, message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("SetDecliningBalanceRate")]
        [SwaggerOperation(Summary = "設定特定財產科目的餘額遞減法折舊率", Description = "為特定財產科目設定餘額遞減法折舊率")]
        public async Task<IActionResult> SetDecliningBalanceRate([FromBody] DecliningBalanceRateRequest request)
        {
            var (success, message) = await _pmsSystemParameterService.SetDecliningBalanceRateForAssetAccountAsync(
                request.AssetAccountId,
                request.Rate,
                request.UserId);

            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("SetInitializationStatus/{isInitialized}")]
        [SwaggerOperation(Summary = "設定初始化狀態", Description = "設定初始化狀態")]
        public async Task<IActionResult> SetInitializationStatus(bool isInitialized)
        {
            var (success, message) = await _pmsSystemParameterService.SetInitializationStatusAsync(isInitialized);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("GetInitializationStatus")]
        [SwaggerOperation(Summary = "取得初始化狀態", Description = "取得初始化狀態")]
        public async Task<IActionResult> GetInitializationStatus()
        {
            var (success, isInitialized, message) = await _pmsSystemParameterService.GetInitializationStatusAsync();
            if (success)
            {
                return Ok(new { isInitialized, message });
            }
            return BadRequest(new { message });
        }

    }

    /// <summary>
    /// 餘額遞減法折舊率請求模型
    /// </summary>
    public class DecliningBalanceRateRequest
    {
        /// <summary>
        /// 財產科目ID
        /// </summary>
        public string AssetAccountId { get; set; }

        /// <summary>
        /// 折舊率
        /// </summary>
        public decimal Rate { get; set; }

        /// <summary>
        /// 操作用戶ID
        /// </summary>
        public string UserId { get; set; }
    }
}