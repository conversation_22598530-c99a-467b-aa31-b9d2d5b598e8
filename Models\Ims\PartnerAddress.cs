using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 商業夥伴地址資訊 - 支援台灣及國際地址格式 </summary>
public class PartnerAddress
{
    /// <summary> 地址編號 - 唯一識別碼 </summary>
    [Key]
    [Comment("地址編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerAddressID { get; set; }

    /// <summary> 商業夥伴編號 - 關聯至商業夥伴主檔 </summary>
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 詳細地址 - 包含路段、巷弄、號碼等完整地址資訊 </summary>
    [Required]
    [MaxLength(300)]
    [Comment("詳細地址")]
    [Column(TypeName = "nvarchar(300)")]
    public string Address { get; set; }

    /// <summary> 縣市 - 台灣：直轄市/縣市，國際：城市名稱 </summary>
    [MaxLength(50)]
    [Comment("縣市")]
    [Column(TypeName = "nvarchar(50)")]
    public string? City { get; set; }

    /// <summary> 鄉鎮市區 - 台灣：鄉鎮市區，國際：州/省/區域 </summary>
    [MaxLength(50)]
    [Comment("鄉鎮市區")]
    [Column(TypeName = "nvarchar(50)")]
    public string? District { get; set; }

    /// <summary> 郵遞區號 - 台灣：3+2碼或5碼，國際：各國郵政編碼格式 </summary>
    [MaxLength(20)]
    [Comment("郵遞區號")]
    [Column(TypeName = "nvarchar(20)")]
    public string? PostalCode { get; set; }

    /// <summary> 國家地區 - ISO 3166-1 國家代碼或中文國家名稱 </summary>
    [MaxLength(50)]
    [Comment("國家地區")]
    [Column(TypeName = "nvarchar(50)")]
    public string? Country { get; set; }

    /// <summary> 是否為主要地址 - 每個夥伴僅能有一個主要地址 </summary>
    [Comment("是否為主要地址")]
    public bool IsPrimary { get; set; }

    /// <summary> 導航屬性 - 關聯至商業夥伴主檔 </summary>
    public virtual Partner Partner { get; set; } = null!;

    /// <summary> 建構式 - 初始化預設值 </summary>
    public PartnerAddress()
    {
        PartnerAddressID = Guid.NewGuid();
        Address = string.Empty;
        IsPrimary = false;
        Country = "台灣"; // 預設為台灣
    }
}

/// <summary> 商業夥伴地址 DTO - 資料傳輸物件 </summary>
public class PartnerAddressDTO
{
    /// <summary> 地址編號 - 唯一識別碼 </summary>
    public Guid PartnerAddressID { get; set; }

    /// <summary> 商業夥伴編號 - 關聯至商業夥伴主檔 </summary>
    public Guid PartnerID { get; set; }

    /// <summary> 詳細地址 - 包含路段、巷弄、號碼等完整地址資訊 </summary>
    public string Address { get; set; }

    /// <summary> 縣市 - 台灣：直轄市/縣市，國際：城市名稱 </summary>
    public string? City { get; set; }

    /// <summary> 鄉鎮市區 - 台灣：鄉鎮市區，國際：州/省/區域 </summary>
    public string? District { get; set; }

    /// <summary> 郵遞區號 - 台灣：3+2碼或5碼，國際：各國郵政編碼格式 </summary>
    public string? PostalCode { get; set; }

    /// <summary> 國家地區 - ISO 3166-1 國家代碼或中文國家名稱 </summary>
    public string? Country { get; set; }

    /// <summary> 是否為主要地址 - 每個夥伴僅能有一個主要地址 </summary>
    public bool IsPrimary { get; set; }

    /// <summary> 建構式 - 初始化預設值 </summary>
    public PartnerAddressDTO()
    {
        PartnerAddressID = Guid.NewGuid();
        Address = string.Empty;
        IsPrimary = false;
        Country = "台灣"; // 預設為台灣
    }
}
