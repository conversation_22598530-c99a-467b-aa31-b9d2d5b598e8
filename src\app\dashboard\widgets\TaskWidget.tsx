"use client";

import { List, Tag, Typography, Avatar, Spin } from "antd";
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useState, useEffect } from "react";

const { Title, Text } = Typography;

export function TaskWidget() {
  const [loading, setLoading] = useState(true);

  // 模擬任務數據
  const tasks = [
    {
      id: 1,
      title: "完成月度報告",
      status: "pending",
      priority: "high",
      dueDate: "2024-01-15",
    },
    {
      id: 2,
      title: "系統維護檢查",
      status: "in-progress",
      priority: "medium",
      dueDate: "2024-01-12",
    },
    {
      id: 3,
      title: "用戶培訓準備",
      status: "completed",
      priority: "low",
      dueDate: "2024-01-10",
    },
    {
      id: 4,
      title: "數據備份驗證",
      status: "pending",
      priority: "high",
      dueDate: "2024-01-14",
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
      case "in-progress":
        return <ClockCircleOutlined style={{ color: "#faad14" }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: "#ff4d4f" }} />;
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      completed: { color: "success", text: "已完成" },
      "in-progress": { color: "processing", text: "進行中" },
      pending: { color: "warning", text: "待處理" },
    };
    const config = statusMap[status as keyof typeof statusMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getPriorityTag = (priority: string) => {
    const priorityMap = {
      high: { color: "red", text: "高" },
      medium: { color: "orange", text: "中" },
      low: { color: "green", text: "低" },
    };
    const config = priorityMap[priority as keyof typeof priorityMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large">
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
            }}
          >
            載入任務列表...
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: "100%" }}>
      <Title level={5} style={{ marginBottom: "16px" }}>
        近期任務
      </Title>

      <List
        size="small"
        dataSource={tasks}
        renderItem={(task) => (
          <List.Item>
            <List.Item.Meta
              avatar={getStatusIcon(task.status)}
              title={
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Text>{task.title}</Text>
                  <div>
                    {getPriorityTag(task.priority)}
                    {getStatusTag(task.status)}
                  </div>
                </div>
              }
              description={
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  截止日期: {task.dueDate}
                </Text>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );
}

export default TaskWidget;
