using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 檔案上傳主表 - 多租戶檔案管理
    /// </summary>
    public class FileUpload : ModelBaseEntity
    {
        [Key]
        [Comment("檔案編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string FileUploadId { get; set; } // 檔案編號

        [Required]
        [Comment("企業群組編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string EnterpriseGroupsId { get; set; } // 企業群組編號 - 分區識別碼

        [Required]
        [Comment("原始檔案名稱")]
        [Column(TypeName = "nvarchar(255)")]
        public string OriginalFileName { get; set; } // 原始檔案名稱

        [Required]
        [Comment("儲存檔案名稱")]
        [Column(TypeName = "nvarchar(255)")]
        public string StoredFileName { get; set; } // 儲存檔案名稱（含GUID）

        [Required]
        [Comment("檔案路徑")]
        [Column(TypeName = "nvarchar(500)")]
        public string FilePath { get; set; } // 相對檔案路徑

        [Required]
        [Comment("檔案大小")]
        [Column(TypeName = "bigint")]
        public long FileSize { get; set; } // 檔案大小（bytes）

        [Required]
        [Comment("檔案類型")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContentType { get; set; } // MIME類型

        [Comment("檔案副檔名")]
        [Column(TypeName = "nvarchar(10)")]
        public string FileExtension { get; set; } // 檔案副檔名

        [Comment("檔案分類")]
        [Column(TypeName = "nvarchar(50)")]
        public string FileCategory { get; set; } // 檔案分類（如：document, image, attachment等）

        [Comment("來源模組")]
        [Column(TypeName = "nvarchar(50)")]
        public string SourceModule { get; set; } // 來源模組（IMS, PMS, PAS等）

        [Comment("來源資料表")]
        [Column(TypeName = "nvarchar(100)")]
        public string SourceTable { get; set; } // 來源資料表

        [Comment("來源記錄編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SourceRecordId { get; set; } // 來源記錄編號

        [Comment("檔案描述")]
        [Column(TypeName = "nvarchar(500)")]
        public string Description { get; set; } // 檔案描述

        [Comment("排序順序")]
        [Column(TypeName = "int")]
        public int SortOrder { get; set; } // 排序順序

        [Comment("是否為主要檔案")]
        [Column(TypeName = "bit")]
        public bool IsPrimary { get; set; } // 是否為主要檔案

        [Comment("存取權限")]
        [Column(TypeName = "nvarchar(20)")]
        public string AccessLevel { get; set; } // 存取權限（Public, Private, Restricted）

        [Comment("檔案狀態")]
        [Column(TypeName = "nvarchar(20)")]
        public string FileStatus { get; set; } // 檔案狀態（Active, Archived, Deleted）

        [Comment("檔案雜湊值")]
        [Column(TypeName = "nvarchar(64)")]
        public string FileHash { get; set; } // SHA256雜湊值，用於重複檔案檢測

        [Comment("下載次數")]
        [Column(TypeName = "int")]
        public int DownloadCount { get; set; } // 下載次數

        [Comment("最後存取時間")]
        [Column(TypeName = "bigint")]
        public long? LastAccessTime { get; set; } // 最後存取時間

        // 導航屬性
        [ForeignKey("EnterpriseGroupsId")]
        public virtual EnterpriseGroups EnterpriseGroups { get; set; }

        public FileUpload()
        {
            FileUploadId = Guid.NewGuid().ToString();
            EnterpriseGroupsId = "";
            OriginalFileName = "";
            StoredFileName = "";
            FilePath = "";
            FileSize = 0;
            ContentType = "";
            FileExtension = "";
            FileCategory = "";
            SourceModule = "";
            SourceTable = "";
            SourceRecordId = "";
            Description = "";
            SortOrder = 0;
            IsPrimary = false;
            AccessLevel = "Private";
            FileStatus = "Active";
            FileHash = "";
            DownloadCount = 0;
            LastAccessTime = null;
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 檔案上傳 DTO
    /// </summary>
    public class FileUploadDTO : ModelBaseEntityDTO
    {
        public string FileUploadId { get; set; }
        public string EnterpriseGroupsId { get; set; }
        public string OriginalFileName { get; set; }
        public string StoredFileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; }
        public string FileExtension { get; set; }
        public string FileCategory { get; set; }
        public string SourceModule { get; set; }
        public string SourceTable { get; set; }
        public string SourceRecordId { get; set; }
        public string Description { get; set; }
        public int SortOrder { get; set; }
        public bool IsPrimary { get; set; }
        public string AccessLevel { get; set; }
        public string FileStatus { get; set; }
        public string FileHash { get; set; }
        public int DownloadCount { get; set; }
        public long? LastAccessTime { get; set; }

        public FileUploadDTO()
        {
            FileUploadId = "";
            EnterpriseGroupsId = "";
            OriginalFileName = "";
            StoredFileName = "";
            FilePath = "";
            FileSize = 0;
            ContentType = "";
            FileExtension = "";
            FileCategory = "";
            SourceModule = "";
            SourceTable = "";
            SourceRecordId = "";
            Description = "";
            SortOrder = 0;
            IsPrimary = false;
            AccessLevel = "Private";
            FileStatus = "Active";
            FileHash = "";
            DownloadCount = 0;
            LastAccessTime = null;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 檔案上傳請求 DTO
    /// </summary>
    public class FileUploadRequestDTO
    {
        [Required(ErrorMessage = "企業群組編號為必填")]
        public string EnterpriseGroupsId { get; set; }

        [Required(ErrorMessage = "檔案為必填")]
        public IFormFile File { get; set; }

        public string FileCategory { get; set; } = "document";
        public string SourceModule { get; set; } = "";
        public string SourceTable { get; set; } = "";
        public string SourceRecordId { get; set; } = "";
        public string Description { get; set; } = "";
        public int SortOrder { get; set; } = 0;
        public bool IsPrimary { get; set; } = false;
        public string AccessLevel { get; set; } = "Private";
    }

    /// <summary>
    /// 批量檔案上傳請求 DTO
    /// </summary>
    public class BatchFileUploadRequestDTO
    {
        [Required(ErrorMessage = "企業群組編號為必填")]
        public string EnterpriseGroupsId { get; set; }

        [Required(ErrorMessage = "檔案列表為必填")]
        public List<IFormFile> Files { get; set; }

        public string FileCategory { get; set; } = "document";
        public string SourceModule { get; set; } = "";
        public string SourceTable { get; set; } = "";
        public string SourceRecordId { get; set; } = "";
        public List<string> Descriptions { get; set; } = new List<string>();
        public string AccessLevel { get; set; } = "Private";
    }

    /// <summary>
    /// 檔案下載請求 DTO
    /// </summary>
    public class FileDownloadRequestDTO
    {
        [Required(ErrorMessage = "檔案編號為必填")]
        public string FileUploadId { get; set; }

        [Required(ErrorMessage = "企業群組編號為必填")]
        public string EnterpriseGroupsId { get; set; }
    }

    /// <summary>
    /// 檔案查詢請求 DTO
    /// </summary>
    public class FileQueryRequestDTO
    {
        [Required(ErrorMessage = "企業群組編號為必填")]
        public string EnterpriseGroupsId { get; set; }

        public string SourceModule { get; set; } = "";
        public string SourceTable { get; set; } = "";
        public string SourceRecordId { get; set; } = "";
        public string FileCategory { get; set; } = "";
        public string FileStatus { get; set; } = "Active";
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
