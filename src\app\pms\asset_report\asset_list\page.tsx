"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Row,
  message,
  DatePicker,
  Statistic,
  Typography,
  Badge,
  Tag,
  Empty,
  Col,
  Grid,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  PrinterOutlined,
  FileExcelOutlined,
  EyeOutlined,
  DatabaseOutlined,
  DollarOutlined,
  CalendarOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { getAssets } from "@/services/pms/assetService";
import { getAssetAccounts } from "@/services/pms/assetAccountService";
import { getAssetSubAccounts } from "@/services/pms/assetSubAccountService";
import { getAssetCategories } from "@/services/pms/assetCategoryService";
import { getDepartments } from "@/services/common/departmentService";
import { getAssetStatuses } from "@/services/pms/assetStatusService";
import { getStorageLocations } from "@/services/pms/storageLocationService";
import { getUnits } from "@/services/common/unitService";
import { getUsers } from "@/services/common/userService";
import { formatTWCurrency } from "@/utils/formatUtils";
import { siteConfig } from "@/config/site";
import { STATUS_COLORS } from "@/constants/pms/statusColors";
import ReportHeader, {
  getReportPrintStyles,
} from "@/app/components/common/ReportHeader";

const { Option } = Select;
const { RangePicker } = DatePicker;
const { useBreakpoint } = Grid;
const { Title, Text } = Typography;

// 查詢參數介面
interface AssetListQuery {
  keyword?: string;
  assetAccountId?: string;
  assetStatusId?: string;
  departmentId?: string;
  assetCategoryId?: string;
  storageLocationId?: string;
  acquisitionDateRange?: [string, string];
}

// 統計資料介面
interface AssetListStatistics {
  totalCount: number;
  totalValue: number;
  totalDepreciation: number;
  totalSubsidy: number;
  netValue: number;
}

// 分類統計介面
interface CategoryStatistics {
  categoryName: string;
  count: number;
  totalValue: number;
  totalSubsidy: number;
  netValue: number;
  items: any[];
}

// 主組件
const AssetListPage: React.FC = () => {
  // =========================== 狀態管理 ===========================
  const [data, setData] = useState<any[]>([]);
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<AssetListStatistics>({
    totalCount: 0,
    totalValue: 0,
    totalDepreciation: 0,
    totalSubsidy: 0,
    netValue: 0,
  });
  const [categoryStats, setCategoryStats] = useState<CategoryStatistics[]>([]);

  // 表單和選項
  const [searchForm] = Form.useForm();
  const [assetAccounts, setAssetAccounts] = useState<any[]>([]);
  const [assetSubAccounts, setAssetSubAccounts] = useState<any[]>([]);
  const [assetCategories, setAssetCategories] = useState<any[]>([]);
  const [assetStatuses, setAssetStatuses] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [storageLocations, setStorageLocations] = useState<any[]>([]);
  const [units, setUnits] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);

  // 列印相關
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrintMode, setIsPrintMode] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // 響應式斷點
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // =========================== 數據載入 ===========================

  // 按科目分組資料
  const groupDataByCategory = useCallback(
    (assetData: any[]) => {
      const categoryMap = new Map<string, CategoryStatistics>();

      assetData.forEach((item) => {
        const asset = item.asset || item;
        // 根據 assetAccountId 找出對應的科目名稱
        let categoryName = "未分類";

        if (asset.assetAccountId) {
          const account = assetAccounts.find(
            (acc) => acc.assetAccountId === asset.assetAccountId
          );
          if (account) {
            categoryName = account.assetAccountName;
          }
        }

        const purchaseAmount = asset.purchaseAmount || asset.unitPrice || 0;
        const subsidyAmount = asset.subsidyAmount || 0;
        const depreciation = asset.accumulatedDepreciationAmount || 0;
        const netValue = purchaseAmount - depreciation;

        if (!categoryMap.has(categoryName)) {
          categoryMap.set(categoryName, {
            categoryName,
            count: 0,
            totalValue: 0,
            totalSubsidy: 0,
            netValue: 0,
            items: [],
          });
        }

        const categoryData = categoryMap.get(categoryName)!;
        categoryData.count += 1;
        categoryData.totalValue += purchaseAmount;
        categoryData.totalSubsidy += subsidyAmount;
        categoryData.netValue += netValue;
        categoryData.items.push(item);
      });

      // 對每個分類內的項目按財產編號排序
      const sortedCategories = Array.from(categoryMap.values()).map(
        (category) => ({
          ...category,
          items: category.items.sort((a, b) => {
            const assetA = a.asset || a;
            const assetB = b.asset || b;
            const assetNoA = assetA.assetNo || "";
            const assetNoB = assetB.assetNo || "";
            return assetNoA.localeCompare(assetNoB, "zh-TW", { numeric: true });
          }),
        })
      );

      // 不按科目名稱排序，保持原始順序，但"未分類"放在最後
      sortedCategories.sort((a, b) => {
        if (a.categoryName === "未分類" && b.categoryName !== "未分類")
          return 1;
        if (a.categoryName !== "未分類" && b.categoryName === "未分類")
          return -1;
        return 0; // 保持原始順序
      });

      return sortedCategories;
    },
    [assetAccounts]
  );

  // 計算統計數據
  const calculateStatistics = useCallback(
    (assetData: any[]) => {
      let totalValue = 0;
      let totalDepreciation = 0;
      let totalSubsidy = 0;

      assetData.forEach((item) => {
        const asset = item.asset || item;
        const purchaseAmount = asset.purchaseAmount || asset.unitPrice || 0;
        const depreciation = asset.accumulatedDepreciationAmount || 0;
        const subsidyAmount = asset.subsidyAmount || 0;

        totalValue += purchaseAmount;
        totalDepreciation += depreciation;
        totalSubsidy += subsidyAmount;
      });

      const newStatistics: AssetListStatistics = {
        totalCount: assetData.length,
        totalValue: Math.round(totalValue),
        totalDepreciation: Math.round(totalDepreciation),
        totalSubsidy: Math.round(totalSubsidy),
        netValue: Math.round(totalValue - totalDepreciation),
      };

      setStatistics(newStatistics);

      // 只有當科目資料載入完成後才計算分類統計
      if (assetAccounts.length > 0) {
        const categoryStatistics = groupDataByCategory(assetData);
        setCategoryStats(categoryStatistics);
      }
    },
    [groupDataByCategory, assetAccounts]
  );

  // 載入所有基礎資料
  const loadBaseData = useCallback(async () => {
    try {
      const [
        accountsRes,
        subAccountsRes,
        categoriesRes,
        statusesRes,
        departmentsRes,
        locationsRes,
        unitsRes,
        usersRes,
      ] = await Promise.all([
        getAssetAccounts(),
        getAssetSubAccounts(),
        getAssetCategories(),
        getAssetStatuses(),
        getDepartments(),
        getStorageLocations(),
        getUnits(),
        getUsers(),
      ]);

      if (accountsRes.success) setAssetAccounts(accountsRes.data || []);
      if (subAccountsRes.success)
        setAssetSubAccounts(subAccountsRes.data || []);
      if (categoriesRes.success) setAssetCategories(categoriesRes.data || []);
      if (statusesRes.success) setAssetStatuses(statusesRes.data || []);
      if (departmentsRes.success) setDepartments(departmentsRes.data || []);
      if (locationsRes.success) setStorageLocations(locationsRes.data || []);
      if (unitsRes.success) setUnits(unitsRes.data || []);
      if (usersRes.success) setUsers(usersRes.data || []);
    } catch (error) {
      console.error("載入基礎資料錯誤:", error);
      message.error("載入基礎資料失敗");
    }
  }, []);

  // 載入財產資料
  const loadData = useCallback(
    async (query?: AssetListQuery) => {
      try {
        setLoading(true);
        const response = await getAssets();

        if (response.success && response.data) {
          let assetData = Array.isArray(response.data) ? response.data : [];

          // 調試：檢查資料結構
          if (assetData.length > 0) {
            console.log("Asset data sample:", assetData[0]);
            console.log("Asset accounts:", assetAccounts);
          }

          // 應用篩選條件
          if (query) {
            assetData = assetData.filter((item) => {
              const asset = item.asset || item;

              // 關鍵字搜尋
              if (query.keyword) {
                const keyword = query.keyword.toLowerCase();
                const searchFields = [
                  asset.assetName,
                  asset.assetNo,
                  asset.specification,
                ].filter(Boolean);

                const matchKeyword = searchFields.some((field) =>
                  field?.toLowerCase().includes(keyword)
                );

                if (!matchKeyword) return false;
              }

              // 其他篩選條件
              if (
                query.assetAccountId &&
                asset.assetAccountId !== query.assetAccountId
              ) {
                return false;
              }

              if (
                query.assetStatusId &&
                asset.assetStatusId !== query.assetStatusId
              ) {
                return false;
              }

              if (
                query.departmentId &&
                asset.departmentId !== query.departmentId
              ) {
                return false;
              }

              if (
                query.assetCategoryId &&
                asset.assetCategoryId !== query.assetCategoryId
              ) {
                return false;
              }

              if (
                query.storageLocationId &&
                asset.storageLocationId !== query.storageLocationId
              ) {
                return false;
              }

              // 取得日期範圍篩選
              if (query.acquisitionDateRange && asset.acquisitionDate) {
                const acquisitionDate = dayjs(asset.acquisitionDate);
                const [startDate, endDate] = query.acquisitionDateRange;

                if (startDate && acquisitionDate.isBefore(dayjs(startDate))) {
                  return false;
                }

                if (endDate && acquisitionDate.isAfter(dayjs(endDate))) {
                  return false;
                }
              }

              return true;
            });
          }

          setData(assetData);
          setFilteredData(assetData);
          calculateStatistics(assetData);
        } else {
          console.error("API 回應錯誤:", response);
          message.error("載入資料失敗");
        }
      } catch (error) {
        console.error("載入資料錯誤:", error);
        message.error("載入資料失敗");
      } finally {
        setLoading(false);
      }
    },
    [calculateStatistics]
  );

  // =========================== 事件處理 ===========================

  // 搜尋處理
  const handleSearch = async () => {
    const formValues = await searchForm.getFieldsValue();
    const query: AssetListQuery = {};

    if (formValues.keyword) {
      query.keyword = formValues.keyword.trim();
    }

    if (formValues.assetAccountId) {
      query.assetAccountId = formValues.assetAccountId;
    }

    if (formValues.assetStatusId) {
      query.assetStatusId = formValues.assetStatusId;
    }

    if (formValues.departmentId) {
      query.departmentId = formValues.departmentId;
    }

    if (formValues.assetCategoryId) {
      query.assetCategoryId = formValues.assetCategoryId;
    }

    if (formValues.storageLocationId) {
      query.storageLocationId = formValues.storageLocationId;
    }

    if (formValues.acquisitionDateRange) {
      query.acquisitionDateRange = [
        formValues.acquisitionDateRange[0].format("YYYY-MM-DD"),
        formValues.acquisitionDateRange[1].format("YYYY-MM-DD"),
      ];
    }

    await loadData(query);
  };

  // 重置搜尋
  const handleResetSearch = () => {
    searchForm.resetFields();
    loadData();
  };

  // 列印處理
  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const printWindow = window.open("", "_blank");

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${siteConfig.copyright}財產清冊</title>
              <style>
                body { 
                  font-family: Arial, sans-serif; 
                  margin: 0; 
                  padding: 2px; 
                  background: white;
                  font-size: 12px;
                }
                table { 
                  width: 100%; 
                  border-collapse: collapse; 
                  border: 1px solid #000;
                  font-size: 9px;
                  table-layout: auto;
                }
                th { 
                  border: 1px solid #000; 
                  padding: 2px; 
                  background-color: #f5f5f5; 
                  font-weight: bold; 
                  text-align: center;
                  white-space: nowrap;
                  font-size: 9px;
                  vertical-align: middle;
                }
                td { 
                  border: 1px solid #000; 
                  padding: 1px 2px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-size: 8px;
                  vertical-align: top;
                  line-height: 1.2;
                  max-width: 120px;
                }
                td.long-content {
                  white-space: normal;
                  word-wrap: break-word;
                  word-break: break-word;
                  max-width: none;
                }
                .category-row { 
                  background-color: #e6f3ff !important; 
                  font-weight: bold; 
                }
                .category-row td {
                  padding: 2px 4px;
                  font-size: 10px;
                  white-space: normal;
                  word-wrap: break-word;
                }
                .subtotal-row { 
                  background-color: #f9f9f9 !important; 
                  font-weight: bold; 
                }
                .total-row { 
                  background-color: #f0f0f0 !important; 
                  font-weight: bold; 
                }
                .text-center { 
                  text-align: center; 
                }
                .text-right { 
                  text-align: right; 
                }
                ${getReportPrintStyles("財產清冊")}
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  // 匯出 Excel
  const handleExportExcel = () => {
    try {
      const csvData = convertToCSV(filteredData, true);
      const fileName = `${siteConfig.copyright}財產清冊_${dayjs().format(
        "YYYYMMDD"
      )}.csv`;
      downloadCSV(csvData, fileName);
      message.success("匯出成功");
    } catch (error) {
      console.error("匯出錯誤:", error);
      message.error("匯出失敗");
    }
  };

  // 轉換為 CSV 格式
  const convertToCSV = (data: any[], includeCompanyHeader: boolean = false) => {
    const headers = [
      "財產名稱",
      "存放地點",
      "數量",
      "單位",
      "取得日期",
      "耐用年限",
      "取得原價",
      "補助款",
      "未折減餘額",
      "保管者",
      "使用者",
      "財產編號",
      "說明",
    ];

    let csvContent = "";

    if (includeCompanyHeader) {
      csvContent += `${siteConfig.copyright || "公司名稱"}財產清冊\n`;
      csvContent += `列印日期：${dayjs().format("YYYY/MM/DD")}\n`;
      csvContent += `總計：${
        statistics.totalCount
      } 筆，總值：${formatTWCurrency(statistics.totalValue)}\n\n`;
    }

    csvContent += headers.join(",") + "\n";

    categoryStats.forEach((category) => {
      // 分類標題
      csvContent += `\n${category.categoryName}\n`;

      category.items.forEach((item) => {
        const asset = item.asset || item;
        const unitPrice = asset.unitPrice || 0;
        const subsidyAmount = asset.subsidyAmount || 0;
        const depreciation = asset.accumulatedDepreciationAmount || 0;
        const remainingValue = unitPrice - depreciation;

        const storageLocation = storageLocations.find(
          (location) => location.storageLocationId === asset.storageLocationId
        );

        const row = [
          asset.assetName || "",
          storageLocation?.name || "",
          asset.quantity || 1,
          item.unit?.name || "",
          asset.acquisitionDate
            ? dayjs(asset.acquisitionDate).format("YYYY/MM/DD")
            : "",
          asset.usefulLife || "",
          unitPrice,
          subsidyAmount,
          remainingValue,
          item.custodian?.name || "",
          item.assetUser?.name || "",
          asset.assetNo || "",
          asset.description || "",
        ];

        csvContent += row.join(",") + "\n";
      });

      // 小計行
      csvContent += `小計,,,,,,${category.totalValue},${category.totalSubsidy},${category.netValue},,,\n`;
    });

    // 合計行
    csvContent += `\n合計,,,,,,${statistics.totalValue},${statistics.totalSubsidy},${statistics.netValue},,,\n`;

    return csvContent;
  };

  // 下載 CSV 檔案
  const downloadCSV = (csvContent: string, filename: string) => {
    const BOM = "\uFEFF";
    const blob = new Blob([BOM + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // =========================== 初始化 ===========================
  useEffect(() => {
    const initializeData = async () => {
      await loadBaseData();
      await loadData();
    };
    initializeData();
  }, []);

  // 當科目資料載入完成後，重新計算分類統計
  useEffect(() => {
    if (assetAccounts.length > 0 && data.length > 0) {
      calculateStatistics(data);
    }
  }, [assetAccounts, data, calculateStatistics]);

  // =========================== 渲染組件 ===========================

  // 表格欄位定義
  const columns = [
    {
      title: "財產編號",
      key: "assetNo",
      width: 120,
      fixed: "left" as const,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.assetNo;
      },
    },
    {
      title: "財產名稱",
      dataIndex: ["asset", "assetName"],
      key: "assetName",
      width: 150,
      fixed: "left" as const,
      ellipsis: true,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.assetName;
      },
    },
    {
      title: "財產科目",
      key: "assetAccount",
      width: 120,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const account = assetAccounts.find(
          (acc) => acc.assetAccountId === asset.assetAccountId
        );
        return account?.assetAccountName || "";
      },
    },
    {
      title: "財產子目",
      key: "assetSubAccount",
      width: 120,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const subAccount = assetSubAccounts.find(
          (sub) => sub.assetSubAccountId === asset.assetSubAccountId
        );
        return subAccount?.assetSubAccountName || "";
      },
    },
    {
      title: "使用狀態",
      key: "assetStatus",
      width: 100,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const status = assetStatuses.find(
          (s) => s.assetStatusId === asset.assetStatusId
        );
        return status?.name || "";
      },
    },
    {
      title: "所屬部門",
      key: "department",
      width: 120,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const department = departments.find(
          (dept) => dept.departmentId === asset.departmentId
        );
        return department?.name || "";
      },
    },
    {
      title: "存放地點",
      key: "storageLocation",
      width: 120,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const storageLocation = storageLocations.find(
          (location) => location.storageLocationId === asset.storageLocationId
        );
        return storageLocation?.name || "";
      },
    },
    {
      title: "數量",
      key: "quantity",
      width: 80,
      align: "center" as const,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.quantity || 1;
      },
    },
    {
      title: "單位",
      key: "unit",
      width: 80,
      align: "center" as const,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const unit = units.find((u) => u.unitId === asset.unitId);
        return unit?.name || "";
      },
    },
    {
      title: "取得日期",
      key: "acquisitionDate",
      width: 120,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.acquisitionDate
          ? dayjs(asset.acquisitionDate).format("YYYY/MM/DD")
          : "";
      },
    },
    {
      title: "耐用年限",
      key: "serviceLife",
      width: 100,
      align: "center" as const,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.serviceLife || "";
      },
    },
    {
      title: "金額",
      children: [
        {
          title: "購入金額",
          key: "purchaseAmount",
          width: 120,
          align: "right" as const,
          render: (text: string, record: any) => {
            const asset = record.asset || record;
            return formatTWCurrency(
              asset.purchaseAmount || asset.unitPrice || 0
            );
          },
        },
        {
          title: "補助款",
          key: "subsidyAmount",
          width: 120,
          align: "right" as const,
          render: (text: string, record: any) => {
            const asset = record.asset || record;
            return formatTWCurrency(asset.subsidyAmount || 0);
          },
        },
        {
          title: "未折減餘額",
          key: "remainingValue",
          width: 120,
          align: "right" as const,
          render: (text: string, record: any) => {
            const asset = record.asset || record;
            const purchaseAmount = asset.purchaseAmount || asset.unitPrice || 0;
            const depreciation = asset.accumulatedDepreciationAmount || 0;
            return formatTWCurrency(purchaseAmount - depreciation);
          },
        },
      ],
    },
    {
      title: "保管者",
      key: "custodian",
      width: 100,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const custodian = users.find((u) => u.userId === asset.custodianId);
        return custodian?.name || "";
      },
    },
    {
      title: "使用者",
      key: "assetUser",
      width: 100,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        const assetUser = users.find((u) => u.userId === asset.userId);
        return assetUser?.name || "";
      },
    },
    {
      title: "規格",
      key: "specification",
      width: 150,
      ellipsis: true,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.specification || "";
      },
    },
    {
      title: "說明",
      key: "notes",
      ellipsis: true,
      render: (text: string, record: any) => {
        const asset = record.asset || record;
        return asset.notes || asset.description || "";
      },
    },
  ];

  return (
    <div style={{ padding: "20px" }}>
      <Card title={`財產清冊`}>
        {/* 統計卡片區域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={12} sm={8} md={6} lg={6}>
            <Card>
              <Statistic
                title="財產總數"
                value={statistics.totalCount}
                valueStyle={{ color: "#1890ff" }}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={6}>
            <Card>
              <Statistic
                title="財產總值"
                value={statistics.totalValue}
                valueStyle={{ color: "#1890ff" }}
                prefix={<DollarOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={6}>
            <Card>
              <Statistic
                title="累計折舊"
                value={statistics.totalDepreciation}
                valueStyle={{ color: "#fa8c16" }}
                prefix={<CalendarOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} md={6} lg={6}>
            <Card>
              <Statistic
                title="淨值"
                value={statistics.netValue}
                valueStyle={{ color: "#52c41a" }}
                prefix={<HomeOutlined />}
                formatter={(value) => formatTWCurrency(Number(value))}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜尋區域 */}
        <Card title="搜尋條件" style={{ marginBottom: "24px" }}>
          <Form form={searchForm} layout="vertical">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="keyword" label="關鍵字">
                  <Input placeholder="財產名稱、編號、規格" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetAccountId" label="財產科目">
                  <Select placeholder="請選擇" allowClear>
                    {assetAccounts.map((item: any) => (
                      <Option
                        key={item.assetAccountId}
                        value={item.assetAccountId}
                      >
                        {item.assetAccountName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetStatusId" label="使用狀態">
                  <Select placeholder="請選擇" allowClear>
                    {assetStatuses.map((item) => (
                      <Option
                        key={item.assetStatusId}
                        value={item.assetStatusId}
                      >
                        <Tag
                          color={STATUS_COLORS[item.name] || "default"}
                          style={{ marginRight: 8 }}
                        >
                          {item.name}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="departmentId" label="所屬部門">
                  <Select placeholder="請選擇" allowClear>
                    {departments.map((item) => (
                      <Option key={item.departmentId} value={item.departmentId}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="assetCategoryId" label="財產分類">
                  <Select placeholder="請選擇" allowClear>
                    {assetCategories.map((item: any) => (
                      <Option
                        key={item.assetCategoryId}
                        value={item.assetCategoryId}
                      >
                        {item.assetCategoryName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Form.Item name="storageLocationId" label="存放地點">
                  <Select placeholder="請選擇" allowClear>
                    {storageLocations.map((item: any) => (
                      <Option
                        key={item.storageLocationId}
                        value={item.storageLocationId}
                      >
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="acquisitionDateRange" label="取得日期範圍">
                  <RangePicker style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                  >
                    搜尋
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
                    重置
                  </Button>
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => setIsPrintMode(!isPrintMode)}
                  >
                    {isPrintMode ? "返回列表" : "預覽報表"}
                  </Button>
                  {isPrintMode && (
                    <Button
                      type="primary"
                      icon={<PrinterOutlined />}
                      onClick={handlePrint}
                    >
                      列印
                    </Button>
                  )}
                  <Button
                    icon={<FileExcelOutlined />}
                    onClick={handleExportExcel}
                  >
                    匯出 Excel
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 列印/預覽區域 */}
        <div ref={printRef}>
          {isPrintMode ? (
            // 列印模式
            <div
              style={{
                background: "white",
                padding: "5px",
                fontSize: "14px",
                fontFamily: "Arial, sans-serif",
              }}
            >
              {/* 報表標題 */}
              <ReportHeader
                reportTitle="財產清冊"
                currentPage={currentPage}
                totalPages={totalPages}
                isPrintMode={isPrintMode}
              />

              {/* 財產清冊表格 */}
              <table
                style={{
                  width: "100%",
                  borderCollapse: "collapse",
                  border: "1px solid #000",
                  fontSize: "11px",
                }}
              >
                <thead>
                  <tr style={{ backgroundColor: "#f5f5f5" }}>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      財產編號
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      財產名稱
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      財產科目
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      財產子目
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      使用狀態
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      所屬部門
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      存放地點
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      數量
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      單位
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      取得日期
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      耐用年限
                    </th>
                    <th
                      colSpan={3}
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      金額
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      保管者
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      使用者
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      規格
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        textAlign: "center",
                      }}
                    >
                      說明
                    </th>
                  </tr>
                  <tr style={{ backgroundColor: "#f5f5f5" }}>
                    <th colSpan={11} style={{ border: "none" }}></th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        fontSize: "10px",
                        textAlign: "center",
                      }}
                    >
                      購入金額
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        fontSize: "10px",
                        textAlign: "center",
                      }}
                    >
                      補助款
                    </th>
                    <th
                      style={{
                        border: "1px solid #000",
                        padding: "4px",
                        fontSize: "10px",
                        textAlign: "center",
                      }}
                    >
                      未折減餘額
                    </th>
                    <th colSpan={4} style={{ border: "none" }}></th>
                  </tr>
                </thead>
                <tbody>
                  {/* 按分類顯示資料 */}
                  {categoryStats.length === 0 ? (
                    <tr>
                      <td
                        colSpan={18}
                        style={{
                          border: "1px solid #000",
                          padding: "40px",
                          textAlign: "center",
                          fontSize: "16px",
                          color: "#999",
                        }}
                      >
                        <Empty
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                          description="查無符合條件的資料"
                        />
                      </td>
                    </tr>
                  ) : (
                    categoryStats.map((category, categoryIndex) => (
                      <React.Fragment key={categoryIndex}>
                        {/* 分類標題行 */}
                        <tr
                          className="category-row"
                          style={{
                            backgroundColor: "#e6f3ff",
                            fontWeight: "bold",
                          }}
                        >
                          <td
                            colSpan={14}
                            style={{
                              border: "1px solid #000",
                              padding: "4px 8px",
                              fontSize: "12px",
                            }}
                          >
                            {category.categoryName}
                          </td>
                        </tr>

                        {/* 該分類的財產項目 */}
                        {category.items.map((item, index) => {
                          const asset = item.asset || item;
                          const purchaseAmount =
                            asset.purchaseAmount || asset.unitPrice || 0;
                          const subsidyAmount = asset.subsidyAmount || 0;
                          const depreciation =
                            asset.accumulatedDepreciationAmount || 0;
                          const remainingValue = purchaseAmount - depreciation;

                          // 找出相關的名稱
                          const account = assetAccounts.find(
                            (acc) => acc.assetAccountId === asset.assetAccountId
                          );
                          const subAccount = assetSubAccounts.find(
                            (sub) =>
                              sub.assetSubAccountId === asset.assetSubAccountId
                          );
                          const status = assetStatuses.find(
                            (s) => s.assetStatusId === asset.assetStatusId
                          );
                          const department = departments.find(
                            (dept) => dept.departmentId === asset.departmentId
                          );
                          const unit = units.find(
                            (u) => u.unitId === asset.unitId
                          );
                          const custodian = users.find(
                            (u) => u.userId === asset.custodianId
                          );
                          const assetUser = users.find(
                            (u) => u.userId === asset.userId
                          );
                          const storageLocation = storageLocations.find(
                            (location) =>
                              location.storageLocationId ===
                              asset.storageLocationId
                          );

                          return (
                            <tr key={`${categoryIndex}-${index}`}>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {asset.assetNo}
                              </td>
                              <td
                                className={
                                  (asset.assetName || "").length > 15
                                    ? "long-content"
                                    : ""
                                }
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                }}
                              >
                                {asset.assetName}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {account?.assetAccountName || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {subAccount?.assetSubAccountName || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {status?.name || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {department?.name || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {storageLocation?.name || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {(asset.quantity || 1).toFixed(2)}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {unit?.name || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {asset.acquisitionDate
                                  ? dayjs(asset.acquisitionDate).format(
                                      "YY/MM/DD"
                                    )
                                  : ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {asset.serviceLife || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "right",
                                }}
                              >
                                {purchaseAmount.toLocaleString("zh-TW", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "right",
                                }}
                              >
                                {subsidyAmount.toLocaleString("zh-TW", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "right",
                                }}
                              >
                                {remainingValue.toLocaleString("zh-TW", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {custodian?.name || ""}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                  textAlign: "center",
                                }}
                              >
                                {assetUser?.name || ""}
                              </td>
                              <td
                                className={
                                  (asset.specification || "").length > 20
                                    ? "long-content"
                                    : ""
                                }
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                }}
                              >
                                {asset.specification || ""}
                              </td>
                              <td
                                className={
                                  (asset.notes || asset.description || "")
                                    .length > 20
                                    ? "long-content"
                                    : ""
                                }
                                style={{
                                  border: "1px solid #000",
                                  padding: "2px 4px",
                                }}
                              >
                                {asset.notes || asset.description || ""}
                              </td>
                            </tr>
                          );
                        })}

                        {/* 分類小計行 */}
                        <tr
                          className="subtotal-row"
                          style={{
                            backgroundColor: "#f9f9f9",
                            fontWeight: "bold",
                          }}
                        >
                          <td
                            colSpan={11}
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "center",
                            }}
                          >
                            <strong>{category.categoryName || ""}</strong> 小計
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {category.totalValue.toLocaleString("zh-TW", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {category.totalSubsidy.toLocaleString("zh-TW", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </td>
                          <td
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                              textAlign: "right",
                            }}
                          >
                            {category.netValue.toLocaleString("zh-TW", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </td>
                          <td
                            colSpan={4}
                            style={{
                              border: "1px solid #000",
                              padding: "2px 4px",
                            }}
                          ></td>
                        </tr>
                      </React.Fragment>
                    ))
                  )}

                  {/* 總合計行 */}
                  {categoryStats.length > 0 && (
                    <tr
                      className="total-row"
                      style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}
                    >
                      <td
                        colSpan={11}
                        style={{
                          border: "1px solid #000",
                          padding: "2px 4px",
                          textAlign: "center",
                        }}
                      >
                        合計
                      </td>
                      <td
                        style={{
                          border: "1px solid #000",
                          padding: "2px 4px",
                          textAlign: "right",
                        }}
                      >
                        {statistics.totalValue.toLocaleString("zh-TW", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </td>
                      <td
                        style={{
                          border: "1px solid #000",
                          padding: "2px 4px",
                          textAlign: "right",
                        }}
                      >
                        {statistics.totalSubsidy.toLocaleString("zh-TW", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </td>
                      <td
                        style={{
                          border: "1px solid #000",
                          padding: "2px 4px",
                          textAlign: "right",
                        }}
                      >
                        {statistics.netValue.toLocaleString("zh-TW", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </td>
                      <td
                        colSpan={4}
                        style={{ border: "1px solid #000", padding: "2px 4px" }}
                      ></td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            // 列表模式
            <Card
              title={`財產清單 共 ${filteredData.length} 筆資料`}
              extra={
                <Space>
                  <Badge count={filteredData.length} showZero>
                    <Button size="small">總計</Button>
                  </Badge>
                </Space>
              }
            >
              <Table
                columns={columns}
                dataSource={filteredData}
                rowKey={(record, index) => record.asset?.assetId || index}
                loading={loading}
                scroll={{ x: 1500 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span style={{ color: "#999" }}>
                          查無符合條件的資料
                          <br />
                          請調整搜尋條件後重新查詢
                        </span>
                      }
                    />
                  ),
                }}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
                  pageSizeOptions: ["10", "20", "50", "100"],
                }}
                summary={(pageData) => {
                  const totalValue = pageData.reduce((sum, record) => {
                    const asset = record.asset || record;
                    return sum + (asset.purchaseAmount || asset.unitPrice || 0);
                  }, 0);
                  const totalSubsidy = pageData.reduce((sum, record) => {
                    const asset = record.asset || record;
                    return sum + (asset.subsidyAmount || 0);
                  }, 0);
                  const totalDepreciation = pageData.reduce((sum, record) => {
                    const asset = record.asset || record;
                    return sum + (asset.accumulatedDepreciationAmount || 0);
                  }, 0);

                  return (
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={12}>
                        <Text strong>本頁小計</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={1}>
                        <Text strong>{formatTWCurrency(totalValue)}</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={2}>
                        <Text strong>{formatTWCurrency(totalSubsidy)}</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={3}>
                        <Text strong>
                          {formatTWCurrency(totalValue - totalDepreciation)}
                        </Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        index={4}
                        colSpan={4}
                      ></Table.Summary.Cell>
                    </Table.Summary.Row>
                  );
                }}
              />
            </Card>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AssetListPage;
