import React from "react";
import { But<PERSON>, Space, Tag, Tooltip, Descriptions, Typography } from "antd";
import {
  EditOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  StopOutlined,
  CheckOutlined,
  CloseOutlined,
  UserAddOutlined,
} from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import {
  STATUS_COLORS,
  STATUS_MAPPING,
  MAINTENANCE_TYPE_MAPPING,
  URGENCY_MAPPING,
  URGENCY_COLORS,
} from "./interface";
import { URGENCY_LEVELS } from "./config";
import { VendorMaintenance } from "./interface";
import { DateTimeExtensions } from "@/utils/dateTimeExtensions";
import { formatTWCurrency } from "@/utils/formatUtils";

const { Text } = Typography;

// 檢查狀態是否匹配（支援中文和英文）
const isStatusMatch = (
  currentStatus: string,
  targetStatus: string
): boolean => {
  return (
    currentStatus === targetStatus ||
    STATUS_MAPPING[currentStatus] === targetStatus
  );
};

// 獲取廠商修繕單表格列定義
export const getColumns = (
  handleView: (record: VendorMaintenance) => void,
  handleEdit: (record: VendorMaintenance) => void,
  handleApprove: (record: VendorMaintenance) => void,
  handleAssignVendor: (record: VendorMaintenance) => void,
  handleStartWork: (record: VendorMaintenance) => void,
  handleComplete: (record: VendorMaintenance) => void,
  handleInspect: (record: VendorMaintenance) => void,
  handleClose: (record: VendorMaintenance) => void,
  handleCancel: (record: VendorMaintenance) => void
): ColumnsType<VendorMaintenance> => {
  return [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
      sorter: (a, b) =>
        (a.maintenanceNumber || "").localeCompare(b.maintenanceNumber || ""),
    },
    {
      title: "修繕單號",
      dataIndex: "maintenanceNumber",
      key: "maintenanceNumber",
      width: 150,
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="修繕單號">
                  {record.maintenanceNumber}
                </Descriptions.Item>
                <Descriptions.Item label="財產名稱">
                  {record.assetName}
                </Descriptions.Item>
                <Descriptions.Item label="申請人">
                  {record.applicantName}
                </Descriptions.Item>
                <Descriptions.Item label="申請部門">
                  {record.applicantDepartment}
                </Descriptions.Item>
                <Descriptions.Item label="故障描述">
                  {record.faultDescription}
                </Descriptions.Item>
                <Descriptions.Item label="修繕類型">
                  {MAINTENANCE_TYPE_MAPPING[record.maintenanceType] ||
                    record.maintenanceType}
                </Descriptions.Item>
                <Descriptions.Item label="緊急程度">
                  <Tag color={URGENCY_COLORS[record.urgencyLevel] || "default"}>
                    {URGENCY_MAPPING[record.urgencyLevel] ||
                      record.urgencyLevel}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="預估費用">
                  {formatTWCurrency(record.estimatedCost)}
                </Descriptions.Item>
                <Descriptions.Item label="實際費用">
                  {record.actualCost
                    ? formatTWCurrency(record.actualCost)
                    : "-"}
                </Descriptions.Item>
                <Descriptions.Item label="廠商">
                  {record.vendorName || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="廠商聯絡人">
                  {record.vendorContact || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="廠商電話">
                  {record.vendorPhone || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="備註">
                  {record.notes || "-"}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
          styles={{
            root: { maxWidth: "500px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ fontWeight: "bold", color: "#1890ff" }}>{text}</span>
        </Tooltip>
      ),
      sorter: (a, b) => (a.assetName || "").localeCompare(b.assetName || ""),
    },
    {
      title: "財產名稱",
      dataIndex: "assetName",
      key: "assetName",
      ellipsis: true,
      sorter: (a, b) => (a.assetName || "").localeCompare(b.assetName || ""),
    },
    {
      title: "申請人",
      dataIndex: "applicantName",
      key: "applicantName",
      sorter: (a, b) =>
        (a.applicantName || "").localeCompare(b.applicantName || ""),
    },
    {
      title: "修繕類型",
      dataIndex: "maintenanceType",
      key: "maintenanceType",
      render: (text) => {
        const displayText = MAINTENANCE_TYPE_MAPPING[text] || text;
        return <Tag color="blue">{displayText}</Tag>;
      },
      sorter: (a, b) =>
        (a.maintenanceType || "").localeCompare(b.maintenanceType || ""),
    },
    {
      title: "緊急程度",
      dataIndex: "urgencyLevel",
      key: "urgencyLevel",
      render: (text) => {
        const displayText = URGENCY_MAPPING[text] || text;
        const color = URGENCY_COLORS[text] || "default";
        return <Tag color={color}>{displayText}</Tag>;
      },
      sorter: (a, b) =>
        (a.urgencyLevel || "").localeCompare(b.urgencyLevel || ""),
    },
    {
      title: "狀態",
      dataIndex: "status",
      key: "status",
      render: (text) => {
        const displayText = STATUS_MAPPING[text] || text;
        const color = STATUS_COLORS[text] || "default";
        return <Tag color={color}>{displayText}</Tag>;
      },
      sorter: (a, b) => (a.status || "").localeCompare(b.status || ""),
    },
    {
      title: "申請日期",
      dataIndex: "applicationDate",
      key: "applicationDate",
      render: (timestamp) =>
        DateTimeExtensions.formatDateFromTimestamp(timestamp),
      sorter: (a, b) => (a.applicationDate || 0) - (b.applicationDate || 0),
    },
    {
      title: "預估費用",
      dataIndex: "estimatedCost",
      key: "estimatedCost",
      render: (cost) => <Text type="secondary">{formatTWCurrency(cost)}</Text>,
      sorter: (a, b) => (a.estimatedCost || 0) - (b.estimatedCost || 0),
    },
    {
      title: "實際費用",
      dataIndex: "actualCost",
      key: "actualCost",
      render: (cost) => (cost ? formatTWCurrency(cost) : "-"),
      sorter: (a, b) => (a.actualCost || 0) - (b.actualCost || 0),
    },
    {
      title: "廠商",
      dataIndex: "vendorName",
      key: "vendorName",
      render: (text) => text || "-",
      sorter: (a, b) => (a.vendorName || "").localeCompare(b.vendorName || ""),
    },
    {
      title: "操作",
      key: "action",
      width: 300,
      render: (_, record) => {
        const actions = [];

        // 查看按鈕 - 所有狀態都可查看
        actions.push(
          <Button
            key="view"
            type="link"
            icon={<FileTextOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
        );

        // 編輯按鈕 - 待審核狀態可編輯
        if (isStatusMatch(record.status, "待審核")) {
          actions.push(
            <Button
              key="edit"
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              編輯
            </Button>
          );
        }

        // 審核按鈕 - 待審核狀態
        if (isStatusMatch(record.status, "待審核")) {
          actions.push(
            <Button
              key="approve"
              type="link"
              icon={<CheckCircleOutlined />}
              onClick={() => handleApprove(record)}
            >
              審核
            </Button>
          );
        }

        // 指派廠商按鈕 - 已審核狀態
        if (isStatusMatch(record.status, "已審核")) {
          actions.push(
            <Button
              key="assign"
              type="link"
              icon={<UserAddOutlined />}
              onClick={() => handleAssignVendor(record)}
            >
              指派廠商
            </Button>
          );
        }

        // 開始施工按鈕 - 已指派狀態
        if (isStatusMatch(record.status, "已指派")) {
          actions.push(
            <Button
              key="start"
              type="link"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartWork(record)}
            >
              開始施工
            </Button>
          );
        }

        // 完成修繕按鈕 - 施工中狀態
        if (isStatusMatch(record.status, "施工中")) {
          actions.push(
            <Button
              key="complete"
              type="link"
              icon={<CheckOutlined />}
              onClick={() => handleComplete(record)}
            >
              完成修繕
            </Button>
          );
        }

        // 驗收按鈕 - 已完成狀態
        if (isStatusMatch(record.status, "已完成")) {
          actions.push(
            <Button
              key="inspect"
              type="link"
              icon={<CheckCircleOutlined />}
              onClick={() => handleInspect(record)}
            >
              驗收
            </Button>
          );
        }

        // 結案按鈕 - 已驗收狀態
        if (isStatusMatch(record.status, "已驗收")) {
          actions.push(
            <Button
              key="close"
              type="link"
              icon={<CloseOutlined />}
              onClick={() => handleClose(record)}
            >
              結案
            </Button>
          );
        }

        // 取消按鈕 - 待審核、已審核、已指派狀態可取消
        if (
          isStatusMatch(record.status, "待審核") ||
          isStatusMatch(record.status, "已審核") ||
          isStatusMatch(record.status, "已指派")
        ) {
          actions.push(
            <Button
              key="cancel"
              type="link"
              danger
              icon={<StopOutlined />}
              onClick={() => handleCancel(record)}
            >
              取消
            </Button>
          );
        }

        return (
          <Space size="small" wrap>
            {actions}
          </Space>
        );
      },
    },
  ];
};

// 獲取手機版表格列定義
export const getMobileColumns = (
  handleView: (record: VendorMaintenance) => void,
  handleEdit: (record: VendorMaintenance) => void
): ColumnsType<VendorMaintenance> => [
  {
    title: "修繕申請",
    key: "mobile",
    render: (_, record) => (
      <div>
        <div style={{ marginBottom: "8px" }}>
          <Text strong>{record.maintenanceNumber}</Text>
          <Tag
            color={STATUS_COLORS[record.status] || "default"}
            style={{ marginLeft: "8px" }}
          >
            {STATUS_MAPPING[record.status] || record.status}
          </Tag>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">財產：</Text>
          <Text>{record.assetName}</Text>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">申請人：</Text>
          <Text>{record.applicantName}</Text>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">修繕類型：</Text>
          <Tag color="blue">
            {MAINTENANCE_TYPE_MAPPING[record.maintenanceType] ||
              record.maintenanceType}
          </Tag>
          <Tag color={URGENCY_COLORS[record.urgencyLevel] || "default"}>
            {URGENCY_MAPPING[record.urgencyLevel] || record.urgencyLevel}
          </Tag>
        </div>
        <div style={{ marginBottom: "4px" }}>
          <Text type="secondary">申請日期：</Text>
          <Text>
            {DateTimeExtensions.formatDateFromTimestamp(record.applicationDate)}
          </Text>
        </div>
        <div>
          <Button type="link" size="small" onClick={() => handleView(record)}>
            查看詳情
          </Button>
          {isStatusMatch(record.status, "待審核") && (
            <Button type="link" size="small" onClick={() => handleEdit(record)}>
              編輯
            </Button>
          )}
        </div>
      </div>
    ),
  },
];
