using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("附屬設備管理")]
    public class AccessoryEquipmentController : ControllerBase
    {
        private readonly IAccessoryEquipmentService _accessoryEquipmentService;

        public AccessoryEquipmentController(IAccessoryEquipmentService accessoryEquipmentService)
        {
            _accessoryEquipmentService = accessoryEquipmentService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得附屬設備列表", Description = "取得所有附屬設備列表")]
        public async Task<IActionResult> GetAccessoryEquipmentList()
        {
            var equipments = await _accessoryEquipmentService.GetAllAsync();
            return Ok(equipments);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得附屬設備明細", Description = "依ID取得附屬設備明細")]
        public async Task<IActionResult> GetAccessoryEquipmentDetail(Guid id)
        {
            var equipment = await _accessoryEquipmentService.GetByIdAsync(id);
            if (equipment == null)
                return NotFound($"找不到編號為{id}的附屬設備。");

            return Ok(equipment);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增附屬設備", Description = "新增附屬設備")]
        public async Task<IActionResult> AddAccessoryEquipment([FromBody] AccessoryEquipmentDTO accessoryEquipment)
        {
            var result = await _accessoryEquipmentService.AddAsync(accessoryEquipment);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯附屬設備", Description = "編輯附屬設備")]
        public async Task<IActionResult> EditAccessoryEquipment([FromBody] AccessoryEquipmentDTO accessoryEquipment)
        {
            var result = await _accessoryEquipmentService.UpdateAsync(accessoryEquipment);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除附屬設備", Description = "刪除附屬設備")]
        public async Task<IActionResult> DeleteAccessoryEquipment([FromBody] AccessoryEquipmentDTO accessoryEquipment)
        {
            var result = await _accessoryEquipmentService.DeleteAsync(accessoryEquipment);
            if (!result.Item1)
                return BadRequest(result.Item2);

            return Ok(result.Item2);
        }
    }
}
