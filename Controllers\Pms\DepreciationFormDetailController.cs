﻿using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models.Pms;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Pms
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("折舊紀錄管理")]
    public class DepreciationFormDetailController : ControllerBase
    {
        private readonly IDepreciationFormDetailService _depreciationService;

        public DepreciationFormDetailController(IDepreciationFormDetailService depreciationService)
        {
            _depreciationService = depreciationService;
        }

        [HttpGet]
        [Route("GetAll")]
        [SwaggerOperation(Summary = "取得折舊紀錄", Description = "取得所有財產折舊紀錄")]
        public async Task<IActionResult> GetDepreciation()
        {
            var result = await _depreciationService.GetDepreciationAsync();
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{id}")]
        [SwaggerOperation(Summary = "取得折舊紀錄", Description = "依資產ID取得財產折舊紀錄")]
        public async Task<IActionResult> GetDepreciationDetail(string id)
        {
            var result = await _depreciationService.GetDepreciationByAssetAsync(id);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增折舊紀錄", Description = "新增財產折舊紀錄")]
        public async Task<IActionResult> AddDepreciation([FromBody] DepreciationFormDetailDTO data)
        {
            var (success, message) = await _depreciationService.AddDepreciationAsync(data);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯折舊紀錄", Description = "修改已存在之財產折舊紀錄")]
        public async Task<IActionResult> EditDepreciation([FromBody] DepreciationFormDetailDTO data)
        {
            var (success, message) = await _depreciationService.EditDepreciationAsync(data);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除折舊紀錄", Description = "刪除已存在之財產折舊紀錄")]
        public async Task<IActionResult> DeleteDepreciation([FromBody] DepreciationFormDetailDTO data)
        {
            var (success, message) = await _depreciationService.DeleteDepreciationAsync(data);
            if (success)
            {
                return Ok(new { message });
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("Methods")]
        [SwaggerOperation(Summary = "取得折舊方法", Description = "取得系統支援的折舊方法列表")]
        public async Task<IActionResult> GetDepreciationMethods()
        {
            var (success, methods, message) = await _depreciationService.GetAvailableDepreciationMethodsAsync();
            if (success)
            {
                return Ok(methods);
            }
            return BadRequest(new { message });
        }

        [HttpGet]
        [Route("DecliningBalanceRate/{assetAccountId}")]
        [SwaggerOperation(Summary = "取得餘額遞減法折舊率", Description = "取得指定財產科目的餘額遞減法折舊率")]
        public async Task<IActionResult> GetDecliningBalanceRate(string assetAccountId)
        {
            var (success, rate, message) = await _depreciationService.GetDecliningBalanceRateForAssetAccountAsync(assetAccountId);
            if (success)
            {
                return Ok(new { rate, message });
            }
            return BadRequest(new { message });
        }

    }

    /// <summary>
    /// 折舊計算請求參數
    /// </summary>
    public class DepreciationCalculationRequest
    {
        /// <summary>
        /// 資產ID
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 操作用戶ID
        /// </summary>
        public string UserId { get; set; }
    }

    /// <summary>
    /// 批次折舊計算請求參數
    /// </summary>
    public class BatchDepreciationCalculationRequest
    {
        /// <summary>
        /// 資產ID列表
        /// </summary>
        public List<string> AssetIds { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 操作用戶ID
        /// </summary>
        public string UserId { get; set; }
    }

    /// <summary>
    /// 折舊試算請求參數
    /// </summary>
    public class DepreciationSimulationRequest
    {
        /// <summary>
        /// 資產ID
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }
    }

    /// <summary>
    /// 批次折舊試算請求參數
    /// </summary>
    public class BatchDepreciationSimulationRequest
    {
        /// <summary>
        /// 資產ID列表
        /// </summary>
        public List<string> AssetIds { get; set; }

        /// <summary>
        /// 折舊年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 折舊月份
        /// </summary>
        public int Month { get; set; }
    }
}

