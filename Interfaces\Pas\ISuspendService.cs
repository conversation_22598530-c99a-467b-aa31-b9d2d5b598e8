using FAST_ERP_Backend.Models.Pas;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pas
{
    public interface ISuspendService
    {
        /// <summary>
        /// 取得所有留職停薪資料列表
        /// </summary>
        /// <param name="_userId">使用者編號</param>
        /// <returns>留職停薪資料列表</returns>
        Task<List<SuspendDTO>> GetSuspendListAsync(string _userId);

        /// <summary>
        /// 取得留職停薪資料明細
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>留職停薪資料明細</returns>
        Task<SuspendDTO> GetSuspendDetailAsync(string _uid);

        /// <summary>
        /// 新增留職停薪資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddSuspendAsync(SuspendDTO _data);

        /// <summary>
        /// 編輯留職停薪資料
        /// </summary>
        /// <param name="_data">資料內容</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditSuspendAsync(SuspendDTO _data);

        /// <summary>
        /// 刪除留職停薪資料
        /// </summary>
        /// <param name="_uid">資料編號</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteSuspendAsync(string _uid);
    }
}
