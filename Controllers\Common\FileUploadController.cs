using System;
using System.Security.Claims;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common;
/// <summary> 檔案上傳管理控制器 </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[SwaggerTag("檔案上傳管理")]
public class FileUploadController(IFileUploadService _fileUploadService, ICurrentUserService _currentUserService) : ControllerBase
{
    /// <summary> 上傳單一檔案 </summary>
    [HttpPost]
    [Route("Upload")]
    [SwaggerOperation(Summary = "上傳單一檔案", Description = "上傳單一檔案到指定企業群組")]
    public async Task<IActionResult> UploadFile([FromForm] FileUploadRequestDTO request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            var (success, message, fileInfo) = await _fileUploadService.UploadFileAsync(request, _currentUserService.UserId);

            if (success)
            {
                return Ok(new { success = true, message, data = fileInfo });
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"檔案上傳失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 批量上傳檔案 </summary>
    [HttpPost]
    [Route("UploadBatch")]
    [SwaggerOperation(Summary = "批量上傳檔案", Description = "批量上傳多個檔案到指定企業群組")]
    public async Task<IActionResult> UploadFiles([FromForm] BatchFileUploadRequestDTO request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            var (success, message, fileInfos) = await _fileUploadService.UploadFilesAsync(request, userId);

            if (success)
            {
                return Ok(new { success = true, message, data = fileInfos });
            }
            else
            {
                return BadRequest(new { success = false, message, data = fileInfos });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"批量上傳失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 下載檔案 </summary>
    [HttpGet]
    [Route("Download/{fileUploadId}")]
    [SwaggerOperation(Summary = "下載檔案", Description = "根據檔案編號下載檔案")]
    public async Task<IActionResult> DownloadFile(
        [FromRoute] string fileUploadId,
        [FromQuery] string enterpriseGroupsId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            var request = new FileDownloadRequestDTO
            {
                FileUploadId = fileUploadId,
                EnterpriseGroupsId = enterpriseGroupsId
            };

            var (success, message, fileStream, fileInfo) = await _fileUploadService.DownloadFileAsync(request, userId);

            if (success && fileStream != null && fileInfo != null)
            {
                return File(fileStream, fileInfo.ContentType, fileInfo.OriginalFileName);
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"檔案下載失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 取得檔案列表 </summary>
    [HttpPost]
    [Route("GetFiles")]
    [SwaggerOperation(Summary = "取得檔案列表", Description = "根據條件查詢檔案列表")]
    public async Task<IActionResult> GetFiles([FromBody] FileQueryRequestDTO request)
    {
        try
        {
            var (success, message, files, totalCount) = await _fileUploadService.GetFilesAsync(request);

            if (success)
            {
                return Ok(new
                {
                    success = true,
                    message,
                    data = new { files, totalCount, pageIndex = request.PageIndex, pageSize = request.PageSize }
                });
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得檔案列表失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary>
    /// 取得檔案資訊
    /// </summary>
    [HttpGet]
    [Route("GetFileInfo/{fileUploadId}")]
    [SwaggerOperation(Summary = "取得檔案資訊", Description = "根據檔案編號取得檔案詳細資訊")]
    public async Task<IActionResult> GetFileInfo(
        [FromRoute] string fileUploadId,
        [FromQuery] string enterpriseGroupsId)
    {
        try
        {
            var (success, message, fileInfo) = await _fileUploadService.GetFileInfoAsync(fileUploadId, enterpriseGroupsId);

            if (success)
            {
                return Ok(new { success = true, message, data = fileInfo });
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得檔案資訊失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary>
    /// 更新檔案資訊
    /// </summary>
    [HttpPatch]
    [Route("UpdateFileInfo")]
    [SwaggerOperation(Summary = "更新檔案資訊", Description = "更新檔案的描述、排序等資訊")]
    public async Task<IActionResult> UpdateFileInfo([FromBody] FileUploadDTO fileInfo)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            var (success, message) = await _fileUploadService.UpdateFileInfoAsync(fileInfo, userId);

            if (success)
            {
                return Ok(new { success = true, message, data = (object?)null });
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"更新檔案資訊失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary>
    /// 刪除檔案
    /// </summary>
    [HttpDelete]
    [Route("Delete/{fileUploadId}")]
    [SwaggerOperation(Summary = "刪除檔案", Description = "根據檔案編號刪除檔案")]
    public async Task<IActionResult> DeleteFile(
        [FromRoute] string fileUploadId,
        [FromQuery] string enterpriseGroupsId)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
            var (success, message) = await _fileUploadService.DeleteFileAsync(fileUploadId, enterpriseGroupsId, userId);

            if (success)
            {
                return Ok(new { success = true, message, data = (object?)null });
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"刪除檔案失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary>
    /// 取得企業群組儲存統計
    /// </summary>
    [HttpGet]
    [Route("GetStorageStats/{enterpriseGroupsId}")]
    [SwaggerOperation(Summary = "取得儲存統計", Description = "取得指定企業群組的檔案儲存統計資訊")]
    public async Task<IActionResult> GetStorageStats([FromRoute] string enterpriseGroupsId)
    {
        try
        {
            var (totalFiles, totalSize, totalDownloads) = await _fileUploadService.GetStorageStatsAsync(enterpriseGroupsId);

            return Ok(new
            {
                success = true,
                message = "取得儲存統計成功",
                data = new
                {
                    totalFiles,
                    totalSize,
                    totalDownloads,
                    totalSizeMB = Math.Round(totalSize / 1024.0 / 1024.0, 2)
                }
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得儲存統計失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary>
    /// 檢查檔案是否存在
    /// </summary>
    [HttpGet]
    [Route("Exists/{fileUploadId}")]
    [SwaggerOperation(Summary = "檢查檔案是否存在", Description = "檢查指定檔案是否存在")]
    public async Task<IActionResult> FileExists(
        [FromRoute] string fileUploadId,
        [FromQuery] string enterpriseGroupsId)
    {
        try
        {
            var exists = await _fileUploadService.FileExistsAsync(fileUploadId, enterpriseGroupsId);
            return Ok(new { success = true, message = "檢查完成", data = new { exists } });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"檢查檔案失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary>
    /// 清理過期檔案
    /// </summary>
    [HttpPost]
    [Route("CleanupExpiredFiles")]
    [SwaggerOperation(Summary = "清理過期檔案", Description = "清理指定天數前刪除的檔案")]
    public async Task<IActionResult> CleanupExpiredFiles([FromBody] CleanupExpiredFilesRequestDTO request)
    {
        try
        {
            var (success, message, cleanedCount) = await _fileUploadService.CleanupExpiredFilesAsync(request.DaysOld);

            if (success)
            {
                return Ok(new { success = true, message, data = new { cleanedCount } });
            }
            else
            {
                return BadRequest(new { success = false, message, data = (object?)null });
            }
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"清理過期檔案失敗: {ex.Message}", data = (object?)null });
        }
    }
}

/// <summary> 清理過期檔案請求 DTO </summary>
public class CleanupExpiredFilesRequestDTO
{
    /// <summary>
    /// 過期天數（預設30天）
    /// </summary>
    public int DaysOld { get; set; } = 30;
}
