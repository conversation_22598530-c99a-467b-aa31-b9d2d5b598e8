import { httpClient } from "@/services/http";
import { ApiResponse } from "@/config/api";

// API 路徑
const API_BASE = "/AssetLocationTransfer";

// 介面定義
export interface AssetLocationTransfer {
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime: number;
    deleteUserId: string;
    isDeleted: boolean;
    transferId: string;
    transferNo: string;
    transferDate: number;
    applicantId: string;
    applicantName: string;
    applicantDepartmentId: string;
    applicantDepartmentName: string;
    transferReason: string;
    approvalStatus: string;
    approverId: string;
    approverName: string;
    approvalDate: number;
    approvalComments: string;
    executionStatus: string;
    executorId: string;
    executorName: string;
    executionDate: number;
    notes: string;
}

export interface AssetLocationTransferDetail {
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime: number;
    deleteUserId: string;
    isDeleted: boolean;
    detailId: string;
    transferId: string;
    assetId: string;
    assetNo: string;
    assetName: string;
    originalLocationId: string;
    originalLocationName: string;
    newLocationId: string;
    newLocationName: string;
    originalCustodianId: string;
    originalCustodianName: string;
    newCustodianId: string;
    newCustodianName: string;
    originalUserId: string;
    originalUserName: string;
    newUserId: string;
    newUserName: string;
    originalDepartmentId: string;
    originalDepartmentName: string;
    newDepartmentId: string;
    newDepartmentName: string;
    originalDivisionId: string;
    originalDivisionName: string;
    newDivisionId: string;
    newDivisionName: string;
    changeItems: string;
    detailNotes: string;
}

export interface AssetLocationTransferWithDetails {
    transfer: AssetLocationTransfer;
    details: AssetLocationTransferDetail[];
}

export interface AssetLocationTransferQuery {
    searchTerm?: string;
    approvalStatus?: string;
    executionStatus?: string;
    startDate?: number;
    endDate?: number;
}

export interface ApprovalRequest {
    transferNo: string;
    approvalStatus: string;
    approvalComments: string;
    approverId: string;
}

export interface ExecutionRequest {
    transferNo: string;
    executionStatus: string;
    executorId: string;
}

export interface CurrentLocationInfo {
    assetId: string;
    assetNo: string;
    assetName: string;
    currentLocationId: string;
    currentLocationName: string;
    currentCustodianId: string;
    currentCustodianName: string;
    currentUserId: string;
    currentUserName: string;
    currentDepartmentId: string;
    currentDepartmentName: string;
    currentDivisionId: string;
    currentDivisionName: string;
}

// API 服務函數
export const getAssetLocationTransfers = async (
    query?: AssetLocationTransferQuery
): Promise<ApiResponse<AssetLocationTransfer[]>> => {
    try {
        const params = new URLSearchParams();
        if (query?.searchTerm) params.append("searchTerm", query.searchTerm);
        if (query?.approvalStatus) params.append("approvalStatus", query.approvalStatus);
        if (query?.executionStatus) params.append("executionStatus", query.executionStatus);
        if (query?.startDate) params.append("startDate", query.startDate.toString());
        if (query?.endDate) params.append("endDate", query.endDate.toString());

        const url = params.toString() ? `${API_BASE}?${params.toString()}` : API_BASE;
        return await httpClient<AssetLocationTransfer[]>(url, { method: "GET" });
    } catch (error: any) {
        console.error("取得財產位置變動單列表失敗:", error);
        return {
            success: false,
            data: [],
            message: error.message || "取得資料失敗",
        };
    }
};

export const getAssetLocationTransferByNo = async (
    transferNo: string
): Promise<ApiResponse<AssetLocationTransferWithDetails>> => {
    try {
        return await httpClient<AssetLocationTransferWithDetails>(`${API_BASE}/${transferNo}`, {
            method: "GET",
        });
    } catch (error: any) {
        console.error("取得財產位置變動單詳細資料失敗:", error);
        return {
            success: false,
            data: null as any,
            message: error.message || "取得資料失敗",
        };
    }
};

export const createAssetLocationTransfer = async (
    data: AssetLocationTransferWithDetails
): Promise<ApiResponse<any>> => {
    try {
        return await httpClient(API_BASE, {
            method: "POST",
            body: JSON.stringify(data),
        });
    } catch (error: any) {
        console.error("新增財產位置變動單失敗:", error);
        return {
            success: false,
            data: null,
            message: error.message || "新增失敗",
        };
    }
};

export const updateAssetLocationTransfer = async (
    transferNo: string,
    data: AssetLocationTransferWithDetails
): Promise<ApiResponse<any>> => {
    try {
        return await httpClient(`${API_BASE}/${transferNo}`, {
            method: "POST",
            body: JSON.stringify(data),
        });
    } catch (error: any) {
        console.error("更新財產位置變動單失敗:", error);
        return {
            success: false,
            data: null,
            message: error.message || "更新失敗",
        };
    }
};

export const deleteAssetLocationTransfer = async (
    transferNo: string
): Promise<ApiResponse<any>> => {
    try {
        return await httpClient(`${API_BASE}/${transferNo}/delete`, {
            method: "POST",
        });
    } catch (error: any) {
        console.error("刪除財產位置變動單失敗:", error);
        return {
            success: false,
            data: null,
            message: error.message || "刪除失敗",
        };
    }
};

export const approveAssetLocationTransfer = async (
    transferNo: string,
    approvalData: ApprovalRequest
): Promise<ApiResponse<any>> => {
    try {
        return await httpClient(`${API_BASE}/${transferNo}/approve`, {
            method: "POST",
            body: JSON.stringify(approvalData),
        });
    } catch (error: any) {
        console.error("審核財產位置變動單失敗:", error);
        return {
            success: false,
            data: null,
            message: error.message || "審核失敗",
        };
    }
};

export const executeAssetLocationTransfer = async (
    transferNo: string,
    executionData: ExecutionRequest
): Promise<ApiResponse<any>> => {
    try {
        return await httpClient(`${API_BASE}/${transferNo}/execute`, {
            method: "POST",
            body: JSON.stringify(executionData),
        });
    } catch (error: any) {
        console.error("執行財產位置變動失敗:", error);
        return {
            success: false,
            data: null,
            message: error.message || "執行失敗",
        };
    }
};

export const getAssetCurrentLocation = async (
    assetNo: string
): Promise<ApiResponse<CurrentLocationInfo>> => {
    try {
        return await httpClient<CurrentLocationInfo>(`${API_BASE}/asset/${assetNo}/current-location`, {
            method: "GET",
        });
    } catch (error: any) {
        console.error("取得財產位置資訊失敗:", error);
        return {
            success: false,
            data: null as any,
            message: error.message || "取得資料失敗",
        };
    }
};

export const validateAssetForTransfer = async (
    assetNo: string
): Promise<ApiResponse<any>> => {
    try {
        return await httpClient(`${API_BASE}/asset/${assetNo}/validate`, {
            method: "GET",
        });
    } catch (error: any) {
        console.error("驗證財產失敗:", error);
        return {
            success: false,
            data: null,
            message: error.message || "驗證失敗",
        };
    }
};

export const generateTransferNo = async (
    transferDate?: number
): Promise<ApiResponse<string>> => {
    try {
        const params = transferDate ? `?transferDate=${transferDate}` : "";
        return await httpClient<string>(`${API_BASE}/generate-transfer-no${params}`, {
            method: "GET",
        });
    } catch (error: any) {
        console.error("產生變動單號失敗:", error);
        return {
            success: false,
            data: "",
            message: error.message || "產生失敗",
        };
    }
}; 