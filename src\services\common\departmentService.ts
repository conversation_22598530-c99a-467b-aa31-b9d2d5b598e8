import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 部門資訊
export interface Department {
    departmentId: string;
    name: string;
    sortCode: number;
    enterpriseGroupId: string;
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
}

// 獲取部門列表
export async function getDepartments(): Promise<ApiResponse<Department[]>> {
    try {
        const response = await httpClient(apiEndpoints.getDepartments, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取部門列表失敗",
            data: []
        };
    }
}

// 獲取單一部門資訊
export async function getDepartmentDetail(id: string): Promise<ApiResponse<Department>> {
    try {
        const response = await httpClient(`${apiEndpoints.getDepartmentDetail}/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取部門資訊失敗",
            data: undefined
        };
    }
}

// 新增部門資訊
export async function createDepartment(data: Partial<Department>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addDepartment, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增部門資訊失敗",
        };
    }
}

// 更新部門資訊
export async function updateDepartment(data: Partial<Department>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editDepartment, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新部門資訊失敗",
        };
    }
}

// 刪除部門資訊
export async function deleteDepartment(data: Partial<Department>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteDepartment, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除部門資訊失敗",
        };
    }
} 