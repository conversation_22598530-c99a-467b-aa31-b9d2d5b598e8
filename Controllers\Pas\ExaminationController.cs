using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Pas;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;

namespace FAST_ERP_Backend.Controllers.Pas
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("考試資料管理")]
    public class ExaminationController : ControllerBase
    {
        private readonly IExaminationService _interface;

        public ExaminationController(IExaminationService examinationService)
        {
            _interface = examinationService;
        }

        [HttpGet]
        [Route("GetAll/{_userid}")]
        [SwaggerOperation(Summary = "取得列表", Description = "取得所有考試資料列表")]
        public async Task<IActionResult> GetExaminationList(string _userid)
        {
            var result = await _interface.GetExaminationListAsync(_userid);
            return Ok(result);
        }

        [HttpGet]
        [Route("Get/{_uid}")]
        [SwaggerOperation(Summary = "取得考試明細", Description = "依uid取得考試明細")]
        public async Task<IActionResult> GetExaminationDetail(string _uid)
        {
            var result = await _interface.GetExaminationDetailAsync(_uid);
            return Ok(result);
        }

        [HttpPost]
        [Route("Add")]
        [SwaggerOperation(Summary = "新增考試資料", Description = "新增考試資料")]
        public async Task<IActionResult> AddExamination([FromBody] ExaminationDTO _data)
        {
            var (result, msg) = await _interface.AddExaminationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Edit")]
        [SwaggerOperation(Summary = "編輯考試資料", Description = "編輯考試資料")]
        public async Task<IActionResult> EditExamination([FromBody] ExaminationDTO _data)
        {
            var (result, msg) = await _interface.EditExaminationAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("Delete")]
        [SwaggerOperation(Summary = "刪除考試資料", Description = "刪除考試資料")]
        public async Task<IActionResult> DeleteExamination([FromBody] string _uid)
        {
            var (result, msg) = await _interface.DeleteExaminationAsync(_uid);
            return Ok(new { result, msg });
        }
    }
}
