"use client";

/* 選單設定
  /app/common/menu/page.tsx
*/
import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  TreeSelect,
  Tag,
  Select,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  SystemMenu,
  getSystemMenus,
  createSystemMenu,
  updateSystemMenu,
  deleteSystemMenu,
} from "@/services/common/systemMenuService";
import {
  SystemGroup,
  getSystemGroup,
  getSystemGroups,
} from "@/services/common/systemGroupService";
import { notifySuccess, notifyError } from "@/utils/notification";
import * as AntdIcons from "@ant-design/icons";

const SystemMenuPage: React.FC = () => {
  const [menus, setMenus] = useState<SystemMenu[]>([]);
  const [filteredMenus, setFilteredMenus] = useState<SystemMenu[]>([]);
  const [searchText, setSearchText] = useState("");
  const [selectedGroupId, setSelectedGroupId] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingMenu, setEditingMenu] = useState<SystemMenu | null>(null);
  const [systemGroups, setSystemGroups] = useState<SystemGroup[]>([]);
  const [form] = Form.useForm();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [menuToDelete, setMenuToDelete] = useState<SystemMenu | null>(null);

  // 獲取所有可用的圖標
  const iconList = Object.keys(AntdIcons)
    .filter(
      (key) =>
        key.endsWith("Outlined") ||
        key.endsWith("Filled") ||
        key.endsWith("TwoTone")
    )
    .sort();

  // 加載選單列表和系統群組
  const loadMenus = async () => {
    setLoading(true);
    try {
      const [menuResponse, groupResponse] = await Promise.all([
        getSystemMenus(),
        getSystemGroups(),
      ]);

      if (menuResponse.success && menuResponse.data) {
        setMenus(menuResponse.data);
      } else {
        notifyError("獲取選單列表失敗", menuResponse.message);
      }

      if (groupResponse.success && groupResponse.data) {
        setSystemGroups(groupResponse.data);
      } else {
        notifyError("獲取系統群組失敗", groupResponse.message);
      }
    } catch (error) {
      notifyError("獲取選單列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMenus();
  }, []);

  // 處理搜尋和篩選
  useEffect(() => {
    let filtered = menus;

    // 先按系統群組篩選
    if (selectedGroupId) {
      filtered = filtered.filter(
        (menu) => menu.systemGroupId === selectedGroupId
      );
    }

    // 再按搜尋文字篩選
    if (searchText) {
      filtered = filtered.filter(
        (menu) =>
          menu.label.toLowerCase().includes(searchText.toLowerCase()) ||
          menu.key.toLowerCase().includes(searchText.toLowerCase()) ||
          getSystemGroupName(menu.systemGroupId)
            .toLowerCase()
            .includes(searchText.toLowerCase())
      );
    }

    setFilteredMenus(filtered);
  }, [searchText, selectedGroupId, menus, systemGroups]);

  // 動態渲染 Antd 圖標
  const renderIcon = (iconName: string | null) => {
    if (!iconName) return null;
    const Icon = (AntdIcons as any)[iconName];
    return Icon ? <Icon /> : null;
  };

  // 圖標選項
  const iconOptions = iconList.map((iconName) => ({
    label: (
      <Space>
        {renderIcon(iconName)}
        <span>{iconName}</span>
      </Space>
    ),
    value: iconName,
  }));

  // 獲取系統群組名稱
  const getSystemGroupName = (systemGroupId: string) => {
    const group = systemGroups.find((g) => g.systemGroupId === systemGroupId);
    return group ? group.name : systemGroupId;
  };

  // 表格列定義
  const columns: ColumnsType<SystemMenu> = [
    {
      title: "選單名稱",
      dataIndex: "label",
      key: "label",
      render: (text: string, record: SystemMenu) => (
        <Space>
          {renderIcon(record.icon)}
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: "識別碼",
      dataIndex: "key",
      key: "key",
    },
    {
      title: "圖標",
      dataIndex: "icon",
      key: "icon",
      render: (icon: string | null) => (
        <Space>
          {renderIcon(icon)}
          <span>{icon || "-"}</span>
        </Space>
      ),
    },
    {
      title: "系統群組",
      dataIndex: "systemGroupId",
      key: "systemGroupId",
      render: (systemGroupId: string) => (
        <Tag color="blue">{getSystemGroupName(systemGroupId)}</Tag>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  // 處理新增/編輯表單提交
  const handleSubmit = async (values: any) => {
    try {
      const submittingValues = {
        ...values,
        icon: values.icon || "",
      };

      if (editingMenu) {
        // 更新選單
        const response = await updateSystemMenu({
          ...submittingValues,
          systemMenuId: editingMenu.systemMenuId,
        });
        if (response.success) {
          notifySuccess("更新成功", "選單已更新");
          loadMenus();
        } else {
          notifyError("更新失敗", response.message);
        }
      } else {
        // 新增選單
        const response = await createSystemMenu(submittingValues);
        if (response.success) {
          notifySuccess("新增成功", "選單已新增");
          loadMenus();
        } else {
          notifyError("新增失敗", response.message);
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      notifyError("操作失敗", "請稍後再試");
    }
  };

  // 處理編輯
  const handleEdit = (menu: SystemMenu) => {
    setEditingMenu(menu);
    form.setFieldsValue(menu);
    setIsModalVisible(true);
  };

  // 處理刪除
  const handleDelete = (menu: SystemMenu) => {
    setMenuToDelete(menu);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!menuToDelete) return;

    try {
      const response = await deleteSystemMenu(menuToDelete.systemMenuId);
      if (response.success) {
        notifySuccess("刪除成功", "選單已刪除");
        loadMenus();
        setDeleteModalVisible(false);
        setMenuToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message);
      }
    } catch (error) {
      notifyError("刪除失敗", "請稍後再試");
    }
  };

  // 構建選單層級結構
  const buildMenuHierarchy = (menus: SystemMenu[]) => {
    const menuMap = new Map<string, SystemMenu & { level?: number }>();
    const rootMenus: (SystemMenu & { level?: number })[] = [];

    // 第一步：建立映射和找出根節點
    menus.forEach((menu) => {
      menuMap.set(menu.systemMenuId, { ...menu, level: 1 });
      if (!menu.parentId) {
        rootMenus.push({ ...menu, level: 1 });
      }
    });

    // 第二步：設置所有節點的層級
    const setLevel = (menuId: string, level: number) => {
      const menu = menuMap.get(menuId);
      if (menu) {
        menu.level = level;
        menus.forEach((m) => {
          if (m.parentId === menuId) {
            setLevel(m.systemMenuId, level + 1);
          }
        });
      }
    };

    // 從根節點開始設置層級
    rootMenus.forEach((root) => {
      setLevel(root.systemMenuId, 1);
    });

    return Array.from(menuMap.values());
  };

  // 將選單數據轉換為樹形結構的選項
  interface TreeData {
    title: string;
    value: string;
    children?: TreeData[];
    level?: number;
  }

  const getTreeData = (data: SystemMenu[]): TreeData[] => {
    // 先構建層級結構
    const hierarchicalMenus = buildMenuHierarchy(data);

    // 創建一個映射來存儲每個選單的 TreeData
    const menuMap = new Map<string, TreeData>();

    // 首先創建所有節點
    hierarchicalMenus.forEach((menu) => {
      menuMap.set(menu.systemMenuId, {
        title: `[層級${menu.level}] ${menu.label}`,
        value: menu.systemMenuId,
        level: menu.level,
        children: [],
      });
    });

    // 構建樹形結構
    const rootNodes: TreeData[] = [];
    hierarchicalMenus.forEach((menu) => {
      const node = menuMap.get(menu.systemMenuId);
      if (node) {
        if (menu.parentId) {
          const parentNode = menuMap.get(menu.parentId);
          if (parentNode && parentNode.children) {
            parentNode.children.push(node);
          }
        } else {
          rootNodes.push(node);
        }
      }
    });

    // 清理空的 children 數組
    const cleanupEmptyChildren = (node: TreeData) => {
      if (node.children && node.children.length === 0) {
        delete node.children;
      } else if (node.children) {
        node.children.forEach(cleanupEmptyChildren);
      }
    };
    rootNodes.forEach(cleanupEmptyChildren);

    return rootNodes;
  };

  return (
    <Card title="選單設定">
      <Space style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingMenu(null);
            form.resetFields();
            setIsModalVisible(true);
          }}
        >
          新增選單
        </Button>
        <Select
          placeholder="選擇系統群組"
          allowClear
          style={{ width: 200 }}
          options={[
            { label: "全部系統群組", value: "" },
            ...systemGroups.map((group) => ({
              label: group.name,
              value: group.systemGroupId,
            })),
          ]}
          onChange={(value) => setSelectedGroupId(value)}
        />
        <Input
          placeholder="搜尋選單名稱、識別碼"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </Space>

      <Table
        columns={columns}
        dataSource={filteredMenus}
        rowKey="systemMenuId"
        loading={loading}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
        }}
      />

      <Modal
        title={editingMenu ? "編輯選單" : "新增選單"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText="確認"
        cancelText="取消"
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item name="parentId" label="上級選單">
            <TreeSelect
              treeData={getTreeData(menus)}
              placeholder="請選擇上級選單"
              allowClear
              treeDefaultExpandAll
            />
          </Form.Item>
          <Form.Item
            name="label"
            label="選單名稱"
            rules={[{ required: true, message: "請輸入選單名稱" }]}
          >
            <Input placeholder="請輸入選單名稱" />
          </Form.Item>
          <Form.Item
            name="key"
            label="識別碼"
            rules={[{ required: true, message: "請輸入識別碼" }]}
          >
            <Input placeholder="請輸入識別碼" />
          </Form.Item>
          <Form.Item name="icon" label="圖標">
            <Select
              showSearch
              placeholder="請選擇圖標"
              options={iconOptions}
              allowClear
              style={{ width: "100%" }}
              optionFilterProp="children"
            />
          </Form.Item>
          <Form.Item
            name="systemGroupId"
            label="系統群組"
            rules={[{ required: true, message: "請選擇系統群組" }]}
          >
            <Select
              showSearch
              placeholder="請選擇系統群組"
              options={systemGroups.map((group) => ({
                label: group.name,
                value: group.systemGroupId,
              }))}
              style={{ width: "100%" }}
              optionFilterProp="label"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 刪除確認對話框 */}
      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setMenuToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled: deleteConfirmText !== (menuToDelete?.label || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{menuToDelete?.label}」</strong>以確認刪除：
          </p>
          <Input
            placeholder="請輸入選單名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default SystemMenuPage;
