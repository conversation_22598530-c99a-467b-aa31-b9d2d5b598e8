﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Common
{
    public class AuditLogsService : IAuditLogsService
    {
        private readonly ERPDbContext _context;

        public AuditLogsService(ERPDbContext context)
        {
            _context = context;
        }

        public async Task<List<AuditLogsDTO>> GetAuditLogsAsync(string _auditLogsId)
        {
            var query = _context.Common_AuditLogs.AsQueryable();

            // 如果 _auditLogsId 不為空，則加上篩選條件
            if (!string.IsNullOrEmpty(_auditLogsId))
            {
                query = query.Where(e => e.AuditLogsId == _auditLogsId);
            }

            return await query.Select(log => new AuditLogsDTO
            {
                AuditLogsId = log.AuditLogsId,
                LogContent = log.LogContent,
                IP = log.IP,
                RequestUrl = log.RequestUrl,
                Agent = log.Agent,
            }).ToListAsync();
        }

        public async Task<(bool, string)> AddAuditLogsAsync(AuditLogsDTO logData)
        {
            try
            {
                var newLog = new AuditLogs
                {
                    LogContent = logData.LogContent,
                    IP = logData.IP,
                    RequestUrl = logData.RequestUrl,
                    Agent = logData.Agent,
                    CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                    CreateUserId = "token-登錄者uid",
                };

                await _context.Common_AuditLogs.AddAsync(newLog);
                await _context.SaveChangesAsync();

                return (true, "新增審核日誌成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增審核日誌失敗: {ex.Message}");
            }
        }
    }
}
