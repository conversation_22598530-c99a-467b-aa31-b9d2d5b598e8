﻿using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class EducationService : IEducationService
    {
        private readonly ERPDbContext _context;

        private readonly EmployeeClass _employeeClass;

        private readonly Baseform _baseform;

        private readonly ICurrentUserService _currentUserService;

        public EducationService(
            Baseform baseform,
            ERPDbContext context,
            EmployeeClass employeeClass,
            ICurrentUserService currentUserService)
        {
            _baseform = baseform;
            _context = context;
            _employeeClass = employeeClass;
            _currentUserService = currentUserService;
        }

        //取得所有學歷資料.
        public async Task<List<EducationDTO>> GetEducationListAsync(string _userid)
        {
            try
            {
                var educationsQuery = _context.Pas_Education
                .Where(e => e.UserId == _userid)
                .OrderByDescending(e => e.PeriodDateEnd);

                var educations = await educationsQuery
                .Select(t => new EducationDTO
                {
                    UserId = t.UserId,
                    Uid = t.Uid,
                    DegreeType = t.DegreeType,
                    DegreeTypeName = _employeeClass.GeteduDegreeTypeName(t.DegreeType),
                    GraduateType = t.GraduateType,
                    GraduateTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_graduatedata, t.GraduateType),
                    SchoolName = t.SchoolName,
                    DepartmentName = t.DepartmentName,
                    PeriodDateStart = _baseform.TimestampToDateStr(t.PeriodDateStart),
                    PeriodDateEnd = _baseform.TimestampToDateStr(t.PeriodDateEnd),
                    CertificateDate = _baseform.TimestampToDateStr(t.CertificateDate),
                    CertificateNumber = t.CertificateNumber,
                    Remark = t.Remark,
                    UpdateTime = t.UpdateTime
                }).ToListAsync();

                return educations;
            }
            catch (Exception ex)
            {
                throw new Exception("取得學歷資料錯誤", ex);
            }
        }

        // 取得學歷明細
        public async Task<EducationDTO> GetEducationDetailAsync(string _uid)
        {
            try
            {
                var education = await _context.Pas_Education
                .Where(x => x.Uid.ToString() == _uid)
                .Select(t => new EducationDTO
                {
                    UserId = t.UserId,
                    Uid = t.Uid,
                    DegreeType = t.DegreeType,
                    DegreeTypeName = _employeeClass.GeteduDegreeTypeName(t.DegreeType),
                    GraduateType = t.GraduateType,
                    GraduateTypeName = _employeeClass.GetlistCompareName(_employeeClass.list_graduatedata, t.GraduateType),
                    SchoolName = t.SchoolName,
                    DepartmentName = t.DepartmentName,
                    PeriodDateStart = _baseform.TimestampToDateStr(t.PeriodDateStart),
                    PeriodDateEnd = _baseform.TimestampToDateStr(t.PeriodDateEnd),
                    CertificateDate = _baseform.TimestampToDateStr(t.CertificateDate),
                    CertificateNumber = t.CertificateNumber,
                    Remark = t.Remark,
                    UpdateTime = t.UpdateTime
                }).FirstOrDefaultAsync();

                return education;
            }
            catch (Exception ex)
            {
                throw new Exception("取得學歷明細錯誤", ex);
            }
        }

        public async Task<(bool, string)> AddEducationAsync(EducationDTO _data)
        {

            List<string> list_msg_check = CheckEducationInput(_data, "add");

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                // formdata回來""會變null 需轉回空字串"".
                var newEducation = new Education
                {
                    UserId = _data.UserId,
                    Uid = Guid.NewGuid().ToString().Trim(),
                    DegreeType = _data.DegreeType ?? "",
                    GraduateType = _data.GraduateType ?? "",
                    SchoolName = _data.SchoolName ?? "",
                    DepartmentName = _data.DepartmentName ?? "",
                    PeriodDateStart = _baseform.DateStrToTimestamp(_data.PeriodDateStart ?? ""),
                    PeriodDateEnd = _baseform.DateStrToTimestamp(_data.PeriodDateEnd ?? ""),
                    CertificateDate = _baseform.DateStrToTimestamp(_data.CertificateDate ?? ""),
                    CertificateNumber = _data.CertificateNumber ?? "",
                    Remark = _data.Remark ?? "",
                    UpdateTime = _data.UpdateTime,
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId,
                };

                await _context.Pas_Education.AddAsync(newEducation);

                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                return (true, "新增學歷資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增學歷資料失敗: {ex.InnerException.Message}");
            }
        }

        public async Task<(bool, string)> EditEducationAsync(EducationDTO _data)
        {
            List<string> list_msg_check = CheckEducationInput(_data, "edit");

            if (list_msg_check.Count > 0)
            {
                return (false, list_msg_check[0]);
            }

            // 資料處理.
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                // formdata回來""會變null 需轉回空字串"".
                var existingEducation = await _context.Pas_Education
                .FirstOrDefaultAsync(x => x.Uid == _data.Uid);

                if (existingEducation != null)
                {
                    existingEducation.DegreeType = _data.DegreeType ?? "";
                    existingEducation.GraduateType = _data.GraduateType ?? "";
                    existingEducation.SchoolName = _data.SchoolName ?? "";
                    existingEducation.DepartmentName = _data.DepartmentName ?? "";
                    existingEducation.PeriodDateStart = _baseform.DateStrToTimestamp(_data.PeriodDateStart ?? "");
                    existingEducation.PeriodDateEnd = _baseform.DateStrToTimestamp(_data.PeriodDateEnd ?? "");
                    existingEducation.CertificateDate = _baseform.DateStrToTimestamp(_data.CertificateDate ?? "");
                    existingEducation.CertificateNumber = _data.CertificateNumber ?? "";
                    existingEducation.Remark = _data.Remark ?? "";

                    existingEducation.UpdateTime = _baseform.GetCurrentLocalTimestamp();
                    existingEducation.UpdateUserId = _currentUserService.UserId;
                }
                else
                {
                    return (false, "無對應user資料");
                }

                // 兩張表一起存.
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "編輯學歷資料成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增學歷資料失敗: {ex.InnerException.Message}");
            }

        }

        public async Task<(bool, string)> DeleteEducationAsync(string _uid)
        {
            // 資料處理.
            using var transaction = await _context.Database.BeginTransactionAsync(); // 開啟交易

            try
            {
                var existingEducation = await _context.Pas_Education
                                .FirstOrDefaultAsync(x => x.Uid == _uid && x.IsDeleted != true);

                if (existingEducation != null)
                {
                    existingEducation.DeleteTime = _baseform.GetCurrentLocalTimestamp();
                    existingEducation.DeleteUserId = _currentUserService.UserId;
                    existingEducation.IsDeleted = true;
                }
                else
                {
                    return (false, "資料已刪除或不存在");
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return (true, "刪除成功");
            }
            catch
            {
                await transaction.RollbackAsync();
                return (false, "刪除學歷資料錯誤");
            }

        }

        public List<string> CheckEducationInput(EducationDTO _data, string _mode)
        {
            List<string> list_errorMsg = new List<string>();

            // 檢核item.
            if (_data.SchoolName == "")
            {
                list_errorMsg.Add("請輸入學校名稱");
            }

            if (_data.DegreeType == "")
            {
                list_errorMsg.Add("請選擇學位");
            }

            if (_data.GraduateType == "")
            {
                list_errorMsg.Add("請選擇畢肄業類型");
            }

            if (_data.PeriodDateStart != "" && !_baseform.IsValidDateOrEmpty(_data.PeriodDateStart))
            {
                list_errorMsg.Add("修業起日格式輸入錯誤");
            }

            if (_data.PeriodDateEnd != "" && !_baseform.IsValidDateOrEmpty(_data.PeriodDateEnd))
            {
                list_errorMsg.Add("修業迄日格式輸入錯誤");
            }

            if (_data.CertificateDate != "" && !_baseform.IsValidDateOrEmpty(_data.CertificateDate))
            {
                list_errorMsg.Add("發證日期格式輸入錯誤");
            }

            return list_errorMsg;

        }

    }
}
