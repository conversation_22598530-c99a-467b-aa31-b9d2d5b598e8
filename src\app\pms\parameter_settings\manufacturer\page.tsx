"use client";

/*
  廠牌型號
  /app/pms/parameter_settings/manufacturer/page.tsx
*/

import React, { useEffect, useState } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Descriptions,
  List,
  Typography,
  Dropdown,
  Select,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  FilterOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { MenuProps } from "antd";
import {
  Manufacturer,
  getManufacturers,
  addManufacturer,
  editManufacturer,
  deleteManufacturer,
} from "@/services/pms/manufacturerService";
import { useAuth } from "@/contexts/AuthContext";
import { notifySuccess, notifyError } from "@/utils/notification";
import guidUtils from "@/utils/guidUtils";

const { Text } = Typography;

// 定義篩選選項
const filterOptions = [
  { label: "廠牌名稱", value: "name", type: "input" },
  { label: "型號", value: "model", type: "input" },
  { label: "製造商名稱", value: "manufacturerName", type: "input" },
  { label: "供應商", value: "supplier", type: "input" },
  { label: "聯絡人", value: "contactPerson", type: "input" },
  { label: "聯絡電話", value: "contactPhone", type: "input" },
  { label: "電子郵件", value: "contactEmail", type: "input" },
];

const ManufacturerPage = () => {
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([]);
  const [filteredManufacturers, setFilteredManufacturers] = useState<
    Manufacturer[]
  >([]);
  const [searchText, setSearchText] = useState("");
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingManufacturer, setEditingManufacturer] =
    useState<Manufacturer | null>(null);
  const [form] = Form.useForm();
  const { user } = useAuth();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [manufacturerToDelete, setManufacturerToDelete] =
    useState<Manufacturer | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // 載入製造商資料
  const loadManufacturers = async () => {
    setLoading(true);
    try {
      const response = await getManufacturers();
      if (response.success && response.data) {
        setManufacturers(response.data);
        setFilteredManufacturers(response.data);
      } else {
        notifyError("獲取製造商列表失敗", response.message);
      }
    } catch (error) {
      notifyError("獲取製造商列表失敗", "請稍後再試");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadManufacturers();
  }, []);

  // 檢查手機版
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // 清除篩選條件
  const handleClearFilters = () => {
    setActiveFilters([]);
    setFilterValues({});
  };

  // 新增篩選條件
  const handleAddFilter = (filterKey: string) => {
    if (!activeFilters.includes(filterKey)) {
      setActiveFilters([...activeFilters, filterKey]);
    }
  };

  // 移除篩選條件
  const handleRemoveFilter = (filterKey: string) => {
    setActiveFilters(activeFilters.filter((key) => key !== filterKey));
    const newFilterValues = { ...filterValues };
    delete newFilterValues[filterKey];
    setFilterValues(newFilterValues);
  };

  // 更新篩選值
  const handleFilterValueChange = (filterKey: string, value: any) => {
    setFilterValues({
      ...filterValues,
      [filterKey]: value,
    });
  };

  // 更新篩選選單
  const filterMenu: MenuProps = {
    items: [
      ...filterOptions.map((option) => ({
        key: option.value,
        label: (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: "120px",
            }}
          >
            <span>{option.label}</span>
            {activeFilters.includes(option.value) && (
              <CheckOutlined style={{ color: "#1890ff" }} />
            )}
          </div>
        ),
        onClick: () => {
          if (activeFilters.includes(option.value)) {
            handleRemoveFilter(option.value);
          } else {
            handleAddFilter(option.value);
          }
        },
      })),
      ...(activeFilters.length > 0
        ? [
            { type: "divider" as const },
            {
              key: "clear",
              label: "清除所有篩選",
              onClick: handleClearFilters,
              danger: true,
            },
          ]
        : []),
    ],
  };

  // 產生篩選控制項
  const renderFilterControl = (filterKey: string) => {
    const option = filterOptions.find((opt) => opt.value === filterKey);
    if (!option) return null;

    // 取得對應欄位的唯一值作為選項
    const uniqueValues = Array.from(
      new Set(
        manufacturers
          .map((m) => (m as any)[filterKey] as string | undefined)
          .filter((v) => v)
      )
    ) as string[];

    return (
      <Select
        value={filterValues[filterKey]}
        onChange={(value) => handleFilterValueChange(filterKey, value)}
        style={{ minWidth: 160 }}
        placeholder={`選擇${option.label}`}
        allowClear
        showSearch
        optionFilterProp="children"
      >
        {uniqueValues.map((val) => (
          <Select.Option key={val} value={val}>
            {val}
          </Select.Option>
        ))}
      </Select>
    );
  };

  // 更新搜尋 & 篩選邏輯
  useEffect(() => {
    let filtered = [...manufacturers];

    // 搜尋文字
    if (searchText) {
      filtered = filtered.filter(
        (m) =>
          m.name.toLowerCase().includes(searchText.toLowerCase()) ||
          m.model.toLowerCase().includes(searchText.toLowerCase()) ||
          m.manufacturerName.toLowerCase().includes(searchText.toLowerCase()) ||
          m.supplier.toLowerCase().includes(searchText.toLowerCase()) ||
          m.contactPerson.toLowerCase().includes(searchText.toLowerCase()) ||
          m.contactPhone.toLowerCase().includes(searchText.toLowerCase()) ||
          m.contactEmail.toLowerCase().includes(searchText.toLowerCase()) ||
          (m.description &&
            m.description.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 套用自訂篩選
    filtered = filtered.filter((m) => {
      return activeFilters.every((key) => {
        const value = filterValues[key];
        if (!value) return true;
        const target = (m as any)[key] as string | undefined;
        if (target === undefined || target === null) return false;
        return String(target).toLowerCase() === String(value).toLowerCase();
      });
    });

    setFilteredManufacturers(filtered);
  }, [searchText, manufacturers, activeFilters, filterValues]);

  // 表格欄位定義
  const columns: ColumnsType<Manufacturer> = [
    {
      key: "index",
      width: 80,
      render: (_, __, index) => <span>{index + 1}</span>,
    },
    {
      title: "廠牌名稱",
      dataIndex: "name",
      key: "name",
      width: 120,
      render: (text, record) => (
        <Tooltip
          title={
            <div style={{ padding: "8px" }}>
              <Descriptions size="small" column={1} bordered>
                <Descriptions.Item label="廠牌名稱">
                  {record.name}
                </Descriptions.Item>
                <Descriptions.Item label="型號">
                  {record.model}
                </Descriptions.Item>
                <Descriptions.Item label="製造商名稱">
                  {record.manufacturerName}
                </Descriptions.Item>
                <Descriptions.Item label="供應商">
                  {record.supplier}
                </Descriptions.Item>
                <Descriptions.Item label="聯絡人">
                  {record.contactPerson}
                </Descriptions.Item>
                <Descriptions.Item label="聯絡電話">
                  {record.contactPhone}
                </Descriptions.Item>
                <Descriptions.Item label="電子郵件">
                  {record.contactEmail}
                </Descriptions.Item>
                <Descriptions.Item label="描述">
                  {record.description}
                </Descriptions.Item>
              </Descriptions>
            </div>
          }
          color="#fff"
          placement="right"
          styles={{
            root: { maxWidth: "400px" },
            body: { padding: "0" },
          }}
        >
          <span style={{ cursor: "pointer", color: "#1890ff" }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "型號",
      dataIndex: "model",
      key: "model",
      width: 120,
    },
    {
      title: "製造商名稱",
      dataIndex: "manufacturerName",
      key: "manufacturerName",
      width: 200,
    },
    {
      title: "供應商",
      dataIndex: "supplier",
      key: "supplier",
      width: 200,
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  // 處理新增
  const handleAdd = () => {
    setEditingManufacturer(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 處理編輯
  const handleEdit = (record: Manufacturer) => {
    setEditingManufacturer(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  // 處理刪除
  const handleDelete = (record: Manufacturer) => {
    setManufacturerToDelete(record);
    setDeleteConfirmText("");
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const executeDelete = async () => {
    if (!manufacturerToDelete) return;

    try {
      const response = await deleteManufacturer(manufacturerToDelete);
      if (response.success) {
        notifySuccess("刪除成功", "廠牌型號已成功刪除");
        loadManufacturers();
        setDeleteModalVisible(false);
        setManufacturerToDelete(null);
        setDeleteConfirmText("");
      } else {
        notifyError("刪除失敗", response.message);
      }
    } catch (error) {
      notifyError("刪除失敗", "請稍後再試");
    }
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        manufacturerId: editingManufacturer?.manufacturerId || guidUtils(),
        createTime: editingManufacturer
          ? editingManufacturer.createTime
          : Date.now(),
        createUserId: editingManufacturer
          ? editingManufacturer.createUserId
          : user?.userId || "",
        updateTime: editingManufacturer ? Date.now() : 0,
        updateUserId: editingManufacturer ? user?.userId || "" : "",
        deleteTime: 0,
        deleteUserId: "",
        isDeleted: false,
      };

      const response = editingManufacturer
        ? await editManufacturer(submitData)
        : await addManufacturer(submitData);

      if (response.success) {
        notifySuccess(
          editingManufacturer ? "編輯成功" : "新增成功",
          `製造商已成功${editingManufacturer ? "編輯" : "新增"}`
        );
        setIsModalVisible(false);
        form.resetFields();
        loadManufacturers();
      } else {
        notifyError(
          editingManufacturer ? "編輯失敗" : "新增失敗",
          response.message
        );
      }
    } catch (error) {
      notifyError(editingManufacturer ? "編輯失敗" : "新增失敗", "請稍後再試");
    }
  };

  // 手機版列表
  const renderMobileList = () => {
    return (
      <List
        loading={loading}
        dataSource={filteredManufacturers}
        renderItem={(manufacturer) => (
          <List.Item
            key={manufacturer.manufacturerId}
            style={{
              padding: "12px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <div style={{ width: "100%" }}>
              {/* 標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "8px",
                  gap: "8px",
                }}
              >
                <div
                  style={{
                    width: 24,
                    height: 24,
                    background: "#f0f0f0",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "14px",
                    flexShrink: 0,
                  }}
                >
                  {filteredManufacturers.indexOf(manufacturer) + 1}
                </div>
                <Text strong style={{ fontSize: "16px" }}>
                  {manufacturer.name}
                </Text>
                <Text type="secondary" style={{ fontSize: "14px" }}>
                  ({manufacturer.model})
                </Text>
              </div>

              {/* 基本資訊 */}
              <div style={{ marginBottom: "8px", fontSize: "15px" }}>
                <Space direction="vertical" size={4} style={{ width: "100%" }}>
                  <Text>製造商：{manufacturer.manufacturerName}</Text>
                  <Text>供應商：{manufacturer.supplier}</Text>
                </Space>
              </div>

              {/* 聯絡資訊 */}
              <div style={{ marginBottom: "8px" }}>
                <Space direction="vertical" size={4} style={{ width: "100%" }}>
                  <Text style={{ fontSize: "14px", color: "#666" }}>
                    聯絡人：{manufacturer.contactPerson}
                  </Text>
                  <Text style={{ fontSize: "14px", color: "#666" }}>
                    電話：{manufacturer.contactPhone}
                  </Text>
                  <Text style={{ fontSize: "14px", color: "#666" }}>
                    Email：{manufacturer.contactEmail}
                  </Text>
                </Space>
              </div>

              {/* 描述 */}
              {manufacturer.description && (
                <div style={{ marginBottom: "8px" }}>
                  <Text style={{ fontSize: "14px", color: "#666" }}>
                    描述：{manufacturer.description}
                  </Text>
                </div>
              )}

              {/* 操作按鈕列 */}
              <div
                style={{
                  display: "flex",
                  gap: "16px",
                  marginTop: "12px",
                }}
              >
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(manufacturer)}
                  style={{ padding: 0 }}
                >
                  編輯
                </Button>
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(manufacturer)}
                  style={{ padding: 0 }}
                >
                  刪除
                </Button>
              </div>
            </div>
          </List.Item>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: (total) => `共 ${total} 筆資料`,
          size: "small",
          style: { marginTop: "16px" },
        }}
      />
    );
  };

  return (
    <Card
      title="廠牌型號管理"
      styles={{
        body: { padding: isMobile ? "12px" : "24px" },
      }}
    >
      <Space
        style={{
          marginBottom: 16,
          width: "100%",
          flexDirection: isMobile ? "column" : "row",
        }}
        wrap
      >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ width: isMobile ? "100%" : "auto" }}
        >
          新增廠牌型號
        </Button>
        <Dropdown menu={filterMenu} trigger={["click"]}>
          <Button
            icon={<FilterOutlined />}
            style={{ width: isMobile ? "100%" : "auto" }}
          >
            新增篩選條件
          </Button>
        </Dropdown>
        {/* 篩選條件 */}
        {activeFilters.length > 0 && (
          <div
            style={{
              width: "100%",
              marginTop: isMobile ? "8px" : 0,
              display: "flex",
              flexWrap: "wrap",
              gap: "8px",
            }}
          >
            {activeFilters.map((filterKey) => (
              <div
                key={filterKey}
                style={{ width: isMobile ? "100%" : "auto" }}
              >
                {renderFilterControl(filterKey)}
              </div>
            ))}
          </div>
        )}
        {/* 搜尋 */}
        <Input
          placeholder="搜尋廠牌名稱、型號、製造商"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: "100%" }}
        />
      </Space>

      {isMobile ? (
        renderMobileList()
      ) : (
        <Table
          columns={columns}
          dataSource={filteredManufacturers}
          rowKey="manufacturerId"
          loading={loading}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
            showTotal: (total) => `共 ${total} 筆資料`,
          }}
        />
      )}

      <Modal
        title={editingManufacturer ? "編輯廠牌型號" : "新增廠牌型號"}
        open={isModalVisible}
        onOk={form.submit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "16px",
            }}
          >
            <Form.Item
              name="name"
              label="廠牌名稱"
              rules={[{ required: true, message: "請輸入廠牌名稱" }]}
            >
              <Input placeholder="請輸入廠牌名稱" />
            </Form.Item>

            <Form.Item
              name="model"
              label="型號"
              rules={[{ required: true, message: "請輸入型號" }]}
            >
              <Input placeholder="請輸入型號" />
            </Form.Item>

            <Form.Item
              name="manufacturerName"
              label="製造商名稱"
              rules={[{ required: true, message: "請輸入製造商名稱" }]}
            >
              <Input placeholder="請輸入製造商名稱" />
            </Form.Item>

            <Form.Item
              name="supplier"
              label="供應商"
              rules={[{ required: true, message: "請輸入供應商" }]}
            >
              <Input placeholder="請輸入供應商" />
            </Form.Item>

            <Form.Item
              name="contactPerson"
              label="聯絡人"
              rules={[{ required: true, message: "請輸入聯絡人" }]}
            >
              <Input placeholder="請輸入聯絡人" />
            </Form.Item>

            <Form.Item
              name="contactPhone"
              label="聯絡電話"
              rules={[{ required: true, message: "請輸入聯絡電話" }]}
            >
              <Input placeholder="請輸入聯絡電話" />
            </Form.Item>

            <Form.Item
              name="contactEmail"
              label="電子郵件"
              rules={[
                { required: true, message: "請輸入電子郵件" },
                { type: "email", message: "請輸入有效的電子郵件" },
              ]}
            >
              <Input placeholder="請輸入電子郵件" />
            </Form.Item>

            <Form.Item
              name="sortCode"
              label="排序"
              rules={[{ required: true, message: "請輸入排序" }]}
            >
              <InputNumber placeholder="請輸入排序" style={{ width: "100%" }} />
            </Form.Item>
          </div>

          <Form.Item name="description" label="描述">
            <Input.TextArea
              placeholder="請輸入描述"
              maxLength={200}
              showCount
              rows={4}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 刪除確認 Modal */}
      <Modal
        title="確認刪除"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setManufacturerToDelete(null);
          setDeleteConfirmText("");
        }}
        onOk={executeDelete}
        okText="確認刪除"
        cancelText="取消"
        okButtonProps={{
          danger: true,
          disabled: deleteConfirmText !== (manufacturerToDelete?.name || ""),
        }}
      >
        <div>
          <p>
            請輸入<strong>「{manufacturerToDelete?.name}」</strong>以確認刪除：
          </p>
          <Input
            placeholder="請輸入廠牌名稱"
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
          />
        </div>
      </Modal>
    </Card>
  );
};

export default ManufacturerPage;
