using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Pas
{
    public class PerformancePointTypeService : IPerformancePointTypeService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly ICurrentUserService _currentUserService;

        public PerformancePointTypeService(ERPDbContext context, Baseform baseform, ICurrentUserService currentUserService)
        {
            _context = context;
            _baseform = baseform;
            _currentUserService = currentUserService;
        }

        public async Task<List<PerformancePointTypeDTO>> GetByGroupUidAsync(string groupUid)
        {
            return await _context.Pas_PerformancePointType
                .Where(x => x.groupUid == groupUid && !x.IsDeleted)
                .OrderBy(x => x.CreateTime)
                .Select(x => new PerformancePointTypeDTO
                {
                    uid = x.uid,
                    pointName = x.pointName,
                    groupUid = x.groupUid,
                    groupName = _context.Pas_PerformancePointGroup
                        .Where(g => g.uid == x.groupUid && !g.IsDeleted)
                        .Select(g => g.groupName)
                        .FirstOrDefault() ?? "",
                    weightRatio = x.weightRatio.ToString("0.##")
                })
                .ToListAsync();
        }


        public async Task<PerformancePointTypeDTO?> GetDetailAsync(string uid)
        {
            var entity = await _context.Pas_PerformancePointType
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.uid == uid && !x.IsDeleted);

            if (entity == null) return null;

            return new PerformancePointTypeDTO
            {
                uid = entity.uid,
                pointName = entity.pointName,
                groupUid = entity.groupUid,
                groupName = await _context.Pas_PerformancePointGroup
                    .Where(g => g.uid == entity.groupUid && !g.IsDeleted)
                    .Select(g => g.groupName)
                    .FirstOrDefaultAsync() ?? "",
                weightRatio = entity.weightRatio.ToString("0.##")
            };
        }

        public async Task<(bool, string)> AddAsync(PerformancePointTypeDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = new PerformancePointType
                {
                    uid = Guid.NewGuid().ToString(),
                    pointName = _data.pointName,
                    groupUid = _data.groupUid,
                    weightRatio = decimal.Parse(_data.weightRatio),
                    CreateTime = _baseform.GetCurrentLocalTimestamp(),
                    CreateUserId = _currentUserService.UserId
                };

                _context.Pas_PerformancePointType.Add(entity);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "新增點數類型成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增點數類型失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> EditAsync(PerformancePointTypeDTO _data)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_PerformancePointType.FirstOrDefaultAsync(x => x.uid == _data.uid);
                if (entity == null) return (false, "找不到對應點數類型資料");

                entity.pointName = _data.pointName;
                entity.groupUid = _data.groupUid;
                entity.weightRatio = decimal.Parse(_data.weightRatio);
                entity.UpdateUserId = _currentUserService.UserId;
                entity.UpdateTime = _baseform.GetCurrentLocalTimestamp();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "修改點數類型成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"修改點數類型失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<(bool, string)> DeleteAsync(string uid)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = await _context.Pas_PerformancePointType.FirstOrDefaultAsync(x => x.uid == uid);
                if (entity == null) return (false, "找不到對應點數類型資料");

                entity.IsDeleted = true;
                entity.DeleteUserId = _currentUserService.UserId;
                entity.DeleteTime = _baseform.GetCurrentLocalTimestamp();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return (true, "刪除點數類型成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"刪除點數類型失敗: {ex.InnerException?.Message ?? ex.Message}");
            }
        }

        public async Task<List<CascaderOptionDTO>> GetGroupWithTypesAsCascaderAsync()
        {
            // 撈出所有未刪除的群組
            var groups = await _context.Pas_PerformancePointGroup
                .Where(g => !g.IsDeleted)
                .OrderBy(t => t.CreateTime)
                .AsNoTracking()
                .ToListAsync();

            // 撈出所有未刪除的點數類型
            var types = await _context.Pas_PerformancePointType
                .Where(t => !t.IsDeleted)
                .OrderBy(t => t.CreateTime)
                .AsNoTracking()
                .ToListAsync();

            var result = groups.Select(group => new CascaderOptionDTO
            {
                value = group.uid,
                label = group.groupName,
                children = types
                .Where(t => t.groupUid == group.uid)
                .Select(t => new CascaderOptionDTO
                {
                    value = t.uid,
                    label = t.pointName
                })
                .ToList()
            })
                .Where(g => g.children != null && g.children.Count > 0) // 只保留有 children 的群組
                .ToList();

            return result;
        }
    }
}
