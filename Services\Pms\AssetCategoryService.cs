using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using Newtonsoft.Json;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AssetCategoryService : IAssetCategoryService
    {
        private readonly ERPDbContext _context;
        private readonly IMapper _mapper;

        public AssetCategoryService(ERPDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        /**
         * 新增財產類別
         * @param assetCategory 財產類別資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> AddAsync(AssetCategoryDTO assetCategory)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = _mapper.Map<AssetCategory>(assetCategory);
                entity.AssetCategoryId = assetCategory.AssetCategoryId;
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = assetCategory.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /**
         * 刪除財產類別
         * @param assetCategory 財產類別資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> DeleteAsync(AssetCategoryDTO assetCategory)
        {
            try
            {
                var entity = await _context.Set<AssetCategory>()
                    .FirstOrDefaultAsync(a => a.AssetCategoryId == assetCategory.AssetCategoryId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = assetCategory.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /**
         * 取得所有財產類別
         * @returns 財產類別資料列表
        **/
        public async Task<List<AssetCategoryDTO>> GetAllAsync()
        {
            try
            {
                var entities = await _context.Set<AssetCategory>()
                    .Where(a => !a.IsDeleted)
                    .OrderBy(a => a.CreateTime)
                    .ToListAsync();

                return _mapper.Map<List<AssetCategoryDTO>>(entities);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得所有財產類別時發生錯誤: {ex.Message}");
                return new List<AssetCategoryDTO>();
            }
        }

        /**
         * 取得財產類別ById
         * @param id 財產類別Id
         * @returns 財產類別資料
        **/
        public async Task<string> GetByIdAsync(string id)
        {
            try
            {
                var entity = await _context.Set<AssetCategory>()
                    .FirstOrDefaultAsync(a => a.AssetCategoryId == id && !a.IsDeleted);

                if (entity == null)
                {
                    return "找不到資料";
                }

                var dto = _mapper.Map<AssetCategoryDTO>(entity);
                return JsonConvert.SerializeObject(dto);
            }
            catch (Exception ex)
            {
                return $"取得資料失敗: {ex.Message}";
            }
        }

        /**
         * 更新財產類別
         * @param assetCategory 財產類別資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> UpdateAsync(AssetCategoryDTO assetCategory)
        {
            try
            {
                var entity = await _context.Set<AssetCategory>()
                    .FirstOrDefaultAsync(a => a.AssetCategoryId == assetCategory.AssetCategoryId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(assetCategory, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = assetCategory.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }
    }
}