// 閒置超時相關工具函數
import { eventBus } from './eventBus';
import { siteConfig } from '@/config/site';

// 默認超時時間(毫秒)
const DEFAULT_IDLE_TIMEOUT = siteConfig.auth.idleTimeout;
// 默認顯示警告對話框的時間點（閒置超時前X毫秒）
const DEFAULT_WARNING_TIME = siteConfig.auth.idleWarningTime;

// 自定義事件類型
export const IDLE_EVENTS = {
    // 顯示閒置警告對話框
    SHOW_WARNING: 'idle:show-warning',
    // 隱藏閒置警告對話框（用戶選擇繼續使用）
    HIDE_WARNING: 'idle:hide-warning',
    // 閒置超時（用戶未做選擇或選擇登出）
    TIMEOUT: 'idle:timeout',
};

// 重置定時器前需監聽的用戶活動類型
const USER_ACTIVITY_EVENTS = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click',
];

let idleTimer: NodeJS.Timeout | null = null;
let warningTimer: NodeJS.Timeout | null = null;
let isTimerActive = false;
let isWarningShown = false;

/**
 * 啟動閒置超時監控
 * @param timeoutDuration 超時時間(毫秒)，默認從site配置獲取
 * @param onTimeout 超時後的回調函數
 * @param warningTime 顯示警告的時間點（超時前的毫秒數）
 */
export const startIdleTimeout = (
    timeoutDuration: number = DEFAULT_IDLE_TIMEOUT,
    onTimeout: () => void,
    warningTime: number = DEFAULT_WARNING_TIME
) => {
    // 如果已經在運行，則先停止
    stopIdleTimeout();

    // 設置用戶活動監聽器
    USER_ACTIVITY_EVENTS.forEach((event) => {
        window.addEventListener(event, resetIdleTimer);
    });

    // 創建警告計時器
    const warningTimePoint = timeoutDuration - warningTime;
    warningTimer = setTimeout(() => {
        isWarningShown = true;
        // 觸發顯示警告對話框事件
        eventBus.emit(IDLE_EVENTS.SHOW_WARNING);
    }, warningTimePoint);

    // 創建超時計時器
    idleTimer = setTimeout(() => {
        onTimeout();
    }, timeoutDuration);

    isTimerActive = true;

    // 重置計時器的函數
    function resetIdleTimer() {
        // 如果警告對話框已顯示，則不重置計時器
        if (isWarningShown) {
            return;
        }

        if (warningTimer) {
            clearTimeout(warningTimer);
        }

        if (idleTimer) {
            clearTimeout(idleTimer);
        }

        // 重新創建警告計時器
        warningTimer = setTimeout(() => {
            isWarningShown = true;
            // 觸發顯示警告對話框事件
            eventBus.emit(IDLE_EVENTS.SHOW_WARNING);
        }, warningTimePoint);

        // 重新創建超時計時器
        idleTimer = setTimeout(() => {
            onTimeout();
        }, timeoutDuration);
    }
};

/**
 * 用戶選擇繼續使用系統，重置計時器
 */
export const continueSession = () => {
    isWarningShown = false;

    // 重置所有計時器，重新開始計時
    if (warningTimer) {
        clearTimeout(warningTimer);
    }

    if (idleTimer) {
        clearTimeout(idleTimer);
    }

    // 觸發隱藏警告對話框事件
    eventBus.emit(IDLE_EVENTS.HIDE_WARNING);

    // 重新啟動計時器
    const timeoutDuration = DEFAULT_IDLE_TIMEOUT;
    const warningTime = DEFAULT_WARNING_TIME;
    const warningTimePoint = timeoutDuration - warningTime;

    // 重新創建警告計時器
    warningTimer = setTimeout(() => {
        isWarningShown = true;
        // 觸發顯示警告對話框事件
        eventBus.emit(IDLE_EVENTS.SHOW_WARNING);
    }, warningTimePoint);

    // 重新創建超時計時器
    idleTimer = setTimeout(() => {
        // 觸發超時事件
        eventBus.emit(IDLE_EVENTS.TIMEOUT);
    }, timeoutDuration);
};

/**
 * 停止閒置超時監控
 */
export const stopIdleTimeout = () => {
    if (idleTimer) {
        clearTimeout(idleTimer);
        idleTimer = null;
    }

    if (warningTimer) {
        clearTimeout(warningTimer);
        warningTimer = null;
    }

    // 移除所有事件監聽器
    USER_ACTIVITY_EVENTS.forEach((event) => {
        window.removeEventListener(event, resetIdleTimer);
    });

    // 重置警告狀態
    isWarningShown = false;
    isTimerActive = false;

    // 觸發隱藏警告對話框事件
    eventBus.emit(IDLE_EVENTS.HIDE_WARNING);
};

/**
 * 檢查計時器是否活躍
 */
export const isIdleTimeoutActive = () => isTimerActive;

/**
 * 檢查是否顯示警告
 */
export const isWarningDialogShown = () => isWarningShown;

// 重置計時器的函數
function resetIdleTimer() {
    // 此函數實際定義在startIdleTimeout中，這裡只是為了類型定義
} 