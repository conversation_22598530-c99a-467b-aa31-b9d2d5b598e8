using System;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 日誌服務接口
    /// </summary>
    public interface ILoggerService
    {
        /// <summary> 記錄調試日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="source">來源</param>
        Task LogDebugAsync(string message, string source = "System");

        /// <summary> 記錄信息日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="source">來源</param>
        Task LogInfoAsync(string message, string source = "System");

        /// <summary> 記錄警告日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="source">來源</param>
        Task LogWarningAsync(string message, string source = "System");

        /// <summary> 記錄錯誤日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="exception">異常</param>
        /// <param name="source">來源</param>
        Task LogErrorAsync(string message, Exception? exception = null, string source = "System");

        /// <summary> 記錄異動日誌 </summary>
        /// <typeparam name="T">資料型別</typeparam>
        /// <param name="message">日誌消息</param>
        /// <param name="changedData">異動資料</param>
        /// <param name="transactionId">交易ID</param>
        /// <param name="source">來源</param>
        Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") where T : class;
    }
}