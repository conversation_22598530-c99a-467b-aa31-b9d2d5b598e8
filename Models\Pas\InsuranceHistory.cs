using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    [Comment("員工保險級距歷程記錄")]
    public class InsuranceHistory : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; }

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; }

        [Comment("保險類型 (1=勞保, 2=健保, 3=職災)")]
        [Column(TypeName = "int")]
        public int InsuranceType { get; set; } // 1=勞保, 2=健保, 3=職災

        [Comment("投保級距編號 (對應 InsuranceGrade.uid)")]
        [Column(TypeName = "nvarchar(100)")]
        public string InsuranceGradeUid { get; set; }

        [Comment("生效日期 (timestamp)")]
        [Column(TypeName = "bigint")]
        public long StartDate { get; set; }

        [Comment("結束日期 (timestamp，可為 null)")]
        [Column(TypeName = "bigint")]
        public long? EndDate { get; set; }

        public InsuranceHistory()
        {
            uid = "";
            UserId = "";
            InsuranceType = 1;
            InsuranceGradeUid = "";
            StartDate = 0;
            EndDate = null;

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class InsuranceHistoryDTO : ModelBaseEntityDTO
    {
        /// <summary>
        /// 資料編號
        /// </summary>
        public string uid { get; set; } = "";

        /// <summary>
        /// 使用者編號
        /// </summary>
        public string UserId { get; set; } = "";

        /// <summary>
        /// 保險類型
        /// </summary>
        public int InsuranceType { get; set; } = 1;

        /// <summary>
        /// 保險級距資料UID
        /// </summary>
        public string InsuranceGradeUid { get; set; } = "";

        /// <summary>
        /// 生效日期
        /// </summary>
        public string StartDate { get; set; } = ""; // timestamp

        /// <summary>
        /// 結束日期
        /// </summary>
        public string? EndDate { get; set; } = null; // timestamp 可為 null
    }

    // 請求 DTO 類
    public class GetEffectiveGradeRequest
    {
        /// <summary>
        /// 使用者編號
        /// </summary>
        [JsonPropertyName("userId")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 保險類型
        /// </summary>
        [JsonPropertyName("insuranceType")]
        public int InsuranceType { get; set; }

        /// <summary>
        /// 目標日期 (timestamp)
        /// </summary>
        [JsonPropertyName("targetDate")]
        public string TargetDate { get; set; } = string.Empty;
    }

    public class GetAllEffectiveGradesRequest
    {
        /// <summary>
        /// 使用者編號
        /// </summary>
        [JsonPropertyName("userId")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 目標日期 (timestamp)
        /// </summary>
        [JsonPropertyName("targetDate")]
        public string TargetDate { get; set; } = string.Empty;
    }

}
