using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pms
{
    /// <summary>
    /// 財產位置變動單主檔
    /// </summary>
    public class AssetLocationTransfer : ModelBaseEntity
    {
        [Key]
        [Comment("位置變動單編號")]
        public Guid TransferId { get; set; } // 位置變動單編號

        [Comment("變動單號")]
        [Column(TypeName = "nvarchar(50)")]
        public string TransferNo { get; set; } // 變動單號

        [Comment("變動日期")]
        [Column(TypeName = "bigint")]
        public long TransferDate { get; set; } // 變動日期

        [Comment("申請人員")]
        [Column(TypeName = "nvarchar(100)")]
        public string ApplicantId { get; set; } // 申請人員

        [Comment("申請人員姓名")]
        [NotMapped]
        public string ApplicantName { get; set; } // 申請人員姓名

        [Comment("申請部門")]
        [Column(TypeName = "nvarchar(100)")]
        public string ApplicantDepartmentId { get; set; } // 申請部門

        [Comment("申請部門名稱")]
        [NotMapped]
        public string ApplicantDepartmentName { get; set; } // 申請部門名稱

        [Comment("變動原因")]
        [Column(TypeName = "nvarchar(500)")]
        public string TransferReason { get; set; } // 變動原因

        [Comment("審核狀態")]
        [Column(TypeName = "nvarchar(20)")]
        public string ApprovalStatus { get; set; } // 審核狀態 (PENDING, APPROVED, REJECTED)

        [Comment("審核人員")]
        [Column(TypeName = "nvarchar(100)")]
        public string ApproverId { get; set; } // 審核人員

        [Comment("審核人員姓名")]
        [NotMapped]
        public string ApproverName { get; set; } // 審核人員姓名

        [Comment("審核日期")]
        [Column(TypeName = "bigint")]
        public long? ApprovalDate { get; set; } // 審核日期

        [Comment("審核意見")]
        [Column(TypeName = "nvarchar(500)")]
        public string ApprovalComments { get; set; } // 審核意見

        [Comment("執行狀態")]
        [Column(TypeName = "nvarchar(20)")]
        public string ExecutionStatus { get; set; } // 執行狀態 (PENDING, COMPLETED, CANCELLED)

        [Comment("執行人員")]
        [Column(TypeName = "nvarchar(100)")]
        public string ExecutorId { get; set; } // 執行人員

        [Comment("執行人員姓名")]
        [NotMapped]
        public string ExecutorName { get; set; } // 執行人員姓名

        [Comment("執行日期")]
        [Column(TypeName = "bigint")]
        public long? ExecutionDate { get; set; } // 執行日期

        [Comment("備註")]
        [Column(TypeName = "nvarchar(1000)")]
        public string Notes { get; set; } // 備註

        // 導航屬性
        public virtual ICollection<AssetLocationTransferDetail> TransferDetails { get; set; }

        public AssetLocationTransfer()
        {
            TransferId = Guid.NewGuid();
            TransferNo = "";
            TransferDate = 0;
            ApplicantId = "";
            ApplicantDepartmentId = "";
            TransferReason = "";
            ApprovalStatus = "PENDING"; // 預設為待審核
            ApproverId = "";
            ApprovalComments = "";
            ExecutionStatus = "PENDING"; // 預設為待執行
            ExecutorId = "";
            Notes = "";
            TransferDetails = new HashSet<AssetLocationTransferDetail>();
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 財產位置變動單明細
    /// </summary>
    public class AssetLocationTransferDetail : ModelBaseEntity
    {
        [Key]
        [Comment("明細編號")]
        public Guid DetailId { get; set; } // 明細編號

        [Comment("位置變動單編號")]
        public Guid TransferId { get; set; } // 位置變動單編號

        [Comment("財產編號")]
        public Guid AssetId { get; set; } // 財產編號

        [Comment("財產編號")]
        [NotMapped]
        public string AssetNo { get; set; } // 財產編號

        [Comment("財產名稱")]
        [NotMapped]
        public string AssetName { get; set; } // 財產名稱

        [Comment("原存放地點")]
        [Column(TypeName = "nvarchar(100)")]
        public string OriginalLocationId { get; set; } // 原存放地點

        [Comment("原存放地點名稱")]
        [NotMapped]
        public string OriginalLocationName { get; set; } // 原存放地點名稱

        [Comment("新存放地點")]
        [Column(TypeName = "nvarchar(100)")]
        public string NewLocationId { get; set; } // 新存放地點

        [Comment("新存放地點名稱")]
        [NotMapped]
        public string NewLocationName { get; set; } // 新存放地點名稱

        [Comment("原保管人")]
        [Column(TypeName = "nvarchar(100)")]
        public string OriginalCustodianId { get; set; } // 原保管人

        [Comment("原保管人姓名")]
        [NotMapped]
        public string OriginalCustodianName { get; set; } // 原保管人姓名

        [Comment("新保管人")]
        [Column(TypeName = "nvarchar(100)")]
        public string NewCustodianId { get; set; } // 新保管人

        [Comment("新保管人姓名")]
        [NotMapped]
        public string NewCustodianName { get; set; } // 新保管人姓名

        [Comment("原使用人")]
        [Column(TypeName = "nvarchar(100)")]
        public string OriginalUserId { get; set; } // 原使用人

        [Comment("原使用人姓名")]
        [NotMapped]
        public string OriginalUserName { get; set; } // 原使用人姓名

        [Comment("新使用人")]
        [Column(TypeName = "nvarchar(100)")]
        public string NewUserId { get; set; } // 新使用人

        [Comment("新使用人姓名")]
        [NotMapped]
        public string NewUserName { get; set; } // 新使用人姓名

        [Comment("原部門")]
        [Column(TypeName = "nvarchar(100)")]
        public string OriginalDepartmentId { get; set; } // 原部門

        [Comment("原部門名稱")]
        [NotMapped]
        public string OriginalDepartmentName { get; set; } // 原部門名稱

        [Comment("新部門")]
        [Column(TypeName = "nvarchar(100)")]
        public string NewDepartmentId { get; set; } // 新部門

        [Comment("新部門名稱")]
        [NotMapped]
        public string NewDepartmentName { get; set; } // 新部門名稱

        [Comment("原股別")]
        [Column(TypeName = "nvarchar(100)")]
        public string OriginalDivisionId { get; set; } // 原股別

        [Comment("原股別名稱")]
        [NotMapped]
        public string OriginalDivisionName { get; set; } // 原股別名稱

        [Comment("新股別")]
        [Column(TypeName = "nvarchar(100)")]
        public string NewDivisionId { get; set; } // 新股別

        [Comment("新股別名稱")]
        [NotMapped]
        public string NewDivisionName { get; set; } // 新股別名稱

        [Comment("變動項目")]
        [Column(TypeName = "nvarchar(200)")]
        public string ChangeItems { get; set; } // 變動項目 (位置、保管人、使用人、部門等)

        [Comment("明細備註")]
        [Column(TypeName = "nvarchar(500)")]
        public string DetailNotes { get; set; } // 明細備註

        // 導航屬性
        public virtual AssetLocationTransfer Transfer { get; set; }
        public virtual Asset Asset { get; set; }

        public AssetLocationTransferDetail()
        {
            DetailId = Guid.NewGuid();
            TransferId = Guid.Empty;
            AssetId = Guid.Empty;
            OriginalLocationId = "";
            NewLocationId = "";
            OriginalCustodianId = "";
            NewCustodianId = "";
            OriginalUserId = "";
            NewUserId = "";
            OriginalDepartmentId = "";
            NewDepartmentId = "";
            OriginalDivisionId = "";
            NewDivisionId = "";
            ChangeItems = "";
            DetailNotes = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 財產位置變動單 DTO
    /// </summary>
    public class AssetLocationTransferDTO : ModelBaseEntityDTO
    {
        public Guid TransferId { get; set; } // 位置變動單編號
        public string TransferNo { get; set; } // 變動單號
        public long TransferDate { get; set; } // 變動日期
        public string ApplicantId { get; set; } // 申請人員
        public string ApplicantName { get; set; } // 申請人員姓名
        public string ApplicantDepartmentId { get; set; } // 申請部門
        public string ApplicantDepartmentName { get; set; } // 申請部門名稱
        public string TransferReason { get; set; } // 變動原因
        public string ApprovalStatus { get; set; } // 審核狀態
        public string ApproverId { get; set; } // 審核人員
        public string ApproverName { get; set; } // 審核人員姓名
        public long? ApprovalDate { get; set; } // 審核日期
        public string ApprovalComments { get; set; } // 審核意見
        public string ExecutionStatus { get; set; } // 執行狀態
        public string ExecutorId { get; set; } // 執行人員
        public string ExecutorName { get; set; } // 執行人員姓名
        public long? ExecutionDate { get; set; } // 執行日期
        public string Notes { get; set; } // 備註

        public AssetLocationTransferDTO()
        {
            TransferId = Guid.Empty;
            TransferNo = "";
            TransferDate = 0;
            ApplicantId = "";
            ApplicantName = "";
            ApplicantDepartmentId = "";
            ApplicantDepartmentName = "";
            TransferReason = "";
            ApprovalStatus = "PENDING";
            ApproverId = "";
            ApproverName = "";
            ApprovalDate = null;
            ApprovalComments = "";
            ExecutionStatus = "PENDING";
            ExecutorId = "";
            ExecutorName = "";
            ExecutionDate = null;
            Notes = "";
        }
    }

    /// <summary>
    /// 財產位置變動單明細 DTO
    /// </summary>
    public class AssetLocationTransferDetailDTO : ModelBaseEntityDTO
    {
        public Guid DetailId { get; set; } // 明細編號
        public Guid TransferId { get; set; } // 位置變動單編號
        public Guid AssetId { get; set; } // 財產編號
        public string AssetNo { get; set; } // 財產編號
        public string AssetName { get; set; } // 財產名稱
        public string OriginalLocationId { get; set; } // 原存放地點
        public string OriginalLocationName { get; set; } // 原存放地點名稱
        public string NewLocationId { get; set; } // 新存放地點
        public string NewLocationName { get; set; } // 新存放地點名稱
        public string OriginalCustodianId { get; set; } // 原保管人
        public string OriginalCustodianName { get; set; } // 原保管人姓名
        public string NewCustodianId { get; set; } // 新保管人
        public string NewCustodianName { get; set; } // 新保管人姓名
        public string OriginalUserId { get; set; } // 原使用人
        public string OriginalUserName { get; set; } // 原使用人姓名
        public string NewUserId { get; set; } // 新使用人
        public string NewUserName { get; set; } // 新使用人姓名
        public string OriginalDepartmentId { get; set; } // 原部門
        public string OriginalDepartmentName { get; set; } // 原部門名稱
        public string NewDepartmentId { get; set; } // 新部門
        public string NewDepartmentName { get; set; } // 新部門名稱
        public string OriginalDivisionId { get; set; } // 原股別
        public string OriginalDivisionName { get; set; } // 原股別名稱
        public string NewDivisionId { get; set; } // 新股別
        public string NewDivisionName { get; set; } // 新股別名稱
        public string ChangeItems { get; set; } // 變動項目
        public string DetailNotes { get; set; } // 明細備註

        public AssetLocationTransferDetailDTO()
        {
            DetailId = Guid.Empty;
            TransferId = Guid.Empty;
            AssetId = Guid.Empty;
            AssetNo = "";
            AssetName = "";
            OriginalLocationId = "";
            OriginalLocationName = "";
            NewLocationId = "";
            NewLocationName = "";
            OriginalCustodianId = "";
            OriginalCustodianName = "";
            NewCustodianId = "";
            NewCustodianName = "";
            OriginalUserId = "";
            OriginalUserName = "";
            NewUserId = "";
            NewUserName = "";
            OriginalDepartmentId = "";
            OriginalDepartmentName = "";
            NewDepartmentId = "";
            NewDepartmentName = "";
            OriginalDivisionId = "";
            OriginalDivisionName = "";
            NewDivisionId = "";
            NewDivisionName = "";
            ChangeItems = "";
            DetailNotes = "";
        }
    }

    /// <summary>
    /// 財產位置變動單完整資料 DTO
    /// </summary>
    public class AssetLocationTransferWithDetailsDTO
    {
        public AssetLocationTransferDTO Transfer { get; set; }
        public List<AssetLocationTransferDetailDTO> Details { get; set; }

        public AssetLocationTransferWithDetailsDTO()
        {
            Transfer = new AssetLocationTransferDTO();
            Details = new List<AssetLocationTransferDetailDTO>();
        }
    }

    /// <summary>
    /// 財產位置變動審核請求 DTO
    /// </summary>
    public class AssetLocationTransferApprovalDTO
    {
        public string TransferNo { get; set; } // 位置變動單編號
        public string ApprovalStatus { get; set; } // 審核狀態 (APPROVED, REJECTED)
        public string ApprovalComments { get; set; } // 審核意見
        public string ApproverId { get; set; } // 審核人員

        public AssetLocationTransferApprovalDTO()
        {
            TransferNo = "";
            ApprovalStatus = "";
            ApprovalComments = "";
            ApproverId = "";
        }
    }

    /// <summary>
    /// 財產位置變動執行請求 DTO
    /// </summary>
    public class AssetLocationTransferExecutionDTO
    {
        public string TransferNo { get; set; } // 位置變動單編號
        public string ExecutionStatus { get; set; } // 執行狀態 (COMPLETED, CANCELLED)
        public string ExecutorId { get; set; } // 執行人員

        public AssetLocationTransferExecutionDTO()
        {
            TransferNo = "";
            ExecutionStatus = "";
            ExecutorId = "";
        }
    }
}