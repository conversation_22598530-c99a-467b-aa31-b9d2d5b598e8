import React from "react";
import { ColumnsType } from "antd/es/table";
import { Button, Tag, Space, Tooltip, Typography } from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  PlayCircleOutlined,
  StopOutlined,
  SwapOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import {
  AssetLocationTransfer,
  AssetLocationTransferDetail,
} from "./interface";
import {
  getApprovalStatusText,
  getExecutionStatusText,
  getApprovalStatusColor,
  getExecutionStatusColor,
  getChangeItemsText,
} from "./interface";

const { Text } = Typography;

// 主要列表欄位
export const getMainTableColumns = (
  onEdit: (record: AssetLocationTransfer) => void,
  onView: (record: AssetLocationTransfer) => void,
  onDelete: (record: AssetLocationTransfer) => void,
  onApprove: (record: AssetLocationTransfer) => void,
  onReject: (record: AssetLocationTransfer) => void,
  onExecute: (record: AssetLocationTransfer) => void,
  onCancel: (record: AssetLocationTransfer) => void,
  currentUserId?: string,
  permissions?: any
): ColumnsType<AssetLocationTransfer> => [
  {
    title: "變動單號",
    dataIndex: "transferNo",
    key: "transferNo",
    width: 150,
    fixed: "left",
    render: (text: string) => (
      <Text copyable={{ text }} strong>
        {text}
      </Text>
    ),
  },
  {
    title: "變動日期",
    dataIndex: "transferDate",
    key: "transferDate",
    width: 120,
    render: (timestamp: number) =>
      timestamp ? dayjs(timestamp).format("YYYY-MM-DD") : "-",
  },
  {
    title: "申請人",
    dataIndex: "applicantName",
    key: "applicantName",
    width: 100,
    ellipsis: true,
  },
  {
    title: "申請部門",
    dataIndex: "applicantDepartmentName",
    key: "applicantDepartmentName",
    width: 150,
    ellipsis: true,
  },
  {
    title: "變動原因",
    dataIndex: "transferReason",
    key: "transferReason",
    width: 200,
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip title={text} placement="topLeft">
        {text}
      </Tooltip>
    ),
  },
  {
    title: "審核狀態",
    dataIndex: "approvalStatus",
    key: "approvalStatus",
    width: 100,
    render: (status: string) => (
      <Tag color={getApprovalStatusColor(status)}>
        {getApprovalStatusText(status)}
      </Tag>
    ),
  },
  {
    title: "執行狀態",
    dataIndex: "executionStatus",
    key: "executionStatus",
    width: 100,
    render: (status: string) => (
      <Tag color={getExecutionStatusColor(status)}>
        {getExecutionStatusText(status)}
      </Tag>
    ),
  },
  {
    title: "審核人",
    dataIndex: "approverName",
    key: "approverName",
    width: 100,
    ellipsis: true,
    render: (text: string) => text || "-",
  },
  {
    title: "審核日期",
    dataIndex: "approvalDate",
    key: "approvalDate",
    width: 120,
    render: (timestamp: number) =>
      timestamp ? dayjs(timestamp).format("YYYY-MM-DD") : "-",
  },
  {
    title: "執行人",
    dataIndex: "executorName",
    key: "executorName",
    width: 100,
    ellipsis: true,
    render: (text: string) => text || "-",
  },
  {
    title: "執行日期",
    dataIndex: "executionDate",
    key: "executionDate",
    width: 120,
    render: (timestamp: number) =>
      timestamp ? dayjs(timestamp).format("YYYY-MM-DD") : "-",
  },
  {
    title: "備註",
    dataIndex: "notes",
    key: "notes",
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip title={text} placement="topLeft">
        {text || "-"}
      </Tooltip>
    ),
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right",
    render: (_, record) => {
      const isPending = record.approvalStatus === "PENDING";
      const isApproved = record.approvalStatus === "APPROVED";
      const isRejected = record.approvalStatus === "REJECTED";
      const isExecutionPending = record.executionStatus === "PENDING";
      const isCompleted = record.executionStatus === "COMPLETED";
      const isCancelled = record.executionStatus === "CANCELLED";

      return (
        <Space size="small">
          <Tooltip title="檢視">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onView(record)}
            />
          </Tooltip>

          {isPending && (
            <>
              <Tooltip title="編輯">
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => onEdit(record)}
                />
              </Tooltip>
              <Tooltip title="審核">
                <Button
                  type="text"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => onApprove(record)}
                />
              </Tooltip>
              <Tooltip title="駁回">
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => onReject(record)}
                  danger
                />
              </Tooltip>
            </>
          )}

          {isApproved && isExecutionPending && (
            <>
              <Tooltip title="執行變動">
                <Button
                  type="text"
                  size="small"
                  icon={<PlayCircleOutlined />}
                  onClick={() => onExecute(record)}
                />
              </Tooltip>
              <Tooltip title="取消執行">
                <Button
                  type="text"
                  size="small"
                  icon={<StopOutlined />}
                  onClick={() => onCancel(record)}
                  danger
                />
              </Tooltip>
            </>
          )}

          {(isPending || (isRejected && !isCompleted)) && (
            <Tooltip title="刪除">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => onDelete(record)}
                danger
              />
            </Tooltip>
          )}
        </Space>
      );
    },
  },
];

// 變動明細欄位
export const getDetailTableColumns = (
  onEditDetail?: (record: AssetLocationTransferDetail) => void,
  onDeleteDetail?: (record: AssetLocationTransferDetail) => void,
  isViewMode?: boolean
): ColumnsType<AssetLocationTransferDetail> => [
  {
    title: "財產編號",
    dataIndex: "assetNo",
    key: "assetNo",
    width: 120,
    render: (text: string) => (
      <Text copyable={{ text }} strong>
        {text}
      </Text>
    ),
  },
  {
    title: "財產名稱",
    dataIndex: "assetName",
    key: "assetName",
    width: 200,
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip title={text} placement="topLeft">
        {text}
      </Tooltip>
    ),
  },
  {
    title: "變動項目",
    dataIndex: "changeItems",
    key: "changeItems",
    width: 120,
    render: (items: string) => (
      <Tag icon={<SwapOutlined />} color="blue">
        {getChangeItemsText(items)}
      </Tag>
    ),
  },
  {
    title: "原存放地點",
    dataIndex: "originalLocationName",
    key: "originalLocationName",
    width: 150,
    ellipsis: true,
  },
  {
    title: "新存放地點",
    dataIndex: "newLocationName",
    key: "newLocationName",
    width: 150,
    ellipsis: true,
    render: (text: string) => (
      <Text type={text ? "success" : "secondary"}>{text || "未設定"}</Text>
    ),
  },
  {
    title: "原保管人",
    dataIndex: "originalCustodianName",
    key: "originalCustodianName",
    width: 100,
    ellipsis: true,
  },
  {
    title: "新保管人",
    dataIndex: "newCustodianName",
    key: "newCustodianName",
    width: 100,
    ellipsis: true,
    render: (text: string) => (
      <Text type={text ? "success" : "secondary"}>{text || "未設定"}</Text>
    ),
  },
  {
    title: "原使用人",
    dataIndex: "originalUserName",
    key: "originalUserName",
    width: 100,
    ellipsis: true,
  },
  {
    title: "新使用人",
    dataIndex: "newUserName",
    key: "newUserName",
    width: 100,
    ellipsis: true,
    render: (text: string) => (
      <Text type={text ? "success" : "secondary"}>{text || "未設定"}</Text>
    ),
  },
  {
    title: "原部門",
    dataIndex: "originalDepartmentName",
    key: "originalDepartmentName",
    width: 120,
    ellipsis: true,
  },
  {
    title: "新部門",
    dataIndex: "newDepartmentName",
    key: "newDepartmentName",
    width: 120,
    ellipsis: true,
    render: (text: string) => (
      <Text type={text ? "success" : "secondary"}>{text || "未設定"}</Text>
    ),
  },
  {
    title: "備註",
    dataIndex: "detailNotes",
    key: "detailNotes",
    width: 150,
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip title={text} placement="topLeft">
        {text || "-"}
      </Tooltip>
    ),
  },
  ...(isViewMode
    ? []
    : [
        {
          title: "操作",
          key: "action",
          width: 100,
          fixed: "right" as const,
          render: (_: any, record: AssetLocationTransferDetail) => (
            <Space size="small">
              {onEditDetail && (
                <Tooltip title="編輯">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => onEditDetail(record)}
                  />
                </Tooltip>
              )}
              {onDeleteDetail && (
                <Tooltip title="刪除">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => onDeleteDetail(record)}
                    danger
                  />
                </Tooltip>
              )}
            </Space>
          ),
        },
      ]),
];

// 財產選擇欄位
export const getAssetSelectionColumns = (
  selectedAssets: string[],
  onAssetSelect: (assetIds: string[]) => void
): ColumnsType<any> => [
  {
    title: "財產編號",
    dataIndex: "assetNo",
    key: "assetNo",
    width: 120,
    render: (text: string) => (
      <Text copyable={{ text }} strong>
        {text}
      </Text>
    ),
  },
  {
    title: "財產名稱",
    dataIndex: "assetName",
    key: "assetName",
    width: 200,
    ellipsis: {
      showTitle: false,
    },
    render: (text: string) => (
      <Tooltip title={text} placement="topLeft">
        {text}
      </Tooltip>
    ),
  },
  {
    title: "目前存放地點",
    dataIndex: "currentLocationName",
    key: "currentLocationName",
    width: 150,
    ellipsis: true,
  },
  {
    title: "目前保管人",
    dataIndex: "currentCustodianName",
    key: "currentCustodianName",
    width: 100,
    ellipsis: true,
  },
  {
    title: "目前使用人",
    dataIndex: "currentUserName",
    key: "currentUserName",
    width: 100,
    ellipsis: true,
  },
  {
    title: "目前部門",
    dataIndex: "currentDepartmentName",
    key: "currentDepartmentName",
    width: 120,
    ellipsis: true,
  },
  {
    title: "財產狀態",
    dataIndex: "assetStatusName",
    key: "assetStatusName",
    width: 100,
    render: (text: string) => <Tag color="blue">{text}</Tag>,
  },
];

export default {
  getMainTableColumns,
  getDetailTableColumns,
  getAssetSelectionColumns,
};
