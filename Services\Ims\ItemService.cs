using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;
using System.Transactions;

namespace FAST_ERP_Backend.Services.Ims;
/// <summary> 庫存品服務 </summary>
public class ItemService(ERPDbContext _context, ICurrentUserService _currentUserService, ILoggerService _logger) : IItemService
{
    /// <summary> 庫存品列表 </summary>
    public async Task<List<ItemDTO>> GetAllAsync()
    {
        var items = await _context.Ims_Item
            .OrderBy(e => e.ItemID)
            .Include(t => t.Prices)
            .ToListAsync();

        var result = items.Select(t => new ItemDTO
        {
            ItemID = t.ItemID,
            CustomNO = t.CustomNO,
            Name = t.Name,
            InternationalBarCode = t.InternationalBarCode,
            Unit = t.Unit,
            ItemCategoryID = t.ItemCategoryID,
            Description = t.Description,
            IsStop = t.IsStop,
            TaxType = t.TaxType,
            Prices = t.Prices != null ? t.Prices.Select(p => new ItemPriceDTO
            {
                ItemPriceID = p.ItemPriceID,
                ItemID = p.ItemID,
                PriceTypeID = p.PriceTypeID,
                Price = p.Price,
                CreateTime = p.CreateTime,
                CreateUserId = p.CreateUserId,
                UpdateTime = p.UpdateTime,
                UpdateUserId = p.UpdateUserId,
                DeleteTime = p.DeleteTime,
                DeleteUserId = p.DeleteUserId,
                IsDeleted = p.IsDeleted
            }).ToList() : new List<ItemPriceDTO>(),
            CreateTime = t.CreateTime,
            CreateUserId = t.CreateUserId,
            UpdateTime = t.UpdateTime,
            UpdateUserId = t.UpdateUserId,
            DeleteTime = t.DeleteTime,
            DeleteUserId = t.DeleteUserId,
            IsDeleted = t.IsDeleted
        }).ToList();
        return result;
    }

    /// <summary> 庫存品取得 </summary>
    public async Task<List<ItemDTO>> GetAsync(Guid ItemID)
    {
        var items = await _context.Ims_Item.Where(e => e.ItemID == ItemID)
            .OrderBy(e => e.ItemID)
            .Include(t => t.Prices)
            .ToListAsync();

        var result = items.Select(t => new ItemDTO
        {
            ItemID = t.ItemID,
            CustomNO = t.CustomNO,
            Name = t.Name,
            InternationalBarCode = t.InternationalBarCode,
            Unit = t.Unit,
            ItemCategoryID = t.ItemCategoryID,
            Description = t.Description,
            IsStop = t.IsStop,
            TaxType = t.TaxType,
            Prices = t.Prices != null ? t.Prices.Select(p => new ItemPriceDTO
            {
                ItemPriceID = p.ItemPriceID,
                ItemID = p.ItemID,
                PriceTypeID = p.PriceTypeID,
                Price = p.Price,
                CreateTime = p.CreateTime,
                CreateUserId = p.CreateUserId,
                UpdateTime = p.UpdateTime,
                UpdateUserId = p.UpdateUserId,
                DeleteTime = p.DeleteTime,
                DeleteUserId = p.DeleteUserId,
                IsDeleted = p.IsDeleted
            }).ToList() : new List<ItemPriceDTO>(),
            CreateTime = t.CreateTime,
            CreateUserId = t.CreateUserId,
            UpdateTime = t.UpdateTime,
            UpdateUserId = t.UpdateUserId,
            DeleteTime = t.DeleteTime,
            DeleteUserId = t.DeleteUserId,
            IsDeleted = t.IsDeleted
        }).ToList();
        return result;
    }

    /// <summary> 庫存品新增 </summary>
    public async Task<(bool, string)> AddAsync(ItemDTO dto)
    {
        if (dto == null)
        {
            return (false, "庫存品資料不可為空!");
        }

        var entity = new Item
        {
            ItemID = Guid.NewGuid(),
            CustomNO = dto.CustomNO,
            Name = dto.Name,
            InternationalBarCode = dto.InternationalBarCode,
            Unit = dto.Unit,
            ItemCategoryID = dto.ItemCategoryID,
            Description = dto.Description,
            IsStop = dto.IsStop,
            TaxType = dto.TaxType, // 使用傳入的稅別
            CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId
        };

        await _context.Ims_Item.AddAsync(entity);
        var result = await _context.SaveChangesAsync();

        if (result <= 0)
        {
            return (false, "新增庫存品失敗!");
        }

        return (true, $"庫存品 {entity.ItemID} 新增成功!");
    }

    /// <summary> 庫存品更新 </summary>
    public async Task<(bool, string)> UpdateAsync(ItemDTO dto)
    {
        var entity = await _context.Ims_Item
            .FirstOrDefaultAsync(i => i.ItemID == dto.ItemID);
        if (entity == null)
        {
            return (false, $"庫存品 {dto.ItemID} 不存在!");
        }

        entity.CustomNO = dto.CustomNO;
        entity.Name = dto.Name;
        entity.InternationalBarCode = dto.InternationalBarCode;
        entity.Unit = dto.Unit;
        entity.ItemCategoryID = dto.ItemCategoryID;
        entity.Description = dto.Description;
        entity.IsStop = dto.IsStop;
        entity.TaxType = dto.TaxType; // 使用傳入的稅別
        entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.UpdateUserId = _currentUserService.UserId;

        _context.Ims_Item.Update(entity);
        await _context.SaveChangesAsync();
        return (true, $"Item {entity.ItemID} updated successfully");
    }

    /// <summary> 庫存品刪除 </summary>
    public async Task<(bool, string)> DeleteAsync(ItemDTO dto)
    {
        var entity = await _context.Ims_Item
            .FirstOrDefaultAsync(i => i.ItemID == dto.ItemID);
        if (entity == null)
        {
            return (false, $"庫存品 {dto.ItemID} 不存在!");
        }
        entity.IsDeleted = true;
        entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        entity.DeleteUserId = _currentUserService.UserId;

        _context.Ims_Item.Update(entity);
        await _context.SaveChangesAsync();
        return (true, $"庫存品 {entity.ItemID} 刪除成功!");
    }
}