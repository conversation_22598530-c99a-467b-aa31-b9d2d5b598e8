using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AssetStatusService : IAssetStatusService
    {
        private readonly ERPDbContext _context;

        public AssetStatusService(ERPDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得財產狀態資料
        /// </summary>
        /// <param name="_assetStatusId"></param>
        /// <returns></returns>
        public async Task<List<AssetStatusDTO>> GetAssetStatusAsync(string _assetStatusId = "")
        {
            var query = _context.Set<AssetStatus>().AsQueryable();

            if (!string.IsNullOrEmpty(_assetStatusId))
            {
                query = query.Where(u => u.AssetStatusId == Guid.Parse(_assetStatusId));
            }

            return await query.Select(u => new AssetStatusDTO
            {
                AssetStatusId = u.AssetStatusId,
                AssetStatusNo = u.AssetStatusNo,
                Name = u.Name,
                SortCode = u.SortCode,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                DeleteTime = u.DeleteTime,
                DeleteUserId = u.DeleteUserId
            }).ToListAsync();
        }

        /// <summary>
        /// 新增財產狀態
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> AddAssetStatusAsync(AssetStatusDTO _data)
        {
            try
            {
                // 檢查財產狀態編號是否已存在
                var existingStatusNo = await _context.Set<AssetStatus>()
                    .Where(u => u.AssetStatusNo == _data.AssetStatusNo && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingStatusNo != null)
                {
                    return (false, "財產狀態編號已存在");
                }

                // 檢查財產狀態名稱是否已存在
                var existingStatusName = await _context.Set<AssetStatus>()
                    .Where(u => u.Name == _data.Name && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingStatusName != null)
                {
                    return (false, "財產狀態名稱已存在");
                }

                var entity = new AssetStatus
                {
                    AssetStatusNo = _data.AssetStatusNo,
                    Name = _data.Name,
                    CreateUserId = _data.CreateUserId,
                };

                _context.Add(entity);
                await _context.SaveChangesAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 編輯財產狀態
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> EditAssetStatusAsync(AssetStatusDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetStatus>().FindAsync(_data.AssetStatusId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 檢查財產狀態編號是否已存在
                var existingStatusNo = await _context.Set<AssetStatus>()
                    .Where(u => u.AssetStatusNo == _data.AssetStatusNo && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingStatusNo != null && existingStatusNo.AssetStatusId != _data.AssetStatusId)
                {
                    return (false, "財產狀態編號已存在");
                }

                // 檢查財產狀態名稱是否已存在
                var existingStatusName = await _context.Set<AssetStatus>()
                    .Where(u => u.Name == _data.Name && (u.DeleteTime == null || u.DeleteTime == 0))
                    .FirstOrDefaultAsync();

                if (existingStatusName != null && existingStatusName.AssetStatusId != _data.AssetStatusId)
                {
                    return (false, "財產狀態名稱已存在");
                }

                entity.AssetStatusNo = _data.AssetStatusNo;
                entity.Name = _data.Name;
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除財產狀態
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public async Task<(bool, string)> DeleteAssetStatusAsync(AssetStatusDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetStatus>().FindAsync(_data.AssetStatusId);
                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得財產狀態詳細資料
        /// </summary>
        /// <param name="_assetStatusId"></param>
        /// <returns></returns>
        public async Task<AssetStatusDTO> GetAssetStatusDetailAsync(string _assetStatusId)
        {
            var entity = await _context.Set<AssetStatus>().FindAsync(_assetStatusId);
            if (entity == null)
            {
                return null;
            }

            return new AssetStatusDTO
            {
                AssetStatusId = entity.AssetStatusId,
                AssetStatusNo = entity.AssetStatusNo,
                Name = entity.Name,
                SortCode = entity.SortCode,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId
            };
        }
    }
}