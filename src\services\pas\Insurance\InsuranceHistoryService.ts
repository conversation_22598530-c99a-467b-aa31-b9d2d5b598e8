import { apiEndpoints } from "@/config/api";
import { httpClient } from "../../http";
import { ApiResponse } from "@/config/api";

// 保險級距歷程資料介面
export interface InsuranceHistory {
    uid: string;
    userId: string;
    insuranceType: number;
    insuranceGradeUid: string;
    startDate: string;
    endDate?: string | null;
}

// 建立空的保險級距歷程資料
export const createEmptyInsuranceHistory = (): InsuranceHistory => ({
    uid: '',
    userId: '',
    insuranceType: 1, // 預設勞保
    insuranceGradeUid: '',
    startDate: '',
    endDate: null,
});

// 取得保險級距歷程明細
export async function getInsuranceHistoryDetail(uid: string): Promise<ApiResponse<InsuranceHistory>> {
    try {
        const response = await httpClient(`${apiEndpoints.getInsuranceHistoryDetail}/${uid}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得保險級距歷程資料錯誤",
        };
    }
}

// 取得員工保險歷程列表
export async function getInsuranceHistoryByUserAndType(
    userId: string,
    insuranceType: number
): Promise<ApiResponse<InsuranceHistory[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getInsuranceHistoryByUserAndType}/${userId}/${insuranceType}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得保險級距歷程列表錯誤",
        };
    }
}

// 取得有效保險級距
export async function getEffectiveInsuranceGrade(
    userId: string,
    insuranceType: number,
    targetDate: string
): Promise<ApiResponse<InsuranceHistory>> {
    try {
        const requestData = {
            userId,
            insuranceType,
            targetDate
        };

        const response = await httpClient(apiEndpoints.getEffectiveInsuranceGrade, {
            method: "POST",
            body: JSON.stringify(requestData),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "取得有效保險級距資料錯誤"
        };
    }
}

// 取得所有保險類型的有效級距（便利方法）
export async function getAllEffectiveInsuranceGrades(
    userId: string,
    targetDate: string
): Promise<ApiResponse<Record<number, InsuranceHistory | null>>> {
    try {
        // 保險類型常數（1: 勞保, 2: 健保, 3: 職災）
        const INSURANCE_TYPES = {
            LABOR: 1,
            HEALTH: 2,
            ACCIDENT: 3
        };

        // 獲取所有保險類型的有效級距
        const promises = [
            getEffectiveInsuranceGrade(userId, INSURANCE_TYPES.LABOR, targetDate),
            getEffectiveInsuranceGrade(userId, INSURANCE_TYPES.HEALTH, targetDate),
            getEffectiveInsuranceGrade(userId, INSURANCE_TYPES.ACCIDENT, targetDate)
        ];

        const results = await Promise.all(promises);
        const effectiveGrades: Record<number, InsuranceHistory | null> = {
            [INSURANCE_TYPES.LABOR]: null,
            [INSURANCE_TYPES.HEALTH]: null,
            [INSURANCE_TYPES.ACCIDENT]: null
        };

        results.forEach((result, index) => {
            if (result.success && result.data) {
                const insuranceType = [INSURANCE_TYPES.LABOR, INSURANCE_TYPES.HEALTH, INSURANCE_TYPES.ACCIDENT][index];
                effectiveGrades[insuranceType] = result.data;
            }
        });

        return {
            success: true,
            data: effectiveGrades
        };
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取所有有效保險級距失敗"
        };
    }
}

// 取得所有保險類型的有效級距（直接調用後端API）
export async function getAllEffectiveInsuranceGradesFromAPI(
    userId: string,
    targetDate: string
): Promise<ApiResponse<Record<number, InsuranceHistory | null>>> {
    try {
        const response = await httpClient(apiEndpoints.getAllEffectiveInsuranceGrades, {
            method: "POST",
            body: JSON.stringify({
                userId,
                targetDate
            }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取所有有效保險級距失敗"
        };
    }
}

// 新增保險級距歷程
export async function addInsuranceHistory(data: Partial<InsuranceHistory>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addInsuranceHistory, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增保險級距歷程失敗",
        };
    }
}

// 編輯保險級距歷程
export async function editInsuranceHistory(data: Partial<InsuranceHistory>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editInsuranceHistory, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯保險級距歷程失敗",
        };
    }
}

// 刪除保險級距歷程
export async function deleteInsuranceHistory(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteInsuranceHistory, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除保險級距歷程失敗",
        };
    }
}
