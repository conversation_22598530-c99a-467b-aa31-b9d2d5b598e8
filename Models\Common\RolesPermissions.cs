﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common
{
    public class RolesPermissionsDTO
    {
        public string RolesPermissionsId { get; set; } //權限編號
        public string RolesId { get; set; } //角色編號
        public string SystemMenuId { get; set; } //系統選單編號
        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態

        public RolesPermissionsDTO()
        {
            RolesPermissionsId = "";
            RolesId = "";
            SystemMenuId = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class RolesPermissions : ModelBaseEntity
    {
        [Key]
        [Comment("權限編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string RolesPermissionsId { get; set; } //權限編號

        [Comment("角色編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string RolesId { get; set; } //角色編號

        [Comment("系統選單編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string SystemMenuId { get; set; } //系統選單編號

        public SystemMenu SystemMenu { get; set; }

         // 導航屬性 
        public virtual Roles Roles { get; set; }

        public RolesPermissions()
        {
            RolesPermissionsId = "";
            RolesId = "";
            SystemMenuId = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}
