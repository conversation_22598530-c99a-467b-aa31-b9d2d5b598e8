.employee-page-container {
    padding: 1.5rem;
}

.employee-page-title {
    font-size    : 1.5rem;
    font-weight  : bold;
    margin-bottom: 1rem;
}

.employee-card {
    width        : 100%;
    border-radius: 12px;
    overflow     : hidden;
    transition   : all 0.3s ease;
}

.employee-card-selected {
    border    : 2px solid #1890ff;
    box-shadow: 0 0 10px #1890ff;
    background: linear-gradient(145deg, #f6f8ff, #ffffff);
}

.employee-card-normal {
    border    : 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    background: linear-gradient(145deg, #ffffff, #f8fafc);
}

.employee-card-body {
    padding : 20px;
    height  : 100%;
    position: relative;
}

.employee-card-header {
    position      : relative;
    margin-bottom : 16px;
    padding-bottom: 12px;
    border-bottom : 1px solid rgba(0, 0, 0, 0.06);
}

.employee-name-tag {
    background   : #1890ff;
    color        : white;
    padding      : 2px 8px;
    border-radius: 4px;
    font-size    : 12px;
    font-weight  : 500;
    display      : inline-block;
}

.employee-name {
    margin         : 0;
    font-size      : 18px;
    font-weight    : 600;
    color          : #1f2937;
    flex           : 1;
    display        : flex;
    align-items    : center;
    justify-content: space-between;
}

.selected-badge {
    font-size    : 11px;
    padding      : 2px 6px;
    background   : #e6f7ff;
    color        : #1890ff;
    border-radius: 10px;
    font-weight  : normal;
}

.employee-info-row {
    display      : flex;
    align-items  : center;
    padding      : 6px 10px;
    background   : rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    gap          : 8px;
}

.info-label {
    color    : #6b7280;
    font-size: 13px;
    width    : 70px;
}

.info-value {
    color      : #111827;
    font-size  : 13px;
    font-weight: 500;
}

.incomplete-warning {
    margin-top   : 12px;
    padding      : 8px 12px;
    background   : #fff2f0;
    border-radius: 6px;
    border       : 1px solid #ffccc7;
    display      : flex;
    align-items  : center;
    gap          : 6px;
}

.warning-dot {
    width        : 5px;
    height       : 5px;
    border-radius: 50%;
    background   : #ff4d4f;
    display      : inline-block;
}

.warning-text {
    color      : #cf1322;
    font-size  : 13px;
    font-weight: 500;
}

.function-menu-card {
    background   : linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 12px;
    box-shadow   : 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.function-button {
    height         : 40px;
    border-radius  : 8px;
    display        : flex;
    align-items    : center;
    justify-content: center;
}

.primary-function-button {
    background: linear-gradient(to right, #1890ff, #096dd9);
    border    : none;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.default-function-button {
    background: #ffffff;
    border    : 1px solid #e6e6e6;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.danger-function-button {
    background: #fff1f0;
    border    : 1px solid #ffccc7;
    box-shadow: 0 1px 2px rgba(255, 77, 79, 0.05);
}

.tabs-container {
    background   : linear-gradient(to bottom, #ffffff, #f5f7fa);
    border-radius: 12px;
    padding      : 24px;
    box-shadow   : 0 2px 12px rgba(0, 0, 0, 0.08);
}

.tabs-header {
    margin-bottom  : 24px;
    display        : flex;
    justify-content: space-between;
    align-items    : center;
}

.tabs-title {
    margin     : 0;
    font-size  : 18px;
    font-weight: 600;
    color      : #1f2937;
}

.tab-content {
    background   : #ffffff;
    border-radius: 12px;
    padding      : 28px;
    min-height   : 400px;
    box-shadow   : 0 1px 3px rgba(0, 0, 0, 0.05);
    position     : relative;
    overflow     : hidden;
}

.empty-state {
    text-align    : center;
    padding       : 60px 20px;
    color         : #6B7280;
    font-size     : 16px;
    background    : #f9fafb;
    border-radius : 12px;
    border        : 1px dashed #e5e7eb;
    display       : flex;
    flex-direction: column;
    align-items   : center;
    gap           : 16px;
}

.empty-state-title {
    font-size  : 16px;
    font-weight: 500;
    color      : #374151;
}

.empty-state-subtitle {
    font-size: 14px;
    color    : #6B7280;
}

.pagination-controls {
    position        : absolute;
    top             : 16px;
    right           : 16px;
    display         : flex;
    align-items     : center;
    gap             : 8px;
    background-color: rgba(255, 255, 255, 0.9);
    padding         : 8px;
    border-radius   : 8px;
    box-shadow      : 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index         : 10;
}

.page-number {
    font-weight: bold;
    min-width  : 50px;
    text-align : center;
}