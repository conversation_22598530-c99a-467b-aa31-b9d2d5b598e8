import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 資產來源
export interface AssetSource {
    assetSourceId: string;
    assetSourceName: string;
    createTime: number | null;
    createUserId: string | null;
    createUserName: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    updateUserName: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    deleteUserName: string | null;
}

export async function getAssetSources(): Promise<ApiResponse<AssetSource[]>> {
    try {
        const response = await httpClient(apiEndpoints.getAssetSources, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "獲取資產來源列表失敗",
            data: [],
        };
    }
}

export async function createAssetSource(data: Partial<AssetSource>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addAssetSource, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增資產來源失敗",
        };
    }
}

export async function updateAssetSource(data: Partial<AssetSource>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editAssetSource, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "更新資產來源失敗",
        };
    }
}

export async function deleteAssetSource(data: Partial<AssetSource>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteAssetSource, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除資產來源失敗",
        };
    }
} 