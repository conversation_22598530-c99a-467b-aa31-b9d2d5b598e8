﻿using System.Security.Cryptography;
using System.Text;

namespace FAST_ERP_Backend.Server.Tools
{
    public class EmployeeClass
    {

        public class CompareCodeName
        {
            public string code;
            public string name;
        }

        private decimal dc_thisyearHealthrat = 0.0211M; //現年二代健保補充費率.

        public decimal GetthisyearHealthrat()
        {
            return dc_thisyearHealthrat;
        }

        //身分證錯誤註記.
        public List<CompareCodeName> list_iderrdata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "無" },
            new CompareCodeName { code = "A", name = "原始給號錯誤" },
            new CompareCodeName { code = "B", name = "離職無從查對" }
        };

        //員工非薪資所得格式代號.
        public List<CompareCodeName> list_incomecode = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "50", name = "非固定薪資/津貼/獎金" },
            new CompareCodeName { code = "51", name = "租賃金(房屋稅籍編號)" },
            new CompareCodeName { code = "52", name = "短期票券利息" },
            new CompareCodeName { code = "53", name = "權利金" },
            new CompareCodeName { code = "54", name = "股利或盈餘所得" },
            new CompareCodeName { code = "5A", name = "金融業利息" },
            new CompareCodeName { code = "5B", name = "其他利息所得" },
            new CompareCodeName { code = "60", name = "資產基礎證券分配之利息所得" },
            new CompareCodeName { code = "90", name = "告發或檢舉獎金" },
            new CompareCodeName { code = "91", name = "競技競賽及機會中獎獎金" },
            new CompareCodeName { code = "92", name = "其他所得" },
            new CompareCodeName { code = "93", name = "退職所得" },
            new CompareCodeName { code = "94", name = "員工認股所得" },
            new CompareCodeName { code = "95", name = "政府補助款" },
            new CompareCodeName { code = "97", name = "捐贈" },
            new CompareCodeName { code = "9A", name = "執行業務所得" }, //需附加業務別代號 可參考https://download.tax.nat.gov.tw/imx/IMX-USER-113.pdf
            new CompareCodeName { code = "9B", name = "演講/稿/教授費" } //需附加費用別代號 98□非自行出版 99□自行出版

        };

        //稱謂.
        public List<CompareCodeName> list_deptypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "配偶" },
            new CompareCodeName { code = "2", name = "父母" },
            new CompareCodeName { code = "3", name = "子女" },
            new CompareCodeName { code = "4", name = "祖父母" },
            new CompareCodeName { code = "5", name = "孫子女" },
            new CompareCodeName { code = "6", name = "外祖父母" },
            new CompareCodeName { code = "7", name = "外孫子女" },
            new CompareCodeName { code = "8", name = "曾祖父母" },
            new CompareCodeName { code = "9", name = "外增祖父母" },
            new CompareCodeName { code = "10", name = "其他眷屬" }
        };

        //勞保健保註記通用.
        public List<CompareCodeName> list_marktypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "身心輕度" },
            new CompareCodeName { code = "2", name = "身心中度" },
            new CompareCodeName { code = "3", name = "極重或重" }
        };

        //發薪狀況.
        public List<CompareCodeName> list_payoffdata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "0", name = "停薪" },
            new CompareCodeName { code = "1", name = "正常" }
        };

        //員工自提額類型(員工自提類型).
        public List<CompareCodeName> list_EmployeeContributionTypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "0", name = "不提撥" },
            new CompareCodeName { code = "1", name = "提撥比率" },
            new CompareCodeName { code = "2", name = "提撥固額" }
        };

        //薪資所得稅率種類(計稅型式).
        public List<CompareCodeName> list_IncomeTaxdata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "1", name = "自動換算" },
            new CompareCodeName { code = "2", name = "固定稅率" },
            new CompareCodeName { code = "3", name = "固定稅額" }
        };

        //特支費型式.
        public List<CompareCodeName> list_spfeetypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "主管" },
            new CompareCodeName { code = "2", name = "總幹事" }
        };

        //血型.
        public List<CompareCodeName> list_bloodtypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "A", name = "A型" },
            new CompareCodeName { code = "A+", name = "A型陽性" },
            new CompareCodeName { code = "A-", name = "A型陰性" },
            new CompareCodeName { code = "B", name = "B型" },
            new CompareCodeName { code = "B+", name = "B型陽性" },
            new CompareCodeName { code = "B-", name = "B型陰性" },
            new CompareCodeName { code = "AB", name = "AB型" },
            new CompareCodeName { code = "AB+", name = "AB型陽性" },
            new CompareCodeName { code = "AB-", name = "AB型陰性" },
            new CompareCodeName { code = "O", name = "O型" },
            new CompareCodeName { code = "O+", name = "O型陽性" },
            new CompareCodeName { code = "O-", name = "O型陰性" }

        };

        //證號別.
        public List<CompareCodeName> list_idtypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "0", name = "本國個人" },
            new CompareCodeName { code = "1", name = "公司行號機關團體" },
            new CompareCodeName { code = "3", name = "外僑或大陸人士" }
        };

        //上下午別.
        public List<CompareCodeName> list_noontypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "A", name = "上午" },
            new CompareCodeName { code = "P", name = "下午" }
        };

        //差勤時間別.
        public List<CompareCodeName> list_attendTimedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "0", name = "00:00" },
            new CompareCodeName { code = "1", name = "00:30" },
            new CompareCodeName { code = "2", name = "01:00" },
            new CompareCodeName { code = "3", name = "01:30" },
            new CompareCodeName { code = "4", name = "02:00" },
            new CompareCodeName { code = "5", name = "02:30" },
            new CompareCodeName { code = "6", name = "03:00" },
            new CompareCodeName { code = "7", name = "03:30" },
            new CompareCodeName { code = "8", name = "04:00" },
            new CompareCodeName { code = "9", name = "04:30" },
            new CompareCodeName { code = "10", name = "05:00" },
            new CompareCodeName { code = "11", name = "05:30" },
            new CompareCodeName { code = "12", name = "06:00" },
            new CompareCodeName { code = "13", name = "06:30" },
            new CompareCodeName { code = "14", name = "07:00" },
            new CompareCodeName { code = "15", name = "07:30" },
            new CompareCodeName { code = "16", name = "08:00" },
            new CompareCodeName { code = "17", name = "08:30" },
            new CompareCodeName { code = "18", name = "09:00" },
            new CompareCodeName { code = "19", name = "09:30" },
            new CompareCodeName { code = "20", name = "10:00" },
            new CompareCodeName { code = "21", name = "10:30" },
            new CompareCodeName { code = "22", name = "11:00" },
            new CompareCodeName { code = "23", name = "11:30" },
            new CompareCodeName { code = "24", name = "12:00" },
            new CompareCodeName { code = "25", name = "12:30" },
            new CompareCodeName { code = "26", name = "13:00" },
            new CompareCodeName { code = "27", name = "13:30" },
            new CompareCodeName { code = "28", name = "14:00" },
            new CompareCodeName { code = "29", name = "14:30" },
            new CompareCodeName { code = "30", name = "15:00" },
            new CompareCodeName { code = "31", name = "15:30" },
            new CompareCodeName { code = "32", name = "16:00" },
            new CompareCodeName { code = "33", name = "16:30" },
            new CompareCodeName { code = "34", name = "17:00" },
            new CompareCodeName { code = "35", name = "17:30" },
            new CompareCodeName { code = "36", name = "18:00" },
            new CompareCodeName { code = "37", name = "18:30" },
            new CompareCodeName { code = "38", name = "19:00" },
            new CompareCodeName { code = "39", name = "19:30" },
            new CompareCodeName { code = "40", name = "20:00" },
            new CompareCodeName { code = "41", name = "20:30" },
            new CompareCodeName { code = "42", name = "21:00" },
            new CompareCodeName { code = "43", name = "21:30" },
            new CompareCodeName { code = "44", name = "22:00" },
            new CompareCodeName { code = "45", name = "22:30" },
            new CompareCodeName { code = "46", name = "23:00" },
            new CompareCodeName { code = "47", name = "23:30" }
        };

        //差勤類別.
        public List<CompareCodeName> list_attendkinddata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "休假" },
            new CompareCodeName { code = "2", name = "公假" },
            new CompareCodeName { code = "F", name = "出差" },
            new CompareCodeName { code = "G", name = "補休" },
            new CompareCodeName { code = "3", name = "事假" },
            new CompareCodeName { code = "4", name = "病假" },
            new CompareCodeName { code = "5", name = "婚假" },
            new CompareCodeName { code = "6", name = "產假" },
            new CompareCodeName { code = "7", name = "喪假" },
            new CompareCodeName { code = "8", name = "陪產" },
            new CompareCodeName { code = "9", name = "產前" },
            new CompareCodeName { code = "A", name = "曠職" },
            new CompareCodeName { code = "B", name = "加班" },
            new CompareCodeName { code = "C", name = "外出" },
            new CompareCodeName { code = "D", name = "遲到" },
            new CompareCodeName { code = "E", name = "早退" },
            new CompareCodeName { code = "0", name = "應勤" },
            new CompareCodeName { code = "H", name = "下班" },
            new CompareCodeName { code = "I", name = "加下" },
            new CompareCodeName { code = "J", name = "防疫" },
            new CompareCodeName { code = "K", name = "公傷" }
        };

        //錄用類別.
        public List<CompareCodeName> list_categorydata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "01", name = "企劃管理" },
            new CompareCodeName { code = "02", name = "會務管理" },
            new CompareCodeName { code = "03", name = "財務管理" },
            new CompareCodeName { code = "04", name = "人事管理" },
            new CompareCodeName { code = "05", name = "事務管理" },
            new CompareCodeName { code = "06", name = "供銷業務" },
            new CompareCodeName { code = "07", name = "信用業務" },
            new CompareCodeName { code = "08", name = "市場業務" },
            new CompareCodeName { code = "09", name = "農業推廣" },
            new CompareCodeName { code = "10", name = "保險業務" },
            new CompareCodeName { code = "11", name = "加工製造" },
            new CompareCodeName { code = "12", name = "資訊業務" },
            new CompareCodeName { code = "13", name = "農業休閒" }
        };

        //任用資格(原title1).
        public List<CompareCodeName> list_JobroleTypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "正式" },
            new CompareCodeName { code = "2", name = "試用" },
            new CompareCodeName { code = "3", name = "代理" },
            new CompareCodeName { code = "4", name = "臨時" }
        };

        //職稱.
        public List<CompareCodeName> list_Jobtitledata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "01", name = "總幹事" },
            new CompareCodeName { code = "02", name = "副總幹事" },
            new CompareCodeName { code = "03", name = "主任秘書" },
            new CompareCodeName { code = "04", name = "秘書" },
            new CompareCodeName { code = "05", name = "組長" },
            new CompareCodeName { code = "06", name = "部主任" },
            new CompareCodeName { code = "07", name = "廠長(工廠主任)" },
            new CompareCodeName { code = "08", name = "場長" },
            new CompareCodeName { code = "09", name = "辦事處(分部)主任" },
            new CompareCodeName { code = "10", name = "一等專員" },
            new CompareCodeName { code = "11", name = "二等專員" },
            new CompareCodeName { code = "12", name = "技師" },
            new CompareCodeName { code = "13", name = "副技師" },
            new CompareCodeName { code = "14", name = "專員" },
            new CompareCodeName { code = "15", name = "課長" },
            new CompareCodeName { code = "16", name = "股長" },
            new CompareCodeName { code = "17", name = "課員" },
            new CompareCodeName { code = "18", name = "技術員" },
            new CompareCodeName { code = "19", name = "股員" },
            new CompareCodeName { code = "20", name = "辦事員" },
            new CompareCodeName { code = "21", name = "助理技術員" },
            new CompareCodeName { code = "22", name = "雇員" },
            new CompareCodeName { code = "23", name = "技工" },
            new CompareCodeName { code = "24", name = "工友" },
            new CompareCodeName { code = "25", name = "季節工" },
            new CompareCodeName { code = "26", name = "臨時工" },
            new CompareCodeName { code = "27", name = "主任" },
            new CompareCodeName { code = "28", name = "特約人員" }
        };

        //薪資類型(原forest1).
        public List<CompareCodeName> list_salaryTypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "薪點" },
            new CompareCodeName { code = "3", name = "月薪(月前)" },
            new CompareCodeName { code = "4", name = "月薪(月底)" },
            new CompareCodeName { code = "5", name = "日薪(月前)" },
            new CompareCodeName { code = "6", name = "日薪(月底)" }
        };

        //職等.
        public List<CompareCodeName> list_leveldata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "十二職等" },
            new CompareCodeName { code = "2", name = "十一職等" },
            new CompareCodeName { code = "3", name = "十職等" },
            new CompareCodeName { code = "4", name = "九職等" },
            new CompareCodeName { code = "5", name = "八職等" },
            new CompareCodeName { code = "6", name = "七職等" },
            new CompareCodeName { code = "7", name = "六職等" },
            new CompareCodeName { code = "8", name = "五職等" },
            new CompareCodeName { code = "9", name = "四職等" },
            new CompareCodeName { code = "10", name = "三職等" },
            new CompareCodeName { code = "11", name = "二職等" },
            new CompareCodeName { code = "12", name = "一職等" }
        };

        //級數.
        public List<CompareCodeName> list_rankdata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "", name = "" },
            new CompareCodeName { code = "1", name = "九級" },
            new CompareCodeName { code = "2", name = "八級" },
            new CompareCodeName { code = "3", name = "七級" },
            new CompareCodeName { code = "4", name = "六級" },
            new CompareCodeName { code = "5", name = "五級" },
            new CompareCodeName { code = "6", name = "四級" },
            new CompareCodeName { code = "7", name = "三級" },
            new CompareCodeName { code = "8", name = "二級" },
            new CompareCodeName { code = "9", name = "一級" },
            new CompareCodeName { code = "10", name = "年功一" },
            new CompareCodeName { code = "11", name = "年功二" },
            new CompareCodeName { code = "12", name = "年功三" },
            new CompareCodeName { code = "13", name = "年功四" },
            new CompareCodeName { code = "14", name = "年功五" }
        };

        //評議別種類.
        public List<CompareCodeName> list_meetingkinddata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "01", name = "聘僱" },
            new CompareCodeName { code = "02", name = "職等" },
            new CompareCodeName { code = "03", name = "核薪" },
            new CompareCodeName { code = "04", name = "解聘" },
            new CompareCodeName { code = "05", name = "解僱" },
            new CompareCodeName { code = "06", name = "考核" },
            new CompareCodeName { code = "07", name = "績效獎金" },
            new CompareCodeName { code = "08", name = "獎懲" },
            new CompareCodeName { code = "09", name = "晉升" },
            new CompareCodeName { code = "10", name = "調任" },
            new CompareCodeName { code = "11", name = "資遣" },
            new CompareCodeName { code = "12", name = "退休" },
            new CompareCodeName { code = "13", name = "優退" },
            new CompareCodeName { code = "14", name = "撫卹" },
            new CompareCodeName { code = "15", name = "復議" },
            new CompareCodeName { code = "16", name = "資深績優" },
            new CompareCodeName { code = "17", name = "其他" }
        };

        //升遷類型.
        public List<CompareCodeName> list_promotionTypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "01", name = "聘僱" },
            new CompareCodeName { code = "02", name = "職等" },
            new CompareCodeName { code = "03", name = "核薪" },
            new CompareCodeName { code = "04", name = "解聘" },
            new CompareCodeName { code = "05", name = "解僱" },
            new CompareCodeName { code = "06", name = "考核" },
            new CompareCodeName { code = "07", name = "績效獎金" },
            new CompareCodeName { code = "08", name = "獎懲" },
            new CompareCodeName { code = "09", name = "晉升" },
            new CompareCodeName { code = "10", name = "調任" },
            new CompareCodeName { code = "11", name = "資遣" },
            new CompareCodeName { code = "12", name = "退休" },
            new CompareCodeName { code = "13", name = "優退" },
            new CompareCodeName { code = "14", name = "撫卹" },
            new CompareCodeName { code = "15", name = "復議" },
            new CompareCodeName { code = "16", name = "資深績優" },
            new CompareCodeName { code = "17", name = "其他" }
        };

        //結業類型.
        public List<CompareCodeName> list_graduatedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "1", name = "畢業" },
            new CompareCodeName { code = "2", name = "肄業" }
        };

        //學位等級.
        public List<CompareCodeName> list_DegreeTypedata = new List<CompareCodeName>
        {
            new CompareCodeName { code = "1", name = "國小" },
            new CompareCodeName { code = "2", name = "國中" },
            new CompareCodeName { code = "3", name = "高中/職" },
            new CompareCodeName { code = "4", name = "專科" },
            new CompareCodeName { code = "5", name = "大學" },
            new CompareCodeName { code = "6", name = "碩士" },
            new CompareCodeName { code = "7", name = "博士" },
            new CompareCodeName { code = "9", name = "其他" }
        };

        //留復職識別.
        public List<CompareCodeName> list_SuspendType = new List<CompareCodeName>
        {
            new CompareCodeName { code = "0", name = "復職" },
            new CompareCodeName { code = "1", name = "留職" }
        };

        //留復職種類.
        public List<CompareCodeName> list_SuspendKind = new List<CompareCodeName>
        {
            new CompareCodeName { code = "1", name = "服役" },
            new CompareCodeName { code = "2", name = "進修" },
            new CompareCodeName { code = "3", name = "訓練" },
            new CompareCodeName { code = "4", name = "借調" },
            new CompareCodeName { code = "9", name = "其他" }
        };


        /// <summary>
        /// 獲取錄用類別名稱
        /// </summary>
        /// <param name="_categoryCode">類別代號</param>
        /// <returns></returns>
        public string GetcategoryName(string _categoryCode)
        {
            for (int i = 0; i < list_categorydata.Count; i++)
            {
                if (list_categorydata[i].code == _categoryCode)
                {
                    return list_categorydata[i].name;
                }
            }

            return "錄用類別未對應";
        }

        /// <summary>
        /// 獲取任用資格名稱
        /// </summary>
        /// <param name="_JobroleTypeCode">任用資格代號</param>
        /// <returns></returns>
        public string GetJobroleTypeName(string _JobroleTypeCode)
        {
            for (int i = 0; i < list_JobroleTypedata.Count; i++)
            {
                if (list_JobroleTypedata[i].code == _JobroleTypeCode)
                {
                    return list_JobroleTypedata[i].name;
                }
            }

            return "任用資格未對應";

        }

        /// <summary>
        /// 獲取資料對應.
        /// </summary>
        /// <param name="_inputlist">資料對應list</param>
        /// <param name="_inputcode">資料對應代號</param>
        /// <returns></returns>
        public string GetlistCompareName(List<CompareCodeName> _inputlist, string _inputcode)
        {
            for (int i = 0; i < _inputlist.Count; i++)
            {
                if (_inputlist[i].code == _inputcode)
                {
                    return _inputlist[i].name;
                }
            }

            return "資料未對應";
        }

        /// <summary>
        /// 獲取證號別.
        /// </summary>
        /// <param name="_idtype">證號別代號</param>
        /// <returns></returns>
        public string GetidtypeName(string _idtype)
        {
            for (int i = 0; i < list_idtypedata.Count; i++)
            {
                if (list_idtypedata[i].code == _idtype)
                {
                    return list_idtypedata[i].name;
                }
            }

            return "證號別未對應";
        }

        /// <summary>
        /// 獲取職稱名稱
        /// </summary>
        /// <param name="_JobtitleCode">職稱代號</param>
        /// <returns></returns>
        public string GetJobtitleName(string _JobroleCode)
        {
            for (int i = 0; i < list_Jobtitledata.Count; i++)
            {
                if (list_Jobtitledata[i].code == _JobroleCode)
                {
                    return list_Jobtitledata[i].name;
                }
            }

            return "職稱未對應";
        }

        /// <summary>
        /// 獲取薪俸類型名稱
        /// </summary>
        /// <param name="_salaryTypeCode">薪俸代號</param>
        /// <returns></returns>
        public string GetsalaryTypeName(string _salaryTypeCode)
        {
            for (int i = 0; i < list_salaryTypedata.Count; i++)
            {
                if (list_salaryTypedata[i].code == _salaryTypeCode)
                {
                    return list_salaryTypedata[i].name;
                }
            }

            return "薪俸未對應";

        }

        /// <summary>
        /// 獲取特支費對應名稱
        /// </summary>
        /// <param name="_spfeetypeCode">特支費代號</param>
        /// <returns></returns>
        public string GetspfeetypeName(string _spfeetypeCode)
        {
            for (int i = 0; i < list_spfeetypedata.Count; i++)
            {
                if (list_spfeetypedata[i].code == _spfeetypeCode)
                {
                    return list_spfeetypedata[i].name;
                }
            }

            return "特支費未對應";

        }

        /// <summary>
        /// 獲取職等名稱
        /// </summary>
        /// <param name="_levelCode">職等代號</param>
        /// <returns></returns>
        public string GetlevelName(string _levelCode)
        {
            for (int i = 0; i < list_leveldata.Count; i++)
            {
                if (list_leveldata[i].code == _levelCode)
                {
                    return list_leveldata[i].name;
                }
            }

            return "職等未對應";

        }

        /// <summary>
        /// 獲取級數名稱
        /// </summary>
        /// <param name="_rankCode">級數代號</param>
        /// <returns></returns>
        public string GetrankName(string _rankCode)
        {
            for (int i = 0; i < list_rankdata.Count; i++)
            {
                if (list_rankdata[i].code == _rankCode)
                {
                    return list_rankdata[i].name;
                }
            }
            return "級數未對應";
        }

        /// <summary>
        /// 獲取評議別種類名稱
        /// </summary>
        /// <param name="_meetingkindCode">評議別種類代號</param>
        /// <returns></returns>
        public string GetmeetingkindName(string _meetingkindCode)
        {
            for (int i = 0; i < list_meetingkinddata.Count; i++)
            {
                if (list_meetingkinddata[i].code == _meetingkindCode)
                {
                    return list_meetingkinddata[i].name;
                }
            }
            return "評議別種類未對應";
        }

        /// <summary>
        /// 獲取升遷類型名稱
        /// </summary>
        /// <param name="_promotionTypeCode">升遷類型代號</param>
        /// <returns></returns>
        public string GetPromotionTypeName(string _promotionTypeCode)
        {
            for (int i = 0; i < list_promotionTypedata.Count; i++)
            {
                if (list_promotionTypedata[i].code == _promotionTypeCode)
                {
                    return list_promotionTypedata[i].name;
                }
            }
            return "升遷類型未對應";
        }

        /// <summary>
        /// 獲取最高學歷名稱
        /// </summary>
        /// <param name="_eduCode">最高學歷代號</param>
        /// <returns></returns>
        public string GeteduDegreeTypeName(string _eduCode)
        {
            for (int i = 0; i < list_DegreeTypedata.Count; i++)
            {
                if (list_DegreeTypedata[i].code == _eduCode)
                {
                    return list_DegreeTypedata[i].name;
                }
            }
            return "最高學歷未對應";
        }

        /// <summary>
        /// 獲取血型名稱.
        /// </summary>
        /// <param name="_idtype">證號別代號</param>
        /// <returns></returns>
        public string GetbloodtypeName(string _idtype)
        {
            for (int i = 0; i < list_bloodtypedata.Count; i++)
            {
                if (list_bloodtypedata[i].code == _idtype)
                {
                    return list_bloodtypedata[i].name;
                }
            }

            return "血型未對應";
        }

    }
}
