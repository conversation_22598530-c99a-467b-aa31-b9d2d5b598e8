"use client";

import { List, Badge, Typography, Avatar, Tag, Spin } from "antd";
import {
  BellOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useState, useEffect } from "react";

const { Title, Text } = Typography;

export function NotificationWidget() {
  const [loading, setLoading] = useState(true);

  // 模擬通知數據
  const notifications = [
    {
      id: 1,
      title: "系統更新完成",
      content: "系統已成功更新到最新版本",
      type: "success",
      time: "2 分鐘前",
      read: false,
    },
    {
      id: 2,
      title: "新任務分配",
      content: "您有一個新的任務需要處理",
      type: "info",
      time: "10 分鐘前",
      read: false,
    },
    {
      id: 3,
      title: "存儲空間警告",
      content: "系統存儲空間使用率已達 85%",
      type: "warning",
      time: "1 小時前",
      read: true,
    },
    {
      id: 4,
      title: "備份失敗",
      content: "昨日自動備份執行失敗，請檢查",
      type: "error",
      time: "2 小時前",
      read: true,
    },
    {
      id: 5,
      title: "會議提醒",
      content: "下午 3:00 有重要會議",
      type: "info",
      time: "3 小時前",
      read: true,
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
      case "warning":
        return <WarningOutlined style={{ color: "#faad14" }} />;
      case "error":
        return <ExclamationCircleOutlined style={{ color: "#ff4d4f" }} />;
      default:
        return <InfoCircleOutlined style={{ color: "#1890ff" }} />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "#52c41a";
      case "warning":
        return "#faad14";
      case "error":
        return "#ff4d4f";
      default:
        return "#1890ff";
    }
  };

  if (loading) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spin size="large">
          <div
            style={{
              padding: "20px",
              textAlign: "center",
              color: "#666",
            }}
          >
            載入通知中心...
          </div>
        </Spin>
      </div>
    );
  }

  return (
    <div style={{ height: "100%" }}>
      <div
        style={{ display: "flex", alignItems: "center", marginBottom: "16px" }}
      >
        <BellOutlined
          style={{ fontSize: "20px", color: "#1890ff", marginRight: "8px" }}
        />
        <Title level={5} style={{ margin: 0 }}>
          通知中心
        </Title>
        <Badge
          count={notifications.filter((n) => !n.read).length}
          style={{ marginLeft: "8px" }}
        />
      </div>

      <List
        size="small"
        dataSource={notifications}
        renderItem={(notification) => (
          <List.Item
            style={{
              opacity: notification.read ? 0.6 : 1,
              backgroundColor: notification.read ? "transparent" : "#f6ffed",
              padding: "8px",
              borderRadius: "4px",
              marginBottom: "4px",
            }}
          >
            <List.Item.Meta
              avatar={
                <Avatar
                  size="small"
                  icon={getNotificationIcon(notification.type)}
                  style={{
                    backgroundColor: getNotificationColor(notification.type),
                  }}
                />
              }
              title={
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Text
                    strong={!notification.read}
                    style={{ fontSize: "13px" }}
                  >
                    {notification.title}
                  </Text>
                  {!notification.read && (
                    <Badge status="processing" style={{ marginLeft: "8px" }} />
                  )}
                </div>
              }
              description={
                <div>
                  <Text
                    type="secondary"
                    style={{ fontSize: "12px", display: "block" }}
                  >
                    {notification.content}
                  </Text>
                  <Text
                    type="secondary"
                    style={{ fontSize: "11px", marginTop: "4px" }}
                  >
                    {notification.time}
                  </Text>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );
}

export default NotificationWidget;
