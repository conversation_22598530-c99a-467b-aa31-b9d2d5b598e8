import { apiEndpoints } from "@/config/api";
import { httpClient } from "../http";
import { ApiResponse } from "@/config/api";

// 檢覈資料
export interface Certification {
    uid: string;
    userId: string;
    certificateName: string;
    certificateYearMonth: string;
    certificateInstitution: string;
    certificateDate: string;
    certificateNumber: string;
    remark: string;
}

export const createEmptyCertification = (): Certification => ({
    uid: '',
    userId: '',
    certificateName: '',
    certificateYearMonth: '',
    certificateInstitution: '',
    certificateDate: '',
    certificateNumber: '',
    remark: '',
});

// 搜尋檢覈列表
export async function getCertificationList(userid: string): Promise<ApiResponse<Certification[]>> {
    try {
        const response = await httpClient(`${apiEndpoints.getCertificationList}/${userid}`, {
            method: "GET",
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "搜尋檢覈資料失敗",
        };
    }
}

// 搜尋檢覈明細
export async function getCertificationDetail(uid: string): Promise<ApiResponse<Certification>> {
    return await httpClient(`${apiEndpoints.getCertificationDetail}/${uid}`, {
        method: "GET",
    });
}

// 新增檢覈資料
export async function addCertification(data: Partial<Certification>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.addCertification, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "新增檢覈資料失敗",
        };
    }
}

// 編輯檢覈資料
export async function editCertification(data: Partial<Certification>): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.editCertification, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "編輯檢覈資料失敗",
        };
    }
}

// 刪除檢覈資料
export async function deleteCertification(uid: string): Promise<ApiResponse> {
    try {
        const response = await httpClient(apiEndpoints.deleteCertification, {
            method: "POST",
            body: JSON.stringify(uid),
            headers: {
                "Content-Type": "application/json",
            },
        });
        return response;
    } catch (error: any) {
        return {
            success: false,
            message: error.message || "刪除檢覈資料失敗",
        };
    }
}
