using FAST_ERP_Backend.Models.Pms;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Pms
{
    public interface IAssetService
    {
        /// <summary>
        /// 取得新的財產編號
        /// </summary>
        /// <param name="subject">科目</param>
        /// <param name="subSubject">子目</param>
        /// <param name="category">類別</param>
        /// <returns>新的財產編號</returns>
        Task<(bool success, string assetNo, string message)> GetNewAssetNoAsync(string subject, string subSubject, string category);

        /// <summary>
        /// 取得資產資料列表
        /// </summary>
        /// <returns>資產資料列表</returns>
        Task<List<AssetWithAccessoriesDTO>> GetAssetAsync();

        /// <summary>
        /// 新增資產資料
        /// </summary>
        /// <param name="_data">資產資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> AddAssetAsync(AssetWithAccessoriesDTO _data);

        /// <summary>
        /// 編輯資產資料
        /// </summary>
        /// <param name="_data">資產資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> EditAssetAsync(AssetWithAccessoriesDTO _data);

        /// <summary>
        /// 刪除資產資料
        /// </summary>
        /// <param name="_data">資產資料</param>
        /// <returns>執行結果及訊息</returns>
        Task<(bool, string)> DeleteAssetAsync(AssetWithAccessoriesDTO _data);

        /// <summary>
        /// 取得資產詳細資料
        /// </summary>
        /// <param name="_assetId">資產編號</param>
        /// <returns>資產詳細資料</returns>
        Task<AssetWithAccessoriesDTO> GetAssetDetailAsync(string _assetId);

        /// <summary>
        /// 驗證Excel檔案格式和內容
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <returns>驗證結果</returns>
        Task<BatchValidationResultDTO> ValidateExcelFileAsync(Stream fileStream);

        /// <summary>
        /// 批次匯入資產資料
        /// </summary>
        /// <param name="fileStream">Excel檔案流</param>
        /// <param name="userId">操作使用者ID</param>
        /// <returns>匯入結果</returns>
        Task<BatchImportResultDTO> BatchImportAssetsAsync(Stream fileStream, string userId);

        /// <summary>
        /// 下載批次轉檔範本
        /// </summary>
        /// <returns>Excel範本檔案</returns>
        Task<(byte[] fileBytes, string fileName)> DownloadBatchTemplateAsync();
    }
}