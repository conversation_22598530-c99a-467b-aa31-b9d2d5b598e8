using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Pms;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using FAST_ERP_Backend.Server.Tools;
using Newtonsoft.Json;

namespace FAST_ERP_Backend.Services.Pms
{
    public class AssetSubAccountService : IAssetSubAccountService
    {
        private readonly ERPDbContext _context;
        private readonly Baseform _baseform;
        private readonly IMapper _mapper;

        public AssetSubAccountService(Baseform baseform, ERPDbContext context, IMapper mapper)
        {
            _baseform = baseform;
            _context = context;
            _mapper = mapper;
        }

        /**
         * 新增財產子目
         * @param assetSubAccount 財產子目資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> AddAsync(AssetSubAccountDTO assetSubAccount)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var entity = _mapper.Map<AssetSubAccount>(assetSubAccount);
                entity.AssetSubAccountId = Guid.NewGuid();
                entity.CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.CreateUserId = assetSubAccount.CreateUserId;

                await _context.AddAsync(entity);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();
                return (true, "新增成功");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return (false, $"新增失敗: {ex.Message}");
            }
        }

        /**
         * 刪除財產子目
         * @param _data 財產子目資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> DeleteAsync(AssetSubAccountDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetSubAccount>()
                    .FirstOrDefaultAsync(a => a.AssetSubAccountId == _data.AssetSubAccountId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                // 檢查是否有關聯的財產資料
                var hasRelatedAssets = await _context.Set<Asset>()
                    .AnyAsync(a => a.AssetSubAccountId == _data.AssetSubAccountId && !a.IsDeleted);

                if (hasRelatedAssets)
                {
                    return (false, "此財產子目已被財產資料使用，無法刪除");
                }

                entity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.DeleteUserId = _data.DeleteUserId;
                entity.IsDeleted = true;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"刪除失敗: {ex.Message}");
            }
        }

        /**
         * 取得所有財產子目
         * @returns 財產子目資料列表
        **/
        public async Task<List<AssetSubAccountDTO>> GetAllAsync()
        {
            try
            {
                var entities = await _context.Set<AssetSubAccount>()
                    .Where(a => !a.IsDeleted)
                    .OrderBy(a => a.SortCode)
                    .ToListAsync();

                var result = _mapper.Map<List<AssetSubAccountDTO>>(entities);

                // 獲取相關資料
                foreach (var item in result)
                {
                    // 財產科目資訊
                    var assetAccount = await _context.Set<AssetAccount>()
                        .AsNoTracking()
                        .FirstOrDefaultAsync(a => a.AssetAccountId == item.AssetAccountId && !a.IsDeleted);

                    if (assetAccount != null)
                    {
                        item.AssetAccountNo = assetAccount.AssetAccountNo;
                        item.AssetAccountName = assetAccount.AssetAccountName;
                    }

                    // 使用者資訊
                    if (!string.IsNullOrEmpty(item.CreateUserId))
                    {
                        var createUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.CreateUserId);
                        item.CreateUserName = createUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.UpdateUserId))
                    {
                        var updateUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.UpdateUserId);
                        item.UpdateUserName = updateUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.DeleteUserId))
                    {
                        var deleteUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.DeleteUserId);
                        item.DeleteUserName = deleteUser?.Name ?? "";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得所有財產子目時發生錯誤: {ex.Message}");
                return new List<AssetSubAccountDTO>();
            }
        }

        /**
         * 取得財產科目下的子目
         * @param assetAccountId 財產科目Id
         * @returns 財產子目資料列表
        **/
        public async Task<List<AssetSubAccountDTO>> GetByAssetAccountIdAsync(string assetAccountId)
        {
            try
            {
                var entities = await _context.Set<AssetSubAccount>()
                    .Where(a => a.AssetAccountId == Guid.Parse(assetAccountId) && !a.IsDeleted)
                    .OrderBy(a => a.SortCode)
                    .ToListAsync();

                var result = _mapper.Map<List<AssetSubAccountDTO>>(entities);

                // 獲取相關資料
                foreach (var item in result)
                {
                    // 財產科目資訊
                    var assetAccount = await _context.Set<AssetAccount>()
                        .AsNoTracking()
                        .FirstOrDefaultAsync(a => a.AssetAccountId == item.AssetAccountId && !a.IsDeleted);

                    if (assetAccount != null)
                    {
                        item.AssetAccountNo = assetAccount.AssetAccountNo;
                        item.AssetAccountName = assetAccount.AssetAccountName;
                    }

                    // 使用者資訊
                    if (!string.IsNullOrEmpty(item.CreateUserId))
                    {
                        var createUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.CreateUserId);
                        item.CreateUserName = createUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.UpdateUserId))
                    {
                        var updateUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.UpdateUserId);
                        item.UpdateUserName = updateUser?.Name ?? "";
                    }

                    if (!string.IsNullOrEmpty(item.DeleteUserId))
                    {
                        var deleteUser = await _context.Set<Users>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(u => u.UserId == item.DeleteUserId);
                        item.DeleteUserName = deleteUser?.Name ?? "";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得財產科目下的子目時發生錯誤: {ex.Message}");
                return new List<AssetSubAccountDTO>();
            }
        }

        /**
         * 取得財產子目ById
         * @param id 財產子目Id
         * @returns 財產子目資料
        **/
        public async Task<string> GetByIdAsync(string id)
        {
            try
            {
                var entity = await _context.Set<AssetSubAccount>()
                    .FirstOrDefaultAsync(a => a.AssetSubAccountId == Guid.Parse(id) && !a.IsDeleted);

                if (entity == null)
                {
                    return "找不到資料";
                }

                var dto = _mapper.Map<AssetSubAccountDTO>(entity);

                // 獲取財產科目資訊
                var assetAccount = await _context.Set<AssetAccount>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(a => a.AssetAccountId == entity.AssetAccountId && !a.IsDeleted);

                if (assetAccount != null)
                {
                    dto.AssetAccountName = assetAccount.AssetAccountName;
                    dto.AssetAccountNo = assetAccount.AssetAccountNo;
                }

                return JsonConvert.SerializeObject(dto);
            }
            catch (Exception ex)
            {
                return $"取得資料失敗: {ex.Message}";
            }
        }

        /**
         * 更新財產子目
         * @param _data 財產子目資料
         * @returns 結果(成功/失敗, 訊息)
        **/
        public async Task<(bool, string)> UpdateAsync(AssetSubAccountDTO _data)
        {
            try
            {
                var entity = await _context.Set<AssetSubAccount>()
                    .FirstOrDefaultAsync(a => a.AssetSubAccountId == _data.AssetSubAccountId && !a.IsDeleted);

                if (entity == null)
                {
                    return (false, "找不到資料");
                }

                _mapper.Map(_data, entity);
                entity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                entity.UpdateUserId = _data.UpdateUserId;

                _context.Update(entity);
                await _context.SaveChangesAsync();
                return (true, "編輯成功");
            }
            catch (Exception ex)
            {
                return (false, $"編輯失敗: {ex.Message}");
            }
        }
    }
}