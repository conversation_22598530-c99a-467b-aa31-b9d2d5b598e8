'use client';
import { useEffect, useState } from 'react';
import {
    Button,
    Card,
    Form,
    Input,
    Modal,
    Select,
    Space,
    Switch,
    Table,
    message,
    Popconfirm,
    Tag,
    Tabs,
    Statistic,
    Typography
} from 'antd';
import {
    RegularSalaryItem,
    addRegularSalaryItem,
    deleteRegularSalaryItem,
    editRegularSalaryItem,
    getRegularSalaryItemList,
    createEmptyRegularSalaryItem,
} from '@/services/pas/RegularSalary/RegularSalaryItemService';
import { getRegularSalaryCreaseTypeOptions } from '@/services/pas/OptionParameterService';
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import ApiSelect from '@/app/pas/components/ApiSelect';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title, Text } = Typography;

const RegularSalaryMaintainPage = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState<RegularSalaryItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState<RegularSalaryItem | null>(null);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);
    const [isDecrease, setIsDecrease] = useState(false);
    const [activeTab, setActiveTab] = useState<string>('addition');

    // 獲取資料
    const fetchData = async () => {
        setLoading(true);
        try {
            const response = await getRegularSalaryItemList();
            if (response.success) {
                setData(response.data || []);
            } else {
                message.error(response.message || '獲取資料失敗');
            }
        } catch (error) {
            message.error('獲取資料時發生錯誤');
        }
        setLoading(false);
    };

    useEffect(() => {
        fetchData();
    }, []);

    // 處理編輯時的初始狀態
    useEffect(() => {
        if (editingRecord) {
            setIsDecrease(editingRecord.itemType === '2');
        }
    }, [editingRecord]);

    // 處理新增/編輯
    const handleSubmit = async (values: any) => {
        try {
            const submitData = {
                ...values,
                uid: editingRecord?.uid || '',
                isTaxable: isDecrease ? false : values.isTaxable, // 確保減項時 isTaxable 為 false
            };

            const response = editingRecord
                ? await editRegularSalaryItem(submitData)
                : await addRegularSalaryItem(submitData);

            if (response.success) {
                message.success(editingRecord ? '編輯成功' : '新增成功');
                setModalVisible(false);
                form.resetFields();
                fetchData();
            } else {
                message.error(response.message || (editingRecord ? '編輯失敗' : '新增失敗'));
            }
        } catch (error) {
            message.error('操作失敗');
        }
    };

    // 處理刪除
    const handleDelete = async (uid: string) => {
        try {
            const response = await deleteRegularSalaryItem(uid);
            if (response.success) {
                message.success('刪除成功');
                fetchData();
            } else {
                message.error(response.message || '刪除失敗');
            }
        } catch (error) {
            message.error('刪除失敗');
        } finally {
            setDeleteUid(null);
        }
    };

    // 監聽項目類型變化
    const handleItemTypeChange = (value: string) => {
        // 假設 '2' 為減項
        const isDecreaseType = value === '2';
        setIsDecrease(isDecreaseType);
        if (isDecreaseType) {
            form.setFieldValue('isTaxable', false);
        }
    };

    // 分類薪資項目
    const additionItems = data.filter(item => item.itemType === '1');
    const deductionItems = data.filter(item => item.itemType === '2');

    // 表格列定義
    const getColumns = () => [
        {
            title: '項目名稱',
            dataIndex: 'itemName',
            key: 'itemName',
            render: (text: string) => (
                <Text strong style={{ fontSize: '15px' }}>{text}</Text>
            ),
        },
        {
            title: '課稅',
            dataIndex: 'isTaxableName',
            key: 'isTaxableName',
            render: (text: string, record: RegularSalaryItem) => (
                <Tag color={record.isTaxable ? 'success' : 'warning'} style={{ padding: '4px 12px', borderRadius: '12px' }}>
                    {text}
                </Tag>
            ),
        },
        {
            title: '說明',
            dataIndex: 'description',
            key: 'description',
            render: (text: string) => (
                <Text ellipsis={{ tooltip: text }}>{text || '-'}</Text>
            ),
        },
        {
            title: '啟用狀態',
            dataIndex: 'isEnableName',
            key: 'isEnableName',
            render: (text: string, record: RegularSalaryItem) => (
                <Tag
                    color={record.isEnable ? 'success' : 'default'}
                    style={{
                        padding: '4px 12px',
                        borderRadius: '12px',
                        background: record.isEnable ? 'rgba(82, 196, 26, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                    }}
                >
                    {text}
                </Tag>
            ),
        },
        {
            title: '操作',
            key: 'action',
            render: (_: any, record: RegularSalaryItem) => (
                <Space size="middle" onClick={(e) => e.stopPropagation()}>
                    <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => {
                            setEditingRecord(record);
                            form.setFieldsValue(record);
                            setModalVisible(true);
                        }}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title={
                            <div>
                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                <Text>確定要刪除此筆資料嗎？</Text>
                            </div>
                        }
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確認"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                    >
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    const renderSalaryTable = (items: RegularSalaryItem[]) => (
        <Table
            columns={getColumns()}
            dataSource={items}
            loading={loading}
            rowKey="uid"
            rowClassName={(record) =>
                record.uid === deleteUid ? 'row-deleting-pulse' : ''
            }
            pagination={false}
            style={{ marginTop: 16 }}
        />
    );

    return (
        <div className="p-6">
            <Card
                title={<Title level={4} style={{ margin: 0 }}>常態薪資項目設定</Title>}
                extra={
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                            setEditingRecord(null);
                            setIsDecrease(false);
                            form.setFieldsValue(createEmptyRegularSalaryItem());
                            setModalVisible(true);
                        }}
                        style={{ borderRadius: '6px' }}
                    >
                        新增項目
                    </Button>
                }
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <Tabs
                    activeKey={activeTab}
                    onChange={setActiveTab}
                    type="card"
                    items={[
                        {
                            key: 'addition',
                            label: (
                                <Space>
                                    加項
                                    <Tag color="blue" style={{ marginLeft: 8, borderRadius: '10px' }}>
                                        {additionItems.length}
                                    </Tag>
                                </Space>
                            ),
                            children: renderSalaryTable(additionItems)
                        },
                        {
                            key: 'deduction',
                            label: (
                                <Space>
                                    減項
                                    <Tag color="orange" style={{ marginLeft: 8, borderRadius: '10px' }}>
                                        {deductionItems.length}
                                    </Tag>
                                </Space>
                            ),
                            children: renderSalaryTable(deductionItems)
                        }
                    ]}
                />
            </Card>

            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                            setDeleteUid(null);
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}

            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {editingRecord ? '編輯常態薪資項目' : '新增常態薪資項目'}
                    </Title>
                }
                open={modalVisible}
                onCancel={() => {
                    setModalVisible(false);
                    form.resetFields();
                }}
                footer={null}
                width={600}
                style={{ top: 20 }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                    initialValues={createEmptyRegularSalaryItem()}
                    className="mt-4"
                >
                    <Form.Item
                        name="itemName"
                        label={<Text strong>項目名稱</Text>}
                        rules={[{ required: true, message: '請輸入項目名稱' }]}
                    >
                        <Input placeholder="請輸入項目名稱" />
                    </Form.Item>

                    <Form.Item
                        name="itemType"
                        label={<Text strong>項目類型</Text>}
                        rules={[{ required: true, message: '請選擇項目類型' }]}
                        className="w-full"
                    >
                        <ApiSelect
                            fetchOptions={getRegularSalaryCreaseTypeOptions}
                            placeholder="請選擇薪資項目類型"
                            onChange={handleItemTypeChange}
                        />
                    </Form.Item>

                    {!isDecrease && (
                        <Form.Item
                            name="isTaxable"
                            label={<Text strong>課稅</Text>}
                            valuePropName="checked"
                        >
                            <Switch />
                        </Form.Item>
                    )}

                    <Form.Item
                        name="description"
                        label={<Text strong>說明</Text>}
                    >
                        <Input.TextArea
                            rows={4}
                            placeholder="請輸入項目說明"
                            style={{ borderRadius: '6px' }}
                        />
                    </Form.Item>

                    <Form.Item
                        name="isEnable"
                        label={<Text strong>啟用狀態</Text>}
                        valuePropName="checked"
                        initialValue={true}
                    >
                        <Switch />
                    </Form.Item>

                    <Form.Item>
                        <Space className="w-full justify-end">
                            <Button
                                onClick={() => {
                                    setModalVisible(false);
                                    form.resetFields();
                                }}
                                style={{ borderRadius: '6px' }}
                            >
                                取消
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                style={{ borderRadius: '6px' }}
                            >
                                確定
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default RegularSalaryMaintainPage; 