using System;

namespace FAST_ERP_Backend.Server.Tools
{
    public static class DateTimeConverter
    {
        /// <summary>
        /// 將時間戳記轉換為指定格式的日期時間字串
        /// </summary>
        /// <param name="timestamp">Unix 時間戳記（秒）</param>
        /// <param name="format">日期時間格式，預設為 "yyyy/MM/dd HH:mm"</param>
        /// <returns>格式化後的日期時間字串</returns>
        public static string ConvertToDateTimeString(long timestamp, string format = "yyyy/MM/dd HH:mm")
        {
            if (timestamp <= 0)
            {
                return string.Empty;
            }

            // 使用 DateTimeOffset 來處理時區轉換
            var dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(timestamp);
            var localDateTime = dateTimeOffset.LocalDateTime;
            
            return localDateTime.ToString(format);
        }

        /// <summary>
        /// 將時間戳記轉換為民國年格式
        /// </summary>
        /// <param name="timestamp">Unix 時間戳記（秒）</param>
        /// <param name="format">日期時間格式，預設為 "yyy/MM/dd HH:mm"</param>
        /// <returns>民國年格式的日期時間字串</returns>
        public static string ConvertToROCYearString(long timestamp, string format = "yyy/MM/dd HH:mm")
        {
            if (timestamp <= 0)
            {
                return string.Empty;
            }

            var dateTime = DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime;
            var rocYear = dateTime.Year - 1911;
            var formatWithROC = format.Replace("yyyy", rocYear.ToString("D3"));
            return dateTime.ToString(formatWithROC);
        }

        /// <summary>
        /// 計算兩個時間戳記之間的差異（分鐘）
        /// </summary>
        /// <param name="startTimestamp">開始時間戳記（秒）</param>
        /// <param name="endTimestamp">結束時間戳記（秒）</param>
        /// <returns>分鐘差（四捨五入到整數）</returns>
        public static int CalculateMinutesDifference(long startTimestamp, long endTimestamp)
        {
            if (startTimestamp <= 0 || endTimestamp <= 0)
            {
                return 0;
            }

            var startTime = DateTimeOffset.FromUnixTimeSeconds(startTimestamp).LocalDateTime;
            var endTime = DateTimeOffset.FromUnixTimeSeconds(endTimestamp).LocalDateTime;
            var timeSpan = endTime - startTime;

            return (int)Math.Round(timeSpan.TotalMinutes);
        }

        /// <summary>
        /// 將分鐘數轉換為時間格式字串（只顯示非零的單位）
        /// </summary>
        /// <param name="totalMinutes">總分鐘數</param>
        /// <returns>格式化的時間字串，例如：1日0時12分</returns>
        public static string ConvertMinutesToTimeString(int totalMinutes)
        {
            if (totalMinutes <= 0)
            {
                return "0分";
            }

            var days = totalMinutes / (24 * 60);
            var remainingMinutes = totalMinutes % (24 * 60);
            var hours = remainingMinutes / 60;
            var minutes = remainingMinutes % 60;

            var result = "";
            if (days > 0)
            {
                result += $"{days}日";
            }
            if (hours > 0 || days > 0)
            {
                result += $"{hours}時";
            }
            result += $"{minutes}分";

            return result;
        }
    }
} 