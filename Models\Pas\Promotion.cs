using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 升遷異動資料表
    /// </summary>
    public class Promotion : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string Uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用者編號

        [Comment("升遷類型")]
        [Column(TypeName = "nvarchar(50)")]
        public string PromotionType { get; set; } // 升遷類型

        [Comment("職稱代號")]
        [Column(TypeName = "nvarchar(50)")]
        public string JobTitle { get; set; } // 職稱代號

        [Comment("職等")]
        [Column(TypeName = "nvarchar(10)")]
        public string JobLevel { get; set; } // 職等

        [Comment("級數")]
        [Column(TypeName = "nvarchar(10)")]
        public string JobRank { get; set; } // 級數

        [Comment("升遷日期")]
        [Column(TypeName = "bigint")]
        public long? PromotionDate { get; set; } // 升遷日期

        [Comment("生效日期")]
        [Column(TypeName = "bigint")]
        public long? EffectiveDate { get; set; } // 生效日期

        [Comment("升遷原因")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string PromotionReason { get; set; } // 升遷原因

        [Comment("開支部門異動DATAUID")]
        [Column(TypeName = "nvarchar(100)")]
        public string ExpenseDepartmentChangeUid { get; set; } // 開支部門異動DATAUID

        [Comment("服務部門異動DATAUID")]
        [Column(TypeName = "nvarchar(100)")]
        public string ServiceDepartmentChangeUid { get; set; } // 服務部門異動DATAUID

        [Comment("任用資格")]
        [Column(TypeName = "nvarchar(10)")]
        public string JobroleType { get; set; } // 任用資格

        [Comment("薪俸類型")]
        [Column(TypeName = "nvarchar(10)")]
        public string SalaryType { get; set; } // 薪俸類型

        [Comment("薪俸")]
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SalaryAmount { get; set; } // 薪俸

        [Comment("錄用類別")]
        [Column(TypeName = "nvarchar(100)")]
        public string CategoryType { get; set; } // 錄用類別

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Remark { get; set; } // 備註

        public Promotion()
        {
            Uid = "";
            UserId = "";
            PromotionType = "";
            JobTitle = "";
            JobLevel = "";
            JobRank = "";
            PromotionDate = null;
            EffectiveDate = null;
            PromotionReason = "";
            ExpenseDepartmentChangeUid = "";
            ServiceDepartmentChangeUid = "";
            JobroleType = "";
            SalaryType = "";
            SalaryAmount = null;
            CategoryType = "";
            Remark = "";
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class PromotionDTO : ModelBaseEntityDTO
    {
        public string Uid { get; set; } // 資料編號
        public string UserId { get; set; } // 使用者編號
        public string PromotionType { get; set; } // 升遷類型
        public string PromotionTypeName { get; set; } // 升遷類型名稱
        public string JobTitle { get; set; } // 職稱代號
        public string JobTitleName { get; set; } // 職稱名稱
        public string JobLevel { get; set; } // 職等
        public string JobLevelName { get; set; } // 職等名稱
        public string JobRank { get; set; } // 級數
        public string JobRankName { get; set; } // 級數名稱
        public string PromotionDate { get; set; } // 升遷日期
        public string EffectiveDate { get; set; } // 生效日期
        public string PromotionReason { get; set; } // 升遷原因
        public string ExpenseDepartmentChangeUid { get; set; } // 開支部門異動DATAUID
        public string ServiceDepartmentChangeUid { get; set; } // 服務部門異動DATAUID
        public string JobroleType { get; set; } // 任用資格
        public string JobroleTypeName { get; set; } // 任用資格名稱
        public string SalaryType { get; set; } // 薪俸類型
        public string SalaryTypeName { get; set; } // 薪俸類型名稱
        public string SalaryAmount { get; set; } // 薪俸
        public string CategoryType { get; set; } // 錄用類別
        public string CategoryTypeName { get; set; } // 錄用類別名稱
        public string Remark { get; set; } // 備註

        // 關聯的部門異動資料
        public ExpenseDepartmentChangeDTO ExpenseDepartmentChange { get; set; }
        public ServiceDepartmentChangeDTO ServiceDepartmentChange { get; set; }

        public PromotionDTO()
        {
            Uid = "";
            UserId = "";
            PromotionType = "";
            PromotionTypeName = "";
            JobTitle = "";
            JobTitleName = "";
            JobLevel = "";
            JobLevelName = "";
            JobRank = "";
            JobRankName = "";
            PromotionDate = "";
            EffectiveDate = "";
            PromotionReason = "";
            ExpenseDepartmentChangeUid = "";
            ServiceDepartmentChangeUid = "";
            JobroleType = "";
            JobroleTypeName = "";
            SalaryType = "";
            SalaryTypeName = "";
            SalaryAmount = "";
            CategoryType = "";
            CategoryTypeName = "";
            Remark = "";
            ExpenseDepartmentChange = new ExpenseDepartmentChangeDTO();
            ServiceDepartmentChange = new ServiceDepartmentChangeDTO();
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }
}