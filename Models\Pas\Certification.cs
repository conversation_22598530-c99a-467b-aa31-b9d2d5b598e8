﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Pas
{
    /// <summary>
    /// 檢覈認證資料表
    /// </summary>
    public class Certification : ModelBaseEntity
    {
        [Key]
        [Comment("資料編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string uid { get; set; } // 資料編號

        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string userId { get; set; } // 使用者編號

        [Comment("檢覈名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string certificateName { get; set; } // 檢覈名稱

        [Comment("檢覈年月")]
        [Column(TypeName = "nvarchar(100)")]
        public string? certificateYearMonth { get; set; } // 檢覈年月

        [Comment("檢覈機關")]
        [Column(TypeName = "nvarchar(100)")]
        public string? certificateInstitution { get; set; } // 檢覈機關

        [Comment("發證日期")]
        [Column(TypeName = "bigint")]
        public long? certificateDate { get; set; } // 發證日期

        [Comment("證書文號")]
        [Column(TypeName = "nvarchar(100)")]
        public string? certificateNumber { get; set; } // 證書文號

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string? remark { get; set; } // 備註

        public Certification()
        {
            uid = "";
            userId = "";
            certificateName = "";
            certificateYearMonth = "";
            certificateInstitution = "";
            certificateDate = null;
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    /// <summary>
    /// 檢覈認證資料 DTO
    /// </summary>
    public class CertificationDTO : ModelBaseEntityDTO
    {
        public string uid { get; set; } // 資料編號
        public string userId { get; set; } // 使用者編號
        public string certificateName { get; set; } // 檢覈名稱
        public string certificateYearMonth { get; set; } // 檢覈年月
        public string certificateInstitution { get; set; } // 檢覈機關
        public string certificateDate { get; set; } // 發證日期（timestamp）
        public string certificateNumber { get; set; } // 證書文號
        public string remark { get; set; } // 備註

        public CertificationDTO()
        {
            uid = "";
            userId = "";
            certificateName = "";
            certificateYearMonth = "";
            certificateInstitution = "";
            certificateDate = "";
            certificateNumber = "";
            remark = "";

            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

}

