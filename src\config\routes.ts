/* 路由
  /config/routes.ts
*/

// 基礎路由路徑
export const basePaths = {
    root: '/',
    login: '/login',
    dashboard: '/dashboard',
    common: '/common',
    enterpriseGroups: '/common/enterprisegroups',
    profile: '/profile',
    settings: '/settings',
    messages: '/messages',
    notifications: '/notifications',
    users: '/common/users',
    systemMenu: '/common/menu',
    enterpriseImage: '/common/enterpriseimage',
    security: '/common/security',
    unit: '/common/unit',

    // Pms 
    pms: '/pms',
    pms_parameter_settings: '/pms/parameter_settings',
    pms_asset_account: '/pms/parameter_settings/asset_account',
    pms_asset_sub_account: '/pms/parameter_settings/asset_sub_account',
    pms_accessory_equipment: '/pms/parameter_settings/accessory_equipment',
    pms_amortization_source: '/pms/parameter_settings/amortization_source',
    pms_asset_category: '/pms/parameter_settings/asset_category',
    pms_asset_source: '/pms/parameter_settings/asset_source',
    pms_storage_location: '/pms/parameter_settings/storage_location',
    pms_system_parameter_setting: '/pms/parameter_settings/system_parameter_setting',
    pms_manufacturer: '/pms/parameter_settings/manufacturer',
    pms_user_role: '/pms/parameter_settings/user_role',
    pms_asset_batch_import: '/pms/parameter_settings/asset_batch_import',

    document_maintenance: '/pms/document_maintenance',
    pms_asset_carryout_form: '/pms/document_maintenance/asset_carryout_form',
    pms_asset_change_improvement: '/pms/document_maintenance/asset_change_improvement',
    pms_asset_location_change_form: '/pms/document_maintenance/asset_location_change_form',
    pms_fixed_asset_depreciation_form: '/pms/document_maintenance/fixed_asset_depreciation_form',
    pms_fixed_asset_maintenance_form: '/pms/document_maintenance/fixed_asset_maintenance_form',
    pms_fixed_asset_sale_form: '/pms/document_maintenance/fixed_asset_sale_form',
    pms_fixed_asset_scrap_form: '/pms/document_maintenance/fixed_asset_scrap_form',
    pms_vendor_maintenance_form: '/pms/document_maintenance/vendor_maintenance_form',

    asset_report: '/pms/asset_report',
    pms_asset_card: '/pms/asset_report/asset_card',
    pms_asset_depreciation: '/pms/asset_report/asset_depreciation',
    pms_asset_increase_decrease: '/pms/asset_report/asset_increase_decrease',
    pms_asset_inventory: '/pms/asset_report/asset_inventory',
    pms_asset_inventory_list: '/pms/asset_report/asset_inventory_list',
    pms_asset_inventory_record: '/pms/asset_report/asset_inventory_record',
    pms_asset_label: '/pms/asset_report/asset_label',
    pms_asset_list: '/pms/asset_report/asset_list',
    pms_asset_scrap: '/pms/asset_report/asset_scrap',

    // Pas 
    pas: '/pas',
    pas_employee_main: '/pas/employee_main',
    pas_overview: '/pas/overview',

    // # region Ims路由
    ims: '/ims',
    ims_basic: '/ims/basic',
    ims_basic_item: '/ims/basic/item',
    ims_basic_partner: '/ims/basic/partner',
    // # endregion Ims路由
} as const;

// 導出路由對象
export const routes = {
    root: basePaths.root,
    login: basePaths.login,
    dashboard: basePaths.dashboard,
    enterpriseGroups: basePaths.enterpriseGroups,
    profile: basePaths.profile,
    settings: basePaths.settings,
    messages: basePaths.messages,
    notifications: basePaths.notifications,
    users: basePaths.users,
    systemMenu: basePaths.systemMenu,
    enterpriseImage: basePaths.enterpriseImage,
    security: basePaths.security,
    unit: basePaths.unit,

    // Pms
    pms: basePaths.pms,
    parameter_settings: basePaths.pms_parameter_settings,
    pms_asset_account: basePaths.pms_asset_account,
    pms_asset_sub_account: basePaths.pms_asset_sub_account,
    pms_accessory_equipment: basePaths.pms_accessory_equipment,
    pms_amortization_source: basePaths.pms_amortization_source,
    pms_asset_category: basePaths.pms_asset_category,
    pms_asset_source: basePaths.pms_asset_source,
    pms_storage_location: basePaths.pms_storage_location,
    pms_system_parameter_setting: basePaths.pms_system_parameter_setting,
    pms_manufacturer: basePaths.pms_manufacturer,
    pms_user_role: basePaths.pms_user_role,
    pms_asset_batch_import: basePaths.pms_asset_batch_import,

    pms_asset_carryout_form: basePaths.pms_asset_carryout_form,
    pms_asset_change_improvement: basePaths.pms_asset_change_improvement,
    pms_asset_location_change_form: basePaths.pms_asset_location_change_form,
    pms_fixed_asset_depreciation_form: basePaths.pms_fixed_asset_depreciation_form,
    pms_fixed_asset_maintenance_form: basePaths.pms_fixed_asset_maintenance_form,
    pms_fixed_asset_sale_form: basePaths.pms_fixed_asset_sale_form,
    pms_fixed_asset_scrap_form: basePaths.pms_fixed_asset_scrap_form,
    pms_vendor_maintenance_form: basePaths.pms_vendor_maintenance_form,

    pms_asset_card: basePaths.pms_asset_card,
    pms_asset_depreciation: basePaths.pms_asset_depreciation,
    pms_asset_increase_decrease: basePaths.pms_asset_increase_decrease,
    pms_asset_inventory: basePaths.pms_asset_inventory,
    pms_asset_inventory_list: basePaths.pms_asset_inventory_list,
    pms_asset_inventory_record: basePaths.pms_asset_inventory_record,
    pms_asset_label: basePaths.pms_asset_label,
    pms_asset_list: basePaths.pms_asset_list,
    pms_asset_scrap: basePaths.pms_asset_scrap,

    // pas.
    pas: basePaths.pas,
    pas_employee_main: basePaths.pas_employee_main,

    // # region Ims路由
    ims: basePaths.ims,
    ims_basic: basePaths.ims_basic,
    ims_basic_item: basePaths.ims_basic_item,
    ims_basic_partner: basePaths.ims_basic_partner,
    // # endregion Ims路由
    pas_overview: basePaths.pas_overview,
} as const;

// 路由名稱映射
export const routeNames: Record<string, string> = {
    // 基礎路由
    [basePaths.root]: '首頁',
    [basePaths.login]: '登入',
    [basePaths.dashboard]: '儀表板',
    [basePaths.common]: '系統設定',
    [basePaths.enterpriseGroups]: '公司基本資訊',
    [basePaths.profile]: '個人資料',
    [basePaths.settings]: '修改密碼',
    [basePaths.messages]: '訊息中心',
    [basePaths.notifications]: '通知中心',
    [basePaths.users]: '用戶管理',
    [basePaths.systemMenu]: '選單設定',
    [basePaths.enterpriseImage]: '公司商標&印章管理',
    [basePaths.security]: '權限管理',
    [basePaths.unit]: '單位管理',

    // Pms
    [basePaths.pms]: '財產管理系統',
    [basePaths.pms_parameter_settings]: '基本資料管理',
    [basePaths.pms_asset_account]: '財產科目',
    [basePaths.pms_asset_sub_account]: '財產子目',
    [basePaths.pms_accessory_equipment]: '附屬設備',
    [basePaths.pms_amortization_source]: '攤提來源',
    [basePaths.pms_asset_category]: '財產類別',
    [basePaths.pms_asset_source]: '財產來源',
    [basePaths.pms_storage_location]: '存放位置',
    [basePaths.pms_system_parameter_setting]: '系統參數設定',
    [basePaths.pms_manufacturer]: '廠牌型號',
    [basePaths.pms_user_role]: '保管人&使用人',
    [basePaths.pms_asset_batch_import]: '財產整批轉檔',

    [basePaths.document_maintenance]: '財產管理',
    [basePaths.pms_asset_carryout_form]: '資產攜出單',
    [basePaths.pms_asset_change_improvement]: '資產變更改善單',
    [basePaths.pms_asset_location_change_form]: '資產位置變更單',
    [basePaths.pms_fixed_asset_depreciation_form]: '固定資產折舊單',
    [basePaths.pms_fixed_asset_maintenance_form]: '固定資產維護單',
    [basePaths.pms_fixed_asset_sale_form]: '固定資產出售單',
    [basePaths.pms_fixed_asset_scrap_form]: '固定資產報廢單',
    [basePaths.pms_vendor_maintenance_form]: '廠商維修單',

    [basePaths.asset_report]: '報表列印',
    [basePaths.pms_asset_card]: '財產卡片',
    [basePaths.pms_asset_depreciation]: '財產折舊',
    [basePaths.pms_asset_increase_decrease]: '財產增減',
    [basePaths.pms_asset_inventory]: '財產盤點',
    [basePaths.pms_asset_inventory_list]: '財產盤點清單',
    [basePaths.pms_asset_inventory_record]: '財產盤點記錄',
    [basePaths.pms_asset_label]: '財產標籤',
    [basePaths.pms_asset_list]: '財產清冊',
    [basePaths.pms_asset_scrap]: '財產報廢',

    // Pas
    [basePaths.pas]: '人事薪資系統',
    [basePaths.pas_employee_main]: '員工資料主頁',
    [basePaths.pas_overview]: '功能總覽',

    // # region Ims路由
    [basePaths.ims]: '進銷存系統',
    [basePaths.ims_basic]: '基本資料',
    [basePaths.ims_basic_item]: '庫存品管理',
    [basePaths.ims_basic_partner]: '商業夥伴管理',
    // # endregion Ims路由

} as const;

export type RouteKeys = keyof typeof routes;