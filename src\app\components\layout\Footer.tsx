"use client";

/* 頁尾
  /app/components/layout/Footer.tsx
*/
import React, { useEffect, useState } from "react";
import { Layout } from "antd";
import ConnectionStatus from "@/app/components/common/ConnectionStatus";
import ConnectionCount from "@/app/components/common/ConnectionCount";
import { siteConfig } from "@/config/site";

const { Footer: AntFooter } = Layout;
const Footer: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <AntFooter
      style={{
        textAlign: "center",
        padding: isMobile ? "8px 10px" : "24px",
        whiteSpace: "nowrap",
        overflow: "auto",
        fontSize: isMobile ? "12px" : "14px",
        margin: 0,
        backgroundColor: "#f0f2f5",
      }}
    >
      © {new Date().getFullYear()} {siteConfig.copyright}
      <ConnectionStatus />
      <ConnectionCount />
    </AntFooter>
  );
};

export default Footer;
