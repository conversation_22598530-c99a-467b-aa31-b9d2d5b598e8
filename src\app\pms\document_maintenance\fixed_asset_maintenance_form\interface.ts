import { Asset, AssetDetail } from "@/services/pms/assetService";
import { Department } from "@/services/common/departmentService";
import { Division } from "@/services/common/divisionService";
import { AssetAccount } from "@/services/pms/assetAccountService";
import { AssetSubAccount } from "@/services/pms/assetSubAccountService";
import { AssetCategory } from "@/services/pms/assetCategoryService";
import { AssetStatus } from "@/services/pms/assetStatusService";
import { Unit } from "@/services/common/unitService";
import { EquipmentType } from "@/services/pms/equipmentTypeService";
import { AmortizationSource } from "@/services/pms/amortizationSourceService";
import { Manufacturer } from "@/services/pms/manufacturerService";
import { User } from "@/services/common/userService";
import { StorageLocation } from "@/services/pms/storageLocationService";

// 定義固定資產維護單的查詢參數介面
export interface AssetMaintenanceFormQuery {
    keyword?: string;
    assetCategoryId?: string;
    assetStatus?: string;
    departmentId?: string;
    assetAccountId?: string;
    equipmentTypeId?: string;
    acquisitionDate?: string;
    assetNo?: string;
    custodian?: string;
    user?: string;
    certificateNo?: string;
}

// 定義附屬設備介面
export interface AccessoryEquipment {
    accessoryEquipmentId: string;
    equipmentNo: string;
    equipmentName: string;
    equipmentType: string;
    specification: string;
    purchaseDate: number;
    purchasePrice: number;
    assetId: string;
    asset: any;
    usageStatus: string;
    remarks: string;
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime: number;
    deleteUserId: string;
}

// 定義保險單位介面
export interface InsuranceUnit {
    insuranceUnitId: string;
    name: string;
    companyNo: string;
    contactPerson: string;
    contactPhone: string;
    contactEmail: string;
    address: string;
    website: string;
    description: string;
    sortCode: number;
    insuranceAmount: number;
    insuranceStartDate: number | null;
    insuranceExpiryDate: number | null;
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime: number;
    deleteUserId: string;
    assetInsuranceUnits: any[];
}

// 狀態顏色映射
export const STATUS_COLORS: Record<string, string> = {
    "正常使用": "green",
    "閒置": "blue",
    "借出": "orange",
    "維修中": "gold",
    "已報廢": "red",
    "已丟失": "magenta",
    "已轉移": "cyan",
    "已出售": "purple",
};

// 保險狀態顏色映射
export const INSURANCE_STATUS_COLORS: Record<string, string> = {
    "未知": "default",
    "未開始": "blue",
    "投保中": "green",
    "已到期": "red",
};

// 表單初始值
export const formInitialValues = {
    assetStatus: "",
    plannedForDisposal: "N",
    isStillUsable: "N",
    quantity: 1,
};

export interface FormProps {
    editingAsset: AssetDetail | null;
    isViewMode: boolean;
    onCancel: () => void;
    onSuccess: (assetDetailData: AssetDetail) => void;
    assetStatusOptions: AssetStatus[];
    assetCategories: AssetCategory[];
    departments: Department[];
    divisions: Division[];
    assetAccounts: AssetAccount[];
    assetSubAccounts: AssetSubAccount[];
    units: Unit[];
    equipmentTypes: EquipmentType[];
    manufacturers: Manufacturer[];
    custodians: User[];
    assetUsers: User[];
    assetSources: AssetSource[];
    amortizationSources: AmortizationSource[];
    storageLocations: StorageLocation[];
}

export interface AssetSource {
    assetSourceId: string;
    assetSourceName: string;
    sortCode: number;
    createTime: number;
    createUserId: string;
    updateTime: number;
    updateUserId: string;
    deleteTime: number;
    deleteUserId: string;
    assetAssetSources: any[];
}
