"use client";

import React from 'react';
import { ShopOutlined } from '@ant-design/icons';
import GenericCategoryManagement, { GenericCategory, CategoryService, CategoryConfig } from './GenericCategoryManagement';
import { SupplierCategory } from '@/services/ims/partner';
import { addSupplierCategory, editSupplierCategory, deleteSupplierCategory, buildSupplierCategoryTree } from '@/services/ims/SupplierCategoryService';

interface SupplierCategoryAdapterProps {
  visible: boolean;
  onClose: () => void;
  categories: SupplierCategory[];
  onDataChange: () => void;
}

// 供應商分類服務適配器
const supplierCategoryService: CategoryService<SupplierCategory> = {
  add: async (category: Partial<SupplierCategory>) => {
    return await addSupplierCategory({
      name: category.name || '',
      description: category.description || '',
      parentID: category.parentID || null,
      sortCode: category.sortCode || 0,
    });
  },
  
  edit: async (category: Partial<SupplierCategory>) => {
    return await editSupplierCategory({
      supplierCategoryID: category.supplierCategoryID || '',
      name: category.name || '',
      description: category.description || '',
      parentID: category.parentID || null,
      sortCode: category.sortCode || 0,
    });
  },
  
  delete: async (id: string) => {
    return await deleteSupplierCategory(id);
  },
  
  buildTree: (categories: SupplierCategory[]) => {
    return buildSupplierCategoryTree(categories);
  }
};

// 供應商分類配置
const supplierCategoryConfig: CategoryConfig = {
  title: '供應商分類管理',
  icon: <ShopOutlined style={{ color: '#1890ff' }} />,
  emptyMessage: '尚無供應商分類',
  emptyDescription: '點擊右側「新增分類」按鈕開始建立供應商分類結構。',
  entityName: '供應商分類'
};

// 映射函數
const mapToGeneric = (category: SupplierCategory): GenericCategory => ({
  id: category.supplierCategoryID,
  name: category.name,
  description: category.description,
  parentID: category.parentID,
  sortCode: category.sortCode,
  children: category.children?.map(mapToGeneric)
});

const mapFromGeneric = (generic: GenericCategory, original?: SupplierCategory): Partial<SupplierCategory> => ({
  supplierCategoryID: generic.id || original?.supplierCategoryID,
  name: generic.name,
  description: generic.description,
  parentID: generic.parentID,
  sortCode: generic.sortCode
});

const getIdField = (category: SupplierCategory): string => category.supplierCategoryID;

const SupplierCategoryAdapter: React.FC<SupplierCategoryAdapterProps> = (props) => {
  return (
    <GenericCategoryManagement
      {...props}
      config={supplierCategoryConfig}
      service={supplierCategoryService}
      getIdField={getIdField}
      mapToGeneric={mapToGeneric}
      mapFromGeneric={mapFromGeneric}
    />
  );
};

export default SupplierCategoryAdapter;
