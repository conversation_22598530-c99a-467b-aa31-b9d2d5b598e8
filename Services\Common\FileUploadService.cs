using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Hosting;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Server.Tools;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 檔案上傳服務實作
    /// </summary>
    public class FileUploadService : IFileUploadService
    {
        private readonly ERPDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly Baseform _baseform;
        private readonly ILoggerService _logger;

        // 預設允許的檔案類型
        private readonly string[] _defaultAllowedExtensions = {
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff",
            ".txt", ".csv", ".xml", ".json", ".zip", ".rar", ".7z"
        };

        // 預設最大檔案大小（MB）
        private const int _defaultMaxSizeInMB = 10;

        public FileUploadService(
            ERPDbContext context,
            IWebHostEnvironment environment,
            Baseform baseform,
            ILoggerService logger)
        {
            _context = context;
            _environment = environment;
            _baseform = baseform;
            _logger = logger;
        }

        /// <summary>
        /// 上傳單一檔案
        /// </summary>
        public async Task<(bool Success, string Message, FileUploadDTO? FileInfo)> UploadFileAsync(
            FileUploadRequestDTO request, 
            string userId)
        {
            try
            {
                // 驗證檔案
                var (isValid, validationMessage) = ValidateFile(request.File);
                if (!isValid)
                {
                    return (false, validationMessage, null);
                }

                // 驗證企業群組是否存在
                var enterpriseGroup = await _context.Common_EnterpriseGroups
                    .FirstOrDefaultAsync(e => e.EnterpriseGroupsId == request.EnterpriseGroupsId && !e.IsDeleted);
                if (enterpriseGroup == null)
                {
                    return (false, "企業群組不存在", null);
                }

                // 產生檔案雜湊值
                string fileHash;
                using (var stream = request.File.OpenReadStream())
                {
                    fileHash = await GenerateFileHashAsync(stream);
                }

                // 檢查重複檔案
                var duplicateFile = await GetDuplicateFileAsync(fileHash, request.EnterpriseGroupsId);
                if (duplicateFile != null)
                {
                    return (false, $"檔案已存在：{duplicateFile.OriginalFileName}", duplicateFile);
                }

                // 建立檔案儲存路徑
                var storagePath = GetStoragePath(request.EnterpriseGroupsId, request.FileCategory);
                var storedFileName = GenerateStoredFileName(request.File.FileName);
                var fullPath = Path.Combine(storagePath, storedFileName);

                // 確保目錄存在
                Directory.CreateDirectory(storagePath);

                // 儲存檔案
                using (var fileStream = new FileStream(fullPath, FileMode.Create))
                {
                    await request.File.CopyToAsync(fileStream);
                }

                // 建立檔案記錄
                var fileUpload = new FileUpload
                {
                    FileUploadId = Guid.NewGuid().ToString(),
                    EnterpriseGroupsId = request.EnterpriseGroupsId,
                    OriginalFileName = request.File.FileName,
                    StoredFileName = storedFileName,
                    FilePath = GetRelativePath(fullPath),
                    FileSize = request.File.Length,
                    ContentType = request.File.ContentType,
                    FileExtension = Path.GetExtension(request.File.FileName).ToLowerInvariant(),
                    FileCategory = request.FileCategory,
                    SourceModule = request.SourceModule,
                    SourceTable = request.SourceTable,
                    SourceRecordId = request.SourceRecordId,
                    Description = request.Description,
                    SortOrder = request.SortOrder,
                    IsPrimary = request.IsPrimary,
                    AccessLevel = request.AccessLevel,
                    FileStatus = "Active",
                    FileHash = fileHash,
                    DownloadCount = 0,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CreateUserId = userId
                };

                _context.Common_FileUploads.Add(fileUpload);
                await _context.SaveChangesAsync();

                // 記錄成功日誌
                await _logger.LogDataAsync(
                    $"檔案上傳成功 - 檔名: {request.File.FileName}, 大小: {request.File.Length} bytes",
                    new
                    {
                        FileUploadId = fileUpload.FileUploadId,
                        FileName = request.File.FileName,
                        FileSize = request.File.Length,
                        ContentType = request.File.ContentType,
                        EnterpriseGroupsId = request.EnterpriseGroupsId
                    },
                    Guid.NewGuid().ToString(),
                    "FileUploadService"
                );

                var fileDto = MapToDTO(fileUpload);
                return (true, "檔案上傳成功", fileDto);
            }
            catch (Exception ex)
            {
                await _logger.LogErrorAsync(
                    $"檔案上傳失敗 - 檔名: {request.File?.FileName ?? "未知"}",
                    ex,
                    "FileUploadService"
                );
                return (false, $"檔案上傳失敗: {ex.Message}", null);
            }
        }

        /// <summary>
        /// 批量上傳檔案
        /// </summary>
        public async Task<(bool Success, string Message, List<FileUploadDTO> FileInfos)> UploadFilesAsync(
            BatchFileUploadRequestDTO request, 
            string userId)
        {
            var uploadedFiles = new List<FileUploadDTO>();
            var errors = new List<string>();

            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();

                for (int i = 0; i < request.Files.Count; i++)
                {
                    var file = request.Files[i];
                    var description = i < request.Descriptions.Count ? request.Descriptions[i] : "";

                    var singleRequest = new FileUploadRequestDTO
                    {
                        EnterpriseGroupsId = request.EnterpriseGroupsId,
                        File = file,
                        FileCategory = request.FileCategory,
                        SourceModule = request.SourceModule,
                        SourceTable = request.SourceTable,
                        SourceRecordId = request.SourceRecordId,
                        Description = description,
                        SortOrder = i,
                        IsPrimary = i == 0, // 第一個檔案設為主要檔案
                        AccessLevel = request.AccessLevel
                    };

                    var (success, message, fileInfo) = await UploadFileAsync(singleRequest, userId);
                    if (success && fileInfo != null)
                    {
                        uploadedFiles.Add(fileInfo);
                    }
                    else
                    {
                        errors.Add($"{file.FileName}: {message}");
                    }
                }

                if (errors.Count == 0)
                {
                    await transaction.CommitAsync();
                    return (true, $"成功上傳 {uploadedFiles.Count} 個檔案", uploadedFiles);
                }
                else
                {
                    await transaction.RollbackAsync();
                    return (false, $"部分檔案上傳失敗: {string.Join("; ", errors)}", uploadedFiles);
                }
            }
            catch (Exception ex)
            {
                return (false, $"批量上傳失敗: {ex.Message}", uploadedFiles);
            }
        }

        /// <summary>
        /// 下載檔案
        /// </summary>
        public async Task<(bool Success, string Message, Stream? FileStream, FileUploadDTO? FileInfo)> DownloadFileAsync(
            FileDownloadRequestDTO request, 
            string userId)
        {
            try
            {
                var fileInfo = await GetFileInfoAsync(request.FileUploadId, request.EnterpriseGroupsId);
                if (!fileInfo.Success || fileInfo.FileInfo == null)
                {
                    return (false, "檔案不存在", null, null);
                }

                var fullPath = GetFullPath(fileInfo.FileInfo.FilePath);
                if (!File.Exists(fullPath))
                {
                    return (false, "實體檔案不存在", null, null);
                }

                // 更新下載次數和最後存取時間
                var fileEntity = await _context.Common_FileUploads
                    .FirstOrDefaultAsync(f => f.FileUploadId == request.FileUploadId && 
                                            f.EnterpriseGroupsId == request.EnterpriseGroupsId);
                if (fileEntity != null)
                {
                    fileEntity.DownloadCount++;
                    fileEntity.LastAccessTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    fileEntity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    fileEntity.UpdateUserId = userId;
                    await _context.SaveChangesAsync();
                }

                var fileStream = new FileStream(fullPath, FileMode.Open, FileAccess.Read);
                return (true, "檔案下載成功", fileStream, fileInfo.FileInfo);
            }
            catch (Exception ex)
            {
                return (false, $"檔案下載失敗: {ex.Message}", null, null);
            }
        }

        /// <summary>
        /// 刪除檔案
        /// </summary>
        public async Task<(bool Success, string Message)> DeleteFileAsync(
            string fileUploadId, 
            string enterpriseGroupsId, 
            string userId)
        {
            try
            {
                var fileEntity = await _context.Common_FileUploads
                    .FirstOrDefaultAsync(f => f.FileUploadId == fileUploadId && 
                                            f.EnterpriseGroupsId == enterpriseGroupsId && 
                                            !f.IsDeleted);

                if (fileEntity == null)
                {
                    return (false, "檔案不存在");
                }

                // 軟刪除
                fileEntity.IsDeleted = true;
                fileEntity.DeleteTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                fileEntity.DeleteUserId = userId;
                fileEntity.FileStatus = "Deleted";

                await _context.SaveChangesAsync();

                // 可選：實際刪除實體檔案（建議保留一段時間後再刪除）
                // var fullPath = GetFullPath(fileEntity.FilePath);
                // if (File.Exists(fullPath))
                // {
                //     File.Delete(fullPath);
                // }

                return (true, "檔案刪除成功");
            }
            catch (Exception ex)
            {
                return (false, $"檔案刪除失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 取得檔案列表
        /// </summary>
        public async Task<(bool Success, string Message, List<FileUploadDTO> Files, int TotalCount)> GetFilesAsync(
            FileQueryRequestDTO request)
        {
            try
            {
                var query = _context.Common_FileUploads
                    .Where(f => f.EnterpriseGroupsId == request.EnterpriseGroupsId && !f.IsDeleted);

                if (!string.IsNullOrEmpty(request.SourceModule))
                    query = query.Where(f => f.SourceModule == request.SourceModule);

                if (!string.IsNullOrEmpty(request.SourceTable))
                    query = query.Where(f => f.SourceTable == request.SourceTable);

                if (!string.IsNullOrEmpty(request.SourceRecordId))
                    query = query.Where(f => f.SourceRecordId == request.SourceRecordId);

                if (!string.IsNullOrEmpty(request.FileCategory))
                    query = query.Where(f => f.FileCategory == request.FileCategory);

                if (!string.IsNullOrEmpty(request.FileStatus))
                    query = query.Where(f => f.FileStatus == request.FileStatus);

                var totalCount = await query.CountAsync();

                var files = await query
                    .OrderBy(f => f.SortOrder)
                    .ThenByDescending(f => f.CreateTime)
                    .Skip((request.PageIndex - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync();

                var fileDtos = files.Select(MapToDTO).ToList();
                return (true, "取得檔案列表成功", fileDtos, totalCount);
            }
            catch (Exception ex)
            {
                return (false, $"取得檔案列表失敗: {ex.Message}", new List<FileUploadDTO>(), 0);
            }
        }

        /// <summary>
        /// 取得檔案資訊
        /// </summary>
        public async Task<(bool Success, string Message, FileUploadDTO? FileInfo)> GetFileInfoAsync(
            string fileUploadId,
            string enterpriseGroupsId)
        {
            try
            {
                var fileEntity = await _context.Common_FileUploads
                    .FirstOrDefaultAsync(f => f.FileUploadId == fileUploadId &&
                                            f.EnterpriseGroupsId == enterpriseGroupsId &&
                                            !f.IsDeleted);

                if (fileEntity == null)
                {
                    return (false, "檔案不存在", null);
                }

                var fileDto = MapToDTO(fileEntity);
                return (true, "取得檔案資訊成功", fileDto);
            }
            catch (Exception ex)
            {
                return (false, $"取得檔案資訊失敗: {ex.Message}", null);
            }
        }

        /// <summary>
        /// 更新檔案資訊
        /// </summary>
        public async Task<(bool Success, string Message)> UpdateFileInfoAsync(
            FileUploadDTO fileInfo,
            string userId)
        {
            try
            {
                var fileEntity = await _context.Common_FileUploads
                    .FirstOrDefaultAsync(f => f.FileUploadId == fileInfo.FileUploadId &&
                                            f.EnterpriseGroupsId == fileInfo.EnterpriseGroupsId &&
                                            !f.IsDeleted);

                if (fileEntity == null)
                {
                    return (false, "檔案不存在");
                }

                // 更新可修改的欄位
                fileEntity.Description = fileInfo.Description;
                fileEntity.SortOrder = fileInfo.SortOrder;
                fileEntity.IsPrimary = fileInfo.IsPrimary;
                fileEntity.AccessLevel = fileInfo.AccessLevel;
                fileEntity.FileStatus = fileInfo.FileStatus;
                fileEntity.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                fileEntity.UpdateUserId = userId;

                await _context.SaveChangesAsync();
                return (true, "檔案資訊更新成功");
            }
            catch (Exception ex)
            {
                return (false, $"檔案資訊更新失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 檢查檔案是否存在
        /// </summary>
        public async Task<bool> FileExistsAsync(string fileUploadId, string enterpriseGroupsId)
        {
            return await _context.Common_FileUploads
                .AnyAsync(f => f.FileUploadId == fileUploadId &&
                             f.EnterpriseGroupsId == enterpriseGroupsId &&
                             !f.IsDeleted);
        }

        /// <summary>
        /// 根據雜湊值檢查重複檔案
        /// </summary>
        public async Task<FileUploadDTO?> GetDuplicateFileAsync(string fileHash, string enterpriseGroupsId)
        {
            var duplicateFile = await _context.Common_FileUploads
                .FirstOrDefaultAsync(f => f.FileHash == fileHash &&
                                        f.EnterpriseGroupsId == enterpriseGroupsId &&
                                        !f.IsDeleted);

            return duplicateFile != null ? MapToDTO(duplicateFile) : null;
        }

        /// <summary>
        /// 清理過期檔案
        /// </summary>
        public async Task<(bool Success, string Message, int CleanedCount)> CleanupExpiredFilesAsync(int daysOld = 30)
        {
            try
            {
                var cutoffTime = DateTimeOffset.UtcNow.AddDays(-daysOld).ToUnixTimeSeconds();

                var expiredFiles = await _context.Common_FileUploads
                    .Where(f => f.IsDeleted && f.DeleteTime.HasValue && f.DeleteTime.Value < cutoffTime)
                    .ToListAsync();

                int cleanedCount = 0;
                foreach (var file in expiredFiles)
                {
                    try
                    {
                        // 刪除實體檔案
                        var fullPath = GetFullPath(file.FilePath);
                        if (File.Exists(fullPath))
                        {
                            File.Delete(fullPath);
                        }

                        // 從資料庫中移除記錄
                        _context.Common_FileUploads.Remove(file);
                        cleanedCount++;
                    }
                    catch (Exception ex)
                    {
                        await _logger.LogErrorAsync(
                            $"清理檔案失敗 - 檔名: {file.OriginalFileName}",
                            ex,
                            "FileUploadService"
                        );
                    }
                }

                await _context.SaveChangesAsync();
                return (true, $"成功清理 {cleanedCount} 個過期檔案", cleanedCount);
            }
            catch (Exception ex)
            {
                return (false, $"清理過期檔案失敗: {ex.Message}", 0);
            }
        }

        /// <summary>
        /// 取得企業群組的儲存統計
        /// </summary>
        public async Task<(long TotalFiles, long TotalSize, long TotalDownloads)> GetStorageStatsAsync(
            string enterpriseGroupsId)
        {
            try
            {
                var stats = await _context.Common_FileUploads
                    .Where(f => f.EnterpriseGroupsId == enterpriseGroupsId && !f.IsDeleted)
                    .GroupBy(f => f.EnterpriseGroupsId)
                    .Select(g => new
                    {
                        TotalFiles = g.Count(),
                        TotalSize = g.Sum(f => f.FileSize),
                        TotalDownloads = g.Sum(f => f.DownloadCount)
                    })
                    .FirstOrDefaultAsync();

                return stats != null
                    ? (stats.TotalFiles, stats.TotalSize, stats.TotalDownloads)
                    : (0, 0, 0);
            }
            catch
            {
                return (0, 0, 0);
            }
        }

        /// <summary>
        /// 驗證檔案類型和大小
        /// </summary>
        public (bool IsValid, string Message) ValidateFile(
            IFormFile file,
            string[] allowedExtensions = null,
            int maxSizeInMB = 10)
        {
            if (file == null || file.Length == 0)
            {
                return (false, "檔案不能為空");
            }

            // 檢查檔案大小
            var maxSizeInBytes = (maxSizeInMB > 0 ? maxSizeInMB : _defaultMaxSizeInMB) * 1024 * 1024;
            if (file.Length > maxSizeInBytes)
            {
                return (false, $"檔案大小不能超過 {maxSizeInMB}MB");
            }

            // 檢查檔案類型
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var allowedExts = allowedExtensions ?? _defaultAllowedExtensions;

            if (!allowedExts.Contains(fileExtension))
            {
                return (false, $"不支援的檔案類型: {fileExtension}");
            }

            // 檢查檔案名稱
            if (string.IsNullOrWhiteSpace(file.FileName) || file.FileName.Length > 255)
            {
                return (false, "檔案名稱無效或過長");
            }

            return (true, "檔案驗證通過");
        }

        /// <summary>
        /// 產生檔案雜湊值
        /// </summary>
        public async Task<string> GenerateFileHashAsync(Stream fileStream)
        {
            using var sha256 = SHA256.Create();
            var hashBytes = await Task.Run(() => sha256.ComputeHash(fileStream));
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }

        #region 私有輔助方法

        /// <summary>
        /// 取得儲存路徑
        /// </summary>
        private string GetStoragePath(string enterpriseGroupsId, string fileCategory)
        {
            var uploadRoot = _environment.WebRootPath ?? Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
            var year = DateTime.Now.Year.ToString();
            var month = DateTime.Now.Month.ToString("D2");

            return Path.Combine(uploadRoot, "uploads", enterpriseGroupsId, fileCategory, year, month);
        }

        /// <summary>
        /// 產生儲存檔案名稱
        /// </summary>
        private string GenerateStoredFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var guid = Guid.NewGuid().ToString("N");
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            return $"{timestamp}_{guid}{extension}";
        }

        /// <summary>
        /// 取得相對路徑
        /// </summary>
        private string GetRelativePath(string fullPath)
        {
            var uploadRoot = _environment.WebRootPath ?? Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
            return Path.GetRelativePath(uploadRoot, fullPath).Replace('\\', '/');
        }

        /// <summary>
        /// 取得完整路徑
        /// </summary>
        private string GetFullPath(string relativePath)
        {
            var uploadRoot = _environment.WebRootPath ?? Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
            return Path.Combine(uploadRoot, relativePath.Replace('/', Path.DirectorySeparatorChar));
        }

        /// <summary>
        /// 將 Entity 轉換為 DTO
        /// </summary>
        private FileUploadDTO MapToDTO(FileUpload entity)
        {
            return new FileUploadDTO
            {
                FileUploadId = entity.FileUploadId,
                EnterpriseGroupsId = entity.EnterpriseGroupsId,
                OriginalFileName = entity.OriginalFileName,
                StoredFileName = entity.StoredFileName,
                FilePath = entity.FilePath,
                FileSize = entity.FileSize,
                ContentType = entity.ContentType,
                FileExtension = entity.FileExtension,
                FileCategory = entity.FileCategory,
                SourceModule = entity.SourceModule,
                SourceTable = entity.SourceTable,
                SourceRecordId = entity.SourceRecordId,
                Description = entity.Description,
                SortOrder = entity.SortOrder,
                IsPrimary = entity.IsPrimary,
                AccessLevel = entity.AccessLevel,
                FileStatus = entity.FileStatus,
                FileHash = entity.FileHash,
                DownloadCount = entity.DownloadCount,
                LastAccessTime = entity.LastAccessTime,
                CreateTime = entity.CreateTime,
                CreateUserId = entity.CreateUserId,
                UpdateTime = entity.UpdateTime,
                UpdateUserId = entity.UpdateUserId,
                DeleteTime = entity.DeleteTime,
                DeleteUserId = entity.DeleteUserId,
                IsDeleted = entity.IsDeleted
            };
        }

        #endregion
    }
}
