﻿using System;
using System.Data;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Interfaces.Common;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    [SwaggerTag("縣市資料管理")]
    public class CityController : ControllerBase
    {
        private readonly ICityService _Interface;

        public CityController(ICityService cityService)
        {
            _Interface = cityService;
        }

        [HttpGet]
        [Route("GetCity")]
        [SwaggerOperation(Summary = "取得縣市列表", Description = "取得所有縣市列表")]
        public async Task<IActionResult> GetCityList()
        {
            var cities = await _Interface.GetCityAsync();
            return Ok(cities);
        }

        [HttpGet]
        [Route("GetCity/{_cityId?}")]
        [SwaggerOperation(Summary = "取得縣市明細", Description = "依ID取得縣市明細")]
        public async Task<IActionResult> GetCityDetail(string _cityId)
        {
            var city = await _Interface.GetCityDetailAsync(_cityId);
            return Ok(city);
        }

        [HttpPost]
        [Route("AddCity")]
        [SwaggerOperation(Summary = "新增縣市", Description = "新增縣市資料")]
        public async Task<IActionResult> AddCity([FromBody] CityDTO _data)
        {
            var (result, msg) = await _Interface.AddCityAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("EditCity")]
        [SwaggerOperation(Summary = "編輯縣市", Description = "修改已存在之縣市資料")]
        public async Task<IActionResult> EditCity([FromBody] CityDTO _data)
        {
            var (result, msg) = await _Interface.EditCityAsync(_data);
            return Ok(new { result, msg });
        }

        [HttpPost]
        [Route("DeleteCity")]
        [SwaggerOperation(Summary = "刪除縣市", Description = "刪除已存在之縣市資料")]
        public async Task<IActionResult> DeleteCity([FromBody] CityDTO _data)
        {
            var (result, msg) = await _Interface.DeleteCityAsync(_data);
            return Ok(new { result, msg });
        }
    }
}