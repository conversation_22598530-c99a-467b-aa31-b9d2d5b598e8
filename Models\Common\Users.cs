﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Common;

namespace FAST_ERP_Backend.Models.Common
{
    public class Users : ModelBaseEntity
    {
        [Key]
        [Comment("使用者編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string UserId { get; set; } // 使用者編號

        [Comment("帳號")]
        [Column(TypeName = "nvarchar(50)")]
        public string Account { get; set; } // 帳號

        [Comment("密碼")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Password { get; set; } // 密碼

        [Comment("使用者名稱")]
        [Column(TypeName = "nvarchar(50)")]
        public string Name { get; set; } // 使用者名稱

        [Comment("公司群組編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string EnterpriseGroupId { get; set; } // 公司群組編號

        [Comment("角色編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string RolesId { get; set; } // 角色編號

        [Comment("職務編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string PositionId { get; set; } // 職務編號

        [Comment("電子信箱")]
        [Column(TypeName = "nvarchar(50)")]
        public string EMail { get; set; } // 電子信箱

        [Comment("戶籍地址")]
        [Column(TypeName = "nvarchar(100)")]
        public string PermanentAddress { get; set; } // 戶籍地址

        [Comment("通訊地址")]
        [Column(TypeName = "nvarchar(100)")]
        public string MailingAddress { get; set; } // 通訊地址

        [Comment("電話")]
        [Column(TypeName = "nvarchar(15)")]
        public string TelNo { get; set; } // 電話

        [Comment("手機")]
        [Column(TypeName = "nvarchar(10)")]
        public string Phone { get; set; } // 手機

        [Comment("備用電話")]
        [Column(TypeName = "nvarchar(10)")]
        public string AltPhone { get; set; } // 備用電話

        [Comment("排序編碼")]
        [Column(TypeName = "int")]
        public int SortCode { get; set; } // 排序編碼

        [Comment("解鎖時間")]
        [Column(TypeName = "bigint")]
        public long? UnlockTime { get; set; } // 解鎖時間

        public Users()
        {
            UserId = "";
            Account = "";
            Password = "";
            Name = "";
            EnterpriseGroupId = "";
            RolesId = "";
            PositionId = "";
            EMail = "";
            PermanentAddress = "";
            MailingAddress = "";
            TelNo = "";
            Phone = "";
            AltPhone = "";
            SortCode = 0;
            UnlockTime = null;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class UsersDTO
    {
        public string UserId { get; set; }         // 使用者編號
        public string Account { get; set; }        // 帳號
        public string Password { get; set; }       // 密碼
        public string Name { get; set; }           // 使用者名稱
        public string EnterpriseGroupId { get; set; } // 公司群組編號
        public string? EnterpriseGroupName { get; set; } // 公司群組名稱
        public string RolesId { get; set; }        // 角色編號
        public string? RolesName { get; set; }      // 角色名稱
        public string PositionId { get; set; }     // 職務編號
        public string? PositionName { get; set; }   // 職務名稱
        public string EMail { get; set; }          // 電子信箱
        public string PermanentAddress { get; set; } // 戶籍地址
        public string MailingAddress { get; set; } // 通訊地址
        public string TelNo { get; set; }          // 電話
        public string Phone { get; set; }          // 手機
        public string AltPhone { get; set; }       // 備用電話
        public int SortCode { get; set; }          // 排序編碼
        public long? UnlockTime { get; set; }      // 解鎖時間
        public long? CreateTime { get; set; } //新增時間
        public string? CreateUserId { get; set; } //新增者編號
        public long? UpdateTime { get; set; } //更新時間
        public string? UpdateUserId { get; set; } //更新者編號
        public long? DeleteTime { get; set; } //刪除時間
        public string? DeleteUserId { get; set; } //刪除者編號
        public bool IsDeleted { get; set; } //刪除狀態

        public UsersDTO()
        {
            UserId = "";
            Account = "";
            Password = "";
            Name = "";
            EnterpriseGroupId = "";
            EnterpriseGroupName = null;
            RolesId = "";
            RolesName = null;
            PositionId = "";
            PositionName = null;
            EMail = "";
            PermanentAddress = "";
            MailingAddress = "";
            TelNo = "";
            Phone = "";
            AltPhone = "";
            SortCode = 0;
            UnlockTime = null;
            CreateTime = null;
            CreateUserId = null;
            UpdateTime = null;
            UpdateUserId = null;
            DeleteTime = null;
            DeleteUserId = null;
            IsDeleted = false;
        }
    }

    public class ChangeUsersPasswordDTO
    {
        public string UserId { get; set; }         // 使用者編號
        public string oldPassword { get; set; }       // 舊密碼
        public string newPassword { get; set; }       // 新密碼


        public ChangeUsersPasswordDTO()
        {
            UserId = "";
            oldPassword = "";
            newPassword = "";
        }
    }
}